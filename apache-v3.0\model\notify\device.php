<?php
namespace model\notify;

trait device
{
    /**
     * @name: 重启
     */
    public function reboot()
    {
        $params = [
            "MAC"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $this->log->actionLog("#model#notify#reboot#mac=$mac;");
        
        \devReboot($mac);
    }

    /**
     * @name: 下发设备配置
     */
    public function setConfig()
    {
        $params = [
            "MAC"=>"",
            "Config"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $config = $params["Config"];
        $this->log->actionLog("#model#notify#setConfig#mac=$mac;config=$config");
        \devConfigUpdate($mac, $config);
    }

    public function setConfigOnce()
    {
        $params = [
            "MAC"=>"",
            "Config"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $config = $params["Config"];
        $this->log->actionLog("#model#notify#setConfigOnce#mac=$mac;config=$config");
        \devConfigUpdateOnce($mac, $config);
    }

    /**
     * @name: 远程控制
     */
    public function remote()
    {
        $params = [
            "MAC"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $this->log->actionLog("#model#notify#remote#mac=$mac;");
        $res = \createRemoteDevConfig($mac);
        $code = $res["result"];
        $url = $res["url"];
        $time = $res["timeout"];
        
        if ($code != 0) {
            $message = ["-1"=>MSGTEXT["unavailableService"],"-2"=>MSGTEXT["deviceDisconnected"],"-3"=>MSGTEXT["notForModel"],"-4"=>MSGTEXT["upgradeDevVersion"]];
            echo json_encode(["code"=>1,"msg"=>$message[$code]]);
        } else {
            echo json_encode(["code"=>0,"msg"=>"","url"=>$url,"timeout"=>$time]);
        }
    }

    /**
     * @name: 在小区中添加设备
     */
    public function devComAddForManage($isRoom = 0)
    {
        $params = [
            "Build"=>"",
            "MAC"=>"",
            "NodeID"=>"",
            "userAliasId"=>"",
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $buildId = $params["Build"];
        $mac = $params["MAC"];
        if ($isRoom == 0) {
            $ownerID = $params["NodeID"];
        } else {
            // 如果添加用户时修改设备，NodeID是Devices的ID,ID是用户ID
            $ownerID = $params["ID"];
        }
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#addForComManage#mac=$mac;buildId=$buildId;ownerID=$ownerID;userId=$userId");

        switch ($buildId) {
            case 'public':
                //添加设备，通知后台
                \webCommunityModifyNotify(WEB_COMM_PUB_ADD_DEV, "", $mac, $userId, "");
                break;
            default:
                if ($ownerID) {
                    $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$ownerID,":ParentID"=>$userId]]);
                    $node = $data[0]["Account"];
                    $unitID = $data[0]["UnitID"];
                    \webCommunityModifyNotify(WEB_COMM_ADD_DEV, $node, $mac, $userId, $unitID);
                } else {
                    \webCommunityModifyNotify(WEB_COMM_UNIT_ADD_DEV, "", $mac, $userId, $buildId);
                }
                break;
        }
    }

    /**
     * @name: 删除社区的设备
     */
    public function devComDel()
    {
        $params = [
            "userAliasId"=>"",
            "grade"=>"",
            "mac"=>"",
            "unitId"=>"",
            "node"=>"",
            "AccessGroup"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $mac = $params["mac"];
        $unitId = $params["unitId"];
        $node = $params["node"];
        $grade = $params["grade"];
        $accessGroup = $params["AccessGroup"];
        $this->log->actionLog("#model#notify#delForCom#".json_encode($params));
        if ($grade == 1) {
            \webCommunityModifyNotify(WEB_COMM_PUB_DEL_DEV, $node, $mac, $userId, $unitId);
        } elseif ($grade == 2) {
            \webCommunityModifyNotify(WEB_COMM_UNIT_DEL_DEV, $node, $mac, $userId, $unitId);
        } else {
            \webCommunityModifyNotify(WEB_COMM_DEL_DEV, $node, $mac, $userId, $unitId);
        }
    }

    /**
     * @name: 批量删除社区的设备
     */
    public function devComMulDev()
    {
        $params = [
            "data"=>"",
            "userAliasId"=>"",
            "AccessGroup"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $userId = $params["userAliasId"];
        $accessGroup = $params["AccessGroup"];
        $this->log->actionLog("#model#notify#devComMulDev#".json_encode($params));
        $nodes = [];
        $units = [];
        $community = [];
        $macs = [];
        foreach ($data as $value) {
            if ($value["Grade"] == 1) {
                $community[$value["MAC"]] = $value["MAC"];
            } elseif ($value["Grade"] == 2) {
                $units[$value["UnitID"]] = $value["MAC"];
            } else {
                // $nodes[$value["Node"]] = [$value["UnitID"],$value["MAC"]];
                array_push($nodes, [$value["Node"], $value["UnitID"], $value["MAC"]]);
            }
            array_push($macs, $value["MAC"]);
        }

        foreach ($community as $value) {
            \webCommunityModifyNotify(WEB_COMM_PUB_DEL_DEV, "", $value, $userId, "");
        }
        foreach ($units as $unitId => $value) {
            \webCommunityModifyNotify(WEB_COMM_UNIT_DEL_DEV, "", $value, $userId, $unitId);
        }
        foreach ($nodes as $value) {
            $node = $value[0];
            $mac = $value[2];
            $unitId = $value[1];
            \webCommunityModifyNotify(WEB_COMM_DEL_DEV, $node, $mac, $userId, $unitId);
        }
    }

    /**
     * @name: 修改社区的设备
     */
    public function devComUpdate()
    {
        $params = [
            "data"=>"",
            "userAliasId"=>"",
            "ID"=>"",
            "AccessGroup"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $node = $data["Node"];
        $mac = $data["MAC"];
        $unitId = $data["UnitID"];
        $accessGroup = $data["AccessGroup"];
        $this->log->actionLog("#model#notify#devComUpdate#".json_encode($params));
        $grade = $data["Grade"];
        if ($grade == 1) {
            \webCommunityModifyNotify(WEB_COMM_PUB_MODIFY_DEV, $node, $mac, $userId, $unitId);
        } elseif ($grade == 2) {
            \webCommunityModifyNotify(WEB_COMM_UNIT_MODIFY_DEV, $node, $mac, $userId, $unitId);
        } else {
            \webCommunityModifyNotify(WEB_COMM_MODIFY_DEV, $node, $mac, $userId, $unitId);
        }
    }

    public function devPerAddForManage()
    {
        $params = [
            "MAC"=>"",
            "Node"=>"",
            "userAliasId"=>"",
            "userAlias"=>""
        ];
        $this->log->actionLog("#model#notify#devPerAddForManage#");
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $node = $params["Node"];
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        \webPersonalModifyNotify(WEB_PER_ADD_DEV, $node, $mac, $userId);
    }

    public function devAddForUser()
    {
        $params = [
            "MAC"=>"",
            "Node"=>"",
            "Role"=>"",
            "PcMngId"=>"",
            "UnitID"=>""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $node = $params["Node"];
        $role = $params["Role"];
        $pcMngId = $params["PcMngId"];
        $unitId = $params["UnitID"];
        $this->log->actionLog("#model#notify#devAddForUser#");
        if (in_array($role, COMROLE)) {
            \webCommunityModifyNotify(WEB_COMM_ADD_DEV, $node, $mac, $pcMngId, $unitId);
        } else {
            \webPersonalModifyNotify(WEB_PER_ADD_DEV, $node, $mac, $pcMngId);
        }
    }

    public function devPerDelete()
    {
        $params = [
            "MAC"=>"",
            "Node"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $node = $params["Node"];
        $this->log->actionLog("#model#notify#devPerDelete#");
        \webPersonalModifyNotify(WEB_PER_DEL_DEV, $node, $mac);
    }

    public function devPerUpdate()
    {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#devPerUpdate#");

        $data = $this->db->queryAllList("PersonalDevices", ["equation"=>[":ID"=>$id]])[0];
        $node = $data["Node"];
        $mac = $data["MAC"];
        \webPersonalModifyNotify(WEB_PER_MODIFY_DEV, $node, $mac);
    }
}
