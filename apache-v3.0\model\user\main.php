<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;

include_once __DIR__."/add.php";
include_once __DIR__."/afterAdd.php";
include_once __DIR__."/remove.php";
include_once __DIR__."/update.php";
include_once __DIR__."/query.php";

include_once __DIR__."/../basic/user.php";
class CUser extends \basic\CUser {
    use \model\user\add;
    use \model\user\afterAdd;
    use \model\user\remove;
    use \model\user\update;
    use \model\user\query;
}