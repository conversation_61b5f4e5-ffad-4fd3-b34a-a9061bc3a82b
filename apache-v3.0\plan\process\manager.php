<?php
namespace plan\process;

const MANAGER_PROCESS = [
    "getPMBill"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.getBill",
            
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "editPMBill"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.modifyBill"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],
    ],
    "queryCaptureForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryCaptureForPM"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "deleteCaptureForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteForCommunity"
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForCom"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
    "queryDoorLogForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryDoorLogForPM"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getPMChargeMode"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getChargeMode"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["ChargeMode"=>"ChargeMode"]
        ],
    ],
    "addPropertyWorker"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"firstLastNameDeal","params"=>["FirstName","LastName"]],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.add"
        ],[
            "type"=>"model",
            "model"=>"notify.createPropertyEmail"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ],
    ],
    "editPropertyWorker"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"propertyData.edit"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],
    ],
    "delPropertyWorker"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.delete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],
    ],
    "getPropertyWorker"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getMngTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.query"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "resetPwPropertyWorker"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"propertyData.resetpw"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessResetPw,
            "params"=>["passwd"]
        ],
    ],
    "getMngBill"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getBillInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "setMngBill"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.editBill"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],
    ],
    "checkMngPw"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.checkPw"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery
        ],
    ],
    "changeMngPw"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.changePw"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessChangePw
        ],
    ],
    "getMngOpera"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getmyOpera"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery
        ],
    ],
    "changeMngTimeZone"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.changeTimeZone"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.communityTimeUpdate",
            "dataContainer"=>"setAliasIdToId",
        ]
    ],
    "getMngChargeMode"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getChargeMode"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["ChargeMode"=>"ChargeMode","SendExpireEmailType"=>"SendExpireEmailType","SendRenew"=>"SendRenew"]
        ],
    ],
    "setMngChargeMode"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.chargeMode"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],
    ],
    "manageLogin"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"login.loginManage"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["token"=>"token","grade"=>"grade","account"=>"account","timeZone"=>"timeZone","communityID"=>"communityID","perAccount"=>"perAccount","Role"=>"Role",
            "Initialization"=>"Initialization","haveCustomer"=>"HaveCustomer","IsNew"=>"IsNew", 'Business' => 'Business']
        ]
    ],
    "queryComPerManage"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.queryComPerManage"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "changeCommunityProperty"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.changeCommunityProperty"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "queryInstallArea"=>[
        [
            "type"=>"model",
            "model"=>"manage.queryInstallArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryAllInstaller"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.queryAllInstaller"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "setManageLang"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.setLang"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "getDisChargeMode"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getDisChargeMode"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["ChargeMode"=>"ChargeMode"]
        ]
    ]
];
