<?php
require_once(dirname(__FILE__) . '/../socket.php');
function update_mac_communit_config($communitid, $mac)
{
    $data[0] = 2105;
    $data[1] = "";
    $data[2] = $mac;
    $data[3] = $communitid;
    $data[4] = 0;

    $Socket = new CWebCommunityModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    $Socket->copy($data);
}

function update_mac_installer_config($mac)
{
    $data[0] = 1016;
    $data[1] = "";
    $data[2] = $mac;
    $data[3] = 0;

    $Socket = new CWebPersonalModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_PERSONAL_MESSAGE);
    $Socket->copy($data);
}

function getDB()
{
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = "127.0.0.1";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}
const LOG_FILE2 = "/tmp/updateconfig.log";
function LOG_TRACE2($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);
    //当文件不存在时候，返回错误信息 会影响ios解析数据
    @file_put_contents(LOG_FILE2, $Now." ".$content, FILE_APPEND);
    @file_put_contents(LOG_FILE2, "\n", FILE_APPEND);
}

$db = getDB();
$sth1 = $db->prepare("select Mac,Config,MngAccountID from Devices where MngAccountID=5037 and Type=2;");
$sth1->execute();
$devices = $sth1->fetchAll(PDO::FETCH_ASSOC);
foreach ($devices as $row => $dev) {
    $mac = $dev['Mac'];
    $mngid = $dev['MngAccountID'];
    $config1 = $dev['Config'];
    LOG_TRACE2("before:$mac $mngid, $config1\n");
    
    $config="Config.Indoor.PAGE1AREA1.DisplayType = 8";
        
    $sth = $db->prepare("update Devices set Config=:Config where Mac=:Mac");
    $sth->bindParam(':Config', $config, PDO::PARAM_STR);
    $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
    //默认注释
    #$sth->execute();
    
    #update_mac_communit_config($mngid, $mac);
    sleep(3);
}
