<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 10:05:28
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";

class CPerMngSpecialCheck implements IMiddleware {
    function handle (\Closure $next) {
        global $gApp,$cLog,$cMessage;
        $userId = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $data = $db->querySList("select Role from Account where ID = :ID",[":ID"=>$userId])[0];
        $cLog->actionLog("#middle#perMngSpecialCheck#");
        $role = $data["Role"];
        if($role != 5) $cMessage->echoErrorMsg(StateNotPermissionCode5);
        $next();
    }
}