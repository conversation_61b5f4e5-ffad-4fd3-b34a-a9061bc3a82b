<?php

require_once(dirname(__FILE__) . '/funcs_common.php');

const BILL_SEND_TYPE_MESSAGE = 0;
const BILL_SEND_TYPE_EMAIL = 1;

const ORDER_TYPE_AUTO_SEND = 1;
const ORDER_TYPE_MANUAL_SEND = 2;

/**
 * 阿塞拜疆 手动通过Message+Email发送账单
 * @param string $uuid 对应订单的uuid
 */

 function ManualSendBill($uuid)
 {
    global $cLog;
    $cLog->TRACE('[ManualSendBill] bill uuid is:' . $uuid);
    $bill_info = GetCommunalFeeInfo($uuid);

    //不为手动发送的订单 直接返回
    if ($bill_info['OrderType'] != ORDER_TYPE_MANUAL_SEND)
    {
        $cLog->TRACE('[ManualSendBill]order is no manual type. return');
        return;
    }

    //发送邮件消息
    SendBillEmail($bill_info);
    //发送Message消息
    SendBillMessage($bill_info);
 }


//根据订单发送账单邮件
function SendBillEmail($bill_info)
{
    AzerManualSendBill(BILL_SEND_TYPE_EMAIL, $bill_info);
}

//根据订单发送账单Message
function SendBillMessage($bill_info)
{
    AzerManualSendBill(BILL_SEND_TYPE_MESSAGE, $bill_info);
}


function AzerManualSendBill($send_type, $bill_info)
{
    global $cLog;
    $effect_time = $bill_info['EffectTime'];
    $expire_time = $bill_info['ExpireTime'];
    $bill_uuid = $bill_info['UUID'];
    $per_account_uuid = $bill_info['PersonalAccountUUID'];
    $per_account_info = GetPerMasterAccountInfo($per_account_uuid);

    if(!$per_account_info)
    {
        $cLog->TRACE("cannot find per account info, uuid: " . $per_account_uuid);
        return;
    }

    $send_info['amount'] = $bill_info['Amount'] / 100;
    $send_info['effect_year'] = GetYear($effect_time);
    //订单有效期
    $send_info['effect_year'] = GetYear($effect_time);
    $send_info['effect_month'] = GetMonth($effect_time);
    $send_info['effect_day'] = GetDay($effect_time);
    $send_info['expire_year'] = GetYear($expire_time);
    $send_info['expire_month'] = GetMonth($expire_time);
    $send_info['expire_day'] = GetDay($expire_time);
    //支付链接
    $send_info['pay_link'] = GetPayLink($bill_uuid);

    if($send_type == BILL_SEND_TYPE_EMAIL)
    {
        $per_account_user_info = GetPerAccountUserInfo($per_account_info['UserInfoUUID']);
        if($per_account_user_info['Email']) //有邮箱才发
        {
            $send_info['email_type'] = "manual_send_bill";
            $send_info['language'] = $per_account_info['Language'];
            $send_info['project_type'] = PROJECT_TYPE_RESIDENCE;
            $send_info['email'] = $per_account_user_info['Email'];
            AzerSendEmailNotify($send_info);
        }  else
        {
            $cLog->TRACE("user email is null, user uuid:" . $per_account_uuid);
        }
    }
    else if($send_type == BILL_SEND_TYPE_MESSAGE)
    {
        //Message指定发送对象
        $send_info['account'] = $per_account_info['Account'];
        AzerSendMessageNotify($send_info);
    }
}

function AzerSendMessageNotify($message_info)
{
    global $cLog;
    $account = $message_info['account']; //Message发送对象Account

    $payload = [
        "OEM" => OEM_NAME_AZER,
        "data" => json_encode($message_info)
    ];

    $cLog->TRACE("Manual Send Azer Bill Message, User Account: $account");
    $data[] = $account;
    $data[] = json_encode($payload);

    $sendMessageNotifySocket = new CSendMessageNotifySocket();
    $sendMessageNotifySocket->setMsgID(MSG_P2A_SEND_MESSAGE_NOTIFY);
    $sendMessageNotifySocket->setMsgOEM(OEM_TYPE_AZER); //阿塞拜疆OEM类型
    $sendMessageNotifySocket->setMsgFrom(PROJECT_TYPE_RESIDENCE);
    $sendMessageNotifySocket->copy($data);               
}


 //根据timestamp类型的Date获取日期
function GetDay($date)
{
    $time_stamp = strtotime($date);
    return intval(date('d', $time_stamp));
}
//根据timestamp类型的Date获取月份，1-12对应1月-12月
function GetMonth($date)
{
    $time_stamp = strtotime($date);
    return intval(date('n', $time_stamp));
}

//根据timestamp类型的Date获取四位数年份
function GetYear($date)
{
    $time_stamp = strtotime($date);
    return intval(date('Y', $time_stamp));
}
//获取订单信息
function GetCommunalFeeInfo($uuid)
{
    global $db;
    $bill_info = $db->querySList("select Amount, PersonalAccountUUID, ProjectUUID, OrderType, EffectTime, ExpireTime, UUID
                                                        from CommunalFeeOrderList where UUID = :uuid", [":uuid"=> $uuid])[0];
    return $bill_info;
} 
//根据订单获取支付链接
function GetPayLink($uuid)
{
    return "https://" . WEB_DOMAIN . "/smartplus/BankPayRedirect.html?UUID=" . $uuid;
}
