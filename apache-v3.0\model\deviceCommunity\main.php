<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

include_once __DIR__ . "/../../util/model.php";
include_once __DIR__ . "/../../util/string.php";
include_once __DIR__ . "/../../util/computed.php";
include_once __DIR__ . "/../../util/time.php";


include_once __DIR__ . "/../basic/device.php";

class CDeviceCommunity extends \basic\CDevice
{
    /**
     * @msg: 小区添加设备
     * @service: rps,sip
     */
    public function add($isRoom = 0)
    {
        global $cMessage;
        $params = [
            "Build" => "",
            "NetGroupNumber" => "",
            "MAC" => "",
            "Relay" => "",
            "SecurityRelay"=>"",
            "Location" => "",
            "Type" => "",
            "NodeID" => "",
            "ArmingFunction" => "",
            "Builds" => "",
            "IsAllBuild" => "",
            "userAliasId" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $buildId = $params["Build"];
        $netWork = $params["NetGroupNumber"];
        $mac = $params["MAC"];
        $relay = $params["Relay"];
        $securityRelay = $params["SecurityRelay"];
        $location = $params["Location"];
        $type = $params["Type"];
        if ($isRoom == 0) {
            $ownerID = $params["NodeID"];
        } else {
            // 如果添加用户时修改设备，NodeID是Devices的ID,ID是用户ID,设备类型是室内机
            $ownerID = $params["ID"];
            $type = '2';
        }
        $armingFunction = $params["ArmingFunction"];
        $builds = $params["Builds"];
        $isAllBuild = $params["IsAllBuild"];
        $userId = $params["userAliasId"];
        $mac = strtoupper($mac);
        $this->log->actionLog("#model#deviceCommunity#add#params=" . json_encode($params));

        //检查mac地址是否存在
        // if($this->db->isExistFiled("Devices",[":MAC"=>$mac],null) || $this->db->isExistFiled("PersonalDevices",[":MAC"=>$mac],null))
        //     $cMessage->echoErrorMsg(StateMacExits);

        //根据buildid不同获取不同设备
        switch ($buildId) {
            case 'public':
                $this->addPublicDevices($mac, $location, $type, $netWork, $relay, $armingFunction, null, $isAllBuild, $builds, $securityRelay);
                break;
            default:
                if ($ownerID) {
                    $this->addOwnerDevices($mac, $location, $type, $netWork, $relay, $armingFunction, $ownerID, $securityRelay);
                } else {
                    $this->addPublicDevices($mac, $location, $type, $netWork, $relay, $armingFunction, $buildId, 0, [], $securityRelay);
                }
                break;
        }

        if ($type == 2) {
            $devices = $this->db->querySList('select count(*) from Devices where MngAccountID=:MngAccountID and Type = 2', [":MngAccountID" => $userId])[0]['count(*)'];
            if ($devices == 1) {
                $this->services["billsysUtil"]->checkPlan($userId);
            }
        }

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeDeviceTypeArray[$type], $this->env, [$mac], $account);
        $this->auditLog->setLog(AuditCodeDeviceNetGroup, $this->env, [$netWork, $mac], $account);
        $roomSip = null;
        if ($ownerID) {
            $roomSip = $this->db->querySList('select Account from PersonalAccount where ID=:ID', [':ID' => $ownerID])[0]['Account'];
        }
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        \util\computed\setSmartHomeTask(['Key' => $mac, 'Type' => 8, 'RoomSip'=>$roomSip]);
    }

    /**
     * @description 增加公共设备
     * <AUTHOR> @param $mac MAC地址
     * @param $location 位置名称
     * @param $type 设备类型
     * @param $netWork 网络组
     * @param $relay relay配置
     * @param $armingFunction 布撤防功能 0-否 1-是 smartplus显示arming的入口
     * @param $id
     * @param $isAllBuild 是否所有楼栋 0-否 1-是
     * @param $builds 公共区域公共设备，非所有楼栋时，选择楼栋的id
     * @param $securityRelay securityRelay配置
     * @return void
     * @throws \Exception
     * @lastEditor csc 2022/3/28 15:30 V6.4
     */
    public function addPublicDevices($mac, $location, $type, $netWork, $relay, $armingFunction, $id, $isAllBuild = 0, $builds = [], $securityRelay = '')
    {
        global $cMessage;
        $params = [
            "StairShow" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $stairShow = $params["StairShow"];
        $user = $params["userAlias"];
        //公共设备
        $data = $this->db->queryAllList("Account", ["equation" => [":Account" => $user]]);
        $myID = $data[0]["ID"];
        $parentID = $data[0]["ParentID"];
        // V6.3 区别办公设备和社区设备
        if ($data[0]["Grade"] == COMMUNITYGRADE) {
            $projectType = 0;
        } elseif ($data[0]["Grade"] == OFFICEGRADE) {
            $projectType = 1;
        }
        if ($id) {
            //如果往build添加设备，先检验是否是其名下build
            $data = $this->db->queryAllList("CommunityUnit", ["equation" => [":ID" => $id, ":MngAccountID" => $myID]]);
            if (count($data) == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
        } else {
            // 外围设备管理build,非全选需要验证build的所属
            if (($type == 3 || $type == 1 || $type == 0 || $type == 50) && $isAllBuild == 0 && $builds) {
                $builds = explode(";", $builds);
                $data = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $myID]]);
                foreach ($data as &$value) {
                    $value = $value["ID"];
                }
                unset($value);
                foreach ($builds as $value) {
                    if (!in_array($value, $data)) {
                        $cMessage->echoErrorMsg(StateNotPermission);
                    }
                }
            }
        }
        $id = $id ? $id : "";


        //同步到maclibrary 成功返回true，失败返回false
        $addRps = $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 1, "mngID" => $parentID, "permngID" => $myID]);
        if (!$addRps) {
            $this->addPCMng2MacLibrary($mac, $myID);
            $addRps = $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 1, "name" => "", "mngID" => $parentID, "permngID" => $myID]);
            if (!$addRps) {
                $cMessage->echoErrorMsg(StateMAC2Library);
            }
        }

        $sip = $this->services["sip"]->assignSip($user, "0");

        if ($sip == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }


        //生成Sip密码
        $sipPw = \util\string\generatePw(12);

        //freeswish插入sip
        $options = [
            "type" => $type,
            "node" => ($id === null || $id === "") ? "*******.0" : "1." . $id . ".0.0.0",
            "group" => 0,
            "community" => $myID, //小区管理员id
            "enableGroup" => 0,
            "sip" => $sip,
            "passwd" => $sipPw
        ];

        $this->services["sip"]->add2Freeswish($sip, $options);
        // 标志位
        $flag = 8 & ($isAllBuild * 8);

        $RTSPKey = \util\string\randString(6);
        $bindArray = [":MAC" => $mac, ":ArmingFunction" => $armingFunction, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":Location" => $location, ":Type" => $type, ":SipAccount" => $sip, ":MngAccountID" => $myID, ":Grade" => 1, ":RtspPwd" => $RTSPKey, ":SipPwd" => $sipPw, ":NetGroupNumber" => $netWork, ":StairShow" => $stairShow, ":Flags" => $flag, ":ProjectType" => $projectType];
        if ($id) {
            $bindArray = [":MAC" => $mac, ":ArmingFunction" => $armingFunction, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":Location" => $location, ":Type" => $type, ":SipAccount" => $sip, ":MngAccountID" => $myID, ":Grade" => 2, ":RtspPwd" => $RTSPKey, ":UnitID" => $id, ":SipPwd" => $sipPw, ":NetGroupNumber" => $netWork, ":StairShow" => $stairShow, ":Flags" => $flag, ":ProjectType" => $projectType];
        }
        $this->db->insert2List("Devices", $bindArray);
        $deviceId = $this->db->lastInsertId();

        if (\util\computed\checkAddRelayDevice($type)) {
            $relays = explode(";", $relay);
            $relayValue = 0;
            foreach ($relays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $relayValue += \util\computed\getRelayValue($key);
                }
            }

            $securityRelays = explode(";", $securityRelay);
            $securityRelayValue = 0;
            foreach ($securityRelays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $securityRelayValue += \util\computed\getRelayValue($key);
                }
            }

            // 为物业添加
            $pubKeys = $this->db->querySList("select ID from PubPrivateKey where MngAccountID=:MngAccountID and OwnerType=:OwnerType", [":MngAccountID" => $myID, ":OwnerType" => 0]);
            $pubCards = $this->db->querySList("select ID from PubRfcardKey where MngAccountID=:MngAccountID and OwnerType=:OwnerType", [":MngAccountID" => $myID, ":OwnerType" => 0]);
            foreach ($pubKeys as $pubKey) {
                $this->db->insert2List("PubPrivateKeyList", [":KeyID" => $pubKey["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            foreach ($pubCards as $pubCard) {
                $this->db->insert2List("PubRfcardKeyList", [":KeyID" => $pubCard["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            // 为住户添加
            if ($id) {
                $perKeys = $this->db->querySList("select ID from PersonalPrivateKey where UnitID=:UnitID", [":UnitID" => $id]);
                $perCards = $this->db->querySList("select ID from PersonalRfcardKey where UnitID=:UnitID", [":UnitID" => $id]);
            } else {
                $perKeys = $this->db->querySList("select ID from PersonalPrivateKey where MngAccountID=:MngAccountID", [":MngAccountID" => $myID]);
                $perCards = $this->db->querySList("select ID from PersonalRfcardKey where MngAccountID=:MngAccountID", [":MngAccountID" => $myID]);
            }
            foreach ($perKeys as $perKey) {
                $this->db->insert2List("PersonalPrivateKeyList", [":KeyID" => $perKey["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            foreach ($perCards as $perCard) {
                $this->db->insert2List("PersonalRfcardKeyList", [":KeyID" => $perCard["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
        }

        if (($type == 3 || $type == 1 || $type == 0 || $type == 50) && $id == "") {
            // var_dump($builds);
            // 最外层管理机管理楼栋,非全选要添加
            if ($isAllBuild == 0) {
                foreach ($builds as $value) {
                    $this->db->insert2List("PubDevMngList", [":DevicesID" => $deviceId, ":UnitID" => $value]);
                }
            }
        }
    }

    /**
     * @description 添加房间设备
     * <AUTHOR> @param $mac
     * @param $location
     * @param $type
     * @param $netWork
     * @param $relay
     * @param $armingFunction
     * @param $ownerID
     * @param $securityRelay
     * @return void
     * @throws \Exception
     * @lastEditor csc 2022/3/28 16:30 V6.4
     */
    public function addOwnerDevices($mac, $location, $type, $netWork, $relay, $armingFunction, $ownerID, $securityRelay = '')
    {
        global $cMessage;
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];

        //验证用户是否在其名下
        $data = $this->db->queryAllList("Account", ["equation" => [":Account" => $user]]);
        $myID = $data[0]["ID"];
        $parentID = $data[0]["ParentID"];
        // V6.3 区别办公设备和社区设备
        if ($data[0]["Grade"] == COMMUNITYGRADE) {
            $projectType = 0;
        } elseif ($data[0]["Grade"] == OFFICEGRADE) {
            $projectType = 1;
        }
        $areaMngAccount = $this->db->queryAllList("Account", ["equation" => [":ID" => $parentID]])[0]["Account"];
        //验证是否是所属的用户
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $ownerID, ":ParentID" => $myID]]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $node = $data[0]["Account"];
        $unitID = $data[0]["UnitID"];
        $roomId = $data[0]["RoomID"];

        // 使用build号+房间号
        $unitName = $this->db->querySList("select UnitName from CommunityUnit where ID = $unitID")[0]["UnitName"];

        // 6.3 办公设备Owner使用Department+员工名
        if ($projectType == 1) {
            $roomName = $data[0]["Name"];
        } elseif ($projectType == 0) {
            $roomName = $this->db->querySList("select RoomName from CommunityRoom where ID = $roomId")[0]["RoomName"];
        }

        //同步到maclibrary 成功返回true，失败返回false
        $addRps = $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 0, "name" => "$unitName-$roomName", "mngID" => $parentID, "permngID" => $myID]);
        if (!$addRps) {
            $this->addPCMng2MacLibrary($mac, $myID);
            $addRps = $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 0, "name" => "$unitName-$roomName", "mngID" => $parentID, "permngID" => $myID]);
            if (!$addRps) {
                $cMessage->echoErrorMsg(StateMAC2Library);
            }
        }

        //用户设备
        $sip = $this->services["sip"]->assignSip($user, $type == 2 ? '1' : '8');
        if ($sip == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }


        //生成Sip密码
        $sipPw = \util\string\generatePw(12);

        //查询主账号的sip群组
        $groups = $this->db->queryAllList("SipGroup2", ["equation" => [":Account" => $node]]);
        $group = $groups[0]["SipGroup"];


        $enableGroup = $this->services["sip"]->getEnableGroup($node, $type);

        //freeswish插入sip
        $options = [
            "type" => $type,
            "node" => "1." . $unitID . ".0.0." . $ownerID, //主账号id
            "group" => $group,
            "community" => $myID,
            "enableGroup" => $enableGroup, //室内机群响铃
            "sip" => $sip,
            "passwd" => $sipPw
        ];

        $this->services["sip"]->add2Freeswish($sip, $options);
        $expretime = DEFAULTEXPIRETIME;
        $RTSPKey = \util\string\randString(6);
        $bindArray = [":MAC" => $mac, ":ArmingFunction" => $armingFunction, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":Location" => $location, ":Type" => $type, ":SipAccount" => $sip, ":MngAccountID" => $myID, ":Grade" => 3, ":RtspPwd" => $RTSPKey, ":UnitID" => $unitID, ":Node" => $node, ":SipPwd" => $sipPw, ":NetGroupNumber" => $netWork, ":ExpireTime" => $expretime, ":ProjectType" => $projectType];
        $this->db->insert2List("Devices", $bindArray);

        // V5.3添加门口机刷pin和card列表
        if (\util\computed\checkAddRelayDevice($type)) {
            $relays = explode(";", $relay);
            $relayValue = 0;
            foreach ($relays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $relayValue += \util\computed\getRelayValue($key);
                }
            }

            $securityRelays = explode(";", $securityRelay);
            $securityRelayValue = 0;
            foreach ($securityRelays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $securityRelayValue += \util\computed\getRelayValue($key);
                }
            }

            $perKeys = $this->db->querySList("select ID from PersonalPrivateKey where Node = :Node", [":Node" => $node]);
            $perCards = $this->db->querySList("select ID from PersonalRfcardKey where Node = :Node", [":Node" => $node]);
            foreach ($perKeys as $perKey) {
                $this->db->insert2List("PersonalPrivateKeyList", [":KeyID" => $perKey["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            foreach ($perCards as $perCard) {
                $this->db->insert2List("PersonalRfcardKeyList", [":KeyID" => $perCard["ID"], ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }

            // V6.1 添加进个人权限组
            // 查看userAccessGroup是否存在，不存在(可能是房间没住户也可能是旧小区)不需要添加
            $userAccessGroup = $this->db->querySList('select ID from UserAccessGroup where Account=:Account', [":Account" => $node]);
            if (count($userAccessGroup) != 0) {
                $accessGroupId = $userAccessGroup[0]['ID'];
                $this->db->insert2List('UserAccessGroupDevice', [':UserAccessGroupID' => $accessGroupId, ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            // 从账户也要添加
            $subs = $this->db->querySList('select Account from PersonalAccount where ParentID=:ParentID and Role = 21', [':ParentID' => $ownerID]);
            foreach ($subs as $sub) {
                $userAccessGroup = $this->db->querySList('select ID from UserAccessGroup where Account=:Account', [":Account" => $sub['Account']]);
                if (count($userAccessGroup) != 0) {
                    $accessGroupId = $userAccessGroup[0]['ID'];
                    $this->db->insert2List('UserAccessGroupDevice', [':UserAccessGroupID' => $accessGroupId, ":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
                }
            }
        }
    }

    /**
     * @msg:
     * @service: rps,sip
     */
    public function delete($isRoom = 0)
    {
        global $cMessage;
        $params = [
            "ID" => "",
            // 社区管理员id
            "userAliasId" => "",
            "NodeID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        if ($isRoom == 0) {
            $id = $params["ID"];
        } else {
            $id = $params["NodeID"];
        }
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#deviceCommunity#delete#userId=$userId;id=$id;");

        // 6.2特殊室内机不能删除
        $isSpecialDevice = $this->db->querySList("select S.ID from DevicesSpecial S join Devices D on D.MAC = S.MAC where D.ID = :ID", [":ID" => $id]);
        if (count($isSpecialDevice) > 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]]);
        $myID = $myData[0]["ID"];
        $parentID = $myData[0]["ParentID"];
        $areaAccount = $this->db->queryAllList("Account", ["equation" => [":ID" => $parentID]])[0]["Account"];
        if (!$this->db->isExistFiled("Devices", [":ID" => $id, ":MngAccountID" => $myID], null)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $data = $this->db->queryAllList("Devices", ["equation" => [":ID" => $id]]);
        $this->log->actionLog("#model#deviceCommunity#delete#data=" . json_encode($data));
        $mac = $data[0]["MAC"];
        $sip = $data[0]["SipAccount"];
        $grade = $data[0]["Grade"];
        $roomSip = null;
        if ($grade == 3) {
            $roomSip = $data[0]["Node"];
        }
        $this->services["sip"]->sipCollecting($sip, $areaAccount);
        $this->services["sip"]->del2Freeswish($sip);
        $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 5]);
        $this->db->delete2ListWID("Devices", $id);
        $this->db->delete2ListWKey("PubAppTmpKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("PubPrivateKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("PubRfcardKeyList", "MAC", $mac);
        //$this->db->delete2ListWKey("PubRfcardKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("PersonalAppTmpKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("PersonalPrivateKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("PersonalRfcardKeyList", "MAC", $mac);
        $this->db->delete2ListWKey("Temperature", "MAC", $mac);
        $this->db->delete2ListWKey("PubDevMngList", "DevicesID", $id);
        // 6.1后数据巡检发现bug，增加删除alarm
        $this->db->delete2ListWKey("Alarms", "DevicesMAC", $mac);
        // motion
        $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 0);
        // capture
        $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 1);
        // 6.1 删除用户权限组内设备，删除公共权限组内设备
        $accesses = $this->db->querySList('select UserAccessGroupID from UserAccessGroupDevice where MAC=:MAC', [':MAC' => $mac]);
        foreach ($accesses as &$access) {
            $access = $access['UserAccessGroupID'];
        }
        unset($access);
        \util\computed\setGAppData(["AccessGroup" => $accesses]);
        $this->db->delete2ListWKey("UserAccessGroupDevice", "MAC", $mac);
        $this->db->delete2ListWKey("AccessGroupDevice", "MAC", $mac);

        \util\computed\setGAppData(["mac" => $mac, "grade" => $grade, "unitId" => $data[0]["UnitID"], "node" => $data[0]["Node"]]);
        $this->auditLog->setLog(AuditCodeDeleteDevice, $this->env, [$mac], $myData['Account']);

        if ($data[0]["Type"] == 2) {
            $devices = $this->db->querySList('select count(*) from Devices where MngAccountID=:MngAccountID and Type = 2', [":MngAccountID" => $userId])[0]['count(*)'];
            if ($devices == 0) {
                $this->services["billsysUtil"]->checkPlan($userId);
            }
        }
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        \util\computed\setSmartHomeTask(['Key' => $mac, 'Type' => 11, 'CommunityID' => $myID, 'RoomSip'=>$roomSip]);
    }

    public function afterDelete()
    {
    }


    /**
     * @msg:
     * @service: rps,sip
     */
    public function batchDelete()
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $ids = $params["ID"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#deviceCommunity#batchDelete#userId=$userId;ids=$ids");
        if (count($ids) == 0) {
            return;
        }
        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]]);
        $myID = $myData[0]["ID"];
        $parentID = $myData[0]["ParentID"];

        $where = "";
        $bindArray = [];
        foreach ($ids as $key => $id) {
            if ($key == count($ids) - 1) {
                $where .= "ID = :ID$key";
            } else {
                $where .= "ID = :ID$key OR ";
            }
            $bindArray[":ID$key"] = $id;
        }

        $data = $simtArray = $this->db->querySList("select * from Devices where ( $where ) AND MngAccountID = $myID", $bindArray);
        if (count($simtArray) !== count($ids)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        foreach ($ids as $id) {
            // 6.2特殊室内机不能删除
            $isSpecialDevice = $this->db->querySList("select S.ID from DevicesSpecial S join Devices D on D.MAC = S.MAC where D.ID = :ID", [":ID" => $id]);
            if (count($isSpecialDevice) > 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->db->delete2ListWID("Devices", $id);
            $this->db->delete2ListWKey("PubDevMngList", "DevicesID", $id);
        }
        $sips = [];
        $macs = [];
        $accesses = [];
        $areaAccount = $this->db->queryAllList("Account", ["equation" => [":ID" => $parentID]])[0]["Account"];
        $haveIndoor = false;
        $smartHomeOptions = [];
        foreach ($data as $value) {
            array_push($sips, $value["SipAccount"]);
            array_push($macs, $value["MAC"]);
            // 通知智能家居增加房间主账号字段  @LastEditors: cj
            $roomSip = null;
            if ($value['Grade'] == 3) {
                $roomSip = $value['Node'];
            }
            array_push($smartHomeOptions, ['Key' => $value["MAC"], 'Type' => 11, 'CommunityID' => $myID, 'RoomSip'=>$roomSip]);
            $this->services["sip"]->sipCollecting($value["SipAccount"], $areaAccount);
            $mac = $value["MAC"];
            $this->db->delete2ListWKey("PubAppTmpKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("PubPrivateKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("PubRfcardKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("PersonalAppTmpKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("PersonalPrivateKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("PersonalRfcardKeyList", "MAC", $mac);
            $this->db->delete2ListWKey("Temperature", "MAC", $mac);
            // 6.1后数据巡检发现bug，增加删除alarm
            $this->db->delete2ListWKey("Alarms", "DevicesMAC", $mac);
            // motion
            $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 0);
            // capture
            $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 1);
            // 6.1 删除用户权限组内设备，删除公共权限组内设备
            $accesses[$mac] = $this->db->querySList('select UserAccessGroupID from UserAccessGroupDevice where MAC=:MAC', [':MAC' => $mac]);
            foreach ($accesses[$mac] as &$access) {
                $access = $access['UserAccessGroupID'];
            }
            unset($access);
            $this->db->delete2ListWKey("UserAccessGroupDevice", "MAC", $mac);
            $this->db->delete2ListWKey("AccessGroupDevice", "MAC", $mac);

            if ($value["Type"] == 2) {
                $haveIndoor = true;
            }
            $this->auditLog->setLog(AuditCodeDeleteDevice, $this->env, [$mac], $myData['Account']);
        }
        $this->services["rps"]->modifyMap(["mac" => $macs, "type" => 5]);
        $this->services["sip"]->del2Freeswish($sips);
        \util\computed\setGAppData(["data" => $data, 'AccessGroup' => $accesses]);
        if ($haveIndoor) {
            $devices = $this->db->querySList('select count(*) from Devices where MngAccountID=:MngAccountID and Type = 2', [":MngAccountID" => $userId])[0]['count(*)'];
            if ($devices == 0) {
                $this->services["billsysUtil"]->checkPlan($userId);
            }
        }
        \util\computed\setSmartHomeTask($smartHomeOptions, false);
    }

    /**
     * @msg:
     * @service: sip
     */
    public function edit($isRoom = 0)
    {
        global $cMessage;
        $params = [
            "Location" => "",
            "NetGroupNumber" => "",
            "Type" => "",
            "ID" => "",
            "StairShow" => "",
            "Relay" => "",
            "SecurityRelay"=>"",
            "ArmingFunction" => "",
            "Builds" => "",
            "IsAllBuild" => "",
            "userAliasId" => "",
            "MAC" => "",
            "NodeID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $location = $params["Location"];
        $netWork = $params["NetGroupNumber"];
        $type = $params["Type"];

        $stairShow = $params["StairShow"];
        $relay = $params["Relay"];
        $securityRelay = $params["SecurityRelay"];
        $builds = $params["Builds"];
        $isAllBuild = $params["IsAllBuild"];
        $armingFunction = $params["ArmingFunction"];
        $userId = $params["userAliasId"];
        if ($isRoom == 0) {
            $id = $params["ID"];
        } else {
            // 如果修改用户时修改设备，NodeID是Devices的ID,ID是用户ID,设备类型是室内机
            $id = $params["NodeID"];
            $ownerID = $params["ID"];
            $isAllBuild = 0;
            $stairShow = 1;
            $builds = "";
            $type = '2';
        }
        $mac = $params["MAC"];
        $this->log->actionLog("#model#deviceCommunity#edit#params=" . json_encode($params));
        if (!$this->db->isExistFiled("Devices", [":ID" => $id, ":MngAccountID" => $userId], null)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $data = $this->db->queryAllList("Devices", ["equation" => [":ID" => $id]]);
        $oldType = $data[0]["Type"];
        $node = $data[0]["Node"];
        $sip = $data[0]["SipAccount"];
        $oldNetWord = $data[0]["NetGroupNumber"];
        $oldMAC = $data[0]["MAC"];
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        $roomSip = null;
        if ($data[0]['Grade'] == 3) {
            $roomSip = $data[0]['Node'];
        }

        // 6.2MAC可修改，MAC不变保留原有逻辑；MAC修改先删除设备，再新增,DevicesSpecial直接更新
        if ($mac !== $oldMAC) {
            // 判断是不是方案绑定的室内机
            $isSpecial = $this->db->querySList("select ID from DevicesSpecial where MAC = :MAC", [":MAC" => $oldMAC]);
            if (count($isSpecial) > 0) {
                $this->db->update2ListWID("DevicesSpecial", [":ID" => $isSpecial[0]["ID"], ':MAC' => $mac]);
                $this->log->actionLog("#model#deviceCommunity#updateDevicesSpecial#MAC=" . $mac);
            }
            // 分为修改用户信息同时修改设备信息和直接修改设备信息
            if ($isRoom == 0) {
                $this->delete();
                $this->models["notify"]->devComDel();
                $this->add();
                $this->models["notify"]->devComAddForManage();
            } else {
                $this->delete(1);
                $this->models["notify"]->devComDel();
                $this->add(1);
                $this->models["notify"]->devComAddForManage(1);
            }

            // kxl家庭设备mac变更要通知家居重置安装进度
            if ($data[0]['Grade'] == 3) {
                $homeId = $this->db->querySList('select HomeID from SmartHomeUserMap where Account=:Account', [":Account" => $node])[0]['HomeID'];
                if ($homeId) {
                    \util\computed\setSmartHomeTask(["Key" => $homeId, "Type" => 12, 'RoomSip'=>$roomSip]);
                }
            }
        } else {
            $enableGroup = $this->services["sip"]->getEnableGroup($node, $type);
            $this->services["sip"]->addMul2Freeswish([["enableGroup" => $enableGroup, "type" => $type, "sip" => $sip]]);

            // 标志位
            // $flag = $isAllBuild == 1 ? intval($data[0]["Flags"]) | ($isAllBuild * 8) : intval($data[0]["Flags"]) & ($isAllBuild * 8 + 7);
            $flag = \util\computed\bitOperation($data[0]["Flags"], $isAllBuild == 1 ? 1 : 0, 4);

            $bindArray = [":Location" => $location, ":ArmingFunction" => $armingFunction, ":Type" => $type, ":ID" => $id,
                ":NetGroupNumber" => $netWork, ":StairShow" => $stairShow, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":Flags" => $flag];
            $this->db->update2ListWID("Devices", $bindArray);

            // 最外围管理机
            if ($data[0]["Grade"] == 1) {
                // 外围设备管理build,非全选需要验证build的所属
                if ($type == 3 || $type == 1 || $type == 0 || $type == 50) {
                    if ($isAllBuild == 0 && $builds) {
                        $builds = explode(";", $builds);
                        $units = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]);
                        foreach ($units as &$value) {
                            $value = $value["ID"];
                        }
                        unset($value);
                        foreach ($builds as $value) {
                            if (!in_array($value, $units)) {
                                $cMessage->echoErrorMsg(StateNotPermission);
                            }
                        }
                    }
                    $this->db->delete2ListWKey("PubDevMngList", "DevicesID", $id);
                    // 非全选要添加
                    if ($isAllBuild == 0) {
                        foreach ($builds as $value) {
                            $this->db->insert2List("PubDevMngList", [":DevicesID" => $id, ":UnitID" => $value]);
                        }
                    }
                }
            }

            // V6.1记录权限组变化
            $accesses = [];
            $relays = explode(";", $relay);
            $relayValue = 0;
            foreach ($relays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $relayValue += \util\computed\getRelayValue($key);
                }
            }

            $securityRelays = explode(";", $securityRelay);
            $securityRelayValue = 0;
            foreach ($securityRelays as $key => $value) {
                if (explode(",", $value)[4] == 1) {
                    $securityRelayValue += \util\computed\getRelayValue($key);
                }
            }

            if ($data[0]["Grade"] == 1 || $data[0]["Grade"] == 2) {
                // V6.1 公共设备从门口机，门禁更改成室内机等，从权限组删除
                // 同时旧小区的住户授权也要删除
                if (\util\computed\checkAddRelayDevice($oldType)
                    && !\util\computed\checkAddRelayDevice($type)
                ) {
                    $accesses = $this->db->querySList('select UserAccessGroupID from UserAccessGroupDevice where MAC=:MAC', [':MAC' => $data[0]["MAC"]]);
                    foreach ($accesses as &$access) {
                        $access = $access['UserAccessGroupID'];
                    }
                    unset($access);
                    $this->db->delete2ListWKey('AccessGroupDevice', 'MAC', $data[0]["MAC"]);
                    $this->db->delete2ListWKey('PersonalPrivateKeyList', 'MAC', $data[0]["MAC"]);
                    $this->db->delete2ListWKey('PersonalRfcardKeyList', 'MAC', $data[0]["MAC"]);
                    // 公共
                    $this->db->delete2ListWKey('PubPrivateKeyList', 'MAC', $data[0]["MAC"]);
                    $this->db->delete2ListWKey('PubRfcardKeyList', 'MAC', $data[0]["MAC"]);
                } elseif (!\util\computed\checkAddRelayDevice($oldType)
                    && \util\computed\checkAddRelayDevice($type)
                ) {
                    // V6.1 公共设备室内机等更改成门口机、门禁需要加入旧小区住户授权
                    // 最外围的设备为所有住户添加
                    if ($data[0]["Grade"] == 1) {
                        $pinIds = $this->db->querySList('select ID from PersonalPrivateKey where MngAccountID = :MngAccountID', [':MngAccountID' => $userId]);
                        $cardIds = $this->db->querySList('select ID from PersonalRfcardKey where MngAccountID = :MngAccountID', [':MngAccountID' => $userId]);
                    } else {
                        $pinIds = $this->db->querySList('select ID from PersonalPrivateKey where UnitID = :UnitID', [':UnitID' => $data[0]['UnitID']]);
                        $cardIds = $this->db->querySList('select ID from PersonalRfcardKey where UnitID = :UnitID', [':UnitID' => $data[0]['UnitID']]);
                    }

                    foreach ($pinIds as $pinId) {
                        $this->db->insert2List('PersonalPrivateKeyList', [
                            ':MAC' => $data[0]['MAC'],
                            ':KeyID' => $pinId['ID'],
                            ':Relay' => $relayValue,
                            ':SecurityRelay' => $securityRelayValue
                        ]);
                    }
                    foreach ($cardIds as $cardId) {
                        $this->db->insert2List('PersonalRfcardKeyList', [
                            ':MAC' => $data[0]['MAC'],
                            ':KeyID' => $cardId['ID'],
                            ':Relay' => $relayValue,
                            ':SecurityRelay' => $securityRelayValue
                        ]);
                    }

                    // 公共部分，为物业添加
                    $pinIds = $this->db->querySList('select ID from PubPrivateKey where MngAccountID = :MngAccountID and OwnerType = 0', [':MngAccountID' => $userId]);
                    $cardIds = $this->db->querySList('select ID from PubRfcardKey where MngAccountID = :MngAccountID and OwnerType = 0', [':MngAccountID' => $userId]);

                    foreach ($pinIds as $pinId) {
                        $this->db->insert2List('PubPrivateKeyList', [
                            ':MAC' => $data[0]['MAC'],
                            ':KeyID' => $pinId['ID'],
                            ':Relay' => $relayValue,
                            ':SecurityRelay' => $securityRelayValue
                        ]);
                    }
                    foreach ($cardIds as $cardId) {
                        $this->db->insert2List('PubRfcardKeyList', [
                            ':MAC' => $data[0]['MAC'],
                            ':KeyID' => $cardId['ID'],
                            ':Relay' => $relayValue,
                            ':SecurityRelay' => $securityRelayValue
                        ]);
                    }
                }
            } else {
                // V6.1 个人设备切换设备要变更个人权限组
                // 权限组不存在可能是旧小区或者是空房间
                $userAccessGroup = $this->db->querySList('select ID from UserAccessGroup where Account=:Account', [':Account' => $node]);
                if (count($userAccessGroup) != 0) {
                    // 门口机，门禁更改成室内机等，从个人权限组删除
                    if (\util\computed\checkAddRelayDevice($oldType)
                        && !\util\computed\checkAddRelayDevice($type)
                    ) {
                        // 直接删除，主从全部
                        $this->db->exec2ListWArray(
                            'delete from UserAccessGroupDevice where MAC=:MAC',
                            [":MAC" => $data[0]["MAC"]]
                        );
                    } elseif (!\util\computed\checkAddRelayDevice($oldType)
                        && \util\computed\checkAddRelayDevice($type)
                    ) {
                        // 室内机等=>门口机，门禁，添加到个人权限组，relay全选
                        $this->db->insert2List(
                            'UserAccessGroupDevice',
                            [':UserAccessGroupID' => $userAccessGroup[0]['ID'], ':MAC' => $data[0]["MAC"], ':Relay' => $relayValue, ':SecurityRelay' => $securityRelayValue]
                        );
                        // 从账户插入
                        $subs = $this->db->querySList(
                            'select S.Account from PersonalAccount S join PersonalAccount P on P.ID = S.ParentID where P.Account = :Account and S.Role = 21',
                            [':Account' => $node]
                        );
                        foreach ($subs as $sub) {
                            $userAccessGroup = $this->db->querySList('select ID from UserAccessGroup where Account=:Account', [':Account' => $sub['Account']]);
                            $this->db->insert2List(
                                'UserAccessGroupDevice',
                                [':UserAccessGroupID' => $userAccessGroup[0]['ID'], ':MAC' => $data[0]["MAC"], ':Relay' => $relayValue, ':SecurityRelay' => $securityRelayValue]
                            );
                        }
                    }
                }
            }


            // V5.3 更改relay刷新pin和card list列表
            $mac = $data[0]["MAC"];
            $oldRelay = $data[0]["Relay"];
            $oldSecurityRelay = $data[0]["SecurityRelay"];
            $result = \util\computed\relayValueDiff($relay, $oldRelay);
            $securityResult = \util\computed\relayValueDiff($securityRelay, $oldSecurityRelay);
            if ($result !== true or $securityResult !== true) {
                // 住户全更新
                $this->db->update2ListWKey("PersonalPrivateKeyList", [":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue], "MAC");
                $this->db->update2ListWKey("PersonalRfcardKeyList", [":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue], "MAC");
                // 公共的只更新staff
                $pubKeys = $this->db->querySList("select L.ID from PubPrivateKeyList L join PubPrivateKey P on P.ID = L.KeyID where P.MngAccountID = :MngAccountID and P.OwnerType = :OwnerType and L.MAC = :MAC", [":MngAccountID" => $userId, ":OwnerType" => 0, ":MAC" => $mac]);
                $pubCards = $this->db->querySList("select L.ID from PubRfcardKeyList L join PubRfcardKey P on P.ID = L.KeyID where P.MngAccountID = :MngAccountID and P.OwnerType = :OwnerType and L.MAC = :MAC", [":MngAccountID" => $userId, ":OwnerType" => 0, ":MAC" => $mac]);
                foreach ($pubKeys as $pubKey) {
                    $this->db->update2ListWID("PubPrivateKeyList", [":ID" => $pubKey["ID"], ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
                }
                foreach ($pubCards as $pubCard) {
                    $this->db->update2ListWID("PubRfcardKeyList", [":ID" => $pubCard["ID"], ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
                }
            }

            \util\computed\setGAppData(["data" => $data[0], "AccessGroup" => $accesses]);

            $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
            if ($oldType != $type) {
                $this->auditLog->setLog(AuditCodeDeviceTypeArray[$type], $this->env, [$mac], $account);
            }
            if ($oldNetWord != $netWork) {
                $this->auditLog->setLog(AuditCodeDeviceNetGroup, $this->env, [$netWork, $mac], $account);
            }
            $this->models["notify"]->devComUpdate();
        }
    }

    public function afterEdit()
    {
        $params = [
            "data" => "",
            "userAliasId" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#deviceCommunity#afterEdit#userId=$userId;id=$id;data=" . json_encode($data));
        $grade = $data[0]["Grade"];
        // if($grade == 1)
        //     communityUpdateNodeNotify("",$userId,"",2,$data[0]["MAC"]);
        // elseif($grade == 2)
        //     communityUpdateNodeNotify("",$userId,$data[0]["UnitID"],3,$data[0]["MAC"]);
        // else
        //     communityUpdateNodeNotify($data[0]["Node"],$userId,$data[0]["UnitID"],1);
        // personnalModifyDev($id,0);
    }

    public function query()
    {
        $params = [
            "ParentID" => "",
            "userAliasId" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $ownerID = $params["ParentID"];
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        switch ($id) {
            case 'community':
                $data = $this->getCommunityDevices($userId, $offset, $rows);
                break;
            case 'all':
                $data = $this->getCommunityDevices($userId, $offset, $rows);
                break;
            case 'public':
                $data = $this->getPublicDevices($userId, $offset, $rows, null);
                break;
            default:
                if ($ownerID) {
                    $data = $this->getownerDevices($userId, $offset, $rows, $ownerID);
                } else {
                    $data = $this->getPublicDevices($userId, $offset, $rows, $id);
                }
                break;
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getCommunityDevices($userId, $offset, $row)
    {
        //获取搜索值
        $serch = $this->getSerchKey();
        $where = $serch["condition"];
        $bindArray = $serch["bindArray"];

        // V7.0判断属于哪个项目 社区/办公
        $projectGrade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID" => $userId])[0]["Grade"];
        if ($projectGrade == COMMUNITYGRADE) {
            $bindArray[":ProjectType"] = 0;
            $simtArray = $this->db->querySList("select D.*,R.RoomName,C.UnitName,A.Name,A.ID as NodeID from Devices D left join PersonalAccount A on A.Account = D.Node 
            left join CommunityUnit C on C.ID = D.UnitID left join CommunityRoom R on A.RoomID = R.ID where D.MngAccountID = $userId and 
            D.ProjectType = :ProjectType $where order by D.ID desc limit $offset,$row", $bindArray);
        } elseif ($projectGrade == OFFICEGRADE) {
            $bindArray[":ProjectType"] = 1;
            $simtArray = $this->db->querySList("select D.*,C.UnitName as DepartmentName,A.Name,A.ID as NodeID from Devices D left join PersonalAccount A on A.Account = D.Node 
            left join CommunityUnit C on C.ID = D.UnitID where D.MngAccountID = $userId and D.ProjectType = :ProjectType $where order by D.ID desc limit $offset,$row", $bindArray);
        }
        //总设备
        $total = $this->db->querySList("select count(D.ID) as total from Devices D left join PersonalAccount A on A.Account = D.Node 
        left join CommunityUnit C on C.ID = D.UnitID where D.MngAccountID = $userId and D.ProjectType = :ProjectType $where", $bindArray)[0]["total"];
        $data = [];
        $data["total"] = $total;

        $data["detail"] = $this->gulpData($simtArray, 1, $bindArray[":ProjectType"]);
        $data["row"] = $data["detail"];
        return $data;
    }

    public function getPublicDevices($userId, $offset, $row, $unitID)
    {
        $serch = $this->getSerchKey();
        $where2 = $serch["condition"];
        $bindArray = $serch["bindArray"];
        //公共设备
        $where = "AND D.Grade = 1";
        if ($unitID) {
            $where = "AND D.Grade = 2 AND D.UnitID = :UnitID";
            $bindArray[":UnitID"] = $unitID;
        }
        // V7.0判断属于哪个项目 社区/办公
        $projectGrade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID" => $userId])[0]["Grade"];
        if ($projectGrade == COMMUNITYGRADE) {
            $bindArray[":ProjectType"] = 0;
        } elseif ($projectGrade == OFFICEGRADE) {
            $bindArray[":ProjectType"] = 1;
        }
        $total = $this->db->querySList("select count(D.ID) as total from Devices D where D.MngAccountID = $userId and D.ProjectType = :ProjectType $where $where2", $bindArray)[0]["total"];

        $data["total"] = $total;
        $simtArray = $this->db->querySList("select D.* from Devices D where D.MngAccountID = $userId and D.ProjectType = :ProjectType $where $where2 order by D.ID desc limit $offset,$row", $bindArray);
        $data["detail"] = $this->gulpData($simtArray, 1, $bindArray[":ProjectType"]);
        $data["row"] = $data["detail"];
        return $data;
    }

    public function getownerDevices($userId, $offset, $rows, $ownerID, $brand = 0)
    {
        global $cMessage;
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $ownerID, ":ParentID" => $userId]]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $serch = $this->getSerchKey();
        $where = $serch["condition"];
        $bindArray = $serch["bindArray"];
        $bindArray[":ID"] = $ownerID;
        // 获取全部第三方品牌设备
        if ($brand === 'all') {
            $where .= ' AND D.Brand != :Brand';
            $bindArray[':Brand'] = 0;
        } else {
            $where .= ' AND D.Brand = :Brand';
            $bindArray[':Brand'] = $brand;
        }
        $data = [];
        $total = $this->db->querySList("select count(*) as total from Devices D left join PersonalAccount A on A.Account = D.Node where A.ID = :ID $where", $bindArray)[0]["total"];
        $data["total"] = $total;
        $simtArray = $this->db->querySList("select D.*,A.Name from Devices D left join PersonalAccount A on A.Account = D.Node where A.ID = :ID $where order by D.ID desc limit $offset,$rows", $bindArray);
        $data["detail"] = $this->gulpData($simtArray, 1);
        $data["row"] = $this->gulpData($simtArray);
        return $data;
    }

    public function getSerchKey()
    {
        //搜索选项
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [];
        switch ($serchKey) {
            case 'MAC':
                $where = "AND D.MAC like :SerchValue";
                $bindArray[":SerchValue"] = "%$serchValue%";
                break;
            case 'Location':
                $where = "AND D.Location like :SerchValue";
                $bindArray[":SerchValue"] = "%$serchValue%";
                break;
            case 'SipAccount':
                $where = "AND D.SipAccount like :SerchValue";
                $bindArray[":SerchValue"] = "%$serchValue%";
                break;
            case 'Owner':
                $where = "AND A.Name like :SerchValue";
                if ($serchValue === "") {
                    $where = "AND (A.Name like :SerchValue or A.Name is null)";
                }
                $bindArray[":SerchValue"] = "%$serchValue%";
                break;
        }
        return ["condition" => $where, "bindArray" => $bindArray];
    }

    public function gulpData($data, $type = 0, $projectType = 0)
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $deviceIds = [];
        $homeIds = [];
        $userId = $params["userAliasId"];
        if ($projectType == 0) {
            $Parts = "Builds";
            $PartNames = "BuildNames";
            $IsAllPart = "IsAllBuild";
        } elseif ($projectType == 1) {
            $Parts = "Departments";
            $PartNames = "DepartmentNames";
            $IsAllPart = "IsAllDepartment";
        }
        foreach ($data as &$value) {
            unset($value["PrivatekeyMD5"]);
            unset($value["RfidMD5"]);
            unset($value["ConfigMD5"]);
            unset($value["ContactMD5"]);
            unset($value["SipPwd"]);
            unset($value["RtspPwd"]);
            unset($value["outerIP"]);
            unset($value["AuthCode"]);
            // V6.5.1 增加Floor字段
            if ($value['Floor'] !== '' && $value['RoomName'] !== null) {
                $value['RoomName'] = $value['RoomName'].' ('.MSGTEXT['floor'].' '.$value['Floor'].')';
            }
            $value["SipType"] = ["UDP", "TCP", "TLS"][$value["SipType"]];
            $value[$Parts] = [];
            //v6.1修改，PM info需要
            $value[$PartNames] = [];
            // 计算IsAllBuild和Builds
            $value[$IsAllPart] = (intval($value["Flags"]) & 8) >> 3;
            if ($value["Grade"] == 1 && ($value["Type"] == 3 || $value["Type"] == 1 || $value["Type"] == 0 || $value["Type"] == 50) && $value["IsAllBuild"] == 0) {
                //v6.1修改，PM info需要
                $builds = $this->db->querySList("select PDM.UnitID,CU.UnitName from PubDevMngList PDM join CommunityUnit CU on PDM.UnitID =CU.ID where DevicesID = :DevicesID", [":DevicesID" => $value["ID"]]);
                foreach ($builds as $build) {
                    array_push($value[$Parts], $build["UnitID"]);
                    array_push($value[$PartNames], $build["UnitName"]);
                }
            }
            // 6.2住宅高级方案里有室内机方案
            if ($projectType == 0) {
                $value["Special"] = $this->db->querySList("select count(ID) from DevicesSpecial where MAC = :MAC", [":MAC" => $value["MAC"]])[0]["count(ID)"];
            }
            // if (ABLE_SMART_HOME) {
            //     $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            //     $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            //     if ($smartHomeSwitch == 1) {
            //         $deviceId = $this->db->querySList("select SmartHomeUUID from SmartHomeDeviceMap where MAC = :MAC", [":MAC" => $value["MAC"]])[0]["SmartHomeUUID"];
            //         $homeId = $this->db->querySList("select HomeID from SmartHomeUserMap where Account = :Account", [":Account" => $value["Node"]])[0]["HomeID"];
            //         array_push($deviceIds, $deviceId);
            //         array_push($homeIds, $homeId);
            //     }
            // }
            $value["Special"] = $this->db->querySList("select count(ID) from DevicesSpecial where MAC = :MAC", [":MAC" => $value["MAC"]])[0]["count(ID)"];
        }
        unset($value);
        // if (count($deviceIds) != 0) {
        //     $actives = \util\smartHome\getDeviceState($deviceIds);
        //     $homes = \util\smartHome\getConstructions($homeIds);
        //     foreach ($data as $key => &$value) {
        //         $value["Active"] = $actives[$key] == 1 && $homes[$key]['state'] == 4 ? 0 : 1;
        //     }
        //     unset($value);
        // }
        return $data;
    }

    public function getAllPublicDevice()
    {
        $params = [
            "Key" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $key = $params["Key"];
        $userId = $params["userAliasId"];
        $data = $this->db->querySList("select * from Devices where MngAccountID = :MngAccountID and (Grade = 1 or Grade = 2) and (MAC like :Key or Location like :Key)", [":MngAccountID" => $userId, ":Key" => "%$key%"]);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getAllPubForKeyChoose()
    {
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->querySList(
            "select D.MAC,D.Location,U.UnitName,D.Relay,D.SecurityRelay,D.Status,D.Type,D.Grade from Devices D left join CommunityUnit U on U.ID = D.UnitID where D.MngAccountID = :MngAccountID and D.Grade != 3 and D.Type in (0,1,50) order by D.Grade",
            [":MngAccountID" => $userId]
        );

        \util\computed\setGAppData(["data" => $data]);
    }

    public function getAllForKeyChoose()
    {
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->querySList(
            "select D.MAC,D.Location,U.UnitName from Devices D left join CommunityUnit U on U.ID = D.UnitID where D.MngAccountID = :MngAccountID",
            [":MngAccountID" => $userId]
        );
        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryForPM()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => "",
            "Build" => "",
            "Room" => "",
            "Type" => "",
            "Status" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $buildID = $params["Build"];
        $roomID = $params["Room"];
        $type = $params["Type"];
        $status = $params["Status"];

        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "and ProjectType = :ProjectType";
        $bindArray = [":MngAccountID" => $userId];
        // V7.0判断属于哪个项目 社区/办公
        $projectGrade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID" => $userId])[0]["Grade"];
        if ($projectGrade == COMMUNITYGRADE) {
            $bindArray[":ProjectType"] = 0;
        } elseif ($projectGrade == OFFICEGRADE) {
            $bindArray[":ProjectType"] = 1;
        }
        switch ($serchKey) {
            case 'MAC':
                $where .= " and D.MAC like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Location':
                $where .= " and D.Location like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Owner':
                $where .= " and P.Name like :Key";
                if ($serchValue == "") {
                    $where .= " and (P.Name like :Key or P.Name is null)";
                }
                $bindArray[":Key"] = "%$serchValue%";
                break;
            default:
                break;
        }
        if ($buildID != "all") {
            $where .= " and D.UnitID = :UnitID";
            $bindArray[":UnitID"] = $buildID;
        }
        if ($roomID != "all") {
            $where .= " and P.RoomID = :RoomID";
            $bindArray[":RoomID"] = $roomID;
        }
        if ($status != "all") {
            $where .= " and D.Status = :Status";
            $bindArray[":Status"] = $status;
        }
        if ($type != "all") {
            $where .= " and D.Type = :Type";
            $bindArray[":Type"] = $type;
        }

        $data = [];
        if ($bindArray[":ProjectType"] == 0) {
            $sql = "select D.ID from Devices D left join PersonalAccount P on D.Node = P.Account left join CommunityUnit U on D.UnitID = U.ID left join 
                    CommunityRoom R on P.RoomID = R.ID where D.MngAccountID = :MngAccountID $where";
            $data["total"] = count($this->db->querySList($sql, $bindArray));

            $sql = "select D.ID,D.Relay,D.SecurityRelay,D.Location,D.MAC,D.LastConnection,D.Type,D.Status,U.UnitName,D.StairShow,R.RoomName,P.Name,D.Grade from Devices D
            left join PersonalAccount P on D.Node = P.Account left join CommunityUnit U on D.UnitID = U.ID left join CommunityRoom R on P.RoomID = R.ID
            where D.MngAccountID = :MngAccountID $where order by D.ID desc limit $offset,$rows";
        } elseif ($bindArray[":ProjectType"] == 1) {
            $sql = "select D.ID from Devices D left join PersonalAccount P on D.Node = P.Account left join 
                    CommunityUnit U on D.UnitID = U.ID where D.MngAccountID = :MngAccountID $where";
            $data["total"] = count($this->db->querySList($sql, $bindArray));

            $sql = "select D.ID,D.Relay,D.SecurityRelay,D.Location,D.MAC,D.LastConnection,D.Type,D.Status,U.UnitName as DepartmentName,D.StairShow,P.Name as Owner,D.Grade from Devices D
                    left join PersonalAccount P on D.Node = P.Account left join CommunityUnit U on D.UnitID = U.ID 
                    where D.MngAccountID = :MngAccountID $where order by D.ID desc limit $offset,$rows";
        }

        $data["row"] = $data["detail"] = $this->db->querySList($sql, $bindArray);
        $data["row"] = \util\time\setQueryTimeZone($data["row"], $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function editPM()
    {
        global $cMessage;
        $params = [
            "Location" => "",
            "ID" => "",
            "StairShow" => "",
            "Relay" => "",
            "SecurityRelay" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $location = $params["Location"];
        $id = $params["ID"];
        $stairShow = $params["StairShow"];
        $userId = $params["userAliasId"];
        $relay = $params["Relay"];
        $securityRelay = $params["SecurityRelay"];
        $this->log->actionLog("#model#deviceCommunity#editPM#userId=$userId;id=$id;relay=$relay;securityRelay=$securityRelay;stairShow=$stairShow");
        $data = $this->db->queryAllList("Devices", ["equation" => [":ID" => $id, ":MngAccountID" => $userId]]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        // 6.1 室内机也有local relay
        // $relay = $data[0]["Type"] == 2 ? "" : $relay;
        $this->db->update2ListWID("Devices", [":ID" => $id, ':Location' => $location, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":StairShow" => $stairShow]);

        // V5.3更新设备列表
        $oldRelay = $data[0]["Relay"];
        $oldSecurityRelay = $data[0]["SecurityRelay"];
        $mac = $data[0]["MAC"];
        $result = \util\computed\relayValueDiff($relay, $oldRelay);
        $securityResult = \util\computed\relayValueDiff($relay, $oldSecurityRelay);
        if ($result !== true or $securityResult !== true) {
            $relays = explode(';', $relay);
            $relayValue = 0;
            foreach ($relays as $key => $value) {
                if (explode(',', $value)[4] == 1) {
                    $relayValue += \util\computed\getRelayValue($key);
                }
            }
            $securityRelays = explode(';', $securityRelay);
            $securityRelayValue = 0;
            foreach ($securityRelays as $key => $value) {
                if (explode(',', $value)[4] == 1) {
                    $securityRelayValue += \util\computed\getRelayValue($key);
                }
            }

            // 住户全更新
            $this->db->update2ListWKey("PersonalPrivateKeyList", [":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue], "MAC");
            $this->db->update2ListWKey("PersonalRfcardKeyList", [":MAC" => $mac, ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue], "MAC");
            // 公共的只更新staff
            $pubKeys = $this->db->querySList("select L.ID from PubPrivateKeyList L join PubPrivateKey P on P.ID = L.KeyID where P.MngAccountID = :MngAccountID and P.OwnerType = :OwnerType and L.MAC = :MAC", [":MngAccountID" => $userId, ":OwnerType" => 0, ":MAC" => $mac]);
            $pubCards = $this->db->querySList("select L.ID from PubRfcardKeyList L join PubRfcardKey P on P.ID = L.KeyID where P.MngAccountID = :MngAccountID and P.OwnerType = :OwnerType and L.MAC = :MAC", [":MngAccountID" => $userId, ":OwnerType" => 0, ":MAC" => $mac]);
            foreach ($pubKeys as $pubKey) {
                $this->db->update2ListWID("PubPrivateKeyList", [":ID" => $pubKey["ID"], ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
            foreach ($pubCards as $pubCard) {
                $this->db->update2ListWID("PubRfcardKeyList", [":ID" => $pubCard["ID"], ":Relay" => $relayValue, ":SecurityRelay" => $securityRelayValue]);
            }
        }

        \util\computed\setGAppData(["data" => $data[0]]);
    }

    public function queryInfo()
    {
        $params = [
            "ID" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $data = $this->db->querySList("select D.*,U.UnitName,R.RoomName,R.Floor,A.Name as Owner from Devices D left join CommunityUnit U on D.UnitID = U.ID 
					left join PersonalAccount A on D.Node = A.Account left join CommunityRoom R on R.ID = A.RoomID 
                    where D.ID = :ID and D.MngAccountID = :MngAccountID", [":ID" => $id, ":MngAccountID" => $userId]);

        // v6.1PM详情公共设备需要显示绑定的楼栋
        $data = $this->gulpData($data);

        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryDoor()
    {
        $params = [
            "userAliasId" => "",
            "Build" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $build = $params["Build"];

        // 选择楼栋时，只能选择有管理这栋楼的设备
        $sql = "select D.MAC,D.Location,U.UnitName,D.Status,D.Relay,D.SecurityRelay,D.Grade from Devices D left join CommunityUnit U on D.UnitID = U.ID 
        where D.MngAccountID = :MngAccountID and D.Type in (0,1,50) and D.Grade != 3";
        $bindArray = [":MngAccountID" => $userId];
        if ($build) {
            $sql = "select MAC,Location,'' as UnitName,Status,Relay,SecurityRelay,Grade from Devices
            where (
                ID in (select DevicesID from PubDevMngList where UnitID = :UnitID)
                or Flags & 8 = 8
            )
            and MngAccountID = :MngAccountID and Type in (0,1,50)
            and Grade = 1
            union 
            select D.MAC,D.Location,U.UnitName,D.Status,D.Relay,D.SecurityRelay,D.Grade from Devices D left join CommunityUnit U on D.UnitID = U.ID where D.MngAccountID = :MngAccountID and D.Type in (0,1,50)
             and D.Grade = 2 and D.UnitID = :UnitID";
            $bindArray[":UnitID"] = $build;
        }
        $data = $this->db->querySList($sql, $bindArray);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryForArea()
    {
        // V7.0增加type，区别获取哪个项目设备 0住宅 1办公
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "type" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $type = $params["type"];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        if ($type == 0) {
            $where = "and C.Grade = :Grade and D.ProjectType = 0";
            $bindArray[":Grade"] = COMMUNITYGRADE;
        } elseif ($type == 1) {
            $where = "and C.Grade = :Grade and D.ProjectType = 1";
            $bindArray[":Grade"] = OFFICEGRADE;
        }
        $bindArray[":parentID"] = $userId;
        switch ($serchKey) {
            case 'MAC':
                $where = "$where AND D.MAC LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'IPAddress':
                $where = "$where AND D.IPAddress LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'SipAccount':
                $where = "$where AND D.SipAccount LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Installer':
                $where = "$where AND A.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Project':
                $where = "$where AND C.Location LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $total = $this->db->querySList("SELECT count(D.ID) as total FROM Devices D left JOIN Account C ON C.ID = D.MngAccountID join Account A on C.ManageGroup = A.ID WHERE C.ParentID = :parentID $where", $bindArray)[0]["total"];
        $data = $this->db->querySList("SELECT  D.*,C.Location as Project,A.Account as Installer FROM Devices D JOIN Account C 
        ON C.ID = D.MngAccountID join Account A on C.ManageGroup = A.ID WHERE C.ParentID = :parentID $where order by D.ID desc
        limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$val) {
            unset($val["PrivatekeyMD5"]);
            unset($val["RfidMD5"]);
            unset($val["ConfigMD5"]);
            unset($val["ContactMD5"]);
            unset($val["SipPwd"]);
            unset($val["RtspPwd"]);
            unset($val["outerIP"]);
            unset($val["AuthCode"]);
            $val["SipType"] = ["UDP", "TCP", "TLS"][$val["SipType"]];
        }
        $rows = [];
        for ($i = 0; $i < count($data); $i++) {
            $row = $data[$i];
            $curDevice = array();
            $curDevice['ID'] = $row['ID'];
            $curDevice['Project'] = $row['Project'];
            $curDevice['MAC'] = $row['MAC'];
            $curDevice['Installer'] = $row['Installer'];
            $curDevice['IP Address'] = $row['IPAddress'];
            $curDevice['Type'] = $row['Type'];
            $curDevice['Status'] = $row['Status'];
            $curDevice['SipAccount'] = $row['SipAccount'] ? $row['SipAccount'] : '--';
            $curDevice['Last Connected'] = $row['LastConnection'] ? $row['LastConnection'] : '--';
            array_push($rows, $curDevice);
        }

        \util\computed\setGAppData(["data" => ["total" => $total, "detail" => $data, "row" => $rows]]);
    }

    public function queryForSuper()
    {
        // V7.0增加type，区别获取哪个项目设备 0住宅 1办公
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "type" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $type = $params["type"];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        // 项目类型区分
        if ($type == 0) {
            $where = "where A.Grade = :Grade and D.ProjectType = 0 ";
            $bindArray[":Grade"] = COMMUNITYGRADE;
        } elseif ($type == 1) {
            $where = "where A.Grade = :Grade and D.ProjectType = 1 ";
            $bindArray[":Grade"] = OFFICEGRADE;
        }
        switch ($serchKey) {
            case 'MAC':
                $where .= "and D.MAC LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'IPAddress':
                $where .= "and D.IPAddress LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'SipAccount':
                $where .= "and D.SipAccount LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Installer':
                $where .= "and A2.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Project':
                $where .= "and A.Location LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Manage':
                $where .= "and B.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $total = $this->db->querySList("SELECT count(*) as total FROM Devices D left JOIN Account A ON A.ID = D.MngAccountID
        join Account A2 on A2.ID = A.ManageGroup left join Account B on B.ID = A.ParentID $where", $bindArray)[0]["total"];

        $data = $this->db->querySList("SELECT  D.*,A2.Account as Installer,A.Location as Project,B.Account FROM Devices D 
        left JOIN Account A ON A.ID = D.MngAccountID join Account A2 on A2.ID = A.ManageGroup
        left join Account B on B.ID = A.ParentID $where order by D.ID desc limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

        foreach ($data as &$val) {
            unset($val["PrivatekeyMD5"]);
            unset($val["RfidMD5"]);
            unset($val["ConfigMD5"]);
            unset($val["ContactMD5"]);
            unset($val["SipPwd"]);
            unset($val["RtspPwd"]);
            unset($val["outerIP"]);
            unset($val["AuthCode"]);
            $val["SipType"] = ["UDP", "TCP", "TLS"][$val["SipType"]];
        }
        $rows = [];

        for ($i = 0; $i < count($data); $i++) {
            $row = $data[$i];
            $curDevice = array();
            $curDevice['ID'] = $row['ID'];
            $curDevice['Manage'] = $row['Account'];
            $curDevice['Installer'] = $row['Installer'];
            $curDevice['Project'] = $row['Project'];
            $curDevice['MAC'] = $row['MAC'];
            $curDevice['IP Address'] = $row['IPAddress'];
            $curDevice['Type'] = $row['Type'];
            $curDevice['Status'] = $row['Status'];
            $curDevice['SipAccount'] = $row['SipAccount'] ? $row['SipAccount'] : '--';
            $curDevice['Last Connected'] = $row['LastConnection'] ? $row['LastConnection'] : '--';
            array_push($rows, $curDevice);
        }

        \util\computed\setGAppData(["data" => ["total" => $total, "detail" => $data, "row" => $rows]]);
    }

    public function queryDevWithType()
    {
        $params = [
            "Type" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $user = $params["userAlias"];

        $deviceTabel = "Devices";
        $data = $this->models["devicePersonal"]->queryAllDevWithType($type, $deviceTabel, $user);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getUserDoorDevices()
    {
        $params = [
            "ID" => "",
            "userAliasId" => "",
            "PID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $roomId = $params["ID"];
        $communityId = $params["userAliasId"];
        $personalAccountID = $params['PID'];

        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', ['ID' => $communityId])[0]['Grade'];
        if ($projectGrade == COMMUNITYGRADE) {
            $userData = $this->db->querySList(
                'select P.Account,U.UnitName from PersonalAccount P join CommunityUnit U on P.UnitID=U.ID where P.ParentID=:ParentID and P.RoomID=:ID',
                [':ParentID' => $communityId, ':ID' => $roomId]
            )[0];
        } elseif ($projectGrade == OFFICEGRADE) {
            $userData = $this->db->querySList(
                'select P.Account,U.UnitName from PersonalAccount P join CommunityUnit U on P.UnitID=U.ID where P.ParentID=:ParentID and P.ID=:ID',
                [':ParentID' => $communityId, ':ID' => $personalAccountID]
            )[0];
        }
        $account = $userData['Account'];
        $build = $userData['UnitName'];
        $data = $this->db->querySList('select ID,MAC,Type,Relay,SecurityRelay,Location,Status from Devices where Node=:Node and Type in (0,1,50)', [':Node' => $account]);
        foreach ($data as &$val) {
            $val['UnitName'] = $build;
            $relays = explode(';', $val['Relay']);
            $temp = [];
            foreach ($relays as $key => $relay) {
                if (explode(',', $relay)[4] == 1) {
                    array_push($temp, $key);
                }
            }
            $val['ValidRelayKey'] = implode(';', $temp);

            $securityRelays = explode(';', $val['SecurityRelay']);
            $temp = [];
            foreach ($securityRelays as $key => $securityRelay) {
                if (explode(',', $securityRelay)[4] == 1) {
                    array_push($temp, $key);
                }
            }
            $val['ValidSecurityRelayKey'] = implode(';', $temp);
        }
        unset($val);

        \util\computed\setGAppData(["data" => $data]);
    }
}
