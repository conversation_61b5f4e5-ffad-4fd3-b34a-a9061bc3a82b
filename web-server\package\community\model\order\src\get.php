<?php
namespace package\community\model\order\src;

trait Get
{
    public function getPriceToDay()
    {
        $params = ['NextTime', 'Users', 'userId', 'role', 'VideoSites', 'ThirdLockUUIDs'];
        list($nextTime, $users, $userId, $role, $videoSites, $thirdLockUUIDs) = $this->getParams($params);
        $users = json_decode($users, true);
        $videoSitesArr = json_decode($videoSites, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);
        $this->loadUtil('order', true);

        $payerType = 2;
        if ($role === RPROPERTYMANAGE) {
            $payerType = 1;
        } elseif ($role === RAREAGRADE) {
            $payerType = 3;
        } elseif ($role === RSUBDISTRIBUTOR) {
            $payerType = 4;
        }

        $price = $this->utils->_common->order->getPriceToDay($nextTime, $users, PAY_TYPE_MULTIPLE, $payerType, $userId, $videoSitesArr, $thirdLockUUIDsArr);
        return ['Price' => $price];
    }

    /**
     * @description:ins获取订单列表
     * @author:lwj 2022/11/30 10:56 V6.6
     * @lastEditor: lwj 2022/11/30 10:56 V6.6
     * @param:
     * @return array
     */
    public function getListForIns()
    {
        $params = [PROXY_ROLE['projectId'], PROXY_ROLE['installerId'], 'Community', 'Type:payment.communitytype', 'Status', 'Key', 'TimeZone', 'CustomizeForm'];
        list($projectId, $installer, $community, $type, $status, $key, $timeZone, $customizeForm) = $this->getParams($params);
        list($offset, $rows) = $this->getParamsLimitAndSearch();

        $where = 'where IsDelete = 0 and PayerType = :PayerType ';
        $bindArray = [':PayerType' => PAYER_TYPE_INS];

        if(!empty($key)){
            $where .= ' and OrderNumber like :Key ';
            $bindArray[':Key' ] = "%$key%";
        }
        $this->loadUtil('order', true);
        $typeArr = explode(',', $type);
        $typeWhere = [];
        foreach ($typeArr as $type) {
            if ($type !== 'all' && $type !== null) {
                if ($type == PAYMENT_SEARCH_TYPE['AutoRenewVideoStorage']) {
                    // 包含自动续费,且必须存在视频存储(10,11)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 9)) != 0 OR (MixType & (1 << 10)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewApp']) {
                    // 包含自动续费，且必须包含(2,4,6,8)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 1)) != 0 OR (MixType & (1 << 3)) != 0 OR (MixType & (1 << 7)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewRentManager']) {
                    // 包含自动续费，且必须包含rentmanage(12)
                    $tmpWhere = "SubscriptionUUID != '' And (MixType & (1 << 11)) != 0";
                }  elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewThirdLock']) {
                    // 包含自动续费，且必须包含三方锁(15,17)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 14)) != 0 OR (MixType & (1 << 16)) != 0)";
                } else {
                    // 排除自动续费
                    $tmpWhere = "SubscriptionUUID = ''";
                }
                //获得MixType
                $mixType = $this->utils->_common->order->getOrderListType($type);
                $tmpWhere .= $this->utils->_common->order->getMixTypeSqlQuery($mixType);
                $typeWhere[] = '(' . $tmpWhere . ')';
            }
        }
        if (!empty($typeWhere)) {
            $where .= ' and (' . implode(' or ', $typeWhere) . ')';
        }

        if ($status !== 'all' && $status !== null) {
            $where .= ' and Status = :Status';
            $bindArray[':Status'] = $status;
        }

        $orderListTable = PROXY_TABLES['orderList'];
        $orderEndUserListTable = PROXY_TABLES['orderEndUserList'];
        $thirdLockListTable = PROXY_TABLES['orderThirdLockList'];
        $accountTable = PROXY_TABLES['account'];

        $this->loadUtil('account', true);
        if($community === 'all' || $community === null){
            $manages = $this->db->querySList(
                "select ID from $accountTable where ManageGroup = :ManageGroup",
                [':ManageGroup' => $installer]
            );
            $manageId = array_column($manages, 'ID');
            if (count($manages) !== 0) {
                $manageId = implode(',', $manageId);
                $where .= " and InstallID in ($manageId)";
            } else {
                $where .= ' and 1=0';
            }
        }else{
            // V6.5.0 兼容批量支付小区检索包括ins,dis和super
            $manageInfo = $this->utils->_common->account->getManagerInfo($community);
            $orderIds = $this->db->querySList(
                "select OrderID from $orderEndUserListTable where ProjectUUID = :ProjectUUID",
                [':ProjectUUID' => $manageInfo['UUID']]
            );
            $thirdLockOrderIds = $this->db->querySList(
                "select OrderID from $thirdLockListTable where ProjectUUID = :ProjectUUID",
                [':ProjectUUID' => $manageInfo['UUID']]
            );
            $orderIds = array_merge($orderIds, $thirdLockOrderIds);
            if (count($orderIds) > 0) {
                $orderIds = implode(',', array_unique(array_column($orderIds, 'OrderID')));
                $where .= " and (InstallID = :InstallID or ID in ($orderIds))";
            } else {
                $where .= ' and InstallID = :InstallID';
            }
            $bindArray[':InstallID'] = $community;
        }

        // 搜索条件：insId=小区ID payId=小区 or insId
        $manages = $this->db->querySList(
            "select A.ID, A.Grade from $accountTable A join $accountTable B 
            on A.ManageGroup=B.ManageGroup where B.ID=:ID",
            [':ID' => $projectId]
        );

        $personalManageId = 0;
        $manageIds = [];
        foreach ($manages as $manage) {
            if (intval($manage['Grade']) === COMMUNITYGRADE || intval($manage['Grade']) === PERSONGRADE) {
                $manageIds[] = $manage['ID'];

                if (intval($manage['Grade']) === PERSONGRADE) {
                    $personalManageId = $manage['ID'];
                }
            }
        }
        // 6.5.0 修复批量不展示单住户订单列表, 单住户的订单为非批量且InstallID为InsID
        $manageId = implode(',', $manageIds);

        $where .= " and AccountID in ($manageId) 
            and (ID not in (select ID from $orderListTable where (IsBatch = 0 and InstallID = $personalManageId) or ProjectType=1))";

        //v6.7.1去除办公的数据
        $disID = $this->dao->account->selectByKey('ID', $installer, 'ParentID')[0]['ParentID'];
        $officeList = $this->dao->account->selectByArray([['Grade' , OFFICEGRADE], ['ParentID' , $disID]], 'ID');
        if (count($officeList) !== 0) {
            $officeIDs = array_column($officeList, 'ID');
            $officeIDs = implode(',', $officeIDs);
            $where .= " and InstallID not in ($officeIDs) ";
        }
        // 排除新办公的类型
        $where .= "and (ProjectType <> 4)";

        $total = $this->db->querySList(
            "select count(*) from $orderListTable $where",
            $bindArray
        )[0]['count(*)'];
        $orderDatas = $this->db->querySList(
            "select * from $orderListTable $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        $this->loadUtil('order', true);
        $statusMsgList = $this->utils->_common->order->getStatusMsgList();
        $payTypeMsgList = $this->utils->_common->order->getPayTypeMsgList();
        $payerMsgList = $this->utils->_common->order->getPayerMsgList();
        foreach ($orderDatas as &$value) {
            $endData = $this->db->querySList(
                "select ID,ProjectUUID from $thirdLockListTable where OrderID = :OrderID union all 
                 select ID,ProjectUUID from $orderEndUserListTable where OrderID = :OrderID ",
                [':OrderID' => $value['ID']]
            );
            $projectUUID = array_unique(array_column($endData, 'ProjectUUID'));
            $value['Projects'] = '';
            $count = count($projectUUID);
            if ($count > 0) {
                $projects = [];
                list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($projectUUID);
                if(!empty($uuidBindStr) && !empty($uuidBindArray)){
                    $projects = $this->db->querySList(
                        "select Location from $accountTable where UUID in ($uuidBindStr)",
                        $uuidBindArray
                    );
                }
                $value['Projects'] = implode(',', array_column($projects, 'Location'));
            }

            $value['AptNumber'] = count($endData);
            $value['Community'] = '';
            $pcMngData = $this->db->querySList(
                "select Account,Grade,ManageGroup,Location from $accountTable where ID = :ID and ManageGroup != :ID",
                [':ID' => $value['InstallID']]
            );
            if (count($pcMngData) !== 0) {
                $pcMngData = $pcMngData[0];
                $value['Community'] = $pcMngData['Location'];
            }

            if ($value['PayerType'] === strval(PAYER_TYPE_INS)) {
                $value['Payer'] = $this->db->querySList(
                    "select A.Account from $accountTable A
                 join $accountTable B on A.ID=B.ManageGroup where B.ID=:ID",
                    [':ID' => $value['AccountID']]
                )[0]['Account'];
            }
            $value['Projects'] = $value['Projects'] === '' ? $value['Community'] : $value['Projects'];

            $mixTypeArr = $this->share->util->getBitPositions($value['MixType']);
            $value['TypeStr'] = $this->utils->_common->order->getPaymentTypeStr($mixTypeArr, $value['SubscriptionUUID']);
            $disCount =$this->share->util->computedDiscount($value['FinalPrice'], $value['Discount']);
            $value['TotalPriceNum'] = $this->share->util->outputComputedCount($disCount);
            $value['TotalPrice'] = '$' . $value['TotalPriceNum'];
            $value['StatusEnum'] = $value['Status'];
            $value['Status'] = $statusMsgList[$value['Status']];
            $value['Type'] = $value['Type'] === strval(PAY_TYPE['landline']) ? PAY_TYPE['renewToMonth'] : $value['Type'];
            $value['TypeEnum'] = $value['Type'];
            $value['Type'] = $payTypeMsgList[$value['Type']];
            $value['PayerTypeName'] = $payerMsgList[$value['PayerType']];
            $value['bmurl'] = BMAPYURL . '?order=' . $value['BmOrderNumber'] . '&token=' . $value['WebHookToken'] . '&code=' . $value['PayCode'];
        }
        $orderDatas = $this->share->util->setQueryTimeZone($orderDatas, $timeZone, $customizeForm);
        return ['data' => ['total' => $total, 'row' => $orderDatas, 'detail' => $orderDatas]];
    }

    /**
     * @description:pm获取订单列表
     * @author:lwj 2022/11/30 10:56 V6.6
     * @lastEditor: lwj 2022/11/30 10:56 V6.6
     * @param:
     * @return array
     */
    public function getListForPm()
    {
        $params = [PROXY_ROLE['pmId'], PROXY_ROLE['projectId'], 'Type:payment.communitytype', 'Status', 'Key', USER_ATTR['timeZone'], USER_ATTR['timeFormat']];
        list($pmId, $community, $type, $status, $key, $timeZone, $customizeForm) = $this->getParams($params);
        list($offset, $rows) = $this->getParamsLimitAndSearch();

        $where = 'where AccountID = :AccountID and IsDelete = 0 and PayerType = :PayerType ';
        $bindArray = [':AccountID' => $pmId, ':PayerType' => PAYER_TYPE_PM];

        if(!empty($key)){
            $where .= ' and OrderNumber like :Key ';
            $bindArray[':Key' ] = "%$key%";
        }

        $this->loadUtil('order', true);
        $typeArr = explode(',', $type);
        $typeWhere = [];
        foreach ($typeArr as $type) {
            if ($type !== 'all' && $type !== null) {
                if ($type == PAYMENT_SEARCH_TYPE['AutoRenewVideoStorage']) {
                    // 包含自动续费,且必须存在视频存储(10,11)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 9)) != 0 OR (MixType & (1 << 10)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewApp']) {
                    // 包含自动续费，且必须包含(2,4,6,8)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 1)) != 0 OR (MixType & (1 << 3)) != 0 OR (MixType & (1 << 7)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewRentManager']) {
                    // 包含自动续费，且必须包含rentmanage(12)
                    $tmpWhere = "SubscriptionUUID != '' And (MixType & (1 << 11)) != 0";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewThirdLock']) {
                    // 包含自动续费，且必须包含三方锁(15,17)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 14)) != 0 OR (MixType & (1 << 16)) != 0)";
                } else {
                    // 排除自动续费
                    $tmpWhere = "SubscriptionUUID = ''";
                }
                //获得MixType
                $mixType = $this->utils->_common->order->getOrderListType($type);
                $tmpWhere .= $this->utils->_common->order->getMixTypeSqlQuery($mixType);
                $typeWhere[] = '(' . $tmpWhere . ')';
            }
        }
        if (!empty($typeWhere)) {
            $where .= ' and (' . implode(' or ', $typeWhere) . ')';
        }

        if ($status !== 'all' && $status !== null) {
            $where .= ' and Status = :Status';
            $bindArray[':Status'] = $status;
        }

        $orderListTable = PROXY_TABLES['orderList'];
        $orderEndUserListTable = PROXY_TABLES['orderEndUserList'];
        $thirdLockListTable = PROXY_TABLES['orderThirdLockList'];
        // 单小区支付,InstallID为小区ID
        $where .= ' and InstallID = :InstallID';
        $bindArray[':InstallID'] = $community;

        $total = $this->db->querySList(
            "select count(*) from $orderListTable $where",
            $bindArray
        )[0]['count(*)'];
        $orderDatas = $this->db->querySList(
            "select * from $orderListTable $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        $this->loadUtil('order', true);
        $statusMsgList = $this->utils->_common->order->getStatusMsgList();
        $payTypeMsgList = $this->utils->_common->order->getPayTypeMsgList();
        $payerMsgList = $this->utils->_common->order->getPayerMsgList();
        foreach ($orderDatas as &$value) {
            $endData = $this->db->querySList(
                "select ID,ProjectUUID from $thirdLockListTable where OrderID = :OrderID union all 
                 select ID,ProjectUUID from $orderEndUserListTable where OrderID = :OrderID ",
                [':OrderID' => $value['ID']]
            );
            $mixTypeArr = $this->share->util->getBitPositions($value['MixType']);
            $value['TypeStr'] = $this->utils->_common->order->getPaymentTypeStr($mixTypeArr, $value['SubscriptionUUID']);
            $value['AptNumber'] = count($endData);
            $disCount = $this->share->util->computedDiscount($value['FinalPrice'], $value['Discount']);
            $value['TotalPriceNum'] = $this->share->util->outputComputedCount($disCount);
            $value['TotalPrice'] = '$' . $value['TotalPriceNum'];
            $value['StatusEnum'] = $value['Status'];
            $value['Status'] = $statusMsgList[$value['Status']];
            $value['Type'] = $value['Type'] === strval(PAY_TYPE['landline']) ? PAY_TYPE['renewToMonth'] : $value['Type'];
            $value['TypeEnum'] = $value['Type'];
            $value['Type'] = $payTypeMsgList[$value['Type']];
            $value['PayerTypeName'] = $payerMsgList[$value['PayerType']];
            $value['bmurl'] = BMAPYURL . '?order=' . $value['BmOrderNumber'] . '&token=' . $value['WebHookToken'] . '&code=' . $value['PayCode'];
        }
        $orderDatas = $this->share->util->setQueryTimeZone($orderDatas, $timeZone, $customizeForm);
        return [
            'data' => ['total' => $total, 'row' => $orderDatas, 'detail' => $orderDatas]
        ];
    }

    /**
     * @description:主账号获取订单列表
     * @author:lwj 2022/11/30 10:56 V6.6
     * @lastEditor: lwj 2022/11/30 10:56 V6.6
     * @param:
     * @return array
     */
    public function getListForApp()
    {
        $params = [PROXY_ROLE['mainUserId'], 'TimeZone', 'CustomizeForm'];
        list($id, $timeZone, $customizeForm) = $this->getParams($params);
        list($offset, $rows) = $this->getParamsLimitAndSearch(true);

        $where = 'where AccountID = :AccountID and IsDelete = 0 and PayerType = :PayerType ';
        $bindArray = [':AccountID' => $id, ':PayerType' => PAYER_TYPE_USER];

        $orderListTable = PROXY_TABLES['orderList'];
        $orderEndUserListTable = PROXY_TABLES['orderEndUserList'];

        $total = $this->db->querySList(
            "select count(*) from $orderListTable $where",
            $bindArray
        )[0]['count(*)'];
        $orderDatas = $this->db->querySList(
            "select * from $orderListTable $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        $this->loadUtil('order', true);
        $statusMsgList = $this->utils->_common->order->getStatusMsgList();
        $payTypeMsgList = $this->utils->_common->order->getPayTypeMsgList();
        $payerMsgList = $this->utils->_common->order->getPayerMsgList();
        foreach ($orderDatas as &$value) {
            $endData = $this->db->querySList(
                "select ID,ProjectUUID from $orderEndUserListTable where OrderID = :OrderID",
                [':OrderID' => $value['ID']]
            );
            $value['AptNumber'] = count($endData);
            $disCount = $this->share->util->computedDiscount($value['FinalPrice'], $value['Discount']);
            $value['TotalPriceNum'] = $this->share->util->outputComputedCount($disCount);
            $value['TotalPrice'] = '$' . $value['TotalPriceNum'];
            $value['StatusEnum'] = $value['Status'];
            $value['Status'] = $statusMsgList[$value['Status']];
            $value['Type'] = $value['Type'] === strval(PAY_TYPE['landline']) ? PAY_TYPE['renewToMonth'] : $value['Type'];
            $value['TypeEnum'] = $value['Type'];
            $value['Type'] = $payTypeMsgList[$value['Type']];
            $value['PayerTypeName'] = $payerMsgList[$value['PayerType']];
            $value['bmurl'] = BMAPYURL . '?order=' . $value['BmOrderNumber'] . '&token=' . $value['WebHookToken'] . '&code=' . $value['PayCode'];
        }
        $orderDatas = $this->share->util->setQueryTimeZone($orderDatas, $timeZone, $customizeForm);
        return [
            'data' => ['total' => $total, 'row' => $orderDatas, 'detail' => $orderDatas]
        ];
    }

    /**
     * @description:订单详情
     * @author:lwj 2022/11/30 10:56 V6.6
     * @lastEditor: lwj 2022/11/30 10:56 V6.6
     * @param:{string} ID orderID
     * @return array
     */
    public function getInfo()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm', PROXY_ROLE['pmId'], PROXY_ROLE['projectId']];
        list($id, $timeZone, $customizeForm, $pmId, $projectId) = $this->getParams($params);
        $manageId = empty($pmId) ? $projectId:$pmId;
        $this->loadUtil('order', true);
        $isValidOrder = $this->utils->_common->order->checkOrderIdInManage($id, $manageId);
        if ($isValidOrder === false) {
            return ['data' => []];
        }

        $orderData = $this->dao->orderList->selectByKey('ID', $id)[0];
        if (empty($orderData)) {
            return ['data' => []];
        }

        $this->loadUtil('order', true);
        $type = $orderData['Type'];
        $mixType = $orderData['MixType'];
        $orderData['OriginalPrice'] = $this->share->util->outputComputedCount($orderData['TotalPrice']);
        $disCount = $this->share->util->computedDiscount($orderData['FinalPrice'], $orderData['Discount']);
        $orderData['TotalPrice'] = $this->share->util->outputComputedCount($disCount);
        $orderData['CouponCount'] = $this->share->util->outputComputedCount($orderData['CouponCount']);
        $orderData['BeforeOncePrice'] = $this->share->util->outputComputedCount($orderData['BeforeOncePrice']);
        $orderData['StatusEnum'] = $orderData['Status'];

        $statusMsgList = $this->utils->_common->order->getStatusMsgList();
        $payTypeMsgList = $this->utils->_common->order->getPayTypeMsgList();

        $this->loadUtil('account', true);
        $orderData['ProjectType'] = $this->utils->_common->account->accountSelectByKey('ID', $orderData['InstallID'])[0]['Grade'];

        if (intval($orderData['PayerType']) === PAYER_TYPE_INS) {
            $orderData['Payer'] = $this->utils->_common->account->getInstallerByProjectID($orderData['AccountID'])['Account'];
        }

        //PM订单展示成LoginAccount
        if ($orderData['PayerType'] === strval(PAYER_TYPE_PM)) {
            $orderData['Payer'] = $this->utils->_common->account->getManagerListByArray([['Account', $orderData['Payer']]])[0]['LoginAccount'];
        }
        $orderData = $this->share->util->setQueryTimeZone([$orderData], $timeZone, $customizeForm)[0];
        //setQueryTimeZone没有NextTime，怕影响业务先单独放这里处理
        $orderData['NextTime'] = $this->share->util->setTimeZone($orderData['NextTime'], $timeZone, $customizeForm);

        $mixTypeArr = $this->share->util->getBitPositions($orderData['MixType']);
        if ($type == PAY_TYPE['rentManagerIntegration']) {
            $this->loadUtil('order', true);
            $orderData['RentManager'] = $this->utils->_common->order->getRentManagerAmountList($orderData['ID'], $orderData['Months'], $orderData['OriginalPrice']);
        } else {
            $orderData['Children'] = $this->callSelfFunc('getChildrenAmountList', [$id, $mixType, $orderData['Months']]);
        }
        $orderData['ChildrenThirdLock'] = [];
        //订单有包含三方锁，额外查询三方锁每项内容
        if (array_intersect($mixTypeArr, [PAY_TYPE['singleThirdLockActive'], PAY_TYPE['communityThirdLockActive'], PAY_TYPE['singleThirdLockRenew'], PAY_TYPE['communityThirdLockRenew']])) {
            $orderData['ChildrenThirdLock'] = $this->callSelfFunc('getChildrenThirdLockList', [$id]);
        }

        $type = $orderData['Type'];
        $type = $this->callSelfFunc('getRealOrderType', [$orderData['Children'], $type]);
        $orderData['Status'] = $statusMsgList[$orderData['Status']];
        $orderData['Type'] = $payTypeMsgList[$orderData['Type']];
        $orderData['TypeEnum'] = $type;
        $this->loadUtil('order', true);
        $orderData['TypeStr'] = $this->utils->_common->order->getPaymentTypeStr($mixTypeArr, $orderData['SubscriptionUUID']);
        $orderData['ShowNextTime'] = $this->utils->_common->order->getShowNextExpireTime($mixTypeArr, $orderData['SubscriptionUUID']);

        return ['data' => $orderData];
    }

    public function getChildrenAmountList()
    {
        $params = ['OrderID', 'MixType', 'OrderMonths'];
        list($orderID, $mixType, $orderMonths) = $this->getParams($params);
        $childrenList = [];
        $mixTypeArr = $this->share->util->getBitPositions($mixType);
        foreach ($mixTypeArr as $type) {
            $orderType = intval($type);
            $renewAppArr = [PAY_TYPE['active'], PAY_TYPE['buyOutApp'], PAY_TYPE['landline']];
            if (in_array($orderType, $renewAppArr)) {
                $subOrderType = [PAY_SUB_TYPE['active'], PAY_SUB_TYPE['buyOutApp'], PAY_SUB_TYPE['landline'], PAY_SUB_TYPE['activePM']];
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', $subOrderType]]);
                foreach ($result as &$val) {
                    $val['Price'] = $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $childrenList[] = $val;
                }
            }

            if ($orderType === PAY_TYPE['renewToMonth']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['renewToMonth']]]);
                foreach ($result as &$val) {
                    $outApps = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['landline']], ['ParentID', $val['AppID']]]);
                    $val['OutApps'] = count($outApps);
                    $val['OutAppsAmount'] = $val['OutApps'] === 0 ? 0 : $this->share->util->outputComputedCount(
                        $outApps[0]['Amount'] * $val['OutApps']
                    );
                    $outAppsAmount = ($val['OutApps'] === 0 ? 0 : $outApps[0]['Amount'] * count($outApps)) + $val['Amount'];
                    $val['Price'] = $this->share->util->outputComputedCount($outAppsAmount * $orderMonths);
                    $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $childrenList[] = $val;
                }
            }

            if ($orderType === PAY_TYPE['renewToDay']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', [PAY_SUB_TYPE['renewToDay'], PAY_SUB_TYPE['renewPM']]]]);
                foreach ($result as &$val) {
                    $val['Price'] = $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $val['MonthlyFee'] = json_decode($val['ChargeData'], true)['MonthlyFee'];
                    $childrenList[] = $val;
                }
            }

            // v7.1.0 视频存储
            if ($orderType === PAY_TYPE['communityVideoStorage']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['communityVideoStorage']]]);
                foreach ($result as $val) {
                    $item = [
                        'Amount' => $this->share->util->outputComputedCount($val['Amount']),
                        'Price' => $this->share->util->outputComputedCount($val['Amount']),
                        'Object' => $val['Object'],
                        'Type' => $val['Type'],
                        'ProjectUUID' => $val['ProjectUUID'],
                        'ProjectName' => $val['ProjectName'],
                        'MonthlyFee' => json_decode($val['ChargeData'], true)['MonthlyFee'],
                        'Days' => $val['Days'],
                    ];

                    $childrenList[] = $item;
                }
            }
        }

        return $childrenList;
    }

    //获取三方锁每项内容
    public function getChildrenThirdLockList()
    {
        $params = ['OrderID'];
        list($orderID) = $this->getParams($params);
        $childrenList = [];

        $thirdLockItemList = $this->dao->orderThirdLockList->selectByKey('OrderID', $orderID);
        foreach ($thirdLockItemList as $item) {
            $childrenList[] = [
                'Amount' => $this->share->util->outputComputedCount($item['Amount']),
                'Price' => $this->share->util->outputComputedCount($item['Amount']),
                'Type' => $item['ServiceType'],
                'ProjectUUID' => $item['ProjectUUID'],
                'ProjectName' => $item['ProjectName'],
                'MonthlyFee' => json_decode($item['ChargeData'], true)['MonthlyFee'],
                'Days' => $item['Days'],
                'Brand' => $item['Brand'],
                'LockUUID' => $item['LockUUID'],
                'LockName' => $item['LockName'],
                'PersonalAccountUUID' => $item['PersonalAccountUUID'],
                'AptName' => $item['AptName'],
                'CommunityUnitUUID' => $item['CommunityUnitUUID'],
                'UnitName' => $item['UnitName'],
            ];
        }

        return $childrenList;
    }

    public function getRealOrderType()
    {
        $params = ['Children', 'OriginType'];
        list($children, $originType) = $this->getParams($params);

        if ($originType != PAY_TYPE['mix']) {
            return $originType;
        }


        $allVideoStorage = true; // 标记是否所有子订单都是视频存储类型
        $hasOtherType = false; // 标记是否有非视频存储类型

        foreach ($children as $item) {
            if ($item['Type'] != PAY_SUB_TYPE['communityVideoStorage']) {
                $allVideoStorage = false; // 并非都是视频存储
                $hasOtherType = true; // 存在其他类型
            }
        }

        // 根据子订单类型设置 $realType
        if ($allVideoStorage) {
            $realType = PAY_TYPE['communityVideoStorage'];
        } elseif ($hasOtherType) {
            $realType = PAY_TYPE['mix'];
        } else {
            $realType = $originType;
        }

        return $realType;
    }
}
