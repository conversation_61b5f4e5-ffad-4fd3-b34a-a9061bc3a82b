<?php
/**
 * @description:
 * @author: csc 2023/12/15 13:52 V6.7.1
 * @lastEditor kxl
 */

namespace package\community\model\subscription\src;

use package\community\model\subscription\config\Code;

trait Get
{
    /**
     * @description: 计算订阅金额
     * @param {array} Users 续费的用户列表
     * @param {int} IntervalType 周期类型 0:月 1:季 2:年 3:天
     * @return void
     * @throws \Exception
     * @author: csc 2023/12/15 14:14 V6.7.1
     * @lastEditors: csc 2023/12/15 14:14 V6.7.1
     */
    public function computedPrice()
    {
        //获取参数
        $params = ['Users:object', 'IntervalType:enum("0","1","2","3")', 'userId', 'role', PROXY_ROLE['installerUUID'],
            'VideoSites:object', 'SubscriptionUUID?:or-rule("uuid","string-empty")', 'ThirdLockUUIDs?:object'];
        list($users, $intervalType, $userId, $role, $insUUID, $videoSites, $subscriptionUUID, $thirdLockUUIDs) = $this->getParams($params);


        $this->loadUtil('account', true);
        $this->loadUtil('order', true);

        $usersArr = json_decode($users, true);
        $videoSitesArr = json_decode($videoSites, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        if (empty($usersArr) && empty($videoSitesArr) && empty($thirdLockUUIDsArr)) {
            return ['data' => ['Price' => 0]];
        }
        $usersInfo = $this->utils->_common->order->getSubscribeUserInfo($usersArr);
        $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoSitesArr, PAY_TYPE_MULTIPLE);
        $thirdLockInfo = $this->utils->_common->order->getSubscribeThirdLockInfo($thirdLockUUIDsArr, PAY_TYPE_MULTIPLE);
        if (empty($usersInfo['all']) && empty($videoStorageInfo['site']) && empty($thirdLockInfo['site'])) {
            $this->output->echoErrorMsg(STATE_PARAMS_ERROR, ['externalErrorObj' => Code::EXT_STATE_USER_NOT_EXIST]);
        }

        // 混合支付检查支付权限
        $this->loadUtil('subscription', true);
        $payerType = $this->utils->_common->subscription->getPayerType($role);
        $userPrice = $videoPrice = $thirdLockPrice = 0;
        if (!empty($usersInfo['all'])) {
            $this->utils->_common->order->checkPayPermission($usersInfo, $userId, $payerType);
            //设置Type等参数
            $this->loadModel('subscription', true, ['dataContainer' => [
                'Type' => SUBSCRIBE_TYPE['communityRenew'],
                'Users' => $usersInfo, 'IntervalType' => $intervalType
            ]]);
            $userPrice = $this->models->_common->subscription->computedPrice()['Price'];
        }
        if (!empty($videoStorageInfo['site'])) {
            $this->loadModel('subscription', true);
            $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $userId, $payerType);
            $videoPrice = $this->models->_common->subscription->computedVideoStoragePrice(2, $intervalType, $videoStorageInfo, $subscriptionUUID)['Price'];
        }
        if (!empty($thirdLockInfo['site'])) {
            $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $userId, $payerType);
            $this->loadModel('subscription', true);
            $thirdLockPrice = $this->models->_common->subscription->computedThirdLockPrice(2, $intervalType, $thirdLockInfo, $subscriptionUUID)['Price'];
        }

        $price = \share\util\outputComputedCount(\share\util\inputComputedCount($userPrice + $videoPrice + $thirdLockPrice));

        return ['data' => ['Price' => $price]];
    }

    /**
     * @description:获得订阅列表
     * @param: {array} searchArray 查询数组,格式如[['PayerUUID', '1'], ['ProjectUUID', '2'], ...]
     * @param: {string} field 需要查询的subscriptionOrderList表中的字段
     * @return array
     * @author: shoubin.chen 2023-12-14 16:06:20 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 16:06:20 v6.7.1
     */
    public function getSubscriptionList()
    {
        $params = ['SearchArray:is-array'];
        list($array) = $this->getParams($params);
        $defaultField = "ID,SubscriptionNumber,Status,TotalPrice,Discount,StartTime,NextPayTime,CreateTime,EndTime,IntervalType,Cycles,PayPlatform,EndReason,UUID,TimeZone,IsBatch,Type,MixType";

        list($offset, $row, $searchKey, $searchValue) = $this->getParamsLimitAndSearch();
        $data = $this->dao->subscriptionList->orderBy("Status = 1 DESC,CreateTime DESC")->limit("$offset, $row")->selectByArray($array, $defaultField);
        $total = $this->dao->subscriptionList->selectByArray($array, "count(*)")[0]['count(*)'];

        $changeField = "ID,Status,IntervalType,Cycles,PayPlatform,IsBatch,Type";
        foreach ($data as &$item) {
            //时间转换和类型转换
            $item = $this->share->util->setQueryTimeZoneForObject($item, $item['TimeZone']);
            $item = $this->share->util->changeStringToNumber($item, $changeField);
            //已到期的隐藏下次支付时间
            $item['NextPayTime'] = $item['Status'] === SUBSCRIBE_STATUS['expired'] ? '' : $item['NextPayTime'];
            //金额计算
            $disCount = $this->share->util->computedDiscount($item['TotalPrice'], $item['Discount']);
            $item['TotalPrice'] = $this->share->util->outputComputedCount($disCount);
            $item['Function'] = $this->utils->self->dealSubscriptionFunction($item['Type'], $item['MixType']);
        }
        unset($item);

        return [intval($total), $data];
    }

    /**
     * @description:具体的某个社区查看自动扣费列表
     * @param: {string} pmId PM的ID
     * @param: {string} projectId 项目ID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getListForCom()
    {
        $params = [PROXY_ROLE_CHECK['projectUUID'], PROXY_ROLE_CHECK['installerUUID']];
        list($projectUUID, $insUUID) = $this->getParams($params);
        //条件数组

        $community = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityRenew']);
        $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityVideoStorageRenew']);
        $rent = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
        $thirdLock = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityThirdLockRenew']);

        $array = [
            ['PayerUUID', $insUUID],
            ['ProjectUUID', $projectUUID],
            ['IsBatch', SUBSCRIBE_BATCH_TYPE['notBatch']],
            ['MixType', "& ($community | $video | $rent | $thirdLock) !=0", 'origin'],
            ['IsDelete', 0]
        ];
        list($total, $data) = $this->callSelfFunc('getSubscriptionList', [$array]);

        return ['data' => ['total' => $total, 'row' => $data]];
    }

    /**
     * @description:Installer从Home页面进入，获取所有的自动扣费订单列表
     * @param: {string} pmId PM的ID
     * @param: {string} projectId 项目ID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getListForIns()
    {
        $params = [PROXY_ROLE_CHECK['installerUUID']];
        list($insUUID) = $this->getParams($params);

        $community = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityRenew']);
        $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityVideoStorageRenew']);
        $rent = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
        $thirdLock = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityThirdLockRenew']);

        $array = [
            ['PayerUUID', $insUUID],
            ['MixType', "& ($community | $video | $rent | $thirdLock) !=0", 'origin'],
            ['IsDelete', 0]
        ];
        list($total, $data) = $this->callSelfFunc('getSubscriptionList', [$array]);

        return ['data' => ['total' => $total, 'row' => $data]];
    }

    /**
     * @description:PM查看自动扣费列表
     * @param: {string} pmId PM的ID
     * @param: {string} projectId 项目ID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getListForPm()
    {
        $params = [PROXY_ROLE_CHECK['pmUUID'], PROXY_ROLE_CHECK['projectUUID']];
        list($pmUUID, $projectUUID) = $this->getParams($params);

        $community = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityRenew']);
        $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityVideoStorageRenew']);
        $rent = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
        $thirdLock = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityThirdLockRenew']);

        //条件数组
        $array = [
            ['PayerUUID', $pmUUID],
            ['ProjectUUID', $projectUUID],
            ['IsBatch', SUBSCRIBE_BATCH_TYPE['notBatch']],
            ['MixType', "& ($community | $video | $rent | $thirdLock) !=0", 'origin'],
            ['IsDelete', 0]
        ];

        list($total, $data) = $this->callSelfFunc('getSubscriptionList', [$array]);
        return ['data' => ['total' => $total, 'row' => $data]];
    }

    /**
     * @description:获取自动续费订单详情
     * @param: {array} searchArray 查询数组,格式如[['PayerUUID', '1'], ['ProjectUUID', '2'], ...]
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getSubscriptInfo()
    {
        $params = ['SearchArray:is-array'];
        list($array) = $this->getParams($params);
        $defaultField = "ID,SubscriptionNumber,Status,TotalPrice,Discount,StartTime,NextPayTime,CreateTime,EndTime,IntervalType,Cycles,PayPlatform,EndReason,UUID,InsUUID,ProjectUUID,TimeZone,Type";

        $order = $this->dao->subscriptionList->selectByArray($array, $defaultField)[0];
        if ($order === null) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_OR_NOT_PERMISSION]);
        }
        if ($order['ProjectUUID']!==null){
            $this->loadUtil('account',true);
            $order['ProjectID'] = $this->utils->_common->account->accountSelectByKey('UUID', $order['ProjectUUID'], 'ID')[0]['ID'];
        }
        //时间转换和类型转换
        $order = $this->share->util->setQueryTimeZoneForObject($order, $order['TimeZone']);
        $order = $this->share->util->changeStringToNumber($order, "ID,Status,IntervalType,Cycles,PayPlatform");
        //已到期的隐藏下次支付时间
        $order['NextPayTime'] = $order['Status'] === SUBSCRIBE_STATUS['expired'] ? '' : $order['NextPayTime'];
        //金额计算
        $disCount = $this->share->util->computedDiscount($order['TotalPrice'], $order['Discount']);
        $order['TotalPrice'] = $this->share->util->outputComputedCount($disCount);

        //EndReason转化为Reason，并匹配对应的状态
        $this->loadUtil('subscription', true);
        $order['Reason'] = $this->utils->_common->subscription->changeEndReason($order['EndReason']);
        $order['bmUrl'] = $this->utils->_common->subscription->getBmURL($order['UUID']);


        $this->loadUtil('communityUnit', true);
        $this->loadUtil('account', true);
        $this->loadUtil('videoStorage', true);
        $this->loadUtil('rentManager');

        //判断订阅的类型是社区还是rentManager
        if (intval($order['Type']) != SUBSCRIBE_TYPE['rentManagerRenew']) {
            $endUserList = $this->dao->subscriptionEndUserList->selectByArray([['SubscriptionUUID', $order['UUID']]], "Amount,Discount,PersonalAccountUUID,ProjectUUID,Type,SiteUUID,Brand,LockUUID");
            // 社区
            //缓存map,key存放社区主账户UUID，value存放房间号
            $map = [];
            foreach ($endUserList as &$item) {
                if ($item['Type'] == SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew']){
                    $item['ExpireTime'] = $this->utils->_common->videoStorage->videoStorageSelectByKey('AccountUUID',$item['SiteUUID'])[0]['ExpireTime'];
                    $project = $this->utils->_common->account->accountSelectByKey('UUID',$item['ProjectUUID'],'ID,Location')[0];
                    $item['ProjectName'] = $project['Location'];
                    $item['ID'] = $project['ID'];
                } else if ($item['Type'] == SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew']){
                    $lockInfo = $this->utils->_common->subscription->getThirdLockInfoByLockUUID($item['LockUUID']);
                    $item['ExpireTime'] = $lockInfo['ExpireTime'];
                    $item['ProjectName'] = $lockInfo['ProjectName'];
                    $item['LockUUID'] = $lockInfo['UUID'];
                    $item['UnitName'] = $lockInfo['UnitName'];
                    $item['RoomName'] = $lockInfo['RoomName'];
                    $item['RoomNumber'] = $lockInfo['RoomNumber'];
                    $item['Name'] = $lockInfo['LockName'];
                    $item['Email'] = '';
                    if (!empty($item['PersonalAccountUUID'])) {
                        $user = $this->utils->_common->account->getUserInfoByUUID($item['PersonalAccountUUID']);
                        $item['Email'] = $user['Email'];
                    }
                } else{
                    $user = $this->utils->_common->account->getUserListByArray([['UUID', $item['PersonalAccountUUID']]], 1, [], 'A.ID,A.UUID,A.RoomNumber,A.UnitID,A.RoomID,A.ParentUUID,A.Name,A.Role,A.RoomID,A.ExpireTime,AUF.Email')[0];
                    $item = array_merge($item, $user);
                    //查找楼栋名
                    $item['UnitName'] = $this->utils->_common->communityUnit->getUnitInfoByKey('ID', $user['UnitID'])['UnitName'];
                    $item['ProjectName'] = $this->utils->_common->account->accountSelectByKey('UUID',$item['ProjectUUID'],'Location')[0]['Location'];
                    //查找房间名
                    $item['RoomName'] = $this->getRoomName($user, $map);
                }

                //金额计算
                $disCount = $this->share->util->computedDiscount($item['Amount'], $item['Discount']);
                $item['Amount'] = $this->share->util->outputComputedCount($disCount);
                //过期时间
                $item = $this->share->util->setQueryTimeZoneForObject($item, $order['TimeZone']);

                $item['ServiceType'] = $this->utils->self->dealSubscriptionEndUserType($item['Type'],$user['Role']);
                unset($item['PersonalAccountUUID'], $item['RoomID'], $item['ParentUUID']);
            }
            unset($item);
            $order['Items'] = $endUserList;
            $order['RentManagers'] = [];
        } else {
            //rentManager
            $subscriptionUUID = $order['UUID'];
            $rentManagerCustomerList = $this->dao->rentManagerSubscriptionList->selectByKey('SubscriptionUUID', $subscriptionUUID);
            $data = [];
            if (!empty($rentManagerCustomerList)) {
                foreach ($rentManagerCustomerList as $item) {
                    $customerInfo ['ID'] = json_decode($item['ChargeData'], true)['ID'];
                    $customerInfo ['UUID'] = $item['RentManagerCustomerUUID'];
                    $customerInfo ['CompanyName'] = json_decode($item['ChargeData'], true)['CompanyName'];
                    $customerInfo ['CompanyCode'] = json_decode($item['ChargeData'], true)['CompanyCode'];
                    $customerInfo ['Email'] = json_decode($item['ChargeData'], true)['Email'];
                    $customerInfo ['PmName'] = json_decode($item['ChargeData'], true)['PmName'];
                    $customerInfo ['MonthlyFee'] = $this->share->util->outputComputedCount($item['MonthlyFee']);
                    $data[] = $customerInfo;
                }
            }
            $order['RentManagers'] = $data;
            $order['Items'] = [];
        }

        return $order;
    }

    /**
     * @description:Dis获取自动续费订单详情
     * @param: {string} distributorId dis的ID
     * @param: {string} UUID 自动续费订单UUID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getInfoForDis()
    {
        $params = [PROXY_ROLE_CHECK['distributorUUID'], 'UUID:uuid'];
        list($disUUID, $uuid) = $this->getParams($params);

        $array = [
            ['PayerUUID', $disUUID],
            ['UUID', $uuid],
            ['IsDelete', 0]
        ];

        $data = $this->callSelfFunc('getSubscriptInfo', [$array]);
        return ['data' => $data];
    }

    /**
     * @description:sub-dis获取自动续费订单详情
     * @param: {string} subDistributorId dis的ID
     * @param: {string} UUID 自动续费订单UUID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getInfoForSubDis()
    {
        $params = [PROXY_ROLE_CHECK['subDistributorUUID'], 'UUID:uuid'];
        list($subDisUUID, $uuid) = $this->getParams($params);

        $array = [
            ['PayerUUID', $subDisUUID],
            ['UUID', $uuid],
            ['IsDelete', 0]
        ];

        $data = $this->callSelfFunc('getSubscriptInfo', [$array]);
        return ['data' => $data];
    }

    /**
     * @description:ins获取自动续费订单详情
     * @param: {string} installerId ins的ID
     * @param: {string} UUID 自动续费订单UUID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getInfoForIns()
    {
        $params = [PROXY_ROLE_CHECK['installerUUID'], 'UUID:uuid', 'IsBatch:enum("0","1")'];
        list($insUUID, $uuid, $isBatch) = $this->getParams($params);

        $array = [
            ['PayerUUID', $insUUID],
            ['UUID', $uuid],
            // 详情可以不需要限制，否则会影响单个视频存储check订阅跳转查看详情。
//            ['IsBatch', intval($isBatch)],
            ['IsDelete', 0]
        ];

        $data = $this->callSelfFunc('getSubscriptInfo', [$array]);
        return ['data' => $data];
    }

    /**
     * @description:ins获取自动续费订单详情
     * @param: {string} installerId ins的ID
     * @param: {string} UUID 自动续费订单UUID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getInfoForCom()
    {
        $params = [PROXY_ROLE_CHECK['installerUUID'], PROXY_ROLE_CHECK['projectUUID'], 'UUID:uuid'];
        list($insUUID, $projectUUID, $uuid) = $this->getParams($params);
        $array = [
            ['PayerUUID', $insUUID],
            ['UUID', $uuid],
            ['IsBatch', SUBSCRIBE_BATCH_TYPE['notBatch']],
            ['IsDelete', 0]
        ];

        $data = $this->callSelfFunc('getSubscriptInfo', [$array]);
        return ['data' => $data];
    }

    /**
     * @description:pm获取自动续费订单详情
     * @param: {string} pmId pm的ID
     * @param: {string} UUID 自动续费订单UUID
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:42:54 v6.7.1
     */
    public function getInfoForPM()
    {
        $params = [PROXY_ROLE_CHECK['pmUUID'], 'UUID:uuid'];
        list($pmUUID, $uuid) = $this->getParams($params);

        $array = [
            ['PayerUUID', $pmUUID],
            ['UUID', $uuid],
            ['IsDelete', 0]
        ];

        $data = $this->callSelfFunc('getSubscriptInfo', [$array]);
        return ['data' => $data];
    }

    /**
     * @description:获得房间号
     * @param $user array 主/从用户信息
     * @param $map array 缓存map,key是PersonalAccount的UUID，value是房间号
     * @return mixed|string
     * @author: shoubin.chen 2023-12-22 11:52:05 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-22 11:52:05 v6.7.1
     */
    private function getRoomName($user, &$map)
    {
        $this->loadUtil('communityRoom', true);
        $role = intval($user['Role']);

        $uuid = $role === COMENDMROLE ? $user['UUID'] : $user['ParentUUID'];
        $roomName = isset($map[$uuid]) ? $map[$uuid] : $this->utils->_common->communityRoom->getRoomInfoByPersonalAccount($uuid)['RoomName'];
        $map =  $this->share->util->addToMap($map, $uuid, $roomName);
        return $roomName;
    }

    /**
     * @description: 获得社区视频存储订阅详情
     * @param: {string} UUID 社区UUID
     * @author: shoubin.chen 2024/11/27 15:26:01 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/27 15:26:01  V7.1.0
     */
    public function getVideoStorageSubscriptionInfo()
    {
        $params = ['UUID:uuid'];
        list($uuid) = $this->getParams($params);
        $this->loadUtil('videoStorage', true);
        $videoStorage = $this->utils->_common->videoStorage->videoStorageSelectByArray([['AccountUUID', $uuid]], 'DevicesLimitNum,StorageDays,ExpireTime')[0];
        if (empty($videoStorage)) {
            return ['data' => []];
        }
        //查找月费
        $this->loadUtil('account', true);
        $community = $this->utils->_common->account->accountSelectByKey('UUID', $uuid)[0];
        $timeZone = $community['TimeZone'];
        $customizeForm = $community['CustomizeForm'];
        $this->loadUtil('order', true);
        $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo([$community['ID']], PAY_TYPE_MULTIPLE);
        $this->loadProvider('billsysUtil');
        $videoCharges = $this->services->billsysUtil->getVideoStorageCharge($videoStorageInfo);
        $monthFee = array_values($videoCharges)[0]['Model']['MonthlyFee'];
        $subscription = $this->utils->self->getSubscriptionByCommunityVideoStorage($uuid);
        $result['DeviceNum'] = $this->utils->_common->videoStorage->getDeviceNumStr($videoStorage['DevicesLimitNum']);
        $result['StorageDays'] = $videoStorage['StorageDays'];
        $result['ExpireTime'] = $videoStorage['ExpireTime'];
        $result['MonthFee'] = (string)$monthFee;

        //查找是否存在进行中的订阅
        $nextPayTime = '';
        if ($subscription !== false) {
            $nextPayTime = $subscription['NextPayTime'];
        }

        $result['NextPayTime'] = $nextPayTime;
        $result = $this->share->util->setQueryTimeZoneForObject($result, $timeZone, $customizeForm, [], true);
        return ['data' => $result];
    }

    /**
     * @description: 获得社区视频存储是否有订阅
     * @param: {string} UUID 社区UUID
     * @author: shoubin.chen 2024/11/27 15:26:01 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/27 15:26:01  V7.1.0
     */
    public function getVideoStorageInfo()
    {
        $params = ['UUID:uuid'];
        list($uuid) = $this->getParams($params);
        $subscription = $this->utils->self->getSubscriptionByCommunityVideoStorage($uuid);
        $autoRenewRecordUUID = $payerUUID = '';
        $isAutoRenew = false;
        if ($subscription !== false) {
            $autoRenewRecordUUID = $subscription['UUID'];
            $payerUUID = $subscription['PayerUUID'];
            $isAutoRenew = true;
        }
        return ['data' => ['SubscriptionUUID' => $autoRenewRecordUUID, 'IsAutoRenew' => $isAutoRenew, 'PayerUUID' => $payerUUID]];
    }

    /**
     * @description: 查询dormakaba锁有没有订阅
     * @param: {string} UUID 锁的uuid
     * @author: kzr 2025/02/25 10:42:00 V7.1.0
     * @lastEditor: kzr 2025/02/25 10:42:00 V7.1.0
     */
    public function getThirdLockSubscriptionInfo(){
        $params = ['UUID:or-rule("uuid","is-array")','Brand'];
        list($uuid,$brand) = $this->getParams($params);
        
        $subscription = $this->utils->self->getSubscriptionByThirdLock($uuid,$brand);
        
        // $autoRenewRecordUUID = $payerUUID = '';
        // $isAutoRenew = false;
        $data = [];
        if ($subscription !== false&&is_array($subscription)) {
            $data = $subscription;
        }
        return ['data' => $data];
    }
}

