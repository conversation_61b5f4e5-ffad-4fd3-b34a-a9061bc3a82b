<?php

/*
installer APP Feedback功能测试脚本
*/

error_reporting(1);

require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');
require_once(dirname(__FILE__) . '/../../config/base.php');
require_once(dirname(__FILE__) . '/../../config/global.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../funcs_email.php');
require_once(dirname(__FILE__) . '/../funcs_user.php');

//使用说明：php send_feedback.php $uuid
if ($argc != 2) {
    echo "param error\n";
    exit(1);
}

$uuid = $argv[1];
sendFeedbackEmail($uuid);