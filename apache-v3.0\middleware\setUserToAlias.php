<?php
/*
 * @Description: 设置user和userId到userAlias和userAliasId
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
class CSetUserToAlias implements IMiddleware {
    public function handle(\Closure $next) {
        global $gApp;
        $gApp['userAlias'] = $gApp["user"];
        $gApp['userAliasId'] = $gApp["userId"];
        
        $next();
    }
}