<?php
/**
 * @Author:zyc
 * @Date 2021/5/26
 * @LastEditors:zyc
 */

namespace model;

require_once __DIR__.'/../communityData/util.func.php';

class CApartment
{
    //V6.1 APT查询接口
    public function getPMAptList()
    {
        $params = [
            'userAliasId' => '',
            'Build' => '',
            'Apt' => '',
            'Key' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userAliasId'];
        $buildId = $params['Build'];
        $apt = $params['Apt'];
        $key = $params['Key'];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $data = [];
        $where = ' Role = 20 and PA.ParentID = :ParentID ';
        $bindArray = [':ParentID' => $userId];

        if ($buildId && $buildId !== 'all') {
            $where .= ' and CU.ID = :build ';
            $bindArray[':build'] = $buildId;
        }
        if ($apt && $apt !== 'all') {
            $where .= ' and CR.ID = :apt ';
            $bindArray[':apt'] = $apt;
        }
        if ($key) {
            $where .= ' and PA.RoomNumber like :key ';
            $bindArray[':key'] = "%$key%";
        }

        $sql = "select PA.ID, CU.UnitName as Build, CR.RoomName as AptNumber, CR.Floor, PA.RoomNumber as AptName, PA.Special, PA.EnableIpDirect,PA.TempKeyPermission,PC.AllowCreateSlaveCnt,PC.Flags as FamilyMemberControl
                from PersonalAccount PA left join CommunityRoom CR on PA.RoomID = CR.ID left join CommunityUnit CU on PA.UnitID = CU.ID left join PersonalAccountCnf PC
                on PA.Account = PC.Account where $where order by PA.CreateTime desc";
        $data['total'] = count($this->db->querySList($sql, $bindArray));
        $data['row'] = $this->db->querySList($sql." limit $offset,$rows", $bindArray);

        // 获取社区高级功能，此处限制QRCode
        $this->log->actionLog("#model#apartment#getPMAptList#userId=$userId");
        $FeatureExpireTime = $this->db->querySList('select FeatureExpireTime from CommunityInfo where AccountID = :ID', [':ID' => $userId])[0]['FeatureExpireTime'];
        $Item = $this->db->querySList('select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID', [':ID' => $userId])[0]['Item'];
        $IsDefaultFeature = $this->db->querySList('select FeatureID from ManageFeature where AccountID = :ID', [':ID' => $userId])[0]['FeatureID'];
        $FeatureItem = communityData\checkFeaturePlan($FeatureExpireTime, $Item, $IsDefaultFeature);

        foreach ($data['row'] as &$row) {
            if ($row['Floor'] !== '') {
                $row['AptNumber'] = $row['AptNumber'].' ('.MSGTEXT['floor'].' '.$row['Floor'].')';
            }
            $bindArrayID = [':ID' => $row['ID']];
            $row['LimitCreateQRcode'] = intval($FeatureItem[2]);
            $row['LimitFamilyMember'] = intval($FeatureItem[3]);
            $row['FamilyMemberControl'] = intval(str_split($row['FamilyMemberControl'])[0]);
            $row['AllowCreateSlaveCnt'] = $row['AllowCreateSlaveCnt'];
            $row['EnableIpDirect'] = intval($row['EnableIpDirect']);
            $row['TempKeyPermission'] = intval($row['TempKeyPermission']);

            if ($row['Special'] == 1) {
                $row['Residents'] = 0;
            } else {
                $residentSql = 'select PA1.ID from PersonalAccount PA1 join PersonalAccount PA2 on PA1.ParentID = PA2.ID where PA2.ID = :ID';
                $row['Residents'] = count($this->db->querySList($residentSql, $bindArrayID)) + 1;
            }
            $devicesSql = 'select D.ID from  PersonalAccount PA join Devices D on PA.Account = D.Node where PA.ID = :ID';
            $row['Devices'] = count($this->db->querySList($devicesSql, $bindArrayID));

            $callTypeSql = 'select PAC.CallType from PersonalAccount PA join PersonalAccountCnf PAC on PA.Account = PAC.Account where PA.ID = :ID';
            $row['CallType'] = intval($this->db->querySList($callTypeSql, $bindArrayID)[0]['CallType']);
        }
        unset($row);

        $data['detail'] = $data['row'];
        \util\computed\setGAppData(['data' => $data]);
    }

    //V6.1 APT 修改接口
    public function modifyPMApt()
    {
        $params = [
            'ID' => '',
            'AptName' => '',
            'CallType' => '',
            'EnableIpDirect' => '',
            'TempKeyPermission' => '',
            'userAliasId' => '',
            'FamilyMemberControl' => '',
            'AllowCreateSlaveCnt' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userAliasId'];
        $ID = $params['ID'];
        $aptName = $params['AptName'];
        $callType = $params['CallType'];
        $enableIpDirect = $params['EnableIpDirect'];
        $tempKeyPermission = $params['TempKeyPermission'];
        $familyMemberControl = $params['FamilyMemberControl'];
        $allowCreateSlaveCnt = $params['AllowCreateSlaveCnt'];
        $data = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $ID]])[0];
        $oldCallType = $this->db->queryAllList('PersonalAccountCnf', ['equation' => [':Account' => $data['Account']]])[0];
        \util\computed\setGAppData(['Account' => $data['Account']]);

        $newPhoneStatus = $callType == 1 ? 1 : 0;
        $this->db->update2ListWID('PersonalAccount', [':ID' => $ID, ':RoomNumber' => $aptName, ':EnableIpDirect' => $enableIpDirect,
            ':TempKeyPermission' => $tempKeyPermission, ':PhoneStatus' => $newPhoneStatus, ]);
        // 6.2需求，家庭成员数限制
        if ($familyMemberControl == '1') {
            if ($oldCallType['Flags'] % 2 == 1) {
                $familyNumber = $oldCallType['Flags'];
            } else {
                $familyNumber = $oldCallType['Flags'] + 1;
            }
            $this->db->update2ListWKey('PersonalAccountCnf', [':Account' => $data['Account'], ':CallType' => $callType, ':Flags' => $familyNumber, ':AllowCreateSlaveCnt' => $allowCreateSlaveCnt], 'Account');
        } else {
            if ($oldCallType['Flags'] % 2 == 1) {
                $familyNumber = $oldCallType['Flags'] - 1;
            } else {
                $familyNumber = $oldCallType['Flags'];
            }
            $this->db->update2ListWKey('PersonalAccountCnf', [':Account' => $data['Account'], ':CallType' => $callType, ':Flags' => $familyNumber], 'Account');
        }

        $this->services['sip']->phoneStatusCheck($data['PhoneStatus'], $newPhoneStatus, $data['Account']);

        if ($data['EnableIpDirect'] != $enableIpDirect) {
            $this->services['sip']->setIpDirect('Devices', $data['Account'], $enableIpDirect);
        }

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeLogEditApt, $this->env, [$aptName], $account);
        if ($callType != $oldCallType['CallType']) {
            $unitName = $this->db->querySlist('select B.UnitName from PersonalAccount A join CommunityUnit B where A.ID=:ID', [':ID' => $ID])[0]['UnitName'];
            $this->auditLog->setLog(AuditCodeCallTypeArray[$callType], $this->env, [$unitName, $aptName], $account);
        }
    }

    //V6.1 APT Info接口
    public function getPMAptInfo()
    {
        $params = [
            'userAliasId' => '',
            'ID' => '',
            'SelfTimeZone' => '',
            'SelfCustomizeForm' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userAliasId'];
        $ID = $params['ID'];
        $bindArrayID = [':ID' => $ID];
        // 获取account信息
        $sql = 'select PA.ID, CU.UnitName as Building, CR.RoomName as AptNumber, CR.Floor, PA.RoomNumber as AptName, PA.Special, PA.EnableIpDirect,PA.TempKeyPermission,PC.AllowCreateSlaveCnt,PC.Flags as FamilyMemberControl 
                from PersonalAccount PA left join CommunityRoom CR on PA.RoomID = CR.ID left join CommunityUnit CU on PA.UnitID = CU.ID left join PersonalAccountCnf PC
                on PA.Account = PC.Account where PA.ID=:ID order by PA.CreateTime desc';
        $account = $this->db->querySList("$sql", $bindArrayID)[0];
        if ($account['Floor'] !== '') {
            $floor = $account['Floor'];
            $account['AptNumber'] = $account['AptNumber'].' ('.MSGTEXT['floor']." $floor)";
        }
        $timeZone = $params['SelfTimeZone'];
        $customizeForm = $params['SelfCustomizeForm'];

        //获取Residents信息
        if ($account['Special'] == 1) {
            $residents = [];
        } else {
            $residentSql = 'select ID,Name, MobileNumber,Email,CreateTime,Role from PersonalAccount where ID = :ID or ParentID= :ID order by Role asc';
            $residents = $this->db->querySList($residentSql, $bindArrayID);
        }
        $residents = \util\time\setQueryTimeZone($residents, $timeZone, $customizeForm);
        //获取Devices信息,目前只展示AK设备
        $devicesSql = 'select D.ID, D.Type, D.Location, D.MAC, D.Status from PersonalAccount PA  join Devices D on PA.Account = D.Node where PA.ID = :ID and D.Brand = 0';
        $devices = $this->db->querySList($devicesSql, $bindArrayID);

        //获取CallType信息
        $callTypeSql = 'select PAC.CallType from PersonalAccount PA join PersonalAccountCnf PAC on PA.Account = PAC.Account where PA.ID = :ID';
        $callType = intval($this->db->querySList($callTypeSql, $bindArrayID)[0]['CallType']);

        unset($account['Special']);
        $account['EnableIpDirect'] = intval($account['EnableIpDirect']);
        $account['TempKeyPermission'] = intval($account['TempKeyPermission']);
        $data = $account;
        $data['ID'] = intval($data['ID']);
        $data['CallType'] = $callType;

        // 获取社区高级功能，此处限制QRCode
        $this->log->actionLog("#model#apartment#getPMAptInfo#ID=$userId");
        $FeatureExpireTime = $this->db->querySList('select FeatureExpireTime from CommunityInfo where AccountID = :ID', [':ID' => $userId])[0]['FeatureExpireTime'];
        $Item = $this->db->querySList('select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID', [':ID' => $userId])[0]['Item'];
        $IsDefaultFeature = $this->db->querySList('select FeatureID from ManageFeature where AccountID = :ID', [':ID' => $userId])[0]['FeatureID'];
        $FeatureItem = communityData\checkFeaturePlan($FeatureExpireTime, $Item, $IsDefaultFeature);

        $data['LimitCreateQRcode'] = intval($FeatureItem[2]);
        $data['LimitFamilyMember'] = intval($FeatureItem[3]);
        $data['FamilyMemberControl'] = intval(str_split($account['FamilyMemberControl'])[0]);
        $data['AllowCreateSlaveCnt'] = $account['AllowCreateSlaveCnt'];

        foreach ($residents as &$master) {
            $masterSql = 'select ExpireTime,Active from PersonalAccount where ID = :ID';
            $masterData = $this->db->querySList($masterSql, ['ID' => $master['ID']])[0];
            if ($masterData['Active'] == 0) {
                $master['Mark'] = 'Inactivated';
            } elseif ($masterData['Active'] == 1) {
                if ($masterData['ExpireTime'] < time()) {
                    $master['Mark'] = 'Activation';
                } else {
                    $master['Mark'] = 'Expired';
                }
            }
        }
        $data['Residents'] = $residents;
        $data['Devices'] = $devices;
        foreach ($devices as &$device) {
            $device['Status'] = intval($device['Status']);
            $device['Type'] = intval($device['Type']);
        }
        $data['Devices'] = $devices;

        \util\computed\setGAppData(['data' => $data]);
    }

    public function getComIndoorMonitor()
    {
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityID = $params["userAliasId"];
        $IsDefaultFeature = $this->db->querySList('select FeatureID from ManageFeature where AccountID = :ID', [':ID' => $communityID])[0]['FeatureID'];
        if ($IsDefaultFeature == 0) {
            $isComMonitor = 0;
        } else {
            $item = $this->db->querySList('select Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID 
            where M.AccountID = :ID', [':ID' => $communityID])[0]['Item'];
            //是否有室内机方案
            $isComMonitor = \util\computed\getSpecifyBitLE($item, 5);
        }
        \util\computed\setGAppData(['isComMonitor' => $isComMonitor]);
    }
}
