<?php
/**
 * @Author:zyc
 * @Date 2021/8/17
 * @LastEditors:zyc
 * @Description:国内云PM和ins账号用手机号注册主从账号需要添加到默认落地账号
 */

namespace middleware;

use \interfaces\middleware\main\IMiddleware;

include_once __DIR__ . "/../interfaces/middleware/main.php";

class CSetInitLandline implements IMiddleware
{
    public function handle(\Closure $next) {
        global $cLog;
        $params = [
            "MobileNumber" => "",
            "Phone" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mobileNumber = $params['MobileNumber'];
        $phone = $params['Phone'];
        $initLandline = '';
        if ($mobileNumber && $phone === '' && SERVER_LOCATION==='cn') {
            include_once __DIR__ . "/../notify/xiamen_phone.php";
            if (IsXiamenPhone($mobileNumber)) {
                $initLandline = $mobileNumber;
            } else {
                $initLandline = '0' . $mobileNumber;
            }
            \util\computed\setGAppData(["Phone" => $initLandline]);
        }
        $cLog->actionLog("#middle#setInitLandline#.phone:$initLandline");
        $next();
    }
}