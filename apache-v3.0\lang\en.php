<?php
  const MSGTEXT = [ 

"accountExits"=>"This account already exists",
"accountNotExit"=>"Account doesn't exist",
"accountIncorrect"=>"Invalid User Name or Password",
"accountNIncorrect"=>"Invalid Account",
"activeEmpty"=>"Active value is required",
"addFail"=>"Adding Failed",
"addSuccess"=>"Adding succeeded.",
"addSuccessPw"=>"Adding succeeded,The password is '%s'.",
"addTmpKeyFail"=>"Add TEMP key failed, please try it again",
"aptDuplicated"=>"Apartment %s is duplicated",
"aptDigits"=>"Apartment %s is invalid, which must be between 1 and 6 digit numbers",
"aptExit"=>"Apartment %s already exists",
"abnormal"=>"Abnormal",
"activation"=>"Activation",
"additionalApp"=>"Additional App",
"bindDevice"=>"Please delete all devices under this account",
"bindMAClibrary"=>"Please delete MAC at MAC library",
"bindUser"=>"Please delete users under this account",
"buildingBindDevice"=>"Please delete devices under this building",
"buildingBindUser"=>"Please delete users under this building",
"buildingDigits"=>"Invalid building %s, which must be between 1 and 2 digit numbers",
"buildingExit"=>"Building already exists",
"BindingDeviceFailed"=>"Bind device failed, the device may be bonded to other user or haven't added to MAC library ",
"chcekMacExits"=>"Adding failed, MAC address invalid or already exists",
"changePasswdFail"=>"Modifying password failed",
"changePasswdPEmail"=>"Modifying password succeed, please check at email %s",
"community"=>"Community",
"deleteFail"=>"Delete Failed",
"deleteSuccess"=>"Delete Successfully",
"deviceTypeEmpty"=>"Device type is required",
"deviceNotFindUser"=>"Device not found, please contact your administrator",
"dealSuccess"=>"Set Success",
"doorUnit"=>"Door Unit",
"emailExits"=>"Email already exists",
"emailPExit"=>"Email %s already exists",
"emailNotExits"=>"This Email doesn't exist.",
"emailDuplicated"=>"Email %s is duplicated",
"errorVersion"=>"Error Version",
"emailOrAccountNotExit"=>"This Email doesn't exist.",
"firstNameEmpty"=>"First name is required",
"failed"=>"Failed",
"family"=>"Family",
"guardPhone"=>"Guard Phone",
"incorrectSipAccount"=>"No more available SIP account",
"incorrectSipAccountGroup"=>"No more available SIP group",
"importDataSuccess"=>"Import data success",
"importFailMACExit"=>"Import failed, please check whether the MAC address exists or valid:\r\n%s",
"invaildDC"=>"Invalid Device Code",
"InvalidFile"=>"Invalid File",
"invalidPEmail"=>"Invalid Email %s",
"invalidPName"=>"Invalid user name %s",
"invalidPCalltype"=>"Invalid call type %s",
"invalidPPin"=>"Invalid PIN %s",
"invalidPActive"=>"Invalid active value %s",
"invalidPage"=>"Invalid page",
"invalidPDeviceType"=>"Invalid device type %s",
"invaildVerCode"=>"Invalid verification code",
"invalidIdentity"=>"Invalid identity information! You may have logged in elsewhere, please login again.",
"indoorMonitor"=>"Indoor Monitor",
"inactivated"=>"Inactivated",
"normal"=>"Normal",
"expired"=>"Expired",
"lastNameEmpty"=>"Last name is required",
"locationEmpty"=>"Location is required",
"locationPLoog"=>"%s too long location",
"locationLoog"=>"Too long location",
"loginError"=>"Login Error",
"loginFail"=>"Login Failed",
"loginSuccess"=>"Login Success",
"limitIP"=>"You are trying too often, please try again in 5 minutes",
"limitDevice"=>"Your device number has reached the maximum limit",
"MAC2PLibrary"=>"MAC address:%s is invalid, please check your MAC library.",
"MAC2Library"=>"MAC address invalid, please check your MAC library",
"macExits"=>"MAC address already exists",
"MACLength"=>"MAC address length must be 12 digits.",
"modifySuccess"=>"Modify Success",
"modifyFailed"=>"Modify Failed",
"maxHouse"=>"The users' number has reached the maximum limit, please contact the administrator",
"modifyAptFail"=>"Saving failed!the APT No. already exists, you should delete it firstly.",
"nameloog"=>"User name too long, the user name can contain up to 64 characters",
"nameExit"=>"User name already exists",
"notPermission"=>"You don't have operate permission",
"noSip"=>"No more SIP account",
"passwordIncorrect"=>"Password Incorrect",
"passwdChangeSuccess"=>"Change password succeed",
"passwordResetSuccess"=>"Reset Password Success",
"passwordReset2"=>"The password has been reset to '%s'.",
"payTimeOut"=>"Pay Time Out",
"payFailed"=>"Pay Failed",
"processing"=>"Processing",
"paySuccess"=>"Payment Successful",
"redirectedOnRPS"=>"This MAC address is redirected on RPS.",
"registerFailed"=>"Register Failed",
"registerSuccess"=>"Register Success",
"roomNotExit"=>"This user doesn't exist!",
"RFCardExit"=>"This RF card has already exists",
"registered"=>"Registered",
"PrivateKeyExists"=>"This private key has exists",
"passwordCorrect"=>"Invalid Password",
"timeLessCurrent"=>"Invalid Upgrade Time",
"timeZoneChangeSuccess"=>"Change time zone succeed",
"timeOut"=>"Timeout",
"unbindMACUser"=>"Please unbind %s with the user first",
"unKnowDT"=>"Unknown Device Type",
"userBindUser"=>"Please delete users under this account first",
"userNotExit"=>"This user doesn't exists",
"userMaxPLimt"=>"Create failed, you can only add up to %s family members",
"unregistered"=>"Unregistered",
"validMAC"=>"Please input a valid MAC address",
"versionExit"=>"The version has already exists",
"versionNameNumberExit"=>"Version name or number has already exists",
"sipStatus"=>"SIP account assignment failed, please it try again",
"sentCodeLater"=>"We have sent a verification code to you, please try it again later",
"setSuccess"=>"Setup Success",
"sendEmailSuccess"=>"Send Email Success",
"SetFailed"=>"Setting Failed",
"stairPhone"=>"Stair Phone",
"successed"=>"Succeed",
"subscription"=>"Renew",
"wallPhone"=>"Wall Phone",
"emailMaxLen"=>"The email must be less than 64 characters.",
"serverUpgradeTips"=>"The server upgrade is complete, please refresh the page. Before that, you may copy the data you just input to other where.",
"ActiveFamilyAccount"=>"Please activate the family master's account first.",
"weekly"=>"Weekly",
"daily"=>"Daily",
"never"=>"Never",
"calltypeEmpty"=>"Call type is required",
"addOutApt"=>"You can only add up to %s rooms",
"call"=>"Call",
"unlock"=>"Unlock",
"tryUnlockCall"=>"Call Unlock Failed",
"tryUnlockKey"=>"PIN Code Unlock Failed",
"tryUnlockCard"=>"RF Card Unlock Failed",
"tryUnlockFace"=>"Face Unlock Failed",
"unlockApp"=>"SmartPlus Unlock",
"unlockIndoor"=>"Indoor Monitor Unlock",
"unlockNFC"=>"NFC Unlock",
"unlockBluetooth"=>"Bluetooth Unlock",
"unlockCard"=>"RF Card Unlock",
"unlockPrivateKey"=>"PIN Code Unlock",
"unlockTempKey"=>"Temp Key Unlock",
"alarmDoorUnlock"=>"Door Unlock",
"alarmInfrared"=>"Infrared",
"alarmSmoke"=>"Smoke",
"alarmGas"=>"Gas",
"alarmUrgency"=>"Urgency",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Tamper",
"alarmGate"=>"Gate",
"alarmDoor"=>"Door",
"alarmBedroom"=>"Bedroom",
"alarmGuestRoom"=>"Guest room",
"alarmHall"=>"Hall",
"alarmWindow"=>"Window",
"alarmBalcony"=>"Balcony",
"alarmKitchen"=>"Kitchen",
"alarmStudy"=>"Study",
"alarmBathroom"=>"Bathroom",
"alarmArea"=>"Area",
"RFCardExit2"=>"RF card %s has already exists",
"RFCardDuplicated"=>"RF card %s is duplicated",
"notMacBind"=>"The user '%s' does not have permission to open the door of connect with device '%s'.",
"accountNumLet"=>"Account must consist of numbers and letters",
"networkUnavailable"=>"Network Unavailable.",
"notForModel"=>"Not for this model.",
"upgradeDevVersion"=>"Please upgrade to the latest version first.",
"unavailableService"=>"Service is temporarily unavailable, please try again later.",
"cantDeletePin"=>"You can not delete pin %s",
"residentInRoom"=>"There are already resident in the room %s",
"noAnswer"=>"No Answer",
"indoorAndApp"=>"Both Indoor Monitor and App",
"indoorMonitorOnly"=>"Indoor Monitor Only",
"appOnly"=>"App Only",
"endThanStart"=>"The end time cannot be earlier than the start time.",
"endThanStartFile"=>"Invalid day or time in line '%s'.",
"doorRelease"=>"Door Release",
"success"=>"Success",
"unlockFACE"=>"Face Unlock",
"unlockBLE"=>"Bluetooth Unlock",
"captureSmartPlus"=>"Capture on SmartPlus",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"Failed to unlock",
"deviceDisconnected"=>"The device was disconnected.",
"low"=>"Low",
"motion"=>"Motion",
"capture"=>"Capture",
"failedImport"=>"Import failed",
"notValidMobile"=>"%s is not a valid mobile number.",
"mobileExits"=>"Mobile Number already exists",
"mobileExits2"=>"Mobile Number %s has already exists",
"mobileDuplicated"=>"Mobile Number %s is duplicated",
"mobileNumberExist"=>"Mobile Number doesn't exist.",
"codeIncorrect"=>"Invalid Code",
"sendCodeSuccess"=>"Send verification code successfully",
"codeCorrect"=>"Correct",
"mobileNumberEmpty"=>"Please enter your mobile number.",
"invalidUser"=>"Invalid user %s",
"locationExits"=>"Location address already exists",
"smartPlusIndoor"=>"SmartPlus and indoor monitors",
"phoneIndoor"=>"Phone and indoor monitors",
"smartPlusIndoorBackup"=>"SmartPlus and indoor monitors, with phone as backup",
"smartPlusBackup"=>"Indoor monitors with SmartPlus as backup",
"indoorPhoneBackup"=>"Indoor monitors with phone as backup",
"indoorSmartPlusPhone"=>"Indoor monitors with SmartPlus as backup, finally the phone",
"endUser"=>"End User",
"installer"=>"Installer",
"distributor"=>"Distributor",
"pm"=>"PM",
"superManage"=>"SuperManage",
"loginManagement"=>"Login Management",
"accessControl"=>"Access Control",
"userManagement"=>"User Management",
"deviceManagement"=>"Device Management",
"communityManagement"=>"Community Management",
"auditLogin"=>"Log In: Web",
"auditLogout"=>"Log Out: Web",
"auditAddTempKey"=>"Add temporary key: {0}",
"auditEditTempKey"=>"Edit temporary key: {0}",
"auditDeleteTempKey"=>"Delete temporary key: {0}",
"auditAddRFCard"=>"Add RF card: {0}",
"auditEditRFCard"=>"Edit RF card: {0}",
"auditDeleteRFCard"=>"Delete RF card: {0}",
"auditAddDis"=>"Add distributor: {0}",
"auditEditDis"=>"Edit distributor: {0}",
"auditDeleteDis"=>"Delete distributor: {0}",
"auditAddInstaller"=>"Add installer: {0}",
"auditEditInstaller"=>"Edit installer: {0}",
"auditDeleteInstaller"=>"Delete installer: {0}",
"auditAddPM"=>"Add PM: {0}",
"auditEditPM"=>"Edit PM: {0}",
"auditDeletePM"=>"Delete PM: {0}",
"auditAddEndUser"=>"Add end user: {0}",
"auditEditEndUser"=>"Edit end user: {0}",
"auditDeleteEndUser"=>"Delete end user: {0}",
"auditSetOwnerTime"=>"Set own timezone {0}",
"auditSetOwnPassword"=>"Set Own Password",
"auditAddPIN"=>"Add PIN: {0}",
"auditEditPIN"=>"Edit PIN: {0}",
"auditDeletePIN"=>"Delete PIN: {0}",
"auditImportFace"=>"Import face: {0}",
"auditDeleteFace"=>"Delete face: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Set call type smartPlus and indoor monitors: {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"Set call type phone and indoor monitors: {0}&{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Set call type smartPlus and indoor monitors, with phone as backup: {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"Set call type indoor monitors with SmartPlus as backup: {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Set call type indoor monitors with phone as backup: {0}&{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Set call type indoor monitors with SmartPlus as backup, finally the phone: {0}&{1}",
"auditDeleteDevice"=>"Delete device: {0}",
"auditSetAPTCount"=>"Set number of apartments {0}",
"auditEnableLandline"=>"Enable landline service",
"auditDisableLandline"=>"Disable landline service",
"auditSetSubTime"=>"Set timezone {0}",
"auditSetChargeModeInstall"=>"Set charging model pay by installer",
"auditSetChargeModeUser"=>"Set charging model pay by user/PM",
"auditSetConnectTypeDefault"=>"Set connection type default",
"auditSetConnectTypeTCP"=>"Set connection type tcp",
"auditSetConnectTypeUDP"=>"Set connection type udp",
"auditSetConnectTypeTLS"=>"Set connection type tls",
"auditAddCommunity"=>"Add community: {0}",
"auditDeleteCommunity"=>"Delete community: {0}",
"auditImportCommunity"=>"Import community: {0}",
"auditSetAPTNumber"=>"Set {0} room number {1}",
"auditSetEmail"=>"Set email {0}: {1}",
"auditSetMobile"=>"Set phone number {0}: {1}",
"auditDeviceTypeStair"=>"Set type Multi-tenants Doorphone: {0}",
"auditDeviceTypeDoor"=>"Set type Single-tenant Doorphone: {0}",
"auditDeviceTypeIndoor"=>"Set type Indoor Monitor: {0}",
"auditDeviceTypeGuardPhone"=>"Set type Guard Phone: {0}",
"auditDeviceTypeAccessControl"=>"Set type Access Control: {0}",
"auditSetNetGroup"=>"Set network group {0}: {1}",
"auditEditCommunity"=>"Edit community",
"deliveryMsg"=>"You have %s items that have been delivered to you, please check in time.",
"deliveryTitle"=>"You've got a new package!",
"rfcardDuplicatedLines"=>"Duplicated RF card nubmer in line %s!",
"rfcardNameInvalid"=>"Invalid RF Card name in line %s!",
"rfcardExistLines"=>"RF cards have already existed in line %s.",
"importFailMacExistLines"=>"the MAC address exists or valid in line %s.",
"exportExcelCountNull"=>"No log to export on the date! Please select again.",
"keyIsEqualRoom"=>"Delivery key can not be identical with APT number!",
"visitor"=>"visitor",
"CommunityNameExist"=>"Community name already exists",
"unlockGuardPhone"=>"Guard Phone Unlock",
"auditLoginApp"=>"Log In: App",
"auditLogoutApp"=>"Log Out: App",
"timeForYesterday"=>"Yesterday",
"exportExcelDataBefore"=>"Too large data! Please first export the data before %s first,thanks.",
"tempkeyUsed"=>"TempKey Used",
"tempkeyContent"=>"%s has used the TempKey.",
"accessNameExist"=>"Access Group Name already exists",
"addFaceFail"=>"Please import a clear photo of face.",
"userInvalid"=>"Invalid user in line %s.",
"groupsInvalid"=>"Invalid acess group in line %s.",
"BuildAccessName"=>"Resident-Building %s",
"auditCodeLogEditApt"=>"edit apartment:{0}",
"invalidTimeInLine"=>"Invalid time in line %s.",
"cancel"=>"Cancel",
"cancelSuccess"=>"Cancel succeeded.",
"payOutstanding"=>"Please check if there is any unpaid orders, if not, please contact your service provider",
"featureDeleteError"=>"Feature Plan Be bound.",
"beyondFamilyMember"=>"You can't create more family member accounts, please contact your service provider to create it.",
"indoorMonitorRequired"=>"At least one indoor monitor is required for each apartment.",
"featureActivationFee"=>"Feature(One-time Fee)",
"systemProcessing"=>"System Processing",
"featureMonthlyFee"=>"Feature(Monthly Fee)",
"featurePriceDifferences"=>"Feature(Price differences)",
"updatingSuccess"=>"Updating Succeeded!",
"featureNameBasic"=>"Basic",
"featureNamePremium"=>"Premium",
"indoorMacNotCorrect"=>"Please enter the correct Indoor Monitor MAC.",
"off"=>"Off",
"enterValidAccount"=>"Please enter a valid account",
"invalidKitImportMAC"=>"Please check whether the MAC exists or valid: %s",
"importLessData"=>"Please import less than %s data.",
"invalidQRCode"=>"Identifying Failed, please scan a correct QR code.",
"cannotCreateFamilyMember"=>"You can't create more family member accounts.",
"importProcessing"=>"Importing, please try again later",
"departmentAccessName"=>"%s Access Group",
"idExistsLine"=>"ID already exists in line %s",
"enterFirstNameLine"=>"Please enter First Name in line %s",
"enterLastNameLine"=>"Please enter Last Name in line %s",
"departmentExist"=>"Department already exists",
"idExist"=>"ID already exists",
"layoutIdInvalid"=>"Layout is invalid",
"unlockAppHome"=>"AKHome Unlock",
"officeNameExist"=>"Office name already exists",
"departmentExit"=>"Department already exists.",
"importOutTask"=>"You can only import a template at a time.",
"idDuplicated"=>"ID %s is duplicated",
"aptInvalidLine"=>"Invalid APT in line %s.",
"buildInvalidLine"=>"Invalid Building in line %s.",
"departmentInvalidLine"=>"Invalid Department in line %s.",
"idInvalidLine"=>"Invalid ID in line %s.",
"propertyManager"=>"Property Manager",
"departmentBindDevice"=>"Please delete devices under this department.",
"departmentBindUser"=>"Please delete users under this department.",
"smartPlusValidLine"=>"Invalid Smartplus Intercom Feature in line %s.",
"identityValidLine"=>"Invalid Identity in line %s.",
"eachDoorCount"=>"A single plan to open each door once",
"textUpgradeMsg1"=>"Your account already supports the management of multiple sites, please upgrade smartplus to the latest version.",
"textUpgradeMsg2"=>"Login failed",
"deleteCodeGetLimitTimes"=>"Invalid key.Please try again 24 hours later.",
"deleteCodeOverLimitTimes"=>"Please try again 24 hours later.",
"deleteCodeError"=>"Invalid key",
"textUpgradeMsg"=>"1.Optimized the tempkey function.;2.Added the account cancellation function.;3.Fixed some bugs.",
"paramsError"=>"Parameter error",
"pmappStatusInvalid"=>"Please enable PM App first.",
"delivery_description"=>"Delivery Temp Key",
"webRelayIDInvalidLine"=>"Invalid Web Relay ID in line %s.",
"relayInvalid"=>"Invalid relay in line %s.",
"cancelError"=>"Cancel failed.",
"textUpgradeMsgForComRole"=>"upgrade community role",
"textUpgradeMsgForPerRole"=>"upgrade Personnel role",
"textUpgradeMsgForOffRole"=>"upgrade office role",
"textUpgradeMsgForPMRole"=>"upgrade PM role",
"lockApp"=>"SmartPlus Lock",
"lock"=>"Lock",
"versionLogMaxLen"=>"Version log cannot be larger than %s characters",
"autoLock"=>"Auto Lock",
"pinAndRFcardNotNullLines"=>"At least one of the PIN and RF Card in line %s needs to be filled in!",
"pinExistLines"=>"PIN have already existed in line %s.",
"pinInvalidLines"=>"Invalid PIN in line %s!",
"pinDuplicatedLines"=>"Duplicated PIN in line %s!",
"FaceImportLength"=>"The size of the face import file cannot be larger than %s",
"landlineServerNotActivate"=>"This community has not activate landline service.",
"importFailDisNotExist"=>"Distributor does not exist",
"importFailNotPermission"=>"You do not have the permission to add this MAC address.",
"importFailTooManyAdd"=>"Import failed,for single distributor only.",
"importFailAdded"=>"This MAC address has already been added by another user.",
"macAssignToLimit"=>"You can only assign up to 10 distributors",
"macNumToLimit"=>"You can only upload up to 1000 MAC addresses at a time.",
"addOutFloor"=>"Please enter a number between 1 ~128. ",
"floor"=>"Floor",
"PostalCodeInvalid"=>"Please enter letter or number.",
"onceCodeInvalid"=>"The once code must be 4-5 digits.",
"permanentCodeInvalid"=>"The permanent code must be 6 digits.",
"onceCodeOutNum"=>"You can only add up to 10 once code.",
"permanentCodeOutNum"=>"You can only add up to 10 permanent code.",
"onceCodeExist"=>"The once code already exists.",
"permanentCodeExist"=>"The permanent code already exists.",
"addOutFloorLine"=>"Invalid floor number in line %s.",
"auditManuallyUnlock"=>"Manually Unlock",
"auditManuallyLock"=>"Manually Lock",
"automaticallyUnlock"=>"Automatically Unlock",
"doorClose"=>"Door Close",
"PostalCodeNotEmpty"=>"Please enter at least one letter or number.",
"emergencyAlarm"=>"Emergency Alarm",
"doorSensor"=>"Door Sensor",
"yaleBatteryWarning"=>"Yale Battery Warning",
"auditCodeManuallyUnlock"=>"Manually Unlock",
"auditCodeManuallyLock"=>"Manually Lock",
"2weekBatteryWarning"=>"%s - Estimated battery time remaining: 2 weeks.",
"1weekBatteryWarning"=>"%s - Estimated battery time remaining: 1 week.",
"replaceBatteryWarning"=>"%s - Battery level is extremely low, please replace immediately.",
"open"=>"Open",
"close"=>"Close",
"addContactFavoriteNum"=>"Adding to favorites failed.you can only add up to 300 favorite apartments.",
"addContactBlockNum"=>"Adding to blocklist failed.you can only add up to 100 apartments to blocklist.",
"voiceTitle"=>"Voice Message",
"voiceContent"=>"You have a voice message from %s",
"voiceMsgInvalid"=>"Voice message has expired.",
"toggleFeaturePlan"=>"You can't change feature plan.",
"rtspAddresEmpty"=>"Please enter RTSP Address.",
"rtspAddresInvalid"=>"Invalid RTSP Address.",
"rtspPortEmpty"=>"Please enter port.",
"rtspPortInvalid"=>"Invalid Port.",
"rtspPassWdEmpty"=>"Please enter password.",
"rtspPassWdInvalid"=>"Password too long, the password can contain up to 63 characters.",
"cameraExist"=>"The camera already exists.",
"errorOnRPS"=>"Failed to request RPS server",
"faceImportErrorSystem"=>"System Error",
"faceImportErrorView"=>"Not front view",
"faceImportErrorWearMask"=>"Mask detected",
"faceImportErrorLowResolution"=>"Resolution is too low",
"faceImportErrorWrongFormat"=>"File format error",
"faceImportErrorNoFace"=>"No face dectected",
"faceImportErrorFileLarge"=>"The file is too larger",
"faceImportErrorFaceLarge"=>"The face is too larger",
"faceImportErrorFaceSmall"=>"The face is too small",
"faceImportErrorMultiFaces"=>"More than one face",
"faceImportErrorWrongName"=>"File name is error.",
"faceImportErrorEmptyName"=>"Resident's name is empty.",
"faceImportErrorNoAccountInfo"=>"Get PersonalAccount Info error.",
"faceImportErrorAccountInactive"=>"The PersonalAccount is not active.",
"changeHomeFeatureInvalid"=>"Operation failed! There are installers of this distributor using the 'Home Automation' feature.",
"changeInterComFeatureInvalid"=>"Operation failed! There are installers of this distributor using the 'Intercom' feature.",
"offline"=>"Failed: Offline",
"allFloors"=>"All Floors",
"uploadOversize"=>"The size of the upload file cannot be larger than %s",
"uploadInvalidType"=>"The uploaded file type is not supported",
"uploadFailed"=>"Upload Failed, please try later",
"uploadScreenSaverImgTooMuch"=>"Screensaver pictures cannot be more than %s!",
"screenSaverImgTooLittle"=>"Screensaver pictures cannot be less than %s!",
"screenSaverImgTooMuch"=>"Screensaver pictures cannot be more than %s!",
"screenSaverDevicesOffline"=>"Save failed. The device is offline.",
"saveFailed"=>"Save failed.",
"importingInProgress"=>"Importing in progress, please try again later.",
"importBuildingInvalidLine"=>"Invalid building in line %s",
"importAptInvalidLine"=>"Invalid Apt in line %s",
"importAccountTypeInvalidLine"=>"Invalid Account Type in line %s",
"importFirstNameInvalidLine"=>"Invalid First Name in line %s",
"importLastNameInvalidLine"=>"Invalid Last Name in line %s",
"importKeyInvalidLine"=>"Invalid key in line %s",
"importKeyExistsLine"=>"PIN exists in line %s",
"importCardInvalidLine"=>"Invalid RF Card in line %s",
"importCardExistsLine"=>"RF Card exists in line %s",
"importAccessGroupInvalidLine"=>"Invalid Access Group ID in line %s",
"importAccessGroupNoPermissionLine"=>"No permission Access Group ID in line %s",
"importExceededNumberLine"=>"Exceeded the number of family member in line %s",
"importNoActiveMasterLine"=>"Importing failed in line %s, please activate the family matser first.",
"importMasterExistsLine"=>"Family master already exists in line %s.",
"importNoCreateMasterLine"=>"Importing failed in line %s, please create the family matser first.",
"PrivateKeysDataExist"=>"The private key %s already exist.",
"PrivateKeyDataExists"=>"The private key %s already exists.",
"landLineOpenToClosedFail"=>"Save failed. Please make sure the call type of the apartments in the project are \"SmartPlus and indoor monitor\" or \"Indoor monitors with SmartPlus as backup\"",
"limitWithIp"=>"You are trying too often, please try again in 5 minutes.(IP: %s)",
"subDistributor"=>"Sub Distributor",
"faceImportErrorNotClear"=>"The imported picture is not clear.",


  ];
