<?php

namespace dao;
use framework\BasicDao;
use function Symfony\Component\String\s;

class SystemNotify extends BasicDao
{
    //当前表名
    public $table = 'SystemNotify';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/11/12 09:26 V6.5.4
     * @lastEditors: systemCreator 2024/11/12 09:26 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    public function getNotifyForSuper($bindArray, $offset, $row)
    {
        $fields = "select sn.ID,sn.UUID,sn.Title,sn.Content,sn.CreateTime,sn.UpdateTime,sn.Role";
        $countSql = "select count(*)";
        $sql = " from SystemNotify sn where AccountUUID = :AccountUUID";

        if (!empty($bindArray[':SearchValue'])) {
            $bindArray[':SearchValue'] = '%' . $bindArray[':SearchValue'] . '%';
            if ($bindArray[':SearchKey'] == 'Title') {
                $sql = $sql . ' and sn.Title like :SearchValue';
            } elseif ($bindArray[':SearchKey'] == 'Content') {
                $sql = $sql . ' and sn.Content like :SearchValue';
            }
        }

        if (!empty($bindArray[':Role'])) {
            $sql = $sql . ' and ((sn.Role & :Role) != 0 or Role =1)';
        }

        $sql = $sql . ' Order by sn.UpdateTime desc';
        $sqlLimit = " limit $offset, $row";
        $systemNotifyList = $this->execute("$fields $sql $sqlLimit", $bindArray);
        $total = intval($this->execute("$countSql $sql", $bindArray)[0]['count(*)']);

        foreach($systemNotifyList as &$value) {
            $value['TotalCount'] = $this->execute('select count(*) from SystemNotifyList where SystemNotifyUUID=:SystemNotifyUUID', [':SystemNotifyUUID'=>$value['UUID']])[0]['count(*)'];
            $value['ReadCount'] = $this->execute('select count(*) from SystemNotifyList where SystemNotifyUUID=:SystemNotifyUUID and IsRead=1', [':SystemNotifyUUID'=>$value['UUID']])[0]['count(*)'];
            $value['ReceiverCount'] = $this->execute('select count(*) from SystemNotifyList where SystemNotifyUUID=:SystemNotifyUUID and IsReceiver=1', [':SystemNotifyUUID'=>$value['UUID']])[0]['count(*)'];
        }
        unset($value);

        return [$total, $systemNotifyList];
    }

}