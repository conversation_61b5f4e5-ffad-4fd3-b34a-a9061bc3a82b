<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";
include_once __DIR__."/../staff/util.func.php";

include_once __DIR__."/add.php";
include_once __DIR__."/query.php";
include_once __DIR__."/update.php";
include_once __DIR__."/remove.php";

class CPmKey
{
    public $tables = ["PubPrivateKey","PubRfcardKey"];
    public $listTables = ["PubPrivateKeyList","PubRfcardKeyList"];
    public $personalTables = ["PersonalPrivateKey","PersonalRfcardKey"];
    public $personalListTables = ["PersonalPrivateKeyList","PersonalRfcardKeyList"];
    public $commPerTables = ["CommPerPrivateKey", "CommPerRfKey"];
    public $owernType;
    public $type = 0;

    use \model\pmKey\query;
    use \model\pmKey\add;
    use \model\pmKey\update;
    use \model\pmKey\remove;
}
