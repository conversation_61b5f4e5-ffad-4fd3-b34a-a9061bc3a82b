<?php

namespace model\order;

trait add
{
    /**
     * @msg: 物业创建社区订单
     * @services:charge
     */
    public function createdCommunityOrder()
    {
        global $cMessage;
        $params = [
            'CommunityID' => '',
            'Type' => '',
            'Total' => '',
            'Count' => '',
            'Users' => '',
            // 操作者本人
            'userId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['CommunityID'];
        $type = $params['Type'];
        $total = $params['Total'];
        $month = $params['Count'];
        $users = $params['Users'];
        $userId = $params['userId'];
        $this->log->payLog("#createdCommunityOrder#communityID=$id;type=$type;count=$month;userId=$userId;users=" . json_encode($users));
        $myData = $this->db->queryAllList('Account', ['equation' => [':ID' => $userId]])[0];
        $myEmail = $myData['Email'];
        $user = $myData['Account'];
        $myID = $userId;

        $mngData = $this->db->querySList('select ChargeMode,ParentID from Account where ID = :ID', [':ID' => $id])[0];
        $chargeMode = $mngData['ChargeMode'];
        $disChargeMode = $this->db->querySList('select ChargeMode from Account where ID = :ID', [':ID' => $mngData['ParentID']])[0]['ChargeMode'];
        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        if ($chargeMode == 1) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $areaID = $this->db->queryAllList('Account', ['equation' => [':ID' => $id]])[0]['ParentID'];

        //v7.0新增办公场景
        $chargeType = $myData['Grade'] == OFFICEGRADE ? 'office' : 'multiple';

        // if ($type == 0) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->activeUser($users, $myID, 1, $chargeType, $myEmail, $id, $areaID);
        // } elseif ($type == 1) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->subscriptionUser($users, $myID, 1, $myEmail, $chargeType, $id, $areaID, $month);
        // } elseif ($type == 2) {
        //     // 这边users需要主账号ID
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->buyOutApp($users, $myEmail, $id, $areaID, $chargeType, 1, $myID);
        // } else
        if ($type == 5) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanOnce($myID, 1, $myEmail, $id, $areaID);
        } elseif ($type == 6) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanMonthly($myID, 1, $myEmail, $id, $areaID, $month);
        } else {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $this->log->payLog("#createdCommunityOrder#orderID=$orderID;bmOrderNumber=$bmOrderNumber;code=$code");
        \util\computed\setGAppData(['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"]);
    }

    /**
     * @msg: user创建订单
     * @services:charge
     */
    public function createUserOrder()
    {
        global $cMessage;
        $params = [
            'Type' => '',
            'Total' => '',
            'Count' => '',
            // 操作者本人
            'userId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params['Type'];
        $total = $params['Total'];
        $month = $params['Count'];
        $userId = $params['userId'];
        $this->log->payLog("#createUserOrder#type=$type;count=$month;userId=$userId;");

        $myData = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $userId]])[0];
        $myID = $myData['ID'];
        $communityID = $myData['ParentID'];
        $mngData = $this->db->querySList('select ChargeMode,ParentID from Account where ID = :ID', [':ID' => $communityID])[0];
        $chargeMode = $mngData['ChargeMode'];
        $disChargeMode = $this->db->querySList('select ChargeMode from Account where ID = :ID', [':ID' => $mngData['ParentID']])[0]['ChargeMode'];
        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        if ($chargeMode == 1 || 1) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        if (in_array($myData['Role'], SUBROLE)) {
            $communityID = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $myData['ParentID']]])[0]['ParentID'];
        }
        $email = $myData['Email'] ? $myData['Email'] : ($myData['MobileNumber'] ? $myData['MobileNumber'] : $myData['Account']);
        $users = [$myID];
        $areaID = $this->db->queryAllList('Account', ['equation' => [':ID' => $communityID]])[0]['ParentID'];

        // v7.0新增办公场景
        $userType = in_array($myData['Role'], PERROLE) ? 'single' : (in_array($myData['Role'], COMROLE) ? 'multiple' : 'office');

        if ($type == 0) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->activeUser($users, $myID, 0, $userType, $email, $communityID, $areaID);
        } elseif ($type == 1) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->subscriptionUser($users, $myID, 0, $email, $userType, $communityID, $areaID, $month);
        } elseif ($type == 2) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->buyOutApp($myID, $email, $communityID, $areaID, $userType);
        } elseif ($type == 3) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->landline($users, $myID, 0, $userType, $email, $communityID, $areaID, $month);
        }
        $this->log->payLog("#createUserOrder#orderID=$orderID;bmOrderNumber=$bmOrderNumber;code=$code");
        \util\computed\setGAppData(['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"]);
    }

    /**
     * @msg: Install创建订单
     * @services:charge
     */
    public function createInstallerOrder()
    {
        global $cMessage;
        $params = [
            'Type' => '',
            'Total' => '',
            'Count' => '',
            'Users' => '',
            // 操作者本人
            'userId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params['Type'];
        $total = $params['Total'];
        $month = $params['Count'];
        $users = $params['Users'];
        $id = $myID = $userId = $params['userId'];
        // $this->log->payLog("#createInstallerOrder#communityID=$id;type=$type;count=$month;userId=$userId;users=" . json_encode($users));

        $myData = $this->db->queryAllList('Account', ['equation' => [':ID' => $userId]])[0];
        $areaID = $myData['ParentID'];
        $role = $myData['Grade'];

        $chargeMode = $this->db->querySList('select ChargeMode from Account where ID = :ID', [':ID' => $areaID])[0];
        if ($chargeMode == 1) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        // v6.3新增办公场景
        $chargeType = $role == PERSONGRADE ? 'single' : ($role == COMMUNITYGRADE ? 'multiple' : 'office');
        $myEmail = $this->db->queryAllList('Account', ['equation' => [':ID' => $myData['ManageGroup']]])[0]['Account'];

        if ($type == 5) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanOnce($myID, 2, $myEmail, $id, $areaID);
        } elseif ($type == 6) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanMonthly($myID, 2, $myEmail, $id, $areaID, $month);
        } else {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $this->log->payLog("#createInstallerOrder#orderID=$orderID;bmOrderNumber=$bmOrderNumber;code=$code");
        \util\computed\setGAppData(['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"]);
    }

    /**
     * @msg: 区域管理员创建订单
     * @services:charge
     */
    private function createAreaOrder($chargeType)
    {
        global $cMessage;
        $params = [
            'Type' => '',
            'Total' => '',
            'Count' => '',
            'Users' => '',
            'Manage' => '',
            // 操作者本人
            'userId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params['Type'];
        $total = $params['Total'];
        $month = $params['Count'];
        $users = $params['Users'];
        $installerId = $params['Manage'];
        $myID = $areaID = $params['userId'];

        // 存储购买人使用账户
        $myData = $this->db->queryAllList('Account', ['equation' => [':ID' => $myID]])[0];
        $myEmail = $myData['Account'];
        $payType = 3;
        if ($myData['Grade'] == SUBDISTRIBUTOR) {
            $payType = 4;
        }

        // v7.0办公场景
        $chargeType = $chargeType == 2 ? 'single' : ($chargeType == 1 ? 'multiple' : 'office');

        // if ($type == 0) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->activeUser($users, $myID, 3, $chargeType, $myEmail, $installerId, $areaID);
        // } elseif ($type == 1) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->subscriptionUser($users, $myID, 3, $myEmail, $chargeType, $installerId, $areaID, $month);
        // } elseif ($type == 2) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->buyOutApp($users, $myEmail, $installerId, $areaID, $chargeType, 3, $myID);
        // } elseif ($type == 3) {
        //     list($orderID, $bmOrderNumber, $token, $code) = $this->landline($users, $myID, 3, $chargeType, $myEmail, $installerId, $areaID, $month);
        // } else
        if ($type == 5) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanOnce($myID, $payType, $myEmail, $installerId, $areaID);
        } elseif ($type == 6) {
            list($orderID, $bmOrderNumber, $token, $code) = $this->communityFeaturePlanMonthly($myID, $payType, $myEmail, $installerId, $areaID, $month);
        } else {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        \util\computed\setGAppData(['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"]);
    }

    // v7.0办公场景 区域管理员创建订单
    public function createAreaOfficeOrder()
    {
        $this->createAreaOrder(3);
    }

    public function createAreaInstallOrder()
    {
        $this->createAreaOrder(2);
    }

    public function createAreaCommunityOrder()
    {
        $this->createAreaOrder(1);
    }

    /*
    $users:array 需要激活的用户,参数是主账号ID的数组
    $accountID: 购买人的ID
    $payType:支付类型，物业:1,终端用户:0,2:install,3:区域管理员
    $payer:支付人邮箱
    $installID:对应社区或者install的id
    $areaID:对应区域管理员ID
    */
    public function activeUser($users, $accountID, $payType, $type, $payer, $installID, $areaID)
    {
        global $cMessage;
        // 锁定资源
        $res = $this->isLockPayData($this->changeIDToAccount($users), 'activeUser');
        if ($res !== true) {
            $cMessage->echoErrorMsg(StatePayOutstanding);
        }

        // 向计费系统请求创建订单
        $data = $this->services['billsysUtil']->createOrder($accountID, $payType, $type, $installID, 0, 1, $users);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $type = 1;

        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $accountID, ':WebHookToken' => $data['Token'],
            ':CreateTime' => \util\computed\getNow(), ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => $type, ':Payer' => $payer,
            ':PayerType' => $payType, ':InstallID' => $installID, ':AreaManageID' => $areaID, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code'],
        ]);
        $orderID = $this->db->lastInsertId();
        foreach ($chargeData as $user) {
            $userData = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $user['User']]])[0];
            if ($payType == 1 || $payType == 2) {
                $role = $userData['Role'];
                if (in_array($role, COMROLE)) {
                    $object = $this->db->queryAllList('CommunityRoom', ['equation' => [':ID' => $userData['RoomID']]])[0]['RoomName'];
                } else {
                    $object = $userData['Name'];
                }
            } else {
                $object = $userData['Email'] ? $userData['Email'] : ($userData['MobileNumber'] ? $userData['MobileNumber'] : $userData['Account']);
            }

            if (count($user['Activity']) == 0) {
                $amount = \util\computed\inputComputedCount($user['ActiveFee']);
                $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => $type, ':Amount' => $amount, ':AppID' => $user['User'], ':Object' => $object, ':ChargeData' => json_encode($user)]);
            } else {
                $amount = \util\computed\inputComputedCount($user['Activity']['ActiveFee']);
                $this->db->insert2List('OrderEndUserList', [
                    ':OrderID' => $orderID, ':Type' => $type, ':Amount' => $amount, ':AppID' => $user['User'], ':Object' => $object,
                    ':Discount' => $user['Activity']['ActiveFeePercent'], ':DiscountInfo' => $user['Activity']['Introduction'], ':ActivityUUID' => $user['Activity']['UUID'], ':ChargeData' => json_encode($user),
                ]);
            }
            $this->lockPayData($orderNumber, [$userData['Account']], 0);
        }
        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    /*
    $users:array 用户,参数是主账号ID的数组
    $accountID: 购买人的ID
    $payType:支付类型，物业:1,终端用户:0
    $payer:支付人邮箱
    $installID:对应社区或者install的id
    $areaID:对应区域管理员ID
    $totalPrice:前端计算完成传上来的总价
    $monthlyFee:月租费,
    $appsNumber:套餐内app数,
    $addAppsFee:额外App钱
    */
    public function subscriptionUser($users, $accountID, $payType, $payer, $type, $installID, $areaID, $month)
    {
        $data = $this->services['billsysUtil']->createOrder($accountID, $payType, $type, $installID, 1, $month, $users);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        // 创建续费订单
        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $accountID, ':CreateTime' => \util\computed\getNow(), ':WebHookToken' => $data['Token'],
            ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => 2, ':Payer' => $payer, ':PayerType' => $payType, ':InstallID' => $installID,
            ':AreaManageID' => $areaID, ':Months' => $month, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code'],
        ]);
        $orderID = $this->db->lastInsertId();
        foreach ($chargeData as $val) {
            $id = $val['User'];
            if ($type == 'multiple') {
                $name = $this->db->querySList('select R.RoomName from CommunityRoom R join PersonalAccount A on R.ID = A.RoomID where A.ID = :ID', [':ID' => $id])[0]['RoomName'];
            } else {
                $name = $this->db->querySList(
                    'select Name from PersonalAccount where ID=:ID and Role in(30,31)',
                    [":ID" => $id]
                )[0]['Name'];
            }

            if (count($val['Activity']) == 0) {
                $amount = \util\computed\inputComputedCount($val['MonthlyFee']);
                $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => 2, ':Amount' => $amount, ':AppID' => $id, ':Object' => $name, ':ChargeData' => json_encode($val)]);
            } else {
                $amount = \util\computed\inputComputedCount($val['Activity']['MonthlyFee']);
                $this->db->insert2List('OrderEndUserList', [
                    ':OrderID' => $orderID, ':Type' => 2, ':Amount' => $amount, ':AppID' => $id, ':Object' => $name,
                    ':Discount' => $val['Activity']['ActiveFeePercent'], ':DiscountInfo' => $val['Activity']['Introduction'], ':ActivityUUID' => $val['Activity']['UUID'], ':ChargeData' => json_encode($val),
                ]);
            }
        }

        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    public function buyOutApp($myID, $email, $installID, $areaID, $type, $payType = 0, $owner = null)
    {
        // 订单属于谁，谁买的
        $owner = $owner ? $owner : $myID;
        $params = [
            'ID' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];

        $users = $this->db->querySList('select ParentID as ID from PersonalAccount where ID = :ID', [':ID' => $id]);

        $data = $this->services['billsysUtil']->createOrder($owner, $payType, $type, $installID, 2, 1, [$users[0]['ID']]);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'][0];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $tmpData = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $id]])[0];
        $object = $tmpData['Email'] ? $tmpData['Email'] : ($tmpData['MobileNumber'] ? $tmpData['MobileNumber'] : $tmpData['Account']);
        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $owner, ':WebHookToken' => $data['Token'],
            ':CreateTime' => \util\computed\getNow(), ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => 3, ':Payer' => $email,
            ':PayerType' => $payType, ':InstallID' => $installID, ':AreaManageID' => $areaID, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code'],
        ]);
        $orderID = $this->db->lastInsertId();
        if (count($chargeData['Activity']) == 0) {
            $amount = \util\computed\inputComputedCount($chargeData['AddAppFee']);
            $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => 3, ':Amount' => $amount, ':AppID' => $id, ':Object' => $object, ':ChargeData' => json_encode($chargeData)]);
        } else {
            $amount = \util\computed\inputComputedCount($chargeData['Activity']['AddAppFee']);
            $this->db->insert2List('OrderEndUserList', [
                ':OrderID' => $orderID, ':Type' => 3, ':Amount' => $amount, ':AppID' => $id, ':Object' => $object,
                ':Discount' => $chargeData['Activity']['ActiveFeePercent'], ':DiscountInfo' => $chargeData['Activity']['Introduction'], ':ActivityUUID' => $chargeData['Activity']['UUID'], ':ChargeData' => json_encode($chargeData),
            ]);
        }

        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    /*
    $users:array 用户,参数是主账号ID的数组
    $accountID: 购买人的ID
    $payType:支付类型，物业:1,终端用户:0,installer:2
    $payer:支付人邮箱或物业账号
    $installID:对应社区或者install的id
    $areaID:对应区域管理员ID
    $totalPrice:前端计算完成传上来的总价
    $landlineFee:落地月租费,
    */
    public function landline($users, $accountID, $payType, $type, $payer, $installID, $areaID, $month)
    {
        $data = $this->services['billsysUtil']->createOrder($accountID, $payType, $type, $installID, 1, $month, $users);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);

        // 创建续费订单
        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $accountID, ':CreateTime' => \util\computed\getNow(), ':WebHookToken' => $data['Token'],
            ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => 4, ':Payer' => $payer, ':PayerType' => $payType, ':InstallID' => $installID,
            ':AreaManageID' => $areaID, ':Months' => $month, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code'],
        ]);
        $orderID = $this->db->lastInsertId();
        // 创建子订单
        foreach ($chargeData as $user) {
            $name = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $user['User']]])[0]['Name'];

            if (count($user['Activity']) == 0) {
                $amount = \util\computed\inputComputedCount($user['MonthlyFee']);
                $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => 5, ':Amount' => $amount, ':AppID' => $user['User'], ':Object' => $name, ':ChargeData' => json_encode($user)]);
            } else {
                $amount = \util\computed\inputComputedCount($user['Activity']['MonthlyFee']);
                $this->db->insert2List('OrderEndUserList', [
                    ':OrderID' => $orderID, ':Type' => 5, ':Amount' => $amount, ':AppID' => $user['User'], ':Object' => $name,
                    ':Discount' => $user['Activity']['ActiveFeePercent'], ':DiscountInfo' => $user['Activity']['Introduction'], ':ActivityUUID' => $user['Activity']['UUID'], ':ChargeData' => json_encode($user),
                ]);
            }
        }

        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    private function changeIDToAccount($ids)
    {
        if (count($ids) == 0) {
            return [];
        }
        $wheres = [];
        $bindArray = [];
        foreach ($ids as $index => $id) {
            array_push($wheres, ":id$index");
            $bindArray[":id$index"] = $id;
        }
        $where = 'where ID in (' . implode(',', $wheres) . ')';
        $data = $this->db->querySList("select Account from PersonalAccount $where", $bindArray);
        $account = [];
        foreach ($data as $val) {
            array_push($account, $val['Account']);
        }

        return $account;
    }

    /**
     * @param key:array 关键字，用户、小区账号等，不能是空数组，否则业务上没意义
     * @param type:'activeUser' | 'communityFeaturePlan'
     *
     * @return bool
     */
    public function isLockPayData($keys, $type)
    {
        // 查询是否有被锁定的资源
        $type = ['activeUser' => 0, 'communityFeaturePlan' => 1][$type];
        $wheres = [];
        $bindArray = [':Type' => $type];

        foreach ($keys as $index => $key) {
            array_push($wheres, ":key$index");
            $bindArray[":key$index"] = $key;
        }

        $where = '(' . implode(',', $wheres) . ')';
        $data = $this->db->querySList(
            "select DataKey from LockOrder where DataKey in $where and Type = :Type",
            $bindArray
        );
        if (count($data) != 0) {
            return $data;
        }

        return true;
    }

    public function lockPayData($orderNumber, $keys, $type)
    {
        // 插入资源锁定
        foreach ($keys as $key) {
            $this->db->insert2List('LockOrder', [':DataKey' => $key, ':Type' => $type, ':OrderNumber' => $orderNumber]);
        }
    }

    /*
    $accountID: 购买人的ID
    $payType:支付人类型，物业:1,终端用户:0,2:install,3:区域管理员4:sub dis
    $payer:支付人邮箱
    $communityID:对应社区的id
    $areaID:对应区域管理员ID
    */
    public function communityFeaturePlanOnce($accountID, $payType, $payer, $communityID, $areaID)
    {
        global $cMessage;
        // 锁定资源
        $res = $this->isLockPayData([$communityID], 'communityFeaturePlan');
        if ($res !== true) {
            $cMessage->echoErrorMsg(StatePayOutstanding);
        }

        $disID = $this->db->querySList('select ParentID from Account where ID = :ID', [':ID' => $communityID])[0]['ParentID'];
        $mFeature = $this->db->querySList('select D.FeeUUID,D.FeatureID from ManageFeature C join ManageFeature D on C.FeatureID = D.FeatureID where C.AccountID = :communityID and D.AccountID = :disID', [':communityID' => $communityID, ':disID' => $disID])[0];
        $feeUUID = $mFeature['FeeUUID'];
        $featureID = $mFeature['FeatureID'];
        // 向计费系统请求创建订单
        $data = $this->services['billsysUtil']->createFeatureOrder($accountID, $payType, 4, 1, $feeUUID);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        // 支付类型
        $type = 5;

        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $accountID, ':WebHookToken' => $data['Token'],
            ':CreateTime' => \util\computed\getNow(), ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => $type, ':Payer' => $payer,
            ':PayerType' => $payType, ':InstallID' => $communityID, ':AreaManageID' => $areaID, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code']
        ]);
        $orderID = $this->db->lastInsertId();
        $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => $type, ':Amount' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':AppID' => $featureID, ':ChargeData' => json_encode($chargeData)]);
        $this->lockPayData($orderNumber, [$communityID], 1);

        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    /*
    $accountID: 购买人的ID
    $payType:支付人类型，物业:1,终端用户:0,2:install,3:区域管理员
    $payer:支付人邮箱
    $communityID:对应社区的id
    $areaID:对应区域管理员ID
    $month:月数
    */
    public function communityFeaturePlanMonthly($accountID, $payType, $payer, $communityID, $areaID, $month)
    {
        global $cMessage;

        $disID = $this->db->querySList('select ParentID from Account where ID = :ID', [':ID' => $communityID])[0]['ParentID'];
        $mFeature = $this->db->querySList('select D.FeeUUID,D.FeatureID from ManageFeature C join ManageFeature D on C.FeatureID = D.FeatureID where C.AccountID = :communityID and D.AccountID = :disID', [':communityID' => $communityID, ':disID' => $disID])[0];
        $feeUUID = $mFeature['FeeUUID'];
        $featureID = $mFeature['FeatureID'];
        // 向计费系统请求创建订单
        $data = $this->services['billsysUtil']->createFeatureOrder($accountID, $payType, 5, $month, $feeUUID);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        // 支付类型
        $type = 6;

        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $accountID, ':WebHookToken' => $data['Token'],
            ':CreateTime' => \util\computed\getNow(), ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => $type, ':Payer' => $payer,
            ':PayerType' => $payType, ':InstallID' => $communityID, ':AreaManageID' => $areaID, ':Months' => $month, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']), ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code']
        ]);
        $orderID = $this->db->lastInsertId();
        $this->db->insert2List('OrderEndUserList', [':OrderID' => $orderID, ':Type' => $type, ':Amount' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':AppID' => $featureID, ':ChargeData' => json_encode($chargeData)]);

        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    public function createDifferenceOrder()
    {
        global $cMessage;
        $params = [
            'OldFeeUUID' => '',
            'NewFeeUUID' => '',
            'CommunityID' => '',
            'FeatureFeeType' => '',
            'NewFeatureID' => '',
            'userId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userId'];
        $oldFeeUUID = $params['OldFeeUUID'];
        $newFeeUUID = $params['NewFeeUUID'];
        $communityID = $params['CommunityID'];
        $featureFeeType = $params['FeatureFeeType'];
        $newFeatureID = $params['NewFeatureID'];
        // 锁定资源
        $res = $this->isLockPayData([$communityID], 'communityFeaturePlan');
        if ($res !== true) {
            $cMessage->echoErrorMsg(StatePayOutstanding);
        }

        $count = 1;
        if ($featureFeeType == 1) {
            $featureExpireTime = strtotime($this->db->queryAllList('CommunityInfo', ['equation' => [':AccountID' => $communityID]])[0]['FeatureExpireTime']);
            $now = strtotime(\util\computed\getNow());
            $count = intval(($featureExpireTime - $now) / 86400);
        } else {
            $count = 1;
        }

        // 向计费系统请求创建订单
        $data = $this->services['billsysUtil']->createFeatureOrder($userId, 2, 6, $count, $oldFeeUUID, $newFeeUUID);
        $orderData = $data['OrderData'];

        //创建订单
        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $type = 7;
        $areaID = $this->db->queryAllList('Account', ['equation' => [':ID' => $communityID]])[0]['ParentID'];
        $myData = $this->db->queryAllList('Account', ['equation' => [':ID' => $userId]])[0];
        $installerData = $this->db->queryAllList('Account', ['equation' => [':ID' => $myData['ManageGroup']]])[0];
        $myEmail = $installerData['Account'];
        $this->db->insert2List('OrderList', [
            ':OrderNumber' => $orderNumber, ':AccountID' => $communityID, ':WebHookToken' => $data['Token'],
            ':CreateTime' => \util\computed\getNow(), ':TotalPrice' => \util\computed\inputComputedCount($orderData['TotalPrice']), ':Type' => $type, ':Payer' => $myEmail,
            ':PayerType' => 2, ':InstallID' => $communityID, ':AreaManageID' => $areaID, ':FinalPrice' => \util\computed\inputComputedCount($orderData['FinalPrice']),
            ':BmOrderNumber' => $orderData['Number'], ':PayCode' => $orderData['Code'],
        ]);
        $orderID = $this->db->lastInsertId();

        $chargeData = $data['ChargeData'];
        $this->db->insert2List('OrderEndUserList', [
            ':OrderID' => $orderID, ':Type' => 7, ':Amount' => \util\computed\inputComputedCount($orderData['TotalPrice']),
            ':AppID' => $newFeatureID, ':ChargeData' => json_encode($chargeData),
        ]);
        // 加入资源锁定
        $this->lockPayData($orderNumber, [$communityID], 1);

        // 返回结果数据
        $bmOrderNumber = $orderData['Number'];
        $code = $orderData['Code'];
        $token = $data['Token'];
        $this->log->payLog("#createdCommunityOrder#orderID=$orderID;bmOrderNumber=$bmOrderNumber;code=$code");
        \util\computed\setGAppData(['data' => ['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"]]);
    }
}
