<?php
  const MSGTEXT = [ 

"accountExits"=>"このアカウントはすでに存在します。",
"accountNotExit"=>"アカウントは存在しません。",
"accountIncorrect"=>"無効なユーザー名またはパスワード",
"accountNIncorrect"=>"無効なアカウント",
"activeEmpty"=>"有効な値が必要です。",
"addFail"=>"追加失敗",
"addSuccess"=>"追加成功",
"addSuccessPw"=>"追加に成功しました。パスワードは '%s'です。",
"addTmpKeyFail"=>"一時キーの追加に失敗しました。もう一度お試しください",
"aptDuplicated"=>"アパートメント%sが重複しています",
"aptDigits"=>"アパートメント%sは無効です。1から6桁の数字でなければなりません",
"aptExit"=>"アパートメント%sは既に存在します",
"abnormal"=>"異常",
"activation"=>"アクティベーション",
"additionalApp"=>"追加アプリ",
"bindDevice"=>"このアカウントのすべてのデバイスを削除してください",
"bindMAClibrary"=>"MACライブラリでMACを削除してください",
"bindUser"=>"このアカウントのユーザーを削除してください",
"buildingBindDevice"=>"この建物配下にあるデバイスを削除してください",
"buildingBindUser"=>"この建物配下のユーザーを削除してください",
"buildingDigits"=>"建物%sは無効です。1から2桁の数字でなければなりません",
"buildingExit"=>"建物はすでに存在しています",
"BindingDeviceFailed"=>"デバイスのバインドに失敗しました。デバイスが他のユーザーに結合されているか、MACライブラリに追加されていない可能性があります",
"chcekMacExits"=>"追加失敗。MACアドレスが無効か、すでに存在しています",
"changePasswdFail"=>"パスワード変更失敗",
"changePasswdPEmail"=>"パスワードの変更に成功しました。Eメール%sで確認してください",
"community"=>"コミュニティ",
"deleteFail"=>"削除失敗",
"deleteSuccess"=>"正常に削除しました。",
"deviceTypeEmpty"=>"デバイスタイプが必要です",
"deviceNotFindUser"=>"デバイスが見つかりません。管理者に連絡してください",
"dealSuccess"=>"設定成功",
"doorUnit"=>"ドアユニット",
"emailExits"=>"Eメールは既に存在します",
"emailPExit"=>"Eメール%sは既に存在します",
"emailNotExits"=>"このEメールは存在しません。",
"emailDuplicated"=>"Eメール%sが重複しています",
"errorVersion"=>"エラーバージョン",
"emailOrAccountNotExit"=>"このEメールは存在しません。",
"firstNameEmpty"=>"名が必要です",
"failed"=>"失敗",
"family"=>"ファミリー",
"guardPhone"=>"ガードフォン",
"incorrectSipAccount"=>"利用可能なSIPアカウントはもうありません",
"incorrectSipAccountGroup"=>"利用可能なSIPグループはもうありません",
"importDataSuccess"=>"データインポート成功",
"importFailMACExit"=>"インポートに失敗しました。MACアドレスが存在するか有効かどうかを確認してください：\r\n%s",
"invaildDC"=>"無効なデバイスコード",
"InvalidFile"=>"無効なファイル",
"invalidPEmail"=>"無効なEメール %s",
"invalidPName"=>"無効なユーザー名 %s",
"invalidPCalltype"=>"無効なコールタイプ %s",
"invalidPPin"=>"無効な暗証番号 %s",
"invalidPActive"=>"無効なアクティブ値 %s",
"invalidPage"=>"無効なページ",
"invalidPDeviceType"=>"無効なデバイスタイプ %s",
"invaildVerCode"=>"無効な認証コード",
"invalidIdentity"=>"ID情報が無効です！他の場所でログインしている可能性があります。もう一度ログインしてください。",
"indoorMonitor"=>"室内モニター",
"inactivated"=>"無効",
"normal"=>"ノーマル",
"expired"=>"期限切れ",
"lastNameEmpty"=>"姓が必要です",
"locationEmpty"=>"ロケーションが必要です",
"locationPLoog"=>"%s長すぎるロケーション",
"locationLoog"=>"ロケーションが長すぎます",
"loginError"=>"ログインエラー",
"loginFail"=>"ログイン失敗",
"loginSuccess"=>"ログイン成功",
"limitIP"=>"試行回数が多すぎます。5分後にもう一度お試しください",
"limitDevice"=>"デバイス数が上限に達しました",
"MAC2PLibrary"=>"MACアドレス:%sは無効です。MACライブラリを確認してください。",
"MAC2Library"=>"MACアドレスが無効です。MACライブラリを確認してください",
"macExits"=>"MACアドレスは既に存在します",
"MACLength"=>"MACアドレスの長さは12桁でなければなりません。",
"modifySuccess"=>"変更成功",
"modifyFailed"=>"変更失敗",
"maxHouse"=>"ユーザー数が上限に達しています。管理者に連絡してください",
"modifyAptFail"=>"保存に失敗しました。APT番号はすでに存在しています。最初に削除する必要があります。",
"nameloog"=>"ユーザー名が長すぎます。ユーザー名には64文字まで使用できます",
"nameExit"=>"ユーザー名は既に存在します",
"notPermission"=>"操作権限がありません",
"noSip"=>"これ以上のSIPアカウントはありません",
"passwordIncorrect"=>"パスコード不正",
"passwdChangeSuccess"=>"パスコード変更成功",
"passwordResetSuccess"=>"パスワードリセット成功",
"passwordReset2"=>"パスワードは'%s'にリセットされました。",
"payTimeOut"=>"支払いタイムアウト",
"payFailed"=>"支払い失敗",
"processing"=>"処理中",
"paySuccess"=>"支払い成功",
"redirectedOnRPS"=>"このMACアドレスはRPSでリダイレクトされます。",
"registerFailed"=>"登録失敗",
"registerSuccess"=>"登録成功",
"roomNotExit"=>"このユーザーは存在しません！",
"RFCardExit"=>"このRFカードは既に存在します",
"registered"=>"登録済",
"PrivateKeyExists"=>"このプライベートキーは存在します",
"passwordCorrect"=>"無効なパスワード",
"timeLessCurrent"=>"無効な更新時間",
"timeZoneChangeSuccess"=>"タイムゾーンの変更に成功しました",
"timeOut"=>"タイムアウト",
"unbindMACUser"=>"最初にユーザーと%sのバインドを解除してください",
"unKnowDT"=>"不明なデバイスタイプ",
"userBindUser"=>"最初にこのアカウントのユーザーを削除してください",
"userNotExit"=>"このユーザーは存在しません",
"userMaxPLimt"=>"作成に失敗しました。追加できるのは最大%sファミリーメンバーのみです",
"unregistered"=>"未登録",
"validMAC"=>"有効なMACアドレスを入力してください",
"versionExit"=>"このバージョンはすでに存在しています",
"versionNameNumberExit"=>"バージョン名または番号はすでに存在しています",
"sipStatus"=>"SIPアカウントの割り当てに失敗しました。もう一度お試しください",
"sentCodeLater"=>"認証コードを送信しました。しばらくしてからもう一度お試しください",
"setSuccess"=>"セットアップ成功",
"sendEmailSuccess"=>"Eメール送信成功",
"SetFailed"=>"設定失敗",
"stairPhone"=>"ステアフォン",
"successed"=>"成功",
"subscription"=>"サブスクリプション",
"wallPhone"=>"壁掛け電話",
"emailMaxLen"=>"Eメールは64文字以下にする必要があります。",
"serverUpgradeTips"=>"サーバーのアップグレードが完了しました。ページを更新してください。その前に、入力したデータを他の場所にコピーすることができます。",
"ActiveFamilyAccount"=>"まず、ファミリーマスターのアカウントを有効にしてください。",
"weekly"=>"毎週",
"daily"=>"毎日",
"never"=>"ない",
"calltypeEmpty"=>"コールタイプが必要です",
"addOutApt"=>"追加できる部屋は%s部屋までです",
"call"=>"コール",
"unlock"=>"解錠",
"tryUnlockCall"=>"呼出しで解錠してみてください",
"tryUnlockKey"=>"暗証番号での解錠失敗",
"tryUnlockCard"=>"RFカードで解錠してみてください",
"tryUnlockFace"=>"顔認識で解錠してみてください",
"unlockApp"=>"SmartPlusで解錠",
"unlockIndoor"=>"室内モニターで解錠",
"unlockNFC"=>"NFCで解錠",
"unlockBluetooth"=>"Bluetoothで解錠",
"unlockCard"=>"RFカードで解錠",
"unlockPrivateKey"=>"暗証番号で解錠",
"unlockTempKey"=>"一時キーで解錠",
"alarmDoorUnlock"=>"ドア解錠",
"alarmInfrared"=>"赤外線",
"alarmSmoke"=>"煙",
"alarmGas"=>"ガス",
"alarmUrgency"=>"緊急",
"alarmSOS"=>"SOS",
"alarmTamper"=>"タンパー",
"alarmGate"=>"ゲート",
"alarmDoor"=>"ドア",
"alarmBedroom"=>"バスルーム",
"alarmGuestRoom"=>"ゲストルーム",
"alarmHall"=>"ホール",
"alarmWindow"=>"窓",
"alarmBalcony"=>"バルコニー",
"alarmKitchen"=>"キッチン",
"alarmStudy"=>"書斎",
"alarmBathroom"=>"バスルーム",
"alarmArea"=>"エリア",
"RFCardExit2"=>"RFカード%sは既に存在します",
"RFCardDuplicated"=>"RFカード%sが重複しています",
"notMacBind"=>"ユーザー「%s」には、デバイス「%s」と接続されているドアを開く権限がありません。",
"accountNumLet"=>"アカウントは数字と文字で構成する必要があります",
"networkUnavailable"=>"ネットワークを利用できません。",
"notForModel"=>"このモデル用ではありません。",
"upgradeDevVersion"=>"最初に最新バージョンにアップグレードしてください。",
"unavailableService"=>"サービスは一時的に利用できません。しばらくしてからもう一度お試しください。",
"cantDeletePin"=>"暗証番号%sは削除できません。",
"residentInRoom"=>"部屋%sにはすでに居住者がいます。",
"noAnswer"=>"応答なし",
"indoorAndApp"=>"室内モニターとアプリ(マスター)の両方",
"indoorMonitorOnly"=>"室内モニターのみ",
"appOnly"=>"アプリ(マスター)のみ",
"endThanStart"=>"終了時間を開始時間より前に設定することはできません。",
"endThanStartFile"=>"行 '%s'の無効な日付または時間。",
"doorRelease"=>"ドアリリース",
"success"=>"成功",
"unlockFACE"=>"顔認証で解錠",
"unlockBLE"=>"BLEで解錠",
"captureSmartPlus"=>"SmartPlusでのキャプチャ",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"解錠失敗",
"deviceDisconnected"=>"デバイスが切断されました。",
"low"=>"低",
"motion"=>"モーション",
"capture"=>"キャプチャ",
"failedImport"=>"インポート失敗",
"notValidMobile"=>"％sは有効な携帯電話番号ではありません。",
"mobileExits"=>"携帯電話番号はすでに存在します",
"mobileExits2"=>"携帯電話番号%sはすでに存在しています",
"mobileDuplicated"=>"携帯電話番号%sが重複しています",
"mobileNumberExist"=>"携帯電話番号は存在しません。",
"codeIncorrect"=>"無効なコード",
"sendCodeSuccess"=>"確認コードを正常に送信",
"codeCorrect"=>"正しい",
"mobileNumberEmpty"=>"携帯電話番号を入力してください。",
"invalidUser"=>"無効なユーザー %s",
"locationExits"=>"ロケーションアドレスはすでに存在します",
"smartPlusIndoor"=>"SmartPlusと室内モニター",
"phoneIndoor"=>"電話と室内モニター",
"smartPlusIndoorBackup"=>"SmartPlusと室内モニター、バックアップとしての電話",
"smartPlusBackup"=>"室内モニターとバックアップとしてのSmartPlus",
"indoorPhoneBackup"=>"室内モニターとバックアップとしての電話",
"indoorSmartPlusPhone"=>"室内モニターとバックアップとしてのSmartPlus、そして最後に電話",
"endUser"=>"エンドユーザー",
"installer"=>"インストーラー",
"distributor"=>"ディストリビューター",
"pm"=>"施設管理者",
"superManage"=>"SuperManage",
"loginManagement"=>"ログイン管理",
"accessControl"=>"アクセスコントロール",
"userManagement"=>"ユーザー管理",
"deviceManagement"=>"デバイス管理",
"communityManagement"=>"コミュニティ管理",
"auditLogin"=>"ログイン: Web",
"auditLogout"=>"ログアウト: Web",
"auditAddTempKey"=>"一時キー追加: {0}",
"auditEditTempKey"=>"一時キー編集: {0}",
"auditDeleteTempKey"=>"一時キー削除: {0}",
"auditAddRFCard"=>"RFカード追加: {0}",
"auditEditRFCard"=>"RFカード編集: {0}",
"auditDeleteRFCard"=>"RFカード削除: {0}",
"auditAddDis"=>"ディストリビューター追加: {0}",
"auditEditDis"=>"ディストリビューター編集: {0}",
"auditDeleteDis"=>"ディストリビューター削除: {0}",
"auditAddInstaller"=>"インストーラー追加: {0}",
"auditEditInstaller"=>"インストーラー編集: {0}",
"auditDeleteInstaller"=>"インストーラー削除: {0}",
"auditAddPM"=>"施設管理者追加: {0}",
"auditEditPM"=>"施設管理者編集: {0}",
"auditDeletePM"=>"施設管理者削除: {0}",
"auditAddEndUser"=>"エンドユーザー追加: {0}",
"auditEditEndUser"=>"エンドユーザー編集: {0}",
"auditDeleteEndUser"=>"エンドユーザー削除: {0}",
"auditSetOwnerTime"=>"タイムゾーン設定 {0}",
"auditSetOwnPassword"=>"パスワードを設定",
"auditAddPIN"=>"暗証番号追加: {0}",
"auditEditPIN"=>"暗証番号編集: {0}",
"auditDeletePIN"=>"暗証番号削除: {0}",
"auditImportFace"=>"顔インポート: {0}",
"auditDeleteFace"=>"顔削除: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"コールタイプ設定、SmartPlusと室内モニターを設定：{0}＆{1}",
"auditSetCallTypePhoneIndoor"=>"コールタイプ設定、電話と室内モニターを設定：{0}＆{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"コールタイプ設定、SmartPlusと室内モニター、およびバックアップとしての電話を設定：{0}＆{1}",
"auditSetCallTypeSmartPlusBackup"=>"コールタイプ設定、室内モニターとバックアップとしてのSmartPlusを設定：{0}＆{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"コールタイプ設定、室内モニターとバックアップとしての電話を設定：{0}＆{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"コールタイプ設定、室内モニターとバックアップとしてのSmartPlus、そして最後に電話を設定：{0}＆{1}",
"auditDeleteDevice"=>"デバイス削除: {0}",
"auditSetAPTCount"=>"アパート数設定 {0}",
"auditEnableLandline"=>"固定電話サービス有効",
"auditDisableLandline"=>"固定電話サービス無効",
"auditSetSubTime"=>"タイムゾーン設定 {0}",
"auditSetChargeModeInstall"=>"インストーラーによる課金モデルの支払いの設定",
"auditSetChargeModeUser"=>"ユーザー/施設管理者による課金モデルの支払いを設定",
"auditSetConnectTypeDefault"=>"接続タイプデフォルト設定",
"auditSetConnectTypeTCP"=>"接続タイプtcp設定",
"auditSetConnectTypeUDP"=>"接続タイプudp設定",
"auditSetConnectTypeTLS"=>"接続タイプtls設定",
"auditAddCommunity"=>"コミュニティ追加: {0}",
"auditDeleteCommunity"=>"コミュニティ削除: {0}",
"auditImportCommunity"=>"コミュニティインポート: {0}",
"auditSetAPTNumber"=>"{0}部屋番号{1}設定",
"auditSetEmail"=>"Eメール設定 {0}: {1}",
"auditSetMobile"=>"電話番号設定 {0}: {1}",
"auditDeviceTypeStair"=>"マルチテナントドアフォンタイプ設定: {0}",
"auditDeviceTypeDoor"=>"シングルテナントドアフォンタイプ設定: {0}",
"auditDeviceTypeIndoor"=>"室内モニタータイプ設定: {0}",
"auditDeviceTypeGuardPhone"=>"ガードフォンタイプ設定: {0}",
"auditDeviceTypeAccessControl"=>"アクセスコントロールタイプ設定: {0}",
"auditSetNetGroup"=>"ネットワークグループ設定 {0}: {1}",
"auditEditCommunity"=>"コミュニティ編集",
"deliveryMsg"=>"配達された%sアイテムがあります。時間内に確認してください。",
"deliveryTitle"=>"新しいパッケージがあります！",
"rfcardDuplicatedLines"=>"行%sに重複したRFカード番号があります！",
"rfcardNameInvalid"=>"行%sのRFカード名が無効です！",
"rfcardExistLines"=>"RFカードはすでに行%sに存在しています。",
"importFailMacExistLines"=>"MACアドレスが行%s存在するか、有効です。",
"exportExcelCountNull"=>"その日にエクスポートするログはありません！もう一度選択してください。",
"keyIsEqualRoom"=>"デリバリーキーは部屋番号(APT番号)と同一にすることはできません！",
"visitor"=>"訪問者",
"CommunityNameExist"=>"コミュニティ名はすでに存在します",
"unlockGuardPhone"=>"ガードフォン解錠",
"auditLoginApp"=>"ログイン: アプリ",
"auditLogoutApp"=>"ログアウト: アプリ",
"timeForYesterday"=>"昨日",
"exportExcelDataBefore"=>"データが大きすぎます！最初に%sの前にデータをエクスポートしてください。",
"tempkeyUsed"=>"使用される一時キー",
"tempkeyContent"=>"%sは一時キーを使用しています。",
"accessNameExist"=>"アクセスグループ名はすでに存在します",
"addFaceFail"=>"鮮明な顔の写真をインポートしてください。",
"userInvalid"=>"行%sのユーザーが無効です。",
"groupsInvalid"=>"行%sのアクセスグループが無効です。",
"BuildAccessName"=>"居住者-建物 %s",
"auditCodeLogEditApt"=>"アパート編集:{0}",
"invalidTimeInLine"=>"ライン%sの時間が無効です。",
"cancel"=>"キャンセル",
"cancelSuccess"=>"キャンセル成功",
"payOutstanding"=>"未払いの注文があるかどうかを確認してください。ない場合は、サービスプロバイダーにお問い合わせください。",
"featureDeleteError"=>"機能プランがバインドされました。",
"beyondFamilyMember"=>"これ以上ファミリーメンバーのアカウントを作成することはできません。作成するには、サービスプロバイダーにお問い合わせください。",
"indoorMonitorRequired"=>"各部屋には、少なくとも1台の室内モニターが必要です。",
"featureActivationFee"=>"機能(ワンタイム料金)",
"systemProcessing"=>"システム処理",
"featureMonthlyFee"=>"機能(月額料金)",
"featurePriceDifferences"=>"機能(価格差)",
"updatingSuccess"=>"更新成功！",
"featureNameBasic"=>"基本",
"featureNamePremium"=>"プレミアム",
"indoorMacNotCorrect"=>"正しい室内モニターのMACを入力してください。",
"off"=>"オフ",
"enterValidAccount"=>"有効なアカウントを入力してください",
"invalidKitImportMAC"=>"MACが存在するか有効かどうかを確認してください：%s",
"importLessData"=>"%s以下のデータをインポートしてください。",
"invalidQRCode"=>"認証失敗。正しいQRコードをスキャンしてください。",
"cannotCreateFamilyMember"=>"これ以上ファミリーメンバーのアカウントを作成することはできません。",
"importProcessing"=>"インポート中。しばらくしてから、もう一度お試しください。",
"departmentAccessName"=>"%s アクセスグループ",
"idExistsLine"=>"IDは行%sにすでに存在します",
"enterFirstNameLine"=>"行%sに名前を入力してください",
"enterLastNameLine"=>"行%sに名字を入力してください",
"departmentExist"=>"部門はすでに存在します",
"idExist"=>"IDはすでに存在します",
"layoutIdInvalid"=>"レイアウトが無効です",
"unlockAppHome"=>"BelaHome解錠",
"officeNameExist"=>"オフィス名はすでに存在します",
"departmentExit"=>"部門はすでに存在します。",
"importOutTask"=>"一度にインポートできるのはテンプレートのみです。",
"idDuplicated"=>"ID %sが重複しています",
"aptInvalidLine"=>"行%sのAPTが無効です。",
"buildInvalidLine"=>"行%sの建物が無効です。",
"departmentInvalidLine"=>"行%sの部門が無効です。",
"idInvalidLine"=>"行%sのIDが無効です。",
"propertyManager"=>"施設管理者",
"departmentBindDevice"=>"この部門のデバイスを削除してください。",
"departmentBindUser"=>"この部門のユーザーを削除してください。",
"smartPlusValidLine"=>"行%sのSmartplusインターフォン機能が無効です。",
"identityValidLine"=>"行%sのIDが無効です。",
"eachDoorCount"=>"各ドアを一度だけ開けるシングルプラン",
"textUpgradeMsg1"=>"続行するにはアプリのバージョンをアップグレードしてください。",
"textUpgradeMsg2"=>"ログイン失敗",
"deleteCodeGetLimitTimes"=>"無効なキーです。24時間後にもう一度お試しください。",
"deleteCodeOverLimitTimes"=>"24時間後にもう一度お試しください。",
"deleteCodeError"=>"無効なキー",
"textUpgradeMsg"=>"1.Optimized the tempkey function.;2.Added the account cancellation function.;3.Fixed some bugs.",
"paramsError"=>"パラメーターエラー",
"pmappStatusInvalid"=>"最初に施設管理者アプリを有効にしてください。",
"delivery_description"=>"デリバリー一時キー",
"webRelayIDInvalidLine"=>"行%sのウェブリレーが無効です。",
"relayInvalid"=>"行%sのリレーが無効です。",
"cancelError"=>"キャンセル失敗。",
"textUpgradeMsgForComRole"=>"コミュニティロールをアップグレード",
"textUpgradeMsgForPerRole"=>"社員ロールをアップグレード",
"textUpgradeMsgForOffRole"=>"オフィスロールをアップグレード",
"textUpgradeMsgForPMRole"=>"施設管理者ロールをアップグレード",
"lockApp"=>"SmartPlusでロック",
"lock"=>"ロック",
"versionLogMaxLen"=>"バージョンログは %s文字より大きくすることはできません",
"autoLock"=>"自動ロック",
"pinAndRFcardNotNullLines"=>"%s行目の暗証番号とRFカードは最低1つ入力する必要があります!",
"pinExistLines"=>"暗証番号は%s行目に既に存在します。",
"pinInvalidLines"=>"%s行目の暗証番号が無効です !",
"pinDuplicatedLines"=>"%s行目で暗証番号は重複しています!",
"FaceImportLength"=>"インポート する顔のファイルサイズは %s より大きくできません",
"landlineServerNotActivate"=>"このコミュニティは、固定電話サービスを有効にしていません。",
"importFailDisNotExist"=>"ディストリビューターが存在しません。",
"importFailNotPermission"=>"このMACアドレスを追加する権限がありません。",
"importFailTooManyAdd"=>"一ディストリビューターのみインポートに失敗しました。",
"importFailAdded"=>"このMACアドレスは、別のユーザーによって既に追加されています。",
"macAssignToLimit"=>"最大10ディストリビューターまで割り当てることができます",
"macNumToLimit"=>"一度に最大1000個のMACアドレスまでアップロードできます。",
"addOutFloor"=>"1～128の数字を入力してください。",
"floor"=>"フロア",
"PostalCodeInvalid"=>"文字または数字を入力してください。",
"onceCodeInvalid"=>"ワンス コードは4～5桁にしてください。",
"permanentCodeInvalid"=>"パーマネントコードは6 桁にしてください。",
"onceCodeOutNum"=>"追加できるワンスコードは10 回までです。",
"permanentCodeOutNum"=>"追加できるパーマネントコードは 10 個までです。",
"onceCodeExist"=>"ワンスコードは既に存在しています。",
"permanentCodeExist"=>"パーマネントコードは既に存在します。",
"addOutFloorLine"=>"%s 行目のフロア番号が無効です。",
"auditManuallyUnlock"=>"手動で解錠",
"auditManuallyLock"=>"手動ロック",
"automaticallyUnlock"=>"自動解錠",
"doorClose"=>"ドアを閉める",
"PostalCodeNotEmpty"=>"少なくとも 1 つの文字または数字を入力してください。",
"emergencyAlarm"=>"緊急警報",
"doorSensor"=>"ドアセンサー",
"yaleBatteryWarning"=>"Yaleバッテリー警告",
"auditCodeManuallyUnlock"=>"手動で解錠",
"auditCodeManuallyLock"=>"手動ロック",
"2weekBatteryWarning"=>"%s - 推定バッテリ時間: 2 週間。",
"1weekBatteryWarning"=>"%s - 推定バッテリ時間: 1 週間。",
"replaceBatteryWarning"=>"%s - バッテリ レベルが非常に低下しています。すぐに交換してください。",
"open"=>"開く",
"close"=>"閉じる",
"addContactFavoriteNum"=>"お気に入りへの追加に失敗しました。お気に入りのアパートメントは 300 個までしか追加できません。",
"addContactBlockNum"=>"ブロックリストへの追加に失敗しました。ブロックリストに追加できるアパートメントは 100 個までです。",
"voiceTitle"=>"ボイスメッセージ",
"voiceContent"=>"%s からボイス メッセージがあります",
"voiceMsgInvalid"=>"ボイスメッセージの有効期限が切れています。",
"toggleFeaturePlan"=>"機能プランを変更することはできません。",
"rtspAddresEmpty"=>"RTSPアドレスを入力してください。",
"rtspAddresInvalid"=>"無効なRTSP アドレス。",
"rtspPortEmpty"=>"ポートを入力しえください。",
"rtspPortInvalid"=>"無効なポート。",
"rtspPassWdEmpty"=>"パスワードを入力してください。",
"rtspPassWdInvalid"=>"パスワードが長すぎます。パスワードは最大 63 文字まで設定できます。",
"cameraExist"=>"カメラは既に存在します。",
"errorOnRPS"=>"RPS サーバーのリクエストに失敗しました",
"faceImportErrorSystem"=>"システムエラー",
"faceImportErrorView"=>"正面ではありません",
"faceImportErrorWearMask"=>"マスク検出",
"faceImportErrorLowResolution"=>"解像度が低すぎます",
"faceImportErrorWrongFormat"=>"ファイルフォーマットエラー",
"faceImportErrorNoFace"=>"顔が検出されませんでした",
"faceImportErrorFileLarge"=>"ファイルが大きすぎます。",
"faceImportErrorFaceLarge"=>"顔が大きすぎます。",
"faceImportErrorFaceSmall"=>"顔が小さすぎます。",
"faceImportErrorMultiFaces"=>"複数の顔",
"faceImportErrorWrongName"=>"ファイル名が間違っています。",
"faceImportErrorEmptyName"=>"居住者名が空欄です。",
"faceImportErrorNoAccountInfo"=>"個人アカウント情報の取得エラー。",
"faceImportErrorAccountInactive"=>"個人アカウントが有効ではありません。",
"changeHomeFeatureInvalid"=>"操作に失敗しました！「ホーム オートメーション」機能を使用するディストリビュータのインストーラがあります。",
"changeInterComFeatureInvalid"=>"操作に失敗しました！「Intercom」機能を使用するディストリビュータのインストーラがあります。",
"offline"=>"失敗：オフライン",
"allFloors"=>"すべてのフロア",
"uploadOversize"=>"アップロードファイルのサイズは％sより大きくすることはできません",
"uploadInvalidType"=>"アップロードされたファイルタイプはサポートされていません",
"uploadFailed"=>"アップロードに失敗しました、後で試してください",
"uploadScreenSaverImgTooMuch"=>"スクリーンセーバーの写真は％s以上にすることはできません！",
"screenSaverImgTooLittle"=>"スクリーンセーバーの写真は％s未満にすることはできません！",
"screenSaverImgTooMuch"=>"スクリーンセーバーの写真は％s以上にすることはできません！",
"screenSaverDevicesOffline"=>"保存が失敗しました。",
"saveFailed"=>"保存が失敗しました。",
"importingInProgress"=>"進行中にインポートするには、後でもう一度お試しください。",
"importBuildingInvalidLine"=>"ライン％sの無効な建物",
"importAptInvalidLine"=>"無効なapt in line％s",
"importAccountTypeInvalidLine"=>"無効なアカウントタイプインライン％s",
"importFirstNameInvalidLine"=>"ライン％sの無効な名",
"importLastNameInvalidLine"=>"ライン％sの無効な姓",
"importKeyInvalidLine"=>"ライン％sの無効なキー",
"importKeyExistsLine"=>"暗証番号は%s行目にあります。",
"importCardInvalidLine"=>"ライン％sの無効なRFカード",
"importCardExistsLine"=>"RFカードはライン％sで存在します",
"importAccessGroupInvalidLine"=>"無効なアクセスグループIDライン％s",
"importAccessGroupNoPermissionLine"=>"ライン％sの許可アクセスグループIDはありません",
"importExceededNumberLine"=>"ライン％sで家族の数を超えた",
"importNoActiveMasterLine"=>"インポートはline％sで失敗しました。最初にファミリーマットをアクティブにしてください。",
"importMasterExistsLine"=>"家族のマスターはすでにライン％sに存在しています。",
"importNoCreateMasterLine"=>"インポートはline％sで失敗しました。最初にファミリーMatserを作成してください。",
"PrivateKeysDataExist"=>"秘密鍵％sはすでに存在しています。",
"PrivateKeyDataExists"=>"秘密鍵％sはすでに存在しています。",
"landLineOpenToClosedFail"=>"保存が失敗しました。",
"limitWithIp"=>"頻繁に試しています。5分でもう一度やり直してください。（IP：％s）",
"subDistributor"=>"サブディストリビューター",
"faceImportErrorNotClear"=>"インポートされた画像は明確ではありません。",


  ];
