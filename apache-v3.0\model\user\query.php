<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-20 15:40:20
 * @LastEditors: cj
 */

namespace model\user;

trait query
{
    /**
     * @msg: 获取installer下的个人主账户
     * @param: userAliasId：installer
     */
    public function queryPerMain()
    {
        $this->log->actionLog("#model#user.queryPerMain#");
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $perMngId = $params["userAliasId"];
        $this->queryCom($perMngId, PERENDMROLE);
    }

    /**
     * @msg: 获取个人主账户下的从账户
     * @param: userAliasId：个人主账户
     */
    public function queryPerSub()
    {
        $this->log->actionLog("#model#user.queryPerSub#");
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $perMainUserId = $params["userAliasId"];
        $this->queryCom($perMainUserId, PERENDSROLE);
    }

    /**
     * @msg: 获取社区主账户下的从账户
     * @param: userAliasId：社区主账户
     */
    public function queryComSub()
    {
        $this->log->actionLog("#model#user.queryComSub#");
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $perMainUserId = $params["userAliasId"];
        $this->queryCom($perMainUserId, COMENDSROLE);
    }

    /**
     * @msg: 查询条目
     * @param:
     */
    private function queryCom($parentId, $role)
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $actives = [MSGTEXT["inactivated"], MSGTEXT["normal"], MSGTEXT["expired"]];
        $where = "";
        $bindArray = [":Role" => $role, ":ParentID" => $parentId];
        switch ($searchKey) {
            case 'Name':
                $where .= " AND P.Name like :serchValue ";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Sip':
                //6.2 zyc 2021-10-15修改 单住户User修改sip可以匹配家庭或个人
                $where .= " AND (S.SipGroup like :serchValue or P.SipAccount like :serchValue) ";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'UID':
                $where .= " AND P.Account like :serchValue ";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Email':
                if ($serchValue == "") {
                    $where .= " AND (P.Email is null or P.Email like :serchValue) ";
                } else {
                    $where .= " AND P.Email like :serchValue ";
                }
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'MobileNumber':
                if ($serchValue == "") {
                    $where .= " AND (P.MobileNumber is null or P.MobileNumber like :serchValue) ";
                } else {
                    $where .= " AND P.MobileNumber like :serchValue ";
                }
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
        }

        $total = $this->db->querySList(
            "select count(*) as total from PersonalAccount P left JOIN SipGroup2 S ON P.Account = S.Account where Role = :Role and P.ParentID=:ParentID $where",
            $bindArray
        )[0]["total"];
        $data = $this->db->querySList(
            "select P.UUID as PersonalAccountUUID,P.ID,P.EnableSmartHome,P.EnableIpDirect,P.Account,P.Name,P.FirstName,P.LastName,P.SipAccount,P.Address,P.Active,P.Phone,P.Phone2,P.Phone3,P.PhoneCode,P.Email,P.MobileNumber,P.CreateTime,P.PhoneStatus,P.RoomNumber,P.CustomizeForm,P.ExpireTime,P.TimeZone,P.PhoneExpireTime,
        C.AppCount,C.DevCount,C.ValidTime,S.SipGroup from PersonalAccount P left join PersonalAccountCnf C on P.Account = C.Account left JOIN SipGroup2 S ON P.Account = S.Account where P.Role = :Role AND P.ParentID = :ParentID $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        foreach ($data as &$val) {
            if ($val['PhoneExpireTime'] === '2020-01-01 00:00:00') {
                $val['Landline'] = $actives[0];
            } elseif (strtotime($val['PhoneExpireTime']) < time()) {
                $val['Landline'] = $actives[2];
            } elseif (strtotime($val['PhoneExpireTime']) > time()) {
                $val['Landline'] = $actives[1];
            }
            $val['PhoneExpireTime'] = $val['PhoneExpireTime'] == '2020-01-01 00:00:00' ? DEFAULTEXPIRETIME : $val['PhoneExpireTime'];
            // 6.2新增，获取单住户是否开启绑定室内机
            $isVillaMonitor = $this->db->querySList(
                "select MAC from DevicesSpecial where Account = :Account",
                [":Account" => $val['Account']]
            );
            if (count($isVillaMonitor) > 0 && $isVillaMonitor[0]['MAC'] !== '') {
                $devicesData = $this->db->querySList(
                    'select Relay,Location from PersonalDevices where MAC=:MAC',
                    [':MAC' => $isVillaMonitor[0]['MAC']]
                );
                if (count($devicesData) > 0) {
                    $val['isSingleMonitor'] = '1';
                    $val['MAC'] = $isVillaMonitor[0]['MAC'];
                    $val['Relay'] = $devicesData[0]['Relay'];
                    $val['Location'] = $devicesData[0]['Location'];
                } else {
                    $val['isSingleMonitor'] = '0';
                }
            } elseif (count($isVillaMonitor) > 0) {
                $val['isSingleMonitor'] = '1';
            } else {
                $val['isSingleMonitor'] = '0';
            }
        }
        unset($val);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm, ["ExpireTime"]);

        $rows = [];
        $nowTime = time();
        foreach ($data as $val) {
            $row = $val;
            if ($role == PERENDMROLE) {
                $row['SubCount'] = $this->db->querySList(
                    "select count(*) as total from PersonalAccount where ParentID = :ID and Role = 11",
                    [":ID" => $val['ID']]
                )[0]["total"];
            }
            $cur_device = array();
            $cur_device['ID'] = $row['ID'];
            $cur_device['UUID'] = $row['UUID'];
            $cur_device['UID'] = $row['Account'];
            $cur_device['Name'] = $row['Name'];
            $cur_device['MobileNumber'] = $row['MobileNumber'];
            $cur_device['SipAccount'] = $row['SipAccount'];
            $cur_device['Active'] = $row['Active'];
            $cur_device['RoomNumber'] = $row['RoomNumber'];
            $cur_device['PhoneExpireTime'] = $row['PhoneStatus'] == 1 ? $row['PhoneExpireTime'] : '';
            $cur_device['Address'] = $row['Address'] ? $row['Address'] : '--';
            $cur_device['Phone'] = $row['Phone'] ? $row['Phone'] : '--';
            $cur_device['ExpireTime'] = $row['ExpireTime'] == DEFAULTEXPIRETIME ?
                ["type" => 0, "content" => ""] : (strtotime($row['ExpireTime']) >= $nowTime ?
                    ["type" => 0, "content" => \util\time\setTimeZone($row['ExpireTime'], $timeZone, $customizeForm)] :
                    ["type" => 1, "content" => \util\time\setTimeZone($row['ExpireTime'], $timeZone, $customizeForm)]);
            $cur_device['Email Address'] = $row['Email'] ? $row['Email'] : '--';
            $cur_device['Create Time'] = $row['CreateTime'];
            $cur_device['SipGroup'] = $row['SipGroup'];
            $cur_device['Landline'] = $row['Landline'];
            $cur_device['PersonalAccountUUID'] = $row['PersonalAccountUUID'];
            if ($role == PERENDMROLE) {
                $cur_device['SubCount'] = $this->db->querySList(
                    "select count(*) as total from PersonalAccount where ParentID = :ID and Role = " . PERENDSROLE,
                    [":ID" => $row['ID']]
                )[0]["total"];
            }

            //v6.1修改单住户新增Active列规则与小区管理的一样
            if ($cur_device["Active"] == 1 && time() > strtotime($row['ExpireTime'])) {
                $cur_device["Active"] = 2;
            }
            $cur_device["Active"] = $actives[$cur_device["Active"]];

            if ($val['PhoneStatus'] == 0) {
                $cur_device['Landline'] = MSGTEXT['off'];
            }
            //v6.1修改结束

            array_push($rows, $cur_device);
        }

        $result = ["total" => $total, "row" => $rows, "detail" => $data];
        // 更改数据流
        \util\computed\setGAppData(["data" => $result]);
    }

    /**
     * @msg: 详情查询
     * @param:
     */
    public function queryInfo()
    {
        $params = [
            "ID" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $this->db->querySList(
            "select C.*,P.* from PersonalAccount P left join PersonalAccountCnf C on P.Account = C.Account where P.ID = :ID",
            [":ID" => $id]
        )[0];
        unset($data["Passwd"]);
        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm);
        // 更改数据流
        $data[0]["ValidTime"] = $data[0]["ExpireTime"];
        $data[0]['SipGroup'] = $this->db->querySList(
            "select SipGroup from PersonalAccount P join SipGroup2 S on P.Account = S.Account where P.ID = :ID",
            [":ID" => $id]
        )[0]['SipGroup'];
        $res = $this->db->querySList(
            "select Pb.Name,Pb.ParentID from PersonalAccount Pa join PersonalAccount Pb on Pa.ParentID = Pb.ID where Pa.ID = :ID AND (Pa.Role = " . PERENDSROLE . " or Pa.Role = " . COMENDSROLE . ") 
        union all select Pa.Name,Pa.ParentID from PersonalAccount Pa where Pa.ID = :ID AND (Pa.Role = " . PERENDMROLE . " or Pa.Role = " . COMENDMROLE . ")",
            [":ID" => $id]
        )[0];
        $data[0]['FamilyMaster'] = $res['Name'];
        $data[0]['Installer'] = $this->db->querySList(
            "select A.Account from Account A join Account B on A.ID = B.ManageGroup where B.ID = :ID",
            [":ID" => $res['ParentID']]
        )[0]['Account'];
        \util\computed\setGAppData(["data" => $data[0]]);
    }

    public function queryFromApp()
    {
        // 由于接口的特殊性，直接从全局获取用户身份
        global $gApp;
        $selfId = $gApp["userId"];
        $userAliasId = $gApp["userAliasId"];
        $data = $this->db->querySList(
            "select ID,Name,SipAccount,Active from PersonalAccount where ((ParentID = :ParentID AND 
        (Role = " . PERENDSROLE . " or Role = " . COMENDSROLE . ")) OR ID = :ParentID) AND ID != :ID",
            [":ID" => $selfId, ":ParentID" => $userAliasId]
        );
        // 6.2新需求，判断账号是不是超出pm限制的账号
        $Item = $this->db->querySList(
            'select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID join PersonalAccount P on P.ParentID = M.AccountID 
        where P.ID = :ID',
            [':ID' => $userAliasId]
        );
        if (count($Item) > 0) {
            $isFamilyLimit = \util\computed\getSpecifyBitLE($Item[0]["Item"], 4);
        } else {
            $isFamilyLimit = 0;
        }
        foreach ($data as &$account) {
            if ($isFamilyLimit == 1) {
                $account['Unavailable'] = $this->getAppUserConf($account['ID']);
            } else {
                $account['Unavailable'] = 0;
            }
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 获取一个家庭下所有从账户
     */
    public function queryAllSubApps()
    {
        $params = [
            "ID" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]])[0];
        if (in_array($userData["Role"], SUBROLE)) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userData["ParentID"]]])[0];
        }
        $subRole = $userData["Role"] == PERENDMROLE ? PERENDSROLE : COMENDSROLE;
        $data = $this->db->queryAllList(
            "PersonalAccount",
            ["equation" => [":ParentID" => $userData["ID"], ":Role" => $subRole]]
        );
        foreach ($data as &$value) {
            unset($value["Passwd"]);
            unset($value["SipPwd"]);
        }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 获取一个家庭下所有账户Name和ID
     */
    public function queryFamilyUser()
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $data = $this->db->querySList(
            "select Name,ID,Account,Role from PersonalAccount where ID=:ID or (ParentID = :ID and (Role = " . PERENDSROLE . " or Role = " . COMENDSROLE . "))",
            [":ID" => $id]
        );
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 获取一个installer下所有的主账户的Name和Account
     */
    public function queryAllPerMainUser()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $data = $this->db->querySList(
            "select Name,Account from PersonalAccount where Role = " . PERENDMROLE . " AND ParentID = :ParentID",
            [":ParentID" => $userId]
        );
        \util\computed\setGAppData(["data" => $data]);
    }


    public function queryComUser()
    {
        $params = [
            "Build" => "",
            "Status" => "",
            "Active" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $build = $params["Build"];
        $status = $params["Status"];
        $active = $params["Active"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];

        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $bindArray = [];
        $where = "";
        switch ($searchKey) {
            case 'Name':
                $where = "AND A.Name like :SearchValue";
                $bindArray[":SearchValue"] = "%$serchValue%";
                break;
            case 'Email':
                $where = "AND A.Email like :SearchValue";
                if ($serchValue == "") {
                    $where = "AND (A.Email like :SearchValue OR A.Email is null)";
                }
                $bindArray[":SearchValue"] = "%$serchValue%";
                break;
            case 'RoomName':
                $where = "AND R.RoomName like :SearchValue";
                $bindArray[":SearchValue"] = "%$serchValue%";
                break;
            case 'RoomNumber':
                $where = "AND A.RoomNumber like :SearchValue";
                $bindArray[":SearchValue"] = "%$serchValue%";
                break;
            case 'Unit':
                $where = "AND U.UnitName like :SearchValue";
                $bindArray[":SearchValue"] = "%$serchValue%";
                break;
        }
        if ($status !== 'all') {
            $where .= " AND A.Initialization = :Initialization";
            $bindArray[":Initialization"] = $status;
        }
        if ($active == "0") {
            $where .= " AND A.Active = :Active";
            $bindArray[":Active"] = $active;
        }
        if ($active == "2") {
            $where .= " AND (A.ExpireTime < :ExpireTime and A.Active = 1)";
            $bindArray[":ExpireTime"] = \util\computed\getNow();
        }
        if ($active == "1") {
            $where .= " AND (A.ExpireTime > :ExpireTime and A.Active = 1)";
            $bindArray[":ExpireTime"] = \util\computed\getNow();
        }
        if ($build == "community") {
            $bindArray[":AccountID"] = $userId;
            $sql = "select count(A.ID) as total
                from PersonalAccount A left join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID join PersonalAccountCnf Pf on A.Account = Pf.Account
                where U.MngAccountID = :AccountID AND A.Role = " . COMENDMROLE . " and A.Special = 0 $where";
            $total = $this->db->querySList($sql, $bindArray)[0]["total"];

            $sql = "select A.ID,A.EnableIpDirect,A.Account,A.Special,A.FirstName,A.LastName,A.RoomNumber,A.Email,A.Name,Pf.CallType,R.RoomName,R.Floor,
            A.ExpireTime,A.Active,A.SipAccount,A.CreateTime,A.TimeZone,A.Address,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.Initialization,A.PhoneStatus,U.UnitName as Building 
            from PersonalAccount A left join CommunityUnit U on A.UnitID = U.ID 
            join CommunityRoom R on R.ID = A.RoomID join PersonalAccountCnf Pf on A.Account = Pf.Account
            where U.MngAccountID = :AccountID AND A.Role = " . COMENDMROLE . " and A.Special = 0 $where order by A.ID desc limit $offset,$rows";
            $data = $this->db->querySList($sql, $bindArray);
        } else {
            $bindArray[":UnitID"] = $build;
            $sql = "select count(A.ID) as total
                from PersonalAccount A left join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID join PersonalAccountCnf Pf on A.Account = Pf.Account
                where A.ParentID = $userId AND A.Role = " . COMENDMROLE . " AND A.UnitID = :UnitID $where";
            $total = $this->db->querySList($sql, $bindArray)[0]["total"];
            $sql = "select A.ID,A.EnableIpDirect,A.Account,A.Special,A.FirstName,A.LastName,A.RoomNumber,A.Email,A.MobileNumber,A.Name,Pf.CallType,R.RoomName,R.Floor,
                A.ExpireTime,A.Active,A.SipAccount,A.CreateTime,A.TimeZone,A.Address,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.Initialization,A.PhoneStatus,U.UnitName as Building 
                from PersonalAccount A left join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID join PersonalAccountCnf Pf on A.Account = Pf.Account
                where A.ParentID = $userId AND A.Role = " . COMENDMROLE . " AND A.UnitID = :UnitID $where order by ID desc limit $offset,$rows";
            $data = $this->db->querySList($sql, $bindArray);
        }

        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm, ["ExpireTime"]);
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome && count($data) != 0) {
            $switch = $this->db->querySList(
                'select Switch from CommunityInfo where AccountID = :AccountID',
                [':AccountID' => $userId]
            )[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                $homeIds = [];
                foreach ($data as &$item) {
                    $homeId = $this->db->querySList(
                        'select HomeID from SmartHomeUserMap where Account = :Account',
                        [':Account' => $item['Account']]
                    );
                    $homeId = count($homeId) == 0 ? '' : $homeId[0]['HomeID'];
                    array_push($homeIds, $homeId);
                    $item['HomeId'] = $homeId;
                }
                unset($item);
                // $layouts = \util\smartHome\getHomeLayout($homeIds);
                // foreach ($data as $key => &$item) {
                //     if ($layouts[$key] == '') {
                //         $item['Layout'] = \util\smartHome\getCacheLayout($item['Account']);
                //     } else {
                //         $item['Layout'] = $layouts[$key];
                //     }
                // }
                // unset($item);
            }
        }

        $row = $data;
        $actives = [MSGTEXT["inactivated"], MSGTEXT["normal"], MSGTEXT["expired"]];
        foreach ($row as &$val) {
            $expireTime = strtotime($val["ExpireTime"]);
            if ($val["Active"] == 1 && time() > $expireTime) {
                $val["Active"] = 2;
            }
            $val["Status"] = $val["Initialization"] == "1" ? MSGTEXT["registered"] : MSGTEXT["unregistered"];
            if ($val["Active"] == 0) {
                $val["ExpireTime"] = '--';
            } else {
                $val["ExpireTime"] = \util\time\setTimeZone($val["ExpireTime"], $timeZone, $customizeForm);
            }
            $val["Active"] = $actives[$val["Active"]];
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);

        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $row, "detail" => $data]]);
    }

    /**
     * @name: 按Build获取社区账户，非分页
     */
    public function queryAllBuildComUser()
    {
        $params = [
            "Build" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $build = $params["Build"];
        $userId = $params["userAliasId"];
        if ($build == "community") {
            $data = $this->db->queryAllList(
                "PersonalAccount",
                ["equation" => [":ParentID" => $userId, ":Role" => COMENDMROLE]]
            );
        } else {
            $data = $this->db->queryAllList(
                "PersonalAccount",
                ["equation" => [":UnitID" => $build, ":Role" => COMENDMROLE]]
            );
        }
        foreach ($data as &$val) {
            unset($val["Passwd"]);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 获取社区用户详情
     * deviceCommunity 需要时区数据
     */
    public function queryComUserInfoForMng()
    {
        $params = [
            "ID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $data = $this->getComInfoControl($id, $userId);
        if ($data == false) {
            $data = $this->db->querySList(
                "select A.UUID,A.ID,A.Account,U.UnitName,R.RoomName,A.RoomNumber,A.EnableIpDirect,R.Floor from PersonalAccount A join CommunityRoom R on A.RoomID = R.ID join CommunityUnit U on U.ID = A.UnitID where A.ID = :ID",
                [":ID" => $id]
            )[0];
            $userCnf = $this->db->querySList('select WebRelay,CallType from PersonalAccountCnf where Account = :Account', [':Account' => $data['Account']])[0];
            $data['WebRelayID'] = $userCnf['WebRelay'];
            $data['CallType'] = $userCnf['CallType'];
            $data["Special"] = 1;
        } else {
            $data["Special"] = 0;
        }

        // if (ABLE_SMART_HOME) {
        //     $switch = $this->db->querySList(
        //         'select Switch from CommunityInfo where AccountID = :AccountID',
        //         [':AccountID' => $userId]
        //     )[0]['Switch'];
        //     $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
        //     if ($smartHomeSwitch == 1) {
        //         $homeIds = $this->db->querySList(
        //             'select HomeID from SmartHomeUserMap where Account = :Account',
        //             [':Account' => $data['Account']]
        //         );
        //         $layouts = \util\smartHome\getHomeLayout([$homeIds[0]['HomeID']]);
        //         $data['Layout'] = $layouts[0];
        //     }
        // }

        $data["SipGroup"] = $this->db->querySList(
            "select SipGroup from SipGroup2 where Account = :Account",
            [":Account" => $data["Account"]]
        )[0]["SipGroup"];
        $subUsers = $this->db->querySList(
            "select ID,Name,Email,MobileNumber,FirstName,LastName,Active,Phone,PhoneCode from PersonalAccount where ParentID = :ParentID and Role = 21 order by ID desc",
            [":ParentID" => $id]
        );
        $devices = $this->models["deviceCommunity"]->getownerDevices($userId, 0, 1000, $id, 0)["detail"];
        if (SERVER_LOCATION === 'cn') {
            $thirdPartDevices = $this->models["deviceCommunity"]->getownerDevices($userId, 0, 1000, $id, 'all')["detail"];
        } else {
            $thirdPartDevices = [];
        }
        $spDevice = $this->db->querySList(
            "select Dv.* from DevicesSpecial Ds join Devices Dv on Ds.MAC = Dv.MAC join PersonalAccount Pa on Pa.Account = Ds.Account where Pa.ID = :ID",
            [":ID" => $id]
        )[0];
        $data = ["details" => $data, "subUses" => $subUsers, "devices" => $devices, "spDevice" => $spDevice, 'thirdPartDevices' => $thirdPartDevices];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: PM获取社区主账户列表
     */
    public function queryComUserForPM()
    {
        $params = [
            "Build" => "",
            "Room" => "",
            "Stauts" => "",
            "Active" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $build = $params["Build"];
        $room = $params["Room"];
        $status = $params["Stauts"];
        $active = $params["Active"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $searchKey, $serchValue, $sortField, $sort) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [":ParentID" => $userId];
        switch ($searchKey) {
            case 'Name':
                $where = " and P.Name like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Email':
                $where = " and PU.Email like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Phone':
                $where = " and P.Phone like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Room':
                $where = " and R.RoomName like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            default:
                break;
        }

        if ($build != "all") {
            $where .= " and P.UnitID = :UnitID";
            $bindArray[":UnitID"] = $build;
        }

        if ($room != "all") {
            $where .= " and P.RoomID = :RoomID";
            $bindArray[":RoomID"] = $room;
        }

        if ($status != "all") {
            $where .= " and P.Initialization = :Initialization";
            $bindArray[":Initialization"] = $status;
        }

        if ($active != "all" && $active != "2") {
            $where .= " and P.Active = :Active";
            $bindArray[":Active"] = $active;
        }

        if ($active == "1") {
            $where .= " and P.ExpireTime > :ExpireTime and P.Active = 1";
            $bindArray[":ExpireTime"] = \util\computed\getNow();
        }

        if ($active == "2") {
            $where .= " and P.ExpireTime < :ExpireTime and P.Active = 1";
            $bindArray[":ExpireTime"] = \util\computed\getNow();
        }

        $orderBySql = ' order by P.CreateTime desc,P.ID desc';
        if(in_array($sortField, ['Name', 'RoomNumber', 'Apt']) && in_array($sort, ['asc', 'desc'])){
            $orderBySql = " order by P.$sortField $sort,P.ID desc";
            if($sortField === 'Apt'){
                $orderBySql = " order by CONVERT(R.RoomName, UNSIGNED) $sort,P.ID desc";
            }
        }

        $data = [];
        $sql = "select P.ID,P.RoomNumber,PU.MobileNumber,P.Name,PU.Email,P.ExpireTime,P.Phone,P.Initialization,P.Active,P.ActiveTime,P.CreateTime,R.RoomName,R.Floor,U.UnitName
                from PersonalAccount P join CommunityUnit U on P.UnitID = U.ID join CommunityRoom R on P.RoomID = R.ID
                left join PersonalAccountUserInfo PU on PU.UUID = P.UserInfoUUID
                where P.ParentID = :ParentID and Role = 20 and Special = 0 $where ";
        $data["total"] = count($this->db->querySList($sql, $bindArray));
        $data["detail"] = $data["row"] = $this->db->querySList($sql . " $orderBySql limit $offset,$rows", $bindArray);
        $register = ["0" => MSGTEXT["unregistered"], "1" => MSGTEXT["registered"]];
        $actives = [MSGTEXT["inactivated"], MSGTEXT["normal"], MSGTEXT["expired"]];

        foreach ($data["row"] as &$val) {
            $expritime = strtotime($val["ExpireTime"]);
            if ($val["Active"] == 1 && time() > $expritime) {
                $val["Active"] = 2;
            }
            $val["Active"] = $actives[$val["Active"]];
            // $val["ExpireTime"] = self::setTimeZone($val["ExpireTime"]);
            // $val["CreateTime"] = self::setTimeZone($val["CreateTime"]);
            // $val["ActiveTime"] = self::setTimeZone($val["ActiveTime"]);
            $val["Initialization"] = $register[$val["Initialization"]];
            $val["SubUsers"] = $this->db->querySList(
                "select PU.Email from PersonalAccount P left join PersonalAccountUserInfo PU on PU.UUID = P.UserInfoUUID where ParentID = :ID and Role = 21",
                [":ID" => $val["ID"]]
            );
            $val["SubCount"] = count($val["SubUsers"]);
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);
        $data["row"] = \util\time\setQueryTimeZone($data["row"], $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: PM获取社区从账户列表
     */
    public function queryComSubForPM()
    {
        $params = [
            "ID" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $count = count(
            $this->db->querySList(
                "select ID from PersonalAccount where ParentID=:ParentID and Role = 21",
                [":ParentID" => $id]
            )
        );
        $data = $this->db->querySList(
            "select ID,Name,FirstName,LastName,Email,MobileNumber,CreateTime,Active,Phone,PhoneCode,Active as StatusEnum from PersonalAccount where ParentID=:ParentID and Role = 21 order by ID desc limit $offset,$rows",
            [":ParentID" => $id]
        );
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => ["total" => $count, "row" => $data, "detail" => $data]]);
    }

    /**
     * @name: 超级管理员，区域管理员获取主账户列表
     */
    public function queryMainForSupArea()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => "",
            "role" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $roleCode = $params["role"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        $grade = $myData["Grade"];
        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = " and A.Special = 0 ";
        $where2 = "";
        $where3 = "";
        $bindArray = [];
        switch ($searchKey) {
            case 'Sip':
                $where = "$where AND A.SipAccount LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Email':
                if ($serchValue == "") {
                    $where = "$where AND (A.Email LIKE :serchValue or A.Email is null)";
                } else {
                    $where = "$where AND A.Email LIKE :serchValue";
                }
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'MobileNumber':
                if ($serchValue == "") {
                    $where = "$where AND (A.MobileNumber LIKE :serchValue or A.MobileNumber is null)";
                } else {
                    $where = "$where AND A.MobileNumber LIKE :serchValue";
                }
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Name':
                $where = "$where AND A.Name LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Install':
                $where = "$where AND C.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'FamilyMaster':
                if ($serchValue !== "") {
                    $where2 = " AND D.Name LIKE :serchValue";
                    // 1 = 2是为了不显示主账号 FamilyMaster查询的时候
                    $where3 = " AND 1 = 2";
                    $bindArray[":serchValue"] = "%$serchValue%";
                }
                break;
            case 'Community':
                $where = "$where AND B.Location LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }
        $title = "select A.ID,A.Active,A.SipAccount,A.Name,A.Email,A.MobileNumber,A.Role,A.Address,A.Phone,C.Account as Install,A.CreateTime,A.ExpireTime,A.PhoneExpireTime,B.Location as Community";
        $table1 = "PersonalAccount A JOIN Account B on A.ParentID = B.ID JOIN Account C on B.ManageGroup = C.ID";
        $table2 = "PersonalAccount A JOIN PersonalAccount D on A.ParentID = D.ID JOIN Account B ON D.ParentID = B.ID JOIN Account C on B.ManageGroup = C.ID";

        $sql = "$title,'--' as FamilyMaster from $table1 where (A.Role = " . PERENDMROLE . " or A.Role = " . COMENDMROLE . ") $where $where3 union all $title,D.Name as FamilyMaster
        from $table2 where (A.Role = " . PERENDSROLE . " or A.Role = " . COMENDSROLE . ") $where $where2";

        if ($grade == AREAGRADE) {
            $sql = "$title,'--' as FamilyMaster from $table1 where B.ParentID = $userId and (A.Role = " . PERENDMROLE . " or A.Role = " . COMENDMROLE . ")  $where $where3 union all $title,D.Name as FamilyMaster
            from $table2 where B.ParentID = $userId and (A.Role = " . PERENDSROLE . " or A.Role = " . COMENDSROLE . ") $where $where2";

            //v6.1更改 增加了一个AccountType的搜索条件
            if ($roleCode !== 'all') {
                $sqlWhereRole = " ";
                switch ($roleCode) {
                    case 'familyMaster':
                        $sqlWhereRole = " where t.Role= " . PERENDMROLE . " or t.Role = " . COMENDMROLE;
                        break;
                    case 'familyMember':
                        $sqlWhereRole = "where t.Role= " . PERENDSROLE . " or t.Role = " . COMENDSROLE;
                        break;
                    default:
                        break;
                }
                $sql = "select * from ( $sql ) as t $sqlWhereRole";
            }
        }

        $total = count($this->db->querySList($sql, $bindArray));
        $data = $this->db->querySList("$sql order by ID DESC limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $rows = $data;
        $roles = ["10" => "Family Master", "11" => "Family Member", "20" => "Family Master", "21" => "Family Member"];
        foreach ($rows as &$value) {
            if ($value['Active'] === '0') {
                $value['ExpireTime'] = '--';
            } elseif ($value["Role"] == 10) {
                $value["ExpireTime"] = $value["PhoneExpireTime"];
            }
            if ($value["Role"] == 10 || $value["Role"] == 11) {
                $value["Community"] = '--';
            }
            // $value["Role"] = $roles[$value["Role"]];
        }
        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $rows, "detail" => $data]]);
    }

    /**
     * @name: PM获取账户详情
     */
    public function getInfoForPM()
    {
        $params = [
            "ID" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $data = $this->getComInfoControl($id, $userId);
        
        // V6.5.1 增加Floor字段
        if ($data['Floor'] !== '') {
            $data['RoomName'] = $data['RoomName'].' ('.MSGTEXT['floor'].' '.$data['Floor'].')';
        }

        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm)[0];

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 获取付费账户
     */
    public function getComActiveOrUn()
    {
        $params = [
            "Active" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            'row' => '',
            'page' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $active = $params["Active"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        // kxl 2022-3-14 旧PM兼容
        $row = $params["row"] ? $params["row"] : 10000;
        $page = $params["page"] ? $params["page"] : 1;

        // 刷新一次过期时间
        $this->services["billsysUtil"]->checkPlan($userId);

        $offset = ($page - 1) * $row;
        // 获取已激活用户同时计算单个收费价格
        if ($active == 1) {
            $data = $this->db->querySList(
                "select U.UnitName,R.RoomName,A.Name,A.Email,A.ID,A.ExpireTime from PersonalAccount A 
                join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID 
                where A.ParentID = :ParentID and A.Role = " . COMENDMROLE . " and ExpireTime < '" . DEFAULTEXPIRETIME . "' 
                and A.Special = 0 and A.Active = :Active order by ExpireTime limit $offset, $row",
                [":ParentID" => $userId, ":Active" => $active]
            );

            $total = $this->db->querySList(
                "select count(*) from PersonalAccount A 
                join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID 
                where A.ParentID = :ParentID and A.Role = " . COMENDMROLE . " and ExpireTime < '" . DEFAULTEXPIRETIME . "'
                and A.Special = 0 and A.Active = :Active",
                [":ParentID" => $userId, ":Active" => $active]
            )[0]['count(*)'];

            $ids = [];
            foreach ($data as &$value) {
                array_push($ids, $value['ID']);
            }
            unset($value);
            $charges = $this->services["billsysUtil"]->getCharge('multiple', $userId, $ids, PAYSUBDRCIPTION);

            foreach ($data as &$value) {
                $value["Amcount"] = $charges[$value["ID"]]["MonthlyFee"];
                $value["SaveFee"] = $charges[$value["ID"]]["MonthlySaveFee"];
                $value["Introduction"] = $charges[$value["ID"]]["Introduction"];
                $value["ActivityId"] = $charges[$value["ID"]]["ActivityId"];
                $value['TimeZoneExpireTime'] = \util\time\setTimeZone($value['ExpireTime'], $timeZone, "3");
            }

            $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        } else {
            $data = $this->db->querySList(
                "select U.UnitName,R.RoomName,A.Name,A.Email,A.ID from PersonalAccount A 
                    join CommunityUnit U on A.UnitID = U.ID 
                    join CommunityRoom R on R.ID = A.RoomID 
                    where A.ParentID = :ParentID and A.Role = " . COMENDMROLE . " and A.Special = 0 and A.Active = :Active limit $offset, $row",
                [":ParentID" => $userId, ":Active" => $active]
            );

            $total = $this->db->querySList(
                "select count(*) from PersonalAccount A 
                join CommunityUnit U on A.UnitID = U.ID 
                join CommunityRoom R on R.ID = A.RoomID 
                where A.ParentID = :ParentID and A.Role = " . COMENDMROLE . " and A.Special = 0 and A.Active = :Active",
                [":ParentID" => $userId, ":Active" => $active]
            )[0]['count(*)'];

            $ids = [];
            foreach ($data as &$value) {
                array_push($ids, $value['ID']);
            }
            unset($value);

            $charges = $this->services["billsysUtil"]->getCharge('multiple', $userId, $ids, PAYACTIVE);

            foreach ($data as &$value) {
                $value["Amcount"] = $charges[$value["ID"]]["ActiveFee"];
                $value["SaveFee"] = $charges[$value["ID"]]["ActiveSaveFee"];
                $value["Introduction"] = $charges[$value["ID"]]["Introduction"];
                $value["ActivityId"] = $charges[$value["ID"]]["ActivityId"];
            }
        }
        \util\computed\setGAppData(["data" => $data, "total" => intval($total)]);
    }

    // 已经迁移到web-server
    public function getPerActiveOrUn()
    {
        $params = [
            "Active" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            'row' => '',
            'page' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $active = $params["Active"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $row = $params["row"];
        $page = $params["page"];

        $this->services["billsysUtil"]->checkPerPlan($userId);

        $offset = ($page - 1) * $row;
        if ($active == 1) {
            // V6.2需求 PhoneStatus = 1才被查询
            $data = $this->db->querySList(
                "select Name,Email,ID,PhoneExpireTime as ExpireTime from PersonalAccount 
                where ParentID = :ParentID and Role = " . PERENDMROLE . " and PhoneExpireTime < '" . DEFAULTEXPIRETIME .
                "' and Special = 0 and Active = :Active and PhoneStatus = 1 order by ExpireTime limit $offset, $row",
                [":ParentID" => $userId, ":Active" => $active]
            );

            $total = $this->db->querySList(
                "select count(*) from PersonalAccount 
                where ParentID = :ParentID and Role = " . PERENDMROLE . " and PhoneExpireTime < '" . DEFAULTEXPIRETIME .
                "' and Special = 0 and Active = :Active and PhoneStatus = 1",
                [":ParentID" => $userId, ":Active" => $active]
            )[0]['count(*)'];

            $ids = [];
            foreach ($data as &$value) {
                array_push($ids, $value['ID']);
            }
            unset($value);

            $charges = $this->services["billsysUtil"]->getCharge('single', $userId, $ids, PAYSUBDRCIPTION);

            foreach ($data as &$value) {
                $value["Amcount"] = $charges[$value["ID"]]["MonthlyFee"];
                $value["SaveFee"] = $charges[$value["ID"]]["MonthlySaveFee"];
                $value["Introduction"] = $charges[$value["ID"]]["Introduction"];
                $value["ActivityId"] = $charges[$value["ID"]]["ActivityId"];
                if ($value['ExpireTime'] == '2020-01-01 00:00:00') {
                    $value['ExpireTime'] = DEFAULTEXPIRETIME;
                }
                // 查询绑定设备
                $indoor = $this->db->querySList(
                    "select * from DevicesSpecial D join PersonalAccount P on P.Account = D.Account
                 where P.ID = :ID",
                    [":ID" => $value["ID"]]
                );
                if (count($indoor) == 0) {
                    $value['MonitorPlan'] = 0;
                } else {
                    $value['MonitorPlan'] = 1;
                }
            }

            $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        } else {
            $data = $this->db->querySList(
                "select Name,Email,ID from PersonalAccount where ParentID = :ParentID and Role = " . PERENDMROLE .
                " and Special = 0 and Active = :Active limit $offset, $row",
                [":ParentID" => $userId, ":Active" => $active]
            );

            $total = $this->db->querySList(
                "select count(*) from PersonalAccount where ParentID = :ParentID and Role = " . PERENDMROLE .
                " and Special = 0 and Active = :Active",
                [":ParentID" => $userId, ":Active" => $active]
            )[0]['count(*)'];

            $ids = [];
            foreach ($data as &$value) {
                array_push($ids, $value['ID']);
            }
            unset($value);

            $charges = $this->services["billsysUtil"]->getCharge('single', $userId, $ids, PAYACTIVE);

            foreach ($data as &$value) {
                $value["Amcount"] = $charges[$value["ID"]]["ActiveFee"];
                $value["SaveFee"] = $charges[$value["ID"]]["ActiveSaveFee"];
                $value["Introduction"] = $charges[$value["ID"]]["Introduction"];
                $value["ActivityId"] = $charges[$value["ID"]]["ActivityId"];
                // 查询绑定设备
                $indoor = $this->db->querySList(
                    "select * from DevicesSpecial D join PersonalAccount P on P.Account = D.Account
                 where P.ID = :ID",
                    [":ID" => $value["ID"]]
                );
                if (count($indoor) == 0) {
                    $value['MonitorPlan'] = 0;
                } else {
                    $value['MonitorPlan'] = 1;
                }
            }

            $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        }
        \util\computed\setGAppData(["data" => $data, 'total' => $total]);
    }

    public function getComNextExpireTimeUser()
    {
        $params = [
            "ID" => "",
            "Count" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $ids = $params["ID"];
        $months = $params["Count"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $bindArray = [":ParentID" => $userId, ":Active" => 1];
        $idParam = [];
        foreach ($ids as $key => $id) {
            array_push($idParam, ":ID{$key}");
            $bindArray[":ID{$key}"] = $id;
        }
        $idString = implode(",", $idParam);
        $data = $this->db->querySList(
            "select U.UnitName,R.RoomName,A.Name,A.Email,A.ID,A.ExpireTime from PersonalAccount A 
				join CommunityUnit U on A.UnitID = U.ID 
				join CommunityRoom R on R.ID = A.RoomID 
				where A.ParentID = :ParentID and A.Role = 20 and A.Special = 0 and A.Active = :Active and A.ID in ($idString)",
            $bindArray
        );
        $now = \util\computed\getNow();
        foreach ($data as &$value) {
            $nextTime = \util\computed\computedLastDate($value["ExpireTime"], $months);
            $nextNow = \util\computed\computedLastDate($now, $months);
            if (strtotime($nextTime) < strtotime($nextNow)) {
                $nextTime = $nextNow;
            }
            $value["NextExpireTime"] = $nextTime;
            $value["ExpireTime"] = $value["ExpireTime"];
        }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getLandlineExpireTimeUsers()
    {
        $params = [
            "ID" => "",
            "Count" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $ids = $params["ID"];
        $months = $params["Count"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $bindArray = [":ParentID" => $userId];
        $idParam = [];
        foreach ($ids as $key => $id) {
            array_push($idParam, ":ID{$key}");
            $bindArray[":ID{$key}"] = $id;
        }
        $idString = implode(",", $idParam);

        $now = \util\computed\getNow();
        $data = $this->db->querySList(
            "select ID,Email,Name,PhoneExpireTime as ExpireTime from PersonalAccount where ID in ($idString) and Active = 1 and Special = 0 and Role = 10 and ParentID = :ParentID",
            $bindArray
        );
        foreach ($data as &$value) {
            $nextTime = \util\computed\computedLastDate($value["ExpireTime"], $months);
            $nextNow = \util\computed\computedLastDate($now, $months);
            if (strtotime($nextTime) < strtotime($nextNow)) {
                $nextTime = $nextNow;
            }
            $value["NextExpireTime"] = $nextTime;
        }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function querySubForMng()
    {
        $params = [
            "ID" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $count = count(
            $this->db->querySList(
                "select ID from PersonalAccount where ParentID=:ParentID and (Role = 21 or Role = 11)",
                [":ParentID" => $id]
            )
        );
        $data = $this->db->querySList(
            "select ID,Name,FirstName,LastName,Email,MobileNumber,CreateTime,Active,Phone,PhoneCode from PersonalAccount where ParentID=:ParentID and (Role = 21 or Role = 11) order by ID desc limit $offset,$rows",
            [":ParentID" => $id]
        );
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $data = ["row" => $data, "detail" => $data, "total" => $count];
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getComInfoControl($id, $communityId)
    {
        global $cMessage;
        $data = $this->db->querySList(
            "select P.*,Pf.CallType,R.RoomName,R.Floor,U.UnitName,Pf.WebRelay as WebRelayID from PersonalAccount P join PersonalAccountCnf Pf on P.Account = Pf.Account 
            join CommunityRoom R on P.RoomID = R.ID join CommunityUnit U on U.ID = P.UnitID
            where P.ID = :ID and P.ParentID = :ParentID and P.Role = 20",
            [":ID" => $id, ":ParentID" => $communityId]
        );
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateUserNotExits);
        }

        $data = $data[0];
        if ($data["Special"] == 1) {
            return false;
        }
        unset($data["Passwd"]);
        unset($data["SipPwd"]);

        $rfData = $this->db->queryAllList("PersonalRfcardKey", ["equation" => [":Node" => $data["Account"]]]);
        $keyData = $this->db->queryAllList("PersonalPrivateKey", ["equation" => [":Node" => $data["Account"], ":Special" => 1]]);
        $data["PKey"] = count($keyData) == 0 ? "" : $keyData[0]["Code"];
        foreach ($rfData as &$val) {
            $val = ["text" => $val["Code"], "isModify" => $val["ID"]];
        }
        $data["RfCard"] = $rfData;
        return $data;
    }

    // 新pm查询列表数据
    public function queryNewPMList()
    {
        $NORMAL = 1;
        $INACTIVATED = 2;
        $EXPIRING = 3;
        $EXPIRED = 4;

        $params = [
            "Build" => "",
            "Room" => "",
            "Status" => "",
            "Active" => "",
            "Role" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $build = $params["Build"];
        $room = $params["Room"];
        $status = $params["Status"];
        $active = $params["Active"];
        $userId = $params["userAliasId"];
        $role = $params["Role"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $searchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [":ParentID" => $userId];
        switch ($searchKey) {
            case 'Name':
                $where .= " and P.Name like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Email':
                if ($serchValue != "") {
                    $where .= " and P.Email like :Key";
                    $bindArray[":Key"] = "%$serchValue%";
                }
                break;
            case 'MobileNumber':
                if ($serchValue != "") {
                    $where .= " and P.MobileNumber like :Key";
                    $bindArray[":Key"] = "%$serchValue%";
                }
                break;
            default:
                break;
        }

        switch ($active) {
            case $NORMAL:
                $where .= " and P.Active = 1 and P.ExpireTime > :ExpireTime";
                $bindArray[":ExpireTime"] = \util\computed\getNow();
                break;
            case $INACTIVATED:
                $where .= " and P.Active = 0";
                break;
            case $EXPIRING:
                $where .= " and P.Active = 1 and P.ExpireTime < :ExpireTIme and P.ExpireTime > :ExpireTIme2";
                $bindArray[":ExpireTIme"] = date('Y-m-d H:i:s', strtotime('+7 day'));
                $bindArray[":ExpireTIme2"] = \util\computed\getNow();
                break;
            case $EXPIRED:
                $where .= " and P.Active = 1 and P.ExpireTime < :ExpireTIme";
                $bindArray[":ExpireTIme"] = \util\computed\getNow();
                break;
        }

        $whereMaster = '';
        $whereMember = '';
        if ($room != 'all') {
            $whereMaster .= " and P.RoomID = :RoomID";
            $whereMember .= " and P1.RoomID = :RoomID";
            $bindArray[":RoomID"] = $room;
        }

        if ($build != 'all') {
            $whereMaster .= " and P.UnitID = :UnitID";
            $whereMember .= " and P1.UnitID = :UnitID";
            $bindArray[":UnitID"] = $build;
        }

        if ($status != 'all') {
            $where .= " and P.Initialization = :Initialization";
            $bindArray[":Initialization"] = $status;
        }

        $queryResult = "select P.ID, P.ParentID, P.Name, P.Role, P.Email, P.MobileNumber, CU.UnitName, CR.RoomName, CR.Floor, P.Initialization, P.Active, P.ExpireTime";

        $sqlMaster = " from PersonalAccount P join CommunityUnit CU on P.UnitID = CU.ID join CommunityRoom CR on CR.ID = P.RoomID
        where P.ParentID = :ParentID and P.Role = 20 and P.Special = 0 $where $whereMaster";
        $sqlMember = " from PersonalAccount P join PersonalAccount P1 on P.ParentID = P1.ID
        join CommunityUnit CU on P1.UnitID = CU.ID join CommunityRoom CR on CR.ID = P1.RoomID where P1.ParentID = :ParentID and P.Role = 21 $where $whereMember";


        if ($role == 'master') {
            $total = $this->db->querySList("select count(*) $sqlMaster", $bindArray)[0]['count(*)'];
            $data = $this->db->querySList("$queryResult $sqlMaster order by ID desc limit $offset, $rows", $bindArray);
        } elseif ($role == 'member') {
            $total = $this->db->querySList("select count(*) $sqlMember", $bindArray)[0]['count(*)'];
            $data = $this->db->querySList("$queryResult $sqlMember order by ID desc limit $offset, $rows", $bindArray);
        } else {
            $totals = $this->db->querySList(
                "select count(*) $sqlMaster union all select count(*) $sqlMember",
                $bindArray
            );
            $total = $totals[0]["count(*)"] + $totals[1]["count(*)"];
            $data = $this->db->querySList(
                "$queryResult $sqlMaster union $queryResult $sqlMember order by ID desc limit $offset, $rows",
                $bindArray
            );
        }

        foreach ($data as &$val) {
            if (strtotime($val["ExpireTime"]) < time() && $val["Active"] == 1) {
                $val["Active"] = 2;
            }
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

        $data = ["row" => $data, "detail" => $data, "total" => $total];
        \util\computed\setGAppData(["data" => $data]);
    }

    // 判断账号是否超出pm限制的个数
    public function getAppUserConf($id)
    {
        $this->log->actionLog("#model#user#getPersonalAccount#id=$id");
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]])[0];
        $this->log->actionLog("#model#user#getAppUserConf#data=" . json_encode($data));
        $account = $data["Account"];
        $appData = $this->db->querySList(
            "select count(*),Node,CreateTime from APPSpecial where Account = :Account",
            [":Account" => $account]
        )[0];
        // 只有App注册的社区从账号才有限制
        if ($appData['count(*)'] == 0) {
            return 0;
        } else {
            $now = \util\computed\getNow();
            $isExpired = $this->db->querySList(
                "select count(*) from CommunityInfo Ci left join PersonalAccount Pa on Pa.ParentID = Ci.AccountID
             where Ci.FeatureExpireTime < :NowTime and Pa.Account = :Account",
                [":NowTime" => $now, ":Account" => $appData['Node']]
            )[0]['count(*)'];
            // Feature Plan 已过期
            if ($isExpired > 0) {
                return 0;
            } else {
                $accountInfo = $this->db->querySList(
                    "select AllowCreateSlaveCnt,Flags from PersonalAccountCnf where Account = :Account",
                    [":Account" => $appData['Node']]
                )[0];
                if ($accountInfo['Flags'] % 2 == 1) {
                    $subAppNumber = $this->db->querySList(
                        "select count(*) from APPSpecial where Node = :Node and 
                    CreateTime < :CreateTime",
                        [":Node" => $appData['Node'], ":CreateTime" => $appData['CreateTime']]
                    )[0]['count(*)'];
                    if ($subAppNumber >= $accountInfo['AllowCreateSlaveCnt']) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            }
        }
    }
}
