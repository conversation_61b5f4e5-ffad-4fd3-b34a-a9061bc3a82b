<?php
/*
 * @Description: 通用数据容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-09 11:54:39
 * @LastEditors  : kxl
 */
namespace dataContainer;
include_once __DIR__."/../interfaces/container/main.php";
use interfaces\container\main\dataContainer;
class <PERSON>ommon implements dataContainer {
    private $data;
    public function bind ($key,$value) {
        // $this->data[$key] = $value;
    }
    public function get ($key) {
        global $gApp,$cLog;
        $cLog->actionLog("#dataContainer#common#getKey#$key:".$gApp["plan"]["data"][$key]);
        return $gApp["plan"]["data"][$key];
    }
}