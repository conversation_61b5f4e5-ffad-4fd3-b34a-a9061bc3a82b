<?php
/*
 * @Description: 管理员Account合法性检测
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/model.php";
class CMngAccountCheck implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$cMessage;
        $params = ["Account"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $account = $params["Account"];
        $cLog->actionLog("#middle#mngAccountCheck#Account=".$account);
        $regex = '/^\d*$/';
        if(preg_match($regex, $account)) $cMessage->echoErrorMsg(StateAccountNIncorrect);
        $regex = '/^[0-9a-zA-Z_\-@\.]*$/';
		if(!preg_match($regex, $account)) $cMessage->echoErrorMsg(StateAccountNumLet);
        $next();
    }
}