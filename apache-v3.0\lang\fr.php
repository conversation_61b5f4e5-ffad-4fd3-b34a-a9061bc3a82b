<?php
  const MSGTEXT = [ 

"accountExits"=>"Ce compte existe déjà",
"accountNotExit"=>"Le compte n'existe pas",
"accountIncorrect"=>"Nom d'utilisateur ou mot de passe invalide",
"accountNIncorrect"=>"Compte non valide",
"activeEmpty"=>"Une valeur active est requise",
"addFail"=>"Echec de l'ajout",
"addSuccess"=>"L'ajout a réussi.",
"addSuccessPw"=>"Ajout réussi, le mot de passe est '%s'.",
"addTmpKeyFail"=>"L'ajout de la clé temporaire a échoué, veuillez réessayer.",
"aptDuplicated"=>"L'appartement %s est dupliqué",
"aptDigits"=>"L'appartement %s est invalide, il doit être compris entre 1 et 6 chiffres.",
"aptExit"=>"L'appartement %s existe déjà",
"abnormal"=>"Anomalie",
"activation"=>"Activation",
"additionalApp"=>"Application supplémentaire",
"bindDevice"=>"Veuillez supprimer tous les appareils de ce compte",
"bindMAClibrary"=>"Veuillez supprimer le MAC de la bibliothèque MAC",
"bindUser"=>"Veuillez supprimer les utilisateurs de ce compte",
"buildingBindDevice"=>"Veuillez supprimer les appareils de ce bâtiment",
"buildingBindUser"=>"Veuillez supprimer les utilisateurs de ce bâtiment",
"buildingDigits"=>"Bâtiment non valide %s, doit être un nombre de 1 ou 2 chiffres.",
"buildingExit"=>"Le bâtiment existe déjà",
"BindingDeviceFailed"=>"La liaison de l'appareil a échoué, l'appareil peut être lié à un autre utilisateur ou n'a pas été ajouté à la bibliothèque MAC.",
"chcekMacExits"=>"L'ajout a échoué, l'adresse MAC n'est pas valide ou existe déjà.",
"changePasswdFail"=>"Echec de la modification du mot de passe",
"changePasswdPEmail"=>"Modification du mot de passe réussie, veuillez vérifier vos Emails %s",
"community"=>"Site",
"deleteFail"=>"Echec de la suppression",
"deleteSuccess"=>"Suppression réussie",
"deviceTypeEmpty"=>"Le type d'appareil est requis",
"deviceNotFindUser"=>"Appareil non trouvé, veuillez contacter votre administrateur",
"dealSuccess"=>"Paramétrage réussi",
"doorUnit"=>"Unité de porte",
"emailExits"=>"L'Email existe déjà",
"emailPExit"=>"L'email %s existe déjà",
"emailNotExits"=>"Cet Email n'existe pas.",
"emailDuplicated"=>"L'email %s est dupliqué",
"errorVersion"=>"Erreur Version",
"emailOrAccountNotExit"=>"Cet Email n'existe pas.",
"firstNameEmpty"=>"Le prénom est obligatoire",
"failed"=>"Echec",
"family"=>"Famille",
"guardPhone"=>"Téléphone du gardien",
"incorrectSipAccount"=>"Plus de compte SIP disponible",
"incorrectSipAccountGroup"=>"Plus de groupe SIP disponible",
"importDataSuccess"=>"Importation des données réussie",
"importFailMACExit"=>"L'importation a échoué, veuillez vérifier si l'adresse MAC existe ou si elle est valide : \r\n%s",
"invaildDC"=>"Code d'appareil invalide",
"InvalidFile"=>"Fichier non valide",
"invalidPEmail"=>"Email non valide %s",
"invalidPName"=>"Nom d'utilisateur non valide %s",
"invalidPCalltype"=>"Type d'appel non valide %s",
"invalidPPin"=>"PIN invalide %s",
"invalidPActive"=>"Valeur active invalide %s",
"invalidPage"=>"Page non valide",
"invalidPDeviceType"=>"Type d'appareil non valide %s",
"invaildVerCode"=>"Code de vérification non valide",
"invalidIdentity"=>"Informations d'identité non valides ! Vous vous êtes peut-être connecté ailleurs, veuillez vous connecter à nouveau.",
"indoorMonitor"=>"Moniteur intérieur",
"inactivated"=>"Inactivé",
"normal"=>"Normal",
"expired"=>"Expiré",
"lastNameEmpty"=>"Le nom de famille est obligatoire",
"locationEmpty"=>"L'adresse est requise",
"locationPLoog"=>"%s adresse trop longue",
"locationLoog"=>"Adresse trop longue",
"loginError"=>"Erreur de connexion",
"loginFail"=>"Echec de la connexion",
"loginSuccess"=>"Connexion réussie",
"limitIP"=>"Trop de tentative, veuillez réessayer dans 5 minutes.",
"limitDevice"=>"Le numéro de votre appareil a atteint la limite maximale",
"MAC2PLibrary"=>"L'adresse MAC:%s n'est pas valide, veuillez vérifier votre bibliothèque MAC.",
"MAC2Library"=>"L'adresse MAC n'est pas valide, veuillez vérifier votre bibliothèque MAC.",
"macExits"=>"L'adresse MAC existe déjà",
"MACLength"=>"L'adresse MAC doit être de 12 chiffres.",
"modifySuccess"=>"Modification réussi",
"modifyFailed"=>"Échec de la modification",
"maxHouse"=>" Le nombre d'utilisateurs a atteint la limite maximale, veuillez contacter l'administrateur. La sauvegarde a échoué ! Le numéro APT existe déjà, vous devez d'abord le supprimer.",
"modifyAptFail"=>"La sauvegarde a échoué ! Le numéro APT existe déjà, vous devez d'abord le supprimer.",
"nameloog"=>"Le nom de l'utilisateur est trop long. Le nom de l'utilisateur peut contenir jusqu'à 64 caractères.",
"nameExit"=>"Le nom d'utilisateur existe déjà",
"notPermission"=>"Vous n'avez pas la permission",
"noSip"=>"Plus de compte SIP",
"passwordIncorrect"=>"Mot de passe incorrect",
"passwdChangeSuccess"=>" Changement du mot de passe réussi",
"passwordResetSuccess"=>"Réinitialisation du mot de passe réussi",
"passwordReset2"=>"Le mot de passe a été réinitialisé à '%s'.",
"payTimeOut"=>"Expiration du paiement",
"payFailed"=>"Échec du paiement",
"processing"=>"Traitement",
"paySuccess"=>"Paiement réussi",
"redirectedOnRPS"=>"Cette adresse MAC est redirigée sur RPS.",
"registerFailed"=>"Echec de l'enregistrement",
"registerSuccess"=>"Enregistrement réussi",
"roomNotExit"=>"Cet utilisateur n'existe pas !",
"RFCardExit"=>"Cette carte RF existe déjà",
"registered"=>"Enregistré",
"PrivateKeyExists"=>"Cette clé privée existe",
"passwordCorrect"=>"Mot de passe invalide",
"timeLessCurrent"=>"Heure de mise à jour invalide",
"timeZoneChangeSuccess"=>"Changement de fuseau horaire réussi",
"timeOut"=>"Temps d'arrêt",
"unbindMACUser"=>"Veuillez libérer %s avec l'utilisateur d'abord",
"unKnowDT"=>"Type d'appareil inconnu",
"userBindUser"=>"Veuillez d'abord supprimer les utilisateurs sous ce compte",
"userNotExit"=>"Cet utilisateur n'existe pas",
"userMaxPLimt"=>"Echec de la création, vous ne pouvez pas ajouter plus de %s membres du compte",
"unregistered"=>"Non enregistré",
"validMAC"=>"Veuillez saisir une adresse MAC valide",
"versionExit"=>"La version existe déjà",
"versionNameNumberExit"=>"Le nom ou le numéro de la version existe déjà",
"sipStatus"=>"Echec de l'enregistrement du compte SIP, veuillez réessayer.",
"sentCodeLater"=>"Nous vous avons envoyé un code de vérification, veuillez réessayer plus tard.",
"setSuccess"=>"Configuration réussi",
"sendEmailSuccess"=>"Envoi de l'e-mail réussi",
"SetFailed"=>"Échec du réglage",
"stairPhone"=>"Téléphone d'escalier",
"successed"=>"Réussir",
"subscription"=>"Abonnement",
"wallPhone"=>"Téléphone mural",
"emailMaxLen"=>"L'e-mail doit comporter moins de 64 caractères.",
"serverUpgradeTips"=>"La mise à jour du serveur est terminée, veuillez actualiser la page. Avant, vous pouvez copier les données que vous venez de saisir à un autre endroit.",
"ActiveFamilyAccount"=>"Veuillez d'abord activer le compte du maître de famille.",
"weekly"=>"Hebdomadaire",
"daily"=>"Journalier",
"never"=>"Jamais",
"calltypeEmpty"=>"Le type appel est requis",
"addOutApt"=>"Vous pouvez ajouter jusqu'à %s pièces",
"call"=>"Appel",
"unlock"=>"Déverrouillage",
"tryUnlockCall"=>"Échec de déverrouillage Appel",
"tryUnlockKey"=>"Échec de déverrouillage code PIN",
"tryUnlockCard"=>"Échec de déverrouillage carte RF",
"tryUnlockFace"=>"Échec de déverrouillage visage",
"unlockApp"=>"Déverrouillage SmartPlus",
"unlockIndoor"=>"Déverrouillage Moniteur intérieur",
"unlockNFC"=>"Déverrouillage NFC",
"unlockBluetooth"=>"Déverrouillage Bluetooth",
"unlockCard"=>"Déverrouillage carte RF",
"unlockPrivateKey"=>"Déverrouillage code PIN",
"unlockTempKey"=>"Déverrouillage clé temporaire",
"alarmDoorUnlock"=>"Déverrouillage de la porte",
"alarmInfrared"=>"Infrarouge",
"alarmSmoke"=>"Fumée",
"alarmGas"=>"Gaz",
"alarmUrgency"=>"Urgency",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Tamper",
"alarmGate"=>"Portail",
"alarmDoor"=>"Porte",
"alarmBedroom"=>"Chambre",
"alarmGuestRoom"=>"Chambre d'ami",
"alarmHall"=>"Hall",
"alarmWindow"=>"Fenêtre",
"alarmBalcony"=>"Balcon",
"alarmKitchen"=>"Cuisine",
"alarmStudy"=>"Étude",
"alarmBathroom"=>"Salle de bain",
"alarmArea"=>"Zone",
"RFCardExit2"=>"La carte RF %s existe déjà",
"RFCardDuplicated"=>"La carte RF %s est dupliquée",
"notMacBind"=>"L'utilisateur '%s' n'a pas la permission d'ouvrir la porte avec l'appareil '%s'.",
"accountNumLet"=>"Le compte doit être composé de chiffres et de lettres",
"networkUnavailable"=>"Réseau indisponible.",
"notForModel"=>"Pas pour ce modèle.",
"upgradeDevVersion"=>"Veuillez d'abord effectuer une mise à jour vers la dernière version.",
"unavailableService"=>"Le service est temporairement indisponible, veuillez réessayer ultérieurement.",
"cantDeletePin"=>"Vous ne pouvez pas supprimer le code PIN %s",
"residentInRoom"=>"Il y a déjà des résidents dans la pièce %s",
"noAnswer"=>"Pas de réponse",
"indoorAndApp"=>"Moniteur intérieur et application",
"indoorMonitorOnly"=>"Moniteur intérieur uniquement",
"appOnly"=>"Application uniquement",
"endThanStart"=>"L'heure de fin ne peut être antérieure à l'heure de début.",
"endThanStartFile"=>"Jour ou heure non valide dans la ligne '%s'.",
"doorRelease"=>"Ouverture de la porte",
"success"=>"Succès",
"unlockFACE"=>"Déverrouillage du visage",
"unlockBLE"=>"Déverrouillage du Bluetooth",
"captureSmartPlus"=>"Capture sur SmartPlus",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"Échec du déverrouillage",
"deviceDisconnected"=>"L'appareil a été déconnecté.",
"low"=>"Faible",
"motion"=>"Mouvement",
"capture"=>"Capture",
"failedImport"=>"Échec de l'importation",
"notValidMobile"=>"%s n'est pas un numéro de téléphone mobile valide.",
"mobileExits"=>"Le numéro de mobile existe déjà",
"mobileExits2"=>"Le numéro de mobile %s existe déjà",
"mobileDuplicated"=>"Le numéro de mobile %s est dupliqué",
"mobileNumberExist"=>"Le numéro de portable n'existe pas.",
"codeIncorrect"=>"Code invalide",
"sendCodeSuccess"=>"Envoi réussi du code de vérification",
"codeCorrect"=>"Correct",
"mobileNumberEmpty"=>"Veuillez entrer votre numéro de téléphone mobile.",
"invalidUser"=>"Utilisateur non valide %s",
"locationExits"=>"L'adresse du site existe déjà",
"smartPlusIndoor"=>"Smartplus et moniteurs intérieurs",
"phoneIndoor"=>"Téléphone et moniteurs intérieurs",
"smartPlusIndoorBackup"=>"SmartPlus et moniteurs intérieurs, avec renvoi vers téléphone",
"smartPlusBackup"=>"Moniteurs intérieurs avec renvois vers SmartPlus",
"indoorPhoneBackup"=>"Moniteurs intérieurs avec renvois vers téléphone",
"indoorSmartPlusPhone"=>"Moniteurs intérieurs avec renvois vers SmartPlus, enfin le téléphone",
"endUser"=>"Utilisateur final",
"installer"=>"Installateur",
"distributor"=>"Distributeur",
"pm"=>"GI",
"superManage"=>"SuperGestion",
"loginManagement"=>"Gestion des connexions",
"accessControl"=>"Contrôle d'accès",
"userManagement"=>"Gestion des utilisateurs",
"deviceManagement"=>"Gestion des appareils",
"communityManagement"=>"Gestion du site",
"auditLogin"=>"Connexion: Web",
"auditLogout"=>"Déconnexion: Web",
"auditAddTempKey"=>"Ajouter clé temporaire: {0}",
"auditEditTempKey"=>"Editer clé temporaire: {0}",
"auditDeleteTempKey"=>"Supprimer clé temporaire: {0}",
"auditAddRFCard"=>"Ajouter carte RF: {0}",
"auditEditRFCard"=>"Editer carte RF: {0}",
"auditDeleteRFCard"=>"Supprimer carte RF: {0}",
"auditAddDis"=>"Ajouterer distributeur: {0}",
"auditEditDis"=>"Editer distributeur: {0}",
"auditDeleteDis"=>"Supprimer distributeur: {0}",
"auditAddInstaller"=>"Ajouter installateur: {0}",
"auditEditInstaller"=>"Editer installateur: {0}",
"auditDeleteInstaller"=>"Supprimer installateur: {0}",
"auditAddPM"=>"Ajouter GP: {0}",
"auditEditPM"=>"Editer GP: {0}",
"auditDeletePM"=>"Supprimer GP: {0}",
"auditAddEndUser"=>"Ajouter l'utilisateur final : {0}",
"auditEditEndUser"=>"Editer l'utilisateur final : {0}",
"auditDeleteEndUser"=>"Supprimer l'utilisateur final : {0}",
"auditSetOwnerTime"=>"Définir son propre fuseau horaire {0}",
"auditSetOwnPassword"=>"Définir son propre mot de passe",
"auditAddPIN"=>"Ajouter PIN: {0}",
"auditEditPIN"=>"Editerer PIN: {0}",
"auditDeletePIN"=>"Supprimer PIN: {0}",
"auditImportFace"=>"Importer visage: {0}",
"auditDeleteFace"=>"Supprimer visage: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Définir le type d'appel via moniteurs intérieurs et Smartplus :  {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"Définir le type d'appel via moniteurs intérieurs et téléphone :  {0}&{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Définir le type d'appel via moniteurs intérieurs et Smartplus avec le téléphone en secours :  {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"Définir le type d'appel via moniteurs intérieurs avec Smartplus en secours :  {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Définir le type d'appel via moniteurs intérieurs avec le téléphone en secours :  {0}&{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Définir le type d'appel via moniteurs intérieurs avec SmartPlus en secours, enfin le téléphone : {0}&{1}",
"auditDeleteDevice"=>"Supprimer l'appareil : {0}",
"auditSetAPTCount"=>"Définir le nombre d'appartements {0}",
"auditEnableLandline"=>"Activer le service de téléphonie fixe",
"auditDisableLandline"=>"Désactiver le service de téléphonie fixe",
"auditSetSubTime"=>"Définir le fuseau horaire {0}",
"auditSetChargeModeInstall"=>"Définir le mode de paiement Payer par l'installateur",
"auditSetChargeModeUser"=>"Définir le mode de paiement Payer par l'utilisateur/GP",
"auditSetConnectTypeDefault"=>"Définir le type de connexion défaut",
"auditSetConnectTypeTCP"=>"Définir le type de connexion tcp",
"auditSetConnectTypeUDP"=>"Définir le type de connexion udp",
"auditSetConnectTypeTLS"=>"Définir le type de connexion tls",
"auditAddCommunity"=>"Ajouter site: {0}",
"auditDeleteCommunity"=>"Supprimer site: {0}",
"auditImportCommunity"=>"Importer site: {0}",
"auditSetAPTNumber"=>"Définir {0} numéro de pièce {1}",
"auditSetEmail"=>"Définir email {0} : {1}",
"auditSetMobile"=>"Définir numéro de téléphone {0} : {1}",
"auditDeviceTypeStair"=>"Définir le type Interphone Multi-résidents",
"auditDeviceTypeDoor"=>"Définir le type Interphone villa",
"auditDeviceTypeIndoor"=>"Définir le type Moniteur intérieur: {0}",
"auditDeviceTypeGuardPhone"=>"Définir le type de téléphone de garde : {0}",
"auditDeviceTypeAccessControl"=>"Définir le type Contrôle d'accès : {0}",
"auditSetNetGroup"=>"Définir le groupe de réseau {0} : {1}",
"auditEditCommunity"=>"Modifier site",
"deliveryMsg"=>"Vous avez %s articles qui vous ont été livrés, merci de vérifier. ",
"deliveryTitle"=>"Vous avez un nouveau colis !",
"rfcardDuplicatedLines"=>"Numéro de carte RF dupliqué à la ligne %s !",
"rfcardNameInvalid"=>"Nom de carte RF non valide à la ligne %s !",
"rfcardExistLines"=>"Les cartes RF existe déjà à la ligne %s.",
"importFailMacExistLines"=>"l'adresse MAC existe ou est valide à la ligne %s.",
"exportExcelCountNull"=>"Aucun journal à exporter à cette date ! Veuillez sélectionner à nouveau.",
"keyIsEqualRoom"=>"La clé de livraison ne peut pas être identique au numéro APT !",
"visitor"=>"visiteur",
"CommunityNameExist"=>"Le nom du site existe déjà",
"unlockGuardPhone"=>"Déverrouillage du téléphone de garde",
"auditLoginApp"=>"Connexion : App",
"auditLogoutApp"=>"Déconnexion : App",
"timeForYesterday"=>"Hier",
"exportExcelDataBefore"=>"Données trop volumineuses ! Veuillez d'abord exporter les données %s, merci.",
"tempkeyUsed"=>"Clé temporaire utilisé",
"tempkeyContent"=>"%s a utilisé la clé temporaire.",
"accessNameExist"=>"Le nom du groupe d'accès existe déjà",
"addFaceFail"=>"Veuillez importer une photo nette du visage.",
"userInvalid"=>"Utilisateur non valide dans la ligne %s.",
"groupsInvalid"=>"Groupe d'accès non valide dans la ligne %s.",
"BuildAccessName"=>"Bâtiment résidentiel %s",
"auditCodeLogEditApt"=>"éditer l'appartement:{0}",
"invalidTimeInLine"=>"Heure non valide dans la ligne %s.",
"cancel"=>"Annuler",
"cancelSuccess"=>"Annulation réussie.",
"payOutstanding"=>"Paiement inhabituel ! Veuillez contacter votre fournisseur de services.",
"featureDeleteError"=>"Les forfaits peuvent être liées",
"beyondFamilyMember"=>"Vous ne pouvez pas créer plus de membres du compte, veuillez contacter votre fournisseur de services pour le faire.",
"indoorMonitorRequired"=>"Au moins un moniteur intérieur est requis pour chaque appartement.",
"featureActivationFee"=>"Fonctionnalités (Frais unique)",
"systemProcessing"=>"Traitement du système",
"featureMonthlyFee"=>"Fonctionnalités (Frais mensuel)",
"featurePriceDifferences"=>"Fonctionnalités (différences de prix)",
"updatingSuccess"=>"Mise à jour réussie !",
"featureNameBasic"=>"Basique",
"featureNamePremium"=>"Premium",
"indoorMacNotCorrect"=>"Veuillez entrer le correct MAC du moniteur intérieur",
"off"=>"Off",
"enterValidAccount"=>"Veuillez entrer un compte valide",
"invalidKitImportMAC"=>"Veuillez vérifier si le MAC existe ou est valide : %s",
"importLessData"=>"Veuillez importer moins de %s données.",
"invalidQRCode"=>"Identification échouée, veuillez scanner un code QR valide",
"cannotCreateFamilyMember"=>"Vous ne pouvez pas créer plus de membres du compte.",
"importProcessing"=>"Importation, veuillez réessayer plus tard",
"departmentAccessName"=>"%s Groupe d'accès",
"idExistsLine"=>"L'ID existe déjà dans la ligne %s",
"enterFirstNameLine"=>"Veuillez entrer le prénom dans la ligne %s",
"enterLastNameLine"=>"Veuillez entrer le nom de famille dans la ligne %s",
"departmentExist"=>"Le département existe déjà",
"idExist"=>"L'ID existe déjà",
"layoutIdInvalid"=>"La mise en page n'est pas valide",
"unlockAppHome"=>"Déverrouillage de l'AKHome",
"officeNameExist"=>"Le nom du bureau existe déjà",
"departmentExit"=>"Le département existe déjà.",
"importOutTask"=>"Vous ne pouvez importer qu'un modèle à la fois.",
"idDuplicated"=>"L'ID %s est dupliquée",
"aptInvalidLine"=>"APT invalide dans la ligne %s.",
"buildInvalidLine"=>"Bâtiment invalide dans la ligne %s.",
"departmentInvalidLine"=>"Département invalide dans la ligne %s.",
"idInvalidLine"=>"ID invalide dans la ligne %s.",
"propertyManager"=>"Gestionnaire",
"departmentBindDevice"=>"Veuillez supprimer les appareils de ce département.",
"departmentBindUser"=>"Veuillez supprimer les utilisateurs de ce département.",
"smartPlusValidLine"=>"Configuration SmartPlus interphonie Invalide dans la ligne %s.",
"identityValidLine"=>"Identité non valide dans la ligne %s.",
"eachDoorCount"=>"Un seul plan pour ouvrir chaque porte une fois",
"textUpgradeMsg1"=>"Veuillez mettre à jour la version de l'application pour continuer.",
"textUpgradeMsg2"=>"échec de la connexion",
"deleteCodeGetLimitTimes"=>"Key non valide. Veuillez réessayer 24 heures plus tard.",
"deleteCodeOverLimitTimes"=>"Veuillez réessayer 24 heures plus tard.",
"deleteCodeError"=>"Clé non valide",
"textUpgradeMsg"=>"1. Optimisé la fonction TEMPKEY .; 2. ajouta la fonction d'annulation du compte .; 3. Fixé certains bogues.",
"paramsError"=>"Erreur de paramètre",
"pmappStatusInvalid"=>"Veuillez d'abord activer l'application PM.",
"delivery_description"=>"Clé de température de livraison",
"webRelayIDInvalidLine"=>"ID de relais Web non valide en ligne% s.",
"relayInvalid"=>"Relais non valide en ligne% s.",
"cancelError"=>"Annuler l'échec.",
"textUpgradeMsgForComRole"=>"Améliorer le rôle de la communauté",
"textUpgradeMsgForPerRole"=>"Améliorer le rôle du personnel",
"textUpgradeMsgForOffRole"=>"Améliorer le rôle de bureau",
"textUpgradeMsgForPMRole"=>"Mettre à niveau le rôle de PM",
"lockApp"=>"Smartplus Lock",
"lock"=>"Serrure",
"versionLogMaxLen"=>"Le journal de la version ne peut pas être plus grand que les caractères de% s",
"autoLock"=>"Verrouillage automatique",
"pinAndRFcardNotNullLines"=>"Au moins une des cartes PIN et RF en ligne% s doit être remplie!",
"pinExistLines"=>"La broche a déjà existé en ligne% s.",
"pinInvalidLines"=>"PIN non valide en ligne% s!",
"pinDuplicatedLines"=>"PIN dupliquée en ligne% s!",
"FaceImportLength"=>"La taille du fichier d'importation de visage ne peut pas être supérieure à% s",
"landlineServerNotActivate"=>"Cette communauté n'a pas activé le service fixe.",
"importFailDisNotExist"=>"Le distributeur n'existe pas",
"importFailNotPermission"=>"Vous n'avez pas l'autorisation d'ajouter cette adresse MAC.",
"importFailTooManyAdd"=>"L'importation a échoué, pour le distributeur unique uniquement.",
"importFailAdded"=>"Cette adresse MAC a déjà été ajoutée par un autre utilisateur.",
"macAssignToLimit"=>"Vous ne pouvez affecter que jusqu'à 10 distributeurs",
"macNumToLimit"=>"Vous ne pouvez télécharger que jusqu'à 1000 adresses MAC à la fois.",
"addOutFloor"=>"Veuillez saisir un nombre entre 1 à 128.",
"floor"=>"Sol",
"PostalCodeInvalid"=>"Veuillez saisir la lettre ou le numéro.",
"onceCodeInvalid"=>"Le code autrefois doit être de 4 à 5 chiffres.",
"permanentCodeInvalid"=>"Le code permanent doit être à 6 chiffres.",
"onceCodeOutNum"=>"Vous ne pouvez ajouter que 10 une fois le code.",
"permanentCodeOutNum"=>"Vous ne pouvez ajouter que 10 code permanent.",
"onceCodeExist"=>"Le code autrefois existe déjà.",
"permanentCodeExist"=>"Le code permanent existe déjà.",
"addOutFloorLine"=>"Numéro de plancher non valide en ligne% s.",
"auditManuallyUnlock"=>"Déverrouiller manuellement",
"auditManuallyLock"=>"Verrouillage manuellement",
"automaticallyUnlock"=>"Déverrouiller automatiquement",
"doorClose"=>"Porte de la porte",
"PostalCodeNotEmpty"=>"Veuillez saisir au moins une lettre ou un numéro.",
"emergencyAlarm"=>"Alarme d'urgence",
"doorSensor"=>"Capteur de porte",
"yaleBatteryWarning"=>"Avertissement de batterie Yale",
"auditCodeManuallyUnlock"=>"Déverrouiller manuellement",
"auditCodeManuallyLock"=>"Verrouillage manuellement",
"2weekBatteryWarning"=>"% S - Temps de batterie estimé restant: 2 semaines.",
"1weekBatteryWarning"=>"% S - Temps de batterie estimé restant: 1 semaine.",
"replaceBatteryWarning"=>"% S - Le niveau de batterie est extrêmement faible, veuillez remplacer immédiatement.",
"open"=>"Ouvrir",
"close"=>"Fermer",
"addContactFavoriteNum"=>"Ajout aux favoris, vous ne pouvez ajouter que jusqu'à 300 appartements préférés.",
"addContactBlockNum"=>"Ajout à BlockList a échoué. Vous ne pouvez ajouter que 100 appartements à BlockList.",
"voiceTitle"=>"Message vocal",
"voiceContent"=>"Vous avez un message vocal de% s",
"voiceMsgInvalid"=>"Le message vocal a expiré.",
"toggleFeaturePlan"=>"Vous ne pouvez pas modifier le plan de fonctionnalités.",
"rtspAddresEmpty"=>"Veuillez saisir l'adresse RTSP.",
"rtspAddresInvalid"=>"Adresse RTSP non valide.",
"rtspPortEmpty"=>"Veuillez saisir le port.",
"rtspPortInvalid"=>"Port non valide.",
"rtspPassWdEmpty"=>"Veuillez entrer le mot de passe.",
"rtspPassWdInvalid"=>"Mot de passe trop long, le mot de passe peut contenir jusqu'à 63 caractères.",
"cameraExist"=>"La caméra existe déjà.",
"errorOnRPS"=>"Échec de la demande de serveur RPS",
"faceImportErrorSystem"=>"Erreur système",
"faceImportErrorView"=>"Pas la vue avant",
"faceImportErrorWearMask"=>"Masque détecté",
"faceImportErrorLowResolution"=>"La résolution est trop faible",
"faceImportErrorWrongFormat"=>"Erreur de format de fichier",
"faceImportErrorNoFace"=>"Aucun visage détecté",
"faceImportErrorFileLarge"=>"Le fichier est trop plus grand",
"faceImportErrorFaceLarge"=>"Le visage est trop plus grand",
"faceImportErrorFaceSmall"=>"Le visage est trop petit",
"faceImportErrorMultiFaces"=>"Plus d'un visage",
"faceImportErrorWrongName"=>"Le nom du fichier est une erreur.",
"faceImportErrorEmptyName"=>"Le nom du résident est vide.",
"faceImportErrorNoAccountInfo"=>"Obtenez l'erreur d'informations PersonalAccount.",
"faceImportErrorAccountInactive"=>"Le PersonalAccount n'est pas actif.",
"changeHomeFeatureInvalid"=>"L'opération a échoué!",
"changeInterComFeatureInvalid"=>"L'opération a échoué!",
"offline"=>"Échec: hors ligne",
"allFloors"=>"Tous les étages",
"uploadOversize"=>"La taille du fichier de téléchargement ne peut pas être supérieure à% s",
"uploadInvalidType"=>"Le type de fichier téléchargé n'est pas pris en charge",
"uploadFailed"=>"Le téléchargement a échoué, veuillez essayer plus tard",
"uploadScreenSaverImgTooMuch"=>"Les images de l'économiseur d'écran ne peuvent pas être plus de% s!",
"screenSaverImgTooLittle"=>"Les images de l'économiseur d'écran ne peuvent pas être inférieures à% s!",
"screenSaverImgTooMuch"=>"Les images de l'économiseur d'écran ne peuvent pas être plus de% s!",
"screenSaverDevicesOffline"=>"Échec de la sauvegarde.",
"saveFailed"=>"Échec de la sauvegarde.",
"importingInProgress"=>"Importation en cours, veuillez réessayer plus tard.",
"importBuildingInvalidLine"=>"Bâtiment non valide en ligne% s",
"importAptInvalidLine"=>"Invalide apt en ligne% s",
"importAccountTypeInvalidLine"=>"Type de compte non valide en ligne% s",
"importFirstNameInvalidLine"=>"Prénom non valide en ligne% s",
"importLastNameInvalidLine"=>"Nom de famille non valide en ligne% s",
"importKeyInvalidLine"=>"Clé non valide en ligne% s",
"importKeyExistsLine"=>"La broche existe en ligne% s",
"importCardInvalidLine"=>"Carte RF non valide en ligne% s",
"importCardExistsLine"=>"La carte RF existe en ligne% s",
"importAccessGroupInvalidLine"=>"ID de groupe d'accès non valide en ligne% s",
"importAccessGroupNoPermissionLine"=>"Aucune autorisation d'accès au groupe d'accès en ligne% s",
"importExceededNumberLine"=>"A dépassé le nombre de membres de la famille en ligne% s",
"importNoActiveMasterLine"=>"L'importation a échoué en% de ligne, veuillez d'abord activer le tapis familial.",
"importMasterExistsLine"=>"La maîtrise de la famille existe déjà en ligne% s.",
"importNoCreateMasterLine"=>"L'importation a échoué en% en ligne, veuillez d'abord créer le tapis familial.",
"PrivateKeysDataExist"=>"La clé privée% existe déjà.",
"PrivateKeyDataExists"=>"La clé privée% s existe déjà.",
"landLineOpenToClosedFail"=>"Échec de la sauvegarde.",
"limitWithIp"=>"Vous essayez trop souvent, veuillez réessayer en 5 minutes (IP:% s)",
"subDistributor"=>"Sous-distributeur",
"faceImportErrorNotClear"=>"L'image importée n'est pas claire.",


  ];
