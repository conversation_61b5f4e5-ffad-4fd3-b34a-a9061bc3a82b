<?php
/*
 * @Description: 服务容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-02 15:34:14
 * @LastEditors  : kxl
 */
namespace container;

include_once __DIR__."/../interfaces/container/main.php";

class service implements \interfaces\container\main\serviceContainer
{
    protected $binds = [];
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    // 创建服务
    public function make($service, array $params=[])
    {
        if (array_key_exists($service, $this->binds)) {
            return $this->binds[$service];
        }
        return $this->binds[$service] = $this->resolve($service, $params);
    }
    // 服务解析
    public function resolve($service, array $params=[])
    {
        include_once __DIR__."/../service/$service.php";
        // TODO 反射机制传参
        $service = "\\service\\$service";
        return new $service();
    }
    // 向某个对象注入服务
    public function addService($class, $service, array $params=[])
    {
        $class->services[$service] = $this->make($service, $params);
    }
}
