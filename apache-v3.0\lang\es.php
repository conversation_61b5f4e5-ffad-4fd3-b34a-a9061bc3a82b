<?php
  const MSGTEXT = [ 

"accountExits"=>"Esta cuenta ya existe",
"accountNotExit"=>"La cuenta no existe",
"accountIncorrect"=>"Usuario o contraseña invalido",
"accountNIncorrect"=>"Cuenta no válida",
"activeEmpty"=>"Se requiere valor activo",
"addFail"=>"Agregar fallido",
"addSuccess"=>"Agregando éxito.",
"addSuccessPw"=>"Añadiendo sucesivo, la contraseña es '%s'.",
"addTmpKeyFail"=>"Agregue la tecla TEMP fallida, inténtelo de nuevo",
"aptDuplicated"=>"Apartamento %s está duplicado",
"aptDigits"=>"El apartamento %s no es válido, que debe estar entre 1 y 6 números de dígitos",
"aptExit"=>"Apartamento %s ya existe",
"abnormal"=>"Anormal",
"activation"=>"Activación",
"additionalApp"=>"Aplicación adicional",
"bindDevice"=>"Elimine todos los dispositivos bajo esta cuenta",
"bindMAClibrary"=>"Eliminar Mac en la biblioteca Mac",
"bindUser"=>"Elimine los usuarios en esta cuenta",
"buildingBindDevice"=>"Eliminar dispositivos bajo este edificio",
"buildingBindUser"=>"Elimine los usuarios bajo este edificio",
"buildingDigits"=>"Edificio no válido %s, que debe estar entre 1 y 2 números de dígitos",
"buildingExit"=>"El edificio ya existe",
"BindingDeviceFailed"=>"Fallado en el dispositivo de enlace, el dispositivo puede estar unido a otro usuario o no ha agregado a Mac Biblioteca",
"chcekMacExits"=>"Agregar fallido, dirección MAC inválida o ya existe",
"changePasswdFail"=>"Falló la contraseña de modificación",
"changePasswdPEmail"=>"Modificación de la contraseña tiene éxito, consulte el correo electrónico %s",
"community"=>"Comunidad",
"deleteFail"=>"Eliminar falló",
"deleteSuccess"=>"Eliminar con éxito",
"deviceTypeEmpty"=>"Se requiere el tipo de dispositivo",
"deviceNotFindUser"=>"Dispositivo no encontrado, comuníquese con su administrador",
"dealSuccess"=>"Establecer éxito",
"doorUnit"=>"Unidad de puertas",
"emailExits"=>"el Email ya existe",
"emailPExit"=>"El correo electrónico %s ya existe",
"emailNotExits"=>"Este correo electrónico no existe.",
"emailDuplicated"=>"El correo electrónico %s está duplicado",
"errorVersion"=>"Versión de error",
"emailOrAccountNotExit"=>"Este correo electrónico no existe.",
"firstNameEmpty"=>"Se requiere el primer nombre",
"failed"=>"fail",
"family"=>"Familia",
"guardPhone"=>"Teléfono de guardia",
"incorrectSipAccount"=>"No más cuenta de SIP disponible",
"incorrectSipAccountGroup"=>"No más grupo SIP disponible",
"importDataSuccess"=>"Importar éxito de datos",
"importFailMACExit"=>"La importación falló, verifique si la Dirección MAC existe o es válida: ¿ r \ n% s",
"invaildDC"=>"Código de dispositivo no válido",
"InvalidFile"=>"Archivo inválido",
"invalidPEmail"=>"Correo electrónico no válido %s",
"invalidPName"=>"Nombre de usuario no válido %s",
"invalidPCalltype"=>"Tipo de llamada no válido %s",
"invalidPPin"=>"Pin no válido %s",
"invalidPActive"=>"Valor activo no válido %s",
"invalidPage"=>"Página no válida",
"invalidPDeviceType"=>"Tipo de dispositivo no válido %s",
"invaildVerCode"=>"código de verificación invalido",
"invalidIdentity"=>"Información de identidad no válida!",
"indoorMonitor"=>"Monitor de interior",
"inactivated"=>"Inactivado",
"normal"=>"Normal",
"expired"=>"Venció",
"lastNameEmpty"=>"Se requiere apellido",
"locationEmpty"=>"Se requiere ubicación",
"locationPLoog"=>"%s ubicación demasiado larga",
"locationLoog"=>"Ubicación demasiado larga",
"loginError"=>"Error de inicio de sesión",
"loginFail"=>"Error de inicio de sesion",
"loginSuccess"=>"Acceso exitoso",
"limitIP"=>"Estás tratando con demasiada frecuencia, intente nuevamente en 5 minutos",
"limitDevice"=>"El número de su dispositivo ha alcanzado el límite máximo",
"MAC2PLibrary"=>"Dirección MAC:%s no es válido, consulte su biblioteca Mac.",
"MAC2Library"=>"Dirección MAC Inválida, consulte su biblioteca Mac",
"macExits"=>"La dirección MAC ya existe",
"MACLength"=>"La longitud de la dirección MAC debe ser de 12 dígitos.",
"modifySuccess"=>"Modificar el éxito",
"modifyFailed"=>"Modificar fallido",
"maxHouse"=>"El número de usuarios ha alcanzado el límite máximo, comuníquese con el administrador",
"modifyAptFail"=>"¡Guardar falló! El APT No. ya existe, debes eliminarlo en primer lugar.",
"nameloog"=>"Nombre de usuario demasiado largo, el nombre de usuario puede contener hasta 64 caracteres",
"nameExit"=>"Nombre de usuario ya existe",
"notPermission"=>"No tienes permiso de operación",
"noSip"=>"No más cuenta de SIP",
"passwordIncorrect"=>"Contraseña incorrecta",
"passwdChangeSuccess"=>"Cambiar contraseña tiene éxito",
"passwordResetSuccess"=>"Restablecer el éxito de la contraseña",
"passwordReset2"=>"La contraseña se ha restablecido a '%s'.",
"payTimeOut"=>"Pagar tiempo",
"payFailed"=>"Pagado fallido",
"processing"=>"Procesando",
"paySuccess"=>"Pago exitoso",
"redirectedOnRPS"=>"Esta dirección MAC se redirige en RPS.",
"registerFailed"=>"Registro fallido",
"registerSuccess"=>"Registro exitoso",
"roomNotExit"=>"¡Este usuario no existe!",
"RFCardExit"=>"Esta tarjeta de RF ya existe",
"registered"=>"register",
"PrivateKeyExists"=>"Esta clave privada ha existido",
"passwordCorrect"=>"Contraseña invalida",
"timeLessCurrent"=>"Tiempo de actualización no válido",
"timeZoneChangeSuccess"=>"Cambiar la zona horaria tiene éxito",
"timeOut"=>"Se acabó el tiempo",
"unbindMACUser"=>"Desvane el %s con el usuario primero",
"unKnowDT"=>"Tipo de dispositivo desconocido",
"userBindUser"=>"Elimine los usuarios bajo esta cuenta primero",
"userNotExit"=>"Este usuario no existe",
"userMaxPLimt"=>"Crear fallado, solo puede agregar a los miembros de la familia %s",
"unregistered"=>"No registrado",
"validMAC"=>"Ingrese una dirección MAC válida",
"versionExit"=>"La versión ya existe",
"versionNameNumberExit"=>"El nombre o número de la versión ya existe",
"sipStatus"=>"La asignación de la cuenta SIP falló, por favor intente nuevamente",
"sentCodeLater"=>"Le hemos enviado un código de verificación, inténtelo de nuevo más tarde",
"setSuccess"=>"Configuración de éxito",
"sendEmailSuccess"=>"Enviar éxito por correo electrónico",
"SetFailed"=>"Fallado en la configuración",
"stairPhone"=>"Teléfono de escalera",
"successed"=>"Tener éxito",
"subscription"=>"Renovar",
"wallPhone"=>"Teléfono de pared",
"emailMaxLen"=>"El correo electrónico debe ser menos de 64 caracteres.",
"serverUpgradeTips"=>"La actualización del servidor está completa, actualice la página.",
"ActiveFamilyAccount"=>"Active primero la cuenta del maestro familiar.",
"weekly"=>"Semanalmente",
"daily"=>"A diario",
"never"=>"Nunca",
"calltypeEmpty"=>"Se requiere el tipo de llamada",
"addOutApt"=>"Solo puedes agregar a %s habitaciones",
"call"=>"Llamar",
"unlock"=>"desbloquear",
"tryUnlockCall"=>"Llamada de desbloqueo falló",
"tryUnlockKey"=>"El código PIN desbloqueado falló",
"tryUnlockCard"=>"El desbloqueo de la tarjeta RF falló",
"tryUnlockFace"=>"Falló el desbloqueo de la cara",
"unlockApp"=>"Desbloqueo de SmartPlus",
"unlockIndoor"=>"Desbloqueo del monitor en interiores",
"unlockNFC"=>"Desbloqueo de NFC",
"unlockBluetooth"=>"Desbloqueo de Bluetooth",
"unlockCard"=>"Desbloqueo de la tarjeta RF",
"unlockPrivateKey"=>"Desbloqueo del código PIN",
"unlockTempKey"=>"Desbloqueo de llave temperatura",
"alarmDoorUnlock"=>"Desbloquear la puerta",
"alarmInfrared"=>"Infrarroja",
"alarmSmoke"=>"Fumar",
"alarmGas"=>"Gas",
"alarmUrgency"=>"Urgencia",
"alarmSOS"=>"LLAMADA DE SOCORRO",
"alarmTamper"=>"Manosear",
"alarmGate"=>"Puerta",
"alarmDoor"=>"Puerta",
"alarmBedroom"=>"Dormitorio",
"alarmGuestRoom"=>"Habitación de huéspedes",
"alarmHall"=>"Sala",
"alarmWindow"=>"Ventana",
"alarmBalcony"=>"Balcón",
"alarmKitchen"=>"Cocina",
"alarmStudy"=>"Estudiar",
"alarmBathroom"=>"Baño",
"alarmArea"=>"Área",
"RFCardExit2"=>"La tarjeta RF %s ya ha existido",
"RFCardDuplicated"=>"La tarjeta RF %s está duplicada",
"notMacBind"=>"El usuario '%s' no tiene permiso para abrir la puerta de conectar con el dispositivo '%s'.",
"accountNumLet"=>"La cuenta debe consistir en números y letras",
"networkUnavailable"=>"Red no disponible.",
"notForModel"=>"No para este modelo.",
"upgradeDevVersion"=>"Actualice primero a la última versión.",
"unavailableService"=>"El servicio no está disponible temporalmente, intente nuevamente más tarde.",
"cantDeletePin"=>"No puedes eliminar el pin %s",
"residentInRoom"=>"Ya hay residente en la sala %s",
"noAnswer"=>"Sin respuesta",
"indoorAndApp"=>"Tanto el monitor en interiores como la aplicación",
"indoorMonitorOnly"=>"Solo monitor en interiores",
"appOnly"=>"Solo la aplicación",
"endThanStart"=>"La hora final no puede ser antes de la hora de inicio.",
"endThanStartFile"=>"Día o tiempo inválido en la línea '%s'.",
"doorRelease"=>"Liberación de la puerta",
"success"=>"Éxito",
"unlockFACE"=>"Desbloqueo de la cara",
"unlockBLE"=>"Desbloqueo de Bluetooth",
"captureSmartPlus"=>"Captura en SmartPlus",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"No se pudo desbloquear",
"deviceDisconnected"=>"El dispositivo fue desconectado.",
"low"=>"Bajo",
"motion"=>"Movimiento",
"capture"=>"Captura",
"failedImport"=>"Importación fallida",
"notValidMobile"=>"%s no es un número móvil válido.",
"mobileExits"=>"El número de móvil ya existe",
"mobileExits2"=>"El número de móvil %s ya ha existido",
"mobileDuplicated"=>"El número de móvil %s está duplicado",
"mobileNumberExist"=>"El número de móvil no existe.",
"codeIncorrect"=>"Codigo invalido",
"sendCodeSuccess"=>"Enviar código de verificación con éxito",
"codeCorrect"=>"Correcta",
"mobileNumberEmpty"=>"Por favor, introduzca su número de móvil.",
"invalidUser"=>"Usuario no válido %s",
"locationExits"=>"La dirección de ubicación ya existe",
"smartPlusIndoor"=>"SmartPlus y monitores interiores",
"phoneIndoor"=>"Teléfono y monitores interiores",
"smartPlusIndoorBackup"=>"SmartPlus y monitores interiores, con teléfono como copia de seguridad",
"smartPlusBackup"=>"Monitores interiores con SmartPlus como copia de seguridad",
"indoorPhoneBackup"=>"Monitores interiores con teléfono como copia de seguridad",
"indoorSmartPlusPhone"=>"Monitores interiores con SmartPlus como copia de seguridad, finalmente el teléfono",
"endUser"=>"Usuario final",
"installer"=>"Instaladora",
"distributor"=>"Distribuidora",
"pm"=>"PM",
"superManage"=>"Supermisión",
"loginManagement"=>"Gestión de inicio de sesión",
"accessControl"=>"Control de acceso",
"userManagement"=>"Gestión de usuarios",
"deviceManagement"=>"Gestión de dispositivos",
"communityManagement"=>"Gestión comunitaria",
"auditLogin"=>"Iniciar sesión: Web",
"auditLogout"=>"Iniciar sesión: Web",
"auditAddTempKey"=>"Agregar clave temporal: {0}",
"auditEditTempKey"=>"Editar clave temporal: {0}",
"auditDeleteTempKey"=>"Eliminar la tecla temporal: {0}",
"auditAddRFCard"=>"Agregar tarjeta RF: {0}",
"auditEditRFCard"=>"Editar tarjeta RF: {0}",
"auditDeleteRFCard"=>"Eliminar tarjeta RF: {0}",
"auditAddDis"=>"Agregar distribuidor: {0}",
"auditEditDis"=>"Editar distribuidor: {0}",
"auditDeleteDis"=>"Eliminar distribuidor: {0}",
"auditAddInstaller"=>"Agregar instalador: {0}",
"auditEditInstaller"=>"Editar instalación: {0}",
"auditDeleteInstaller"=>"Eliminar instalador: {0}",
"auditAddPM"=>"Agregar pm: {0}",
"auditEditPM"=>"Editar PM: {0}",
"auditDeletePM"=>"Eliminar pm: {0}",
"auditAddEndUser"=>"Agregar usuario final: {0}",
"auditEditEndUser"=>"Editar usuario final: {0}",
"auditDeleteEndUser"=>"Eliminar usuario final: {0}",
"auditSetOwnerTime"=>"Establezca su propia zona horaria {0}",
"auditSetOwnPassword"=>"Establecer contraseña propia",
"auditAddPIN"=>"Agregar pin: {0}",
"auditEditPIN"=>"Editar pin: {0}",
"auditDeletePIN"=>"Eliminar pin: {0}",
"auditImportFace"=>"Importar cara: {0}",
"auditDeleteFace"=>"Eliminar la cara: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Establecer tipo de llamada SmartPlus y monitores interiores: {0} \\u0026 {1}",
"auditSetCallTypePhoneIndoor"=>"Establezca el tipo de teléfono y monitores interiores: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Establezca el tipo de llamada SmartPlus y los monitores interiores, con el teléfono como copia de seguridad: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusBackup"=>"Establezca monitores de interiores de tipo de llamada con SmartPlus como copia de seguridad: {0} \\u0026 {1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Establezca el tipo de llamado monitores interiores con el teléfono como copia de seguridad: {0} \\u0026 {1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Establezca monitores interiores de tipo de llamada con SmartPlus como copia de seguridad, finalmente el teléfono: {0} \\u0026 {1}",
"auditDeleteDevice"=>"Eliminar el dispositivo: {0}",
"auditSetAPTCount"=>"Establecer un número de apartamentos {0}",
"auditEnableLandline"=>"Habilitar servicio fijo",
"auditDisableLandline"=>"Deshabilitar el servicio fijo",
"auditSetSubTime"=>"Establecer TimeZone {0}",
"auditSetChargeModeInstall"=>"Establecer el modelo de carga pagado por instalador",
"auditSetChargeModeUser"=>"Establecer el modelo de carga pagado por usuario/PM",
"auditSetConnectTypeDefault"=>"Establecer Tipo de conexión predeterminado",
"auditSetConnectTypeTCP"=>"Establecer el tipo de conexión TCP",
"auditSetConnectTypeUDP"=>"Establecer el tipo de conexión UDP",
"auditSetConnectTypeTLS"=>"Establecer el tipo de conexión TLS",
"auditAddCommunity"=>"Agregar comunidad: {0}",
"auditDeleteCommunity"=>"Eliminar comunidad: {0}",
"auditImportCommunity"=>"Comunidad de importación: {0}",
"auditSetAPTNumber"=>"Establecer {0} número de habitación {1}",
"auditSetEmail"=>"Establecer correo electrónico {0}: {1}",
"auditSetMobile"=>"Establecer el número de teléfono {0}: {1}",
"auditDeviceTypeStair"=>"Establecer Tipo de puerta múltiple Tenants: {0}",
"auditDeviceTypeDoor"=>"Establecer Tipo de puerta de enlace único: {0}",
"auditDeviceTypeIndoor"=>"Establecer el monitor de interior de tipo: {0}",
"auditDeviceTypeGuardPhone"=>"Establecer Tipo Guard Phone: {0}",
"auditDeviceTypeAccessControl"=>"Control de acceso de tipo establecido: {0}",
"auditSetNetGroup"=>"Establecer el grupo de red {0}: {1}",
"auditEditCommunity"=>"Editar comunidad",
"deliveryMsg"=>"Tiene un %S de artículos que se le han entregado, por favor verifique la hora.",
"deliveryTitle"=>"¡Tienes un nuevo paquete!",
"rfcardDuplicatedLines"=>"Número de tarjeta RFID duplicada en línea %s!",
"rfcardNameInvalid"=>"Nombre de tarjeta de RF inválido en línea %s!",
"rfcardExistLines"=>"Las tarjetas de RF ya han existido en la línea %s.",
"importFailMacExistLines"=>"La dirección MAC existe o válida en línea %s.",
"exportExcelCountNull"=>"¡No hay registro para exportar en la fecha!",
"keyIsEqualRoom"=>"¡La clave de entrega no puede ser idéntica con el número APT!",
"visitor"=>"visitante",
"CommunityNameExist"=>"El nombre de la comunidad ya existe",
"unlockGuardPhone"=>"Desbloqueo del teléfono de guardia",
"auditLoginApp"=>"Iniciar sesión: aplicación",
"auditLogoutApp"=>"Iniciar sesión: aplicación",
"timeForYesterday"=>"Yesterday",
"exportExcelDataBefore"=>"¡Datos demasiado grandes!",
"tempkeyUsed"=>"Tempkey usado",
"tempkeyContent"=>"%s ha utilizado el TempKey.",
"accessNameExist"=>"El nombre del grupo de acceso ya existe",
"addFaceFail"=>"Importa una foto clara de la cara.",
"userInvalid"=>"Usuario no válido en línea %s.",
"groupsInvalid"=>"Grupo de acceso no válido en línea %s.",
"BuildAccessName"=>"Edificio residente %s",
"auditCodeLogEditApt"=>"Editar apartamento: {0}",
"invalidTimeInLine"=>"Tiempo no válido en línea %s.",
"cancel"=>"Cancelar",
"cancelSuccess"=>"Cancelar sucesivo.",
"payOutstanding"=>"Veuillez vérifier s'il y a des commandes impayées, sinon, veuillez contacter votre fournisseur de services",
"featureDeleteError"=>"El plan de características estará atado.",
"beyondFamilyMember"=>"No puede crear más cuentas para miembros de la familia, comuníquese con su proveedor de servicios para crearlo.",
"indoorMonitorRequired"=>"Se requiere al menos un monitor interior para cada apartamento.",
"featureActivationFee"=>"Característica (tarifa única)",
"systemProcessing"=>"Procesamiento del sistema",
"featureMonthlyFee"=>"Característica (tarifa mensual)",
"featurePriceDifferences"=>"Característica (diferencias de precios)",
"updatingSuccess"=>"¡Actualización tuvo éxito!",
"featureNameBasic"=>"Chemistry",
"featureNamePremium"=>"De primera calidad",
"indoorMacNotCorrect"=>"Ingrese el monitor de interior correcto Mac.",
"off"=>"Apagada",
"enterValidAccount"=>"Ingrese una cuenta válida",
"invalidKitImportMAC"=>"Compruebe si el Mac existe o válido: %s",
"importLessData"=>"Importe menos de %de datos de S.",
"invalidQRCode"=>"Identificación fallida, escanee un código QR correcto.",
"cannotCreateFamilyMember"=>"No puede crear más cuentas para miembros de la familia.",
"importProcessing"=>"Importando, intente nuevamente más tarde",
"departmentAccessName"=>"Grupo de acceso %s",
"idExistsLine"=>"ID ya existe en la línea %s",
"enterFirstNameLine"=>"Ingrese el primer nombre en línea %s",
"enterLastNameLine"=>"Ingrese el apellido en línea %s",
"departmentExist"=>"Departamento ya existe",
"idExist"=>"ID ya existe",
"layoutIdInvalid"=>"El diseño no es válido",
"unlockAppHome"=>"Eyes desbloquear",
"officeNameExist"=>"El nombre de la oficina ya existe",
"departmentExit"=>"El departamento ya existe.",
"importOutTask"=>"Solo puede importar una plantilla a la vez.",
"idDuplicated"=>"ID %s está duplicado",
"aptInvalidLine"=>"Apto inválido en línea %s.",
"buildInvalidLine"=>"Edificio no válido en línea %s.",
"departmentInvalidLine"=>"Departamento no válido en línea %s.",
"idInvalidLine"=>"ID no válida en línea %s.",
"propertyManager"=>"Administrador de la propiedad",
"departmentBindDevice"=>"Elimine dispositivos bajo este departamento.",
"departmentBindUser"=>"Elimine los usuarios de este departamento.",
"smartPlusValidLine"=>"La función de intercomunicador SmartPlus inválida en línea %s.",
"identityValidLine"=>"Identidad no válida en la línea %s.",
"eachDoorCount"=>"Un solo plan para abrir cada puerta una vez",
"textUpgradeMsg1"=>"Actualice la versión de la aplicación para continuar.",
"textUpgradeMsg2"=>"error de inicio de sesion",
"deleteCodeGetLimitTimes"=>"Clave no válida. Inténtelo de nuevo 24 horas después.",
"deleteCodeOverLimitTimes"=>"Inténtelo de nuevo 24 horas después.",
"deleteCodeError"=>"Tecla inválida",
"textUpgradeMsg"=>"1. Optimizó la función TempKey.",
"paramsError"=>"error de parametro",
"pmappStatusInvalid"=>"Actúe primero la aplicación PM.",
"delivery_description"=>"Temperadora de entrega",
"webRelayIDInvalidLine"=>"ID de retransmisión web no válida en línea %s.",
"relayInvalid"=>"Relé inválido en línea %s.",
"cancelError"=>"Cancelar fallido.",
"textUpgradeMsgForComRole"=>"actualizar el rol de la comunidad",
"textUpgradeMsgForPerRole"=>"actualizar el rol de personal",
"textUpgradeMsgForOffRole"=>"Actualizar el rol de la oficina",
"textUpgradeMsgForPMRole"=>"Actualizar el rol de PM",
"lockApp"=>"Bloqueo smartplus",
"lock"=>"Cerrar",
"versionLogMaxLen"=>"El registro de la versión no puede ser mayor que %s de caracteres",
"autoLock"=>"Bloqueo automático",
"pinAndRFcardNotNullLines"=>"¡Al menos uno de los PIN y la tarjeta RF en la línea %s debe completarse!",
"pinExistLines"=>"Los pin ya han existido en la línea %s.",
"pinInvalidLines"=>"Pin inválido en línea %s!",
"pinDuplicatedLines"=>"Pin duplicado en línea %s!",
"FaceImportLength"=>"El tamaño del archivo de importación facial no puede ser mayor que %s",
"landlineServerNotActivate"=>"Esta comunidad no ha activado el servicio fijo.",
"importFailDisNotExist"=>"El distribuidor no existe",
"importFailNotPermission"=>"No tiene el permiso para agregar esta dirección MAC.",
"importFailTooManyAdd"=>"La importación falló, solo para distribuidor único.",
"importFailAdded"=>"Esta dirección MAC ya ha sido agregada por otro usuario.",
"macAssignToLimit"=>"Solo puede asignar hasta 10 distribuidores",
"macNumToLimit"=>"Solo puede cargar hasta 1000 direcciones MAC a la vez.",
"addOutFloor"=>"Ingrese un número entre 1 ~ 128.",
"floor"=>"Piso",
"PostalCodeInvalid"=>"Ingrese la carta o el número.",
"onceCodeInvalid"=>"El código una vez debe ser de 4-5 dígitos.",
"permanentCodeInvalid"=>"El código permanente debe ser de 6 dígitos.",
"onceCodeOutNum"=>"Solo puede agregar hasta 10 una vez en el código.",
"permanentCodeOutNum"=>"Solo puede agregar hasta 10 código permanente.",
"onceCodeExist"=>"El código una vez ya existe.",
"permanentCodeExist"=>"El código permanente ya existe.",
"addOutFloorLine"=>"Número de piso no válido en línea %s.",
"auditManuallyUnlock"=>"Desbloquear manualmente",
"auditManuallyLock"=>"Bloquear manualmente",
"automaticallyUnlock"=>"Desbloquear automáticamente",
"doorClose"=>"Puerta cerrada",
"PostalCodeNotEmpty"=>"Ingrese al menos una letra o número.",
"emergencyAlarm"=>"Alarma de emergencia",
"doorSensor"=>"Sensor de puertas",
"yaleBatteryWarning"=>"Advertencia de batería de Yale",
"auditCodeManuallyUnlock"=>"Desbloquear manualmente",
"auditCodeManuallyLock"=>"Bloquear manualmente",
"2weekBatteryWarning"=>"%S - Tiempo de batería estimado restante: 2 semanas.",
"1weekBatteryWarning"=>"%S - Tiempo de batería estimado restante: 1 semana.",
"replaceBatteryWarning"=>"%S - El nivel de la batería es extremadamente bajo, reemplace de inmediato.",
"open"=>"Abierto",
"close"=>"Cerca",
"addContactFavoriteNum"=>"Además, falló los favoritos. Solo puede sumar hasta 300 apartamentos favoritos.",
"addContactBlockNum"=>"Agregando a Blocklist falló. Solo puede sumar hasta 100 apartamentos para Blocklist.",
"voiceTitle"=>"Mensaje de voz",
"voiceContent"=>"Tienes un mensaje de voz de %s",
"voiceMsgInvalid"=>"El mensaje de voz ha expirado.",
"toggleFeaturePlan"=>"No puedes cambiar el plan de características.",
"rtspAddresEmpty"=>"Ingrese la dirección RTSP.",
"rtspAddresInvalid"=>"Dirección RTSP no válida.",
"rtspPortEmpty"=>"Ingrese el puerto.",
"rtspPortInvalid"=>"Puerto no válido.",
"rtspPassWdEmpty"=>"Por favor, ingrese contraseña.",
"rtspPassWdInvalid"=>"Contraseña demasiado larga, la contraseña puede contener hasta 63 caracteres.",
"cameraExist"=>"La cámara ya existe.",
"errorOnRPS"=>"No se pudo solicitar el servidor RPS",
"faceImportErrorSystem"=>"Error del sistema",
"faceImportErrorView"=>"No vista frontal",
"faceImportErrorWearMask"=>"Máscara detectada",
"faceImportErrorLowResolution"=>"La resolución es demasiado baja",
"faceImportErrorWrongFormat"=>"Error de formato de archivo",
"faceImportErrorNoFace"=>"Sin cara detectada",
"faceImportErrorFileLarge"=>"El archivo es demasiado mayor",
"faceImportErrorFaceLarge"=>"La cara es demasiado más grande",
"faceImportErrorFaceSmall"=>"La cara es demasiado pequeña",
"faceImportErrorMultiFaces"=>"Más de una cara",
"faceImportErrorWrongName"=>"El nombre del archivo es error.",
"faceImportErrorEmptyName"=>"El nombre del residente está vacío.",
"faceImportErrorNoAccountInfo"=>"Obtenga un error de información personal.",
"faceImportErrorAccountInactive"=>"El personal de personal no está activo.",
"changeHomeFeatureInvalid"=>"¡Operación fallida!",
"changeInterComFeatureInvalid"=>"¡Operación fallida!",
"offline"=>"Fallido: fuera de línea",
"allFloors"=>"Todos los pisos",
"uploadOversize"=>"El tamaño del archivo de carga no puede ser mayor que %s",
"uploadInvalidType"=>"El tipo de archivo cargado no es compatible",
"uploadFailed"=>"Subir fallado, intente más tarde",
"uploadScreenSaverImgTooMuch"=>"¡Las imágenes de pantalla de pantalla de pantalla de pantalla no pueden ser más del %s!",
"screenSaverImgTooLittle"=>"¡Las imágenes de pantalla de pantalla de pantalla no pueden ser menos del %s!",
"screenSaverImgTooMuch"=>"¡Las imágenes de pantalla de pantalla de pantalla de pantalla no pueden ser más del %s!",
"screenSaverDevicesOffline"=>"Error al guardar.",
"saveFailed"=>"Error al guardar.",
"importingInProgress"=>"Importando en progreso, intente nuevamente más tarde.",
"importBuildingInvalidLine"=>"Edificio no válido en línea %s",
"importAptInvalidLine"=>"Apto inválido en línea %s",
"importAccountTypeInvalidLine"=>"Tipo de cuenta inválido en línea %s",
"importFirstNameInvalidLine"=>"Nombre inválido en línea %s",
"importLastNameInvalidLine"=>"Apellido no válido en línea %s",
"importKeyInvalidLine"=>"Clave no válida en línea %s",
"importKeyExistsLine"=>"El pin existe en la línea %s",
"importCardInvalidLine"=>"Tarjeta de RF no válida en línea %s",
"importCardExistsLine"=>"La tarjeta RF existe en la línea %s",
"importAccessGroupInvalidLine"=>"ID de grupo de acceso no válido en línea %s",
"importAccessGroupNoPermissionLine"=>"Sin permiso de acceso a la identificación del grupo en línea %s",
"importExceededNumberLine"=>"Excedió el número de miembros de la familia en la línea %s",
"importNoActiveMasterLine"=>"Importar fallado en la línea %s, active primero el Matser familiar.",
"importMasterExistsLine"=>"Family Master ya existe en la línea %s.",
"importNoCreateMasterLine"=>"Importar fallado en la línea %s, crea primero el Matser familiar.",
"PrivateKeysDataExist"=>"La clave privada %s ya existe.",
"PrivateKeyDataExists"=>"La clave privada %s ya existe.",
"landLineOpenToClosedFail"=>"Error al guardar.",
"limitWithIp"=>"Lo intentas demasiado a menudo, por favor intenta de nuevo en 5 minutos. (ip:% s)",
"subDistributor"=>"Sub distribuidor",
"faceImportErrorNotClear"=>"La imagen importada no está clara.",


  ];
