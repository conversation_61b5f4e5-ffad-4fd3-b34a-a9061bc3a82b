<?php
/*
 * @Description: 根据管理员设置代理为主账户
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
class CSetMngToMainActiveAlias implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp,$cMessage;
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        
        $userAliasId = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setMainToPCMngAlias#");
        $data = $db->querySList("select ID,Account from PersonalAccount where ParentID=:ParentID and ID=:ID and Active=1 and Role in(".PERENDMROLE.",".COMENDMROLE.")",[":ParentID"=>$userAliasId,":ID"=>$id]);
        if(count($data) == 0) $cMessage->echoErrorMsg(StateActiveFamilyAccount);
        $data = $data[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}