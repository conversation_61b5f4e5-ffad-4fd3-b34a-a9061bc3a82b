<?php
/**
 * @description
 * <AUTHOR>
 * @date 2022-03-30 16:00:20
 * @version V6.4
 * @lastEditor cj
 * @lastEditTime 2023-07-13 10:08:19
 * @lastVersion V6.6.0
 */

namespace package\common\model\order\method;

use package\common\model\order\config\Code;

trait Check
{
    /*
     *@description 支付来源是否是终端用户付费
     *<AUTHOR> 2022-03-30 16:05:02 V6.4
     *@lastEditor kxl 2022-03-30 16:05:02 V6.4
     *@param {*} PayerType
     *@return boolean
     */
    public function checkIsPayerTypeEndUser()
    {
        $params = ['PayerType'];
        list($payerType) = $this->getParams($params);
        return $payerType === 0;
    }

    /*
     *@description 支付来源是否是PM付费
     *<AUTHOR> 2022-03-30 16:05:02 V6.4
     *@lastEditor kxl 2022-03-30 16:05:02 V6.4
     *@param {*} PayerType
     *@return boolean
     */
    public function checkIsPayerTypePM()
    {
        $params = ['PayerType'];
        list($payerType) = $this->getParams($params);
        return $payerType === 1;
    }

    /*
     *@description 支付来源是否是Ins付费,包含Ins使用社区或者办公等身份
     *<AUTHOR> 2022-03-30 16:05:02 V6.4
     *@lastEditor kxl 2022-03-30 16:05:02 V6.4
     *@param {*} PayerType
     *@return boolean
     */
    public function checkIsPayerTypeIns()
    {
        $params = ['PayerType'];
        list($payerType) = $this->getParams($params);
        return $payerType === 2;
    }

    /*
     *@description 支付来源是否是Dis付费
     *<AUTHOR> 2022-03-30 16:05:02 V6.4
     *@lastEditor kxl 2022-03-30 16:05:02 V6.4
     *@param {*} PayerType
     *@return boolean
     */
    public function checkIsPayerTypeDis()
    {
        $params = ['PayerType'];
        list($payerType) = $this->getParams($params);
        return $payerType === 3;
    }

    /*
     *@description 支付来源是否是sub Dis付费
     *<AUTHOR> 2023-03-27 13:40:12 V6.5.4
     *@lastEditor cj 2022-03-30 13:40:12 V6.5.4
     *@param {*} PayerType
     *@return boolean
     */
    public function checkIsPayerTypeSubDis()
    {
        $params = ['PayerType'];
        list($payerType) = $this->getParams($params);
        return $payerType === 4;
    }

    /*
     *@description 检测支付权限，包括chargeMode的权限
     *<AUTHOR> 2022-03-31 14:46:06 V6.4
     *@lastEditor kxl 2022-03-31 14:46:06 V6.4
     *@param {*} Users getSubscribeUserInfo的处理结果
     *@param {*} PayerId 购买人的ID
     *@param {*} PayType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员4:sub dis
     *@param {*} InstallerId installer的Id，不是社区或者office
     *@return projectIds
     */
    public function checkPayPermission()
    {
        $params = ['Users', 'PayerId', 'PayType'];
        list($users, $payerId, $payerType) = $this->getParams($params);

        $this->log->debug(
            'params:payerId={payerId};payerType={payerType};users={users}',
            ['payerId' => $payerId, 'payerType' => $payerType,'users' => $users]
        );

        $projectIds = [];
        
        $userIds = $this->share->util->assocArrayToArray($users['all'], 'ID');
        if ($this->callSelfFunc('checkIsPayerTypeEndUser', [$payerType])) {
            // V6.4 由于V5.3 IOS内购原因 目前终端用户暂不支持付费
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_END_USER_PAYER]);

            $this->loadUtil('account');
            $projectId = $this->utils->account->getUserInfo($payerId)[0]['ParentID'];
            $this->callSelfFunc('checkPMOrUserChargeMode', [$projectId]);
            // 终端用户只能给自己或者从账号付费,从账号也一样
            // 验证$users从账户是否在主账户下，或者是否是主账户自己，从账户过滤完只能是空数组
            $subUsers = array_merge(array_diff($userIds, [$payerId]));
            if (count($subUsers) !== 0) {
                $this->loadUtil('account');
                if ($this->utils->account->subUserInMainCheck(implode(";", $subUsers), $payerId)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUB_USER_IN_MAIN_CHECK]);
                }
            }
            $projectIds = [$projectId];
        }

        $mains = $users['main'];
        $subs = $users['sub'];
        $pms = $users['pm'];
        $all = $users['all'];

        if ($this->callSelfFunc('checkIsPayerTypePM', [$payerType])) {
            // PM不能跨项目付费,验证付费的用户不存在跨项目
            if (count($mains) !== 0) {
                $projectId = $mains[0]['ProjectId'];
            } elseif (count($subs) !== 0) {
                $projectId = $subs[0]['ProjectId'];
            } elseif (count($pms) !== 0) {
                $projectId = $pms[0]['ProjectId'];
            }
            // 检查chargeMode，需要分别是pay by pm和dis的pay by dis关闭时生效
            $this->callSelfFunc('checkPMOrUserChargeMode', [$projectId]);
            $this->callSelfFunc('checkSubscribeUserInProjects', [$users, [$projectId]]);
            $projectIds = [$projectId];
            $this->loadUtil('account');
            $this->utils->account->checkPmManageProjects($payerId, $projectIds);
        }

        if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])
        || $this->callSelfFunc('checkIsPayerTypeDis', [$payerType])
        || $this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
            
            // sub dis需检测有没有支付权限
            if ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                $this->callSelfFunc('checkSubDisChargeMode', [$payerId]);
            }
            // 收集项目Id,因为项目数量不会太多，所以可以循环检查
            $projectIds = [];
            $payerData = $this->dao->account->selectByID($payerId, 'UUID,ManageGroup')[0];
            foreach ($all as $value) {
                $projectId = $value['ProjectId'];
                if (in_array($projectId, $projectIds)) {
                    continue;
                } else {
                    // 需检测项目是否在对应管理下
                    $this->loadUtil('account');
                    if ($this->callSelfFunc('checkIsPayerTypeDis', [$payerType])) {
                        $this->utils->account->checkProjectInDis($projectId, $payerId);
                    } elseif ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                        $this->utils->account->checkProjectInSubDis($projectId, $payerData['UUID']);
                    } else {
                        $this->utils->account->checkProjectInIns($projectId, $payerData['ManageGroup']);
                    }
                }
                // ins检查chargeMode，dis允许支付
                if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                    $this->callSelfFunc('checkInsChargeMode', [$projectId]);
                }
                array_push($projectIds, $projectId);
            }
            
            // ins, dis付费的时候，检测是否处于允许的项目下
            $this->callSelfFunc('checkSubscribeUserInProjects', [$users, $projectIds]);
        }

        if (count($projectIds) === 0) {
            throw new \Exception('projectIds con not be a empty array');
        }
        return $projectIds;
    }

    /**
     * @description: 校验视频存储权限
     * @author: shoubin.chen 2024/12/2 17:16:48 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/2 17:16:48  V7.1.0
     */
    public function checkPayPermissionByVideoStorage()
    {
        $params = ['VideoStorageInfo', 'PayerId', 'PayType'];
        list($videoStorageInfo, $payerId, $payerType) = $this->getParams($params);

        $this->log->debug(
            'params:payerId={payerId};payerType={payerType};users={users}',
            ['payerId' => $payerId, 'payerType' => $payerType,'users' => $videoStorageInfo]
        );

        if ($this->callSelfFunc('checkIsPayerTypeEndUser', [$payerType])) {
            // V6.4 由于V5.3 IOS内购原因 目前终端用户暂不支持付费
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_END_USER_PAYER]);
        }

        $projectIds = $videoStorageInfo['projectIds'];
        if ($this->callSelfFunc('checkIsPayerTypePM', [$payerType])) {
            // PM不能跨项目付费,验证付费的用户不存在跨项目
            if (count($projectIds) > 1) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PM_NOT_PAY_DIFF_COMMUNITY]);
            }
            $projectId = $projectIds[0];
            // 检查chargeMode，需要分别是pay by pm和dis的pay by dis关闭时生效
            $this->callSelfFunc('checkPMOrUserChargeMode', [$projectId]);

            $projectIds = [$projectId];
            $this->loadUtil('account');
            $this->utils->account->checkPmManageProjects($payerId, $projectIds);
        }

        if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])
            || $this->callSelfFunc('checkIsPayerTypeDis', [$payerType])
            || $this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {

            // sub dis需检测有没有支付权限
            if ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                $this->callSelfFunc('checkSubDisChargeMode', [$payerId]);
            }
            // 收集项目Id,因为项目数量不会太多，所以可以循环检查
            $projectIds = [];
            $payerData = $this->dao->account->selectByID($payerId, 'UUID,ManageGroup')[0];
            $videoSite = $videoStorageInfo['site'];
            foreach ($videoSite as $site) {
                $projectId = $site['ProjectID'];
                if (in_array($projectId, $projectIds)) {
                    continue;
                } else {
                    // 需检测项目是否在对应管理下
                    $this->loadUtil('account');
                    if ($this->callSelfFunc('checkIsPayerTypeDis', [$payerType])) {
                        $this->utils->account->checkProjectInDis($projectId, $payerId);
                    } elseif ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                        $this->utils->account->checkProjectInSubDis($projectId, $payerData['UUID']);
                    } else {
                        $this->utils->account->checkProjectInIns($projectId, $payerData['ManageGroup']);
                    }
                }
                // ins检查chargeMode，dis允许支付
                if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                    $this->callSelfFunc('checkInsChargeMode', [$projectId]);
                }
                array_push($projectIds, $projectId);
            }
        }

        if (count($projectIds) === 0) {
            throw new \Exception('projectIds con not be a empty array');
        }
        return $projectIds;
    }

    /**
     * @description: 校验三方锁付费权限
     * @return array|mixed
     * @throws \Exception
     * @author: csc 2025/2/6 15:43 V7.1.0
     * @lastEditors: csc 2025/2/6 15:43 V7.1.0
     */
    public function checkPayPermissionByThirdLock()
    {
        $params = ['ThirdLockInfo', 'PayerId', 'PayType'];
        list($thirdLockInfo, $payerId, $payerType) = $this->getParams($params);

        $this->log->debug(
            'params:payerId={payerId};payerType={payerType};users={users}',
            ['payerId' => $payerId, 'payerType' => $payerType,'thirdLockInfo' => $thirdLockInfo]
        );

        if ($this->callSelfFunc('checkIsPayerTypeEndUser', [$payerType])) {
            // V6.4 由于V5.3 IOS内购原因 目前终端用户暂不支持付费
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_END_USER_PAYER]);
        }

        $projectIds = $thirdLockInfo['projectIds'];
        //todo 711chargeMode需要修改
        if ($this->callSelfFunc('checkIsPayerTypePM', [$payerType])) {
            // PM不能跨项目付费,验证付费的用户不存在跨项目
            if (count($projectIds) > 1) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PM_NOT_PAY_DIFF_COMMUNITY]);
            }
            $projectId = $projectIds[0];
            // 检查chargeMode，需要分别是pay by pm和dis的pay by dis关闭时生效
            $this->callSelfFunc('checkPMOrUserChargeMode', [$projectId]);

            $projectIds = [$projectId];
            $this->loadUtil('account');
            $this->utils->account->checkPmManageProjects($payerId, $projectIds);
        }

        if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])
            || $this->callSelfFunc('checkIsPayerTypeDis', [$payerType])
            || $this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {

            // sub dis需检测有没有支付权限
            if ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                $this->callSelfFunc('checkSubDisChargeMode', [$payerId]);
            }
            // 收集项目Id,因为项目数量不会太多，所以可以循环检查
            $projectIds = [];
            $payerData = $this->dao->account->selectByID($payerId, 'UUID,ManageGroup')[0];
            $lockSite = $thirdLockInfo['site'];
            foreach ($lockSite as $site) {
                $projectId = $site['ProjectID'];
                if (in_array($projectId, $projectIds)) {
                    continue;
                } else {
                    // 需检测项目是否在对应管理下
                    $this->loadUtil('account');
                    if ($this->callSelfFunc('checkIsPayerTypeDis', [$payerType])) {
                        $this->utils->account->checkProjectInDis($projectId, $payerId);
                    } elseif ($this->callSelfFunc('checkIsPayerTypeSubDis', [$payerType])) {
                        $this->utils->account->checkProjectInSubDis($projectId, $payerData['UUID']);
                    } else {
                        $this->utils->account->checkProjectInIns($projectId, $payerData['ManageGroup']);
                    }
                }
                // ins检查chargeMode，dis允许支付
                if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                    $this->callSelfFunc('checkInsChargeMode', [$projectId]);
                }
                array_push($projectIds, $projectId);
            }
        }

        if (count($projectIds) === 0) {
            throw new \Exception('projectIds con not be a empty array');
        }
        return $projectIds;
    }

    /*
     *@description 检查付费用户是否都处在允许的项目中
     *<AUTHOR> 2022-03-31 14:15:40 V6.4
     *@lastEditor kxl 2022-03-31 14:15:40 V6.4
     *@param {*} Users getSubscribeUserInfo的处理结果
     *@param {array<string>} ProjectIds 项目id数组
     *@return void
     */
    public function checkSubscribeUserInProjects()
    {
        $params = ['Users', 'ProjectIds'];
        list($users, $projectIds) = $this->getParams($params);

        $this->log->debug(
            'params:projectIds={projectIds};users={users}',
            ['projectIds'=>$projectIds,'users'=>$users]
        );

        $mains = $users['main'];
        $subs = $users['sub'];
        $pms = $users['pm'];
        
        foreach ($mains as $main) {
            if (!in_array($main['ProjectId'], $projectIds)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_MAIN_USER_NOT_BELONG_PROJECT]);
            }
        }

        foreach ($subs as $sub) {
            if (!in_array($sub['ProjectId'], $projectIds)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUB_USER_NOT_BELONG_PROJECT]);
            }
        }

        foreach ($pms as $pm) {
            if (!in_array($pm['ProjectId'], $projectIds)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PM_USER_NOT_BELONG_PROJECT]);
            }
        }
    }

    /*
     *@description 检查终端用户或者PM的付费模式权限
     *<AUTHOR> 2022-03-31 15:28:03 V6.4
     *@lastEditor kxl 2022-03-31 15:28:03 V6.4
     *@param {*} PROXY_ROLE['projectId']
     *@return void
     */
    public function checkPMOrUserChargeMode()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        
        $this->checkChargeMode($projectId, 'pm');
    }

    public function checkInsChargeMode()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->checkChargeMode($projectId);
    }

    public function checkSubDisChargeMode()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->checkChargeMode($projectId, 'subDis');
    }

    /*
     *@description
     *<AUTHOR> 2022-03-31 15:33:59 V6.4
     *@lastEditor kxl 2022-03-31 15:33:59 V6.4
     *@param {string} projectId
     *@param {string} type检测类型，installer | pm |sub dis
     *@return void
     */
    private function checkChargeMode($projectId, $type = 'installer')
    {
        $this->loadUtil('account');
        $projectData = $this->utils->account->getManagerInfo($projectId);
        $projectChargeMode = $projectData['ChargeMode'];
        $projectChargeMode = intval($projectChargeMode);

        $insId = $projectData['ManageGroup'];
        $insChargeMode = $this->utils->account->getManagerInfo($insId)['PayType'];
        $insChargeMode = intval($insChargeMode);

        $hasPermission = false;

        if ($type === 'pm'){
            if ($projectChargeMode === $insChargeMode && $projectChargeMode == CHARGEMODE_NORMAL){
                $hasPermission = true;
            }
        }else if ($type === 'subDis'){
            $hasPermission = $projectChargeMode === CHARGEMODE_NORMAL;
        }else{ // ins
            $hasPermission = $insChargeMode === CHARGEMODE_NORMAL;
        }
        if (!$hasPermission) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_CHARGE_MODE_INVALID]);
        }
    }

    public function checkProjectInSameTimeZone()
    {
        $params = ['projectIds'];
        list($projectIds) = $this->getParams($params);
        $this->loadUtil('account', true);
        $data = $this->utils->_common->account->accountSelectByArray([['ID', $projectIds]], 'TimeZone');

        // 只比较时区部分，不比较地点
        $timeZone = explode(' ', $data[0]['TimeZone'])[0];

        foreach ($data as $value) {
            if (explode(' ', $value['TimeZone'])[0] !== $timeZone) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_SAME_TIME_ZONE]);
            }
        }
    }

    
    /*
     *@description 检测Manage对订单Id是否有检测权限
     *<AUTHOR> 2023-03-27 17:39:36 V6.5.4
     *@lastEditor cj 2023-03-27 17:39:36 V6.5.4
     *@param {*} OrderId
     *@param {*} ManageId dis/subDis ID
     *@return bool
     */
    public function checkOrderIdInManage()
    {
        $params = ['OrderId', 'ManageId'];
        list($orderId, $manageId) = $this->getParams($params);
        $accountId = $this->dao->orderList->selectByID($orderId, 'AccountID')[0]['AccountID'];
        $this->loadUtil('account', true);
        $manageData = $this->utils->_common->account->getManagerInfo($manageId);
        if (in_array($manageData['Grade'], [COMMUNITYGRADE, OFFICEGRADE, PERSONGRADE])) {
            $accountManageGroup = $this->utils->_common->account->getManagerInfo($accountId)['ManageGroup'];
            return $manageData['ManageGroup'] === $accountManageGroup;
        }
        return $accountId === $manageId;
    }

    /*
     *@description 检测订单是否正常
     *<AUTHOR> 2023-12-29 15:24:30 V6.7.0
     *@lastEditor cj 2023-12-29 15:24:30 V6.7.0
     *@param {*} OrderNumber 计费系统订单号
     *@return ['type' => $type, 'number' => $orderData['OrderNumber']]
     */
    public function checkOrderNormal()
    {
        $params = ['OrderNumber'];
        list($bmOrderNumber) = $this->getParams($params);
        $orderData = $this->dao->orderList->selectByArray([['BmOrderNumber', $bmOrderNumber], ['Status', 0]], 'OrderNumber,ID,MixType,InstallID')[0];
        $orderId = $orderData['ID'];
        $mixType = $orderData['MixType'];

        $mixTypeArr = $this->share->util->getBitPositions($mixType);
        foreach ($mixTypeArr as $type){
            // 1=激活;2=家庭续费;3=额外app购买费用,4=个人落地费用;5=高级功能一次性付费;6=高级功能月费;7=>高级功能差价;8=按日期续费,9=自动续费（在这不会有这种情况），10单住户视频存储，11社区视频存储
            // 14位=单住户三方锁激活,15位=社区三方锁激活,16位=单住户三方锁续费,17位=社区三方锁续费
            $active = ($type == 2 || $type == 4 || $type == 8) ? 1 : 0; // 续费的前提是账号已经激活

            // 2021-10-12#zyc#6.2#5,6,7为高级付费功能,判断社区是否存在
            $this->loadUtil('account', true);
            if ($type == 5 || $type == 6 || $type == 7) {
                $accountCount = $this->utils->_common->account->accountSelectByKey('ID', $orderData['InstallID'], 'count(*)')[0]['count(*)'];
                // 无效的订单
                if (intval($accountCount) === 0) {
                    $this->log->payLog("#priceCheck# pay failed because user that id=".$orderData["InstallID"]." is not exits.");
                    $this->dao->orderList->update(['ID'=>$orderId, 'Status'=>4]);
                    $this->db->commit();
                    $this->output->echoErrorMsg(STATE_PAY_FAILED);
                }
            } elseif ($type == 10) {
                // 单住户视频存储,只筛选子订单是单住户视频存储的数据
                $singles = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderId], ['Type', [PAY_SUB_TYPE['singleVideoStorage']]]], 'SiteUUID');
                if (count($singles) > 0) {
                    $endUsers = array_column($singles, 'SiteUUID');
                    $userCount = $this->utils->_common->account->personalAccountSelectByArray([['UUID', $endUsers], ['Role', PERENDMROLE]], 'count(*)')[0]['count(*)'];
                    // 无效的订单
                    if (intval($userCount) != count($singles)) {
                        $this->log->payLog("#priceCheck# pay failed because user that uuid=" . json_encode($endUsers) . " is not exits.");
                        $this->dao->orderList->update(['ID' => $orderId, 'Status' => 4]);
                        $this->db->commit();
                        $this->output->echoErrorMsg(STATE_PAY_FAILED);
                    }
                }
            } elseif ($type == 11) {
                // 社区住户视频存储，只筛选子订单是社区视频存储的数据
                $communities = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderId], ['Type', [PAY_SUB_TYPE['communityVideoStorage']]]], 'SiteUUID');
                if (count($communities) > 0) {
                    $communities = array_column($communities, 'SiteUUID');
                    $accountCount = $this->utils->_common->account->accountSelectByArray([['UUID', $communities]], 'count(*)')[0]['count(*)'];
                    // 无效的订单
                    if (intval($accountCount) != count($communities)) {
                        $this->log->payLog("#priceCheck# pay failed because community that uuid=" . json_encode($communities) . " is not exits.");
                        $this->dao->orderList->update(['ID' => $orderId, 'Status' => 4]);
                        $this->db->commit();
                        $this->output->echoErrorMsg(STATE_PAY_FAILED);
                    }
                }
            } elseif (in_array($type, [14, 15, 16, 17])) { //三方锁
                $orderEnds = $this->dao->orderThirdLockList->selectByArray([['OrderID', $orderId]], 'LockUUID');
                if (count($orderEnds) > 0) {
                    $lockUUIDs = array_column($orderEnds, 'LockUUID');
                    $lockCount = $this->dao->thirdLockRelateInfo->selectByArray([['LockUUID', $lockUUIDs]], 'count(*)')[0]['count(*)'];
                    if (intval($lockCount) != count($orderEnds)) {
                        $this->log->payLog("#priceCheck# pay failed because lock that uuid=" . json_encode($lockUUIDs) . " is not exits.");
                        $this->dao->orderList->update(['ID' => $orderId, 'Status' => 4]);
                        $this->db->commit();
                        $this->output->echoErrorMsg(STATE_PAY_FAILED);
                    }
                }
            } else {
                $orderEnds = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderId],
                    ['Type', PAY_SUB_TYPE['singleVideoStorage'], '!='],
                    ['Type', PAY_SUB_TYPE['communityVideoStorage'], '!=']], 'AppID');
                if (count($orderEnds) > 0) {
                    $appIds = array_column($orderEnds, 'AppID');
                    $userCount = $this->utils->_common->account->personalAccountSelectByArray([['ID', $appIds], ['Active', $active]], 'count(*)')[0]['count(*)'];
                    // 无效的订单
                    if (intval($userCount) != count($orderEnds)) {
                        $this->log->payLog("#priceCheck# pay failed because user that id=" . json_encode($appIds) . "&& active=$active is not exits.");
                        $this->dao->orderList->update(['ID' => $orderId, 'Status' => 4]);
                        $this->db->commit();
                        $this->output->echoErrorMsg(STATE_PAY_FAILED);
                    }
                }
            }
        }

        return ['mixType' => $mixType, 'number' => $orderData['OrderNumber']];
    }

    /**
     * @Author: chenpl
     * @Description: 判断是否是同一个人创建的订单，如果是同一个人，则返回空，否则返回上一笔的支付人
     * @Params: OrderNumber: 前一笔订单的订单号, payId当前订单的支付人ID, PayerType: 支付人类型
     * @Return:
     * @Date: 2024/11/6
     */
    function checkIsSamePayer()
    {
        $params = ['OldOrderAccounts', 'CurrentOrderPayerId'];
        list($oldOrderAccounts, $currentOrderPayerId) = $this->getParams($params);

        $lockOrderNumbers = $this->dao->lockOrder->selectByArray([['DataKey', $oldOrderAccounts]], 'OrderNumber');
        // 查询非当前支付者支付的锁定的订单
        $orderInfos = $this->dao->orderList->selectByArray([['OrderNumber', array_column($lockOrderNumbers, 'OrderNumber')], ['AccountID', $currentOrderPayerId, '!=']]);

        if (count($orderInfos) > 0) {
            // 当前锁定的订单中存在非当前支付者
            $payers = [];
            //支付者可能存在PM，INS，DIS，SubDis, 所以对于PM的话，需要获取对应的名字
            foreach ($orderInfos as $orderInfo) {
                if ($orderInfo['PayerType'] == '1') {
                    $this->loadUtil('manage', true);
                    $pmInfo = $this->utils->_common->manage->getPropertyInfo($orderInfo['AccountID']);
                    $payers [] = $pmInfo['FirstName'] . ' ' . $pmInfo['LastName'];
                } else {
                    $payers [] = $orderInfo['Payer'];
                }
            }

            return implode(',', $payers);
        }

        return '';
    }
}
