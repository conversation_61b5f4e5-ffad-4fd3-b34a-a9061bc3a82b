<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

class CCapture
{
    private $typeName = [MSGTEXT['motion'], MSGTEXT['capture']];
    public $types = ["0" => MSGTEXT["call"], "1" => MSGTEXT["unlockTempKey"], "2" => MSGTEXT["unlockPrivateKey"], "3" => MSGTEXT["unlockCard"], "4" => MSGTEXT["unlockFACE"], "5" => MSGTEXT["unlockApp"], "6" => MSGTEXT["unlockApp"], "7" => MSGTEXT["unlockIndoor"], "99" => "", "100" => MSGTEXT["unlockNFC"], "101" => MSGTEXT["unlockBLE"], "102" => MSGTEXT["captureSmartPlus"]];

    /**
     * @name: 截图查询
     * @service captureUtil
     */
    private function queryCom($client, $type)
    {
        $params = [
            "MAC" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "userAlias" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $begin = $params["BeginTime"];
        $end = $params["EndTime"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $user = $params["userAlias"];
        $this->log->actionLog("#model#capture#queryCom#param:mac=$mac;begin=$begin;end=$end;");
        $showNextPage = false;
        if ($client == "app") {
            $showNextPage = true;
        }
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, $showNextPage);

        $where = "";
        $bindArray = [];

        if ($mac) {
            $where = "$where AND MAC = :MAC";
            $bindArray[':MAC'] = $mac;
        }
        if ($begin) {
            $where = "$where AND CaptureTime > :BeginTime";
            $bindArray[':BeginTime'] = \util\time\setTimeZone($begin, $timeZone, "", "-");
        }
        if ($end) {
            $where = "$where AND CaptureTime < :EndTime";
            $bindArray[':EndTime'] = \util\time\setTimeZone($end, $timeZone, "", '-');
        }

        $ip = \util\computed\getIp();

        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }


        $sql = "select ID from %s where Node = :Node $where";
        $bindArray[':Node'] = $user;
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo($type, $sql, $bindArray);
        if ($type == 1) {
            $sql = "select ID,PicUrl,SPicUrl,CaptureAction,CaptureType,Initiator,Response,CaptureTime,Status,Location,SipAccount 
            from %s where Node = :Node $where order by ID desc";
        } else {
            $sql = "select ID,PicUrl,SPicUrl,CaptureTime,Status,Location,SipAccount 
            from %s where Node = :Node $where order by ID desc";
        }


        list($count, $data) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $rows = [];
        foreach ($data as $value) {
            $current = [];
            $current['Time'] = \util\time\setYesterday(\util\time\setTime($value['CaptureTime'], $customizeForm), $timeZone);
            $current['Log'] = $value['CaptureLog'];
            $current['ID'] = $value['ID'];
            $current['Location'] = $value['Location'];
            $current['SipAccount'] = $value['SipAccount'];
            $current['CaptureTime'] = $value['CaptureTime'];
            $current['Action'] = $this->types[$value['CaptureType']];
            $current['Initiator'] = $value['Initiator'];
            if ($value['Response'] === '0') {
                $current['Response'] = MSGTEXT['success'];
            } elseif ($value['Response'] === '1') {
                $current['Response'] = MSGTEXT['failed'];
            } elseif ($value['Response'] === '2') {
                $current['Response'] = MSGTEXT['offline'];
            }
            $pic = $value['PicUrl'];
            $spic = $value['SPicUrl'];
            $current['PicUrl'] = $this->setCaptureIp($value['CaptureTime'], $pic);
            $current['PicSUrl'] = $this->setCaptureIp($value['CaptureTime'], $pic, $spic);
            $current['SPicUrl'] = $current['PicSUrl'];
            $current['Status'] = $value['Status'];
            $current['Type'] = $this->typeName[$type];
            array_push($rows, $current);
        }
        $this->log->actionLog("#model#capture#queryForMotion#" . json_decode($data));
        \util\computed\setGAppData(["data" => ["detail" => $data, "row" => $rows, "total" => $count]]);
    }

    public function queryCaptureForApp()
    {
        $this->queryCom("app", 1);
    }

    public function queryMotionForApp()
    {
        $this->queryCom("app", 0);
    }

    public function queryCaptureForWeb()
    {
        $this->queryCom("web", 1);
    }

    public function queryMotionForWeb()
    {
        $this->queryCom("web", 0);
    }

    /**
     * @name: door log
     * @service captureUtil
     */
    private function queryDoorLog($selType = "door")
    {
        $params = [
            "MAC" => "",
            "userAlias" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $where = "";
        $bindArray = [":Node" => $user];

        if ($mac) {
            $where = "and MAC = :MAC";
            $bindArray[":MAC"] = $mac;
        }

        if ($selType == "door") {
            $where .= " and (CaptureType = 103 or CaptureType < 102)";
        } else {
            $where .= " and (CaptureType = 102)";
        }


        $offset = 0;
        $rows = 100;

        $sql = "select * from %s where Node = :Node $where order by ID desc";
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
        list($count, $data) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        return $data;
    }

    /**
     * @name: capture for app
     * @service captureUtil
     */
    public function queryDoorLogForApp()
    {
        $params = [
            "SelfCustomizeForm" => "",
            "SelfTimeZone" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $customizeForm = $params["SelfCustomizeForm"];
        $timeZone = $params["SelfTimeZone"];
        $data = [];
        $data["detail"] = $simtArray = $this->seleteCapLog("capture");
        $ip = \util\computed\getIp();
        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }


        $rows = [];
        $simtArray = \util\time\setQueryTimeZone($simtArray, $timeZone, $customizeForm);
        foreach ($simtArray as $value) {
            $current = [];
            $current['Time'] = \util\time\setYesterday(\util\time\setTime($value['CaptureTime'], $customizeForm), $timeZone);
            $current['Log'] = $value['CaptureLog'];
            $current['ID'] = $value['ID'];
            $current['Location'] = $value['Location'];
            $current['SipAccount'] = $value['SipAccount'];
            $current['Action'] = $this->types[$value['CaptureType']];
            $current['Initiator'] = $value['Initiator'];
            if ($value['Response'] === '0') {
                $current['Response'] = MSGTEXT['success'];
            } elseif ($value['Response'] === '1') {
                $current['Response'] = MSGTEXT['failed'];
            } elseif ($value['Response'] === '2') {
                $current['Response'] = MSGTEXT['offline'];
            }
            $pic = $value['PicUrl'];
            $spic = $value['SPicUrl'];
            $current['PicUrl'] = $this->setCaptureIp($value['CaptureTime'], $pic);
            $current['PicSUrl'] = $this->setCaptureIp($value['CaptureTime'], $pic, $spic);
            $current['SPicUrl'] = $current['PicSUrl'];
            $current['Status'] = $value['Status'];
            array_push($rows, $current);
        }
        $data["row"] = $rows;
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: door log for app
     * @service captureUtil
     */
    public function queryDoorLogForApp2()
    {
        $params = [
            "SelfCustomizeForm" => "",
            "SelfTimeZone" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $customizeForm = $params["SelfCustomizeForm"];
        $timeZone = $params["SelfTimeZone"];
        $userId = $params["userAliasId"];
        // 用户智能锁绑定判断
        $lockIndex = $deviceIndex = 0;
        $userData = $this->db->querySList('select Role,Account,ParentUUID,UUID from PersonalAccount where ID = :ID', [':ID' => $userId])[0];
        $mainUUID = $userData['UUID'];
        if (in_array(intval($userData['Role']), SUBROLE)) {
            $mainUUID = $userData['ParentUUID'];
        }
        $isSmartLockLog = $this->db->querySList('select count(*) from ThirdPartyLockCapture where PersonalAccountUUID = :PersonalAccountUUID', [
            ':PersonalAccountUUID' => $mainUUID])[0]['count(*)'];
            $devices = [];
        if (intval($isSmartLockLog) > 0) {
            // 查询智能锁 log
            list($data,$lockIndex,$deviceIndex) = $this->getDeviceAndLockCap();
        } else {
            $data = $this->seleteCapLog();
        }

        $communityData = $this->db->querySlist('select A.Grade,I.IsNew from Account A join CommunityInfo I on A.ID=I.AccountID join PersonalAccount P on A.UUID=P.ParentUUID where P.UUID=:UUID', [':UUID'=>$mainUUID])[0];

        $macs = array_column($data, 'MAC');
        if (count($macs) !== 0) {
            $deviceTable = in_array(intval($userData['Role']), COMROLE) ? 'Devices' : 'PersonalDevices'; 
            $devices = $this->db->querySList("select MAC,Relay,SecurityRelay from $deviceTable where MAC in ('".implode("','", $macs)."')");
            $devicesMACKey = [];
            foreach ($devices as $value) {
                $devicesMACKey[$value['MAC']] = $value;
            }
        }

        $types = CAPTURE_TYPE;
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome) {
            $account = $this->db->querySList("select CU.MngAccountID from PersonalAccount PA join CommunityUnit CU
                on PA.UnitID = CU.ID where PA.ID = $userId and Role in (20, 21)");
            if (count($account) != 0) {
                $accountId = $account[0]["MngAccountID"];
                $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $accountId])[0]['Switch'];
                $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
                if ($smartHomeSwitch == 1) {
                    $types = CAPTURE_TYPE_HOME;
                }
            }
        }
        $ip = \util\computed\getIp();
        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }


        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$val) {
            // TODO
            if ($val["Response"] == 0) {
                $val["ListText"] = $val["Initiator"] . " " . ($val["CaptureType"] == 103 ? MSGTEXT["call"] : '');
            } else {
                $val["ListText"] = MSGTEXT['failedUnlock'];
            }

            // 200=Auto Lock,201=Lock by app
            // V6.5.0 202=door sensor open;203=door sensor close
            if ($val["CaptureType"] == '201' || $val['CaptureType'] == '200') {
                $val['LockAction'] = MSGTEXT['lock'];
            } elseif ($val["CaptureType"] == '202') {
                $val['LockAction'] = MSGTEXT['open'];
                $val["ListText"] = MSGTEXT['doorSensor'];
            } elseif ($val["CaptureType"] == '203') {
                $val['LockAction'] = MSGTEXT['close'];
                $val["ListText"] = MSGTEXT['doorSensor'];
            } else {
                $val['LockAction'] = MSGTEXT['unlock'];
            }
            if ($val["Response"] == 0) {
                $val["CaptureCall"] = ($val["CaptureType"] == 0 || $val["CaptureType"] == 103) ? 1 : 0;
                if ($val['IsSmartLock'] === '1') {
                    $val["CaptureCall"] = 1;
                }
                $val["CaptureType"] = $types[$val["CaptureType"]] ? $types[$val["CaptureType"]] : "--";
            } else {
                $val["CaptureType"] = MSGTEXT['failedUnlock'];
            }
            $val['Time'] = \util\time\setYesterday(\util\time\setTime($val['CaptureTime'], $customizeForm), $timeZone);
            $pic = $val['PicUrl'];
            $spic = $val['SPicUrl'];
            $val['PicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic);
            $val['SPicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic, $spic);
            if ($communityData['Grade'] == 21 && $communityData['IsNew'] == 1) {
                $val['RelayName'] = implode(',',\util\computed\relayValueToName($val['Relay'], $devicesMACKey[$val['MAC']]['Relay']));
                $val['SecurityRelayName'] = implode(',',\util\computed\relayValueToName($val['SecurityRelay'], $devicesMACKey[$val['MAC']]['SecurityRelay']));
            }
            
        }
        \util\computed\setGAppData(["data" => $data, 'LockIndex' => $lockIndex, 'DeviceIndex' => $deviceIndex]);
    }

    /**
     * @name: PM query capture
     * @service captureUtil
     */
    public function queryCaptureForPM()
    {
        $params = [
            "Type" => "",
            "StartTime" => "",
            "EndTime" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $startTime = $params["StartTime"];
        $endTime = $params["EndTime"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [":MngAccountID" => $userId];

        if ($type == 1) {
            $where .= " and C.CaptureType = 102";
        }

        if ($startTime) {
            $bindArray[":StartTime"] = \util\time\setTimeZone($startTime, $timeZone, "", "-");
            $where .= " and C.CaptureTime >= :StartTime";
        }
        if ($endTime) {
            $bindArray[":EndTime"] = \util\time\setTimeZone($endTime, $timeZone, "", "-");
            $where .= " and  C.CaptureTime <= :EndTime ";
        }

        $data = [];
        $sql = "select C.ID from %s C
        where C.MngAccountID = :MngAccountID $where";
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo($type, $sql, $bindArray);

        $sql = "select C.* from %s C
			where C.MngAccountID = :MngAccountID $where";
        $sql .= " order by ID desc";
        list($count, $simtArray) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);


        $ip = \util\computed\getIp();

        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }
        $simtArray = \util\time\setQueryTimeZone($simtArray, $timeZone, $customizeForm);

        $macBindArray = [];
        $macWheres = [];
        foreach ($simtArray as $key => $value) {
            array_push($macWheres, ":MAC$key");
            $macBindArray[":MAC$key"] = $value['MAC'];
        }
        $macData = [];
        if (count($macWheres) !== 0) {
            $macWheres = implode(',', $macWheres);
            $macs = $this->db->querySList("select D.Location,U.UnitName,D.MAC from Devices D 
            join CommunityUnit U on D.UnitID = U.ID where D.MAC in ($macWheres)", $macBindArray);
            foreach ($macs as $mac) {
                $macData[$mac['MAC']] = $mac;
            }
        }

        $data["total"] = $count;
        $data["row"] = $data["detail"] = $simtArray;
        foreach ($data["row"] as &$val) {
            $pic = $val['PicUrl'];
            $spic = $val['SPicUrl'];
            $val['PicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic);
            $val['SPicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic, $spic);
            $val['CaptureAction'] = $val['CaptureType'] = $this->types[$val['CaptureType']];
            // $val['Location'] = $macData[$val['MAC']]['Location'];
            $val['UnitName'] = $macData[$val['MAC']]['UnitName'];
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: PM query door log
     * @service captureUtil
     */
    public function queryDoorLogForPM()
    {
        $params = [
            "LogType" => "",
            "StartTime" => "",
            "EndTime" => "",
            "TempKeyID" => "",
            "TmpKey" => "",
            "Build" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $logType = $params["LogType"];
        $tmpkey = $params["TmpKey"];
        $tempKeyID = $params["TempKeyID"];
        $start = $params["StartTime"];
        $end = $params["EndTime"];
        $buildID = $params["Build"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $whereCapture = "";
        $bindArrayCapture = [];
        $whereDevice = "";
        $bindArrayDevice = [":MngAccountID" => $userId];

        // 查询小区信息
        $userConf = $this->db->querySList('select A.Grade,I.IsNew from Account A join CommunityInfo I on A.ID=I.AccountID where A.ID=:ID', [':ID'=>$userId])[0];


        if ($logType == "1") {
            $whereCapture .= " and C.CaptureType = 103";
        } elseif ($logType == "0") {
            $whereCapture .= " and C.CaptureType < 102";
        } elseif ($logType == "2") {
            // CaptureType 105 指Door Close
            $whereCapture .= " and C.CaptureType = 105";
        } else {
            $whereCapture .= " and (C.CaptureType != 102 and C.CaptureType != 104)";
        }

        if (!empty($serchValue)) {
            switch ($serchKey) {
                case 'Initiator':
                    $whereCapture = " and C.Initiator like :serchValue";
                    $bindArrayCapture[":serchValue"] = "%$serchValue%";
                    break;
                case 'Device':
                    $whereCapture = " and C.Location like :serchValue";
                    $bindArrayCapture[":serchValue"] = "%$serchValue%";
                    break;
                case 'Apt':
                    $whereCapture = " and C.RoomNum like :serchValue";
                    $bindArrayCapture[":serchValue"] = "%$serchValue%";
                    break;
                case 'RfCard':
                    if (empty($serchValue)) {
                        $whereCapture = " and C.CaptureType = 3";
                    } else {
                        $whereCapture = " and C.CaptureType = 3 and C.KeyNum like :serchValue";
                        $bindArrayCapture[":serchValue"] = "%$serchValue%";
                    }
                    break;
                case 'Pin':
                    if (empty($serchValue)) {
                        $whereCapture = " and C.CaptureType = 2";
                    } else {
                        $whereCapture = " and C.CaptureType = 2 and C.KeyNum = :serchValue";
                        $bindArrayCapture[":serchValue"] = "$serchValue";
                    }
                    break;
                default:
                    break;
            }
        }

        if ($start) {
            $whereCapture .= " and CaptureTime >= :StartTime";
            $bindArrayCapture[":StartTime"] = \util\time\setTimeZone($start, $timeZone, "", "-");
        }
        if ($end) {
            $whereCapture .= " and CaptureTime <= :EndTime";
            $bindArrayCapture[":EndTime"] = \util\time\setTimeZone($end, $timeZone, "", "-");
        }

        if ($tmpkey) {
            //V6.1 修改，因为加密后原先TmpKey查询是****无法查询出信息，所有增加了tmpId来查询真正的Tmpkey
            if ($tmpkey === "****") {
                list($tableType, $ID) = explode('_', $tempKeyID);
                $tempTables = ['PersonalAppTmpKey', 'PubAppTmpKey'];
                $tempTablesSearch = ['TmpKey', 'Code'];
                $TmpKey = $this->db->querySList(
                    "select " . $tempTablesSearch[$tableType] . " from " . $tempTables[$tableType] . " where MngAccountID = :MngAccountID and ID = :ID",
                    [":ID" => $ID, ":MngAccountID" => $userId]
                )[0][$tempTablesSearch[$tableType]];
                $whereCapture .= " and (C.KeyNum = :KeyNum and C.CaptureType = 1)";
                $bindArrayCapture[":KeyNum"] = $TmpKey;
            } else {
                $whereCapture .= " and (C.KeyNum = :KeyNum and C.CaptureType = 1)";
                $bindArrayCapture[":KeyNum"] = $tmpkey;
            }
        }

        if ($buildID && $buildID != "all") {
            $devices = [];
            $deviceData = $this->db->querySList("select D.MAC,D.Location,U.UnitName from Devices D
            left join CommunityUnit U on U.ID = D.UnitID where D.UnitID = :UnitID", [':UnitID' => $buildID]);
            $mac = [];
            foreach ($deviceData as $val) {
                array_push($mac, "'".$val['MAC']."'");
                $devices[$val['MAC']] = $val;
            }

            if (count($mac) == 0) {
                $data = ['row'=>[], 'detail'=>[], 'total'=>0];
            } else {
                $mac = "(".implode(",", $mac).")";
                $data = [];
                $sql = "select C.* from %s C where MAC in $mac $whereCapture";
                $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArrayCapture);
                $sql = "$sql order by C.ID desc";
                list($count, $simtArray) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArrayCapture, $offset, $rows);
                $data["row"] = $data["detail"] = $simtArray;
                $data["total"] = $count;
            }
        } else {
            $devices = [];
            $deviceData = $this->db->querySList(
                "select D.MAC,D.Location,U.UnitName from Devices D
                left join CommunityUnit U on U.ID = D.UnitID where D.MngAccountID = :MngAccountID",
                [':MngAccountID' => $userId]
            );
            foreach ($deviceData as $val) {
                $devices[$val['MAC']] = $val;
            }

            $data = [];
            $sql = "select C.* from %s C where MngAccountID = :MngAccountID $whereCapture";
            $bindArrayCapture[':MngAccountID'] = $userId;
            $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArrayCapture);
            $sql = "$sql order by C.ID desc";
            list($count, $simtArray) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArrayCapture, $offset, $rows);
            $data["row"] = $data["detail"] = $simtArray;
            $data["total"] = $count;
        }

        $macs = array_column($data["row"], 'MAC');
        if (count($macs) !== 0) {
            $deviceTable = 'Devices'; 
            $deviceDatas = $this->db->querySList("select MAC,Relay,SecurityRelay from $deviceTable where MAC in ('".implode("','", $macs)."')");
            $devicesMACKey = [];
            foreach ($deviceDatas as $value) {
                $devicesMACKey[$value['MAC']] = $value;
            }
        }

        $ip = \util\computed\getIp();

        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }
        $types = CAPTURE_TYPE;
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome) {
            $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                $types = CAPTURE_TYPE_HOME;
            }
        }
        $data["row"] = \util\time\setQueryTimeZone($data["row"], $timeZone, $customizeForm);
        foreach ($data["row"] as &$val) {
            $val['OriginResponse'] = $val['Response'];
            $val['OriginCaptureAction'] = $val['CaptureAction'];
            if ($val["CaptureType"] == 103) {
                $val["LogType"] = MSGTEXT["call"];
            } elseif ($val["CaptureType"] == 105) {
                // 一键关门
                $val["LogType"] = MSGTEXT["doorClose"];
            } else {
                $val["LogType"] = MSGTEXT["doorRelease"];
            }

            if ($val['Response'] === '0') {
                $val['Response'] = MSGTEXT['success'];
            } elseif ($val['Response'] === '1') {
                $val['Response'] = MSGTEXT['failed'];
            } elseif ($val['Response'] === '2') {
                $val['Response'] = MSGTEXT['offline'];
            }
            $val["CaptureAction"] = $types[$val["CaptureType"]];
            $pic = $val['PicUrl'];
            $spic = $val['SPicUrl'];
            $val['PicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic);
            $val['SPicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic, $spic);
            // 区分doorlog的pin和tempkey，pin永远返回****
            if ($val["CaptureType"] == 2) {
                $val["KeyNum"] = '****';
            } elseif ($val["CaptureType"] == 1) {
                $val["KeyNum"] = \util\computed\setPinIsEncryptPin($userId, $val["KeyNum"]);
            } elseif ($val["CaptureType"] == 4) {
                $val["KeyNum"] = '';
            }

            if ($val["Initiator"] == 'visitor' || $val["Initiator"] == 'Unknown') {
                $val["Initiator"] = MSGTEXT['visitor'];
            }
            $val['UnitName'] = $devices[$val['MAC']]['UnitName'];
            // 兼容后台记录的两种格式
            if ($val['RoomNum'] !== '' && $val['RoomNum'] !== '--') {
                // 个人设备，Node记的是设备的所属者
                if ($val['DevType'] === '0') {
                    $floor = $this->db->querySList('select Floor from CommunityRoom where UnitID = :UnitID and RoomName = :RoomName', [
                        ':UnitID' => $val['UnitID'], ':RoomName' => $val['RoomNum']])[0]['Floor'];
                } else {
                    // 公共设备，Node记的是开门者的Account
                    $floor = $this->db->querySList('select C.Floor from CommunityRoom C join PersonalAccount P on P.RoomID = C.ID where Account = :Account', [
                        ':Account' => $val['Node']])[0]['Floor'];
                }
                if ($floor !== '' && $floor !== null) {
                    $val['RoomNum'] = $val['RoomNum'].' ('.MSGTEXT['floor'].' '.$floor.')';
                }
            }
            if ($userConf['Grade'] == 21 && $userConf['IsNew'] == 1) {
                $val['RelayName'] = implode(',',\util\computed\relayValueToName($val['Relay'], $devicesMACKey[$val['MAC']]['Relay']));
                $val['SecurityRelayName'] = implode(',',\util\computed\relayValueToName($val['SecurityRelay'], $devicesMACKey[$val['MAC']]['SecurityRelay']));
            }
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function deleteCom($client, $type)
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $user = $params["userAlias"];
        $this->log->actionLog("#model#build#delete#user=$user;id=$id");
        $id = explode(";", $id);
        $bindArray = [":Node" => $user];
        $where = "";
        foreach ($id as $key => $val) {
            $bindArray[":ID$key"] = $val;
            if ($key == 0) {
                $where .= "ID = :ID" . $key;
            } else {
                $where .= " OR ID = :ID" . $key;
            }
        }

        if ($where == "") {
            $sql = "select ID,PicUrl,SPicUrl from %s where Node = :Node";
        } else {
            $sql = "select ID,PicUrl,SPicUrl from %s where Node = :Node AND ($where)";
        }


        $tables = $this->services["captureUtil"]->getCaptureTables($type);
        $simtArray = $this->services["captureUtil"]->getCaptureResult($tables, $sql, $bindArray);

        if (count($simtArray) <= 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $pic = [];
        $spic = [];
        foreach ($simtArray as $value) {
            array_push($pic, $value['PicUrl']);
            array_push($spic, $value['SPicUrl']);
        }

        $pic = implode(";", $pic);
        $spic = implode(";", $spic);

        foreach ($id as $value) {
            $this->services["captureUtil"]->deleteCaptureRow($value, $type);
        }

        //TODO 删除通知
        \util\computed\setGAppData(["pic" => $pic, "spic" => $spic]);
        $this->log->endUserLog(5, null, "delete " . ($type == 0 ? "motion" : "capture"));
    }

    public function deleteForCommunity()
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#capture#deleteForCommunity#userId=$userId;id=$id");
        $ids = explode(";", $id);
        $pic = [];
        $spic = [];
        foreach ($ids as $id) {
            $sql = "select C.ID from %s C join Devices D on C.MAC = D.MAC where D.MngAccountID = :MngAccountID and C.ID = :ID";
            $bindArray = [":MngAccountID" => $userId, ":ID" => $id];
            $tables = $this->services["captureUtil"]->getCaptureTables(0);
            $data = $this->services["captureUtil"]->getCaptureResult($tables, $sql, $bindArray);
            if (count($data) === 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->services["captureUtil"]->deleteCaptureRow($id, 0);
            foreach ($data as $value) {
                array_push($pic, $value['PicUrl']);
                array_push($spic, $value['SPicUrl']);
            }
        }

        $pic = implode(";", $pic);
        $spic = implode(";", $spic);
        \util\computed\setGAppData(["pic" => $pic, "spic" => $spic]);
    }

    public function afterDelete()
    {
    }

    public function deleteCaptrueForApp()
    {
        $this->deleteCom("app", 1);
    }

    public function deleteCaptrueForWeb()
    {
        $this->deleteCom("web", 1);
    }

    public function deleteMotionForWeb()
    {
        $this->deleteCom("web", 0);
    }

    public function deleteMotionForApp()
    {
        $this->deleteCom("app", 0);
    }

    public function seleteCapLog($selType = "door")
    {
        $params = [
            "MAC" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $user = $params["userAlias"];
        $where = "";

        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $user]])[0];
        if ($data["Role"] == 21 || $data["Role"] == 11) {
            $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $data["ParentID"]]])[0];
        }

        $bindArray = [":Node" => $data["Account"]];
        if ($mac) {
            $where = "and MAC = :MAC";
            $bindArray[":MAC"] = $mac;
        }

        if ($selType == "door") {
            $where .= " and (CaptureType = 103 or CaptureType < 102)";
        } else {
            $where .= " and (CaptureType = 102)";
        }

        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        // $sql = "select C.*,D.Location from %s C join Devices D on C.MAC = D.MAC where ((C.Node = :Node) or (D.Node = :Node and D.Grade = 3)) and C.Type = 1 $where order by C.ID desc";
        $sql = "select * from %s where Node = :Node $where order by ID desc";
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
        list($count, $data) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
        return $data;
    }

    public function queryTemperatureForPM()
    {
        $params = [
            "StartTime" => "",
            "EndTime" => "",
            "Status" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#queryTemperatureForPM#params=" . json_encode($params));
        $startTime = $params["StartTime"];
        $endTime = $params["EndTime"];
        $status = $params["Status"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        $bindArray = [":MngAccountID" => $userId];
        $where = " where MngAccountID=:MngAccountID ";
        if ($startTime) {
            $bindArray[":StartTime"] = \util\time\setTimeZone($startTime, $timeZone, "", "-");
            $where .= " and CaptureTime >= :StartTime";
        }
        if ($endTime) {
            $bindArray[":EndTime"] = \util\time\setTimeZone($endTime, $timeZone, "", "-");
            $where .= " and CaptureTime <= :EndTime";
        }

        if ($status != 'all') {
            $bindArray[":Status"] = $status;
            $where .= " and Status = :Status";
        }

        $total = $this->db->querySList("select count(*) as total from Temperature $where", $bindArray)[0]["total"];
        $data = [];
        $data["total"] = $total;
        $data["row"] = $data["detail"] = $this->db->querySList("select * from Temperature $where order by ID desc limit $offset,$rows", $bindArray);


        $ip = \util\computed\getIp();

        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }

        $statusTextArr = [MSGTEXT["normal"], MSGTEXT["abnormal"], MSGTEXT["low"]];
        foreach ($data["row"] as &$val) {
            $val["StatusOriginal"] = $val["Status"];
            $val["Status"] = $statusTextArr[$val["Status"]];
            $val["CaptureTime"] = \util\time\setTimeZone($val["CaptureTime"], $timeZone, $customizeForm);
            $val["Celsius"] = number_format($val["Fahrenheit"], 1) . "℃";
            $val["Fahrenheit"] = number_format($val["Fahrenheit"] * 9 / 5 + 32, 1) . "℉";
            $pic = $val['PicUrl'];
            $spic = $val['SPicUrl'];
            $val['PicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic);
            $val['SPicUrl'] = $this->setCaptureIp($val['CaptureTime'], $pic, $spic);
            $val["Location"] = $this->db->querySList("select Location  from Devices where MAC = :MAC", [":MAC" => $val["MAC"]])[0]["Location"];
        }
        // 为了新PM log的Status不受影响显示红色值新增的参数

        \util\computed\setGAppData(["data" => $data]);
    }


    public function getExportTime()
    {
        global $gApp;
        $params = [
            "Flag" => "",
            "StartTime" => "",
            "EndTime" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $flag = $params["Flag"];
        $communityID = $params["userAliasId"];
        $userId = $gApp["userId"];
        $bindArray = [":LogType" => $flag, ":communityID" => $communityID, ":PmID" => $userId];
        $timeZone = $params["SelfTimeZone"];

        $times = ["exportBeginTime" => ""];
        $sql = "SELECT LastTime FROM PmExportLog WHERE PmID = :PmID AND CommunityID = :communityID AND LogType = :LogType AND ExportType = '0' ORDER BY LastTime DESC limit 1";
        $timesData = $this->db->querySList($sql, $bindArray);
        if (empty($timesData)
            ||
            (
                strtotime(\util\time\setTimeZone($timesData[0]["LastTime"], $timeZone, "", "+"))
                <
                strtotime('-' . EXPORTDAY)
            )
        ) {
            $times["beginFlag"] = true;
            $times["exportBeginTime"] = date('Y-m-d 00:00:00', strtotime('-' . EXPORTDAY));
        } else {
            $times["beginFlag"] = false;
            $str = $timesData[0]["LastTime"] . " +1 day";
            $times["exportBeginTime"] = date('Y-m-d 00:00:00', strtotime($str));
        }
//        $times["exportBeginTime"] exportPMLog= \util\time\setTimeZone($times["exportBeginTime"] . ' 00:00:00', $timeZone, $customizeForm, '-');
        $arr = $this->getExportEndTime($flag, $times["exportBeginTime"], $times["beginFlag"]);

        //这里判断是为了把所有开始时间是今天的都给拒绝掉
        if (date("Y-m-d", strtotime($times["exportBeginTime"])) == date("Y-m-d", strtotime("now"))) {
            $times["timeFlag"] = 2;
        } else {
            $times["timeFlag"] = $arr["timeFlag"];
        }

        $times["exportEndTime"] = $arr["EndTime"];
        $times["countFlag"] = $arr["countFlag"];
//        返回给前端的数据要转时区，然后截取掉时分秒
        $times["exportBeginTime"] = $this->setExportLogTime(\util\time\setTimeZone($times["exportBeginTime"], $timeZone, "", "+"));
        $times["exportEndTime"] = $this->setExportLogTime(\util\time\setTimeZone($times["exportEndTime"], $timeZone, "", "+"));
        $times["exportOnlyBeginTime"] = $this->setExportLogTime(\util\time\setTimeZone(date('Y-m-d H:i:s', strtotime("-" . EXPORTDAY)), $timeZone, "", "+"));
        $times["exportOnlyEndTime"] = $this->setExportLogTime(\util\time\setTimeZone(date('Y-m-d H:i:s', strtotime("now")), $timeZone, "", "+"));
        \util\computed\setGAppData(["data" => $times]);
    }

    /**
     * 返回结束时间
     * @param $type
     * @param $beginTime
     * @return array
     */
    private function getExportEndTime($type, $beginTime, $beginFlag)
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $start = $beginTime;
        $userId = $params["userAliasId"];
        $where = "";
        $bindArray = [":MngAccountID" => $userId];

        $DoorLog = 1;
        $Capture = 2;
        $TemperatureCapture = 3;
        $CallHistory = 4;

        if ($type == $DoorLog) {
            $where .= " and (CaptureType = 103 or CaptureType < 102)";
        } elseif ($type == $Capture) {
            $where .= " and CaptureType = 102";
        }

        if ($start) {
            if ($type == $CallHistory) {
                $where .= " and StartTime >= :StartTime and StartTime <= :EndTime";
            } else {
                $where .= " and CaptureTime >= :StartTime and CaptureTime <= :EndTime";
            }
            $bindArray[":StartTime"] = $start;
            // 添加判断如果是一次都没有查询的情况查询30天前到今天的前一天，正常今天就不能导出
            if ($beginFlag) {
                $day = strval(intval(explode(" ", EXPORTDAY)[0]) - 1) . " day";
                $str = $bindArray[":StartTime"] . " +" . $day;
            } else {
                $str = $bindArray[":StartTime"] . " +" . EXPORTDAY;
            }
            $end = date('Y-m-d 23:59:59', strtotime($str));
            $bindArray[":EndTime"] = $end;
            $this->log->actionLog("#model#capture#getExportEndTime#starttime:$start");
            $this->log->actionLog("#model#capture#getExportEndTime#endtime:$end");
        }

        $returnArr = $this->getExportLogCount($type, $where, $bindArray);
        $count = $returnArr["count"];
        $returnData = [];

        // 如果不是第一次导出，lastTime+1 还比剩下的第一条数据小，为了防止空档期的后一天，当天超过限制条数的计算
        if (!$beginFlag &&
            (date("Y-m-d", strtotime($returnArr["startTime"])) > date("Y-m-d", strtotime($beginTime)))
        ) {
            $endTime = $bindArray[":EndTime"];
            $bindArray[":StartTime"] = date('Y-m-d 00:00:00', strtotime($returnArr["startTime"]));
            $bindArray[":EndTime"] = date('Y-m-d 23:59:59', strtotime($returnArr["startTime"]));
            $returnArr = $this->getExportLogCount($type, $where, $bindArray);
            $oneTimeCount = $returnArr["count"];
            if ($oneTimeCount > EXPORTCOUNT) {
                $returnData["countFlag"] = 1;
                $returnData["timeFlag"] = 1;
                $returnData["EndTime"] = date("Y-m-d", strtotime($returnArr["startTime"]));
                return $returnData;
            }
            $bindArray[":EndTime"] = $endTime;
        }

        if (intval($count) == 0) {
            $returnData["countFlag"] = 2;
            $returnData["EndTime"] = date("Y-m-d H:i:s", strtotime("-1 day"));
            return $returnData;
        }

        if (intval($count) > EXPORTCOUNT) {
            $offset = \util\computed\exportLogOffset($count, EXPORTCOUNT);
            if ($type == $DoorLog) {
                $sql = "select C.ID from %s C where C.MngAccountID = :MngAccountID $where";
                $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
                $sql = "select C.CaptureTime from %s C where C.MngAccountID = :MngAccountID $where order by C.ID desc";
                // 因为要计算EXPORTCOUNT的前后时间是否一样，故2取两条
                $dataArray = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, 2)[1];
                if ($beginFlag) {
                    $oneTime = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $count - 1, 1)[1][0]["CaptureTime"];
                }
            } elseif ($type == $Capture) {
                $sql = "select C.ID from %s C where C.MngAccountID = :MngAccountID $where";
                $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
                $sql = "select C.CaptureTime  from %s C where C.MngAccountID = :MngAccountID $where order by C.ID desc";
                $dataArray = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, 2)[1];
                if ($beginFlag) {
                    $oneTime = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $count - 1, 1)[1][0]["CaptureTime"];
                }
            } elseif ($type == $TemperatureCapture) {
                $sql = "select CaptureTime  from Temperature where MngAccountID = :MngAccountID $where order by ID desc limit $offset ,2";
                $dataArray = $this->db->querySList($sql, $bindArray);
                if ($beginFlag) {
                    $sql = "select CaptureTime  from Temperature where MngAccountID = :MngAccountID $where limit 1";
                    $oneTime = $this->db->querySList($sql, $bindArray)[0]["CaptureTime"];
                }
            } else {
                $sql = "select ID from %s where MngAccountID = :MngAccountID $where";
                $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql, $bindArray);
                $sql = "select StartTime from %s where MngAccountID = :MngAccountID $where order by ID desc";
                $dataArray = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, $offset, 2)[1];
                if ($beginFlag) {
                    $oneTime = $dataArray = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, $count - 1, 1)[1][0]["StartTime"];
                }
            }

            $returnData["countFlag"] = 1;
            $timeStr = "CaptureTime";
            if ($type == $CallHistory) {
                $timeStr = "StartTime";
            }

            // 确定结束时间
            if (date("Y-m-d", strtotime($dataArray[1][$timeStr])) == date("Y-m-d", strtotime($dataArray[0][$timeStr]))) {
                if ($beginFlag && date("Y-m-d", strtotime($dataArray[1][$timeStr])) == date("Y-m-d", strtotime($oneTime))) {
                    $returnData["EndTime"] = date("Y-m-d H:i:s", strtotime($oneTime));
                    $returnData["timeFlag"] = 1;
                    return $returnData;
                } else {
                    $str = $dataArray[0][$timeStr] . " -1 day";
                    $returnData["EndTime"] = date("Y-m-d H:i:s", strtotime($str));
                }
            } else {
                $returnData["countFlag"] = 0;
                $returnData["EndTime"] = date("Y-m-d H:i:s", strtotime($dataArray[1][$timeStr]));
                return $returnData;
            }

            // 比较开始时间和结束时间
            if (date("Y-m-d", strtotime($start)) <= date("Y-m-d", strtotime($returnData["EndTime"]))
            ) {
                $returnData["countFlag"] = 0;
            } else {
                if (strtotime($start) < strtotime("now")) {
                    $returnData["timeFlag"] = 1;
                } elseif (strtotime($start) == strtotime("now")) {
                    $returnData["timeFlag"] = 2;
                }
            }
        } else {
            $returnData["countFlag"] = 0;
            $returnData["EndTime"] = date("Y-m-d", strtotime("-1 day"));
        }

        return $returnData;
    }

    /**
     * 获取导出条数
     * @param $LogType
     * @param $where
     * @param $bindArray
     * @return int $count
     */
    private function getExportLogCount($LogType, $where, $bindArray)
    {
        if ($LogType == 1) {
            $sql = "select ID from %s where MngAccountID = :MngAccountID $where";
            $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
            $sql = "select CaptureTime as StartTime  from %s where MngAccountID = :MngAccountID $where";
            list($count, $startTime) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, 0, 1);
        } elseif ($LogType == 2) {
            $sql = "select ID from %s where MngAccountID = :MngAccountID $where";
            $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
            $sql = "select CaptureTime as StartTime from %s where MngAccountID = :MngAccountID $where";
            list($count, $startTime) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, 0, 1);
        } elseif ($LogType == 3) {
            $sql = "select CaptureTime as StartTime from Temperature where MngAccountID = :MngAccountID $where";
            $simtArray = $this->db->querySList($sql, $bindArray);
            $count = count($simtArray);
            $startTime = $simtArray;
        } else {
            $sql = "select ID from %s where MngAccountID = :MngAccountID $where";
            $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql, $bindArray);
            $sql = "select StartTime from %s where MngAccountID = :MngAccountID $where";
            list($count, $startTime) = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, 0, 1);
        }
        return ["count" => $count, "startTime" => $startTime[0]];
    }

    /**
     * @param $strTime
     * @return false|string
     */
    private function setExportLogTime($strTime)
    {
        return substr($strTime, 0, strpos($strTime, ' '));
    }

    public function insertPMExportLog()
    {
        global $cMessage;
        $params = [
            "ExportType" => "",
            "Type" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "userId" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $PmId = $params["userId"];
        $CommunityId = $params["userAliasId"];
        $TraceID = \util\string\randString(22);
        $LogType = $params["Type"];
        $ExportType = $params["ExportType"];
        $LastTime = substr(\util\time\setTimeZone($params["EndTime"] . " 23:59:59", $timeZone, "", "-"), 0, strpos(\util\time\setTimeZone($params["EndTime"] . " 23:59:59", $timeZone, "", "-"), ' ')) . " 23:59:59";
        $BeginTime = \util\time\setTimeZone($params["BeginTime"] . " 00:00:00", $timeZone, "", "-");
        $where = "";
        $countArray = [":MngAccountID" => $CommunityId];
        if ($BeginTime) {
            if ($LogType == 4) {
                $where .= " and StartTime >= :StartTime and StartTime <= :EndTime";
            } else {
                $where .= " and CaptureTime >= :StartTime and CaptureTime <= :EndTime";
            }
            $countArray[":StartTime"] = $BeginTime;
            $countArray[":EndTime"] = $LastTime;
        }
        if ($LogType == 1) {
            $where .= " and (CaptureType = 103 or CaptureType < 102)";
        } elseif ($LogType == 2) {
            $where .= " and CaptureType = 102";
        }
        //最后修改日期2021/4/14 之前改过这个方法，这边没有获取到正确的数据
        $returnArr = $this->getExportLogCount($LogType, $where, $countArray);
        if ($returnArr["count"] == 0) {
            $cMessage->echoErrorMsg(StateExportExcelCountNull);
        }

        /*
         * 为了不影响之前加截图的流程这里加个导出类型判断
         * 如果导出的是excel且超过19999条，提示选择日期之前
         */
        if ($ExportType == "1") {
            /**
             * 如果选择时间段超过条数，首先要找到超过条数的时间，
             * 判断超过条数时间之前有没有数据，如果有，就提示选择这个时间之前，不能导出
             * 如果没有就提示startTime时间的后一天之前
             */
            if ($returnArr["count"] > EXPORTCOUNT) {
                if ((date("Y-m-d", strtotime($BeginTime)) === date("Y-m-d", strtotime($LastTime)))) {
                    \util\computed\setGAppData(["data" => ["isShowMorePrompt" => true]]);
                }
                $flagTime = date("Y-m-d", strtotime($LastTime . ' -1 day'));
                while (true) {
                    if ($flagTime == date("Y-m-d", strtotime($BeginTime))) {
                        $cMessage->echoErrorMsg(StateExportExcelDataBefore, [], [date("Y-m-d", strtotime($flagTime . '+1 day'))]);
                    }
                    $countArray[":EndTime"] = $flagTime . ' 23:59:59';
                    $returnArrSon = $this->getExportLogCount($LogType, $where, $countArray);
                    if ($returnArrSon['count'] == 0) {
                        //导出最新的前 x条数
                        \util\computed\setGAppData(["data" => ["isShowMorePrompt" => true]]);
                        break;
                    } elseif ($returnArrSon['count'] < EXPORTCOUNT) {
                        $cMessage->echoErrorMsg(StateExportExcelDataBefore, [], [date("Y-m-d", strtotime($flagTime . '+1 day'))]);
                    } else {
                        $flagTime = date("Y-m-d", strtotime($flagTime . ' -1 day'));
                    }
                }
            }
        }

        $bindArray = [":PmID" => $PmId, ":CommunityID" => $CommunityId, ":TraceID" => $TraceID, ":LogType" => $LogType, ":ExportType" => $ExportType, ":LastTime" => $LastTime];
        $sql = "INSERT Info PmExportLog (PmID,CommunityID,TraceID,LogType,ExportType,LastTime) VALUES (:PmID,:CommunityID,:TraceID,:LogType,:ExportType:,:LastTime)";
        $this->db->insert2List("PmExportLog", $bindArray);
        $rowData =
            $this->db->querySList("select * from PmExportLog where TraceID = :TraceID", [":TraceID" => $TraceID])[0];
        $rowData["BeginTime"] = $BeginTime;
        \util\computed\setGAppData(["rowData" => $rowData]);
    }

    public function exportPMLogExcel()
    {
        $params = [
            "TraceID" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $TraceID = $params["TraceID"];
        $rowData = $this->db->querySList("select * from PmExportLog where TraceID = :TraceID", [":TraceID" => $TraceID])[0];
        \util\computed\setGAppData(["DownloadUrl" => $rowData["DownloadUrl"]]);
    }

    /**
     * @description 将网页版door log记录整理成app的door log格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatPmAppDoorLog()
    {
        $params = [
            "data" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $params['data'];

        $formatData = [];
        if (!empty($data)) {
            $formatData = $data['row'];
            foreach ($formatData as &$val) {
                $val['Response'] = $val['OriginResponse'];
                if ($val["Response"] == 0) {
                    $val["ListText"] = $val["Initiator"] . " " . ($val["CaptureType"] == 103 ? MSGTEXT["call"] : MSGTEXT["unlock"]);
                } elseif ($val['Response'] === '2') {
                    $val["ListText"] = MSGTEXT['offline'];
                } else {
                    $val["ListText"] = MSGTEXT['failedUnlock'];
                }

                if ($val["Response"] == 0) {
                    $val["CaptureCall"] = ($val["CaptureType"] == 0 || $val["CaptureType"] == 103) ? 1 : 0;
                    $val["CaptureType"] = !empty($val['CaptureAction']) ? $val['CaptureAction'] : '--';
                } elseif ($val['Response'] === '2') {
                    $val["CaptureType"] = MSGTEXT['offline'];
                } else {
                    $val["CaptureType"] = MSGTEXT['failedUnlock'];
                }
                $val['Time'] = \util\time\setYesterday(\util\time\setTime($val['CaptureTime'], $customizeForm), $timeZone);
                $val['CaptureAction'] = $val['OriginCaptureAction'];
                unset($val['OriginResponse'], $val['OriginCaptureAction']);
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $formatData]);
    }

    /**
     * @description 设置pm app查询capture默认值
     * @return void
     * @lastEditor csc 2022/4/12 9:58 V6.4
     * <AUTHOR> 2022/4/12 9:58 V6.4
     */
    public function setQueryCaptureParams()
    {
        \util\computed\setGAppData(["Type" => 1]);
    }

    /**
     * @description 将网页版capture记录整理成app的capture格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatPmAppCapture()
    {
        $params = [
            "data" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "PMAppUserAccount" => ""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $params['data'];
        $userAccount = $params['PMAppUserAccount']['Account'];

        if (!empty($data)) {
            foreach ($data['row'] as &$val) {
                $val['Time'] = \util\time\setYesterday(\util\time\setTime($val['CaptureTime'], $customizeForm), $timeZone);
                $val['Log'] = isset($val['CaptureLog']) ? $val['CaptureLog'] : null;
                $val['Action'] = $val['CaptureAction'];
                if ($val['Response'] === '0') {
                    $val['Response'] = MSGTEXT['success'];
                } elseif ($val['Response'] === '1') {
                    $val['Response'] = MSGTEXT['failed'];
                } elseif ($val['Response'] === '2') {
                    $val['Response'] = MSGTEXT['offline'];
                }
                $val['PicSUrl'] = $val['SPicUrl'];
                $val['IsSelf'] = 0;
                if ($val['Node'] == $userAccount) {
                    $val['IsSelf'] = 1;
                }
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @description 设置pm app查询motion默认值
     * @return void
     * @lastEditor csc 2022/4/12 9:58 V6.4
     * <AUTHOR> 2022/4/12 9:58 V6.4
     */
    public function setQueryMotionParams()
    {
        \util\computed\setGAppData(["Type" => 0]);
    }

    /**
     * @description 将网页版motion记录整理成app的motion格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatPmAppMotion()
    {
        $params = [
            "data" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $params['data'];

        if (!empty($data)) {
            foreach ($data['row'] as &$val) {
                $val['Time'] = \util\time\setYesterday(\util\time\setTime($val['CaptureTime'], $customizeForm), $timeZone);
                $val['Log'] = isset($val['CaptureLog']) ? $val['CaptureLog'] : null;
                $val['Action'] = isset($val['CaptureAction']) ? $val['CaptureAction'] : null;
                $val['Initiator'] = isset($val['Initiator']) ? $val['Initiator'] : null;
                $val['Response'] = $val['Response'] ? MSGTEXT['failed'] : MSGTEXT['success'];
                $val['PicSUrl'] = $val['SPicUrl'];
                $val['type'] = MSGTEXT['motion'];
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function setCaptureIp($createTime, $pic, $spic = null)
    {
        $ip = \util\computed\getIp();
        // 澳洲云迁移
        if (SERVER_LOCATION === 'au' && strtotime($createTime) < strtotime(AU_CLOUD_CAPTURE_TIME)) {
            return  \util\computed\computedImageLinkAuCloud($spic === null ? $pic : $spic);
        }
        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }

        if ($spic === null) {
            return  \util\computed\computedImageLink($pic, $ipFix);
        } else {
            return \util\computed\computedImageLink($spic, $ipFix, $pic);
        }
    }

    public function getDeviceAndLockCap($selType = "door")
    {
        $params = [
            "MAC" => "",
            "userAlias" => "",
            "DeviceIndex" => "",
            "LockIndex" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        $mac = $params["MAC"];
        $user = $params["userAlias"];
        if ($offset === 0) {
            $deviceIndex = 0;
            $lockIndex = 0;
        } else {
            $deviceIndex = $params['DeviceIndex'] ?:$offset;
            $lockIndex = $params['LockIndex'] ?:0;
        }
        $where = "";

        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $user]])[0];
        if ($data["Role"] == 21 || $data["Role"] == 11) {
            $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $data["ParentID"]]])[0];
        }

        $bindArray = [":Node" => $data["Account"]];
        $lockBindArray = [':PersonalAccountUUID' => $data['UUID']];
        if ($mac) {
            $where = "and P.MAC = :MAC";
            $bindArray[":MAC"] = $mac;
            $lockBindArray[":MAC"] = $mac;
        }

        // 0=CALL Unlock,1=tempKey,2=localKey,3=RFCard,4=FACE,100=NFC,101=BLE,200=Auto Lock,201=Lock by app
        if ($selType == "door") {
            $where .= " and (P.CaptureType >= 103 or P.CaptureType < 102)";
        } else {
            $where .= " and (P.CaptureType = 102)";
        }

        // $sql = "select C.*,D.Location from %s C join Devices D on C.MAC = D.MAC where ((C.Node = :Node) or (D.Node = :Node and D.Grade = 3)) and C.Type = 1 $where order by C.ID desc";
        $sql = "select P.*,T.LockName,T.LockType from %s P left join ThirdPartyLockCapture T on P.PicName = T.PicName where P.Node = :Node $where order by P.ID desc";
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
        list($count, $deviceLogData) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $deviceIndex, $rows);
        // 获取三方锁记录
        $lockLogData = $this->db->querySList("select P.*,'1' as IsSmartLock,P.LockName as Location from ThirdPartyLockCapture P where P.PersonalAccountUUID =:PersonalAccountUUID and P.PicName = '' $where order by P.ID desc limit $lockIndex, $rows", $lockBindArray);
        
        unset($val);
        $allData = array_merge($deviceLogData, $lockLogData);
        $captureTimeArr = array_column($allData, 'CaptureTime');
        array_multisort($captureTimeArr, SORT_DESC, $allData);
        $data = [];
        $count = count($deviceLogData)+count($lockLogData);
        $num = $count < $rows ? $count:$rows;
        for ($i = 0; $i < $num; $i++) {
            if ($allData[$i]['IsSmartLock'] == '1' && $i !== ($num-1)) {
                $lockIndex++;
            } elseif ($i !== ($num-1)) {
                $deviceIndex++;
            }
            array_push($data, $allData[$i]);
        }

        return [$data, $lockIndex, $deviceIndex];
    }
}
