<?php
/*
 * @Description: 将first name 和 last name 拼接成name 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/computed.php";
class CFirstLastNameDeal implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog;
        $params = ["FirstName"=>"","LastName"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $cLog->actionLog("#middle#firstLastNameDeal#Name=".$firstName." ".$lastName);
        \util\computed\setGAppData(["Name"=>$firstName." ".$lastName]);
        $next();
    }
}