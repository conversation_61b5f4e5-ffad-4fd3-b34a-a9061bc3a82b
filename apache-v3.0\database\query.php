<?php
/*
 * @Description: 数据库查询方法
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-19 20:27:49
 * @LastEditors  : kxl
 */
namespace database;

use PDO;

trait QueryDatabase
{
    public function isExistFiled($table, $filed, $id)
    {
        //检查某个字段在表中是否存在
        //$table 表名，$filed 字段键值对[:字段名=>字段值]，id，可选，去除某个id项
        $where = $this->bindArr2StrEqu($filed, "=");
        
        $this->sql = "select ID from $table where $where";
        if ($id !== null) {
            $this->sql = "select ID from $table where $where AND ID != :ID";
        }
        $simt = $this->db->prepare($this->sql);
        foreach ($filed as $key1 => $value1) {
            $simt->bindValue($key1, $value1);
        }
        if ($id !== null) {
            $simt->bindValue(":ID", $id);
        }
        $simt->execute();
        $simtArray = $simt->fetchall(PDO::FETCH_ASSOC);
        if (count($simtArray) === 0) {
            return false;
        }
        return true;
    }
    
    public function querySList($sql, $bindArray = [])
    {
        $simt = $this->db->prepare($sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();
        $data = $simt->fetchAll(PDO::FETCH_ASSOC);
        return $data;
    }
    
    public function queryAllList($tableName, $bindArray = [])
    {
        //普通全表查询
        $where = $this->getWhere($bindArray);
        $this->sql = "select * from $tableName $where";
        $simt = $this->db->prepare($this->sql);
        foreach ($bindArray as $key => $value) {
            if (!is_array($value)) {
                continue;
            }
            foreach ($value as $key1 => $value1) {
                $simt->bindValue($key1, $value1);
            }
        }
        $simt->execute();
        $simtArray = $simt->fetchall(PDO::FETCH_ASSOC);
        return $simtArray;
    }
}
