<?php
  const MSGTEXT = [ 

"accountExits"=>"Bu hesap zaten mevcut",
"accountNotExit"=>"Hesap mevcut değil",
"accountIncorrect"=>"Geçersiz kullanıcı adı veya şifre",
"accountNIncorrect"=>"Geçersiz Hesap",
"activeEmpty"=>"Aktif değer gerekli",
"addFail"=>"Ekleme Başarısız",
"addSuccess"=>"Ekleme Başarılı",
"addSuccessPw"=>"Ekleme başarılı, Şifre '%s'.",
"addTmpKeyFail"=>"Geçici şifre eklenemedi, lütfen tekrar deneyin",
"aptDuplicated"=>"%s dairesi kopyalandı",
"aptDigits"=>"%s dairesi geçersiz, 1 ile 6 basamaklı sayı arasında olmalıdır",
"aptExit"=>"%s dairesi zaten var",
"abnormal"=>"Anormal",
"activation"=>"Etkinleştirme",
"additionalApp"=>"Ek Uygulama",
"bindDevice"=>"Lütfen bu hesap altındaki tüm cihazları silin",
"bindMAClibrary"=>"Lütfen MAC kütüphanesinden MAC'i silin",
"bindUser"=>"Lütfen bu hesap altındaki kullanıcıları silin",
"buildingBindDevice"=>"Lütfen bu bloğun altındaki cihazları silin",
"buildingBindUser"=>"Lütfen bu bloğun altındaki kullanıcıları silin",
"buildingDigits"=>"%s geçersiz blok, 1 ila 2 basamaklı sayı arasında olmalıdır",
"buildingExit"=>"Blok zaten var",
"BindingDeviceFailed"=>"Cihaz bağlama başarısız oldu, cihaz başka bir kullanıcıya bağlanmış olabilir veya MAC kitaplığına eklenmemiş olabilir",
"chcekMacExits"=>"Ekleme başarısız, MAC adresi geçersiz veya zaten ekli",
"changePasswdFail"=>"Şifre değiştirme başarısız",
"changePasswdPEmail"=>"Şifre değiştirme başarılı, lütfen %s e-postasını kontrol edin",
"community"=>"Site",
"deleteFail"=>"Silme Başarısız",
"deleteSuccess"=>"Silme Başarılı",
"deviceTypeEmpty"=>"Cihaz türü gerekli",
"deviceNotFindUser"=>"Cihaz bulunamadı, lütfen yöneticinize başvurun",
"dealSuccess"=>"Ayar Başarılı",
"doorUnit"=>"Kapı Ünitesi",
"emailExits"=>"E-posta zaten mevcut.",
"emailPExit"=>"%s e-postası zaten var",
"emailNotExits"=>"Bu e-posta mevcut değil.",
"emailDuplicated"=>"%s e-postası kopyalandı",
"errorVersion"=>"Sürüm Hatası",
"emailOrAccountNotExit"=>"Bu e-posta mevcut değil.",
"firstNameEmpty"=>"Ad gerekli",
"failed"=>"Başarısız oldu",
"family"=>"Aile",
"guardPhone"=>"Güvenlik Telefonu",
"incorrectSipAccount"=>"Kullanılabilir SIP hesabı yok",
"incorrectSipAccountGroup"=>"Kullanılabilir SIP grubu yok",
"importDataSuccess"=>"İçe aktarma başarılı",
"importFailMACExit"=>"İçe aktarma başarısız oldu, lütfen MAC adresinin var olup olmadığını kontrol edin: \r\n%s",
"invaildDC"=>"Geçersiz Cihaz Kodu",
"InvalidFile"=>"Geçersiz Dosya",
"invalidPEmail"=>"Geçersiz e-posta %s",
"invalidPName"=>"Geçersiz kullanıcı ismi %s",
"invalidPCalltype"=>"Geçersiz arama türü %s",
"invalidPPin"=>"Geçersiz PIN %s",
"invalidPActive"=>"Geçersiz aktif değer %s",
"invalidPage"=>"Geçersiz sayfa",
"invalidPDeviceType"=>"%s geçersiz cihaz türü",
"invaildVerCode"=>"Geçersiz doğrulama kodu",
"invalidIdentity"=>"Geçersiz kimlik bilgileri! Başka bir yerde giriş yapmış olabilirsiniz, lütfen tekrar giriş yapın.",
"indoorMonitor"=>"Daire İçi Panel",
"inactivated"=>"Aktif Değil",
"normal"=>"Normal",
"expired"=>"Süresi doldu",
"lastNameEmpty"=>"Soyad gerekli",
"locationEmpty"=>"Lokasyon gerekli",
"locationPLoog"=>"%s çok uzak lokasyon",
"locationLoog"=>"Lokasyon çok uzak",
"loginError"=>"Giriş Hatası",
"loginFail"=>"Giriş Başarısız",
"loginSuccess"=>"Giriş Başarılı",
"limitIP"=>"Çok fazla denediniz, lütfen 5 dakika sonra tekrar deneyin",
"limitDevice"=>"Cihaz sayınız maksimum sınıra ulaştı",
"MAC2PLibrary"=>"MAC adresi: %s geçersiz, lütfen MAC kitaplığınızı kontrol edin.",
"MAC2Library"=>"MAC adresi geçersiz, lütfen MAC kitaplığınızı kontrol edin",
"macExits"=>"MAC adresi zaten var",
"MACLength"=>"MAC adres uzunluğu 12 basamaklı olmalıdır.",
"modifySuccess"=>"Değiştirme Başarılı Oldu",
"modifyFailed"=>"Değiştirme Başarısız Oldu",
"maxHouse"=>"Kullanıcı sayısı maksimum sınıra ulaştı, lütfen yöneticiyle iletişime geçin",
"modifyAptFail"=>"Kaydetme başarısız oldu! Daire numarası zaten var, önce onu silmelisiniz.",
"nameloog"=>"Kullanıcı adı çok uzun, kullanıcı adı en fazla 64 karakter içerebilir",
"nameExit"=>"Kullanıcı adı zaten var",
"notPermission"=>"İşletim izniniz yok",
"noSip"=>"Daha fazla SIP hesabı yok",
"passwordIncorrect"=>"Yanlış Şifre",
"passwdChangeSuccess"=>"Şifre Değiştirme Başarılı",
"passwordResetSuccess"=>"Şifre Sıfırlama Başarılı",
"passwordReset2"=>"Şifre '%s' olarak sıfırlandı.",
"payTimeOut"=>"Ödeme zaman aşımına uğradı",
"payFailed"=>"Ödeme başarısız oldu",
"processing"=>"İşleniyor",
"paySuccess"=>"Ödeme başarılı",
"redirectedOnRPS"=>"Bu MAC adresi RPS'ye yönlendirilir.",
"registerFailed"=>"Kayıt olma başarısız",
"registerSuccess"=>"Kayıt Başarılı",
"roomNotExit"=>"Bu kullanıcı mevcut değil!",
"RFCardExit"=>"Bu RF kart zaten var",
"registered"=>"Kayıt olundu",
"PrivateKeyExists"=>"Bu özel şifre zaten var",
"passwordCorrect"=>"Geçersiz Şifre",
"timeLessCurrent"=>"Geçersiz Güncelleme Zamanı",
"timeZoneChangeSuccess"=>"Saat dilimini değiştirme başarılı",
"timeOut"=>"Zaman aşımı",
"unbindMACUser"=>"Lütfen önce %s kullanıcısıyla bağlantıyı kesin",
"unKnowDT"=>"Bilinmeyen Cihaz Türü",
"userBindUser"=>"Lütfen önce bu hesap altındaki kullanıcıları silin",
"userNotExit"=>"Bu kullanıcı mevcut değil",
"userMaxPLimt"=>"Oluşturma başarısız oldu, en fazla %s aile üyesi ekleyebilirsiniz",
"unregistered"=>"Kayıtlı değil",
"validMAC"=>"Lütfen geçerli bir MAC adresi girin",
"versionExit"=>"Sürüm zaten yüklü",
"versionNameNumberExit"=>"Sürüm adı veya numarası zaten yüklü",
"sipStatus"=>"SIP hesabı ataması başarısız oldu, lütfen tekrar deneyin",
"sentCodeLater"=>"Size bir doğrulama kodu gönderdik, lütfen daha sonra tekrar deneyin",
"setSuccess"=>"Kurulum Başarılı",
"sendEmailSuccess"=>"E-posta Gönderme Başarılı",
"SetFailed"=>"Ayar Başarısız",
"stairPhone"=>"Kat Cihazı",
"successed"=>"Başarılı",
"subscription"=>"Abonelik",
"wallPhone"=>"Duvar Telefonu",
"emailMaxLen"=>"E-posta 64 karakterden az olmalıdır.",
"serverUpgradeTips"=>"Sunucu yükseltme işlemi tamamlandı, lütfen sayfayı yenileyin. Bundan önce, girdiğiniz verileri başka bir yere kopyalayabilirsiniz.",
"ActiveFamilyAccount"=>"Lütfen önce aile yöneticisinin hesabını etkinleştirin.",
"weekly"=>"Haftalık",
"daily"=>"Günlük",
"never"=>"Hiçbir zaman",
"calltypeEmpty"=>"Arama türü gerekli",
"addOutApt"=>"En fazla %s oda ekleyebilirsiniz",
"call"=>"Arama",
"unlock"=>"Kapıyı Aç",
"tryUnlockCall"=>"Arama ile kapıyı açmayı deneyin",
"tryUnlockKey"=>"Şifre ile kapıyı açmayı deneyin",
"tryUnlockCard"=>"RF kart ile kapıyı açmayı deneyin",
"tryUnlockFace"=>"Yüz Tanıma özelliği ile kapıyı açmayı deneyin",
"unlockApp"=>"SmartPlus ile kapı aç",
"unlockIndoor"=>"Daire içi panel ile kapı aç",
"unlockNFC"=>"NFC ile kapı aç",
"unlockBluetooth"=>"Bluetooth ile kapı aç",
"unlockCard"=>"RF kart ile kapı aç",
"unlockPrivateKey"=>"Özel şifreyle kapı aç",
"unlockTempKey"=>"Geçici şifre ile kapı aç",
"alarmDoorUnlock"=>"Kapı Kilidi Açma",
"alarmInfrared"=>"Kızılötesi",
"alarmSmoke"=>"Duman",
"alarmGas"=>"Gaz",
"alarmUrgency"=>"Acil",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Sabotaj",
"alarmGate"=>"Dış Kapı",
"alarmDoor"=>"Kapı",
"alarmBedroom"=>"Yatak Odası",
"alarmGuestRoom"=>"Misafir Odası",
"alarmHall"=>"Koridor",
"alarmWindow"=>"Pencere",
"alarmBalcony"=>"Balkon",
"alarmKitchen"=>"Mutfak",
"alarmStudy"=>"Çalışma Odası",
"alarmBathroom"=>"Banyo",
"alarmArea"=>"Bölge",
"RFCardExit2"=>"%s RF kartı zaten var",
"RFCardDuplicated"=>"%s RF kartı kopyalandı",
"notMacBind"=>"'%s' kullanıcısının '%s' cihazıyla kapı açma izni yok.",
"accountNumLet"=>"Hesap sayı ve harflerden oluşmalıdır",
"networkUnavailable"=>"Ağ kullanılamıyor.",
"notForModel"=>"Bu model için uygun değil.",
"upgradeDevVersion"=>"Lütfen önce en son sürüme geçin.",
"unavailableService"=>"Hizmet geçici olarak kullanılamıyor, lütfen daha sonra tekrar deneyin.",
"cantDeletePin"=>"%s pinini silemezsiniz",
"residentInRoom"=>"%s odasında zaten kullanıcı var",
"noAnswer"=>"Cevap Yok",
"indoorAndApp"=>"Daire İçi Panel ve Uygulama (Master hesap)",
"indoorMonitorOnly"=>"Yalnızca Daire İçi Panel",
"appOnly"=>"Yalnızca Uygulama (Master hesap)",
"endThanStart"=>"Bitiş zamanı başlangıç ​​zamanından önce olamaz.",
"endThanStartFile"=>"'%s' satırında geçersiz gün veya saat.",
"doorRelease"=>"Kapı Açıldı",
"success"=>"Başarılı",
"unlockFACE"=>"Yüz tanıma ile kilidi aç",
"unlockBLE"=>"Bluetooth ile kilidi aç",
"captureSmartPlus"=>"SmartPlus'ta ekran görüntüsü al",
"drmagnet"=>"Manyetik Kontak",
"failedUnlock"=>"Kilit açılamadı",
"deviceDisconnected"=>"Cihazın bağlantısı kesildi.",
"low"=>"Düşük",
"motion"=>"Hareket",
"capture"=>"Ekran Görüntüsü",
"failedImport"=>"İçe aktarma başarısız",
"notValidMobile"=>"% s, geçerli bir cep telefonu numarası değil.",
"mobileExits"=>"Telefon Numarası zaten var",
"mobileExits2"=>"%s Telefon Numarası zaten var",
"mobileDuplicated"=>"%s Telefon Numarası kopyalandı",
"mobileNumberExist"=>"Telefon Numarası mevcut değil.",
"codeIncorrect"=>"Geçersiz kod",
"sendCodeSuccess"=>"Doğrulama kodunu başarıyla gönderin",
"codeCorrect"=>"Doğru",
"mobileNumberEmpty"=>"Lütfen telefon numaranızı giriniz.",
"invalidUser"=>"Geçersiz kullanıcı %s",
"locationExits"=>"Konum adresi zaten var",
"smartPlusIndoor"=>"Daire içi panel ve SmartPlus",
"phoneIndoor"=>"Telefon ve daire içi paneller",
"smartPlusIndoorBackup"=>"Önce daire içi panel ve SmartPlus, sonra telefon",
"smartPlusBackup"=>"Önce daire içi panel, sonra SmartPlus",
"indoorPhoneBackup"=>"Önce daire içi panel, sonra telefon",
"indoorSmartPlusPhone"=>"Önce daire içi panel, sonra SmartPlus, son olarak da telefon",
"endUser"=>"Son Kullanıcı",
"installer"=>"Bayi",
"distributor"=>"Distribütör",
"pm"=>"ÖS",
"superManage"=>"Yönetici",
"loginManagement"=>"Giriş Yönetimi",
"accessControl"=>"Giriş Kontrolü",
"userManagement"=>"Kullanıcı Yönetimi",
"deviceManagement"=>"Cihaz Yönetimi",
"communityManagement"=>"Site Yönetimi",
"auditLogin"=>"Oturumu Aç: Web",
"auditLogout"=>"Oturumu Kapat: Web",
"auditAddTempKey"=>"Geçici şifre ekle: {0}",
"auditEditTempKey"=>"Geçici şifreyi düzenle: {0}",
"auditDeleteTempKey"=>"Geçici şifreyi sil: {0}",
"auditAddRFCard"=>"RF kart ekle: {0}",
"auditEditRFCard"=>"RF kartı düzenle: {0}",
"auditDeleteRFCard"=>"RF kartı sil: {0}",
"auditAddDis"=>"Dağıtıcı firma ekle: {0}",
"auditEditDis"=>"Dağıtıcı firmayı düzenle: {0}",
"auditDeleteDis"=>"Dağıtıcıyı firmayı sil: {0}",
"auditAddInstaller"=>"Bayi ekle: {0}",
"auditEditInstaller"=>"Bayiyi düzenle: {0}",
"auditDeleteInstaller"=>"Bayiyi sil: {0}",
"auditAddPM"=>"PM ekle: {0}",
"auditEditPM"=>"PM'i düzenle: {0}",
"auditDeletePM"=>"PM'i sil: {0}",
"auditAddEndUser"=>"Son kullanıcı ekle:{0}",
"auditEditEndUser"=>"Son kullanıcıyı düzenle: {0}",
"auditDeleteEndUser"=>"Son kullanıcıyı sil: {0}",
"auditSetOwnerTime"=>"Kendi saat dilimini ayarla {0}",
"auditSetOwnPassword"=>"Kendi Parolanızı Ayarlayın",
"auditAddPIN"=>"PIN ekle: {0}",
"auditEditPIN"=>"PIN'i düzenle: {0}",
"auditDeletePIN"=>"PIN'i sil: {0}",
"auditImportFace"=>"İçe yüz verisi aktar: {0}",
"auditDeleteFace"=>"Yüzü sil: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"İç ünite arama türünü SmartPlus ile birlikte çalacak şekilde ayarla: {0}&{1}&{2}",
"auditSetCallTypePhoneIndoor"=>"İç ünite arama türünü telefon ile birlikte çalacak şekilde ayarla: {0}&{1}&{2}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"İç ünite arama türünü SmartPlus ile birlikte çalacak şekilde ayarla, çağrı cevaplanmazsa telefona yönlendir: {0}&{1}&{2}",
"auditSetCallTypeSmartPlusBackup"=>"İç ünite arama türünü ayarla, çağrı cevaplanmazsa SmartPlus'a yönlendir: {0}&{1}&{2}",
"auditSetCallTypeIndoorPhoneBackup"=>"İç ünite arama türünü ayarla, çağrı cevaplanmazsa telefona yönlendir: {0}&{1}&{2}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"İç ünite arama türünü ayarla, SmartPlus'ta da çağrı cevaplanmazsa telefona yönlendir: {0}&{1}&{2}",
"auditDeleteDevice"=>"Cihazı sil: {0}",
"auditSetAPTCount"=>"Daire sayısını belirleyin {0}: {1}",
"auditEnableLandline"=>"Telefon servisini aç: {0}",
"auditDisableLandline"=>"Telefon servisini devre dışı bırakın: {0}",
"auditSetSubTime"=>"Saat dilimini ayarla {0}: {1}",
"auditSetChargeModeInstall"=>"Ödeme türünü bayi öder olarak ayarla: {0}",
"auditSetChargeModeUser"=>"Ödeme türünü kullanıcı/PM öder olarak ayarla: {0}",
"auditSetConnectTypeDefault"=>"Bağlantı türünü varsayılan olarak ayarla: {0}",
"auditSetConnectTypeTCP"=>"Bağlantı türünü tcp olarak ayarla: {0}",
"auditSetConnectTypeUDP"=>"Bağlantı türünü udp olarak ayarla: {0}",
"auditSetConnectTypeTLS"=>"Bağlantı türünü tls olarak ayarla: {0} ",
"auditAddCommunity"=>"Site ekle: {0}",
"auditDeleteCommunity"=>"Siteyi sil: {0}",
"auditImportCommunity"=>"Siteyi içeriye aktar: {0}",
"auditSetAPTNumber"=>"{0} ve {1} daire numarasını ayarla {2}",
"auditSetEmail"=>"E-postayı ayarla {0}: {1}",
"auditSetMobile"=>"Telefon numarasını ayarla {0}: {1}",
"auditDeviceTypeStair"=>"Çok butonlu zil panelini ayarla: {0}",
"auditDeviceTypeDoor"=>"Tek butonlu zil panelini ayarla: {0}",
"auditDeviceTypeIndoor"=>"İç Üniteyi ayarla: {0}",
"auditDeviceTypeGuardPhone"=>"Güvenlik telefonunu ayarla: {0}",
"auditDeviceTypeAccessControl"=>"Giriş Kontolü Cihazını Ayarla: {0}",
"auditSetNetGroup"=>"Ağ grubunu ayarla {0}: {1}",
"auditEditCommunity"=>"Siteyi düzenle: {0}",
"deliveryMsg"=>"Size teslim edilmiş %s ürününüz var, lütfen zamanında kontrol edin.",
"deliveryTitle"=>"Yeni bir güncellemeniz var!",
"rfcardDuplicatedLines"=>"Bu RF kart numarası %s'e kopyalandı!",
"rfcardNameInvalid"=>"%s'de geçersiz kart numarası girdiniz!",
"rfcardExistLines"=>"Bu RF kartları çoktan %s'de kullanılmış.",
"importFailMacExistLines"=>"Bu MAC adresi çoktan girilmiş veya %s'de kullanılmış.",
"exportExcelCountNull"=>"Bu tarihte dışarıya aktarılacak kayıt kütüphanesi yok! Lütfen tekrar seçim yapınız.",
"keyIsEqualRoom"=>"Teslimat şifresi apartman numarası ile aynı olamaz!",
"visitor"=>"ziyaretçi",
"CommunityNameExist"=>"Site adı çoktan girilmiş",
"unlockGuardPhone"=>"Güvenlik Telefonu Kapı Aç",
"auditLoginApp"=>"Oturumu Aç: Uygulama",
"auditLogoutApp"=>"Oturumu Kapat: Uygulama",
"timeForYesterday"=>"Dün",
"exportExcelDataBefore"=>"Çok büyük veri! Lütfen önce verileri %s den önce dışa aktarın, teşekkürler.",
"tempkeyUsed"=>"Kullanılan Geçici Şifre",
"tempkeyContent"=>"%s Geçici Şifre kullanıldı.",
"accessNameExist"=>"Erişim Grubu Adı zaten var",
"addFaceFail"=>"Lütfen yüzünüzün net bir fotoğrafını içe aktarın.",
"userInvalid"=>"%s satırında geçersiz kullanıcı.",
"groupsInvalid"=>"%s satırında geçersiz erişim grubu.",
"BuildAccessName"=>"Konut-Bina %s",
"auditCodeLogEditApt"=>"daireyi düzenle:{0}",
"invalidTimeInLine"=>"%s satırında geçersiz zaman.",
"cancel"=>"İptal",
"cancelSuccess"=>"İptal başarılı oldu.",
"payOutstanding"=>"Lütfen ödenmemiş sipariş olup olmadığını kontrol edin, yoksa lütfen servis sağlayıcınıza başvurun.",
"featureDeleteError"=>"Özellik Planı bağlı.",
"beyondFamilyMember"=>"Daha fazla aile üyesi hesabı oluşturamazsınız, oluşturmak için lütfen servis sağlayıcınıza başvurun.",
"indoorMonitorRequired"=>"Her daire için en az bir iç mekan monitörü gereklidir.",
"featureActivationFee"=>"Özellik (Tek Seferlik Ücret)",
"systemProcessing"=>"Sistem İşleme",
"featureMonthlyFee"=>"Özellik(Aylık Ücret)",
"featurePriceDifferences"=>"Özellik(Fiyat farklılıkları)",
"updatingSuccess"=>"Güncelleme Başarılı!",
"featureNameBasic"=>"Temel",
"featureNamePremium"=>"Ayrıcalıklı",
"indoorMacNotCorrect"=>"Lütfen doğru İç Mekan Monitörün MAC adresini giriniz.",
"off"=>"Kapalı",
"enterValidAccount"=>"Lütfen geçerli bir hesap girin",
"invalidKitImportMAC"=>"Lütfen MAC'in var olup olmadığını veya geçerli olup olmadığını kontrol edin: %s",
"importLessData"=>"Lütfen %s'den daha az veriyi içe aktarın.",
"invalidQRCode"=>"Tanımlama Başarısız, lütfen doğru QR kodunu tarayın.",
"cannotCreateFamilyMember"=>"Daha fazla aile üyesi hesabı oluşturamazsınız.",
"importProcessing"=>"İçe aktarılıyor, lütfen daha sonra tekrar deneyiniz",
"departmentAccessName"=>"%s Erişim Grubu",
"idExistsLine"=>"Kimlik %s satırında zaten mevcut",
"enterFirstNameLine"=>"Lütfen Adınızı %s satırına giriniz",
"enterLastNameLine"=>"Lütfen Soyadınızı %s satırına giriniz",
"departmentExist"=>"Departman mevcut",
"idExist"=>"ID mevcut",
"layoutIdInvalid"=>"Durum geçersiz",
"unlockAppHome"=>"AKHome Kilidini Aç",
"officeNameExist"=>"İsim mevcut",
"departmentExit"=>"Bölüm mevcut.",
"importOutTask"=>"Bir seferde yalnızca bir şablonu içe aktarabilirsiniz.",
"idDuplicated"=>"%s kimliği kopyalandı",
"aptInvalidLine"=>"%s satırında geçersiz APT.",
"buildInvalidLine"=>"%s satırında geçersiz Bina.",
"departmentInvalidLine"=>"%s satırında geçersiz Bölüm.",
"idInvalidLine"=>"%s satırında geçersiz kimlik.",
"propertyManager"=>"Mülk Yöneticisi",
"departmentBindDevice"=>"Lütfen bu bölümün altındaki cihazları siliniz.",
"departmentBindUser"=>"Lütfen bu bölümün altındaki kullanıcıları siliniz.",
"smartPlusValidLine"=>"%s satırında geçersiz Smartplus Interkom Özelliği.",
"identityValidLine"=>"%s satırında geçersiz Kimlik.",
"eachDoorCount"=>"Her kapıyı bir kez açmak için tek bir plan",
"textUpgradeMsg1"=>"Devam etmek için lütfen uygulama sürümünü güncelleyin.",
"textUpgradeMsg2"=>"Giriş başarısız",
"deleteCodeGetLimitTimes"=>"Geçersiz tuş. Lütfen 24 saat sonra tekrar deneyin.",
"deleteCodeOverLimitTimes"=>"24 saat sonra tekrar deneyiniz.",
"deleteCodeError"=>"Geçersiz tuş",
"textUpgradeMsg"=>"1.Bluetooth eller serbest özelliği ekleniyor.;2.Bazı hatalar düzeltildi.",
"paramsError"=>"Parametre hatası",
"pmappStatusInvalid"=>"Lütfen önce PM Uygulamasını etkinleştirin.",
"delivery_description"=>"Sıcaklık Anahtarı",
"webRelayIDInvalidLine"=>"%s satırında geçersiz Web Geçiş Kimliği.",
"relayInvalid"=>"%s satırında geçersiz geçiş.",
"cancelError"=>"İptal edilemedi.",
"textUpgradeMsgForComRole"=>"topluluk işlevini yükselt",
"textUpgradeMsgForPerRole"=>"Personel işlevini yükselt",
"textUpgradeMsgForOffRole"=>"ofis işlevini yükselt",
"textUpgradeMsgForPMRole"=>"PM işlevini yükselt",
"lockApp"=>"SmartPlus Kilidi",
"lock"=>"Kilit",
"versionLogMaxLen"=>"Sürüm günlüğü %s karakterlerinden daha büyük olamaz",
"autoLock"=>"Otomatik kilit",
"pinAndRFcardNotNullLines"=>"%S line pin ve RF kartından en az birinin doldurulması gerekir!",
"pinExistLines"=>"PIN zaten %s.",
"pinInvalidLines"=>"%S Hatta geçersiz pim!",
"pinDuplicatedLines"=>"Çoğaltılmış pim %s!",
"FaceImportLength"=>"Yüz içe aktarma dosyasının boyutu %s'den daha büyük olamaz",
"landlineServerNotActivate"=>"Bu topluluk sabit hat hizmetini etkinleştirmemiştir.",
"importFailDisNotExist"=>"Distribütör yok",
"importFailNotPermission"=>"Bu MAC adresini ekleme izniniz yok.",
"importFailTooManyAdd"=>"İthalat sadece tek distribütör için başarısız oldu.",
"importFailAdded"=>"Bu MAC adresi başka bir kullanıcı tarafından zaten eklendi.",
"macAssignToLimit"=>"Yalnızca en fazla distribütör atayabilirsiniz",
"macNumToLimit"=>"Bir seferde yalnızca 1000 MAC adresi yükleyebilirsiniz.",
"addOutFloor"=>"Lütfen 1 ~ 128 arasında bir sayı girin.",
"floor"=>"Zemin",
"PostalCodeInvalid"=>"Lütfen mektup veya numara girin.",
"onceCodeInvalid"=>"Bir kez kod 4-5 basamak olmalıdır.",
"permanentCodeInvalid"=>"Kalıcı kod 6 haneli olmalıdır.",
"onceCodeOutNum"=>"Kod yalnızca 10'a kadar ekleyebilirsiniz.",
"permanentCodeOutNum"=>"Yalnızca en fazla 10 kalıcı kod ekleyebilirsiniz.",
"onceCodeExist"=>"Bir kez kod zaten var.",
"permanentCodeExist"=>"Kalıcı kod zaten var.",
"addOutFloorLine"=>"%S.",
"auditManuallyUnlock"=>"Manuel olarak kilidini aç",
"auditManuallyLock"=>"Manuel olarak kilitle",
"automaticallyUnlock"=>"Otomatik olarak kilidini aç",
"doorClose"=>"Kapı",
"PostalCodeNotEmpty"=>"Lütfen en az bir harf veya numara girin.",
"emergencyAlarm"=>"Acil alarm",
"doorSensor"=>"Kapı sensörü",
"yaleBatteryWarning"=>"Yale pil uyarı",
"auditCodeManuallyUnlock"=>"Manuel olarak kilidini aç",
"auditCodeManuallyLock"=>"Manuel olarak kilitle",
"2weekBatteryWarning"=>"%s - Tahmini pil süresi kalan: 2 hafta.",
"1weekBatteryWarning"=>"%s - Tahmini pil süresi kalan: 1 hafta.",
"replaceBatteryWarning"=>"%s - pil seviyesi son derece düşük, lütfen hemen değiştirin.",
"open"=>"Açık",
"close"=>"Kapalı",
"addContactFavoriteNum"=>"Favorilere eklemek başarısız oldu. Sadece en fazla 300 favori daire ekleyebilirsiniz.",
"addContactBlockNum"=>"Blok listesine ekleme başarısız oldu. Blok listesine yalnızca 100'e kadar daire ekleyebilirsiniz.",
"voiceTitle"=>"Sesli mesaj",
"voiceContent"=>"%S'den sesli bir mesajınız var",
"voiceMsgInvalid"=>"Sesli mesaj süresi doldu.",
"toggleFeaturePlan"=>"Özellik planını değiştiremezsiniz.",
"rtspAddresEmpty"=>"Lütfen RTSP adresini girin.",
"rtspAddresInvalid"=>"Geçersiz RTSP adresi.",
"rtspPortEmpty"=>"Lütfen bağlantı noktasını girin.",
"rtspPortInvalid"=>"Geçersiz bağlantı noktası.",
"rtspPassWdEmpty"=>"Lütfen şifre giriniz.",
"rtspPassWdInvalid"=>"Parola çok uzun, şifre 63 karakter içerebilir.",
"cameraExist"=>"Kamera zaten var.",
"errorOnRPS"=>"RPS sunucusu talep edemedi",
"faceImportErrorSystem"=>"Sistem hatası",
"faceImportErrorView"=>"Ön görünüm değil",
"faceImportErrorWearMask"=>"Maske algılandı",
"faceImportErrorLowResolution"=>"Çözünürlük çok düşük",
"faceImportErrorWrongFormat"=>"Dosya Biçim Hatası",
"faceImportErrorNoFace"=>"Yüz tespit edilmedi",
"faceImportErrorFileLarge"=>"Dosya çok daha büyük",
"faceImportErrorFaceLarge"=>"Yüz çok daha büyük",
"faceImportErrorFaceSmall"=>"Yüz çok küçük",
"faceImportErrorMultiFaces"=>"Birden fazla yüz",
"faceImportErrorWrongName"=>"Dosya adı hata.",
"faceImportErrorEmptyName"=>"Sakinin adı boş.",
"faceImportErrorNoAccountInfo"=>"PersonalAccount bilgi hatası alın.",
"faceImportErrorAccountInactive"=>"PersonalAccount aktif değil.",
"changeHomeFeatureInvalid"=>"Operasyon başarısız!",
"changeInterComFeatureInvalid"=>"Operasyon başarısız!",
"offline"=>"Başarısız: Çevrimdışı",
"allFloors"=>"Tüm katlar",
"uploadOversize"=>"Yükleme dosyasının boyutu %s'den daha büyük olamaz",
"uploadInvalidType"=>"Yüklenen dosya türü desteklenmiyor",
"uploadFailed"=>"Yükleme başarısız oldu, lütfen daha sonra deneyin",
"uploadScreenSaverImgTooMuch"=>"Ekran koruyucu resimleri %s'den fazla olamaz!",
"screenSaverImgTooLittle"=>"Ekran koruyucu resimleri %s'den az olamaz!",
"screenSaverImgTooMuch"=>"Ekran koruyucu resimleri %s'den fazla olamaz!",
"screenSaverDevicesOffline"=>"Kayıt başarısız.",
"saveFailed"=>"Kayıt başarısız.",
"importingInProgress"=>"İthalat devam etmek, lütfen daha sonra tekrar deneyin.",
"importBuildingInvalidLine"=>"%S hizada geçersiz bina",
"importAptInvalidLine"=>"%S hizada geçersiz apt",
"importAccountTypeInvalidLine"=>"%S hizada geçersiz hesap türü",
"importFirstNameInvalidLine"=>"%S hizada geçersiz ad",
"importLastNameInvalidLine"=>"%S hizada geçersiz soyadı",
"importKeyInvalidLine"=>"%S hizada geçersiz anahtar",
"importKeyExistsLine"=>"PIN %s hatta bulunur",
"importCardInvalidLine"=>"%S hizada geçersiz RF kartı",
"importCardExistsLine"=>"RF kartı %s hatta var",
"importAccessGroupInvalidLine"=>"%S hizada geçersiz erişim grubu kimliği",
"importAccessGroupNoPermissionLine"=>"%S hizada izin erişim grubu kimliği yok",
"importExceededNumberLine"=>"%S hizada aile üyesi sayısını aştı",
"importNoActiveMasterLine"=>"İthalat %s'de başarısız oldu, lütfen önce Family Matser'ı etkinleştirin.",
"importMasterExistsLine"=>"Aile ustası zaten %s.",
"importNoCreateMasterLine"=>"İthalat %s'de başarısız oldu, lütfen önce Family Matser'ı oluşturun.",
"PrivateKeysDataExist"=>"Özel anahtar %s zaten var.",
"PrivateKeyDataExists"=>"Özel anahtar %s zaten var.",
"landLineOpenToClosedFail"=>"Kayıt başarısız.",
"limitWithIp"=>"Çok sık deniyorsunuz, lütfen 5 dakika içinde tekrar deneyin. (IP: %s)",
"subDistributor"=>"Alt distribütör",
"faceImportErrorNotClear"=>"İthal edilen resim net değil.",


  ];
