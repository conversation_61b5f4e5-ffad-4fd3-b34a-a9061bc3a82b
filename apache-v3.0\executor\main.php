<?php
/*
 * @Description: 对外部模块提供
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-18 16:10:57
 * @LastEditors  : kxl
 */
namespace executor;
include_once __DIR__."/../interfaces/executor/main.php";
use interfaces\executor\main\IExecutor;
class CWorkplace {
    private $worker;
    function __construct (IExecutor $executor) {
        $this->worker = $executor;
    }

    function setAttr ($name,$key) {
        $this->worker->$name = $key;
    }

    function todo () {
        $this->worker->parse();
        $this->worker->exec();
    }
}

/**
 * @name: FgetExecutor
 * @msg: 获取对应执行者对象
 * @param {string} $execType:执行者类型
 * @return: 执行者对象
 */
function FgetExecutor ($execType) {
    $executors = [
        "process"=>["name"=>"CProcess","path"=>"process"],
        "request"=>["name"=>"CRequest","path"=>"request"],
        "distributor"=>["name"=>"CDistributor","path"=>"distributor","providers"=>["param"]],
        "errorHandler"=>["name"=>"CErrorHandler","path"=>"errorHandler"],
        "auth"=>["name"=>"CAuthHandler","path"=>"authHandler","providers"=>["webAuth","activeAuth","appAuth","alexaAuth"]],
    ];
    $executorData = $executors[$execType];

    $executorName = "executor\\".$executorData["name"];
    include_once __DIR__."/".$executorData["path"].".php";
    $executor = $executorName::getInstance();

    if(array_key_exists("providers",$executorData)) {
        include_once __DIR__."/../container/service.php";
        $serviceContainer = \container\service::getInstance();
        foreach ($executorData["providers"] as $serviceName) {
            $serviceContainer->addService($executor,$serviceName);
        }
    }

    return $executor;
}
