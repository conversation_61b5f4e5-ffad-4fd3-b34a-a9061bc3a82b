<?php

require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/funcs_common.php');
require_once(dirname(__FILE__) . '/utility.php');
require_once(dirname(__FILE__) . '/funcs_office.php');
require_once(dirname(__FILE__) . '/funcs_email.php');

function getQrCodeInfo($uid, $email, $pwd, &$qrcodeUrl, &$qrcodeBody)
{
    $fdfsUrl = '';
    deleteUserOldQrcode($uid);
    CreateQrCodeUrl($uid, $pwd, $qrcodeUrl, $qrcodeBody, $fdfsUrl, $email);
    UpdateUserQrUrl($uid, $fdfsUrl);
}

//获取新建用户相关登陆信息的二维码图片的url
function CreateQrCodeUrl($uid, $pwd, &$qrurl, &$qrbody, &$fdfsurl, $email="")
{
    if (!empty($email)) {
        $arr['account'] = $email;
    } else {
        $arr['account'] = $uid;
    }

    //PM APP账户创建二维码只用uid
    if (DetectRoleByAccount($uid) == ACCOUNT_ROLE_COMMUNITY_PM) {
        $arr['account'] = $uid;
    }

    $arr['pwd'] = $pwd;
    //modified by chenyc,2019-11-26,网络改成域名的形式，为后续故障时的整体迁移做准备
    if (!empty(CSGAET_DOMAIN)) {
        $arr['gate_srv_net'] = CSGAET_DOMAIN;
    } else {
        $arr['gate_srv_net'] = CSGAET_NET;
    }
    $arr['gate_srv_net_ipv6'] = CSGAET_NET_IPV6;
    $arr['sid'] = SERVERNUMBER;

    //新增https网关地址
    $arr['gate_srv_net_https'] = HTTPS_CSGAET_DOMAIN;
    $arr['gate_srv_net_ipv6_https'] = HTTPS_CSGAET_DOMAIN_IPV6;


    $arr_str = json_encode($arr);

    //加密,内部已经经过base64转换
    $ciphertext = AES128CBC::encrypt($arr_str);

    //生成二维码图片
    $randomKeys = randomkeys(64);
    $file_name = $randomKeys .'.png';
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/per_qrcode/' . $uid_mod . '/' . $uid . '/';
    deldir($file_path);
    mkDirs($file_path);
    $file_root_path = $file_path . $file_name;

    //add by chenzhx 20220324 因为社区导入的动作在webtask是root权限运行的，所以文件和文件夹的权限都是root
    //导致后期通过网页改密码在生成这个二维码时候没有权限
    chown("/var/www/download/per_qrcode/$uid_mod", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod", "nobody");
    chown("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");
    QRcode::png($ciphertext, $file_root_path);
    chown($file_root_path, "nobody");
    chgrp($file_root_path, "nobody");

    $fdfsurl = "/download/per_qrcode/$uid_mod/$uid/$file_name";
    $qrurl = $fdfsurl;

    $qrbody = file_get_contents($file_root_path);
    $qrbody = base64_encode($qrbody);
    $port = "";

    $fdfs_ret = storage_pic_to_fdfs($file_root_path, "");
    if ($fdfs_ret) {
        $fdfsurl = $fdfs_ret;
        $port = ":8091";
    }
    $timeout = 3600 * 24 * 180;
    $qrurl = CreateConfigFdfsLink($fdfsurl, $timeout);

    if (empty(WEB_DOMAIN)) {
        $qrurl = 'https://'. WEB_IP . "$port".$qrurl;
    } else {
        $qrurl = 'https://'. WEB_DOMAIN . "$port".$qrurl;
    }
}

//获取新建用户相关登陆信息的二维码图片的url
//先生成url 在弄成body
function getQrCodeBody($uid)
{
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/per_qrcode/' . $uid_mod . '/' . $uid . '/';
    $file_name = shell_exec("ls ". $file_path. " | tr -d '\n'");
    $file_path = $file_path . "/" . $file_name;

    $qr_body = file_get_contents($file_path);
    return base64_encode($qr_body);
}

//删除用户旧的二维码连接
function deleteUserOldQrcode($uid)
{
    $url = "";
    GetUserQrUrl($uid, $url);

    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = "/var/www/download/per_qrcode/$uid_mod/$uid/*";
    shell_exec("rm ".$file_path);
    fdfs_del_pic_by_url($url);
}


//新建用户,后台将用户名+密码等信息加密后通过邮件发送出去
//$uid:账号
//$pwd:账号密码
//$email:新建账号的邮箱地址
function OfficeCreateUser($uid, $pwd, $email)
{
    //delete old url
    deleteUserOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdateUserQrUrl($uid, $fdfs_url);
    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;

    $cLog->TRACE('[OfficeCreateUser]begin to office create uid, uid = {uid}, email = {email}', ["uid" => $uid, "email" => $email]);

    $perCreateUserSocket = new CPerCreateUserSocket();
    $perCreateUserSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_CREATE_UID);
    $perCreateUserSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $perCreateUserSocket->copy($data);
}

function OfficeChangePwd($uid, $pwd, $email)
{
    //delete old url
    deleteUserOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdateUserQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;
    
    $cLog->TRACE('[OfficeChangePwd]begin to personnal pwd change, uid = {uid}, email = {email}', ["uid" => $uid, "email" => $email]);

    $perChangePwdSocket = new CPerChangePwdSocket();
    $perChangePwdSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_ACCOUNT_CHANGEPWD);
    $perChangePwdSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $perChangePwdSocket->copy($data);
}

//新建用户,后台将用户名+密码等信息加密后通过邮件发送出去
//$uid:账号
//$pwd:账号密码
//$email:新建账号的邮箱地址
function perCreateUser($uid, $pwd, $email)
{
    if (CheckUidEnalbeSmarthome($uid)) {
        return;
    }
    //delete old url
    deleteUserOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdateUserQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;
    $cLog->TRACE('[perCreateUser]begin to personnal create uid, uid = {uid}, email = {email}', ["uid" => $uid, "email" => $email]);
    $perCreateUserSocket = new CPerCreateUserSocket();
    $perCreateUserSocket->setMsgID(MSG_P2A_PERSONNAL_CREATE_UID);
    $perCreateUserSocket->copy($data);
}

//用户重置密码 发送重置后的邮件
//$uid:账号
//$pwd:账号密码
//$email:新建账号的邮箱地址
//单住户和社区都调这个接口
function perChangePwd($uid, $pwd, $email)
{
    // V7.2.0 重置密码，如果未开家居不再发对讲云邮件
    if (CheckUidEnalbeSmarthome($uid)) {
        return;
    }
    changePwdHandle($uid, $pwd, $email);
    return;

    if (DetectProjectTypeByAccount($uid) == PROJECT_TYPE_OFFICE) {
        OfficeChangePwd($uid, $pwd, $email);
        return;
    }

    //delete old url
    deleteUserOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdateUserQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;

    $cLog->TRACE('[perChangePwd]begin to personnal pwd change, uid = {uid}, email = {email}', ["uid" => $uid, "email" => $email]);

    $perChangePwdSocket = new CPerChangePwdSocket();
    $perChangePwdSocket->setMsgID(MSG_P2A_PERSONNAL_CHANGE_PWD);
    $perChangePwdSocket->copy($data);
}

//新建用户,后台将用户名+密码等信息加密后通过邮件发送出去
//$uid:账号
//$pwd:账号密码
//$email:新建账号的邮箱地址
function communityCreateUser($uid, $pwd, $email)
{
    if (DetectProjectTypeByAccount($uid) == PROJECT_TYPE_OFFICE) {
        OfficeCreateUser($uid, $pwd, $email);
        return;
    }
    if (CheckUidEnalbeSmarthome($uid)) {
        return;
    }
    //delete old url
    deleteUserOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdateUserQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;
    $cLog->TRACE('[communityCreateUser]begin to community create account, uid = {uid}, email = {email}', ["uid" => $uid, "email" => $email]);
    $perCreateUserSocket = new CPerCreateUserSocket();
    $perCreateUserSocket->setMsgID(MSG_P2A_COMMUNITY_CREATE_UID);
    $perCreateUserSocket->copy($data);
}

/**
 * delete user qrcode
 * $url 数据库QrUrl
 */
function deleteUserQrcode($uid, $url)
{
    global $cLog;
    $cLog->TRACE('[deleteUserQrcode]begin to deleteUserQrcode, uid = {uid}, url = {url}', ["uid" => $uid, "url" => $url]);
    
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = "/var/www/download/per_qrcode/$uid_mod/$uid/*";
    shell_exec("rm ".$file_path);
    fdfs_del_pic_by_url($url);

    UpdateUserQrUrl($uid, "");
}

function pmCreateUser($account_uuid, $pwd, $email)
{
    $uuid_hash = sprintf('%u', crc32($account_uuid));
    //delete old url
    deleteUserOldQrcode($uuid_hash);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uuid_hash, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdatePMQrUrl($account_uuid, $fdfs_url);

    $data[] = $account_uuid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;

    $cLog->TRACE('[pmCreateUser]begin to pm create uid, uuid = {uuid}, email = {email}', ["uuid" => $account_uuid, "email" => $email]);

    $socket = new CPerCreateUserSocket();
    $socket->setMsgID(MSG_P2A_PM_WEB_CREATE_UID);
    $socket->copy($data);
}

function pmChangePwd($account_uuid, $pwd, $email)
{
    pmChangePwdHandle($account_uuid, $pwd, $email);
    return;

    $uuid_hash = sprintf('%u', crc32($account_uuid));
    //delete old url
    deleteUserOldQrcode($uuid_hash);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uuid_hash, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdatePMQrUrl($account_uuid, $fdfs_url);

    $data[] = $account_uuid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;

    $cLog->TRACE('[pmChangePwd]begin to pm pwd change, uuid = {uuid}, email = {email}', ["uuid" => $account_uuid, "email" => $email]);

    $socket = new CPerChangePwdSocket();
    $socket->setMsgID(MSG_P2A_PM_WEB_CHANGE_PWD);
    $socket->copy($data);
}

/**
 * installer APP发送反馈
 * @param string $feedbackUUID AppInsFeedback的uuid
 */
function sendFeedbackEmail($feedbackUUID)
{
    insAppFeedbackHandle($feedbackUUID);
}

/**
 * 用户删除账号发送确认邮件
 * @param string $email       用户删除账号的邮箱
 * @param int $accountType 删除账号用户类型：EndUser=0、PM=1、Admin=2
 */
function sendDeleteAccountConfirmEmail($email, $accountType)
{
    deleteAccountConfirmHandle($email, $accountType);
}