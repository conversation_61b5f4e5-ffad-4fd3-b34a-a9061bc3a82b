<?php
namespace model\notify;

trait other
{
    public function alarmDeal()
    {
        $params = [
            "userId"=>"",
            "user"=>"",
            "ID"=>"",
            "Result"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userId"];
        $user = $params["user"];
        $id = $params["ID"];
        $result = $params["Result"];
        $this->log->actionLog("#model#notify#alarmDeal#params=".json_encode($params));
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userId]])[0];
        $role = $userData["Role"];
        $node = $user;
        if (in_array($role, SUBROLE)) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userData["ParentID"]]])[0];
            $node = $userData["Account"];
        }
        if (in_array($role, PERROLE)) {
            \AlarmDealNotify($node, $user, $id, $result);
        } else {
            \communityAlarmDealNotify($node, $user, $id, $result);
        }
    }

    public function captureDeleteForUser()
    {
        $params = [
            "userAliasId"=>"",
            "pic"=>"",
            "spic"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $pic = $params["pic"];
        $spic = $params["spic"];
        $this->log->actionLog("#model#notify#afterDelete#");
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userId]])[0];
        $role = $userData["Role"];
       
        if (in_array($role, PERROLE)) {
            \perDelPic($pic);
            \perDelPic($spic);
        } else {
            \communityDelPic($pic);
            \communityDelPic($spic);
        }
    }

    public function captureDeleteForCom()
    {
        $params = [
            "pic"=>"",
            "spic"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $pic = $params["pic"];
        $spic = $params["spic"];
        $this->log->actionLog("#model#notify#captureDeleteForCom#");
        \communityDelPic($pic);
        \communityDelPic($spic);
    }

    public function comImportFace()
    {
        $params = [
            "uniqueId"=>"",
            "path"=>"",
            "communityId"=>"",
            "projectType"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#comImportFace#params=".json_encode($params));
        $uniqueId = $params["uniqueId"];
        $communityId = $params["communityId"];
        $path = $params["path"];
        $projectType = $params["projectType"];
        $projectInfo = $this->db->querySList('select UUID from Account where ID = :ID', [':ID' => $communityId])[0];
        if ($projectType == 'office') {
            \officeUploadFaceHandle($uniqueId, $path, $communityId, $projectInfo['UUID']);
        } elseif ($projectType == 'community') {
            \communityUploadFaceHandle($uniqueId, $path, $communityId, $projectInfo['UUID']);
        }
    }

    public function comDeleteFace()
    {
        $params = [
            "FaceData"=>"",
            "DeleteAllFaceData"=>"",
            "CommunityId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#comDeleteFace#params=".json_encode($params));
        $faceData = $params["FaceData"];
        $deleteAll = $params["DeleteAllFaceData"];
        $communityId = $params["CommunityId"];
        $this->log->actionLog("#model#notify#comDeleteFace#params=".json_encode($params));
        if ($deleteAll) {
            \delAllFacePicsHandle($faceData, $communityId);
        } else {
            \delFacePicsHandle($faceData);
        }
    }

    public function messageAdd()
    {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);

        \perNewTextMesage($params['userAliasId']);
    }

    public function alexaLogin()
    {
        $params = [
            "Account"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        \alexaLoginNotify($account);
    }

    public function alexaSetArming()
    {
        $params = [
            "mac"=>"",
            "mode"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["mac"];
        $mode = $params["mode"];
        \alexaSetArmingNotify($mac, $mode);
    }
}
