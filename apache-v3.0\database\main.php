<?php
/*
 * @Description: 数据库实例(@单例)
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-31 14:23:11
 * @LastEditors  : kxl
 */
namespace database;

include_once __DIR__."/base.php";
include_once __DIR__."/query.php";
include_once __DIR__."/update.php";
include_once __DIR__."/util.php";
class CDatabase
{
    use DataBaseBasic;
    use QueryDatabase;
    use UpdateDatabase;
    use Util;
    private static $instance;
    private function __construct()
    {
        $this->db = $this->connect();
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function lastInsertId()
    {
        return  $this->db->lastInsertId();
    }

    public function begin()
    {
        global $cLog;
        $cLog->actionLog("#database##begin transaction");
        $this->db->beginTransaction();
    }

    public function commit()
    {
        global $cLog;
        $cLog->actionLog("#database#commit#commit transaction");
        $this->db->commit();
    }
}
