<?php
/*
 * @Description: 通知后台
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-05-07 15:37:06
 * @LastEditors  : kxl
 */
namespace model;
include_once __DIR__."/device.php";
include_once __DIR__."/community.php";
include_once __DIR__."/key.php";
include_once __DIR__."/other.php";
include_once __DIR__."/pay.php";
include_once __DIR__."/user.php";
include_once __DIR__."/type.php";
include_once __DIR__."/access.php";
include_once __DIR__."/../../notify/funcs.php";
include_once __DIR__."/../../util/model.php";

class CNotify {
    use \model\notify\device;
    use \model\notify\community;
    use \model\notify\key;
    use \model\notify\other;
    use \model\notify\pay;
    use \model\notify\user;
    use \model\notify\access;
}