<?php

namespace framework;

use \framework\interfaces\dao\IDao;

/**
 * @description: 基础dao类
 * @author: csc 2023/1/30 16:27 V6.5.3
 * @lastEditors: csc 2023/1/30 16:27 V6.5.3
 */
class BasicDao implements IDao
{
    //当前表名
    public $table = '';

    //需要数据混淆的字段
    //格式为：['字段1', '字段2'] ['Phone', 'Email' ...]，对应的Mapping表默认为：PhoneMapping、EmailMapping ...
    //如果字段名不符合通用Mapping表规则，例如PersonalAccount表中的Phone2、Phone3，实际对应的也是PhoneMapping表
    //可指定Mapping写为：['字段1' => 'Mapping', '字段2' => 'Mappping', '字段3']： ['Phone2' => 'Phone', 'Phone3' => 'Phone', 'Email']
    public $confusionField = [];

    //当前表中所有字段，仅在dev时有值，用来检测字段名是否正确
    public $fields = [];

    //主键
    protected $primaryKey = 'ID';

    //限制条件
    protected $condition = [];

    //限制条件参数
    protected $conditionBindArray = [];

    //限制条件参数绑定计数器(防止相同字段重复绑定的bug)
    protected $bindCount = 0;

    //orderby的参数
    protected $orderBy = "";

    //limit参数
    protected $limit = "";

    //是否加锁
    protected $locked = false;

    //以下为常用的log、db等属性
    //log
    protected $log;

    //db
    protected $db;

    //output
    protected $output;

    //auditLog
    protected $auditLog;

    //share
    protected $share;

    //dao
    protected $dao;

    public function __construct($table)
    {
        //赋予常用的log、db等属性
        $this->log = \share\util\getLog();
        $this->db = \share\util\getDatabase();
        $this->output = \share\util\getMessage();
        $this->auditLog = \share\util\getAuditLog();
        $share = new \stdClass();
        $share->util = new \framework\ShareUtilCaller();
        $this->share = $share;
        $this->dao = new \framework\DaoCaller();
        $this->dao->autoload = true;

        //类的变量赋值
        $this->table = $table;
        if (SERVER_ENV === 'dev') {
            $cacheKey = "table_". $table;
            $res = $this->cache($cacheKey);
            if (empty($res)) {
                $sql = 'SHOW FULL COLUMNS FROM ' . $table;
                $res = $this->db->querySList($sql);
                $this->cache($cacheKey, $res);
            }
            foreach ($res as $column) {
                $this->fields[] = $column['Field'];
            }
        }
    }

    /**
     * @description: 通用插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/1/30 17:12 V6.5.3
     * @lastEditors: csc 2023/1/30 17:12 V6.5.3
     */
    public function insert(array $data = []) {
        $bindArray = [];
        $mappingData = [];
        foreach ($data as $column => $val) {
            $column = ltrim($column, ':');
            //判断字段是否是表字段
            if (!in_array($column, $this->fields) && SERVER_ENV === 'dev') {
                throw new \Exception('undefined column '. $column);
            }
            $bindArray[':'.$column] = $val;
            //判断字段是否需要数据混淆，且不为空和null
            if ($this->isConfusionField($column) && $val !== '' && !is_null($val)) {
                $encodeVal = $this->dataEncode($column, $val);
                $bindArray[':'.$column] = $encodeVal;
                $mapping = array_key_exists($column, $this->confusionField) ? $this->confusionField[$column] . 'Mapping' : $column.'Mapping';
                $mappingData[$mapping][] = [
                    ':EnColumn' => $encodeVal,
                    ':EnColumnMd5' => md5($encodeVal),
                    ':DeColumn' => $val,
                    ':Ref' => 1
                ];
            }
        }

        if (!empty($mappingData)) {
            try {
                foreach ($mappingData as $key => $val) {
                    foreach ($val as $item) {
                        $this->db->insert2MappingList($key, $item);
                    }
                }
            } catch (\Exception $e) {
                throw new \Exception('Insert Mapping error:'. $e->getMessage());
            }
        }

        return $this->db->insert2List($this->table, $bindArray);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/1/30 17:13 V6.5.3
     * @lastEditors: csc 2023/1/30 17:13 V6.5.3
     */
    public function update(array $data, $key = 'ID') {
        $bindArray = [];
        $updateConfusionField = []; //当前修改的数据中包含的数据混淆字段
        $newData = []; //键值没有冒号的data
        foreach ($data as $column => $val) {
            $column = ltrim($column, ':');
            $newData[$column] = $val;
            //判断字段是否是表字段
            if (!in_array($column, $this->fields) && SERVER_ENV === 'dev') {
                throw new \Exception('undefined column '. $column);
            }
            $bindArray[':'.$column] = $val;
            //判断字段是否需要数据混淆
            if ($this->isConfusionField($column)) {
                $encodeVal = $this->dataEncode($column, $val);
                $bindArray[':'.$column] = $encodeVal;
                $updateConfusionField[] = $column;
            }
        }

        //有修改到混淆字段，需要更新Mapping表
        if (!empty($updateConfusionField)) {
            try {
                //查询修改影响到的混淆字段的数据
                $field = implode(',', $updateConfusionField);
                $existsData = $this->selectByKey($key, $bindArray[':'.$key], $field);
                //删除修改影响到的混淆字段的数据
                $this->deleteMappingByData($existsData);
                //新增修改影响到的混淆字段的数据
                $mappingData = [];
                foreach ($updateConfusionField as $column) {
                    //如果修改后的数据是空或者null，不需要插入mapping
                    if ($newData[$column] === '' or is_null($newData[$column])) {
                        continue;
                    }
                    $mapping = array_key_exists($column, $this->confusionField) ? $this->confusionField[$column] . 'Mapping' : $column . 'Mapping';
                    $mappingData[$mapping][] = [
                        ':EnColumn' => $bindArray[':'.$column],
                        ':EnColumnMd5' => md5($bindArray[':'.$column]),
                        ':DeColumn' => $newData[$column],
                        ':Ref' => count($existsData) //查询出来有几条原始数据，引用就要加上几次
                    ];
                }
                if (!empty($mappingData)) {
                    foreach ($mappingData as $k => $v) {
                        foreach ($v as $item) {
                            $this->db->insert2MappingList($k, $item);
                        }
                    }
                }
            } catch (\Exception $e) {
                throw new \Exception('Update Mapping error:' . $e->getMessage());
            }
        }

        return $this->db->update2ListWKey($this->table, $bindArray, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: csc 2023/1/30 18:03 V6.5.3
     * @lastEditors: csc 2023/1/30 18:03 V6.5.3
     */
    public function delete($val, $key = 'ID')
    {
        //判断字段是否是表字段
        if (!in_array($key, $this->fields) && SERVER_ENV === 'dev') {
            throw new \Exception('undefined column '. $key);
        }
        //判断字段是否需要数据混淆
        if ($this->isConfusionField($key)) {
            $val = $this->dataEncode($key, $val);
        }
        //如果当前表有对应的数据混淆，mapping中也需要删除
        if (!empty($this->confusionField)) {
            try {
                $data = $this->selectByKey($key, $val);
                $this->deleteMappingByData($data);
            } catch (\Exception $e) {
                throw new \Exception('Delete Mapping error:'. $e->getMessage());
            }
        }

        $this->db->delete2ListWKey($this->table, $key, $val);
    }

    /**
     * @description: 拼接where条件
     * @param {string or array }$field string时为字段名，数组时格式为 [['字段名', '字段值', '符号：可选，不选默认为=']]
     * @param {string or array} $value 字段值，只有symbol为origin时，才允许为array，格式["表达式", "绑定的值"]
     * @param {string} $symbol 符号 可选，默认为 =，
     * 增加值为origin，代表拼接的为 $field $value（$field + $value需要是完整的表达式），可实现更多场景例如 Manage = ID等需要原始字段
     * @return $this
     * @author: csc 2023/2/1 10:34 V6.5.4
     * @lastEditors: csc 2023/2/1 10:34 V6.5.4
     */
    protected function where($field, $value = '', $symbol = '=')
    {
        if (is_array($value) && $symbol != 'origin') {
            throw new \Exception('value is an array');
        }

        if (is_array($field)) {
            //解析数组
            foreach ($field as $fieldInfo) {
                $symbol = isset($fieldInfo[2]) ? $fieldInfo[2] : '=';
                if ($symbol == '%') {
                    $this->whereLike($fieldInfo[0], $fieldInfo[1]);
                    continue;
                }
                if ($symbol == 'origin') {
                    if (is_array($fieldInfo[1])) {
                        $this->condition[] = "(".$fieldInfo[0] . " " . $fieldInfo[1][0] . ")";
                        $this->conditionBindArray = array_merge($this->conditionBindArray, $fieldInfo[1][1]);
                    } else {
                        $this->condition[] = "(".$fieldInfo[0] . " " . $fieldInfo[1] . ")";
                    }
                    continue;
                }

                //是否属于混淆字段
                if ($this->isConfusionField($fieldInfo[0])) {
                    //是的话对字段加密
                    $fieldInfo[1] = $this->dataEncode($fieldInfo[0], $fieldInfo[1]);
                }
                $bindCol = $fieldInfo[0] . '__' . $this->bindCount;
                $this->bindCount++;
                $this->condition[] = $fieldInfo[0]. " $symbol :" .$bindCol;
                $this->conditionBindArray[':'.$bindCol] = $fieldInfo[1];
            }
        } else {
            if ($symbol == '%') {
                $this->whereLike($field, $value);
            } else if ($symbol == 'origin') {
                if (is_array($value)) {
                    $this->condition[] = "(".$field . " " . $value[0] . ")";
                    $this->conditionBindArray = array_merge($this->conditionBindArray, $value[1]);
                } else {
                    $this->condition[] = "(".$field . " " . $value . ")";
                }
            } else {
                //是否属于混淆字段
                if ($this->isConfusionField($field)) {
                    //是的话对字段加密
                    $value = $this->dataEncode($field, $value);
                }
                //直接拼接条件
                $bindCol = $field . '__' . $this->bindCount;
                $this->bindCount++;
                $this->condition[] = "$field $symbol :$bindCol";
                $this->conditionBindArray[':'.$bindCol] = $value;
            }
        }
        return $this;
    }

    /**
     * @description: 拼接whereLike条件
     * @param {string or array }$field string时为字段名
     * @param {string} $value 字段值
     * @return $this
     * @author: csc 2023/2/1 10:34 V6.5.4
     * @lastEditors: csc 2023/2/1 10:34 V6.5.4
     */
    protected function whereLike($field, $value = '') {
        if (is_array($value)) {
            throw new \Exception('value is an array');
        }

        //是否属于混淆字段
        if ($this->isConfusionField($field) && $value !== '' && !is_null($value)) {
            //改为子查询
            $mapping = array_key_exists($field, $this->confusionField) ? $this->confusionField[$field] . 'Mapping' : $field.'Mapping';
            $bindCol = $field . '__' . $this->bindCount;
            $this->bindCount++;
            $this->condition[] = "$field in (select AKCSMapping.$mapping.EnColumn from AKCSMapping.$mapping where DeColumn like :$bindCol)";
            //凯撒加密，因为排序问题先不进行凯撒加密
            //$value = \share\util\caesarEncrypt($value);
            $this->conditionBindArray[':'.$bindCol] = "%$value%";
        } else {
            //防止like的通配符影响
            $value = str_replace('_', '\_', $value);
            $value = str_replace('%', '\%', $value);
            //直接拼接条件
            $bindCol = $field . '__' . $this->bindCount;
            $this->bindCount++;
            $this->condition[] = "$field like :$bindCol";
            $this->conditionBindArray[':'.$bindCol] = "%$value%";
        }

        return $this;
    }

    /**
     * @description: 拼接wherein条件
     * @param {string}$field 为字段名
     * @param {array} $value 字段值
     * @return $this
     * @author: csc 2023/2/1 10:34 V6.5.4
     * @lastEditors: csc 2023/2/1 10:34 V6.5.4
     */
    protected function whereIn($field, $value = [])
    {
        if (!is_array($value)) {
            throw new \Exception('value isn\'t an array');
        }

        if (empty($value)) {
            $this->condition[] = "$field in ('')";
            return $this;
        }

        list($ids, $bindArray) = $this->getEncodeCondition($field, $value);
        $this->condition[] = "$field in ($ids)";
        $this->conditionBindArray = array_merge($this->conditionBindArray, $bindArray);
        return $this;
    }

    /**
     * @description: 拼接where not in条件
     * @param {string}$field 为字段名
     * @param {array} $value 字段值
     * @return $this
     * @author: csc 2023/2/1 10:34 V6.5.4
     * @lastEditors: csc 2023/2/1 10:34 V6.5.4
     */
    protected function whereNotIn($field, $value = [])
    {
        if (!is_array($value)) {
            throw new \Exception('value isn\'t an array');
        }

        if (empty($value)) {
            $this->condition[] = "$field not in ('')";
            return $this;
        }

        list($ids, $bindArray) = $this->getEncodeCondition($field, $value);
        $this->condition[] = "$field not in ($ids)";
        $this->conditionBindArray = array_merge($this->conditionBindArray, $bindArray);
        return $this;
    }

    /**
     * @description: 查询方法
     * @param {string} $fields 待查询的字段值
     * @param {bool} $debugSql 只返回sql不进行查询
     * @param {bool} $autoDecode 是否自动解密。默认都是自动解密，需要原数据时，$autoDecode传false，selectByArray、selectByKey、selectByKeyWArray方法均增加此参数
     * @return array
     * @throws \Exception
     * @author: csc 2023/2/20 11:48 V6.5.4
     * @lastEditors: csc 2023/2/20 11:48 V6.5.4
     */
    protected function select($fields = '*', $debugSql = false, $autoDecode = true) {
        $sql = "select $fields from " . $this->table;
        //判断类变量condition是否为空
        if (!empty($this->condition)) {
            $condition = join(' and ', $this->condition);
            $sql .= " where $condition";
        }
        if (!empty($this->orderBy)) {
            $sql .= " order by " . $this->orderBy;
        }
        if (!empty($this->limit)) {
            $sql .= " limit " . $this->limit;
        }

        if ($this->locked) {
            $sql .= " for update";
        }

        //开启调试，只返回sql不进行查询
        if($debugSql) {
            foreach ($this->conditionBindArray as $k => $v) {
                if (is_numeric($v) or is_null($v) or false === $v) {
                    $sql = str_replace($k, $v, $sql);
                } else {
                    $sql = str_replace($k, "\"$v\"", $sql);
                }
            }
            $this->reset();
            return $sql;
        }

        $res =  $this->db->querySList($sql, $this->conditionBindArray);
        $this->reset();
        foreach ($res as $key => $data) {
            foreach ($data as $col => $val) {
                //是否需要数据混淆自动解密
                if ($this->isConfusionField($col) && $autoDecode) {
                    $res[$key][$col] = $this->dataDecode($col, $val);
                }
            }
        }
        return $res;
    }

    /**
     * @description: 执行复杂sql，只允许查询语句
     * @param {string} $sql 待查询的sql
     * @param {array} $bindArray 绑定的参数数组
     * @return array
     * @throws \Exception
     * @author: csc 2023/2/20 11:47 V6.5.4
     * @lastEditors: csc 2023/2/20 11:47 V6.5.4
     */
    public function execute($sql = '', $bindArray = [])
    {
        if (!preg_match("/^\s*select/i", $sql)) {
            throw new \Exception('Only allow select sql');
        }
        $this->reset();
        return $this->db->querySList($sql, $bindArray);
    }

    /**
     * @description: 重置默认参数，搜索条件和绑定参数等
     * @return void
     * @author: csc 2023/3/9 10:04 V6.6
     * @lastEditors: csc 2023/3/9 10:04 V6.6
     */
    protected function reset() {
        $this->condition = [];
        $this->conditionBindArray = [];
        $this->orderBy = "";
        $this->limit = "";
        $this->bindCount = 0;
        $this->locked = false;
        return $this;
    }

    /**
     * @description: 加密方法
     * @param {string} $column 字段名
     * @param {string} $data 字段值
     * @param {string} $table 所属表，不填默认为本表。也可以填写其他所需表的加解密
     * @return string
     * @author: csc 2023/2/6 14:07 V6.5.4
     * @lastEditors: csc 2023/2/6 14:07 V6.5.4
     */
    public function dataEncode($column, $data, $table = '')
    {
        if ($data === '' or is_null($data)) {
            return $data;
        }
        $aesClass = \share\util\getAesForData();
        $res = $aesClass->encrypt($data);
        if (SERVER_ENV === 'dev') {
            $table = empty($table) ? $this->table : $table;
            $this->log->debug("dataEncode: $table.$column, data: $data, res: $res");
        }

        return $res;
    }

    /**
     * @description: 解密方法
     * @param {string} $column 字段名
     * @param {string} $data 字段值
     * @return string
     * @author: csc 2023/2/6 14:07 V6.5.4
     * @lastEditors: csc 2023/2/6 14:07 V6.5.4
     */
    public function dataDecode($column, $data, $table = '')
    {
        if ($data === '' or is_null($data)) {
            return $data;
        }
        $aesClass = \share\util\getAesForData();
        $res = $aesClass->decrypt($data);
        if (SERVER_ENV === 'dev') {
            $table = empty($table) ? $this->table : $table;
            $this->log->debug("dataDecode: $table.$column, data: $data, res: $res");
        }

        return $res;
    }

    /*
    * $data 数据数组
    * $confusionFieldConfig 数组中所有需要数据混淆解密的字段配置
    * 格式为：['字段1', '字段2'] ['Phone', 'Email' ...]，对应的Mapping表默认为：PhoneMapping、EmailMapping ...
    * 如果字段名不符合通用Mapping表规则，例如PersonalAccount表中的Phone2、Phone3，实际对应的也是PhoneMapping表
    * 可指定Mapping写为：['字段1' => 'Mapping', '字段2' => 'Mappping', '字段3']： ['Phone2' => 'Phone', 'Phone3' => 'Phone', 'Email']
    */
    /**
     * @description: 数组解密
     * @param {array} $data 数据数组 类似于： ['ID' => 105788, 'ParentID' => 437]
     * @param {array} $confusionFieldConfig 数组中所有需要数据混淆解密的字段配置，如果是单表查询可以不填默认以当前表的confusionField字段做为配置
     * 格式为：['字段1', '字段2'] ['Phone', 'Email' ...]，对应的Mapping表默认为：PhoneMapping、EmailMapping ...
     * 如果字段名不符合通用Mapping表规则，例如PersonalAccount表中的Phone2、Phone3，实际对应的也是PhoneMapping表
     * 可指定Mapping写为：['字段1' => 'Mapping', '字段2' => 'Mappping', '字段3']： ['Phone2' => 'Phone', 'Phone3' => 'Phone', 'Email']
     * @return mixed
     * @throws \Exception
     * @author: csc 2024/3/4 11:50 V6.7.1
     * @lastEditors: csc 2024/3/4 11:50 V6.7.1
     */
    public function dataArrDecode($data, $confusionFieldConfig = [])
    {
        if (empty($confusionFieldConfig)) {
            throw new \Exception("dataArrDecode Error: confusionFieldConfig is empty");
        }

        //数组为空直接返回
        if (empty($data)) {
            return $data;
        }

        //依次调用dataDecode()方法解密
        foreach ($confusionFieldConfig as $key => $val) {
            if (is_int($key)) {
                $column = $val;
            } else {
                $column = $key;
            }
            //不存在的字段跳过，可能会有自定义封装的复杂sql方法，field字段是由外部动态传入并非所有字段都有
            if (!array_key_exists($column, $data)) {
                continue;
            }
            $data[$column] = $this->dataDecode($column, $data[$column], 'dataArrDecode');
        }

        return $data;
    }

    /**
     * @description: 获取最后执行的sql
     * @author: csc 2023/3/6 18:12 V6.5.4
     * @lastEditors: csc 2023/3/6 18:12 V6.5.4
     */
    public function getLastSql()
    {
        return $this->db->getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： order by ID ASC
     * @return $this
     * @author: csc 2023/3/7 10:38 V6.6
     * @lastEditors: csc 2023/3/7 10:38 V6.6
     */
    public function orderBy($orderby = '') {
        $this->orderBy = $orderby;
        return $this;
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: csc 2023/3/9 10:17 V6.6
     * @lastEditors: csc 2023/3/9 10:17 V6.6
     */
    public function limit($limit = '')
    {
        $this->limit = $limit;
        return $this;
    }

    /**
     * @description: 用作select for update等的加锁。每次查询后会自动释放，下个查询方法如果还需要for update需要再次调用
     * @return $this
     * @author: csc 2023/6/12 14:04 V6.6
     * @lastEditors: csc 2023/6/12 14:04 V6.6
     */
    public function lock()
    {
        $this->locked = true;
        return $this;
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @param {bool} $autoDecode 是否自动解密
     * @return array|string
     * @throws \Exception
     * @author: csc 2023/2/1 11:27 V6.5.4
     * @lastEditors: csc 2023/2/1 11:27 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        if (!in_array($key, $this->fields) && SERVER_ENV === 'dev') {
            throw new \Exception("Field $key don't exist in Table ".$this->table);
        }

        if (is_array($val)) {
            throw new \Exception("Field $key value is an array ".$this->table);
        }

        return $this->where($key, $val)->select($fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @param {bool} $autoDecode 是否自动解密
     * @return array|string
     * @throws \Exception
     * @author: csc 2023/2/20 11:56 V6.5.4
     * @lastEditors: csc 2023/2/20 11:56 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        if (!in_array($key, $this->fields) && SERVER_ENV === 'dev') {
            throw new \Exception("Field $key don't exist in Table ".$this->table);
        }

        if (!is_array($val)) {
            throw new \Exception("Field $key value is not an array ".$this->table);
        }

        return $this->whereIn($key, $val)->select($fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @param {bool} $autoDecode 是否自动解密
     * @return array|string
     * @throws \Exception
     * @author: csc 2023/3/7 10:10 V6.5.4
     * @lastEditors: csc 2023/3/7 10:10 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        if (empty($array)) {
            throw new \Exception("selectByArray Params array is empty");
        }

        $this->makeSearchCondition($array);

        return $this->select($fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 提供快速将参数加解密的功能，使用之前如果value是数组要判断数组非空，或者判断返回值是否空的，防止where in ()的情况
     * @param {string} $field 字段
     * @param {string or array} $value 字段值或者值的数组
     * @return {array} [字段值, 加密后的值]
     * @author: csc 2023/3/1 15:03 V6.5.4
     * @lastEditors: csc 2023/3/1 15:03 V6.5.4
     */
    protected function getEncodeCondition($field, $value) {
        if (is_array($value)) {
            //是否属于混淆字段
            if ($this->isConfusionField($field)) {
                foreach ($value as &$val) {
                    $val = $this->dataEncode($field, $val);
                }
                unset($val);
            }
            if (empty($value)) {
                return ['', []];
            }
            //拼接条件
            $bindCol = $field . '__' . $this->bindCount . '_';
            $this->bindCount++;
            list($ids, $bindArray) = $this->share->util->getImplodeData($value, $bindCol, false);
            return [$ids, $bindArray];
        } else {
            if ($this->isConfusionField($field)) {
                $value = $this->dataEncode($field, $value);
            }
            return [$field, $value];
        }
    }

    /**
     * @description: 设置或读取缓存（1个参数时为读取缓存，2个参数时为设置缓存），用兼容的全局变量设置缓存仅生效于单次Request，如果要持久化的缓存后期可改用redis等
     * @param {string} $key 缓存key
     * @param {*} $val 缓存值
     * @return mixed|void|null
     * @author: csc 2023/2/20 15:18 V6.5.4
     * @lastEditors: csc 2023/2/20 15:18 V6.5.4
     */
    protected function cache($key, $val = '')
    {
        $args = func_get_args();
        if (count($args) === 1) {
            //读取缓存
            return \share\util\getGlobalParam($key);
        }
        \share\util\setGlobalParam($key, $val);
    }

    /**
     * @description: 提供生成拼接搜索条件和参数的方法
     * @param {array} $array 和selectByArray方法的参数格式相同
     * @param {bool} $flag 是否只生成条件不影响当前查询的参数
     * @param {string} $table $this->condition是否增加表别名，需要增加表别名则输入所需要的别名
     * @param {int} $bindCount 指定bindCount从哪里开始计数
     * @return [$this->condition, $this->conditionBindArray, $this->bindCount]
     * @author: csc 2023/5/9 15:53 V6.6
     * @lastEditors: csc 2023/5/9 15:53 V6.6
     */
    public function makeSearchCondition($array, $flag = false, $table = '', $bindCount = 0)
    {
        if ($flag) {
            $conditionBak = $this->condition;
            $conditionBindArrayBak = $this->conditionBindArray;
            $orderByBak = $this->orderBy;
            $limitBak = $this->limit;
            $bindCountBak = $this->bindCount;
            $lockBak = $this->locked;
            $this->reset();
            $this->bindCount = $bindCount;
            if (empty($bindCount)) {
                //减小bindCount冲突概率
                $this->bindCount = ord($table) * 10000 + rand(1000, 9999) * 100;
            }
        }

        foreach ($array as $arr) {
            $col = $arr[0];
            $val = $arr[1];
            $symbol = isset($arr[2]) ? $arr[2] : (is_array($val) ? "in" : "=");
            if (is_array($val)) {
                if ($symbol == "origin") {
                    $this->where($col, $val, $symbol);
                }else if ($symbol == "in") {
                    $this->whereIn($col, $val);
                } else {
                    $this->whereNotIn($col, $val);
                }
            } else {
                $this->where($col, $val, $symbol);
            }
        }

        $condition = $this->condition;
        $conditionBindArray = $this->conditionBindArray;
        $bindCount = $this->bindCount;

        if (!empty($table)) {
            $condition = array_map(function($value) use($table) {
                if (substr($value, 0, 1) == '(') {
                    return $value;
                }
                return $table . '.' . $value;
            }, $condition);
        }

        if ($flag) {
            $this->reset();
            $this->condition = $conditionBak;
            $this->conditionBindArray = $conditionBindArrayBak;
            $this->orderBy = $orderByBak;
            $this->limit = $limitBak;
            $this->bindCount = $bindCountBak;
            $this->locked = $lockBak;
        }

        return [$condition, $conditionBindArray, $bindCount];
    }

    //是否是数据混淆字段
    public function isConfusionField($field)
    {
        if (empty($this->confusionField)) {
            return false;
        }

        $confusionField = [];
        foreach ($this->confusionField as $key => $val) {
            if (is_int($key)) {
                $confusionField[] = $val;
            } else {
                $confusionField[] = $key;
            }
        }

        return in_array($field, $confusionField);
    }

    /**
     * @description: 删除混淆字段表
     * @param {array} $data 当前表的数据
     * @return void
     * @author: csc 2024/2/28 15:04 V6.8
     * @lastEditors: csc 2024/2/28 15:04 V6.8
     */
    public function deleteMappingByData($data = [])
    {
        if (empty($this->confusionField) or empty($data)) {
            return;
        }

        foreach ($this->confusionField as $key => $val) {
            if (is_int($key)) {
                $column = $val;
                $mapping = $val.'Mapping';
            } else {
                $column = $key;
                $mapping = $val.'Mapping';
            }

            foreach ($data as $row) {
                //数据中不包含加密字段
                if (!array_key_exists($column, $row)) {
                    continue;
                }
                //加密字段为空或者null
                if ($row[$column] === '' or is_null($row[$column])) {
                    continue;
                }
                $this->db->delete2MappingListWKey($mapping, 'DeColumn', $row[$column]);
            }
        }
    }

    /**
     * @description: 根据原始值获取所有已存在的加密字段的原始值（忽略大小写）
     * @param string 字段
     * @param string 字段
     * @return void
     * @author: csc 2024/5/28 17:14 V6.8.0
     * @lastEditors: csc 2024/5/28 17:14 V6.8.0
     */
    public function getDecolumnArrByDeColumn($column, $val)
    {
        if (!$this->isConfusionField($column) or empty($val)) {
            return [$val];
        }

        $mapping = array_key_exists($column, $this->confusionField) ? $this->confusionField[$column] . 'Mapping' : $column.'Mapping';
        $sql = "select * from AKCSMapping.$mapping where DeColumn = :DeColumn";
        $data = $this->execute($sql, [':DeColumn' => $val]);
        $decolumnArr = array_column($data, 'DeColumn');
        $decolumnArr[] = $val;
        return $decolumnArr;
    }

    /**
     * @description: 根据原始值获取所有已存在的加密字段的加密值（忽略大小写）
     * @param string 字段
     * @param string 字段
     * @return void
     * @author: csc 2024/5/28 17:14 V6.8.0
     * @lastEditors: csc 2024/5/28 17:14 V6.8.0
     */
    public function getEncolumnArrByDeColumn($column, $val)
    {
        if (!$this->isConfusionField($column) or empty($val)) {
            return [$val];
        }

        $mapping = array_key_exists($column, $this->confusionField) ? $this->confusionField[$column] . 'Mapping' : $column.'Mapping';
        $sql = "select * from AKCSMapping.$mapping where DeColumn = :DeColumn";
        $data = $this->execute($sql, [':DeColumn' => $val]);
        $encolumnArr = array_column($data, 'EnColumn');
        $encolumnArr[] = $this->dataEncode($column, $val);
        return $encolumnArr;
    }
}