<?php
/*
 * @description: 检测设备MAC是否在Manage下
 * @author: cj
 * @Date: 2023-03-27 18:18:17
 * @version: V6.5.4
 * @LastEditors: cj
 * @LastEditTime: 2023-03-30 11:09:23
 * @LastVersion: V6.5.4
 */

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
class CDevMacInMngCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $gApp,$cMessage;
        global $cLog;
        $user = $gApp["userAlias"];
        $userId = $gApp["userAliasId"];
        $params = ["MAC"=>""];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $db = \database\CDatabase::getInstance();
        $mngData = $db->querySList('select Grade,UUID from Account where ID = :ID', [':ID' => $userId])[0];
        $mngGrade = intval($mngData['Grade']);
        if ($mngGrade === SUPERGRADE) {
            $next();
            return;
        } elseif ($mngGrade === AREAGRADE) {
            $data = $db->querySList("select ID from DeviceForRegister where MngID = :MngID and MAC = :MAC", [":MngID"=>$userId,":MAC"=>$mac]);
        } elseif ($mngGrade === SUBDISTRIBUTOR) {
            $data = $db->querySList("select Community from PersonalDevices where MAC = :MAC", [":MAC"=>$mac]);
            if(!count($data)) {
                $data = $db->querySList("select MngAccountID from Devices where MAC = :MAC", [":MAC"=>$mac]);
                $manageGroup = $db->querySList("select ManageGroup from Account where ID = :ID", [":ID"=>$data[0]['MngAccountID']])[0]['ManageGroup'];
            } else {
                $manageGroup = $db->querySList("select ManageGroup from Account where Account = :Account", [":Account"=>$data[0]['Community']])[0]['ManageGroup'];
            }
            $installerUUID = $db->querySList("select UUID from Account where ID = :ID", [":ID"=>$manageGroup])[0]['UUID'];
            $data = $db->querySList("select ID from SubDisMngList where InstallerUUID = :InstallerUUID and DistributorUUID = :DistributorUUID", [
                ":InstallerUUID"=>$installerUUID, ':DistributorUUID' => $mngData['UUID']]);
        } else {
            $data = $db->querySList("select ID from PersonalDevices where Community = :Community and MAC = :MAC",[":Community"=>$user,":MAC"=>$mac]);
            if(!count($data)) {
                $data = $db->querySList("select ID from Devices where MngAccountID = :MngAccountID and MAC = :MAC",[":MngAccountID"=>$userId,":MAC"=>$mac]);
            }
        }
            
        $cLog->actionLog("#middle#perDevInPerMngCheck#");
        if(!count($data)) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}