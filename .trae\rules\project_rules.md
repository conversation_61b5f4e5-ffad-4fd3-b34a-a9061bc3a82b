# 接口新增规范
## 使用场景
当我需要新增一个接口时，使用该规范，我会将接口的url和请求体，响应体在chat描述中体现。

## 场景描述
{{ 使用chat描述替换该部分 }}

## 接口定义
### 文件路径
根据url定义来决定路由定义在哪个路由文件中，由接口的package和modle共同决定。例如url是`/web-server/v3/web/common/device/setDeviceConfig`,这个路由有中`web`是身份验证方式，`common`是所在的package，`device`是所在的modle，`setDeviceConfig`是接口名称，因此该接口应该定义在`web-server/pakcage/common/router/device.php`中，如果不存在`web-server/pakcage/common/router/device.php`文件，那么应该定义到`web-server/pakcage/common/router/router.php`中。

### 接口内容
接口规则范例如下：
```php
<?php

namespace package\common\router;

use \framework\BasicRouter;

class Router extends BasicRouter
{
    public function exec()
    {
        ...
        $this->setRouterName('v3', 'web', 'common', 'device', 'setDeviceConfig')
            ->setMethod('POST')
            ->addParams('DeviceIds', 'Config', 'AutopType')
            ->addRoles(RAREAGRADE, RCOMMUNITYGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('device\\setDeviceConfig');
        ...
    }
}
```
1. setRouterName方法中定义接口名，接口名忽略`web-server`，从v3开始定义。
2. setMethod定义http方法，可选值有GET, POST
3. addParams定义接口参数的key
4. addRoles定义接口权限，可选值有
 - RPERENDMROLE：单住户主账号
 - RPERENDSROLE：单住户子(从)账号
 - RCOMENDMROLE：社区主账号
 - RCOMENDSROLE：社区子(从)账号
 - ROFFSTAFFROLE：旧办公终端用户账号
 - ROFFPERSONNELROLE：旧办公终端用户账号
 - ROFF_NEW_PERSONNEL_ROLE：新办公终端用户账号
 - RPMENDMROLE： pm app账号
 - RADMINENDMROLE： admin app账号
 - RSUPERGRADE：超级管理员(super)
 - RAREAGRADE：区域管理员(dis)
 - RSUBDISTRIBUTOR：子区域管理员(sub dis)
 - RCOMMUNITYGRADE：ins社区账号
 - RPERSONGRADE: ins单住户账号
 - ROFFICEGRADE： ins办公账号
 - RPROPERTYMANAGE： pm账号
 - RADMINGRADE admin账号
5. setAuth定义接口身份验证方式，可选值有
 - web：web身份验证
 - app：app身份验证
 - false：不需要身份验证
6. setControl定义接口处理controller类，格式为`modle\\class`

## controller定义
接口的controller是客户端调用接口后处理的类，定义在对应package的`control`目录下。例如
```
$this->setRouterName('v3', 'web', 'common', 'device', 'setDeviceConfig')
    ->setMethod('POST')
    ->addParams('DeviceIds', 'Config', 'AutopType')
    ->addRoles(RAREAGRADE, RCOMMUNITYGRADE, RSUBDISTRIBUTOR)
    ->setAuth('web')
    ->setControl('device\\setDeviceConfig')
```
中的controller应该要定义在`web-server/pakcage/common/control/device/setDeviceConfig.php`中。

## controller内容
controller必须继承对应package下的CommonController,并实现一个public exec方法，例如：
```php
<?php
namespace package\common\control\device;
class SetDeviceConfig extends \package\common\control\CommonController
{
    public function exec()
    {
        ...
    }
}
```
exec方法中，使用`\framework\control\Executor`来调用各种不同的方法。
例如：
```php
<?php
namespace package\common\control\device;
use \framework\control\Executor;
class SetDeviceConfig extends \package\common\control\CommonController
{
    public function exec()
    {
        $executor = new Executor();
        $executor->execMiddle('getOperaId', true);
        $executor->execModel('device.checkDevIdsInMng', true);
        $executor->execModel('device.setDeviceConfig', true);
        $executor->execEcho(STATE_SUCCESS_EDIT);
    }
}
```
Executor支持的方法有：
1. execMiddle：调用中间件，其中所有controller都必须调用`$executor->execMiddle('getOperaId', true);`。如果是pm角色，则必须先调用` $executor->execMiddle('setPMAlias', true);`，再调用`$executor->execMiddle('getOperaId', true);`。
2. execModel：调用model。参数1为`模块名.方法名`，参数2为是否是common package，默认是false，代表调用controller所在的package下的model。

3. execEcho：调用echo。参数1为返回状态码。如果是新增的方法，返回状态码是STATE_SUCCESS_ADD，如果是修改的方法，返回状态码是STATE_SUCCESS_EDIT，如果是删除的方法，返回状态码是STATE_SUCCESS_DELETE。如果是查询的方法，返回状态码是STATE_SUCCESS_QUERY，并且第二个参数设置为`['data']`,例如`execEcho(STATE_SUCCESS_QUERY, ['data'])`

## model定义
model定义在`web-server/pakcage/{包名}/model/{模块名}`目录下。例如`$executor->execModel('device.checkDevIdsInMng', true);`中的device模块就应该定义在`web-server/pakcage/common/model/device`目录下。
model的目录结构如下：
```
web-server/pakcage/common/model/device
├── main.php
├── util.php
├── src
    │   ├── file1.php
    │   ├── file2.php
    │   └── ...
├── method
    │   ├── file1.php
    │   ├── file2.php
    │   └── ...
```
main.php内容规则如下：
```php
<?php
namespace package\community\model\device;

class Main extends \framework\BasicModel //  继承BasicModel
{
    public $allowedDao = ['account']; //  允许使用的dao,在src中使用到的dao到必须在这里声明
    use src\Add; //  引入 src/Add.php,是一个trait，必须把src下的所有文件都引入
    ...
}
```


## 判断当前角色
如果接口的访问权限支持多种角色访问，那么有时候需要在controller中判断当前角色。可以调用`\share\util\`命名空间下的方法来判断当前角色。
1. \share\util\isSuperManager()：判断是否是超级管理员
2. \share\util\isDisManager()：判断是否是区域管理员
3. \share\util\isSingleManager()：判断是否是ins单住户账号
4. \share\util\isCommunityManager()：判断是否是ins社区账号
5. \share\util\isOfficeManager()：判断是否是ins办公账号
6. \share\util\isInstallerManage()：判断是否是ins账号,包括ins单住户账号，ins社区账号，ins办公账号
7. \share\util\isPropertyManager()：判断是否是pm账号
8. \share\util\isSingleMainUser()：判断是否是单住户主账号
9. \share\util\isSingleSubUser()：判断是否是单住户子账号
10. \share\util\isCommunityMainUser()：判断是否是社区主账号
11. \share\util\isCommunitySubUser()：判断是否是社区子账号
12. \share\util\isOfficeUser()：判断是否是办公终端用户账号
