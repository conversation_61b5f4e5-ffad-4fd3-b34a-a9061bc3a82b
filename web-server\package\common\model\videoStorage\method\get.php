<?php

namespace package\common\model\videoStorage\method;

trait Get
{
    public function videoStorageSelectByKey()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->videoStorage->selectByKey($key, $val, $fields);
    }


    public function videoStorageByArray()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->videoStorage->selectByArray($array, $fields);
    }

    public function getVideoStoragesBySingles()
    {
        $params = ['Singles', 'Fields'];
        list($singles, $field) = $this->getParams($params);
        if (empty($field)) {
            $field = 'PersonalAccountUUID as SiteUUID,UUID,DevicesLimitNum,StorageDays,"Single" as Type,ExpireTime';
        }
        return $this->dao->videoStorage->selectByArray([['PersonalAccountUUID', $singles], ['ProjectType', 1]], $field);
    }

    public function getVideoStoragesByCommunities()
    {
        $params = ['Communities', 'Fields'];
        list($communities, $field) = $this->getParams($params);
        if (empty($field)) {
            $field = 'AccountUUID as SiteUUID,UUID,DevicesLimitNum,StorageDays,"Community" as Type,ExpireTime';
        }
        return $this->dao->videoStorage->selectByArray([['AccountUUID', $communities], ['ProjectType', 2]], $field);
    }

    public function getDeviceNumStr()
    {
        $params = ['DeviceNum'];
        list($deviceNum) = $this->getParams($params);

        $deviceNum = intval($deviceNum);
        $deviceNumMap = [
            0 => 'unlimited',
            1 => 'one',
            2 => 'two',
            3 => 'three'
        ];

        return $deviceNumMap[$deviceNum];
    }
}