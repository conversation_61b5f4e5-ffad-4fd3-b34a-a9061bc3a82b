<?php
error_reporting(0);

require_once "../../config/base.php";
require_once "../../config/global.php";
require_once "../funcs.php";
require_once "../funcs_office.php";

const WEB_COMM_IMPORT_COMMUNITY = 5004;
const WEB_COMM_IMPORT_FACE_PIC = 5010;
const CSMAIN_PER_DEV_MAINTANCE = 902;
const WEB_PER_UPLOAD_FACE_PIC = 1013;

$granularity = 2; //1-单住户 2-社区/办公
$accountId = 0; //installerID（单住户时）  或  communityID（社区时）
notifyConfig($granularity, $accountId);



function notifyConfig($granularity, $accountId){
    if(!$accountId){
        echo "请修改变量 granularity 和 accountId\n";
        return;
    }
	echo "$accountId\n";
    $db_tmp = \database\CDatabase::getInstance()->db;
	if($granularity==1){
		$sth = $db_tmp->prepare("select ManageGroup,Account from Account where ID = :id");
		$sth->bindParam(':id', $accountId, PDO::PARAM_INT);
		$sth->execute();
		$ret = $sth->fetch(PDO::FETCH_ASSOC);	
		if(!$ret || $ret['ManageGroup'] != $accountId){	//判断下是否确定是installer
			echo "granularity error,exit\n";
			exit;
		}
		$account = $ret['Account'];

		$sth = $db_tmp->prepare("select Account from PersonalAccount where ParentID = :id AND Role = 10");
		$sth->bindParam(':id', $accountId, PDO::PARAM_INT);
		$sth->execute();
		$accountList = $sth->fetchALL(PDO::FETCH_ASSOC);	
		foreach ($accountList as $row => $value){
			$personalAccount = $value['Account'];
			webPersonalModifyNotify(CSMAIN_PER_DEV_MAINTANCE, $personalAccount);
			webPersonalModifyNotify(WEB_PER_UPLOAD_FACE_PIC, $personalAccount);
			usleep(500000);
		}
	}
	else if($granularity==2){
		$sth = $db_tmp->prepare("select Grade from Account where ID = :id");
		$sth->bindParam(':id', $accountId, PDO::PARAM_INT);
		$sth->execute();
		$ret = $sth->fetch(PDO::FETCH_ASSOC);
		if(!$ret || ($ret['Grade'] != 21 && $ret['Grade'] != 23)){	//判断下是否确定是社区
			echo "granularity error,exit\n";
			exit;
		}
        if($ret['Grade'] == 21)
        {
            webCommunityModifyNotify(WEB_COMM_IMPORT_COMMUNITY, "", "", $accountId, "");
            webCommunityModifyNotify(WEB_COMM_IMPORT_FACE_PIC, "", "", $accountId, 0);
        }
        else
        {
            OfficeWebCommunityModifyNotify(WEB_COMM_IMPORT_COMMUNITY, "", "", $accountId, "");
        }
		usleep(1000000);
	}
	else{
		echo "choose error,exit\n";
		exit;
	}
}

