<?php
namespace basic;

abstract class CUser
{
    /**
     * @msg: 添加社区主账户，前提在房间已经创建好了
     * @service: sip,charge
     */
    public function addComMainUsreCom(
        $communityId,
        $buildId,
        $roomID,
        $email,
        $mobile,
        $first,
        $last,
        $phone,
        $phone2,
        $phone3,
        $phoneCode,
        $phoneState,
        $key,
        $cards,
        $lang,
        $tempKeyPermission = 1
    ) {
        global $cMessage;
        $active = 0;
        // V5.2 物业添加默认""
        $key = "";
        $roomName = $this->db->queryALlList("CommunityRoom", ["equation"=>[":ID"=>$roomID]])[0]["RoomName"];
        $password = \util\string\generatePw(8);
        $account = $this->db->queryAllList(
            "PersonalAccount",
            ["equation"=>[":UnitID"=>$buildId,":RoomID"=>$roomID,":ParentID"=>$communityId,":Special"=>1]]
        );
        if (count($account) == 0) {
            $cMessage->echoErrorMsg(StateRoomNotExit);
        }
        $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$communityId]])[0];
        $timeZone = $communityData["TimeZone"];
        $id = $account[0]["ID"];
        $node = $account[0]["Account"];
        // 2021-08-18 国内云的mobileNumber是默认落地号
        if ($mobile && $phone === '' && SERVER_LOCATION === 'cn') {
            include_once __DIR__."/../../notify/xiamen_phone.php";
            if (IsXiamenPhone($mobile)) {
                $phone = $mobile;
            } else {
                $phone = '0' . $mobile;
            }
        }
        $this->services["sip"]->setPhoneStatus($phoneState == 1 ? 1 : 0, $node);
        $nowTime = \util\computed\getNow();
        $chargeService = $this->services["billsysUtil"];
        $charges = $chargeService->getCharge('multiple', $communityId, [$id], PAYACTIVE);
        list($active, $expritime) = $chargeService->computedExTimeActive($charges, $id);
        $updateAccountBind = [
            ":Name"=>$first." ".$last,
            ":FirstName"=>$first,
            ":LastName"=>$last,
            ":Email"=>$email,
            ":Phone"=>$phone,
            ":Phone2"=>$phone2,
            ":Phone3"=>$phone3,
            ":PhoneCode"=>$phoneCode,
            ":ID"=>$id,
            ":Special"=>0,
            ":CreateTime"=>$nowTime,
            ":Initialization"=>0,
            ":MobileNumber"=>$mobile,
            ":PhoneStatus"=>$phoneState == 1 ? 1 : 0,
            ":TimeZone"=>$timeZone,
            ":Active"=>$active,
            ":ExpireTime"=>$expritime,
            ":Passwd"=>md5($password),
            ":CustomizeForm"=>$communityData["CustomizeForm"],
            ":TempKeyPermission"=>$tempKeyPermission,
            ":Language"=>$lang
        ];
        if ($active == 1) {
            $updateAccountBind[":ActiveTime"] = $nowTime;
            $communityTime = $communityData["CreateTime"];
            if (!$communityTime) {
                $this->log->actionLog("#model#addComMainUsreCom#add new user auto active account#update community create time");
                $this->db->update2ListWID("Account", [":ID"=>$communityId, ":CreateTime"=>$nowTime]);
            }
        }
        $this->db->update2ListWID("PersonalAccount", $updateAccountBind);

        if ($active) {
            $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":ActiveTime"=>$nowTime]);
        }

        $this->db->update2ListWKey(
            "PersonalAccountCnf",
            [":Account"=>$node,":CallType"=>$phoneState,":FreeAppCount"=>0],
            "Account"
        );
        // V5.4 #4710 创建用户不在自动生成Key
        // $this->db->insert2List("PersonalPrivateKey",[":MngAccountID"=>$communityId,":UnitID"=>$buildId,":Grade"=>3,":Type"=>0,":Code"=>$key,":Node"=>$node,":Special"=>1,":AccountID"=>$id]);


        $this->db->insert2List("PersonalBillingInfo", [":Account"=>$node]);
        $this->services["sip"]->insertSipEnable($node);
        \util\computed\setGAppData([
            "Account"=>$node,
            "Passwd"=>$password,
            "Active"=>$active,
            "ExpireTime"=>$expritime,
            "ID"=>$id
        ]);

        $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$communityId]])[0];
        $installerData = $this->db->querySList(
            'select Account from Account where ID = :ID',
            [":ID"=>$communityData["ManageGroup"]]
        )[0];
        $buildData = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$buildId])[0];


        $this->auditLog->setLog(AuditCodeAddFamilyMaster, $this->env, [$node], $node);
        $this->auditLog->setLog(AuditCodeCallTypeArray[$phoneState], $this->env, [$buildData['UnitName'], $roomName], $installerData['Account']);
        if ($email != '') {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $first." ".$last], $node);
        }
        if ($mobile != '') {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $first." ".$last], $node);
        }
    }

    public function addNewRoom($build, $roomName, $communityID, $layout = '', $layoutName = '')
    {
        global $cMessage;
        $aptNumber = $this->db->queryAllList(
            "CommunityInfo",
            ["equation"=>[":AccountID"=>$communityID]]
        )[0]["NumberOfApt"];
        $rooms = count($this->db->querySList(
            "select R.ID from CommunityUnit U join CommunityRoom R on R.UnitID = U.ID join PersonalAccount P on P.RoomID = R.ID where U.MngAccountID = :MngAccountID and P.Role = 20 and P.ParentID = :MngAccountID",
            [":MngAccountID"=>$communityID]
        ));
        if ($aptNumber<=$rooms) {
            $cMessage->echoErrorMsg(StateAddOutApt, [], [$aptNumber]);
        }


        //添加房间节点，同时生成一个虚拟账号
        $this->db->insert2List("CommunityRoom", [":UnitID"=>$build,":RoomName"=>$roomName]);
        $roomID = $this->db->lastInsertId();

        $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$communityID]])[0];
        $installerData = $this->db->querySList(
            'select Account from Account where ID = :ID',
            [":ID"=>$communityData["ManageGroup"]]
        )[0];
        $buildData = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$build])[0];

        $sip = $this->services["sip"]->assignSip($communityData["Account"], '2');
        if ($sip == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }
        //生成sip群组
        $sipGroup = $this->services["sip"]->getSipGroup($communityID);
        //生成Sip密码
        $sipPw = \util\string\generatePw(12);
        if ($sipGroup == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccountGroup);
        }

        $this->services["sip"]->insertSipGroup($sipGroup, $sip);

        $uuid = \util\string\uuid();
        $this->db->insert2List(
            "PersonalAccount",
            [":Account"=>$sip,":Passwd"=>"",":Role"=>20,":ParentID"=>$communityID,":Initialization"=>0,
            ":UnitID"=>$build,":SipAccount"=>$sip,":SipPwd"=>$sipPw,":Special"=>1,":RoomID"=>$roomID,":Active"=>0,":UUID"=>$uuid]
        );

        $ownerID = $this->db->lastInsertId();
        $options = ["type"=>6,
                    "node"=>"1.".$build.".0.0.".$ownerID,//主账号id
                    "group"=>$sipGroup,
                    "community"=>$communityID,
                    "enableGroup"=>0,//室内机群响铃
                    "sip"=>$sip,
                    "passwd"=>$sipPw];
        $res = $this->services["sip"]->add2Freeswish($sip, $options);
        $bindArray = [":Account"=>$sip,":FreeAppCount"=>0];
        $this->db->insert2List("PersonalAccountCnf", $bindArray);
        // 插入一条billing
        $this->db->insert2List("PersonalBillingInfo", [":Account"=>$sip]);
        $this->auditLog->setLog(AuditCodeSetAptNumber, $this->env, [$buildData["UnitName"], $roomName], $installerData["Account"]);
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        \util\computed\setSmartHomeTask(['Type' => 6, 'Key' => $sip, 'Info' => json_encode(['layout' => $layout, 'apt'=>$roomName]), 'RoomSip'=>$sip]);
        // if ($layout != '') {
        //     \util\smartHome\cacheLayout($layoutName, $sip);
        // }
        return $ownerID;
    }

    public function modifyControl($communityId, $id, $buildId, $roomID, $first, $last, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, $cards, $email, $mobile, $tempKeyPermission = 1)
    {
        $roomName = $this->db->queryALlList("CommunityRoom", ["equation"=>[":ID"=>$roomID]])[0]["RoomName"];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $newPhoneStatus = $phoneState == 1 ? 1 : 0;
        $email = $email == "" ? null : $email;
        $oldData = $this->db->querySList('select Email,MobileNumber from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Name"=>$first." ".$last,":FirstName"=>$first,":LastName"=>$last,":Phone"=>$phone,":Phone2"=>$phone2,":Phone3"=>$phone3,":PhoneCode"=>$phoneCode,
                        ":RoomID"=>$roomID,":PhoneStatus"=>$newPhoneStatus,":UnitID"=>$buildId,":Email"=>$email,":MobileNumber"=>$mobile,":TempKeyPermission"=>$tempKeyPermission]);

        $oldCnf = $this->db->querySList('select CallType from PersonalAccountCnf where Account = :Account', [":Account"=>$userData[0]["Account"]])[0];

        $this->db->update2ListWKey("PersonalAccountCnf", [":Account"=>$userData[0]["Account"],":CallType"=>$phoneState], "Account");
        $this->services["sip"]->phoneStatusCheck($userData[0]["PhoneStatus"], $newPhoneStatus, $userData[0]["Account"]);

        $keyID = $this->db->queryAllList("PersonalPrivateKey", ["equation"=>[":Node"=>$userData[0]["Account"],":Special"=>1]])[0]["ID"];
        $this->db->update2ListWID("PersonalPrivateKey", [":UnitID"=>$buildId,":Code"=>$key,":ID"=>$keyID]);
        $this->auditLog->setLog(AuditCodeEditFamilyMaster, $this->env, [$userData[0]["Account"]], $userData[0]["Account"]);

        if ($oldCnf["CallType"] != $phoneState) {
            $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$communityId]])[0];
            $installerData = $this->db->querySList('select Account from Account where ID = :ID', [":ID"=>$communityData["ManageGroup"]])[0];
            $buildData = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$buildId])[0];

            $this->auditLog->setLog(AuditCodeCallTypeArray[$phoneState], $this->env, [$buildData['UnitName'], $roomName], $installerData['Account']);
        }
        if ($oldData["Email"] != $email) {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $first." ".$last], $userData[0]['Account']);
        }
        if ($oldData["MobileNumber"] != $mobile) {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $first." ".$last], $userData[0]['Account']);
        }
    }

    public function deleteComUserControl($userId, $ids)
    {
        global $cMessage;
        foreach ($ids as $id) {
            $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id,":Role"=>COMENDMROLE,":ParentID"=>$userId]]);
            $this->log->actionLog('#model#user#deleteComUser#data='.json_encode($data));

            if (count($data) == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $subs = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ParentID"=>$data[0]["ID"],":Role"=>COMENDSROLE]]);
            // 收集从账号sip准备删除和回收
            $sips = [];
            // 收集账号去除token
            $accounts = [$data[0]["Account"]];
            // 收集sip准备删除通话记录
            $callSips = [$data[0]["Account"]];
            foreach ($subs as $sub) {
                $temdata = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$sub["ID"]]])[0];
                $this->db->delete2ListWID("PersonalAccount", $sub["ID"]);
                // 收集从账号sip
                array_push($sips, $temdata["SipAccount"]);
                array_push($callSips, $temdata["SipAccount"]);
            }
            if (count($sips)!=0) {
                $this->services["sip"]->del2Freeswish($sips);
            }

            $node = $data[0]["Account"];
            // 删除tempkey
            $tmpKeyData = $this->db->queryAllList("PersonalAppTmpKey", ["equation"=>[":Node"=>$node]]);
            $keyID = [];
            foreach ($tmpKeyData as $val) {
                array_push($keyID, $val["ID"]);
            }
            //删除 tmpkey 二维码
            // delTmpKeyQrCode($node,implode(";", $keyID));
            $this->db->delete2ListWKey("PersonalAppTmpKey", "Node", $node);

            //删除alarm
            $alarmTable = "Alarms";
            $this->db->delete2ListWKey($alarmTable, "Node", $node);

            //视频录制
            $this->db->delete2ListWKey("VideoLength", "Node", $node);
            $this->db->delete2ListWKey("VideoList", "Node", $node);
            $this->db->delete2ListWKey("VideoSchedule", "Node", $node);

            // bill
            $this->db->delete2ListWKey("PersonalBillingInfo", "Account", $node);

            // 删除log
            $this->db->delete2ListWKey("PersonalLogs", "AccountID", $data[0]["ID"]);

            // 收集设备的SIP,删除capture
            $this->services["captureUtil"]->deleteCaptureRowWKey("Node", $node);
            $devicsData = $this->db->queryAllList("Devices", ["equation"=>[":Node"=>$node]]);
            foreach ($devicsData as $val) {
                $this->services["captureUtil"]->deleteCaptureRowWKey("MAC", $val["MAC"]);
                array_push($callSips, $val["SipAccount"]);
            }

            // 删除通话记录
            foreach ($callSips as $val) {
                $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CallerID", $val);
                $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CalleeID", $val);
            }
            $sipGroup = $this->db->queryAllList("SipGroup2", ["equation"=>[":Account"=>$node]])[0]["SipGroup"];
            $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CalleeID", $sipGroup);

            // 删除token
            foreach ($accounts as $val) {
                $this->db->delete2ListWKey("Token", "Account", $val);
            }

            $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Special"=>1,":NFCCode"=>"",":BLECode"=>"",":Email"=>null,":MobileNumber"=>null,":Name"=>"",":FirstName"=>"",":LastName"=>"",":Phone"=>"",":Phone2"=>"",":Phone3"=>""]);

            $this->db->update2ListWKey("PersonalAccountCnf", [":Account"=>$data[0]["Account"],":AlreadySendEmail"=>0,":FreeAppCount"=>0], "Account");

            $keyID = $this->db->queryAllList("PersonalPrivateKey", ["equation"=>[":AccountID"=>$data[0]["ID"],":Special"=>1]])[0]["ID"];
            $this->db->update2ListWID("PersonalPrivateKey", [":ID"=>$keyID,":Code"=>""]);

            $this->db->delete2ListWID("PersonalPrivateKey", $keyID);
            $this->db->delete2ListWKey("PersonalRfcardKey", "Node", $node);

            // xiaolong 2021-03-17 删除社区用户要同时删除营销活动使用记录，否则这个房间新增用户将无法享受营销活动
            $this->db->delete2ListWKey("UserActivityUsed", "PersonalAccountID", $id);
            // xiaolong 2021-06-02 删除社区用户删除对应权限组关联
            $this->db->delete2ListWKey('AccountAccess', 'Account', $node);

            // 删除主账户message
            $this->db->delete2ListWKey('MessageAccountList', 'Account', $node);

            // V6.1 社区用户删除对应权限组，新版key和card等
            $this->db->delete2ListWKey('AccountAccess', 'Account', $node);
            $this->db->delete2ListWKey('CommPerRfKey', 'Account', $node);
            $this->db->delete2ListWKey('CommPerPrivateKey', 'Account', $node);
            // 删除用户自己的权限组
            $userGroups = $this->db->querySList('select ID from UserAccessGroup where Account = :Account', [":Account"=>$node]);
            $this->db->delete2ListWKey('UserAccessGroup', 'Account', $node);
            foreach ($userGroups as $userGroup) {
                $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $userGroup['ID']);
            }

            // 删除从账户message，权限组，新版key和card等,自己权限组
            foreach ($subs as $sub) {
                $this->db->delete2ListWKey('MessageAccountList', 'Account', $sub['Account']);
                $this->db->delete2ListWKey('AccountAccess', 'Account', $sub['Account']);
                $this->db->delete2ListWKey('CommPerRfKey', 'Account', $sub['Account']);
                $this->db->delete2ListWKey('CommPerPrivateKey', 'Account', $sub['Account']);
                $userGroups = $this->db->querySList('select ID from UserAccessGroup where Account = :Account', [":Account"=>$sub['Account']]);
                $this->db->delete2ListWKey('UserAccessGroup', 'Account', $sub['Account']);
                foreach ($userGroups as $userGroup) {
                    $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $userGroup['ID']);
                }
            }

            $this->auditLog->setLog(AuditCodeDeleteFamilyMaster, $this->env, [$node]);
        }
    }

    public function getComInfoControl($id)
    {
        $data = $this->db->querySList("
            select P.*,Pf.CallType,R.RoomName,U.UnitName,Pk.Code as PKey from PersonalAccount P 
            join PersonalAccountCnf Pfon P.Account = Pf.Account 
            left join PersonalPrivateKey Pk on Pk.Node = P.Account 
            join CommunityRoom R on P.RoomID = R.ID join CommunityUnit U on U.ID = P.UnitID
            where P.ID = :ID and P.Role = 20 and Pk.Special = 1", [":ID"=>$id]);
        if (count($data) == 0) {
            return false;
        }
        $data = $data[0];
        unset($data["Passwd"]);
        unset($data["SipPwd"]);

        $rfData = $this->db->queryAllList("PersonalRfcardKey", ["equation"=>[":Node"=>$data["Account"]]]);
        foreach ($rfData as &$val) {
            $val = ["text"=>$val["Code"],"isModify"=>$val["ID"]];
        }
        $data["RfCard"] = $rfData;
        return $data;
    }
}
