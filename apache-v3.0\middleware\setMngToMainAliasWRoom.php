<?php
/*
 * @Description: 根据房間ID，管理员设置代理为主账户
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
class CSetMngToMainAliasWRoom implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp,$cMessage;
        $communityId = $gApp['userAliasId'];
        $params = ["RoomID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["RoomID"];
        
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setMngToMainAliasWSubId#");
        $data = $db->querySList("select ID,Account from PersonalAccount where RoomID = :ID and ParentID = :ParentID",
        [":ID"=>$id, ":ParentID"=>$communityId]);
        if(count($data) == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $data = $data[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}