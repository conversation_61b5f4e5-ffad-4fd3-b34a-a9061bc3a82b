<?php

namespace model\userData;

include_once __DIR__ . "/util/devFunc.php";

trait update
{
    public function changePw()
    {
        $params = [
            "PassWd" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $password = $params["PassWd"];
        $userId = $params["ID"];
        if (!$password) {
            $password = \util\string\generatePw(8);
        }

        $userData = $this->db->querySList(
            "select Email,Account,Role,UUID from PersonalAccount where ID=:ID",
            [":ID" => $userId]
        )[0];
        $account = $userData["Account"];
        $email = $userData["Email"];

        // 如果是pmapp的用户，需要取对应pm的邮箱
        if ($userData['Role'] == PMENDMROLE) {
            $pmMap = $this->db->querySList('select * from PmAccountMap where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $userData['UUID']])[0];
            $pmAccountData = $this->db->querySList('select Email from Account where UUID = :UUID', [':UUID' => $pmMap['AccountUUID']])[0];
            $email = $pmAccountData['Email'];
        }
        $this->log->actionLog("#model#userData.changePw#account=$account;email=$email");
        $this->db->update2ListWID("PersonalAccount", [":ID" => $userId, ":Passwd" => md5($password)]);

        // 更改数据流
        \util\computed\setGAppData(["Account" => $account, "Email" => $email, "Passwd" => $password]);
        $this->log->endUserLog(1, null, "modify password");
        $this->auditLog->setLog(AuditCodeSelfPassword, $this->env, [], $account);
    }

    public function changeTimeZone()
    {
        $params = [
            "TimeZone" => "",
            "CustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["TimeZone"];
        $customizeForm = $params["CustomizeForm"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#userData.changeTimeZone#timeZone=$timeZone;customizeForm=$customizeForm;userId=$userId");
        $oldTimeZone = $this->db->querySList(
            'select TimeZone from PersonalAccount where ID = :ID',
            [":ID" => $userId]
        )[0]['TimeZone'];
        $this->db->update2ListWID(
            "PersonalAccount",
            [":ID" => $userId, ":TimeZone" => $timeZone, ":CustomizeForm" => $customizeForm]
        );
        $this->log->endUserLog(1, null, "modify timezone");
        if ($oldTimeZone != $timeZone) {
            $account = $this->db->querySlist('select Account from PersonalAccount where ID=:ID', [':ID' => $userId])[0]['Account'];
            $this->auditLog->setLog(AuditCodeSelfTime, $this->env, ["GTM $timeZone"], $account);
        }
    }

    public function changePerName()
    {
        $params = [
            "userAliasId" => "",
            "Name" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $userId = $params["userAliasId"];
        $this->db->update2ListWID("PersonalAccount", [":ID" => $userId, ":Name" => $name]);
    }

    public function changePerData()
    {
        $params = [
            "Name" => "",
            "Phone" => "",
            "Phone2" => "",
            "Phone3" => "",
            "PhoneCode" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $userId = $params["userAliasId"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $user = $data["Account"];
        // $type = in_array($data["Role"], COMROLE) ? 1 : 0;

        // $deviceTable = $type == 0 ? "PersonalDevices" : "Devices";
        // if($enableIpDirect != $data["EnableIpDirect"]){
        //     $this->service["sip"]->setIpDirect($deviceTable,$user,$enableIpDirect);
        // }

        $this->db->update2ListWID(
            "PersonalAccount",
            [
                ":Name" => $name, ":Phone" => $phone, ":Phone2" => $phone2, ":Phone3" => $phone3,
                ":PhoneCode" => $phoneCode, ":ID" => $userId
            ]
        );
    }

    /**
     * @msg: 设置通话记录已读
     * @service: callHistoryUtil
     */
    public function setPhoneRead()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $tables = $this->services["callHistoryUtil"]->getCallHistoryTables();
        $count = 0;
        foreach ($tables as $table) {
            if ($count > 1) {
                break;
            }
            $this->db->update2ListWKey($table["Name"], [":Status" => 1, ":CalleeID" => $user], "CalleeID");
            $count++;
        }
    }

    /**
     * @msg: 设置motion记录已读
     * @service: callHistoryUtil
     */
    public function setMotionRead()
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $tableName = $this->services["captureUtil"]->GetCaptureTableInId($id, 0);
        $this->db->update2ListWID($tableName, [":ID" => $id, ":Status" => 1]);
    }

    public function modifyBill()
    {
        $params = [
            "ID" => "",
            "BillingTitle" => "",
            "Contactor" => "",
            "Street" => "",
            "City" => "",
            "Postcode" => "",
            "Country" => "",
            "TelePhone" => "",
            "Fax" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $title = $params["BillingTitle"];
        $contactor = $params["Contactor"];
        $street = $params["Street"];
        $city = $params["City"];
        $postcode = $params["Postcode"];
        $country = $params["Country"];
        $telePhone = $params["TelePhone"];
        $fax = $params["Fax"];

        $this->db->update2ListWID(
            "PersonalBillingInfo",
            [
                ":ID" => $id,
                ":BillingTitle" => $title,
                ":Contactor" => $contactor,
                ":Street" => $street,
                ":City" => $city,
                ":Postcode" => $postcode,
                ":Country" => $country,
                ":TelePhone" => $telePhone,
                ":Fax" => $fax
            ]
        );
    }

    public function setCall()
    {
        $params = [
            "EnableRobinCall" => "",
            "RobinCallTime" => "",
            "RobinCallVal" => "",
            "EnableIpDirect" => "",
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $enableRobinCall = $params["EnableRobinCall"];
        $robinCallTime = $params["RobinCallTime"];
        $robinCallVal = $params["RobinCallVal"];
        $enableIpDirect = $params["EnableIpDirect"];
        $data = $this->db->querySList(
            "select Role,EnableIpDirect from PersonalAccount where ID=:ID",
            [":ID" => $userId]
        )[0];
        $role = $data["Role"];
        $roleType = in_array($role, PERROLE) ? 1 : 0;
        $enableRobinCall == 1 ? 1 : 0;
        foreach ($robinCallVal as &$value) {
            $tmpValue = explode("-", $value);
            $value = ["ID" => intval($tmpValue[1]), "Type" => intval($tmpValue[0]), "Pre" => $roleType];
        }
        $robinCallVal = json_encode((object)$robinCallVal);

        $deviceTable = $roleType == 1 ? "PersonalDevices" : "Devices";
        if ($enableIpDirect != null) {
            if ($enableIpDirect != $data["EnableIpDirect"]) {
                $this->services["sip"]->setIpDirect($deviceTable, $user, $enableIpDirect);
            }
        }
        if ($enableIpDirect != null && $robinCallTime != null) {
            $this->db->update2ListWKey(
                "PersonalAccountCnf",
                [
                    ":EnableRobinCall" => $enableRobinCall,
                    ":RobinCallTime" => $robinCallTime,
                    ":RobinCallVal" => $robinCallVal,
                    ":Account" => $user
                ],
                "Account"
            );
            $this->db->update2ListWID("PersonalAccount", [":ID" => $userId, ":EnableIpDirect" => $enableIpDirect]);
        } elseif ($enableIpDirect == null && $robinCallTime != null) {
            $this->db->update2ListWKey(
                "PersonalAccountCnf",
                [":EnableRobinCall" => $enableRobinCall, ":RobinCallTime" => $robinCallTime, ":RobinCallVal" => $robinCallVal, ":Account" => $user],
                "Account"
            );
            $this->db->update2ListWID("PersonalAccount", [":ID" => $userId]);
        }
    }

    public function setMotion()
    {
        $params = [
            "EnableMotion" => "",
            "MotionTime" => "",
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $enableMotion = $params["EnableMotion"];
        $motionTime = $params["MotionTime"];
        // $enableMotion = $enableMotion == 1 ? 1 : 0;
        $this->db->update2ListWKey(
            "PersonalAccountCnf",
            [":Account" => $user, ":EnableMotion" => $enableMotion, ":MotionTime" => $motionTime],
            "Account"
        );
    }

    /**
     * @msg: 社区用户初始化
     * @service: sip
     */
    public function setInit()
    {
        $params = [
            "PIN" => "",
            // "CallType" => "",
            // "Phone" => "",
            // "Phone2" => "",
            // "Phone3" => "",
            // "PhoneCode" => "",
            "Password" => "",
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $pin = $params["PIN"];
        // $callType = $params["CallType"];
        // $phone = $params["Phone"];
        // $phone2 = $params["Phone2"];
        // $phone3 = $params["Phone3"];
        // $phoneCode = $params["PhoneCode"];
        $password = $params["Password"];
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        // $phoneStatus = $callType == 1 ? 1 : 0;
        // if ($myData["PhoneStatus"] != $phoneStatus) {
        //     $result = $this->services["sip"]->setPhoneStatus($phoneStatus, $user);
        // }
        $room = "";
        if ($password) {
            $this->db->update2ListWKey(
                "PersonalAccount",
                [
                    // ":Phone" => $phone,
                    // ":Phone2" => $phone2,
                    // ":Phone3" => $phone3,
                    // ":PhoneCode" => $phoneCode,
                    ":Passwd" => md5($password),
                    // ":PhoneStatus" => $phoneStatus,
                    ":Account" => $user,
                    ":Initialization" => 1
                ],
                "Account"
            );
        } else {
            $this->db->update2ListWKey(
                "PersonalAccount",
                [
                    // ":Phone" => $phone,
                    // ":Phone2" => $phone2,
                    // ":Phone3" => $phone3,
                    // ":PhoneCode" => $phoneCode,
                    // ":PhoneStatus" => $phoneStatus,
                    ":Account" => $user,
                    ":Initialization" => 1
                ],
                "Account"
            );
        }

        // $this->db->update2ListWKey("PersonalAccountCnf", [":CallType" => $callType, ":Account" => $user], "Account");

        // $oldCnf = $this->db->querySList(
        //     'select CallType from PersonalAccountCnf where Account = :Account',
        //     [':Account' => $user]
        // )[0];

        //V5.4 #4710 初始化改为插入
        if ($pin) {
            \model\userData\initInsert($user, $userId, $pin);
        }


        $email = $myData["Email"];
        // 如果是pmapp的用户，需要取对应pm的邮箱
        if ($myData['Role'] == PMENDMROLE) {
            $pmMap = $this->db->querySList('select * from PmAccountMap where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $myData['UUID']])[0];
            $pmAccountData = $this->db->querySList('select Email from Account where UUID = :UUID', [':UUID' => $pmMap['AccountUUID']])[0];
            $email = $pmAccountData['Email'];
        }

        if ($password) {
            \util\computed\setGAppData(["Email" => $email, "Passwd" => $password, "Account" => $user]);
        } else {
            \util\computed\setGAppData(["Email" => $email, "Account" => $user]);
        }
        $this->log->endUserLog(1, null, "modify password");

        // if ($oldCnf["CallType"] != $callType && $callType != "") {
        //     $communityData = $this->db->queryAllList("Account", ["equation" => [":ID" => $myData["ParentID"]]])[0];
        //     $installerData = $this->db->querySList(
        //         'select Account from Account where ID = :ID',
        //         [":ID" => $communityData["ManageGroup"]]
        //     )[0];
        //     $buildData = $this->db->querySList(
        //         'select UnitName from CommunityUnit where ID = :ID',
        //         [":ID" => $myData["UnitID"]]
        //     )[0];
        //     $roomData = $this->db->querySList(
        //         "select RoomName from CommunityRoom where ID = :ID",
        //         [":ID" => $myData["RoomID"]]
        //     );

        //     $this->auditLog->setLog(
        //         AuditCodeCallTypeArray[$callType],
        //         $this->env,
        //         [
        //             $buildData['UnitName'],
        //             $roomData['RoomName']
        //         ],
        //         $installerData['Account']
        //     );
        // }
    }

    /**
     * @msg: 社区多功能修改资料接口
     * @service: sip
     */
    public function setData()
    {
        $params = [
            "PIN" => "",
            "Phone" => "",
            "Phone2" => "",
            "Phone3" => "",
            "PhoneCode" => "",
            "FirstName" => "",
            "LastName" => "",
            "Name" => "",
            "EnableIpDirect" => "",
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $pin = $params["PIN"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $name = $params["Name"];
        $enableIpDirect = $params["EnableIpDirect"];
        $this->log->actionLog("#model#userData#setData#params=" . json_encode($params));
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        if ($pin !== null) {
            // 判断是否为从账号
            $role = $this->db->querySList("select Role, ParentID from PersonalAccount where ID = :ID", [":ID" => $userId])[0];
            if ($role['Role'] == '21') {
                $mainId = $role['ParentID'];
                $mainAccount = $this->db->querySList("select Account from PersonalAccount where ID = :ParentID", [":ParentID" => $mainId])[0]['Account'];
            } else {
                $mainId = $userId;
                $mainAccount = $user;
            }
            // 新旧社区
            $isNew = $this->db->querySList('select CI.IsNew from CommunityInfo CI join PersonalAccount P on CI.AccountID=P.ParentID
            where P.ID=:ID', [':ID' => $mainId])[0]['IsNew'];

            /**
             * 2021/1/10 zyc 
             * TOOD：这里办公两个都是主账号且没有isNew，也走CommPerPrivateKey表，
             * 后续有新增用户类型要注意判断语句
             */
            if ($isNew || in_array($role['Role'], OFFROLE)) {
                $data = $this->db->queryAllList("CommPerPrivateKey", ["equation" => [":Account" => $mainAccount, ":Special" => 1]])[0];
                if ($data) {
                    $this->db->update2ListWID("CommPerPrivateKey", [":Code" => $pin, ":ID" => $data["ID"]]);
                } else {
                    \model\userData\initInsert($mainAccount, $mainId, $pin);
                }
            } else {
                $data = $this->db->queryAllList("PersonalPrivateKey", ["equation" => [":Node" => $mainAccount, ":Special" => 1]])[0];
                if ($data) {
                    $this->db->update2ListWID("PersonalPrivateKey", [":Code" => $pin, ":ID" => $data["ID"]]);
                } else {
                    \model\userData\initInsert($mainAccount, $mainId, $pin);
                }
            }
        }

        $bindArray = [":ID" => $userId];
        if ($phone !== null) {
            $bindArray[":Phone"] = $phone;
        }
        if ($phone2 !== null) {
            $bindArray[":Phone2"] = $phone2;
        }
        if ($phone3 !== null) {
            $bindArray[":Phone3"] = $phone3;
        }
        if ($phoneCode !== null) {
            $bindArray[":PhoneCode"] = $phoneCode;
        }
        if ($firstName !== null && $lastName !== null) {
            $bindArray[":FirstName"] = $firstName;
            $bindArray[":LastName"] = $lastName;
            $bindArray[":Name"] = $name;
        }

        if ($enableIpDirect !== null) {
            $bindArray[":EnableIpDirect"] = $enableIpDirect;
            $this->services["sip"]->setIpDirect("Devices", $user, $enableIpDirect);
        }
        $this->db->update2ListWID("PersonalAccount", $bindArray);
        if ($phoneCode == null && $phone3 == null && $phone2 == null && $phone == null) {
            $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
            $phone = $myData["Phone"];
            $phone2 = $myData["Phone2"];
            $phone3 = $myData["Phone3"];
            $phoneCode = $myData["PhoneCode"];
            \util\computed\setGAppData(["Phone" => $phone, "Phone2" => $phone2, "Phone3" => $phone3, "PhoneCode" => $phoneCode]);
        }
        \util\computed\setGAppData(["Account" => $user]);
    }

    public function setPerPhone()
    {
        $params = [
            "Phone" => "",
            "Phone2" => "",
            "Phone3" => "",
            "PhoneCode" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $userId = $params["userAliasId"];
        $this->db->update2ListWID("PersonalAccount", [
            ":Phone" => $phone, ":Phone2" => $phone2, ":Phone3" => $phone3,
            ":PhoneCode" => $phoneCode, ":ID" => $userId
        ]);
    }


    public function setLang()
    {
        global $lang;
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $oldLanguage = $this->db->querySList(
            "select Language from PersonalAccount where ID = :ID",
            [":ID" => $userId]
        )[0]["Language"];
        if ($lang != $oldLanguage) {
            $this->db->update2ListWID("PersonalAccount", [":ID" => $userId, ":Language" => $lang]);
        }
    }
}
