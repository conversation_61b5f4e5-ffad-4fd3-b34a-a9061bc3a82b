<?php
namespace model\userData;
trait afterUpdate {
    function afterPerChangeTimeZone () {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#userData.afterPerChangeTimeZone#userId=$userId");
    }

    function afterComChangeTimeZone () {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#userData.afterComChangeTimeZone#userId=$userId");
    }

    /**
     * @msg: 更新个人用户信息配置
     */
    function afterPerUserEditCnf () {

    }
    /**
     * @msg: 更新社区用户信息配置
     */
    function afterComUserEditCnf () {

    }
    /**
     * @msg: 更新个人联动配置
     */
    function afterPerFamilyEditCnf () {

    }
    
    /**
     * @msg: 更新社区联动配置
     */
    function afterComFamilyEditCnf () {
        
    }

    /**
     * @msg: 更新个人pirvate Key
     */
    function afterPerEditKey () {
        $params = [
			"ID"=>"",
			"Type"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$id = $params["ID"];
		$type = $params["Type"];
		$data = $this->db->queryAllList("PersonalPrivateKey",["equation"=>[":ID"=>$id]])[0];
		$account = $data["Node"];
		$msgID = $data["MngAccountID"];
        $unit = $data["UnitID"];
        // personnalUpdateNodeNotify($account);
    }

    /**
     * @msg: 更新社区pirvate Key
     */
    function afterComEditKey () {
        $params = [
			"ID"=>"",
			"Type"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$id = $params["ID"];
		$type = $params["Type"];
		$data = $this->db->queryAllList("PersonalPrivateKey",["equation"=>[":ID"=>$id]])[0];
		$account = $data["Node"];
		$msgID = $data["MngAccountID"];
        $unit = $data["UnitID"];
        // communityUpdateNodeNotify($account,$msgID,$unit,1);
    }

    /**
     * @msg: 更新个人rfCard
     */
    function afterPerEditCard () {
        $params = [
			"ID"=>"",
			"Type"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$id = $params["ID"];
		$type = $params["Type"];
		$data = $this->db->queryAllList("PersonalRfcardKey",["equation"=>[":ID"=>$id]])[0];
		$account = $data["Node"];
		$msgID = $data["MngAccountID"];
        $unit = $data["UnitID"];
        // personnalUpdateNodeNotify($account);
    }

    /**
     * @msg: 更新社区rfCard
     */
    function afterComEditCard () {
        $params = [
			"ID"=>"",
			"Type"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$id = $params["ID"];
		$type = $params["Type"];
		$data = $this->db->queryAllList("PersonalRfcardKey",["equation"=>[":ID"=>$id]])[0];
		$account = $data["Node"];
		$msgID = $data["MngAccountID"];
        $unit = $data["UnitID"];
        // communityUpdateNodeNotify($account,$msgID,$unit,1);
    }

    /**
     * @msg: 更新个人设备
     * @param: ID:设备id
     */
    function afterPerEditDev () {
    }

     /**
     * @msg: 更新社区设备
     */
    function afterComEditDev () {
    }

    function afterPerAddDev () {
    }

    function afterPerDelDev () {
        
    }
}