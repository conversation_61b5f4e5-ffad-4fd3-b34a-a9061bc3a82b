<?php
const CAPTURE_TYPE =  ["0"=>MSGTEXT["call"],
    "1"=>MSGTEXT["unlockTempKey"],
    // PIN
    "2"=>MSGTEXT["unlockPrivateKey"],
    // RFCARD
    "3"=>MSGTEXT["unlockCard"],
    "4"=>MSGTEXT["unlockFACE"],
    "5"=>MSGTEXT["unlockApp"],
    "6"=>MSGTEXT["unlockApp"],
    "7"=>MSGTEXT["unlockIndoor"],
    "8"=>MSGTEXT["unlockGuardPhone"],
    "9"=>MSGTEXT['auditManuallyUnlock'],
    "105"=>MSGTEXT['auditManuallyLock'],
    "10"=>MSGTEXT['automaticallyUnlock'],
    "99"=>"--",
    "100"=>MSGTEXT["unlockNFC"],
    "101"=>MSGTEXT["unlockBluetooth"],
    "102"=>MSGTEXT["unlockApp"],
    "103"=>MSGTEXT["call"],
    '200' => MSGTEXT["autoLock"],
    '201' => MSGTEXT["lockApp"],
    '202' => MSGTEXT['open'],
    '203' => MSGTEXT['close']];
const CAPTURE_TYPE_HOME = ["0"=>MSGTEXT["call"],
"1"=>MSGTEXT["unlockTempKey"],
// PIN
"2"=>MSGTEXT["unlockPrivateKey"],
// RFCARD
"3"=>MSGTEXT["unlockCard"],
"4"=>MSGTEXT["unlockFACE"],
"5"=>MSGTEXT["unlockAppHome"],
"6"=>MSGTEXT["unlockAppHome"],
"7"=>MSGTEXT["unlockIndoor"],
"8"=>MSGTEXT["unlockGuardPhone"],
"9"=>MSGTEXT['auditManuallyUnlock'],
"105"=>MSGTEXT['auditManuallyLock'],
"10"=>MSGTEXT['automaticallyUnlock'],
"99"=>"--",
"100"=>MSGTEXT["unlockNFC"],
"101"=>MSGTEXT["unlockBluetooth"],
"102"=>MSGTEXT["unlockAppHome"],
"103"=>MSGTEXT["call"],
'200' => MSGTEXT["autoLock"],
'201' => MSGTEXT["lockApp"],
'202' => MSGTEXT['open'],
'203' => MSGTEXT['close']];
const MESSAGE_REVICER_TYPE = [MSGTEXT['indoorAndApp'],MSGTEXT['indoorMonitorOnly'],MSGTEXT['appOnly']];

const DEVICE_TYPE = [];
const CALL_TYPE = [MSGTEXT['smartPlusIndoor'], MSGTEXT['phoneIndoor'], MSGTEXT['smartPlusIndoorBackup'], MSGTEXT['smartPlusBackup'], MSGTEXT['indoorPhoneBackup'], MSGTEXT['indoorSmartPlusPhone']];
// log用
const ROLE_NAME_LIST = [
    RSUPERGRADE=>"super manager",
    RAREAGRADE=>"distributor",
    RCOMMUNITYGRADE=>"community manager",
    RPERSONGRADE=>"personal manager",
    RPROPERTYMANAGE=>"property manage",
    RPERENDMROLE=>"personal main role",
    RPERENDSROLE=>"personal sub role",
    RCOMENDMROLE=>"community main role",
    RCOMENDSROLE=>"community sub role"
];

//导入人脸错误信息
const FACE_ERROR_CODE = [
    //系统错误，包括解码失败，重命名图片失败等
    '-1' => StateFaceImportErrorSystem,
    //人脸的旋转角度 或俯视、仰视、侧脸的角度过大
    '100' => StateFaceImportErrorView,
    //检测到口罩
    '101' => StateFaceImportErrorWearMask,
    //人脸分辨率太小
    '102' => StateFaceImportErrorLowResolution,
    //人脸格式错误
    '103' => StateFaceImportErrorWrongFormat,
    //图片中未检测到人脸
    '104' => StateFaceImportErrorNoFace,
    //图片大于10MB
    '105' => StateFaceImportErrorFileLarge,
    //图片中人脸过大
    '106' => StateFaceImportErrorFaceLarge,
    //图片中人脸过小
    '107' => StateFaceImportErrorFaceSmall,
    //图片中人脸不止1个
    '108' => StateFaceImportErrorMultiFaces,
    //图片命名不规范
    '109' => StateFaceImportErrorWrongName,
    //住户名字为空
    '110' => StateFaceImportErrorEmptyName,
    //无用户信息
    '111' => StateFaceImportErrorNoAccountInfo,
    //账户未激活
    '112' => StateFaceImportErrorAccountInactive,
];
