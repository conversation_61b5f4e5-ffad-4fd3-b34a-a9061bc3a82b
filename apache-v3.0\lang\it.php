<?php
  const MSGTEXT = [ 

"accountExits"=>"questo account esiste già",
"accountNotExit"=>"L'account non esiste",
"accountIncorrect"=>"Nome utente o password errati",
"accountNIncorrect"=>"Account non valido",
"activeEmpty"=>"È richiesto il valore attivo",
"addFail"=>"Aggiunta non riuscita",
"addSuccess"=>"Aggiungimento è riuscito.",
"addSuccessPw"=>"Aggiunta di successo, la password è \\",
"addTmpKeyFail"=>"Aggiungi la chiave Temp non riuscita, riprova",
"aptDuplicated"=>"L'appartamento %s è duplicato",
"aptDigits"=>"L'appartamento %s non è valido, che deve essere compreso tra 1 e 6 cifre",
"aptExit"=>"L'appartamento %s esiste già",
"abnormal"=>"Anormale",
"activation"=>"Attivazione",
"additionalApp"=>"App aggiuntiva",
"bindDevice"=>"Si prega di eliminare tutti i dispositivi con questo account",
"bindMAClibrary"=>"Si prega di eliminare MAC alla libreria MAC",
"bindUser"=>"Si prega di eliminare gli utenti in questo account",
"buildingBindDevice"=>"Si prega di eliminare i dispositivi nell'ambito di questo edificio",
"buildingBindUser"=>"Si prega di eliminare gli utenti sotto questo edificio",
"buildingDigits"=>"Edificio non valido %s, che deve essere compreso tra 1 e 2 cifre",
"buildingExit"=>"La costruzione esiste già",
"BindingDeviceFailed"=>"Bind Device non riuscito, il dispositivo può essere legato ad altri utenti o non ha aggiunto alla libreria MAC",
"chcekMacExits"=>"Aggiunta non è valido l'indirizzo Mac non valido o già esiste",
"changePasswdFail"=>"Modifica della password non riuscita",
"changePasswdPEmail"=>"Modifica della password ESPERE, Verifica su Email %s",
"community"=>"Comunità",
"deleteFail"=>"Elimina non riuscita",
"deleteSuccess"=>"Elimina con successo",
"deviceTypeEmpty"=>"È richiesto il tipo di dispositivo",
"deviceNotFindUser"=>"Dispositivo non trovato, contatta il tuo amministratore",
"dealSuccess"=>"Imposta il successo",
"doorUnit"=>"Unità porta",
"emailExits"=>"Email già esistente",
"emailPExit"=>"Email %s già esiste",
"emailNotExits"=>"Questa e -mail non esiste.",
"emailDuplicated"=>"Email %s è duplicato",
"errorVersion"=>"Versione di errore",
"emailOrAccountNotExit"=>"Questa e -mail non esiste.",
"firstNameEmpty"=>"È richiesto il nome",
"failed"=>"Fallito",
"family"=>"Famiglia",
"guardPhone"=>"Telefono di guardia",
"incorrectSipAccount"=>"Niente più account SIP disponibile",
"incorrectSipAccountGroup"=>"Niente più gruppo SIP disponibile",
"importDataSuccess"=>"Importa il successo dei dati",
"importFailMACExit"=>"",
"invaildDC"=>"Codice dispositivo non valido",
"InvalidFile"=>"File non valido",
"invalidPEmail"=>"Email non valida %s",
"invalidPName"=>"Nome utente non valido %s",
"invalidPCalltype"=>"Tipo di chiamata non valido %s",
"invalidPPin"=>"Pin non valido %s",
"invalidPActive"=>"Valore attivo non valido %s",
"invalidPage"=>"Pagina non valida",
"invalidPDeviceType"=>"Tipo di dispositivo non valido %s",
"invaildVerCode"=>"Codice di verifica non valido",
"invalidIdentity"=>"Informazioni sull'identità non valide!",
"indoorMonitor"=>"Monitor interno",
"inactivated"=>"inactivate",
"normal"=>"Normale",
"expired"=>"expire",
"lastNameEmpty"=>"È richiesto cognome",
"locationEmpty"=>"È richiesta la posizione",
"locationPLoog"=>"%s posizione troppo lunga",
"locationLoog"=>"Posizione troppo lunga",
"loginError"=>"Errore di accesso",
"loginFail"=>"Accesso non riuscito",
"loginSuccess"=>"Accedi al successo",
"limitIP"=>"Stai provando troppo spesso, riprova in 5 minuti",
"limitDevice"=>"Il numero del dispositivo ha raggiunto il limite massimo",
"MAC2PLibrary"=>"Indirizzo MAC:%s non è valido, controlla la tua libreria MAC.",
"MAC2Library"=>"Indirizzo MAC non valido, controlla la tua libreria Mac",
"macExits"=>"L'indirizzo MAC esiste già",
"MACLength"=>"La lunghezza dell'indirizzo MAC deve essere di 12 cifre.",
"modifySuccess"=>"Modifica il successo",
"modifyFailed"=>"Modifica non riuscito",
"maxHouse"=>"Il numero degli utenti ha raggiunto il limite massimo, contattare l'amministratore",
"modifyAptFail"=>"Salvataggio fallito! Il n. Apt esiste già, dovresti eliminarlo in primo luogo.",
"nameloog"=>"Nome utente troppo a lungo, il nome utente può contenere fino a 64 caratteri",
"nameExit"=>"Il nome utente esiste già",
"notPermission"=>"Non hai il permesso di funzionare",
"noSip"=>"Niente più account SIP",
"passwordIncorrect"=>"Password non corretta",
"passwdChangeSuccess"=>"Cambia la password di successo",
"passwordResetSuccess"=>"Reimpostare il successo della password",
"passwordReset2"=>"La password è stata ripristinata a \\",
"payTimeOut"=>"Pagamento del tempo",
"payFailed"=>"La paga non è riuscita",
"processing"=>"in lavorazione",
"paySuccess"=>"Pagamento con successo",
"redirectedOnRPS"=>"Questo indirizzo MAC viene reindirizzato su RPS.",
"registerFailed"=>"Registro non riuscito",
"registerSuccess"=>"Registra il successo",
"roomNotExit"=>"Questo utente non esiste!",
"RFCardExit"=>"Questa scheda RF è già esiste",
"registered"=>"register",
"PrivateKeyExists"=>"Questa chiave privata esiste",
"passwordCorrect"=>"Password non valida",
"timeLessCurrent"=>"Tempo di aggiornamento non valido",
"timeZoneChangeSuccess"=>"Cambia il fuso orario",
"timeOut"=>"Tempo scaduto",
"unbindMACUser"=>"Si prega di smontare %s con l'utente prima",
"unKnowDT"=>"Tipo di dispositivo sconosciuto",
"userBindUser"=>"Si prega di eliminare prima gli utenti con questo account",
"userNotExit"=>"Questo utente non esiste",
"userMaxPLimt"=>"Crea non riuscito, puoi aggiungere solo membri della famiglia fino a %.",
"unregistered"=>"Non registrato",
"validMAC"=>"Si prega di inserire un indirizzo MAC valido",
"versionExit"=>"La versione è già esiste",
"versionNameNumberExit"=>"Il nome o il numero della versione sono già esistiti",
"sipStatus"=>"Assegnazione dell'account SIP non riuscita, per favore riprova",
"sentCodeLater"=>"Ti abbiamo inviato un codice di verifica, riprova più tardi",
"setSuccess"=>"Successo di configurazione",
"sendEmailSuccess"=>"Invia il successo e -mail",
"SetFailed"=>"Impostazione non riuscita",
"stairPhone"=>"Telefono per scale",
"successed"=>"Avere successo",
"subscription"=>"Rinnovare",
"wallPhone"=>"Telefono a parete",
"emailMaxLen"=>"L'e -mail deve essere inferiore a 64 caratteri.",
"serverUpgradeTips"=>"L'aggiornamento del server è completo, aggiorna la pagina.",
"ActiveFamilyAccount"=>"Si prega di attivare prima l'account del master della famiglia.",
"weekly"=>"settimanalmente",
"daily"=>"Quotidiana",
"never"=>"Mai",
"calltypeEmpty"=>"È richiesto il tipo di chiamata",
"addOutApt"=>"Puoi aggiungere solo a %s camere",
"call"=>"Chiamata",
"unlock"=>"Sbloccare",
"tryUnlockCall"=>"Call Unlock non riuscito",
"tryUnlockKey"=>"Sblocco del codice pin non riuscito",
"tryUnlockCard"=>"Sblocco della scheda RF non riuscito",
"tryUnlockFace"=>"Lo sblocco del viso non è riuscito",
"unlockApp"=>"SmartPlus Unlock",
"unlockIndoor"=>"Sblocco del monitor interno",
"unlockNFC"=>"Sblocco NFC",
"unlockBluetooth"=>"Sblocco Bluetooth",
"unlockCard"=>"Sblocco della scheda RF",
"unlockPrivateKey"=>"Sblocco del codice pin",
"unlockTempKey"=>"Sblocco della chiave temp",
"alarmDoorUnlock"=>"Sblocco della porta",
"alarmInfrared"=>"Infrarossa",
"alarmSmoke"=>"Fumo",
"alarmGas"=>"Gas",
"alarmUrgency"=>"Urgenza",
"alarmSOS"=>"sos",
"alarmTamper"=>"Manomettere",
"alarmGate"=>"Cancello",
"alarmDoor"=>"Porta",
"alarmBedroom"=>"Camera da letto",
"alarmGuestRoom"=>"Stanza degli ospiti",
"alarmHall"=>"Sala",
"alarmWindow"=>"Finestra",
"alarmBalcony"=>"Balcone",
"alarmKitchen"=>"Cucina",
"alarmStudy"=>"Studio",
"alarmBathroom"=>"Bagno",
"alarmArea"=>"La zona",
"RFCardExit2"=>"RF Card %S è già esiste",
"RFCardDuplicated"=>"RF Card %S è duplicato",
"notMacBind"=>"L'utente '%s' non ha il permesso di aprire la porta di connessione con il dispositivo '%s'.",
"accountNumLet"=>"L'account deve essere costituito da numeri e lettere",
"networkUnavailable"=>"Rete non disponibile.",
"notForModel"=>"Non per questo modello.",
"upgradeDevVersion"=>"Si prega di aggiornare prima l'ultima versione.",
"unavailableService"=>"Il servizio è temporaneamente non disponibile, riprova più tardi.",
"cantDeletePin"=>"Non puoi eliminare il pin %s",
"residentInRoom"=>"Ci sono già residenti nella stanza %s",
"noAnswer"=>"Nessuna risposta",
"indoorAndApp"=>"Sia il monitor interno che l'app",
"indoorMonitorOnly"=>"Solo monitor interno",
"appOnly"=>"Solo app",
"endThanStart"=>"L'ora finale non può essere prima dell'ora di inizio.",
"endThanStartFile"=>"Giorno o tempo non valido in linea \\",
"doorRelease"=>"Rilascio della porta",
"success"=>"Successo",
"unlockFACE"=>"Sblocco del viso",
"unlockBLE"=>"Sblocco Bluetooth",
"captureSmartPlus"=>"Cattura su SmartPlus",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"Impossibile sbloccare",
"deviceDisconnected"=>"Il dispositivo è stato disconnesso.",
"low"=>"Basso",
"motion"=>"Movimento",
"capture"=>"Catturare",
"failedImport"=>"Importazione non riuscita",
"notValidMobile"=>"%s non è un numero di cellulare valido.",
"mobileExits"=>"Il numero di cellulare esiste già",
"mobileExits2"=>"Il numero di cellulare %s è già esiste",
"mobileDuplicated"=>"Il numero di cellulare %s è duplicato",
"mobileNumberExist"=>"Il numero di cellulare non esiste.",
"codeIncorrect"=>"Codice non VALIDO",
"sendCodeSuccess"=>"Invia il codice di verifica correttamente",
"codeCorrect"=>"Corretta",
"mobileNumberEmpty"=>"Inserisci il tuo numero di cellulare.",
"invalidUser"=>"User %non valido S",
"locationExits"=>"L'indirizzo di posizione esiste già",
"smartPlusIndoor"=>"Monitor SmartPlus e Indoor",
"phoneIndoor"=>"Monitor di telefono e interni",
"smartPlusIndoorBackup"=>"Monitor SmartPlus e Indoor, con telefono come backup",
"smartPlusBackup"=>"Monitor interni con SmartPlus come backup",
"indoorPhoneBackup"=>"Monitor interni con telefono come backup",
"indoorSmartPlusPhone"=>"Monitor interni con SmartPlus come backup, finalmente il telefono",
"endUser"=>"Utente finale",
"installer"=>"Installatrice",
"distributor"=>"Distributrice",
"pm"=>"PM",
"superManage"=>"Supermanage",
"loginManagement"=>"Gestione dell'accesso",
"accessControl"=>"Controllo di accesso",
"userManagement"=>"Gestione utenti",
"deviceManagement"=>"Gestione dei dispositivi",
"communityManagement"=>"Gestione della comunità",
"auditLogin"=>"Accedi: Web",
"auditLogout"=>"Elaborazione: Web",
"auditAddTempKey"=>"Aggiungi la chiave temporanea: {0}",
"auditEditTempKey"=>"Modifica la chiave temporanea: {0}",
"auditDeleteTempKey"=>"Elimina la chiave temporanea: {0}",
"auditAddRFCard"=>"Aggiungi scheda RF: {0}",
"auditEditRFCard"=>"Modifica scheda RF: {0}",
"auditDeleteRFCard"=>"Elimina la scheda RF: {0}",
"auditAddDis"=>"Aggiungi distributore: {0}",
"auditEditDis"=>"Modifica distributore: {0}",
"auditDeleteDis"=>"Elimina distributore: {0}",
"auditAddInstaller"=>"Aggiungi installatore: {0}",
"auditEditInstaller"=>"Modifica installa: {0}",
"auditDeleteInstaller"=>"Elimina Installer: {0}",
"auditAddPM"=>"Aggiungi PM: {0}",
"auditEditPM"=>"Modifica PM: {0}",
"auditDeletePM"=>"Elimina PM: {0}",
"auditAddEndUser"=>"Aggiungi l'utente finale: {0}",
"auditEditEndUser"=>"Modifica l'utente finale: {0}",
"auditDeleteEndUser"=>"Elimina l'utente finale: {0}",
"auditSetOwnerTime"=>"Imposta il proprio fuso orario {0}",
"auditSetOwnPassword"=>"Imposta la propria password",
"auditAddPIN"=>"Aggiungi pin: {0}",
"auditEditPIN"=>"Modifica pin: {0}",
"auditDeletePIN"=>"Elimina PIN: {0}",
"auditImportFace"=>"Importa Face: {0}",
"auditDeleteFace"=>"Elimina faccia: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Imposta il tipo di chiamata SmartPlus e Indoor Monitor: {0} \\u0026 {1}",
"auditSetCallTypePhoneIndoor"=>"Imposta il tipo di chiamata telefonico e monitor interni: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Imposta il tipo di chiamata SmartPlus e Indoor Monitor, con telefono come backup: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusBackup"=>"Imposta monitor interni del tipo di chiamata con smartplus come backup: {0} \\u0026 {1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Imposta il tipo di chiamata monitor interno con telefono come backup: {0} \\u0026 {1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Imposta monitor interni del tipo di chiamata con SmartPlus come backup, finalmente il telefono: {0} \\u0026 {1}",
"auditDeleteDevice"=>"Elimina dispositivo: {0}",
"auditSetAPTCount"=>"Imposta numero di appartamenti {0}",
"auditEnableLandline"=>"Abilita il servizio fisso",
"auditDisableLandline"=>"Disabilita il servizio fisso",
"auditSetSubTime"=>"Imposta timezone {0}",
"auditSetChargeModeInstall"=>"Imposta il modello di ricarica Pay da installatore",
"auditSetChargeModeUser"=>"Imposta il modello di ricarica Pay dall'utente/PM",
"auditSetConnectTypeDefault"=>"Imposta il tipo di connessione predefinito",
"auditSetConnectTypeTCP"=>"Impostare il tipo di connessione TCP",
"auditSetConnectTypeUDP"=>"Imposta il tipo di connessione UDP",
"auditSetConnectTypeTLS"=>"Imposta il tipo di connessione TLS",
"auditAddCommunity"=>"Aggiungi community: {0}",
"auditDeleteCommunity"=>"Elimina community: {0}",
"auditImportCommunity"=>"Importa community: {0}",
"auditSetAPTNumber"=>"Set {0} Numero della stanza {1}",
"auditSetEmail"=>"Imposta email {0}: {1}",
"auditSetMobile"=>"Imposta numero di telefono {0}: {1}",
"auditDeviceTypeStair"=>"Set Type Multi-Tenants Doorphone: {0}",
"auditDeviceTypeDoor"=>"Set tipo di porta singolo-inquilini: {0}",
"auditDeviceTypeIndoor"=>"Imposta Type Indoor Monitor: {0}",
"auditDeviceTypeGuardPhone"=>"Imposta Type Guard Telefono: {0}",
"auditDeviceTypeAccessControl"=>"Imposta Controllo Access Type: {0}",
"auditSetNetGroup"=>"Set Network Group {0}: {1}",
"auditEditCommunity"=>"Modifica comunità",
"deliveryMsg"=>"Hai gli articoli di %S che ti sono stati consegnati, per favore il check -in tempo.",
"deliveryTitle"=>"Hai un nuovo pacchetto!",
"rfcardDuplicatedLines"=>"Numero scheda RFID duplicato in linea %s!",
"rfcardNameInvalid"=>"Nome della scheda RF non valido in riga %s!",
"rfcardExistLines"=>"Le schede RF sono già esistite in linea %s.",
"importFailMacExistLines"=>"L'indirizzo MAC esiste o valido in linea %s.",
"exportExcelCountNull"=>"Nessun registro da esportare alla data!",
"keyIsEqualRoom"=>"La chiave di consegna non può essere identica al numero appropriato!",
"visitor"=>"visitatrice",
"CommunityNameExist"=>"Il nome della comunità esiste già",
"unlockGuardPhone"=>"Sblocco del telefono di guardia",
"auditLoginApp"=>"Accedi: App",
"auditLogoutApp"=>"Elaborazione: app",
"timeForYesterday"=>"Ieri",
"exportExcelDataBefore"=>"Dati troppo grandi!",
"tempkeyUsed"=>"Tempkey usato",
"tempkeyContent"=>"%s ha usato il tempkey.",
"accessNameExist"=>"Il nome del gruppo di accesso esiste già",
"addFaceFail"=>"Si prega di importare una foto chiara del viso.",
"userInvalid"=>"Utente non valido in linea %s.",
"groupsInvalid"=>"Gruppo di accesso non valido in linea %s.",
"BuildAccessName"=>"Resident-Building %s",
"auditCodeLogEditApt"=>"Modifica appartamento: {0}",
"invalidTimeInLine"=>"Tempo non valido in linea %s.",
"cancel"=>"Annulla",
"cancelSuccess"=>"Annulla riuscita.",
"payOutstanding"=>"Si prega di verificare se ci sono ordini non pagati, in caso contrario, si prega di contattare il tuo fornitore di servizi",
"featureDeleteError"=>"Il piano di funzionalità essere vincolato.",
"beyondFamilyMember"=>"Non è possibile creare più account dei membri della famiglia, contattare il tuo fornitore di servizi per crearlo.",
"indoorMonitorRequired"=>"È necessario almeno un monitor interno per ogni appartamento.",
"featureActivationFee"=>"Funzione (commissione una tantum)",
"systemProcessing"=>"Elaborazione del sistema",
"featureMonthlyFee"=>"Funzione (Commissione mensile)",
"featurePriceDifferences"=>"Funzione (differenze di prezzo)",
"updatingSuccess"=>"L'aggiornamento è riuscito!",
"featureNameBasic"=>"Di base",
"featureNamePremium"=>"Premium",
"indoorMacNotCorrect"=>"Inserisci il Mac corretto di monitor interno.",
"off"=>"Spento",
"enterValidAccount"=>"Per favore inserisci un account valido",
"invalidKitImportMAC"=>"Si prega di controllare se il Mac esiste o valido: %s",
"importLessData"=>"Si prega di importare dati meno di %s.",
"invalidQRCode"=>"Identificazione non riuscita, si prega di scansionare un codice QR corretto.",
"cannotCreateFamilyMember"=>"Non puoi creare più account per familiari.",
"importProcessing"=>"Importazione, riprova più tardi",
"departmentAccessName"=>"%S Access Group",
"idExistsLine"=>"Id esiste già in linea %s",
"enterFirstNameLine"=>"Inserisci il nome in riga %s",
"enterLastNameLine"=>"Inserisci cognome in riga %s",
"departmentExist"=>"Il dipartimento esiste già",
"idExist"=>"Id esiste già",
"layoutIdInvalid"=>"Layout non è valido",
"unlockAppHome"=>"Gli occhi si sblocca",
"officeNameExist"=>"Il nome dell'ufficio esiste già",
"departmentExit"=>"Il dipartimento esiste già.",
"importOutTask"=>"Puoi importare solo un modello alla volta.",
"idDuplicated"=>"Id %s è duplicato",
"aptInvalidLine"=>"Apt non valido in linea %s.",
"buildInvalidLine"=>"Edificio non valido in linea %s.",
"departmentInvalidLine"=>"Dipartimento non valido in linea %s.",
"idInvalidLine"=>"ID non valido in linea %s.",
"propertyManager"=>"Manager di proprietà",
"departmentBindDevice"=>"Si prega di eliminare i dispositivi nell'ambito di questo dipartimento.",
"departmentBindUser"=>"Elimina gli utenti sotto questo dipartimento.",
"smartPlusValidLine"=>"Funzione di citofono SmartPlus non valida in linea %s.",
"identityValidLine"=>"Identità non valida in linea %s.",
"eachDoorCount"=>"Un unico piano per aprire ogni porta una volta",
"textUpgradeMsg1"=>"Aggiorna la versione dell'app per continuare.",
"textUpgradeMsg2"=>"Accesso non riuscito",
"deleteCodeGetLimitTimes"=>"Chiave non valida. Riprova 24 ore dopo.",
"deleteCodeOverLimitTimes"=>"Riprova 24 ore dopo.",
"deleteCodeError"=>"Chiave non valida",
"textUpgradeMsg"=>"1. Ottimizzare la funzione Tempkey.; 2 Aggresto la funzione di cancellazione dell'account.; 3. Filare alcuni bug.",
"paramsError"=>"Errore dei parametri",
"pmappStatusInvalid"=>"Si prega di abilitare l'app PM prima.",
"delivery_description"=>"Chiave di temperatura di consegna",
"webRelayIDInvalidLine"=>"ID relè web non valido in linea %s.",
"relayInvalid"=>"Relè non valido in linea %s.",
"cancelError"=>"Annulla non riuscita.",
"textUpgradeMsgForComRole"=>"Aggiorna il ruolo della comunità",
"textUpgradeMsgForPerRole"=>"Aggiorna il ruolo del personale",
"textUpgradeMsgForOffRole"=>"Aggiorna il ruolo dell'ufficio",
"textUpgradeMsgForPMRole"=>"Aggiorna il ruolo del PM",
"lockApp"=>"SmartPlus Lock",
"lock"=>"Serratura",
"versionLogMaxLen"=>"Il registro della versione non può essere più grande di %s caratteri",
"autoLock"=>"Chiusura automatica",
"pinAndRFcardNotNullLines"=>"Almeno una delle schede PIN e RF in linea %S deve essere riempita!",
"pinExistLines"=>"Pin sono già esistiti in linea %s.",
"pinInvalidLines"=>"Pin non valido in linea %s!",
"pinDuplicatedLines"=>"Pin duplicato in linea %s!",
"FaceImportLength"=>"La dimensione del file di importazione facciale non può essere maggiore di %s",
"landlineServerNotActivate"=>"Questa comunità non ha attivato il servizio fisso.",
"importFailDisNotExist"=>"Il distributore non esiste",
"importFailNotPermission"=>"Non hai il permesso di aggiungere questo indirizzo MAC.",
"importFailTooManyAdd"=>"Importazione non riuscita, solo per distributore singolo.",
"importFailAdded"=>"Questo indirizzo MAC è già stato aggiunto da un altro utente.",
"macAssignToLimit"=>"Puoi assegnare solo fino a 10 distributori",
"macNumToLimit"=>"Puoi caricare solo fino a 1000 indirizzi MAC alla volta.",
"addOutFloor"=>"Inserisci un numero tra 1 ~ 128.",
"floor"=>"Pavimento",
"PostalCodeInvalid"=>"Inserisci lettera o numero.",
"onceCodeInvalid"=>"Il codice una volta deve essere di 4-5 cifre.",
"permanentCodeInvalid"=>"Il codice permanente deve essere di 6 cifre.",
"onceCodeOutNum"=>"Puoi aggiungere fino a 10 solo una volta.",
"permanentCodeOutNum"=>"Puoi aggiungere solo fino a 10 codice permanente.",
"onceCodeExist"=>"Il codice una volta esiste già.",
"permanentCodeExist"=>"Il codice permanente esiste già.",
"addOutFloorLine"=>"Numero di pavimento non valido in linea %s.",
"auditManuallyUnlock"=>"Sbloccare manualmente",
"auditManuallyLock"=>"Bloccare manualmente",
"automaticallyUnlock"=>"Sbloccare automaticamente",
"doorClose"=>"Porta chiusura",
"PostalCodeNotEmpty"=>"Inserisci almeno una lettera o numero.",
"emergencyAlarm"=>"Allarme di emergenza",
"doorSensor"=>"Sensore della porta",
"yaleBatteryWarning"=>"AVVERTENZA BATTERIA YALE",
"auditCodeManuallyUnlock"=>"Sbloccare manualmente",
"auditCodeManuallyLock"=>"Bloccare manualmente",
"2weekBatteryWarning"=>"%S - Tempo di batteria stimato rimanente: 2 settimane.",
"1weekBatteryWarning"=>"%S - Tempo di batteria stimato rimanente: 1 settimana.",
"replaceBatteryWarning"=>"%s - Il livello della batteria è estremamente basso, si prega di sostituire immediatamente.",
"open"=>"Aprire",
"close"=>"Phonetics",
"addContactFavoriteNum"=>"Aggiungendo ai preferiti falliti. Puoi aggiungere solo fino a 300 appartamenti preferiti.",
"addContactBlockNum"=>"Aggiunta a Blocklist non riuscito. Puoi aggiungere solo fino a 100 appartamenti a Blocklist.",
"voiceTitle"=>"Messaggio vocale",
"voiceContent"=>"Hai un messaggio vocale da %s",
"voiceMsgInvalid"=>"Il messaggio vocale è scaduto.",
"toggleFeaturePlan"=>"Non è possibile modificare il piano di funzionalità.",
"rtspAddresEmpty"=>"Immettere l'indirizzo RTSP.",
"rtspAddresInvalid"=>"Indirizzo RTSP non valido.",
"rtspPortEmpty"=>"Inserisci la porta.",
"rtspPortInvalid"=>"Porta non valida.",
"rtspPassWdEmpty"=>"Per favore, inserisci la password.",
"rtspPassWdInvalid"=>"Password troppo a lungo, la password può contenere fino a 63 caratteri.",
"cameraExist"=>"La fotocamera esiste già.",
"errorOnRPS"=>"Impossibile richiedere il server RPS",
"faceImportErrorSystem"=>"Errore di sistema",
"faceImportErrorView"=>"Non vista frontale",
"faceImportErrorWearMask"=>"Maschera rilevata",
"faceImportErrorLowResolution"=>"La risoluzione è troppo bassa",
"faceImportErrorWrongFormat"=>"Errore in formato file",
"faceImportErrorNoFace"=>"Nessun viso rilevato",
"faceImportErrorFileLarge"=>"Il file è troppo più grande",
"faceImportErrorFaceLarge"=>"Il viso è troppo più grande",
"faceImportErrorFaceSmall"=>"Il viso è troppo piccolo",
"faceImportErrorMultiFaces"=>"Più di una faccia",
"faceImportErrorWrongName"=>"Il nome del file è un errore.",
"faceImportErrorEmptyName"=>"Il nome del residente è vuoto.",
"faceImportErrorNoAccountInfo"=>"Ottieni un errore Info PersonalAccount.",
"faceImportErrorAccountInactive"=>"Il PersonalAccount non è attivo.",
"changeHomeFeatureInvalid"=>"Operazione fallita!",
"changeInterComFeatureInvalid"=>"Operazione fallita!",
"offline"=>"Fallito: offline",
"allFloors"=>"Tutti i pavimenti",
"uploadOversize"=>"La dimensione del file di caricamento non può essere maggiore di %s",
"uploadInvalidType"=>"Il tipo di file caricato non è supportato",
"uploadFailed"=>"Il caricamento non è riuscito, prova più tardi",
"uploadScreenSaverImgTooMuch"=>"Le immagini di screensaver non possono essere più di %s!",
"screenSaverImgTooLittle"=>"Le immagini di screensaver non possono essere inferiori a %s!",
"screenSaverImgTooMuch"=>"Le immagini di screensaver non possono essere più di %s!",
"screenSaverDevicesOffline"=>"Salva fallito.",
"saveFailed"=>"Salva fallito.",
"importingInProgress"=>"Importando in corso, riprova più tardi.",
"importBuildingInvalidLine"=>"Edificio non valido in linea %s",
"importAptInvalidLine"=>"Apt non valido in linea %s",
"importAccountTypeInvalidLine"=>"Tipo di account non valido in linea %s",
"importFirstNameInvalidLine"=>"Nome non valido in linea %s",
"importLastNameInvalidLine"=>"Cognome non valido in riga %s",
"importKeyInvalidLine"=>"Chiave non valida in linea %s",
"importKeyExistsLine"=>"PIN esiste in linea %s",
"importCardInvalidLine"=>"Scheda RF non valida in linea %s",
"importCardExistsLine"=>"La scheda RF esiste in linea %s",
"importAccessGroupInvalidLine"=>"ID gruppo di accesso non valido in linea %s",
"importAccessGroupNoPermissionLine"=>"Nessun ID gruppo di accesso all'autorizzazione in linea %s",
"importExceededNumberLine"=>"Ha superato il numero di membri della famiglia in linea %s",
"importNoActiveMasterLine"=>"Importazione non riuscita in linea %s, si prega di attivare prima il matser di famiglia.",
"importMasterExistsLine"=>"Il maestro di famiglia esiste già in linea %s.",
"importNoCreateMasterLine"=>"Importazione non riuscita in linea %s, crea prima il matser di famiglia.",
"PrivateKeysDataExist"=>"La chiave privata è già esistente.",
"PrivateKeyDataExists"=>"La chiave privata è già esistente.",
"landLineOpenToClosedFail"=>"Salva fallito.",
"limitWithIp"=>"Si sta provando troppo spesso, si prega di riprovare in 5 minuti. (IP:%s)",
"subDistributor"=>"Distributore secondario",
"faceImportErrorNotClear"=>"L'immagine importata non è chiara.",


  ];
