<?php
/*
 * @Description: 终端用户计划列表
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-19 16:26:47
 * @LastEditors: cj
 */

/*
    name=>[
        "method"=>method,请求方法
        "auth"=>true/false,是否验证，默认true
        "roles"=>[RPERENDMROLE,RCOMENDMROLE],//默认都可以
        "active"=>true/false,过期和激活的验证，默认true
        "type"=>web/app/alexa
        "process"=>[
            中间件调用，一般是管道过滤或处理和验证
            [
                "type"=>"middle",
                "queue"=>[
                    [
                        "name"=>"test",
                        "params"=>"text"//可以是字符串
                        "params"=>["text","text2"...],
                        "params"=>[[
                            name=>"text",
                            type=>"const",
                            value=>"111"
                        ]]
                    ]
                ]
            ],
            [
                "type"=>"model",
                "model"=>"login.endUserLogin",
                "dataContainer"=>"common"//默认common
            ]
            [
                "type"=>"branches",//分支模式，满足即执行多个分支
                "branches"=>[
                    "1"=>[],
                    "2"=>[]
                ]
            ],
            [
                "type"=>"echo",
                "code"=>//状态码
                "options"=>["data"=>[...],"token"=>"TOEKN"]
            ]
        ],
        "processRedefine"=>function(){} 重定义执行计划，达成某些条件时，重新定义计划
    ]

 */
include_once __DIR__ . '/addProcess.php';
include_once __DIR__ . '/process/main.php';
$cAddProcess = CAddProcess::getInstance();
$PLProcess = \plan\process\getProcess();
$PLRouter = [
    'personlogin' => [
        'method' => 'POST',
        'params' => ['Account', 'passwd'],
        'auth' => false,
        'active' => false,
        'type' => 'web',
        'process' => $PLProcess['userLogin'],
    ],
    'loginCode' => [
        'method' => 'POST',
        'params' => ['MobileNumber', 'Code'],
        'auth' => false,
        'type' => 'web',
        'process' => $PLProcess['userLoginCode'],
    ],
    // 'addpersonaluser' => [
    //     'method' => 'POST',
    //     'params' => [
    //         'Name',
    //         'FirstName',
    //         'LastName',
    //         'Email',
    //         'MobileNumber',
    //         'PhoneStatus',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'EnableIpDirect',
    //         'TimeZone',
    //         'RoomNumber',
    //         'Address',
    //         'Language',
    //         'isSingleMonitor',
    //         'Relay',
    //         'MAC',
    //         'Location'
    //     ],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->clear()
    //         ->set(RPERENDMROLE, $PLProcess['addPSUser'])
    //         ->set(RCOMENDMROLE, $PLProcess['addAppCSUser'])
    //         ->set(
    //             RPERSONGRADE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setInitLandline'],
    //                     ],
    //                 ],
    //                 $PLProcess['addPMUser']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'addpersonaluserapp' => [
    //     'method' => 'POST',
    //     'params' => ['Name', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode', 'IsAdmin'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'type' => 'app',
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setInitLandline'],
    //                     ],
    //                 ],
    //                 $PLProcess['addPSUser']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setInitLandline'],
    //                     ],
    //                 ],
    //                 $PLProcess['addAppCSUser']
    //             )
    //         )
    //         ->get(),
    // ],
    // "addpersonaluserfrom3"=>[
    //     "method"=>"POST",
    //     "params"=>["Name","Email"],
    //     "roles"=>[RPERENDMROLE],
    //     "type"=>"web",
    //     "process"=>$cAddProcess->clear()
    //     ->set(RPERENDMROLE,$PLProcess["addPSUser"])
    //     ->get()
    // ],
    // 'deletePersonalUser' => [
    //     'method' => 'POST',
    //     'params' => ['ID'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERSONGRADE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['deletePMUser']
    //             )
    //         )
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['deleteSUser']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['deleteSUser']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'deletepersonaluserapp' => [
    //     'method' => 'POST',
    //     'params' => ['ID'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'type' => 'app',
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['deleteSUser']
    //     ),
    // ],

    // 'modifyperosnaluser' => [
    //     'method' => 'POST',
    //     'params' => [
    //         'ID',
    //         'Email',
    //         'MobileNumber',
    //         'Name',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'Address',
    //         'TimeZone',
    //         'RoomNumber',
    //         'FirstName',
    //         'LastName',
    //         'EnableIpDirect',
    //         'isSingleMonitor',
    //         'Relay',
    //         'MAC',
    //         'Location',
    //         'PhoneStatus'
    //     ],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERSONGRADE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['updatePMUser']
    //             )
    //         )
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['updatePSUser']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['updateCSUser']
    //             )
    //         )
    //         ->get(),
    // ],
    'getpersonaluser' => [
        'method' => 'GET',
        'params' => ['row', 'page', 'serchKey', 'serchValue'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERSONGRADE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(RPERSONGRADE, $PLProcess['getPerMainUserFormPMng'])
            ->set(RPERENDMROLE, $PLProcess['getPerSubUserFormUser'])
            ->set(RCOMENDMROLE, $PLProcess['getComSubUserFormUser'])
            ->get(),
    ],
    'getpersonaluserinfo' => [
        'method' => 'GET',
        'params' => 'ID',
        'type' => 'web',
        'process' => $PLProcess['getUserInfo'],
    ],
    // 未测试从账号
    'getpersonaluserapp' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'type' => 'app',
        'process' => $cAddProcess->clear()
            ->set([RPERENDMROLE, RCOMENDMROLE], $PLProcess['getUserFromApp'])
            ->set(
                [RPERENDSROLE, RCOMENDSROLE],
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['getUserFromApp']
                )
            )
            ->get(),
    ],
    // 'personalresetpw' => [
    //     'method' => 'POST',
    //     'params' => ['ID', 'Password'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['resetUserPw']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'subUserInMainCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['resetUserPw']
    //             )
    //         )
    //         ->set(
    //             RPERSONGRADE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
    //                     ],
    //                 ],
    //                 $PLProcess['resetUserPw']
    //             )
    //         )
    //         ->get(),
    // ],
    'getfuser' => [
        'method' => 'GET',
        'params' => 'ID',
        'roles' => [RPERSONGRADE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(
                RPERSONGRADE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
                        ],
                    ],
                    $PLProcess['queryFamilyUser']
                )
            )
            ->get(),
    ],
    // 'getPersonalUserListForApp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setUserAliasIdToId'],
    //             ],
    //         ],
    //         $PLProcess['queryFamilyUser']
    //     ),
    // ],
    'getpersonaluserlist' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'setUserAliasIdToId'],
                ],
            ],
            $PLProcess['queryFamilyUser']
        ),
    ],
    'setuexpire' => [
        'method' => 'POST',
        'params' => ['ID', 'ExpireTime'],
        'roles' => [RSUPERGRADE, RAREAGRADE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(RSUPERGRADE, $PLProcess['setMainUserExpireTime'])
            ->set(
                RAREAGRADE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'mainUserInAMngCheck', 'params' => 'ID'],
                        ],
                    ],
                    $PLProcess['setMainUserExpireTime']
                )
            )
            ->get(),
    ],
    // 'activeaccount' => [
    //     'method' => 'POST',
    //     'params' => 'ID',
    //     'roles' => [RSUPERGRADE],
    //     'type' => 'web',
    //     'process' => $PLProcess['activeAccount'],
    // ],
    // 'getpersonalsubpay' => [
    //     'method' => 'GET',
    //     // ID:主账户ID
    //     'params' => 'ID',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPROPERTYMANAGE, RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->clear()
    //         ->set(RPERENDMROLE, $PLProcess['queryPerSubPay'])
    //         ->set(RPERSONGRADE, $PLProcess['queryPerAddAppFee'])
    //         ->set(RCOMENDMROLE, $PLProcess['queryComSubPay'])
    //         ->set(RCOMMUNITYGRADE, $PLProcess['queryComAddAppFee'])
    //         ->set(
    //             RPROPERTYMANAGE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPMAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComAddAppFee']
    //             )
    //         )
    //         ->get(),
    // ],
//    'personalcheckpw' => [
//        'method' => 'POST',
//        'params' => 'PassWd',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
//        'type' => 'app',
//        'process' => $PLProcess['checkUserPw'],
//    ],
    'personalcheckpwWeb' => [
        'method' => 'POST',
        'params' => 'PassWd',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['checkUserPw'],
    ],
//    'personalchangepw' => [
//        'method' => 'POST',
//        'params' => 'PassWd',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
//        'type' => 'app',
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setUserAliasIdToId'],
//                ],
//            ],
//            $PLProcess['changeUserPw']
//        ),
//    ],
    'personalchangepwWeb' => [
        'method' => 'POST',
        'params' => 'PassWd',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'type' => 'web',
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'setUserAliasIdToId'],
                ],
            ],
            $PLProcess['changeUserPw']
        ),
    ],
    'personalchangetimezone' => [
        'method' => 'POST',
        'params' => ['TimeZone', 'CustomizeForm'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(
                RPERENDMROLE,
                $cAddProcess->push(
                    $PLProcess['changeMainUserTimeZone'],
                    [
                        'type' => 'model',
                        'model' => 'userData.afterPerChangeTimeZone',
                    ]
                )
            )
            ->set(
                RCOMENDMROLE,
                $cAddProcess->push(
                    $PLProcess['changeMainUserTimeZone'],
                    [
                        'type' => 'model',
                        'model' => 'userData.afterComChangeTimeZone',
                    ]
                )
            )
            ->get(),
    ],
    // 'changename' => [
    //     'method' => 'POST',
    //     'params' => ['Name'],
    //     'roles' => [RPERENDMROLE, RPERENDSROLE],
    //     'type' => 'app',
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->push(
    //                 $PLProcess['changePerUserName'],
    //                 [
    //                     'type' => 'model',
    //                     'model' => 'user.afterUpdatePerMName',
    //                 ]
    //             )
    //         )
    //         ->set(
    //             RPERENDSROLE,
    //             $cAddProcess->push(
    //                 $PLProcess['changePerUserName'],
    //                 [
    //                     'type' => 'model',
    //                     'model' => 'user.afterUpdatePerSName',
    //                 ]
    //             )
    //         )
    //         ->get(),
    // ],

    // NO DATA
    // 'getCallHistory' => [
    //     'method' => 'GET',
    //     'params' => ['SIP', 'row', 'page'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'type' => 'app',
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['queryCallForApp'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryCallForApp']
    //             )
    //         )
    //         ->get(),
    // ],
    'getcallhistoryweb' => [
        'method' => 'GET',
        'params' => ['page', 'row', 'BeginTime', 'EndTime', 'Name'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['queryCallForWeb'],
    ],
    'getpersonaldevicelist' => [
        'method' => 'GET',
        'params' => ['Type'],
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'process' => $cAddProcess->clear()
            ->set(RPERENDMROLE, $PLProcess['queryPerDevWithType'])
            ->set(RCOMENDMROLE, $PLProcess['queryComDevWithType'])
            ->get(),
    ],
    // 未测试从账号
//    'getpersondata' => [
//        'method' => 'GET',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
//        'type' => 'app',
//        'process' => $PLProcess['queryUserOwnerData'],
//    ],
    'getpersondataweb' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['queryUserOwnerData'],
    ],
    // 'changedata' => [
    //     'method' => 'POST',
    //     'params' => ['Name', 'Phone', 'Phone2', 'Phone3', 'PhoneCode'],
    //     'roles' => [RPERENDMROLE],
    //     'type' => 'web',
    //     'process' => $PLProcess['changeUserData'],
    // ],
    // 未测试从账号
//    'getcallmotioncount' => [
//        'method' => 'GET',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
//        'type' => 'app',
//        'process' => $cAddProcess->clear()
//            ->set([RPERENDMROLE, RCOMENDMROLE], $PLProcess['queryActiveUnReadForApp'])
//            ->set(
//                [RPERENDSROLE, RCOMENDSROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                        ],
//                    ],
//                    $PLProcess['queryActiveUnReadForApp']
//                )
//            )
//            ->get(),
//    ],
    // 'setphoneread' => [
    //     'method' => 'POST',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'type' => 'app',
    //     'process' => $PLProcess['setPhoneRead'],
    // ],
    'setmotionread' => [
        'method' => 'POST',
        'params' => ['ID'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'type' => 'app',
        'process' => $PLProcess['setMotionRead'],
    ],
    'personalallad' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['queryAllAD'],
    ],
    // 'personalcharge' => [
    //     'method' => 'GET',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'type' => 'web',
    //     'active' => false,
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryPerCharge']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComCharge']
    //             )
    //         )
    //         ->get(),
    // ],
    // 未测试从账户
    // 'personalchargeapp' => [
    //     'method' => 'GET',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'type' => 'app',
    //     'params' => ['Type'],
    //     'active' => false,
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryPerCharge']
    //             )
    //         )
    //         ->set(
    //             RPERENDSROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryPerCharge']
    //             )
    //         )
    //         ->set(
    //             [RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComCharge']
    //             )
    //         )
    //         ->set(
    //             RCOMENDSROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComCharge']
    //             )
    //         )
    //         ->get(),
    // ],
    'personbill' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['queryUserBill'],
    ],
    'personmodbill' => [
        'method' => 'POST',
        'params' => ['ID', 'BillingTitle', 'Contactor', 'Street', 'City', 'Postcode', 'Country', 'TelePhone', 'Fax'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['editUserBill'],
    ],
    'closeaccount' => [
        'method' => 'POST',
        'params' => 'Password',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['closeAccount'],
    ],
    'personalusercnf' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['getCnf'],
    ],
//    'personalusercnfapp' => [
//        'method' => 'GET',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'type' => 'app',
//        'process' => $PLProcess['getCnf'],
//    ],
    'personalallaind' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $PLProcess['getCallQueue'],
    ],
//    'personalallaindapp' => [
//        'method' => 'GET',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'type' => 'app',
//        'process' => $PLProcess['getCallQueue'],
//    ],
    'personalsetcall' => [
        'method' => 'POST',
        'params' => ['EnableRobinCall', 'RobinCallTime', 'RobinCallVal', 'EnableIpDirect'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(
                RPERENDMROLE,
                $cAddProcess->push(
                    $cAddProcess->push(
                        $PLProcess['setCall'],
                        [
                            'type' => 'model',
                            'model' => 'userData.afterPerFamilyEditCnf',
                        ]
                    ),
                    [
                        'type' => 'model',
                        // V5.5修改成userNodeUpdate联动通知
                        'model' => 'notify.userNodeUpdate',
                    ]
                )
            )
            ->set(
                RCOMENDMROLE,
                $cAddProcess->push(
                    $cAddProcess->push(
                        $PLProcess['setCall'],
                        [
                            'type' => 'model',
                            'model' => 'userData.afterComFamilyEditCnf',
                        ]
                    ),
                    [
                        'type' => 'model',
                        'model' => 'notify.userComDataUpdate',
                    ]
                )
            )
            ->get(),
    ],
//    'personalsetcallapp' => [
//        'method' => 'POST',
//        'params' => ['EnableRobinCall', 'RobinCallVal', 'RobinCallTime'],
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'type' => 'app',
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPERENDMROLE,
//                $cAddProcess->push(
//                    $cAddProcess->push(
//                        $PLProcess['setCall'],
//                        [
//                            'type' => 'model',
//                            'model' => 'userData.afterPerFamilyEditCnf',
//                        ]
//                    ),
//                    [
//                        'type' => 'model',
//                        // V5.5修改成userNodeUpdate联动通知
//                        'model' => 'notify.userNodeUpdate',
//                    ]
//                )
//            )
//            ->set(
//                [RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//                $cAddProcess->push(
//                    $cAddProcess->push(
//                        $PLProcess['setCall'],
//                        [
//                            'type' => 'model',
//                            'model' => 'userData.afterComFamilyEditCnf',
//                        ]
//                    ),
//                    [
//                        'type' => 'model',
//                        'model' => 'notify.userComDataUpdate',
//                    ]
//                )
//            )
//            ->get(),
//    ],
    'personalsetmotion' => [
        'method' => 'POST',
        'params' => ['EnableMotion', 'MotionTime'],
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(
                RPERENDMROLE,
                $cAddProcess->push(
                    $PLProcess['setMotion'],
                    [
                        'type' => 'model',
                        'model' => 'userData.afterPerFamilyEditCnf',
                    ]
                )
            )
            ->set(
                RCOMENDMROLE,
                $cAddProcess->push(
                    $PLProcess['setMotion'],
                    [
                        'type' => 'model',
                        'model' => 'userData.afterComFamilyEditCnf',
                    ]
                )
            )
            ->get(),
    ],
    // V6.5.1暂时还原，V6.5.2删除
    // 'setainitdata' => [
    //     'method' => 'POST',
    //     'params' => ['PIN', 'CallType', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'Password'],
    //     'roles' => [RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'type' => 'app',
    //     'process' => $PLProcess['setInit'],
    // ],
    'getperosnaldata' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RPERENDSROLE, RCOMENDMROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
        'type' => 'app',
        'process' => $cAddProcess->clear()
            ->set([RPERENDMROLE, RPERENDSROLE], $PLProcess['getPerData'])
            ->set([RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE, RCOMENDSROLE], $PLProcess['getComData'])
            ->get(),
    ],
    'getcomsubdata' => [
        'method' => 'GET',
        'roles' => [RCOMENDSROLE, RPMENDMROLE],
        'type' => 'app',
        'process' => $PLProcess['getComSubData'],
    ],
    // 'setperosnaldata' => [
    //     'method' => 'POST',
    //     'params' => ['PIN', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'FirstName', 'LastName'],
    //     'roles' => [RPERENDMROLE, RPERENDSROLE, RCOMENDMROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'type' => 'app',
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RPERENDSROLE], $PLProcess['setPerPhone'])
    //         ->set([RCOMENDMROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['setComData'])
    //         ->get(),
    // ],
    // 'setperosnaldataweb' => [
    //     'method' => 'POST',
    //     // EnableIpDirect参数
    //     'params' => ['PIN', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'FirstName', 'LastName'],
    //     'roles' => [RCOMENDMROLE],
    //     'type' => 'web',
    //     'process' => $PLProcess['setComData'],
    // ],
    'getpersonalmaxappcount' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'app',
        'process' => $PLProcess['getCnf'],
    ],
    'getpersonalexpireday' => [
        'method' => 'GET',
        'type' => 'web',
        'process' => $PLProcess['getExpireDay'],
    ],
    'getpersonalexpiredayapp' => [
        'method' => 'GET',
        'type' => 'app',
        'active' => false,
        'process' => $PLProcess['getExpireDay'],
    ],
    'getpersonallandlinetimeapp' => [
        'method' => 'GET',
        'type' => 'app',
        'active' => false,
        'process' => $PLProcess['getLandlineTime'],
    ],
    'getpersonalorderlist' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['PayType', 'Type', 'Status', 'Key', 'BeginTime', 'EndTime', 'row', 'page'],
        'process' => $PLProcess['getUserOrderList'],
    ],
//    'installgetorderlist' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => [
//            'PayType',
//            'Type',
//            'Status',
//            'Key',
//            'BeginTime',
//            'EndTime',
//            'Installer',
//            'Community',
//            'ProjectType',
//            'IsBatch',
//            'row',
//            'page'
//        ],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RAREAGRADE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setAreaPayType'],
//                        ],
//                    ],
//                    $PLProcess['getMngOrderList']
//                )
//            )
//            ->set(
//                [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setInstallPayType'],
//                        ],
//                    ],
//                    $PLProcess['getMngOrderList']
//                )
//            )
//            ->get(),
//    ],
    // 未测试从账户
    'getpersonalorderlistapp' => [
        'method' => 'GET',
        'type' => 'app',
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'changeParamValue', 'params' => ['page' => 1, 'row' => 1000]],
                ],
            ],
            $PLProcess['getUserOrderList']
        ),
    ],

    'sendtempkeyemailapp' => [
        'method' => 'POST',
        'type' => 'app',
        'params' => ['ID', 'Email'],
        'process' => $PLProcess['sendTempKeyEmail'],
    ],
    'getorderinfo' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'getUserTimeZone'],
                ],
            ],
            $PLProcess['getOrderInfo']
        ),
    ],
    'getapporderinfo' => [
        'method' => 'GET',
        'type' => 'app',
        'params' => ['ID'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'getUserTimeZone'],
                ],
            ],
            $PLProcess['getOrderInfo']
        ),
    ],
//    'installgetorderinfo' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'params' => ['ID'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'getMngTimeZone'],
//                ],
//            ],
//            $PLProcess['getOrderInfo']
//        ),
//    ],
    'getsubappfeeapp' => [
        'method' => 'GET',
        'params' => 'ID',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'type' => 'app',
        'active' => false,
        'process' => $cAddProcess->clear()
            ->set(RPERENDMROLE, $PLProcess['queryPerSubPayApp'])
            ->set(RCOMENDMROLE, $PLProcess['queryComSubPayApp'])
            ->set(
                RPERENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryPerSubPayApp']
                )
            )
            ->set(
                RCOMENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryComSubPayApp']
                )
            )
            ->get(),
    ],
    // 'getpersonalunitpriceapp' => [
    //     'method' => 'GET',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
    //     'type' => 'app',
    //     'active' => false,
    //     'process' => $cAddProcess->clear()
    //         ->set(RPERENDMROLE, $PLProcess['queryPerMonthAppFee'])
    //         ->set(
    //             RPERENDSROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryPerSubPayApp']
    //             )
    //         )
    //         ->set(RCOMENDMROLE, $PLProcess['queryComMonthAppFee'])
    //         ->set(
    //             RCOMENDSROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComSubPayApp']
    //             )
    //         )
    //         ->get(),
    // ],
    'getusernextexpiretime' => [
        'method' => 'GET',
        'type' => 'web',
        'active' => false,
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => 'Month',
        'process' => $PLProcess['getNextExpiretime'],
    ],
    'getusernextexpiretimeapp' => [
        'method' => 'GET',
        'type' => 'app',
        'active' => false,
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => 'Month',
        'process' => $PLProcess['getNextExpiretime'],
    ],
    'getperreceipt' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => 'OrderID',
        'process' => $PLProcess['getUserReceipt'],
    ],
    // 'gettempkeyopera' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE], $PLProcess['TempKeyPermission'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['TempKeyPermission']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'getchargemode' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'active' => false,
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'process' => $PLProcess['getUserChargeMode'],
    // ],
    // 'getchargemodeapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'active' => false,
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE], $PLProcess['getUserChargeMode'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getUserChargeMode']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'getlandlinecharge' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'active' => false,
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE], $PLProcess['getLandlineCharge'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getLandlineCharge']
    //             )
    //         )
    //         ->get(),
    // ],
//    'getcommunitynameapp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'active' => false,
//        'roles' => [RCOMENDMROLE, RCOMENDSROLE],
//        'process' => $cAddProcess->clear()
//            ->set(RCOMENDMROLE, $PLProcess['getUserCommunityName'])
//            ->set(
//                RCOMENDSROLE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                        ],
//                    ],
//                    $PLProcess['getUserCommunityName']
//                )
//            )
//            ->get(),
//    ],
    'deleteorder' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => 'ID',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'process' => $PLProcess['deleteUserOrder'],
    ],
    // 'installdelelteorder' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'params' => 'ID',
    //     'process' => $PLProcess['deleteUserOrder'],
    // ],
    'getpersonaldevice' => [
        'method' => 'GET',
        'type' => 'app',
        'params' => 'Public',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'process' => $cAddProcess->clear()
            ->set(RPERENDMROLE, $PLProcess['queryPerDevForApp'])
            ->set(RCOMENDMROLE, $PLProcess['queryComDevForApp'])
            ->set(
                RPERENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryPerDevForApp']
                )
            )
            ->set(
                RCOMENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryComDevForApp']
                )
            )
            ->get(),
    ],
    'getusercompubdevice' => [
        'method' => 'GET',
        'type' => 'app',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
        'process' => $cAddProcess->clear()
            ->set(RPERENDMROLE, $PLProcess['queryPerPubDevForApp'])
            ->set(RCOMENDMROLE, $PLProcess['queryComPubDevForApp'])
            ->set(
                RPERENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryPerPubDevForApp']
                )
            )
            ->set(
                RCOMENDSROLE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['queryComPubDevForApp']
                )
            )
            ->get(),
    ],
    'getpersonalpcdevice' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['page', 'row', 'MAC', 'Location', 'Type'],
        'process' => $PLProcess['queryDevForWeb'],
    ],
    'changelocation' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['Location', 'ID'],
        'process' => $cAddProcess->clear()
            ->set(RPERENDMROLE, $PLProcess['editPerLocation'])
            ->set(RCOMENDMROLE, $PLProcess['editComLocation'])
            ->get(),
    ],
    'addfromuser' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERENDMROLE],
        'params' => ['Location', 'DeviceCode'],
        'process' => $PLProcess['addUserDevice'],
    ],
    // 已经废弃
    'addfromuserapp' => [
        'method' => 'POST',
        'type' => 'app',
        'roles' => [RPERENDMROLE],
        'params' => ['Location', 'DeviceCode'],
        'process' => $PLProcess['addUserDevice'],
    ],
    'deletefromuser' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERENDMROLE],
        'params' => ['ID'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'perDevInUser', 'params' => 'ID'],
                ],
            ],
            $PLProcess['deletePer']
        ),
    ],
    'getaccountdev' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['getAccountDevice'],
    ],
//    'getpersonalalarm' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'params' => ['row', 'page'],
//        'process' => $cAddProcess->clear()
//            ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getAlarmForApp'])
//            ->set(
//                [RPERENDSROLE, RCOMENDSROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                        ],
//                    ],
//                    $PLProcess['getAlarmForApp']
//                )
//            )
//            ->get(),
//    ],
    'getwebpersonalalarm' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['Status', 'AlarmType', 'BeginTime', 'EndTime', 'Location', 'Device', 'page', 'row'],
        'process' => $PLProcess['getAlarmForWeb'],
    ],
    'dealpersonalalarm' => [
        'method' => 'POST',
        'type' => 'app',
        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
        'params' => ['ID', 'Result'],
        'process' => $cAddProcess->clear()
            ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['dealAlarm'])
            ->set(
                [RPERENDSROLE, RCOMENDSROLE],
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setSubToMainAlias'],
                        ],
                    ],
                    $PLProcess['dealAlarm']
                )
            )
            ->get(),
    ],
    'getwebpersonalunalarmnum' => [
        'method' => 'GET',
        'type' => 'web',
        'process' => $PLProcess['getUndealAlarmNum'],
    ],

    // 'getpersonalcaptrue' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE],
    //     'params' => ['MAC', 'BeginTime', 'EndTime', 'row', 'page'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE], $PLProcess['getCaptrueForApp'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getCaptrueForApp']
    //             )
    //         )
    //         ->get(),
    // ],
    'getpersonalcaptrueweb' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['MAC', 'BeginTime', 'EndTime', 'row', 'page'],
        'process' => $PLProcess['getCaptrueForWeb'],
    ],
    'getpersonalmotionweb' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['MAC', 'BeginTime', 'EndTime', 'row', 'page'],
        'process' => $PLProcess['getMotionForWeb'],
    ],
    // 'getpersonalmotion' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'params' => ['MAC', 'BeginTime', 'EndTime', 'row', 'page'],
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getMotionForApp'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getMotionForApp']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'getnewpersonalcaptrue' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'roles' => [RCOMENDMROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'params' => ['MAC', 'row', 'page'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getDoorLogForApp'])
    //         ->set(
    //             RCOMENDSROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getDoorLogForApp']
    //             )
    //         )
    //         ->get(),
    // ],
    // bug
    // 'getappdoorlog' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'roles' => [RCOMENDMROLE, RCOMENDSROLE, RPERENDMROLE, RPERENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'params' => ['MAC', 'row', 'page', 'DeviceIndex', 'LockIndex'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMENDMROLE, RPERENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getDoorLogForApp2'])
    //         ->set(
    //             [RCOMENDSROLE, RPERENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['getDoorLogForApp2']
    //             )
    //         )
    //         ->get(),
    // ],

    // 'delpersonalcaptrue' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE,RPMENDMROLE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE], $PLProcess['delCaptureForApp'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['delCaptureForApp']
    //             )
    //         )
    //         ->get(),
    // ],

    'deletepersonalcapture' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['ID'],
        'process' => $PLProcess['delCaptureForWeb'],
    ],

    // 'delpersonalmotion' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['delMotionForApp'])
    //         ->set([RPMENDMROLE], $PLProcess['delMotionForPmApp'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['delMotionForApp']
    //             )
    //         )
    //         ->get(),
    // ],

    'deletepersonalmotion' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['ID'],
        'process' => $PLProcess['delMotionForWeb'],
    ],

    'getpersonaltmp' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['row', 'page', 'serchKey', 'serchValue'],
        'process' => $PLProcess['getTmpKey'],
    ],

//    'getpersonalapptmp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'params' => ['row', 'page', 'IsGetNowTempKey'],
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'process' => $cAddProcess->clear()
//            ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getTmpKeyApp'])
//            ->set(
//                [RPERENDSROLE, RCOMENDSROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                        ],
//                    ],
//                    $PLProcess['getTmpKeyApp']
//                )
//            )
//            ->get(),
//    ],
    //enduser 给家庭成员添加rfcard
//    'addPersonalRfCardForApp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE],
//        'params' => ['UserID', 'Code'],
//        'process' => $PLProcess['addRFCard'],
//    ],
    //enduser 编辑rfcard
//    'editPersonalRfCardForApp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE],
//        'params' => ['ID', 'UserID', 'Code'],
//        'process' => $PLProcess['editRFCard'],
//    ],
    //enduser 删除rfcard
//    'delPersonalRfCardForApp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delRFCardForUser'],
//    ],
    //enduser 获取rfcard列表
//    'getPersonalRfCardListForApp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE],
//        'params' => ['row', 'page'],
//        'process' => $PLProcess['getRfCard'],
//    ],
    'getpersonalprivate' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['row', 'page', 'serchKey', 'serchValue'],
        'process' => $PLProcess['getPrivateKey'],
    ],
    'getpersonalrf' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'params' => ['row', 'page', 'serchKey', 'serchValue'],
        'process' => $PLProcess['getRfCard'],
    ],
    'addpersonalprivate' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['addPrivateKey'],
    ],
    'modpersonalprivate' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['editPrivateKey'],
    ],
    'addpersonalrf' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['addRFCard'],
    ],
    'modpersonalrf' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['editRFCard'],
    ],
    'addcompri' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['RoomID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['addComPrivateKey'],
    ],
    'modcompri' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID', 'RoomID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['editComPrivateKey'],
    ],
    'addcomrfc' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['RoomID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['addComRFCard'],
    ],
    'modcomrfc' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID', 'RoomID', 'Allow', 'ExpireTime', 'UserID', 'Code'],
        'process' => $PLProcess['editComRFCard'],
    ],
    'deletepersonalprivate' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delPrivateKeyForUser'],
    ],
    'deletepersonalrfcard' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delRFCardForUser'],
    ],
    'deletepersonaltmpkey' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delTmpKeyForUser'],
    ],
    // 'deletepersonaltmpkeyapp' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'params' => ['ID'],
    //     'process' => $PLProcess['delTmpKeyForUser'],
    // ],
    'deletecompri' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delComPrivateKeyForUser'],
    ],
    'deletecomrfc' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delComRFCardForUser'],
    ],
    'deletecomtmp' => [
        'method' => 'POST',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['delTmpKeyForUser'],
    ],
    // 'addpersonalapptmp' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
    //     'params' => [
    //         'DateFlag',
    //         'StartDay',
    //         'StopDay',
    //         'StartTime',
    //         'StopTime',
    //         'SchedulerType',
    //         'AllowedTimes',
    //         'Description',
    //         'IDNumber',
    //         'MAC'
    //     ],
    //     'process' => $cAddProcess->clear()
    //         ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['addTempKeyForUser'])
    //         ->set(
    //             [RPERENDSROLE, RCOMENDSROLE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSubToMainAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['addTempKeyForUser']
    //             )
    //         )
    //         ->get(),
    // ],
    'personregister' => [
        'method' => 'POST',
        'auth' => false,
        'params' => ['Name', 'Password', 'Email', 'VerCode', 'DeviceCode'],
        'process' => $PLProcess['register'],
    ],
    'personemailcode' => [
        'method' => 'POST',
        'auth' => false,
        'params' => ['Email'],
        'process' => $PLProcess['getVerCode'],
    ],
    'checkcode' => [
        'method' => 'POST',
        'auth' => false,
        'params' => ['Email', 'DeviceCode', 'VerCode'],
        'process' => $PLProcess['deviceCodeCheck'],
    ],
    // 'sendemail' => [
    //     'method' => 'POST',
    //     'auth' => false,
    //     'params' => ['EmailOrId'],
    //     'process' => $PLProcess['sendRsPwEmail'],
    // ],
    // 'personresetpw' => [
    //     'method' => 'POST',
    //     'auth' => false,
    //     'params' => ['TOKEN', 'Password'],
    //     'process' => $PLProcess['resetPwUserPw'],
    // ],
    // 'persongetname' => [
    //     'method' => 'GET',
    //     'auth' => false,
    //     'params' => ['TOKEN'],
    //     'process' => $PLProcess['getResetPwName'],
    // ],
//    'addpersonalmessage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RCOMMUNITYGRADE, RPERSONGRADE],
//        'params' => ['Message', 'MessageTitle', 'Recevier', 'isAllMessage', 'ClientType'],
//        'process' => $PLProcess['addMessage'],
//    ],
//    'deletepersonalmessage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RCOMMUNITYGRADE, RPERSONGRADE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delMngMessage'],
//    ],
//    'getnotification' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'params' => ['row', 'page'],
//        'process' => $cAddProcess->clear()
//            ->set([RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE], $PLProcess['getMessageForApp'])
//            ->set(
//                [RPERENDSROLE, RCOMENDSROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                        ],
//                    ],
//                    $PLProcess['getMessageForApp']
//                )
//            )
//            ->get(),
//    ],
//    'getnotificationdetail' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'params' => ['ID', 'NoticeType'],
//        'process' => $PLProcess['getMessageDetail'],
//    ],
//    'deletemessageforapp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'params' => ['ID', 'VoiceMsgID'],
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE],
//        'process' => $PLProcess['delUserMessage'],
//    ],
    // 'getpersonalmessageSys' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'params' => ['Key'],
    //     'process' => $cAddProcess->clear()
    //         ->set(RPERSONGRADE, $PLProcess['getMessagePerUser'])
    //         ->set(RCOMMUNITYGRADE, $PLProcess['getMessageComUser'])
    //         ->get(),
    // ],
    'getpersonallog' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['DeviceID', 'Result', 'BeginTime', 'EndTime', 'Type', 'page', 'row'],
        'process' => $PLProcess['getUserLog'],
    ],
    'createuserorder' => [
        'method' => 'POST',
        'type' => 'web',
        'active' => false,
        'params' => ['Type', 'Total', 'Count', 'ID'],
        'process' => $PLProcess['createUserOrder'],
    ],
    'createuserorderapp' => [
        'method' => 'POST',
        'type' => 'app',
        'active' => false,
        'params' => ['Type', 'Total', 'Count', 'ID'],
        'process' => $PLProcess['createUserOrder'],
    ],
    'installcreateorder' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPERSONGRADE],
        'params' => ['Type', 'Total', 'Count', 'Users', 'ID'],
        'process' => $PLProcess['createInstallOrder'],
    ],
    'communitycreateorder' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
        'params' => ['Type', 'Total', 'Count', 'Users', 'ID'],
        'process' => $PLProcess['createInstallOrder'],
    ],
//    'getcombuild' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RCOMMUNITYGRADE, ROFFICEGRADE, RPROPERTYMANAGE],
//        'process' => $cAddProcess->clear()
//            ->set([RCOMMUNITYGRADE, ROFFICEGRADE], $PLProcess['getAllBuild'])
//            ->set(
//                [RPROPERTYMANAGE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['getAllBuild']
//                )
//            )
//            ->get(),
//    ],
    // 'addcombuild' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE, RPROPERTYMANAGE],
    //     'params' => ['Name'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMMUNITYGRADE, ROFFICEGRADE], $PLProcess['addBuild'])
    //         ->set(
    //             [RPROPERTYMANAGE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPMAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['addBuild']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'delcombuild' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE, RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMMUNITYGRADE, ROFFICEGRADE], $PLProcess['delBuild'])
    //         ->set(
    //             [RPROPERTYMANAGE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPMAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['delBuild']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'modcombuild' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE, RPROPERTYMANAGE],
    //     'params' => ['ID', 'Name'],
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMMUNITYGRADE, ROFFICEGRADE], $PLProcess['editBuild'])
    //         ->set(
    //             [RPROPERTYMANAGE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPMAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['editBuild']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'getcominfo' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'process' => $PLProcess['getCommunityInfo'],
    // ],
    // 'addcomdevice' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'params' => [
    //         'Build',
    //         'NetGroupNumber',
    //         'MAC',
    //         'Relay',
    //         'Location',
    //         'Type',
    //         'NodeID',
    //         'ArmingFunction',
    //         'StairShow',
    //         'IsAllBuild',
    //         'Builds',
    //         'SecurityRelay'
    //     ],
    //     'process' => $PLProcess['addCommunityDevice'],
    // ],
    // 'deletecomdevice' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteCommunityDevice'],
    // ],
    // 'batchdelcomdevice' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteBetCommunityDevice'],
    // ],
    // 'modcomdevice' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'params' => [
    //         'Build',
    //         'NetGroupNumber',
    //         'Relay',
    //         'SecurityRelay',
    //         'MAC',
    //         'Location',
    //         'Type',
    //         'NodeID',
    //         'ID',
    //         'ArmingFunction',
    //         'StairShow',
    //         'IsAllBuild',
    //         'Builds'
    //     ],
    //     'process' => $PLProcess['editCommunityDevice'],
    // ],
    'getcomdevice' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
        'params' => ['ParentID', 'ID', 'row', 'page', 'serchKey', 'serchValue'],
        'process' => $PLProcess['getCommunityDevice'],
    ],
    'getallcompubdevice' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['Key'],
        'process' => $PLProcess['getCommunityAllPubDev'],
    ],
    'getpdevice' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['Build', 'Room', 'Type', 'Status', 'serchKey', 'serchValue', 'page', 'row'],
        'process' => $PLProcess['getPMDevice'],
    ],
    'modpdevice' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['Location', 'ID', 'Relay', 'StairShow', 'SecurityRelay'],
        'process' => $PLProcess['eidtPMDevice'],
    ],
//    'getpdeviceinfo' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['getPMDeviceInfo'],
//    ],
//    'getpdoordevice' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['Build'],
//        'process' => $PLProcess['getPMDoorDevice'],
//    ],
    'addcomuser' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => [
            'ID',
            'FirstName',
            'LastName',
            'Email',
            'MobileNumber',
            'Phone',
            'Phone2',
            'Phone3',
            'PhoneCode',
            'CallType',
            'Key',
            'Language'
        ],
        'process' => $PLProcess['addComMainUserForCom'],
    ],
    // 'addcomroom' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE],
    //     'params' => [
    //         'ID',
    //         'BuildID',
    //         'RoomNumber',
    //         'RoomName',
    //         'IsAddUser',
    //         'FirstName',
    //         'LastName',
    //         'Email',
    //         'MobileNumber',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'CallType',
    //         'Key',
    //         'Language',
    //         'EnableIpDirect',
    //         'IsComMonitor',
    //         'MAC',
    //         'ArmingFunction',
    //         'NetGroupNumber',
    //         'Location',
    //         'Relay',
    //         'Layout',
    //         'LayoutName'
    //     ],
    //     'process' => $PLProcess['addComRoom'],
    // ],
    'modcomuser' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => [
            'ID',
            'FirstName',
            'LastName',
            'Email',
            'MobileNumber',
            'Phone',
            'Phone2',
            'Phone3',
            'PhoneCode',
            'CallType',
            'Key'
        ],
        'process' => $PLProcess['eidtComMainUserFromCom'],
    ],
    // 'modcomroom' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE],
    //     'params' => [
    //         'ID',
    //         'RoomNumber',
    //         'RoomName',
    //         'IsAddUser',
    //         'FirstName',
    //         'LastName',
    //         'Email',
    //         'MobileNumber',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'CallType',
    //         'Key',
    //         'Language',
    //         'EnableIpDirect',
    //         'IsComMonitor',
    //         'MAC',
    //         'ArmingFunction',
    //         'NetGroupNumber',
    //         'Location',
    //         'Relay',
    //         'NodeID',
    //     ],
    //     'process' => $PLProcess['eidtRoom'],
    // ],
    // 'deletecomuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteComUser'],
    // ],
    // 'deletecomuserroom' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteRoom'],
    // ],
    'batchdelcomuser' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['ID'],
        'process' => $PLProcess['betchDeleteComUser'],
    ],
    'batchdelcomuserroom' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['ID'],
        'process' => $PLProcess['betchDeleteComUser'],
    ],
    // 'comresetpw' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, ROFFICEGRADE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['resetUserPw']
    //     ),
    // ],
    'getallcomuser' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['Build'],
        'process' => $PLProcess['queryAllBuildComUser'],
    ],
    'getcomusreinfo' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['ID'],
        'process' => $PLProcess['queryComUserInfoForMng'],
    ],
    // 'addcompmanage' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['FirstName', 'LastName', 'Email', 'Language'],
    //     'process' => $PLProcess['addPropertyWorker'],
    // ],
    'modcompmanage' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'params' => ['FirstName', 'LastName', 'ID'],
        'process' => $PLProcess['editPropertyWorker'],
    ],
//    'deletecompmanage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPropertyWorker'],
//    ],
    // 'resetpwcompmanage' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['resetPwPropertyWorker'],
    // ],
    'getcompmanage' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'process' => $PLProcess['getPropertyWorker'],
    ],
    'getpusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['Build', 'Room', 'Stauts', 'Active', 'serchKey', 'serchValue', 'page', 'row', 'SortField', 'Sort'],
        'process' => $PLProcess['queryComUserForPM'],
    ],
    'getpsubusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['ID'],
        'process' => $PLProcess['queryComSubForPM'],
    ],
    'getpusersinfo' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['ID'],
        'process' => $PLProcess['queryUserInfoForPM'],
    ],
    // 'addpuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => [
    //         'Build',
    //         'Room',
    //         'FirstName',
    //         'LastName',
    //         'Email',
    //         'MobileNumber',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'PhoneState',
    //         'Key',
    //         'TempKeyPermission',
    //         'RfCard',
    //         'RoomNumber',
    //         'EnableIpDirect'
    //     ],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setInitLandline'],
    //             ],
    //         ],
    //         $PLProcess['addComMainForPM']
    //     ),
    // ],
    // 'addpsubuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //                 ['name' => 'setInitLandline'],
    //                 ['name' => 'setMngToMainActiveAlias', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['addCSUser']
    //     ),
    // ],
    // 'modpuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => [
    //         'ID',
    //         'Build',
    //         'Room',
    //         'FirstName',
    //         'LastName',
    //         'Email',
    //         'MobileNumber',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode',
    //         'PhoneState',
    //         'Key',
    //         'RfCard',
    //         'RoomNumber',
    //         'TempKeyPermission',
    //         'EnableIpDirect'
    //     ],
    //     'process' => $PLProcess['eidtComMainUserFromPM'],
    // ],
    // 'modpsubuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID', 'FirstName', 'LastName', 'Phone', 'PhoneCode'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['updateCSUser']
    //     ),
    // ],
    // 'deletepuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['betchDeleteComUser']
    //     ),
    // ],
    // 'deletepsubuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //                 ['name' => 'subInPCMngCheck', 'params' => ['ID']],
    //                 ['name' => 'setMngToMainAliasWSubId', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['deleteSUser']
    //     ),
    // ],
//    'resetpwpuser' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setPMAlias'],
//                    ['name' => 'mainUserInPCMngCheck', 'params' => 'ID'],
//                ],
//            ],
//            $PLProcess['resetUserPw']
//        ),
//    ],
    'getpactiveunusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['Active', 'row', 'page'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'setPMAlias'],
                ],
            ],
            $PLProcess['getComActiveOrUn']
        ),
    ],
    'getcommunityactiveusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'params' => ['Active', 'row', 'page'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'getMngTimeZone'],
                ],
            ],
            $PLProcess['getComActiveOrUn']
        ),
    ],
    'getinstallactiveusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPERSONGRADE],
        'params' => ['Active', 'row', 'page'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'getMngTimeZone'],
                ],
            ],
            $PLProcess['getPerActiveOrUn']
        ),
    ],
    // 'getinstallchargeplan' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPERSONGRADE],
    //     'process' => $PLProcess['getPerMonthlyPay'],
    // ],
    // 'getcommunitychargeplan' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE],
    //     'process' => $PLProcess['getComMonthlyPay'],
    // ],
    'getpnextexpiretimeusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['ID', 'Count'],
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'setPMAlias'],
                ],
            ],
            $PLProcess['getComNextExpireTimeUser']
        ),
    ],
    'getcommunitynextexpiretimeusers' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['ID', 'Count'],
        'role' => [RCOMMUNITYGRADE],
        'process' => $PLProcess['getComNextExpireTimeUser'],
    ],
    'getlandlinenextexpiretimeusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPERSONGRADE],
        'params' => ['ID', 'Count'],
        'process' => $PLProcess['getLandlineExpireTimeUsers'],
    ],
    // 'getpbillinfo' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getPMBill'],
    // ],
    // 'setpbillinfo' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['BillingTitle', 'Contactor', 'Street', 'City', 'Postcode', 'Country', 'TelePhone', 'Fax'],
    //     'process' => $PLProcess['editPMBill'],
    // ],
    // 'getpreceipt' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['OrderID'],
    //     'process' => $PLProcess['getPMReceipt'],
    // ],
    // 'installgetreceipt' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['OrderID'],
    //     'process' => $PLProcess['getInstallReceipt'],
    // ],
    // 'getpcapture' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['Type', 'StartTime', 'EndTime', 'page', 'row'],
    //     'process' => $PLProcess['queryCaptureForPM'],
    // ],
    // 'deletepcapture' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteCaptureForPM'],
    // ],
//    'setusercnf' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RSUPERGRADE],
//        'params' => ['ID', 'ValidTime'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'changeParamName', 'params' => ['ValidTime' => 'ExpireTime']],
//                ],
//            ],
//            $PLProcess['setMianUserExpieTime']
//        ),
//    ],
    // 'getpdoorlog' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => [
    //         'TmpKey',
    //         'LogType',
    //         'StartTime',
    //         'EndTime',
    //         'page',
    //         'row',
    //         'serchKey',
    //         'serchValue',
    //         'Build',
    //         'TempKeyID'
    //     ],
    //     'process' => $PLProcess['queryDoorLogForPM'],
    // ],
    // 'getpcallhistory' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['Key', 'StartTime', 'EndTime', 'page', 'row'],
    //     'process' => $PLProcess['queryCallForPM'],
    // ],
    // 'getpmessage' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['serchKey', 'serchValue', 'page', 'row'],
    //     'process' => $PLProcess['getMessageForPM'],
    // ],
    // 'getpersonalmessage' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'params' => ['serchKey', 'serchValue', 'page', 'row'],
    //     'process' => $PLProcess['getMessageMng'],
    // ],
//    'addpmessage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['Message', 'MessageTitle', 'Recevier', 'isAllMessage', 'ClientType'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setPMAlias'],
//                ],
//            ],
//            $PLProcess['addMessage']
//        ),
//    ],
//    'deletepmessage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setPMAlias'],
//                ],
//            ],
//            $PLProcess['betchDelMngMessage']
//        ),
//    ],
    // 'getpmessageusers' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['Key', 'Build'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['getMessageComUser']
    //     ),
    // ],
    // 'getpcommunitydata' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getComDataAllCountForPM'],
    // ],
    // 'getpcommunitytmotion' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getComMotionDataForPM'],
    // ],
    'getpallcommunity' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'process' => $PLProcess['getComListForPM'],
    ],
//    'getlastdoorlog' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getComLastFiveDoorLogForPM'],
//    ],
//    'getpweekdata' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getWeekDataForPM'],
//    ],
    // 'getpcommunitydetail' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getCommunityDetailForPM'],
    // ],
//    'setpcommunitydetail' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Location',
//            'Street',
//            'City',
//            'PostalCode',
//            'States',
//            'Country',
//            'AptPinType',
//            'DevOfflineNotify',
//            'EnableSIMWarning',
//            'EnableUserPin',
//            'PhoneCode',
//            'MobileNumber',
//            'TriggerAction'
//        ],
//        'process' => $PLProcess['setCommunityDetailForPM'],
//    ],
    'getcommunitybr' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE, RCOMMUNITYGRADE, RAREAGRADE],
        'params' => ['CommunityID'],
        'process' => $cAddProcess->clear()
            ->set(RCOMMUNITYGRADE, $PLProcess['getBuildRoomForPM'])
            ->set(
                RPROPERTYMANAGE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ["name" => "setPMAlias"],
                        ],
                    ],
                    $PLProcess['getBuildRoomForPM']
                )
            )
            ->set(RAREAGRADE, $PLProcess['getBuildRoomForDis'])
            ->get(),
    ],
//    'setptime' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['TimeZone', 'CustomizeForm'],
//        'process' => $PLProcess['setTimeZoneForPM'],
//    ],
//    'setpmotion' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['EnableMotion', 'MotionTime'],
//        'process' => $PLProcess['setMotionForPM'],
//    ],
    // 'getpchargeplan' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //                 ['name' => 'changeParamValue', 'params' => ['Type' => PAYADDAPP]],
    //             ],
    //         ],
    //         $PLProcess['queryComCharge']
    //     ),
    // ],
    'createdpmorder' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['Type', 'Total', 'Count', 'Users', 'ID'],
        'process' => $PLProcess['createdCommunityOrder'],
    ],
    'discreateinstallorder' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RAREAGRADE],
        'params' => ['Type', 'Total', 'Count', 'Users', 'Manage'],
        'process' => $PLProcess['createAreaInstallOrder'],
    ],
    // 'discreatecommunityorder' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RAREAGRADE],
    //     'params' => ['Type', 'Total', 'Count', 'Users', 'Manage'],
    //     'process' => $PLProcess['createAreaCommunityOrder'],
    // ],
    // 'discreateofficeorder' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RAREAGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['Type', 'Total', 'Count', 'Users', 'Manage'],
    //     'process' => $PLProcess['createAreaOfficeOrder'],
    // ],
//    'getpmorderlist' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['PayType', 'Type', 'Status', 'Key', 'BeginTime', 'EndTime', 'row', 'page'],
//        'process' => $PLProcess['getOrderListForPM'],
//    ],
//    'getporderinfo' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'params' => ['ID'],
//        'role' => [RPROPERTYMANAGE],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setPMAlias'],
//                ],
//            ],
//            $PLProcess['getOrderInfo']
//        ),
//    ],
    // 'deletepotder' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteUserOrder'],
    // ],
    // 'getpchargemode' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getPMChargeMode'],
    // ],
//    'setvisitor' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['IDCardVerification', 'FaceEnrollment'],
//        'process' => $PLProcess['setComVisitorForPM'],
//    ],
//    'addptmpkey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'ExpireTime',
//            'SchedulerType',
//            'Allow',
//            'Description',
//            'IDNumber',
//            'Delivery',
//            'Room',
//            'OfficeUserID'
//        ],
//        'process' => $PLProcess['addTempKeyForPM'],
//    ],
//    'getptmpkey' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['serchKey', 'serchValue', 'page', 'row'],
//        'process' => $PLProcess['queryTempKeyForPM'],
//    ],
//    'deleteptmpkey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delTempKeyForPM'],
//    ],
//    'getptmpkeyinfo' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['queryTempKeyInfoForPM'],
//    ],
//    'addpdprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['addPMAPKey'],
//    ],
//    'addpdrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['addPMARKey'],
//    ],
    'addpsprikey' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => [
            'Name',
            'Key',
            'MAC',
            'DateFlag',
            'StartTime',
            'StopTime',
            'BeginTime',
            'EndTime',
            'SchedulerType'
        ],
        'process' => $PLProcess['addPMSPKey'],
    ],
//    'addpsrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['addPMSRKey'],
//    ],
//    'addprprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'AccountID',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['addPMRPKey'],
//    ],
//    'addprrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'AccountID',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['addPMRRKey'],
//    ],
//    'importprrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['importPMRRKey'],
//    ],
//    'modpdprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'ID',
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['editPMAPKey'],
//    ],
//    'importpsrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['importPMSRKey'],
//    ],
//    'modpdrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'ID',
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['editPMARKey'],
//    ],
    'modpsprikey' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => [
            'ID',
            'Name',
            'Key',
            'MAC',
            'DateFlag',
            'StartTime',
            'StopTime',
            'BeginTime',
            'EndTime',
            'SchedulerType'
        ],
        'process' => $PLProcess['editPMSPKey'],
    ],
//    'modpsrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'ID',
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['editPMSRKey'],
//    ],
//    'modprprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'ID',
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['editPMRPKey'],
//    ],
//    'modprrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => [
//            'ID',
//            'Name',
//            'Key',
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'EndTime',
//            'SchedulerType'
//        ],
//        'process' => $PLProcess['editPMRRKey'],
//    ],
//    'delpdprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPMAPKey'],
//    ],
//    'delpdrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPMARKey'],
//    ],
    'delpsprikey' => [
        'method' => 'POST',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['ID'],
        'process' => $PLProcess['delPMSPKey'],
    ],
//    'delpsrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPMSRKey'],
//    ],
//    'deletepallstaffcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['Type', 'PassWd'],
//        'process' => $PLProcess['delPMAllSRKey'],
//    ],
//    'delprprikey' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPMRPKey'],
//    ],
//    'delprrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPMRRKey'],
//    ],
//    'deletepallusercard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['PassWd', 'Type'],
//        'process' => $PLProcess['delPMAllRRKey'],
//    ],
//    'getpdprikey' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['row', 'page', 'serchKey', 'serchValue'],
//        'process' => $PLProcess['getPMAPKey'],
//    ],
//    'getpdrfcard' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['row', 'page', 'serchKey', 'serchValue'],
//        'process' => $PLProcess['getPMARKey'],
//    ],
    'getpsprikey' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'params' => ['row', 'page', 'serchKey', 'serchValue'],
        'process' => $PLProcess['getPMSPKey'],
    ],
//    'getpsrfcard' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['row', 'page', 'serchKey', 'serchValue'],
//        'process' => $PLProcess['getPMSRKey'],
//    ],
//    'getprprikey' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['row', 'page', 'Build', 'Room', 'Key'],
//        'process' => $PLProcess['getPMRPKey'],
//    ],
//    'getprrfcard' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['row', 'page', 'Build', 'Room', 'Key'],
//        'process' => $PLProcess['getPMRRKey'],
//    ],
    // 'getppubdevice' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['getAllPubDevForKeyChoose'],
    // ],
    'getpalldevice' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RPROPERTYMANAGE],
        'process' => $PLProcess['getAllDevForKeyChoose'],
    ],
    // 'getinstallbill' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPCMngToInstaller'],
    //             ],
    //         ],
    //         $PLProcess['getMngBill']
    //     ),
    // ],
    // 'setinstallbill' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['BillingTitle', 'Contactor', 'Street', 'City', 'Postcode', 'Country', 'TelePhone', 'Fax', 'Email'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPCMngToInstaller'],
    //             ],
    //         ],
    //         $PLProcess['setMngBill']
    //     ),
    // ],
    // V5.2
    // 'getdisbill' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RAREAGRADE],
    //     'process' => $PLProcess['getMngBill'],
    // ],
    // V5.2
    // 'setdisbill' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RAREAGRADE],
    //     'params' => ['BillingTitle', 'Contactor', 'Street', 'City', 'Postcode', 'Country', 'TelePhone', 'Fax', 'Email'],
    //     'process' => $PLProcess['setMngBill'],
    // ],
    // 'addcommunitysubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID', 'Name', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setMngToMainActiveAlias', 'params' => ['ID']],
    //                 ['name' => 'setInitLandline'],
    //             ],
    //         ],
    //         $PLProcess['addCSUser']
    //     ),
    // ],
    // 'addpersubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID', 'Name', 'Email', 'MobileNumber', 'Phone', 'PhoneCode'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setMngToMainActiveAlias', 'params' => ['ID']],
    //                 ['name' => 'setInitLandline'],
    //             ],
    //         ],
    //         $PLProcess['addPSUser']
    //     ),
    // ],
    // 'modcommunitysubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID', 'FirstName', 'LastName', 'Phone', 'PhoneCode'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $PLProcess['updateCSUser'],
    // ],
    // 'modpersubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID', 'Name', 'MobileNumber', 'Phone', 'PhoneCode'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $PLProcess['updatePSUser'],
    // ],
    // 'deletepersubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'subInPCMngCheck', 'params' => ['ID']],
    //                 ['name' => 'setMngToMainAliasWSubId', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['deleteSUser']
    //     ),
    // ],
    // 'deletecommunitysubuser' => [
    //     'method' => 'POST',
    //     'params' => ['ID'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
    //     'type' => 'web',
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'subInPCMngCheck', 'params' => ['ID']],
    //                 ['name' => 'setMngToMainAliasWSubId', 'params' => 'ID'],
    //             ],
    //         ],
    //         $PLProcess['deleteSUser']
    //     ),
    // ],
    'getsubusers' => [
        'method' => 'GET',
        'params' => ['ID', 'page', 'row'],
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
        'type' => 'web',
        'process' => $cAddProcess->unshift(
            [
                'type' => 'middle',
                'queue' => [
                    ['name' => 'mainUserInPCMngCheck', 'params' => ['ID']],
                ],
            ],
            $PLProcess['getSubForMng']
        ),
    ],
//    'getcustomerserverpersonapp' => [
//        'method' => 'GET',
//        'roles' => [RPERENDMROLE, RCOMENDMROLE, RPERENDSROLE, RCOMENDSROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
//        'type' => 'app',
//        'process' => $cAddProcess->clear()
//            ->set(
//                [RPERENDMROLE, RCOMENDMROLE, ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setMainToPCMngAlias'],
//                        ],
//                    ],
//                    $PLProcess['getInstallerCustomerService']
//                )
//            )
//            ->set(
//                [RPERENDSROLE, RCOMENDSROLE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setSubToMainAlias'],
//                            ['name' => 'setMainToPCMngAlias'],
//                        ],
//                    ],
//                    $PLProcess['getInstallerCustomerService']
//                )
//            )
//            ->get(),
//    ],
    'getcustomerserverperson' => [
        'method' => 'GET',
        'roles' => [RPERENDMROLE, RCOMENDMROLE],
        'type' => 'web',
        'process' => $cAddProcess->clear()
            ->set(
                [RPERENDMROLE, RCOMENDMROLE],
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'setMainToPCMngAlias'],
                        ],
                    ],
                    $PLProcess['getInstallerCustomerService']
                )
            )
            ->get(),
    ],
//    'getcustomerserver' => [
//        'method' => 'GET',
//        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'type' => 'web',
//        'process' => $cAddProcess->clear()
//            ->set([RSUPERGRADE, RAREAGRADE], $PLProcess['getSelfCustomerService'])
//            ->set([RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE], $PLProcess['getSelfCustomerService'])
//            ->get(),
//    ],
//    'modcustomerserver' => [
//        'method' => 'POST',
//        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => ['Phone', 'Email'],
//        'type' => 'web',
//        'process' => $PLProcess['editCustomerService'],
//    ],
    'getmaxset' => [
        'method' => 'GET',
        'type' => 'web',
        'process' => $PLProcess['getSysSet'],
    ],
    'getchargeplan' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE],
        'params' => ['ID', 'Type'],
        'process' => $PLProcess['queryManageCharge'],
    ],
    // 超级管理员获取个人收费数据
    // 'getperchargeplan' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'process' => $PLProcess['queryPerChargeSup'],
    // ],
    'setfeesupermanage' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE],
        'params' => [
            'EntryFee',
            'MonthlyFee',
            'AppNumber',
            'FeeApp',
            'MaxEntryFee',
            'MaxMonthlyFee',
            'MaxFeeApps',
            'MaxApps',
            'PerEntryFee',
            'LandlineFee',
            'PerAppNumber',
            'PerFeeApp'
        ],
        'process' => $PLProcess['setSupCharge'],
    ],
    // 废弃
    // "setfeemanage"=>[
    //     "method"=>"POST",
    //     "type"=>"web",
    //     "roles"=>[RSUPERGRADE],
    //     "params"=>["EntryFee","MonthlyFee","AppsNumber","AddAppsFee"],
    //     "process"=>$PLProcess["setManageCharge"]
    // ],
    // 'getendusercharge' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'params' => 'Type',
    //     'active' => false,
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPERENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryPerCharge']
    //             )
    //         )
    //         ->set(
    //             RCOMENDMROLE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'getAliasId'],
    //                         ['name' => 'changeParamName', 'params' => ['userAliasId' => 'ID']],
    //                         ['name' => 'setMainToPCMngAlias'],
    //                     ],
    //                 ],
    //                 $PLProcess['queryComCharge']
    //             )
    //         )
    //         ->get(),
    // ],
    'pay' => [
        'method' => 'POST',
        'type' => 'web',
        'active' => false,
        'params' => ['OrderID', 'PlatformOrderID', 'Type', 'Step'],
        'process' => $PLProcess['pay'],
    ],
    'payapp' => [
        'method' => 'POST',
        'type' => 'app',
        'active' => false,
        'params' => ['OrderID', 'PlatformOrderID', 'Type', 'Step'],
        'process' => $PLProcess['pay'],
    ],
    'getupdatedevice' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUPERGRADE],
        'params' => ['Status', 'Key', 'Version', 'row', 'page'],
        'process' => $PLProcess['getUpgradeList'],
    ],
    'getversionlist' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUPERGRADE],
        'params' => ['Model', 'DisID'],
        'process' => $PLProcess['getUpgradeVersion'],
    ],
    'addupdatecase' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUPERGRADE],
        'params' => ['Device', 'UpdateTime', 'Version', 'InsID', 'ProjectType', 'UpgradeType'],
        'process' => $PLProcess['addUpgrade'],
    ],
    'modupdatecase' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUPERGRADE],
        'params' => ['Device', 'UpdateTime', 'Version', 'ID', 'InsID', 'ProjectType'],
        'process' => $PLProcess['editUpgrade'],
    ],
    'deleteupdate' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUPERGRADE],
        'params' => ['ID'],
        'process' => $PLProcess['deleteUpgrade'],
    ],
    // 'deletedevice' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             [RSUPERGRADE, RAREAGRADE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setSupToComWDevId'],
    //                     ],
    //                 ],
    //                 $PLProcess['deleteCommunityDevice']
    //             )
    //         )
    //         ->set(RCOMMUNITYGRADE, $PLProcess['deleteCommunityDevice'])
    //         ->get(),
    // ],
    'getcommunitydevice' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RAREAGRADE],
        'params' => ['row', 'page', 'serchKey', 'serchValue', 'type'],
        'process' => $PLProcess['getComDevForAreaMng'],
    ],
    'getalldevice' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RSUPERGRADE],
        'params' => ['row', 'page', 'serchKey', 'serchValue', 'type'],
        'process' => $PLProcess['getComDevForSuperMng'],
    ],
    'getsmdevice' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'params' => ['ID', 'row', 'page', 'serchKey', 'serchValue'],
        'process' => $cAddProcess->clear()
            ->set(RSUPERGRADE, $PLProcess['getMacLibraryForSup'])
            ->set(RAREAGRADE, $PLProcess['getMacLibraryForArea'])
            ->set([RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE], $PLProcess['getMacLibraryForPCMng'])
            ->get(),
    ],
    'addmdevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'params' => ['ID', 'MAC'],
        'process' => $cAddProcess->clear()
            ->set(RSUPERGRADE, $PLProcess['addMacLibraryForSup'])
            ->set(RAREAGRADE, $PLProcess['addMacLibraryForArea'])
            ->set([RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE], $PLProcess['addMacLibraryForPCMng'])
            ->get(),
    ],
    'deletemdevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'params' => ['ID'],
        'process' => $PLProcess['deleteMacLibrary'],
    ],
    'mulmdevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'params' => ['ID'],
        'process' => $cAddProcess->clear()
            ->set(RSUPERGRADE, $PLProcess['uploadFileMacLibrarySup'])
            ->set(RAREAGRADE, $PLProcess['uploadFileMacLibraryArea'])
            ->set([RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE], $PLProcess['uploadFileMacLibraryPC'])
            ->get(),
    ],
    // TODO 设备权限控制
    // 'setdevconfig' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['MAC', 'SipType', 'Config'],
    //     'process' => $PLProcess['setDevConfig'],
    // ],
    // TODO 设备权限控制
    // 'setdevconfigonce' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['MAC', 'Config'],
    //     'process' => $PLProcess['setDevConfigOnce'],
    // ],
    // 'setpdevconfig' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RPROPERTYMANAGE],
    //     'params' => ['MAC', 'SipType', 'Config'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['setDevConfig']
    //     ),
    // ],
//    'setpdevconfigonce' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['MAC', 'Config'],
//        'process' => $cAddProcess->unshift(
//            [
//                'type' => 'middle',
//                'queue' => [
//                    ['name' => 'setPMAlias'],
//                ],
//            ],
//            $PLProcess['setDevConfigOnce']
//        ),
//    ],

    // TODO 设备权限控制
    // 'reboot' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['MAC'],
    //     'process' => $PLProcess['rebootDev'],
    // ],
    // 'preboot' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RPROPERTYMANAGE],
    //     'params' => ['MAC'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['rebootDev']
    //     ),
    // ],
    // TODO 设备权限控制
    // 'remote' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['MAC'],
    //     'process' => $PLProcess['remoteDev'],
    // ],
    // 'premote' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RPROPERTYMANAGE],
    //     'params' => ['MAC'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'setPMAlias'],
    //             ],
    //         ],
    //         $PLProcess['remoteDev']
    //     ),
    // ],
    // 'getptemperature' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RPROPERTYMANAGE],
    //     'params' => ['StartTime', 'EndTime', 'Status', 'page', 'row'],
    //     'process' => $PLProcess['getPMTemperature'],
    // ],
//    'getallpersondevice' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => ['Key', 'UnSelect'],
//        'process' => $PLProcess['getDevForUpgrade'],
//    ],
//    'getversion' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => ['row', 'page', 'serchKey', 'serchValue'],
//        'process' => $cAddProcess->clear()
//            ->set(RSUPERGRADE, $PLProcess['getVersionForSup'])
//            ->set(RAREAGRADE, $PLProcess['getVersionForArea'])
//            ->set(
//                [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPCMngToArea'],
//                        ],
//                    ],
//                    $PLProcess['getVersionForArea']
//                )
//            )
//            ->get(),
//    ],
//    'addversion' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['Log', 'Version', 'Url', 'ManageID', 'AllManage'],
//        'process' => $PLProcess['addVersion'],
//    ],
//    'deleteversion' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['ID'],
//        'process' => $PLProcess['deleteVersion'],
//    ],
//    'modifyversion' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['ID', 'Log', 'Version', 'Url', 'ManageID', 'AllManage'],
//        'process' => $PLProcess['editVersion'],
//    ],
//    'versionmanages' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'params' => ['page', 'row'],
//        'process' => $PLProcess['queryModel'],
//    ],
//    'versionmanageall' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'process' => $PLProcess['queryAllModel'],
//    ],
//    'versionmanagea' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['VersionName', 'VersionNumber', 'Type'],
//        'process' => $PLProcess['addModel'],
//    ],
//    'versionmanagem' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['VersionName', 'VersionNumber', 'ID', 'Type'],
//        'process' => $PLProcess['editModel'],
//    ],
//    'deleteversionmanage' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['ID'],
//        'process' => $PLProcess['deleteModel'],
//    ],
    // 'adduser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'params' => [
    //         'Account',
    //         'Role',
    //         'Email',
    //         'Phone',
    //         'Info',
    //         'TimeZone',
    //         'Community',
    //         'HouseCount',
    //         'EnableValidTimeSetting',
    //         'EnableCountSetting',
    //         'ChargeMode',
    //         'SipType',
    //         'Language',
    //         'Confusion',
    //         'IsEncryptPin',
    //         'IsVillaMonitor',
    //         'ProjectFeature'
    //     ],
    //     'process' => $PLProcess['addManager'],
    // ],
    // 'modifyuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'params' => [
    //         'ID',
    //         'Role',
    //         'Email',
    //         'Phone',
    //         'Info',
    //         'TimeZone',
    //         'Community',
    //         'HouseCount',
    //         'EnableValidTimeSetting',
    //         'EnableCountSetting',
    //         'ChargeMode',
    //         'SipType',
    //         'Confusion',
    //         'IsEncryptPin',
    //         'IsVillaMonitor',
    //         'ProjectFeature'
    //     ],
    //     'process' => $PLProcess['editManager'],
    // ],
    // 'deleteuser' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'params' => ['ID'],
    //     'process' => $cAddProcess->clear()
    //         ->set(RSUPERGRADE, $PLProcess['deleteManager'])
    //         ->set(
    //             RAREAGRADE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'manageInAMngCheck', 'params' => ['ID']],
    //                     ],
    //                 ],
    //                 $PLProcess['deleteManager']
    //             )
    //         )
    //         ->get(),
    // ],
    // 'resetpw' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'params' => ['Account'],
    //     'process' => $PLProcess['resetManagerPw'],
    // ],
    // 'getuser' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'params' => ['row', 'page', 'serchKey', 'serchValue'],
    //     'process' => $PLProcess['getAreaMngList'],
    // ],
    // 'getallarea' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'params' => ['Key'],
    //     'process' => $PLProcess['getAllAreaMng'],
    // ],
    // 'getuserpm' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'params' => ['row', 'page', 'serchKey', 'serchValue'],
    //     'process' => $cAddProcess->clear()
    //         ->set(RSUPERGRADE, $PLProcess['getInstallListForSup'])
    //         ->set(RAREAGRADE, $PLProcess['getInstallListForArea'])
    //         ->get(),
    // ],
    // 'changeindetity' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RAREAGRADE, RPERSONGRADE],
    //     'process' => $PLProcess['changeAreaIdentity'],
    // ],
    'isspecial' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
        'process' => $PLProcess['installerIsSpecial'],
    ],
    // 'changepcindetity' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['Account'],
    //     'process' => $PLProcess['changePerToCom'],
    // ],
    // 'getcomlist' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'params' => ['installerId'],
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE, RAREAGRADE],
    //     'process' => $PLProcess['getComGroupList'],
    // ],
    // 'addcomitem' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => [
    //         'Location',
    //         'Street',
    //         'City',
    //         'PostalCode',
    //         'Country',
    //         'States',
    //         'TimeZone',
    //         'AptPinType',
    //         'ChargeMode',
    //         'NumberOfApt',
    //         'EnableLandline',
    //         'CustomizeForm',
    //         'SendExpireEmailType',
    //         'SendRenew',
    //         'FeaturePlan',
    //         'EnableSmartHome'
    //     ],
    //     'process' => $PLProcess['addCommunity'],
    // ],
    // 'modcomitem' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => [
    //         'Location',
    //         'Street',
    //         'City',
    //         'PostalCode',
    //         'Country',
    //         'States',
    //         'TimeZone',
    //         'AptPinType',
    //         'ChargeMode',
    //         'ID',
    //         'CustomizeForm',
    //         'SendExpireEmailType',
    //         'SendRenew',
    //         'EnableSmartHome'
    //     ],
    //     'process' => $PLProcess['editCommunity'],
    // ],
    // 'deletecomitem' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['deleteCommunity'],
    // ],
    // 'getcomselwork' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'params' => ['CommunityID'],
    //     'process' => $PLProcess['queryComPerManage'],
    // ],
//    'chagnecompermange' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
//        'params' => ['CommunityID', 'IDs'],
//        'process' => $PLProcess['changeCommunityProperty'],
//    ],
    // 'getallareainstall' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'process' => $PLProcess['queryInstallArea'],
    // ],
    // 'setcommunitychargeplan' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'params' => ['NumberOfApt', 'EnableLandline', 'ID'],
    //     'process' => $PLProcess['setCommunity'],
    // ],
    'getallpersonaluser' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE],
        'params' => ['row', 'page', 'serchKey', 'serchValue', 'role'],
        'process' => $PLProcess['queryMainUserForSupArea'],
    ],
    // 'checkpw' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE, ROFFICEGRADE],
    //     'params' => ['PassWd'],
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPCMngToInstaller'],
    //                     ],
    //                 ],
    //                 $PLProcess['checkMngPw']
    //             )
    //         )
    //         ->set([RSUPERGRADE, RAREAGRADE, RPROPERTYMANAGE], $PLProcess['checkMngPw'])
    //         ->get(),
    // ],
    // 'changepw' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE, ROFFICEGRADE],
    //     'params' => ['PassWd'],
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             [RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'setPCMngToInstaller'],
    //                     ],
    //                 ],
    //                 $PLProcess['changeMngPw']
    //             )
    //         )
    //         ->set([RSUPERGRADE, RAREAGRADE, RPROPERTYMANAGE], $PLProcess['changeMngPw'])
    //         ->get(),
    // ],
    'myopeara' => [
        'method' => 'GET',
        'type' => 'web',
        'process' => $PLProcess['getMngOpera'],
    ],
    'changetz' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE, ROFFICEGRADE],
        'params' => ['TimeZone'],
        'process' => $PLProcess['changeMngTimeZone'],
    ],
    // 'getmanagechargemode' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'process' => $PLProcess['getMngChargeMode'],
    // ],
    // 'setmanagechargemode' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE],
    //     'params' => ['ChargeMode', 'SendExpireEmailType', 'SendRenew'],
    //     'process' => $PLProcess['setMngChargeMode'],
    // ],
    'importcommunitydata' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE, RPERSONGRADE],
        'params' => ['CommunityID'],
        'process' => $PLProcess['importCommunity'],
    ],
//    'login' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'auth' => false,
//        'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE],
//        'params' => ['Account', 'passwd'],
//        'process' => $PLProcess['manageLogin'],
//    ],
    // 'getallcommunity' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE],
    //     'params' => ['row', 'page', 'Key', 'AreaManage', 'Install'],
    //     'process' => $PLProcess['getAllCommunity'],
    // ],
    'personalaccount' => [
        'method' => 'GET',
        'type' => 'app',
        'process' => $PLProcess['getAccount'],
    ],
    'getaccountapp' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['ID'],
        'process' => $PLProcess['queryAApps'],
    ],
//    'getallorderlist' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => [
//            'Type',
//            'Status',
//            'Key',
//            'BeginTime',
//            'EndTime',
//            'Installer',
//            'Community',
//            'Installer',
//            'Community',
//            'PayType',
//            'row',
//            'page'
//        ],
//        'process' => $PLProcess['getSupOrderList'],
//    ],
    // 'exportorder' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'auth' => false,
    //     'params' => ['token', 'BeginTime', 'EndTime', 'Status', 'ManageID', 'InstallID', 'Type'],
    //     'process' => $PLProcess['exportOrder'],
    // ],
//    'getallorderlistinfo' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RSUPERGRADE],
//        'params' => ['ID'],
//        'process' => $PLProcess['getSupOrderInfo'],
//    ],
    'setdiscount' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE],
        'params' => ['ID', 'Discount'],
        'process' => $PLProcess['setOrderDiscount'],
    ],
    'getpersonalmanagedevice' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['row', 'page', 'serchKey', 'serchValue', 'User'],
        'process' => $PLProcess['queryPerDeviceForMng'],
    ],
    'addpersonalmanagedevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERSONGRADE],
        'params' => ['Type', 'MAC', 'Location', 'Node', 'Relay', 'SecurityRelay'],
        'process' => $PLProcess['addPerDeviceForMng'],
    ],
//    'addpersonaldeviceforserver' => [
//        'method' => 'POST',
//        'type' => 'basic',
//        'roles' => [RPERSONGRADE],
//        'params' => ['Type', 'MAC', 'Location', 'Node', 'Relay', 'SecurityRelay'],
//        'process' => $PLProcess['addPerDeviceForMng'],
//    ],
    'modpersonalmanagedevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RPERSONGRADE],
        'params' => ['Type', 'ID', 'Location', 'Node', 'Relay', 'MAC', 'SecurityRelay'],
        'process' => $PLProcess['editPerDeviceForMng'],
    ],
    // 'modpersonaldeviceforserver' => [
    //     'method' => 'POST',
    //     'type' => 'basic',
    //     'roles' => [RPERSONGRADE],
    //     'params' => ['Location', 'Node', 'MAC'],
    //     'process' => $cAddProcess->unshift(
    //         [
    //             'type' => 'middle',
    //             'queue' => [
    //                 ['name' => 'addModDeviceParam'],
    //             ],
    //         ],
    //         $PLProcess['editPerDeviceForMng']
    //     ),
    // ],
    'deletepersonaldevice' => [
        'method' => 'POST',
        'type' => 'web',
        'roles' => [RSUPERGRADE, RAREAGRADE, RPERSONGRADE],
        'params' => ['ID'],
        'process' => $cAddProcess->clear()
            ->set(RSUPERGRADE, $PLProcess['deletePer'])
            ->set(
                RAREAGRADE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'perDevInAreaCheck', 'params' => 'ID'],
                        ],
                    ],
                    $PLProcess['deletePer']
                )
            )
            ->set(
                RPERSONGRADE,
                $cAddProcess->unshift(
                    [
                        'type' => 'middle',
                        'queue' => [
                            ['name' => 'pcDevInPerMngCheck', 'params' => 'ID'],
                        ],
                    ],
                    $PLProcess['deletePer']
                )
            )
            ->get(),
    ],
    'getpersonalmanagealluser' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPERSONGRADE],
        'process' => $PLProcess['getAllPerMainUserForPerMng'],
    ],
    'getcomuser' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RCOMMUNITYGRADE],
        'params' => ['Build', 'Status', 'Active', 'serchKey', 'serchValue', 'row', 'page'],
        'process' => $PLProcess['getComMainUserForMng'],
    ],
    'getcompri' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['Build', 'RoomID', 'serchKey', 'serchValue', 'row', 'page'],
        'process' => $PLProcess['getPrivateKeyFromCom'],
    ],
    'getcomrfc' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['Build', 'RoomID', 'serchKey', 'serchValue', 'row', 'page'],
        'process' => $PLProcess['getRfCardFromCom'],
    ],
    'getcomtmp' => [
        'method' => 'GET',
        'type' => 'web',
        'params' => ['Build', 'RoomID', 'serchKey', 'serchValue', 'row', 'page'],
        'process' => $PLProcess['getTempKeyFromCom'],
    ],
    // 'getallinstaller' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE],
    //     'process' => $PLProcess['queryAllInstaller'],
    // ],
    // 'disgetreceipt' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RAREAGRADE, RSUBDISTRIBUTOR],
    //     'params' => ['OrderID'],
    //     'process' => $PLProcess['getAreaReceipt'],
    // ],

    'getdischargeplan' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RAREAGRADE],
        'params' => ['Manage'],
        'process' => $PLProcess['getDisCharge'],
    ],

    'getdisactiveusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RAREAGRADE],
        'params' => ['Manage', 'Active', 'row', 'page'],
        'process' => $PLProcess['getDisUserActiveOrUn'],
    ],

    'getdisnextexpiretimeusers' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RAREAGRADE],
        'params' => ['Manage', 'ID', 'Count'],
        'process' => $PLProcess['getDisExpireTimeUsers'],
    ],
    'setuserlang' => [
        'method' => 'POST',
        'type' => 'web',
        'process' => $PLProcess['setUserLang'],
    ],
    // 'setmanagelange' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'roles' => [RSUPERGRADE, RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, RPROPERTYMANAGE, ROFFICEGRADE],
    //     'process' => $PLProcess['setManageLang'],
    // ],
    // 'getdischargemode' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RAREAGRADE, RCOMMUNITYGRADE, RPERSONGRADE, ROFFICEGRADE],
    //     'process' => $PLProcess['getDisChargeMode'],
    // ],
    'getcommunitybru' => [
        'method' => 'GET',
        'type' => 'web',
        'roles' => [RPROPERTYMANAGE],
        'process' => $PLProcess['getRoomResidentForPM'],
    ],
//    'getuserassdev' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => 'RoomID',
//        'process' => $PLProcess['getUserAssociateDevForPM'],
//    ],

    //V5.4 message 模板,物业不需要代理小区
    //    'addpmessagetemplate' => [
    //        'method' => 'POST',
    //        'type' => 'web',
    //        'roles' => [RPROPERTYMANAGE],
    //        'params' => ['Name', 'Title', 'Message'],
    //        'process' => $cAddProcess->clear()
    //            ->set(
    //                RPROPERTYMANAGE,
    //                $cAddProcess->unshift(
    //                    [
    //                        'type' => 'middle',
    //                        'queue' => [
    //                            ['name' => 'setPMAlias'],
    //                        ],
    //                    ],
    //                    $PLProcess['addMessageTemplate']
    //                )
    //            )
    //            ->get(),
    //    ],

    //    'modifypmessagetemplate' => [
    //        'method' => 'POST',
    //        'type' => 'web',
    //        'roles' => [RPROPERTYMANAGE],
    //        'params' => ['ID', 'Name', 'Title', 'Message'],
    //        'process' => $cAddProcess->clear()
    //            ->set(
    //                RPROPERTYMANAGE,
    //                $cAddProcess->unshift(
    //                    [
    //                        'type' => 'middle',
    //                        'queue' => [
    //                            ['name' => 'setPMAlias'],
    //                        ],
    //                    ],
    //                    $PLProcess['editMessageTemplate']
    //                )
    //            )
    //            ->get(),
    //    ],

//    'deletepmessagetemplate' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['deleteMessageTemplate']
//                )
//            )
//            ->get(),
//    ],

//    'getpmessagetemplate' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['queryMessageTemplate']
//                )
//            )
//            ->get(),
//    ],

//    'getpphotos' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['Build', 'Room', 'Key', 'page', 'row'],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['queryComPhotos']
//                )
//            )
//            ->get(),
//    ],

//    'delpphotos' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['deleteFacePhoto']
//                )
//            )
//            ->get(),
//    ],

//    'deletepalluserface' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['PassWd', 'Type'],
//        'process' => $PLProcess['deleteAllFacePhoto'],
//    ],

//    'getpimportphotospresent' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'params' => ['UniqueId'],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['queryImportFacePresent']
//                )
//            )
//            ->get(),
//    ],

//    'importfacephoto' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['ImportFacePhoto']
//                )
//            )
//            ->get(),
//    ],

    // V5.3
    // 'getfamilyfreeapp' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'roles' => [RPERENDMROLE, RCOMENDMROLE],
    //     'process' => $PLProcess['getFreeSubApp'],
    // ],
    'getstripepubkey' => [
        'method' => 'GET',
        'type' => 'web',
        'active' => false,
        'process' => $PLProcess['getStripePubKey'],
    ],
    'getstripepubkeyapp' => [
        'method' => 'GET',
        'type' => 'app',
        'active' => false,
        'process' => $PLProcess['getStripePubKey'],
    ],

    // V5.4
//    'gettoolmenu' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'process' => $PLProcess['getToolMenu'],
//    ],

    // alexa
    'alexalogin' => [
        'method' => 'GET',
        'type' => 'alexa',
        'auth' => false,
        'params' => ['state', 'client_id', 'scope', 'response_type', 'redirect_uri'],
        'process' => $PLProcess['goToAlexaLogin'],
    ],
    'alexaDeleteToken' => [
        'method' => 'POST',
        'type' => 'alexa',
        'auth' => false,
        'params' => ['userId'],
        'process' => $PLProcess['deleteAlexaToken'],
    ],
    'thirdlogin' => [
        'method' => 'POST',
        'type' => 'alexa',
        'auth' => false,
        'params' => ['Account', 'Passwd'],
        'process' => $PLProcess['alexaLogin'],
    ],
    'alexatoken' => [
        'method' => 'POST',
        'type' => 'alexa',
        'auth' => false,
        'params' => ['code', 'redirect_uri'],
        'process' => $PLProcess['alexaToken'],
    ],
    'alexaSetUserId' => [
        'method' => 'POST',
        'type' => 'alexa',
        'params' => ['userId'],
        'process' => $PLProcess['alexaSetUid'],
    ],
    'alexaGetArmStatus' => [
        'method' => 'GET',
        'type' => 'alexa',
        'params' => ['MAC'],
        'process' => $PLProcess['alexaGetStatus'],
    ],
    'alexaSetAuthorization' => [
        'method' => 'POST',
        'type' => 'alexa',
        'params' => ['grant_code'],
        'process' => $PLProcess['alexaSetAuthorization'],
    ],
    'alexachangeArm' => [
        'method' => 'POST',
        'type' => 'alexa',
        'params' => ['mode', 'mac'],
        'process' => $PLProcess['alexaChangeArm'],
    ],
    'alexachangeStatus' => [
        'method' => 'POST',
        'type' => 'alexa',
        'params' => ['status', 'mac'],
        'process' => $PLProcess['alexaChangeStatus'],
    ],
    'alexachangeAlarm' => [
        'method' => 'POST',
        'type' => 'alexa',
        'params' => ['alarm', 'mac'],
        'process' => $PLProcess['alexaChangeAlarm'],
    ],
    // 新增发送验证码接口
    // 'sendmobilevercodeapp' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'auth' => false,
    //     'params' => ['MobileNumber', 'Type'],
    //     'process' => $PLProcess['sendmobilevercodeapp'],
    // ],
    // //检查验证码
    // 'checkverifycode' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'auth' => false,
    //     'params' => ['MobileNumber', 'Code'],
    //     'process' => $PLProcess['checkverifycode'],
    // ],
    'captureorder' => [
        'method' => 'POST',
        'params' => [
            'OrderNumber',
            'Token',
            'PlatformOrder',
            'Email',
            'Type',
            'Price',
            'BeforeOnePrice',
            'CouponNumber',
            'CouponCount',
            'Status'
        ],
        'type' => 'web',
        'auth' => false,
        'process' => $PLProcess['captureOrder'],
    ],
    // 'exportPMLog' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'params' => ['ExportType', 'Type', 'BeginTime', 'EndTime'],
    //     'process' => $cAddProcess->clear()
    //         ->set(
    //             RPROPERTYMANAGE,
    //             $cAddProcess->unshift(
    //                 [
    //                     'type' => 'middle',
    //                     'queue' => [
    //                         ['name' => 'getUserTimeZone'],
    //                         ['name' => 'getUserId'],
    //                         ['name' => 'setPMAlias'],
    //                         ['name' => 'getAliasId'],
    //                     ],
    //                 ],
    //                 $PLProcess['exportPmLog']
    //             )
    //         )
    //         ->get(),
    // ],
//    'getExportTime' => [
//        'method' => 'GET',
//        'params' => ['Flag'],
//        'type' => 'web',
//        'process' => $cAddProcess->clear()
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'getUserTimeZone'],
//                            ['name' => 'setPMAlias'],
//                            ['name' => 'getAliasId'],
//                        ],
//                    ],
//                    $PLProcess['getExportTime']
//                )
//            )
//            ->get(),
//    ],
//    'exportPMLogExcel' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'params' => ['TraceID'],
//        'process' => $PLProcess['exportPMLogExcel'],
//    ],
//    'getallaudit' => [
//        'method' => 'GET',
//        'params' => ['UserType', 'Type', 'BeginTime', 'EndTime', 'row', 'page'],
//        'type' => 'web',
//        'roles' => [RSUPERGRADE, RPROPERTYMANAGE],
//        'process' => $cAddProcess->clear()
//            ->set(
//                RSUPERGRADE,
//                $cAddProcess->unshift(
//                    [
//                        "type" => "middle",
//                        "queue" => [
//                            ["name" => "getMngTimeZone"],
//                        ]
//                    ],
//                    $PLProcess["getAllAudit"]
//                )
//            )
//            ->set(
//                RPROPERTYMANAGE,
//                $cAddProcess->unshift(
//                    [
//                        'type' => 'middle',
//                        'queue' => [
//                            ['name' => 'setPMAlias'],
//                        ],
//                    ],
//                    $PLProcess['getAllAudit']
//                )
//            )
//            ->get(),
//    ],
//    'v2/getpalltypepin' => [
//        'method' => 'GET',
//        'params' => ['row', 'page', 'Building', 'Apt', 'Type', 'serchKey', 'serchValue'],
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getAllTypePinForPM'],
//    ],
//    'v2/getpalltypecard' => [
//        'method' => 'GET',
//        'params' => ['row', 'page', 'Building', 'Apt', 'Type', 'serchKey', 'serchValue'],
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getAllTypeCardForPM'],
//    ],
//    'v2/editppincode' => [
//        'method' => 'POST',
//        'params' => ['ID', 'Type', 'Code'],
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['editPinForPM'],
//    ],
//    'v2/editpcardcode' => [
//        'method' => 'POST',
//        'params' => ['ID', 'Type', 'Code'],
//        'type' => 'web',
//        'roles' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['editCardForPM'],
//    ],
//    'v2/deleteppincode' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delPinForAll'],
//    ],
//    'v2/deletepcardcode' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delCardForAll'],
//    ],
    'v2/getpaptlist' => [
        'method' => 'GET',
        'params' => ['Build', 'Apt', 'Key', 'row', 'page'],
        'type' => 'web',
        'roles' => [RPROPERTYMANAGE],
        'process' => $PLProcess['getPMAptList'],
    ],
    // 'v2/modifypapt' => [
    //     'method' => 'POST',
    //     'params' => [
    //         'ID',
    //         'AptName',
    //         'CallType',
    //         'EnableIpDirect',
    //         'TempKeyPermission',
    //         'FamilyMemberControl',
    //         'AllowCreateSlaveCnt'
    //     ],
    //     'type' => 'web',
    //     'roles' => [RPROPERTYMANAGE],
    //     'process' => $PLProcess['modifyPMApt'],
    // ],
    'v2/getpaptinfo' => [
        'method' => 'GET',
        'params' => ['ID'],
        'type' => 'web',
        'roles' => [RPROPERTYMANAGE],
        'process' => $PLProcess['getPMAptInfo'],
    ],
//    'v2/getpcommunitydata' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getPMCommunityData'],
//    ],
//    'v2/getplastdoorlogs' => [
//        'method' => 'GET',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['getPMComLastDoorLog'],
//    ],
    //V7.0.0重定向到web-server下
//    'getAppUpgradeTXT' => [
//        'method' => 'GET',
//        'params' => ['type', 'role', 'version'],
//        'type' => 'web',
//        'auth' => false,
//        'process' => $PLProcess['getAppUpgradeTXT'],
//    ],
//    'v2/importresrfcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['importResRfCard'],
//    ],
//    'v2/importstaffcard' => [
//        'method' => 'POST',
//        'type' => 'web',
//        'role' => [RPROPERTYMANAGE],
//        'process' => $PLProcess['importStaffCard'],
//    ],
    // 'v2/updatebilllist' => [
    //     'method' => 'POST',
    //     'type' => 'web',
    //     'role' => [RSUPERGRADE],
    //     'params' => ['Type', 'ID', 'Step'],
    //     'process' => $PLProcess['updateBillList'],
    // ],
    // 'v2/getuserbilling' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RSUPERGRADE],
    //     'params' => ['EnduserID'],
    //     'process' => $PLProcess['userBillingInfo'],
    // ],
    'v2/getcomindoormonitor' => [
        'method' => 'GET',
        'type' => 'web',
        'role' => [RCOMMUNITYGRADE],
        'process' => $PLProcess['getComIndoorMonitor'],
    ],
    // "registerpersonaluser" => [
    //     "method" => "POST",
    //     "params" => ["Email", "PhoneCode", "MobileNumber", "Account", "Token"],
    //     "auth" => false,
    //     "type" => "app",
    //     "process" => $PLProcess["registerPMUser"]
    // ],
    // 'supergetreceipt' => [
    //     'method' => 'GET',
    //     'type' => 'web',
    //     'role' => [RSUPERGRADE],
    //     'params' => ['OrderID', 'Payer'],
    //     'process' => $PLProcess['getAreaReceipt'],
    //],
    //删除社区帐号，单住户帐号
    // "deleteAccount" => [
    //     "method" => "POST",
    //     "params" => ["Code"],
    //     "roles" => [RCOMENDMROLE, RCOMENDSROLE, RPERENDMROLE, RPERENDSROLE],
    //     "type" => "app",
    //     'process' => $cAddProcess->clear()
    //         ->set([RCOMENDMROLE, RCOMENDSROLE], $PLProcess['deleteComAccount'])
    //         ->set(RPERENDMROLE, $PLProcess['deleteSingleMainAccount'])
    //         ->set(RPERENDSROLE, $PLProcess['deleteSingleSubAccount'])
    //         ->get(),
    // ],
    //pm app获取door log
    // 'getpdoorlogforapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['page', 'row'],
    //     'process' => $PLProcess['getDoorLogForPMApp'],
    // ],
    //pm app获取Call history列表
    // 'getpcallhistoryforapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['page', 'row'],
    //     'process' => $PLProcess['getCallForPMApp'],
    // ],
    //pm app获取Capture Logs列表
    // 'getpcaptureforapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['page', 'row'],
    //     'process' => $PLProcess['getCaptureForPMApp'],
    // ],
    //pm app获取motion列表
    // 'getpmmotion' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['page', 'row'],
    //     'process' => $PLProcess['getMotionForPMApp'],
    // ],
    //pm app获取message列表
    // 'getpmessageforapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['page', 'row'],
    //     'process' => $PLProcess['getMessageForPMApp'],
    // ],
    //pm app获取message user列表
    // 'getpmessageusersforapp' => [
    //     'method' => 'GET',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['Key', 'Build'],
    //     'process' => $PLProcess['getMessageComUserForPMApp'],
    // ],
    //pm app增加message
    // 'addpmessageforapp' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['Message', 'MessageTitle', 'Recevier', 'isAllMessage', 'ClientType'],
    //     'process' => $PLProcess['addMessageForPMApp'],
    // ],
    //pm app删除message
    // 'deletepmessageforapp' => [
    //     'method' => 'POST',
    //     'type' => 'app',
    //     'role' => [RPMENDMROLE],
    //     'params' => ['ID'],
    //     'process' => $PLProcess['betchDelMngMessageForPMApp'],
    // ],
    //pm app获取楼栋、房间信息
//    'getcommunitybrforapp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'role' => [RPMENDMROLE],
//        'params' => ['CommunityID'],
//        'process' => $PLProcess['getBuildRoomForPMApp'],
//    ],
    //pm app获取pm tempkey列表
//    'getptmpkeyforapp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'role' => [RPMENDMROLE],
//        'params' => ['page', 'row', 'IsGetNowTempKey'],
//        'process' => $PLProcess['queryTempKeyForPMApp'],
//    ],
    //pm app新增tempkey
//    'addptmpkeyforapp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'role' => [RPMENDMROLE],
//        'params' => [
//            'MAC',
//            'DateFlag',
//            'StartTime',
//            'StopTime',
//            'BeginTime',
//            'ExpireTime',
//            'SchedulerType',
//            'Allow',
//            'Description',
//            'IDNumber',
//            'Delivery',
//            'Room',
//            'OfficeUserID'
//        ],
//        'process' => $PLProcess['addTempKeyForPMApp'],
//    ],
    //pm app获取设备信息
//    'getpdoordeviceforapp' => [
//        'method' => 'GET',
//        'type' => 'app',
//        'role' => [RPMENDMROLE],
//        'params' => ['Build'],
//        'process' => $PLProcess['getPMDoorDeviceForPMApp'],
//    ],
    //pm app删除pm tempkey
//    'deleteptmpkeyforapp' => [
//        'method' => 'POST',
//        'type' => 'app',
//        'role' => [RPMENDMROLE],
//        'params' => ['ID'],
//        'process' => $PLProcess['delTempKeyForPMApp'],
//    ],
    //V7.0.0重定向到web-server下
//    'getAppWord' => [
//        'method' => 'GET',
//        'params' => ['word_key'],
//        'type' => 'app',
//        'auth' => R_END_USER_ROLE,
//        'process' => $PLProcess['getAppWord'],
//    ]
];
