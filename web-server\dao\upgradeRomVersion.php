<?php

namespace dao;
use framework\BasicDao;

class UpgradeRomVersion extends BasicDao
{
    //当前表名
    public $table = 'UpgradeRomVersion';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    
    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/05/24 12:01 V6.5.4
     * @lastEditors: systemCreator 2023/05/24 12:01 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /*
     *@description 获取upgrade列表
     *<AUTHOR> 2023-05-24 14:32:58 V6.6.0
     *@lastEditor cj 2023-05-24 14:32:58 V6.6.0
     *@param {*} searchArr 查询条件
     *@param {*} offset
     *@param {*} row
     *@param {*} type 0:单住户设备 1:社区/办公设备
     *@return [$upgradeList, intval($total)]
     */
    public function getUpgradeList($searchArr, $offset, $row, $type = 1)
    {
        $upgradeList = $this->orderBy('ID desc')->limit("$offset,$row")->selectByArray($searchArr, 'Version,Status,ID,CreateTime,UpdateTime,IsNeedReset');
        $total = $this->selectByArray($searchArr, 'count(*)')[0]['count(*)'];
        $deviceTable = $type === 0 ? PROXY_TABLES['personalDevices'] :PROXY_TABLES['devices'];
        $upgradeRomDevicesTable = PROXY_TABLES['upgradeRomDevices'];
        foreach ($upgradeList as &$upgrade) {
            $upgrade['Model'] = $this->dao->romVersion->selectByKey('Version', $upgrade['Version'], 'Model')[0]['Model'];
            $sql = "select D.Location,D.MAC from $deviceTable D left join $upgradeRomDevicesTable U on D.MAC = U.MAC where U.UpgradeRomVerID = :ID";
            $deviceData = $this->execute($sql, [':ID' => $upgrade['ID']]);
            $upgrade['Device'] = array();
            $deviceList = array();
            foreach ($deviceData as $value) {
                array_push($upgrade['Device'], $value['MAC']);
                array_push($deviceList, $value['Location']?$value['Location']:$value['MAC']);
            }
            $upgrade['DeviceList'] = implode(',', $deviceList);
        }
        unset($upgrade);
        return [$upgradeList, intval($total)];
    }

    /*
     *@description 获取upgrade列表
     *<AUTHOR> 2024-07-17 09:49:58 V7.0.0
     *@lastEditor kzr 2024-07-17 09:49:58 V7.0.0
     *@param {*} searchArr 查询条件
     *@param {*} offset
     *@param {*} row
     *@param {*} type 0:单住户设备 1:社区/办公设备
     *@return [$upgradeList, intval($total)]
     */
    public function getUpgradeListByIns($searchArr,$searchSite,$projectData, $offset, $row)
    {
        $sql = "select Version,Status,ID,CreateTime,UpdateTime,IsNeedReset,OwnerAccount,ProjectType from UpgradeRomVersion";
        $sqlTotal = "select COUNT(*) from UpgradeRomVersion";
        $condition = $conditionBindArray = [];
        if (!empty($searchArr)){
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArr,true);
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($condition)){
            $condition=join(' and ', $condition);

            if (!empty($searchSite)){
                [$bindString,$bindArray] = \share\util\getImplodeData($projectData);
                $searchSiteValue = "%".$searchSite[1]."%";
                $condition.=' and OwnerAccount in (SELECT Account FROM Account WHERE Account in('.$bindString.') and Location LIKE :searchSiteValue) ';
                $conditionBindArray = array_merge($conditionBindArray, $bindArray);
                $conditionBindArray[':searchSiteValue'] = $searchSiteValue;
            }

            $sql.=' where '. $condition;
            $sqlTotal .=' where '. $condition;
        }

        
        
        $total = $this->execute($sqlTotal, $conditionBindArray)[0]["COUNT(*)"];

        $sql.= " order by ID desc limit $offset,$row";

        $upgradeList = $this->execute($sql, $conditionBindArray);
        
        $upgradeRomDevicesTable = PROXY_TABLES['upgradeRomDevices'];
        
        $ownerAccount = array_column($upgradeList, 'OwnerAccount');

        $projectSite = [];
        $project = $this->dao->account->selectByArray([['Account',$ownerAccount]]);

        foreach($project as $projectItem){
            $projectSite[$projectItem['Account']] = [
                "ID"=>$projectItem['ID'],
                "Grade"=>$projectItem['Grade'],
                "Location" => $projectItem['Location']
            ];
        }

        foreach ($upgradeList as &$upgrade) {

            if ($projectSite[$upgrade['OwnerAccount']]['Grade'] == COMMUNITYGRADE) {
                $deviceTable = PROXY_TABLES['devices'];  
                $upgrade['Site'] = $projectSite[$upgrade['OwnerAccount']]['Location'];
            } elseif ($projectSite[$upgrade['OwnerAccount']]['Grade'] == OFFICEGRADE) {
                $deviceTable = PROXY_TABLES['devices'];
                $upgrade['Site'] = $projectSite[$upgrade['OwnerAccount']]['Location'];
            } else {
                $deviceTable=PROXY_TABLES['personalDevices'];
                $upgrade['Site'] ="";
                // $upgrade['Site'] =$this->dao->account->getAccountUserInfoByAccountID($projectSite[$upgrade['OwnerAccount']]['ID'])['LoginAccount'];
            }
            $upgrade['ProjectType'] = $upgrade['ProjectType'];
            $upgrade['ProjectId'] = $projectSite[$upgrade['OwnerAccount']]['ID'];
            $upgrade['Model'] = $this->dao->romVersion->selectByKey('Version', $upgrade['Version'], 'Model')[0]['Model'];
            $sql = "select D.Location,D.MAC from $deviceTable D left join $upgradeRomDevicesTable U on D.MAC = U.MAC where U.UpgradeRomVerID = :ID";
            $deviceData = $this->execute($sql, [':ID' => $upgrade['ID']]);
            $upgrade['Device'] = array();
            $deviceList = array();
            foreach ($deviceData as $value) {
                array_push($upgrade['Device'], $value['MAC']);
                array_push($deviceList, $value['Location']?$value['Location']:$value['MAC']);
            }
            $upgrade['DeviceList'] = implode(',', $deviceList);
        }
        unset($upgrade);
        return [$upgradeList, intval($total)];
    }

    /*
     *@description 获取super的升级列表
     *<AUTHOR> 2023-05-24 15:12:39 V6.6.0
     *@lastEditor cj 2023-05-24 15:12:39 V6.6.0
     *@param {*} status
     *@param {*} searchArray
     *@return [$data, intval($total)];
     */
    public function getUpgradeListForSup($superAccount, $status, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $bindArray = [':OwnerAccount' => $superAccount];
        $where = '';
        if ($status !== null && $status !== '') {
            $bindArray[':Status'] = $status;
            $where = "$where AND U.Status = :Status";
        }
        switch ($searchKey) {
            case 'Version':
                $where.= ' and U.Version like :Search';
                $bindArray[':Search'] = "%$searchValue%";
                break;
            case 'Mac':
                $where.= ' and UD.MAC like :Search';
                $bindArray[':Search'] = "%$searchValue%";
                break;
            case 'Installer':
                $where.= ' and Ins.Account like :Search';
                $bindArray[':Search'] = "%$searchValue%";
                break;
            case 'Distributor':
                $where.= ' and Dis.Account like :Search';
                $bindArray[':Search'] = "%$searchValue%";
                break;
            default:
                break;
        }
        $total = $this->db->querySList(
            "select count(DISTINCT(U.ID)) as total from UpgradeRomVersion U left join UpgradeRomDevices UD on U.ID = UD.UpgradeRomVerID
             left join Account Ins on Ins.UUID = U.InsUUID
             left join Account Dis on Dis.ID = Ins.ParentID
            where U.OwnerAccount = :OwnerAccount $where",
            $bindArray
        )[0]['total'];

        $data = $this->db->querySList(
            "select DISTINCT(U.ID),U.Version,U.Status,U.ID,U.CreateTime,U.UpdateTime,U.ProjectType,R.Model,Ins.ID as InsID,Ins.Account as Install,Dis.Account as AreaManage,Dis.ID as DisID 
            from UpgradeRomVersion U 
            left join RomVersion R on U.Version = R.Version
            left join UpgradeRomDevices UD on U.ID = UD.UpgradeRomVerID
            left join Account Ins on Ins.UUID = U.InsUUID
            left join Account Dis on Dis.ID = Ins.ParentID
            where U.OwnerAccount = :OwnerAccount $where order by U.ID desc limit $offset,$rows",
            $bindArray
        );
        $upgradeRomDevicesTable = PROXY_TABLES['upgradeRomDevices'];
        foreach ($data as &$val) {
            $deviceTable = $val['ProjectType'] === '3' ? PROXY_TABLES['personalDevices'] :PROXY_TABLES['devices'];
            $sql = "select D.Location,U.MAC from $deviceTable D right join $upgradeRomDevicesTable U on D.MAC = U.MAC where U.UpgradeRomVerID = :ID";
            $deviceData = $this->execute($sql, [':ID' => $val['ID']]);
            $val['Device'] = array();
            $deviceList = array();
            foreach ($deviceData as $value) {
                array_push($val['Device'], $value['MAC']);
                array_push($deviceList, $value['Location']?$value['Location']:$value['MAC']);
            }
            $val['DeviceList'] = implode(',', $deviceList);
        }
        unset($val);
        return [$data, intval($total)];
    }
}