<?php
/*
 * @Description: 容器接口
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-02 11:51:08
 * @LastEditors  : kxl
 */
namespace interfaces\container\main;

// 数据容器
interface dataContainer {
    function bind ($key,$value);
    function get ($key);
}

// 模块容器
interface modelContainer {
   function make ($class,array $params=[]);
   function resolve ($class,array $params=[]);
   function setDataContainer ($model,dataContainer $dataContainer);
}

// 服务容器
interface serviceContainer {
    function make ($class,array $params=[]);
    function resolve ($class,array $params=[]);
    function addService ($class,$service,array $params=[]);
 }

// 事件容器
interface eventContainer {
    function on ($event,$params,$dataContainer);
    function emit ($event,$params,$dataContainer);
}
