<?php
/*
 * @Description: 将web的row设置为app的row，复用web方法给app时使用
 * @version: 
 * @Author: csc
 * @Date: 2022/4/11 15:13 V6.4
 * @LastEditors  : csc
 */

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
use \interfaces\middleware\main\IMiddleware;

class CSetWebRowToAppRow implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog;

        $cLog->actionLog("#middle#setWebRowToAppRow#");
        \util\computed\setGAppData(["webRowToAppRow" => true]);
        $next();
    }
}