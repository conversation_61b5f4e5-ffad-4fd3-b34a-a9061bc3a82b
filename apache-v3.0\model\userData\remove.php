<?php

namespace model\userData;
trait remove {
    function closeAccount () {
        $params = [
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $user = $params["userAlias"];
        $myData = $this->db->queryAllList("PersonalAccount",["equation"=>[":Account"=>$user]])[0];
        $role = $myData["Role"];
        $areaMngAccount = $this->db->querySList("select A.Account,A.ID from Account A left join Account B on A.ID = B.ParentID left join PersonalAccount P on B.ID = P.ParentID where P.Account = :Account",[":Account"=>$user])[0]["Account"];
    
        //删除设备
        $deviceTabel = in_array($role, PERROLE) ? "PersonalDevices" : "Devices";
        $devicsData = $this->db->queryAllList($deviceTabel,["equation"=>[":Node"=>$user]]);
        $this->db->delete2ListWKey($deviceTabel,"Node",$user);

        //回收mac地址
        $macs = [];
        $sips = [$user];
        $accounts = [$user];
        foreach($devicsData as $val) {
            array_push($macs,$val["MAC"]);
            array_push($sips,$val["SipAccount"]);
        }
        $this->services["rps"]->modifyMap(["mac"=>$macs,"type"=>5]);
        // 通知暂时注释
        // foreach($macs as $value)
        //     personnalDelDev($value);
        $tmpKeyData = $this->db->queryAllList("PersonalAppTmpKey",["equation"=>[":Node"=>$user]]);
        $keyID = [];
        foreach($tmpKeyData as $val)
            array_push($keyID,$val["ID"]);
        
        $this->db->delete2ListWKey("PersonalAppTmpKey","Node",$user);
        $this->db->delete2ListWKey("PersonalPrivateKey","Node",$user);
        $this->db->delete2ListWKey("PersonalRfcardKey","Node",$user);

        //删除 tmpkey 二维码
        // delTmpKeyQrCode($user,implode(";", $keyID));
        
        //删除用户bill信息
        $this->db->delete2ListWKey("PersonalBillingInfo","Account",$user);
        //删除主账号和从账户，删除公共设备关联表
        $this->db->delete2ListWKey("PerNodeDevices","NodeID",$myData["ID"]);

        // 删除通话记录中sip群组记录
        $node = $user;
        $sipGroup = $this->db->queryAllList("SipGroup2",["equation"=>[":Account"=>$node]])[0]["SipGroup"];
        $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CalleeID",$sipGroup);

        //回收sip，sip群组号，通知vsip删除sip
        $subRole = in_array($role, PERROLE) ? PERENDSROLE : COMENDSROLE;
        $subUserData = $this->db->queryAllList("PersonalAccount",["equation"=>[":ParentID"=>$myData["ID"],":Role"=>$subRole]]);
        foreach($subUserData as $val) {
            array_push($sips,$val["SipAccount"]);
            array_push($accounts,$val["Account"]);
        }
        $this->services["sip"]->del2Freeswish($sips);
        $this->db->exec2ListWArray("delete from PersonalAccount where (ID = :ID) or (ParentID = :ID and (Role = 11 or Role = 21))",[":ID"=>$myData["ID"]]);
        foreach($sips as $val)
            $this->services["sip"]->sipCollecting($val,$areaMngAccount);
        $this->db->delete2ListWKey("SipGroup2","Account",$user);
        //视频录制
        $this->db->delete2ListWKey("VideoLength","Node",$node);
        $this->db->delete2ListWKey("VideoList","Node",$node);
        $this->db->delete2ListWKey("VideoSchedule","Node",$node);
        $this->db->delete2ListWKey("PersonalAccountCnf","Account",$user);
        //删除alarm
        $alarmTable = in_array($role, PERROLE) ? "PersonalAlarms" : "Alarms";
        $this->db->delete2ListWKey($alarmTable,"Node",$node);
        $this->db->delete2ListWKey("PersonalLogs","AccountID",$myData["ID"]);
        foreach($sips as $val) {
            $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CallerID",$val);
            $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CalleeID",$val);
        }

        foreach($accounts as $val) {
            $this->db->delete2ListWKey("Token","Account",$val);
        }
    }
}