<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-09 16:41:39
 * @LastEditors: cj
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../database/redis.php";

class CLogin
{
    //tmpToken类型-app重置密码
    private $appResetPwdTmpTokenType = 0;
    /*
     *@msg: 终端用户登陆
     *@service: charge
     */
    public function endUserLogin()
    {
        global $cMessage,$gApp;
        // 参数设置
        $params = [
            "Account"=>""
        ];

        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $this->db->querySList(
            "select * from PersonalAccount where Role in (".PERENDMROLE.",".COMENDMROLE.") and (Account = :Account or Email = :Account or MobileNumber = :Account)",
            [":Account"=>$params["Account"]]
        )[0];


        $name = $data["Name"];
        $role = $data["Role"];
        $account = $data["Account"];
        $email = $data["Email"];
        $timeZone = $data["TimeZone"];
        $active = $data["Active"];
        $expireTime = $data["ExpireTime"];
        $id = $data["ID"];

        // V6.4日本服务器迁移，增加指定dis下用户重定向
        if (\util\role\checkJCloudDis($account)) {
            $cMessage->echoErrorMsg(StateJCloudServer, ['Url' => J_CLOUD]);
        }

        $token = \util\string\randString(TOKENLENGTH);
        // TODO 新旧TOKEN的存取
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        $oldToken = $redis->get($account);
        if ($oldToken !== null) {
            $redis->del($oldToken);
        }
        //存储新token
        $redis->setex($token, TOKENVALIDITYTIME, $account);
        $redis->setex($account, TOKENVALIDITYTIME, $token);

        if ($active == 0) {
            $cMessage->echoErrorMsg(StateNoActiveWeb, []);
        } elseif (strtotime($expireTime) < time()) {
            $cMessage->echoErrorMsg(StateExpireLogin, ["token"=>$token]);
        } else {
            \util\computed\setGAppData([
                'token'=>$token,
                'name'=>$name,
                'role'=>$role,
                'UID'=>$account,
                'email'=>$email,
                'timeZone'=>$timeZone
            ]);
            $gApp["role"] = 'E'.$role;
            $gApp["user"] = $account;
            $gApp["userAliasId"] = $data["ID"];
            $this->log->endUserLog(1, null, "log in");
            $this->auditLog->setLog(AuditCodeLoginWeb, $this->env, [], $account);
        }
    }

    public function loginManage()
    {
        global $cMessage, $gApp;
        // 参数设置
        $params = [
            "Account"=>"",
            "passwd"=>""
//            "userAliasId"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Account"];
        $password = $params["passwd"];

        $ip = \util\computed\getIp();
        $limit = \util\computed\getLimitIp($name, $ip);
        if ($limit) {
            $cMessage->echoErrorMsg(StateLimitIP2, ['time'=>$limit]);
        }

        $data = $this->db->querySList("select * FROM Account WHERE Account = :name and Special = 0 and Passwd = :Passwd", [":name"=>$name,":Passwd"=>$password]);
        if (count($data) != 0) {
            // V6.4日本服务器迁移，增加指定dis下用户重定向
            if (\util\role\checkJCloudDis($name)) {
                $cMessage->echoErrorMsg(StateJCloudServer, ['Url' => J_CLOUD.'/manage-new']);
            }

            $grade = $data[0]["Grade"];
            $timeZone = $data[0]["TimeZone"];
            $role = $data[0]["Role"];
            $initialization = $data[0]["Initialization"];
            $communityID = "";
            $perAccount = "";
            if ($grade == 31) {
                $communityID = $this->db->queryAllList("PropertyMngList", ["equation"=>[":PropertyID"=> $data[0]['ID']]])[0]["CommunityID"];
                // kxl V6.1新旧小区
                $isNew = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [":AccountID"=>$communityID])[0]["IsNew"];
            } elseif ($grade == 21) {
                $perAccount = $this->db->querySList("select Account from Account where ManageGroup = :ManageGroup and Grade = 22", [":ManageGroup"=>$data[0]["ManageGroup"]])[0]["Account"];
            } elseif ($grade == 22) {
                $perAccount = $data[0]['Account'];
            } elseif ($grade == 11) {
                $customerData = $this->db->queryAllList("CustomerService", ["equation"=>[":MngAccount"=>$data[0]["Account"]]]);
                $haveCustomer = 0;
                if (count($customerData) != 0 && $customerData[0]["Email"] != "" && $customerData[0]["Phone"] != "") {
                    $haveCustomer = 1;
                }
            }
            if ($initialization == 0) {
                $this->db->update2ListWKey("Account", [":Initialization"=>1,":Account"=>$name], "Account");
            }

            //兼容旧的ins账号(既是社区又是ins)
            if(in_array($grade, [COMMUNITYGRADE, PERSONGRADE])){
                //清除ins账号组所有的token
                $this->clearInsToken($data[0]["ManageGroup"]);
            }

            $token = \util\string\randString(TOKENLENGTH);
            $token = \util\string\getPreTokenName($token, $grade);
            $redis = \database\CRedis::getInstance();
            $redis->select(REDISDB2TOKEN);

            // V5.0登录是社区管理员时也记为installer账户登录，兼容旧数据
            if ($grade == 21 || $grade == 22) {
                //先取旧token
                $oldToken = $redis->get($perAccount);
                if ($oldToken !== null) {
                    $redis->del($oldToken);
                }

                //存新token
                $redis->setex($token, TOKENVALIDITYTIME, $perAccount);
                $redis->setex($perAccount, TOKENVALIDITYTIME, $token);
            } else {
                //先取旧token
                $oldToken = $redis->get($name);
                if ($oldToken !== null) {
                    $redis->del($oldToken);
                }

                //存新token
                $redis->setex($token, TOKENVALIDITYTIME, $name);
                $redis->setex($name, TOKENVALIDITYTIME, $token);
            }

            \util\computed\setGAppData(['token'=>$token,'grade'=>$grade,'account'=>$name,'timeZone'=>$timeZone,"communityID"=>$communityID,"perAccount"=>$perAccount,
            "Role"=>$role,"Initialization"=>$initialization,"HaveCustomer"=>$haveCustomer,"IsNew"=>$isNew, 'Business' => intval($data[0]['Business'])]);
//            if ($grade == 31){
//                $account=$this->db->querySList('select Account from Account where ID = :ID',[":ID"=>$params['userAliasId']])[0]["Account"];
//            }
//            $this->auditLog->setLog(AuditCodeLoginWeb, $this->env,[],$grade == 31? $account : $name);
            $this->auditLog->setLog(AuditCodeLoginWeb, $this->env, [], $name);
            $gApp["user"] = $name;
            $gApp["role"] = 'M'.$grade;
            $gApp["userAliasId"] = $data[0]["ID"];
        } else {
            list($times, $number) = \util\computed\recordAccountIp($name, $ip);
            if ($times === false) {
                $num = $number >= 3 ? (5 - $number) : -1;
                $cMessage->echoErrorMsg(StateAccountIncorrect, ['number'=>$num]);
            } else {
                $cMessage->echoErrorMsg(StateAccountIncorrect2, ['time'=>$times]);
            }
        }
    }

    /**
     * @description:清除ins账号组的token
     * @author:lwj 2022/9/15 10:27
     * @lastEditor: lwj 2022/9/15 10:27
     * @param insId
     * @return bool
     */
    public function clearInsToken($insId)
    {
        //获取当前ins账号组 排除pm账号
        $insGrades = \util\arr\implodeWithQuotation(PROJECT_ROLE);
        $sql = 'select Account from Account where ManageGroup = :ManageGroup and Grade in('.$insGrades.')';
        $bindArray = [':ManageGroup' => $insId];
        $list = $this->db->querySList($sql, $bindArray);
        if (empty($list)) {
            return true;
        }
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        foreach ($list as $v) {
            $token = $redis->get($v['Account']);
            if ($token !== null) {
                $redis->del($token);
            }
            $redis->del($v['Account']);
        }
        return true;
    }

    public function sendEmail()
    {
        global $cMessage;
        $params = [
            "EmailOrId"=>"",
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $emailOrId = $params["EmailOrId"];
        $data = $this->db->querySList("select Email,Name,Account,Role,UUID,ParentUUID from PersonalAccount where Email = :Account OR Account = :Account", [":Account"=>$emailOrId]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateEmailOrAccountNotExit);
        }
        $email = $data[0]['Email'];

        $tipCode = 0;
        // 如果是pmapp的用户，需要取对应pm的邮箱
        if ($data[0]['Role'] == PMENDMROLE) {
            $pmMap = $this->db->querySList('select * from PmAccountMap where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $data[0]['UUID']])[0];
            $pmAccountData = $this->db->querySList('select Email from Account where UUID = :UUID', [':UUID' => $pmMap['AccountUUID']])[0];
            $email = $pmAccountData['Email'];
        } elseif (in_array($data[0]['Role'], SUBROLE)) {
            $mainEmail = $this->db->querySList('select Email from PersonalAccount where UUID = :UUID', [':UUID' => $data[0]['ParentUUID']])[0]['Email'];
            if (!empty($mainEmail) && empty($data[0]['Email'])) {
                $tipCode = 1;
            }
        }

        if (!$email && !in_array($data[0]['Role'], SUBROLE)) {
            $cMessage->echoErrorMsg(StateEmailNotExits);
        }
        $name = $data[0]["Name"];
        $account = $data[0]["Account"];
        $token = \util\string\randString(TOKENLENGTH);
        $now = date("Y-m-d H:i:s", strtotime("+3 hours"));

        $tmpToken = $this->db->queryAllList("TmpToken", ["equation"=>[":Account"=>$account, ':Type' => $this->appResetPwdTmpTokenType]]);
        if (count($tmpToken) == 0) {
            $this->db->insert2List("TmpToken", [":EndTime"=>$now,":AppToken"=>$token,":Account"=>$account, ':Type' => $this->appResetPwdTmpTokenType]);
        } else {
            $this->db->update2ListWKey("TmpToken", [":EndTime"=>$now,":AppToken"=>$token,":ID"=>$tmpToken[0]['ID']], "ID");
        }
        \util\computed\setGAppData([
            'token'=>$token,
            'name'=>$name,
            'account'=>$account,
            'email'=>$email,
            'TipCode'=> $tipCode
        ]);

        //		$data = [$name,$email,$token];
//		$resetPasswdSocket=new CResetPasswdSocket();
//  	$resetPasswdSocket->copyUserInfo($data);
    }

    public function getName()
    {
        global $cMessage;
        $params = [
            "TOKEN"=>"",
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $token = $params["TOKEN"];
        $nowTime = time();
        $data = $this->db->querySList("select Account,EndTime from TmpToken where AppToken = :AppToken", [":AppToken"=>$token]);
        if (count($data) == 0 || $nowTime> strtotime($data[0]['EndTime'])) {
            $cMessage->echoErrorMsg(StateInvalidPage);
        }
        $account = $data[0]["Account"];
        $name = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$account]])[0]["Name"];
        \util\computed\setGAppData(["data"=>["Name"=>$name]]);
    }

    public function resetPw()
    {
        global $cMessage;
        $params = [
            "TOKEN"=>"",
            "Password"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $token = $params["TOKEN"];
        $password = $params["Password"];

        $nowTime = time();

        //防暴力破解
        \util\computed\checkCommonLimit('resetPw');

        $data = $this->db->queryAllList("TmpToken", ["equation"=>[":AppToken"=>$token]]);
        if (count($data)<=0 || $nowTime>strtotime($data[0]['EndTime'])) {
            \util\computed\addCommonLimit('resetPw');
            $cMessage->echoErrorMsg(StateInvalidPage);
        }
        $account = $data[0]['Account'];
        $this->db->update2ListWKey("PersonalAccount", [":Account"=>$account,":Passwd"=>md5($password)], "Account");
        $this->db->delete2ListWKey("TmpToken", "AppToken", $token);
        \util\computed\setGAppData(["Account"=>$account]);
    }

    public function afterResetPw()
    {
        $params = [
            "Account"=>"",
            "Password"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $password = $params["Password"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$account]]);
        $email = $data[0]['Email'];
        //		perChangePwd($account, $password, $email);
    }

    public function loginForDevice()
    {
        global $cMessage;
        $params = [
            "MAC"=>"",
            "Password"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["MAC"];
        $password = $params["Password"];

        if ($name === null || $password === null || $name===false || $password===false) {
            $cMessage->echoErrorMsg(StateDeviceLoginError);
        }
        $aesPw = $this->services["aes"]->encrypt($name);
        if ($aesPw != $password) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $deviceData = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$name]]);
        $deviceData2 = $this->db->queryAllList("Devices", ["equation"=>[":MAC"=>$name]]);
        if (count($deviceData) == 0 && count($deviceData2) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $deviceData = count($deviceData) == 0 ?  $deviceData2 :	$deviceData;
        $account = $deviceData[0]["Node"];
        $token = \util\string\randString(TOKENLENGTH);
        //TODO token 的存取

        \util\computed\setGAppData(["token"=>$token]);
    }

    public function getVerCode()
    {
        global $cMessage;
        $params = [
            "Email"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $code = rand(100000, 999999);
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Email"=>$email]]);
        if (count($userData)>0) {
            $cMessage->echoErrorMsg(StateEmailExits1008);
        }
        $ip = \util\computed\getIp();;
        $time = \util\computed\getNow();
        $now = date('Y-m-d H:i:s', strtotime('-'.RESENDEMAILTIME.' minute'));
        $data = $this->db->querySList("select ID from EmailCheckCode where Email = :Email and IP = :IP and CreateTime > :CreateTime", [":Email"=>$email,":IP"=>$ip,":CreateTime"=>$now]);
        // 1分钟内不发送
        if (count($data) > 0) {
            $cMessage->echoErrorMsg(StateSentCodeLater);
        }
        $this->db->delete2ListWKey("EmailCheckCode", "Email", $email);
        $this->db->insert2List("EmailCheckCode", [":Email"=>$email,":IP"=>$ip,":CheckCode"=>$code,":CreateTime"=>$time]);
        //  	sendCheckCode($code, $email);
    }

    public function emailCheckValid()
    {
        global $cMessage;
        $params = [
            "Email"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Email"=>$email]]);
        if (count($userData) > 0) {
            $cMessage->echoErrorMsg(StateEmailExits1010);
        }
    }

    public function deviceCodeCheck()
    {
        global $cMessage;
        $params = [
            "Email"=>"",
            "DeviceCode"=>"",
            "VerCode"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $deviceCode = $params["DeviceCode"];
        $verCode = $params["VerCode"];
        $ip = \util\computed\getIp();;
        if (!$this->limitRegCode($ip, 0)) {
            $cMessage->echoErrorMsg(StateLimitIP);
        }
        $this->emailCodeCheckControl($email, $verCode);
        $this->deviceCodeCheckControl($deviceCode);
    }

    private function limitRegCode($ip, $type)
    {
        //TYPE 检测还是增加
        $account = $ip."register";
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2LIMITIP);
//
        $num = $redis->get($account);
        $num = $num == null ? 0 : intval($num);
        if ($type == 0) {
            return $num < 5;
        }

        $num += 1;
        $redis->setex($account, 60*5, $num);
        return false;
    }

    /*
     * 邮箱验证码验证
     * */
    public function emailCodeCheckControl($email, $verCode, $limit = true)
    {
        global $cMessage;
        $ip = \util\computed\getIp();;
        $data = $this->registerEmailCodeCheck($email, $verCode);
        if (count($data) == 0) {
            if ($limit) {
                $this->limitRegCode($ip, 1);
            }
            $cMessage->echoErrorMsg(StateInvaildVerCode, ["key"=>"VerCode"]);
        }
        return $data;
    }
    public function registerEmailCodeCheck($email, $verCode)
    {
        $now = date('Y-m-d H:i:s', strtotime('-'.REGISTEREMAILKCODETIME.' hours'));
        return $this->db->querySList("select ID from EmailCheckCode where Email = :Email and CheckCode = :CheckCode and CreateTime < :CreateTime", [":Email"=>$email,":CheckCode"=>$verCode,":CreateTime"=>$now]);
    }
    /*
     * 设备验证码验证
     * */
    public function deviceCodeCheckControl($deviceCode, $limit = true)
    {
        global $cMessage;
        $ip = \util\computed\getIp();;
        $data = $this->getDeviceCodeData($deviceCode);
        if (count($data) == 0) {
            if ($limit) {
                self::limitRegCode($ip, 1);
            }
            $cMessage->echoErrorMsg(StateInvaildDC1013, ["key"=>"DeviceCode"]);
        }
        return $data;
    }

    public function getDeviceCodeData($deviceCode)
    {
        $devices = $this->db->queryAllList("DeviceCode", ["equation"=>[":Code"=>$deviceCode]]);
        return $devices;
    }

    /*
     *@msg: 终端用户注册
     *@service: sip,charge
     */
    public function register()
    {
        global $cMessage;
        $params = [
            "Name"=>"",
            "Password"=>"",
            "Email"=>"",
            "VerCode"=>"",
            "DeviceCode"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $password = $params["Password"];
        $email = $params["Email"];
        $verCode = $params["VerCode"];
        $deviceCode = $params["DeviceCode"];
        //检测ip是否被禁
        $ip = \util\computed\getIp();;
        $cMessage->echoErrorMsg(StateLimitIP);
        //邮箱是否存在
        $this->emailCheckValidControl($email);
        //验证码是否有效
        $this->emailCodeCheckControl($email, $verCode);
        //设备码验证
        $mac = $this->deviceCodeCheckControl($deviceCode)[0]["MAC"];
        //无效的device code或者非个人管理员下的devic code
        $deviceData = $this->testDeviceCode($mac);
        if ($deviceData == false) {
            $this->limitRegCode($ip, 1);
            $cMessage->echoErrorMsg(StateInvaildDC1013, ["key"=>"DeviceCode"]);
        }
        $mngID = $deviceData["MngID"];
        $personManageID = $deviceData["PerMngID"];

        $result = $this->registerCommonControl($mngID, $name, $password, $email, $personManageID);
        $this->db->delete2ListWKey("DeviceCode", "Code", $deviceCode);

        $sip = $result["sip"];
        $sipGroup = $result["sipGroup"];
        $personManage = $result["personManage"];
        $mngAccount = $result["areaMngAccount"];
        $id = $result["ID"];
        $this->db->insert2List("PersonalBillingInfo", [":Account"=>$sip]);
        \util\computed\setGAppData(["Account"=>$sip,"sip"=>$sip,"manage"=>$personManage,"ID"=>$id]);
    }

    // function registerFromSB () {
    // 	global $cMessage;
    // 	$params = [
    // 		"Name"=>"",
    // 		"Password"=>"",
    // 		"Email"=>"",
    // 		"VerCode"=>"",
    // 		"DeviceCode"=>""
    // 	];
    // 	// 参数获取
    //     $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
    // 	$name = $params["Name"];
    // 	$password = $params["Password"];
    // 	$email = $params["Email"];
    // 	$verCode = $params["VerCode"];
    // 	$deviceCode = $params["DeviceCode"];
    // 	//邮箱是否存在
    // 	$this->emailCheckValidControl($email);
    // 	$deviceCode = explode(";", $deviceCode);
    // 	$where = "";
    // 	$bindArray = [];
    // 	foreach($deviceCode as $key=>$value) {
    // 		if($key == 0)
    // 			$where .= "Code = :Code$key";
    // 		else
    // 			$where .= " or Code = :Code$key";
    // 		$bindArray[":Code$key"] = $value;
    // 	}

    // 	$data = $this->db->querySList("select MAC from DeviceCode where $where",$bindArray);
    // 	if(count($data) != count($deviceCode) || $where == "") $cMessage->echoErrorMsg(StateInvaildDC102);

    // 	$mngIDs = [];
    // 	$perMngIDs = [];

    // 	foreach($data as $mac)
    // 	{
    // 		//无效的device code或者非个人管理员下的devic code
    // 		$deviceData = $this->testDeviceCode($mac);
    // 		if($deviceData == false) $cMessage->echoErrorMsg(StateInvaildVerCode,["key"=>"DeviceCode"]);
    // 		array_push($mngIDs,$deviceData["MngID"]);
    // 		array_push($perMngIDs,$deviceData["PerMngID"]);
    // 	}
    // 	$mngIDs = array_unique($mngIDs);
    // 	$perMngIDs = array_unique($perMngIDs);
    // 	if(count($mngIDs) != 1 || count($perMngIDs)) $cMessage->echoErrorMsg(StateInvaildVerCode,["key"=>"DeviceCode"]);

    // 	$result = $this->registerCommonControl($mngIDs[0],$name,$password,$email,$perMngIDs[0],"device");
    // 	foreach($deviceCode as $value) {
    // 		$this->db->delete2ListWKey("DeviceCode","Code",$value);
    // 	}
    // 	$sip = $result["sip"];
    //     $sipGroup = $result["sipGroup"];
    //     $personManage = $result["personManage"];
    //     $mngAccount = $result["areaMngAccount"];
    //     $id = $result["ID"];
    // 	$this->db->insert2List("PersonalBillingInfo",[":Account"=>$sip]);
    // 	\util\computed\setGAppData(["Account"=>$sip,"sip"=>$sip,"manage"=>$personManage,"ID"=>$id,"DeviceCode"=>$deviceCode]);
    // }

    public function registerCommonControl($mngID, $name, $password, $email, $personManageID, $client="web")
    {
        global $cMessage;
        $areaMngAccount = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$mngID]])[0]["Account"];
        //获取sip账号
        $personManage = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$personManageID]])[0]["Account"];
        $sip = $this->services["sip"]->assignSip($personManage, "6");
        if ($sip == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }
        $sipGroup = $this->services["sip"]->getSipGroup($personManageID);
        if ($sipGroup == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccountGroup);
        }

        $this->db->insert2List("SipGroup2", [":Account"=>$sip,":SipGroup"=>$sipGroup]);
        $sipPw = \util\string\generatePw(12);
        //freeswish插入sip
        $options = ["type"=>6,
                    "node"=>0,//主账号id
                    "group"=>$sipGroup,
                    "community"=>$personManageID,
                    "communityType"=>1,
                    "enableGroup"=>1,//室内机群响铃
                    "sip"=>$sip,
                    "passwd"=>$sipPw];
        $this->services["sip"]->add2Freeswish($sip, $options);
        $now = \util\computed\getNow();
        $chargeService = $this->services["charge"];
        $charges = $chargeService->getCharge($personManageID, 2);
        list($active, $expretime) = $chargeService->computedExTimeActive($charges);
        $uuid = \util\string\uuid();
        $this->db->insert2List("PersonalAccount", [":Name"=>$name,
            ":SipAccount"=>$sip,
            ":Account"=>$sip,
            ":Role"=>10,
            ":ParentID"=>$personManageID,
            ":Passwd"=>md5($password),
            ":Email"=>$email,
            ":CreateTime"=>$now,
            ":SipPwd"=>$sipPw,
            ":RoomNumber"=>$name,
            ":Active"=>$active,
            ":ExpireTime"=>$expretime,
            ":UUID"=>$uuid
        ]);
        $id = $this->db->lastInsertId();
        return ["sip"=>$sip,"sipGroup"=>$sipGroup,"personManage"=>$personManage,"areaMngAccount"=>$areaMngAccount,"ID"=>$id];
    }

    public function afterRegister()
    {
        $params = [
            "mac"=>"",
            "sip"=>"",
            "Password"=>"",
            "Email"=>"",
            "personManage"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["mac"];
        $sip = $params["sip"];
        $password = $params["Password"];
        $email = $params["Email"];
        $manage = $params["personManage"];

        //		perCreateUser($sip, $password, $email);
//		personnalUpdateNodeNotify($sip);
//		if(gettype($mac) === "array")
//		{
//			foreach($mac as $value)
//				personnalAddDev($value);
//			notifyDevCleanDevCode(implode(";", $mac));
//		}
//		else
//		{
//			personnalAddDev($mac);
//			notifyDevCleanDevCode($mac);
//		}
    }

    /*
     * 邮箱存在
     * */
    public function emailCheckValidControl($email, $client="web")
    {
        global $cMessage;
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Email"=>$email]]);
        if (count($userData)>0) {
            $cMessage->echoErrorMsg($client == "web" ? StateEmailExits1010 : StateEmailExits101);
        }
    }

    public function testDeviceCode($mac)
    {
        $registerMac = $this->db->queryAllList("DeviceForRegister", ["equation"=>[":MAC"=>$mac]]);

        if (count($registerMac) == 0 || $registerMac[0]["Owner"] != "" || $registerMac[0]["PerMngID"] == "") {
            return false;
        }

        $grade = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$registerMac[0]["PerMngID"]]])[0]["Grade"];
        if ($grade != PERSONGRADE) {
            return false;
        }
        return $registerMac[0];
    }

    /**
     * 发送验证码
     */
    public function sendCode()
    {
        global $cMessage;
        $beforeTime = \util\computed\getNow();
        $params = [
            "Type"=>"",
            "MobileNumber"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#sendCode#bindArray".json_encode($params));
        $type = $params["Type"];
        $mobilenumber = $params["MobileNumber"];

        //检查手机号是否存在
        if (!$mobilenumber) {
            $cMessage->echoErrorMsg(StateMobileNumberEmpty);
        }
        $data = $this->db->querySList("select Account,PhoneCode from PersonalAccount where MobileNumber = :mobilenumber", [":mobilenumber"=>$mobilenumber]);
        $this->log->actionLog("#model#checkNumber".json_encode($data));
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateMobileNumberNotExist);
        }

        $areacode = $data[0]["PhoneCode"];
        $account = $data[0]["Account"];
        //检查时间，5分钟内不重新生成验证码
        $data = $this->db->querySList("select CreateTime,Code from VerificationCode where Account = :account", [":account"=>$account]);
        if (count($data) == 0) {
            //随机生成验证码
            $code = mt_rand(100000, 999999);
            $this->db->insert2List("VerificationCode", [":Account"=>$account,":Code"=>$code,":CreateTime"=>$beforeTime]);
        } else {
            if (strtotime($beforeTime) - strtotime($data[0]["CreateTime"]) > CODEVAILDTIME) {

                //随机生成验证码
                $code = mt_rand(100000, 999999);
                //更新验证码
                $this->db->update2ListWKey("VerificationCode", [":Account"=>$account,":Code"=>$code,":CreateTime"=>$beforeTime], "Account");
            } else {
                $code = $data[0]["Code"];
            }
        }
        $this->log->actionLog("#model#code".$code);
        // sendVerificationCode($type,$code,$areacode,$mobilenumber);
        \util\computed\setGAppData(["Code"=>$code,"PhoneCode"=>$areacode]);
        $this->log->actionLog("#model#sendCode#".$code);
    }

    /**
     * 检查验证码
     */
    public function checkCode()
    {
        global $cMessage;
        $beforeTime = \util\computed\getNow();
        $params = [
            "Code"=>"",
            "MobileNumber"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#checkCode#bindArray".json_encode($params));
        $code = $params["Code"];
        $mobilenumber = $params["MobileNumber"];

        //防暴力破解
        \util\computed\checkCommonLimit('checkCode');

        //检查手机号是否存在
        if (!$mobilenumber) {
            \util\computed\addCommonLimit('checkCode');
            $cMessage->echoErrorMsg(StateMobileNumberEmpty);
        }
        $data = $this->db->querySList("select Account,PhoneCode from PersonalAccount where MobileNumber = :mobilenumber", [":mobilenumber"=>$mobilenumber]);
        $this->log->actionLog("#model#checkNumber".json_encode($data));
        if (count($data) == 0) {
            \util\computed\addCommonLimit('checkCode');
            $cMessage->echoErrorMsg(StateMobileNumberNotExist);
        }
        $areacode = $data[0]["PhoneCode"];
        $account = $data[0]["Account"];

        //查询验证码
        $data = $this->db->querySList("select CreateTime,Code from VerificationCode where Account = :account", [":account"=>$account]);
        $this->log->actionLog("#model#querySList".json_encode(is_string($code)));
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateCodeIncorrect);
        }
        if (strtotime($beforeTime) - strtotime($data[0]["CreateTime"]) > CODEVAILDTIME || $data[0]['Code']!=$code) {
            $cMessage->echoErrorMsg(StateCodeIncorrect);
        }
        $this->log->actionLog("#model#savecode".json_encode($data[0]["Code"]));
        $token =  \util\string\randString(TOKENLENGTH);
        $now = date("Y-m-d H:i:s", strtotime("+3 hours"));

        $tmpToken = $this->db->queryAllList("TmpToken", ["equation"=>[":Account"=>$account, ':Type' => $this->appResetPwdTmpTokenType]]);
        if (count($tmpToken) == 0) {
            $this->db->insert2List("TmpToken", [":EndTime"=>$now,":AppToken"=>$token,":Account"=>$account, ':Type' => $this->appResetPwdTmpTokenType]);
        } else {
            $this->db->update2ListWKey("TmpToken", [":EndTime"=>$now,":AppToken"=>$token, ":ID"=>$tmpToken[0]['ID']], "ID");
        }
        $data["token"] = $token;
        \util\computed\setGAppData(["data"=>['token'=>$token]]);
        //验证码过期
        $this->db->delete2ListWKey("VerificationCode", "Account", $account);
    }
}
