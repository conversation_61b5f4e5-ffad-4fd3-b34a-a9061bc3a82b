<?php

require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/utility.php');
require_once(dirname(__FILE__) . '/funcs_update_data_version.php');
require_once(dirname(__FILE__) . '/funcs_face_interface.php');
require_once(dirname(__FILE__) . '/../util/time.php');
require_once(dirname(__FILE__) . '/../util/computed.php');
require_once(dirname(__FILE__) . '/../../dao/personalAccountUserInfo.php');

const PROJECT_TYPE_RESIDENCE = 0;
const PROJECT_TYPE_OFFICE = 1;
const PROJECT_TYPE_PERSONAL = 2;
const ACCOUNT_ROLE_OFFICE_MAIN = 30;
const ACCOUNT_ROLE_OFFICE_ADMIN = 31;
const ACCOUNT_ROLE_OFFICE_NEW_PER = 32;
const ACCOUNT_ROLE_COMMUNITY_PM = 40;
const ACCOUNT_ROLE_OFFICE_NEW_ADMIN = 41;
const ACCOUNT_ROLE_PERSONNAL_MAIN = 10;        //个人终端用户主账号
const ACCOUNT_ROLE_PERSONNAL_ATTENDANT = 11;   //个人终端用户从账号
const ACCOUNT_ROLE_PERSONNAL_V_PUB = 12;       //个人虚拟账号，用于公共设备
const ACCOUNT_ROLE_COMMUNITY_MAIN = 20;        //社区用户主账号
const ACCOUNT_ROLE_COMMUNITY_ATTENDANT = 21;   //社区用户从账号
const COMMUNITY_SWITCH_SMARTHOME = 4;
const ACCOUNT_GRADE_COMMUNITY = 21; //社区Account
const ACCOUNT_GRADE_OFFICE = 23; //办公Account

const ACCESS_GROUP_CHECK_RELAY = 0;
const ACCESS_GROUP_CHECK_SECURITY_RELAY = 1;

const COMMUNITY_DEVICE_TYPE_PUBLIC = 1; //公共设备
const COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2; //单元公共设备

const FACE_CREATOR_TYPE_PM = 0;
const FACE_CREATOR_TYPE_ENDUSER = 1;

const OEM_TYPE_AK = 0;
const OEM_TYPE_MYSMART = 1;
const OEM_TYPE_FASTTEL = 2;
const OEM_TYPE_BELAHOME = 3;
const OEM_TYPE_AZER = 4; //阿塞拜疆OEM类型

const FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT = 6;

const DA_TABLE_NAME_ARR = array(
"PersonalAccount",
"PersonalAccountCnf",
"PersonalAccountUserInfo",
"AccountUserInfo",
"Account",
"OfficeInfo",
"Devices",
"PersonalDevices",
"PubDevMngList",
"AccountAccess",
"UserAccessGroup",
"UserAccessGroupDevice",
"AccessGroup",
"AccessGroupDevice",
"CommPerPrivateKey",
"PersonalPrivateKey",
"CommunityRoom",
"PmAccountMap",
"CommunityInfo",
"ThirdPartCamera",
"PersonalThirdPartCamera",
"ContactBlackList",
"ContactFavoriteList",
"PersonalAccountCommunityInfo",
"PersonalAccountOfficeInfo",
"AccessGroup",
"AccessGroupDevice",
"PersonalPrivateKeyList",
"PersonalRfcardKey",
"PersonalRfcardKeyList",
"PubPrivateKey",
"PubPrivateKeyList",
"PubRfcardKey",
"PubRfcardKeyList",
"CommPerRfKey",
"PersonalAppTmpKey",
"PersonalAppTmpKeyList",
"PubAppTmpKey",
"PubAppTmpKeyList",
"Staff",
"Delivery",
"StaffAccess",
"DeliveryAccess",
"CommunityUnit",
"ManageFeature",
"DevicesSpecial",
"IndoorMonitorConfig",
"ExtraRelayList",
"CommunityCallRule",
"AmenityDevice",
"PersonalIDAccess",
"DeliveryIDAccess",
"StaffIDAccess",
"HoldDoor",
"AnalogDevice",
"DevicePushButton",
"OfficeAttendanceDevice",
"VideoStorage",
"VideoStorageDevice"
);
const FACE_FILE_SIZE_LIMIT = 1024*1024*10;

function GetOfficeRoleStr()
{
    $role = ACCOUNT_ROLE_OFFICE_MAIN. "," .  ACCOUNT_ROLE_OFFICE_ADMIN;
    return $role;
}

function DbData2Ids($data_array, $section)
{
    $ids = '';
    foreach ($data_array as $row => $data) {
        if (strlen($ids) == 0) {
            $ids = $data[$section];
        } else {
            $ids = $ids . "," . $data[$section];
        }
    }
    return $ids;
}


function DetectProjectTypeByMngID($communitid)
{
    global $db;
    global $cLog;

    //查主账号
    $grade = $db->querySList("select Grade from Account where ID = $communitid;", [])[0]['Grade'];
    if ($grade == 23) {//office角色
        return PROJECT_TYPE_OFFICE;
    }
    return PROJECT_TYPE_RESIDENCE;
}

function DetectProjectTypeByAccount($account)
{
    global $db;
    global $cLog;

    $Role = $db->querySList("select Role from PersonalAccount where Account = :Account;", [":Account" => $account])[0]["Role"];
    if ($Role == ACCOUNT_ROLE_OFFICE_MAIN || $Role == ACCOUNT_ROLE_OFFICE_ADMIN || $Role == ACCOUNT_ROLE_OFFICE_NEW_PER || $Role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN) {
        return PROJECT_TYPE_OFFICE;
    }
    return PROJECT_TYPE_RESIDENCE;
}

function DetectProjectTypeByAccountUUID($account_uuid)
{
    global $db;
    global $cLog;

    //查主账号
    $Role = $db->querySList("select Role from PersonalAccount where UUID = :uuid;", [":uuid" => $account_uuid])[0]["Role"];
    if ($Role == ACCOUNT_ROLE_OFFICE_MAIN || $Role == ACCOUNT_ROLE_OFFICE_ADMIN) {
        return PROJECT_TYPE_OFFICE;
    }
    return PROJECT_TYPE_RESIDENCE;
}

function DetectRoleByAccount($account)
{
    global $db;
    global $cLog;

    //查主账号
    $Role = $db->querySList("select Role from PersonalAccount where Account = :Account;", [":Account" => $account])[0]["Role"];
    return $Role;
}

function DetectProjectTypeByUserName($username)
{
    global $db;
    global $cLog;

    //查主账号
    $Role = $db->querySList("select Role from PersonalAccount where Name = :name;", [":name" => $username])[0]["Role"];
    if ($Role == ACCOUNT_ROLE_OFFICE_MAIN || $Role == ACCOUNT_ROLE_OFFICE_ADMIN) {
        return PROJECT_TYPE_OFFICE;
    }
    return PROJECT_TYPE_RESIDENCE;
}

function GetDistributorInfoByNode($node)
{
    global $db;
    return $db->querySList("select IsEnableAptChargePlan from DistributorInfo D 
                                        left join Account A on A.Account = D.Account 
                                        left join Account AA ON AA.ParentUUID = A.UUID
                                        left join PersonalAccount P ON P.ParentUUID = AA.UUID 
                                        where P.Account = :Account", [":Account" => $node])[0];
}

function CheckUidEnalbeSmarthome($uid)
{
    global $db;
    $community_id = 0;
    $account = $db->querySList("select Role, ParentID, EnableSmartHome from PersonalAccount where Account =:Account", [":Account" => $uid])[0];
    $role = $account["Role"];
    $parent_id = $account["ParentID"];
    $enable_smarthome = $account["EnableSmartHome"];

    if ($role == ACCOUNT_ROLE_COMMUNITY_MAIN) {
        $community_id = $parent_id;
        $main_account = $uid;
    } elseif ($role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) {
        $main_data = $db->querySList("SELECT ParentID,Account FROM PersonalAccount  WHERE ID = :ParentID", [":ParentID" => $parent_id])[0];
        $community_id = $main_data["ParentID"];
        $main_account = $main_data["Account"];
    } elseif ($role == ACCOUNT_ROLE_PERSONNAL_MAIN) {
        return $enable_smarthome;
    } elseif ($role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT) {
        $enable_smarthome = $db->querySList("SELECT EnableSmartHome FROM PersonalAccount  WHERE ID = :ParentID", [":ParentID" => $parent_id])[0]["EnableSmartHome"];
        return $enable_smarthome;
    }

    // 判断dis是否开启以apt配置家居开关
    $distributor_info = GetDistributorInfoByNode($main_account);
    if ($distributor_info["IsEnableAptChargePlan"]) {
        // 开关下放到apt,判断账号的家居开关
        $enable_smarthome = $db->querySList("select EnableSmartHome from PersonalAccountCnf where Account = :Account", [":Account" => $main_account])[0]["EnableSmartHome"];
    } else {
        // 开关未下放到apt,判断社区的家居开关
        $switch = $db->querySList("select Switch From CommunityInfo  WHERE AccountID = :AccountID", [":AccountID" => $community_id])[0]["Switch"];
        $enable_smarthome = switchHandle($switch, COMMUNITY_SWITCH_SMARTHOME);
    }
    return $enable_smarthome;
}

function UpdateUserQrUrl($uid, $url)
{
    global $db;
    $db->exec2ListWArray("update PersonalAccount set QrUrl=:url  where Account = :account;", [":account" => $uid, ":url" => $url]);
}

function GetUserQrUrl($uid, &$url)
{
    global $db;
    $url = "";
    $data = $db->querySList("select QrUrl from PersonalAccount where Account = :account;", [":account" => $uid]);
    if ($data) {
        $url = $data[0]["QrUrl"];
    }
}

function GetPMQrUrl($uuid, &$url)
{
    global $db;
    $url = "";
    $data = $db->querySList("select QrUrl from Account where UUID = :uuid;", [":uuid" => $uuid]);
    if ($data) {
        $url = $data[0]["QrUrl"];
    }
}

function UpdatePMQrUrl($uuid, $url)
{
    global $db;
    $db->exec2ListWArray("update Account set QrUrl=:url  where UUID = :uuid;", [":uuid" => $uuid, ":url" => $url]);
}
//将relay字段通过二进制按位转化
function relayInfo2RelayInt($relayinfo)
{
    //6.7relay字段变更为json数组
    $relays = json_decode($relayinfo, true);
    if(!is_array($relays)) {
        if(!empty($relayinfo)) {
            LOG_TRACE("pass relay wrong, origin relay info:" . $relayinfo);
        }
        return 0;
    }
    $relayValue = 0;
    $relayIndex = 0;
    foreach($relays as $relay) {
        if($relay['enable'] == 1) {
            $relayValue += (1 << $relayIndex);
        }
        $relayIndex++;
    }
    return $relayValue;
}

//根据relay按位累加值获取relay是否开启数组
function GetOpenRelayArray($relay_int) 
{
    $relay_index = 0;
    $can_open_relay = array();
    while($relay_int) {
        if($relay_int & 1) {
            $can_open_relay[$relay_index] = true;
        } else {
            $can_open_relay[$relay_index] = false;
        }
        //右移一位，按位依次判断relay是否开启
        $relay_int >>= 1;
        $relay_index++;
    }
    return $can_open_relay;
}

function CheckPerCanOpenDoor($node, $mac)
{
    global $db;
    $data = $db->querySList("select IsRepost,Relay,SecurityRelay From PersonalDevices where  MAC = :mac AND Node = :node ", [":mac" => $mac, ":node" => $node])[0];
    if ($data) {
        return $data;
    }
    return array();
}

function GetDevInfo($mac)
{
    global $db;
    $data = $db->querySList("select IsRepost,Relay,SecurityRelay,Node,SipAccount From Devices where MAC = :mac" , [":mac" => $mac])[0];
    if ($data) {
        return $data;
    }
    return array();
}

function GetPerDevInfo($mac)
{
    global $db;
    $data = $db->querySList("select IsRepost,Relay,SecurityRelay,Node,SipAccount From PersonalDevices where MAC = :mac" , [":mac" => $mac])[0];
    if ($data) {
        return $data;
    }
    return array();
}

function checkIsNewCommunity($community_id)
{
    global $db; 
    $data = $db->querySList("select IsNew from CommunityInfo where AccountID = :community_id" , [":community_id" => $community_id]);
    if($data) {
        return $data[0]["IsNew"];
    }
    return 0;
}
//根据设备信息获取时区
function getTimeZone($mng_id)
{
    global $db;
    $data = $db->querySList("select TimeZone From Account where ID=:id" , [":id" => $mng_id])[0];
    if($data) {
        return $data["TimeZone"];
    }
    return "";
}

//校验权限组时间
function checkAccessGroupTime($timezone, $ag_list, $type)
{
    $now_time = \util\computed\getNow();
    $now_time = \util\time\setTimeZone($now_time, $timezone, "", "+");
    $can_open_relay = 0;

    foreach ($ag_list as $key => $ag) {
        if ($ag['default']) {
            return ALL_RELAY_OPEN_VAL;//默认权限组Relay全开
        }

        //校验类型
        if ($type == ACCESS_GROUP_CHECK_RELAY) {
            $ag_relay = $ag['Relay'];
        } elseif ($type == ACCESS_GROUP_CHECK_SECURITY_RELAY) {
            $ag_relay = $ag['SecurityRelay'];
        }
        
        if ($ag['SchedulerType'] == 0) {
            if (strtotime($ag['BeginTime']) < strtotime($now_time) && strtotime($ag['EndTime']) > strtotime($now_time)) {
                $can_open_relay = $can_open_relay | $ag_relay;
            }
        } elseif ($ag['SchedulerType'] == 1) {
            if (\util\time\checkTime($ag['StartTime'], $ag['StopTime'])) {
                $can_open_relay = $can_open_relay | $ag_relay;
            }
        } elseif ($ag['SchedulerType'] == 2) {
            $week = date('w', time());
            if (\util\time\checkWeek($ag['DateFlag'], $week) && \util\time\checkTime($ag['StartTime'], $ag['StopTime'])) {
                $can_open_relay = $can_open_relay | $ag_relay;
            }
        }
    }
    return $can_open_relay;
}

//校验权限组对应Relay/SeRelay值
function CheckAccessGroupCanOpenDoor($account, $mac, $type)
{
    $ret = 0;
    global $db;
    $dev_info = $db->querySList("select Relay,SecurityRelay,MngAccountID,UnitID,Grade from Devices where Mac=:mac", [":mac" => $mac])[0];
    if(!$dev_info) {
        return $ret;
    }
    $ag_list = array();
    //公共设备
    if($dev_info['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC || $dev_info['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT) {
        $list = $db->querySList("select G.ID,CommunityID,UnitID,SchedulerType,DateFlag,BeginTime,EndTime,StartTime,StopTime From AccessGroup G left join AccountAccess A 
        on A.AccessGroupID=G.ID where A.Account=:Account" , [":Account" => $account]);
        if(!$list) {
            return $ret;
        }
        foreach($list as $key => $ag) {
            $community_id = $ag['CommunityID'];
            $unit_id = $ag['UnitID'];
            $ag_id = $ag['ID'];
            //默认权限组
            if ($unit_id > 0) {
                $ag['default'] = 1;
                if ($dev_info['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC && $dev_info['MngAccountID'] == $community_id) {
                    array_push($ag_list, $ag);
                } elseif ($dev_info['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && $dev_info['UnitID'] == $unit_id) {
                    array_push($ag_list, $ag);
                }
            } else {
                $exist_dev = $db->querySList("select MAC,Relay,SecurityRelay From AccessGroupDevice where AccessGroupID=:AGID and MAC=:MAC" , [":AGID" => $ag_id, ":MAC" => $mac]);
                if ($exist_dev) {
                    if($type == ACCESS_GROUP_CHECK_RELAY) {
                        $ag['Relay'] = $exist_dev[0]['Relay'];
                    } else if($type == ACCESS_GROUP_CHECK_SECURITY_RELAY) {
                        $ag['SecurityRelay'] = $exist_dev[0]['SecurityRelay'];
                    }
                    array_push($ag_list, $ag);
                }
            }
        }
    }
    else {
        $ag_list = $db->querySList("select Relay,SecurityRelay,SchedulerType,DateFlag,BeginTime,EndTime,StartTime,StopTime From UserAccessGroupDevice D left join UserAccessGroup A 
        on A.ID=D.UserAccessGroupID where A.Account=:account and D.Mac=:mac", [":account" => $account, ":mac" => $mac]);
        if (!$ag_list) {
            return $ret;
        }
    }
    $time_zone = getTimeZone($dev_info['MngAccountID']);
    if(!$time_zone) {
        return $ret;
    }
    return checkAccessGroupTime($time_zone, $ag_list, $type);
}

function CheckOldCommCanOpenDoor($UserConf, $mac)
{
    global $db;
    //家庭设备
    $isCanOpendoor = 0;
    $apt_dev = $db->querySList("select count(1) as count From Devices where Node = :node and Mac = :mac and Grade = 3" , [":node" => $UserConf['Node'], ":mac" => $mac])[0];
    
    if ($apt_dev['count'] == 0) {
        //最外围公共设备
        $comm_dev = $db->querySList("select count(1) as count From Devices where Mac = :mac and (MngAccountID = :MngID and Grade=1)", [":mac" => $mac, ":MngID" => $UserConf["MngID"]])[0];
        if ($comm_dev['count'] > 0) {
            $isCanOpendoor = 1;
        } else {
            //楼栋外围公共设备
            $unit_dev = $db->querySList("select count(1) as count From Devices where Mac=:mac and (MngAccountID=:MngID and Grade=2 and UnitID=:UnitID)", 
            [":mac" => $mac, ":MngID" => $UserConf['MngID'], ":UnitID" => $UserConf['UnitID']])[0];
            if ($unit_dev['count'] > 0) {
                $isCanOpendoor = 1;
            }
        }
    } else {
        $isCanOpendoor = 1;
    }
    return $isCanOpendoor;
}

//设备relay和权限组relay取交集
function GetValidRelay($relay_int, $acc_relay_int)
{
    return $relay_int & $acc_relay_int;
}

function GetPerMasterAccountInfo($uuid)
{
    global $db;
    $role = ACCOUNT_ROLE_COMMUNITY_MAIN;
    $per_account_info = $db->querySList("select Account, UserInfoUUID, Language, UserInfoUUID
                                                        from PersonalAccount where UUID = :uuid And Role = :role", [":uuid" => $uuid, ":role" => $role])[0];
    return $per_account_info;
}

function GetPerAccountUserInfo($uuid)
{
    $personalAccountUserInfoDao = new \dao\PersonalAccountUserInfo();
    return $personalAccountUserInfoDao->selectByUUID($uuid)[0];
}
