<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-17 18:16:13
 * @LastEditors: cj
 */
namespace model\user;

trait update
{
    public function updatePerMain()
    {
        $params = [
            "ID"=>"",
            "Name"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
            "PhoneStatus"=>"",
            "Address"=>"",
            "TimeZone"=>"",
            "RoomNumber"=>"",
            "EnableIpDirect"=>"",
            "MAC"=>"",
            "Location"=>"",
            "Relay"=>"",
            "isSingleMonitor"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $email = $params["Email"];
        $mobile = $params["MobileNumber"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $phoneStatus = $params["PhoneStatus"];
        $address = $params["Address"];
        $timeZone = $params["TimeZone"];
        $roomNumber = $params["RoomNumber"];
        $enableIpDirect = $params["EnableIpDirect"];
        $mac = $params["MAC"];
        $location = $params["Location"];
        $relay = $params["Relay"];
        $isSingleMonitor = $params["isSingleMonitor"];
        $this->log->actionLog("#model#user#updatePerMain#params=".json_encode($params));
        $oldData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]])[0];

        // V6.2.1 kxl 新增PhoneStatus修改时去刷新计费模型
        $this->services['billsysUtil']->checkPerPlan($oldData['ParentID'], 1, [$id]);

        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Name"=>$name,":Email"=>$email,":MobileNumber"=>$mobile,":Phone"=>$phone,":Phone2"=>$phone2,":EnableIpDirect"=>$enableIpDirect,
        ":Phone3"=>$phone3,":PhoneCode"=>$phoneCode,":Address"=>$address,":TimeZone"=>$timeZone,":RoomNumber"=>$roomNumber,":PhoneStatus"=>$phoneStatus]);
        $account = $this->db->querySList("select Account from PersonalAccount where ID = :ID", [":ID"=>$id])[0]['Account'];
        if ($isSingleMonitor == '0') {
            $this->db->delete2ListWKey("DevicesSpecial", "Account", $account);
        } else {
            // 查询添加特定室内机是否为已存在mac
            $existIndoorDevice = $this->db->querySList("select D.MAC, D.ID from PersonalDevices D join PersonalAccount A on D.Node = A.Account where A.ID = :ID and D.MAC = :MAC and D.Type = 2", [":ID"=>$id, ":MAC"=>$mac]);
            // 删除已存在特定室内机数据
            $this->db->delete2ListWKey("DevicesSpecial", "Account", $account);
            if (count($existIndoorDevice) !== 0) {
                $this->db->insert2List("DevicesSpecial", [":Account"=>$account, ":MAC"=>$mac]);
                $this->db->update2ListWID("PersonalDevices", [":ID"=>$existIndoorDevice[0]['ID'], ":Location"=>$location, ":relay"=>$relay]);
            } else {
                // 不存在走添加新设备步骤
                $this->db->insert2List("DevicesSpecial", [
                    ":Account"=>$account,
                    ":MAC"=>$mac
                ]);
                \util\computed\setGAppData(["Type"=>"2", "Node"=>$account, "Relay"=>$relay, "Location"=>$location]);
                $this->macCheck($mac);
                \util\computed\setGAppBranch("addIndoorMonitor");
            }
        }
        if ($oldData["EnableIpDirect"] != $enableIpDirect) {
            $this->services["sip"]->setIpDirect("PersonalDevices", $oldData["Account"], $enableIpDirect);
        }
        $this->auditLog->setLog(AuditCodeEditFamilyMaster, $this->env, [$oldData['Account']], $oldData['Account']);
        if ($oldData["Email"] != $email) {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $name], $oldData['Account']);
        }
        if ($oldData["MobileNumber"] != $mobile) {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $name], $oldData['Account']);
        }

        if ($oldData["PhoneStatus"] != $phoneStatus) {
            $this->services["sip"]->addMul2Freeswish([['sip'=>$oldData['Account'], "enableGroup"=>$phoneStatus == 1 ? 0 : 1]]);
        }
    }

    public function macCheck($mac)
    {
        global $cMessage;
        $this->log->actionLog("#middle#macCheck#mac=".$mac);
        if (\util\string\checkMAC($mac)) {
            $cMessage->echoErrorMsg(StateMACLength);
        }
        if ($this->db->isExistFiled("Devices", [":MAC"=>$mac], null) || $this->db->isExistFiled("PersonalDevices", [":MAC"=>$mac], null)) {
            $cMessage->echoErrorMsg(StateIndoorMacNotCorrect);
        }
    }

    public function updatePerSub()
    {
        $params = [
            "ID"=>"",
            "Name"=>"",
            "Phone"=>"",
            "PhoneCode"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $phone = $params["Phone"];
        $phoneCode = $params["PhoneCode"];
        $this->log->actionLog("#model#user#updatePerSub#id=$id;name=$name;phone=$phone;phoneCode=$phoneCode;");
        $data = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID'=>$id])[0];
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Name"=>$name,":Phone"=>$phone,":PhoneCode"=>$phoneCode]);
        $this->log->endUserLog(2, null, "edit user:$name");
        $this->auditLog->setLog(AuditCodeEditFamilyMember, $this->env, [$data['Account']], $data['Account']);
    }

    public function updateComSub()
    {
        $params = [
            "ID"=>"",
            "Name"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Phone"=>"",
            "PhoneCode"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $phone = $params["Phone"];
        $phoneCode = $params["PhoneCode"];
        $data = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID'=>$id])[0];
        $this->log->actionLog("#model#user#updateComSub#id=$id;name=$name;phone=$phone;phoneCode=$phoneCode;firstName=$firstName;lastName=$lastName");
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Name"=>$name,":FirstName"=>$firstName,":LastName"=>$lastName,":Phone"=>$phone,":PhoneCode"=>$phoneCode]);
        $this->log->endUserLog(2, null, "edit user:$name");
        $this->auditLog->setLog(AuditCodeEditFamilyMember, $this->env, [$data['Account']], $data['Account']);
    }

    public function updateComMainUser()
    {
        $params = [
            "ID"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
            "CallType"=>"",
            "Key"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $email = $params["Email"];
        $mobile = $params["MobileNumber"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $phoneState = $params["CallType"];
        $key = $params["Key"];
        $userId = $params["userAliasId"];

        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $buildId = $data[0]["UnitID"];
        $roomID = $data[0]["RoomID"];
        $account = $data[0]["Account"];
        $this->modifyControl($userId, $id, $buildId, $roomID, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, false, $email, $mobile);
        \util\computed\setGAppData(["Account"=>$account]);
    }

    public function updateComMainUserForPM()
    {
        $params = [
            "ID"=>"",
            "Build"=>"",
            "Room"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
            "PhoneState"=>"",
            "Key"=>"",
            "RfCard"=>"",
            "RoomNumber"=>"",
            "TempKeyPermission"=>"",
            "EnableIpDirect"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $email = $params["Email"];
        $mobile = $params["MobileNumber"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $phoneState = $params["PhoneState"];
        $key = $params["Key"];
        $card = $params["RfCard"];
        $roomName = $params["RoomNumber"];
        $tempKeyPermission = $params["TempKeyPermission"];
        $enableIpDirect = $params["EnableIpDirect"];
        $userId = $params["userAliasId"];

        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $buildId = $data[0]["UnitID"];
        $roomID = $data[0]["RoomID"];
        $account = $data[0]["Account"];
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":RoomNumber"=>$roomName,":EnableIpDirect"=>$enableIpDirect]);
        $this->modifyControl($userId, $id, $buildId, $roomID, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, $card, $email, $mobile, $tempKeyPermission);
        if ($data[0]["EnableIpDirect"] != $enableIpDirect) {
            $this->services["sip"]->setIpDirect("Devices", $data[0]["Account"], $enableIpDirect);
        }
        \util\computed\setGAppData(["Account"=>$account]);
    }

    public function updateRoom()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Name"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
            "CallType"=>"",
            "Key"=>"",
            "RoomNumber"=>"",
            "RoomName"=>"",
            "IsAddUser"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "userAliasId"=>"",
            "EnableIpDirect"=>"",
            "Language"=>"",
            "IsComMonitor"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $email = $params["Email"];
        $mobile = $params["MobileNumber"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $userId = $params["userAliasId"];
        $aptName = $params["RoomNumber"];
        $aptNo = $params["RoomName"];
        $isAddUser = $params["IsAddUser"];
        $phoneState = $params["CallType"];
        $key = $params["Key"];
        $lang = $params["Language"];
        $enableIpDirect = $params["EnableIpDirect"];
        $isComMonitor = $params["IsComMonitor"];
       
        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]])[0];
        $roomID = $data["RoomID"];
        $buildId = $data["UnitID"];
        $special = $data["Special"];
        // 编辑apt时检查是否重复名字
        if (!$this->db->isExistFiled('CommunityUnit', [':ID' => $buildId, ':MngAccountID' => $userId])) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        if ($this->db->isExistFiled('CommunityRoom', [':RoomName' => $aptNo, ':UnitID' => $buildId], $roomID)) {
            $cMessage->echoErrorMsg(StateAptExit, [], [$aptNo]);
        }
        $oldRoomData = $this->db->querySList('select RoomName from CommunityRoom where ID = :ID', [":ID"=>$roomID])[0];
        if ($oldRoomData["RoomName"] != $aptNo) {
            $this->db->update2ListWID("CommunityRoom", [":ID"=>$roomID,":RoomName"=>$aptNo]);
            $communityData = $this->db->querySList('select A.Location,B.Account as Installer from Account A join Account B on A.ManageGroup = B.ID where A.ID = :ID', [":ID"=>$userId])[0];
            $unitData = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$buildId])[0];
            $this->auditLog->setLog(
                AuditCodeSetAptNumber,
                $this->env,
                [$unitData['UnitName'], $aptNo],
                $communityData['Installer']
            );
            // 通知智能家居增加房间主账号字段  @LastEditors: cj
            \util\computed\setSmartHomeTask(['Type' => 13, 'Key' => $data["Account"], 'Info' => json_encode(['apt'=>$aptNo]), 'RoomSip'=>$data["Account"]]);
        }
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":RoomNumber"=>$aptName]);
        if ($isAddUser === '1') {
            if ($special == 0) {
                $this->modifyControl($userId, $id, $buildId, $roomID, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, false, $email, $mobile);
            } else {
                $this->addComMainUsreCom($userId, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, false, $lang);
                // 兼容新旧小区，兼容installer添加主账户
                $isNew = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [':AccountID' => $userId])[0]['IsNew'];
                if ($isNew == 1) {
                    \util\computed\setGAppBranch('addAccess');
                }
            }
            // V5.4 sip call or ip call
            $this->db->update2ListWKey("PersonalAccount", [":RoomID"=>$roomID,":EnableIpDirect"=>$enableIpDirect], "RoomID");
            if ($data["EnableIpDirect"] != $enableIpDirect) {
                $this->services["sip"]->setIpDirect("Devices", $data["Account"], $enableIpDirect);
            }
        }
        \util\computed\setGAppData(["Account"=>$data["Account"]]);
        // 判断是不是室内机方案
        if ($isComMonitor == "1") {
            // 修改设备
            $this->models["deviceCommunity"]->edit(1);
        }
    }

    /**
     * @msg: 重置密码
     */
    public function resetPw()
    {
        $params = [
            "ID"=>"",
            "Password"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $password = $params["Password"];
        if ($password) {
            \util\computed\setGAppData(["unSend"=>true]);
        }
        $password =  $password ?: \util\string\generatePw(8);

        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Passwd"=>md5($password)]);
        $data = $this->db->querySList("select Email,Account from PersonalAccount where ID=:ID", [":ID"=>$id])[0];
        $account = $data["Account"];
        $email = $data["Email"];
        $this->log->actionLog("#model#user#resetPerMainPw#id=$id;email=$email;account=$account");
        // 删除Token
        $this->deleteMainUserToken($account);
        $this->deleteSubUserToken($account);
        // 更改数据流
        \util\computed\setGAppData(["Account"=>$account,"Email"=>$email,"Passwd"=>$password]);
        $this->log->endUserLog(1, null, "modify password");
    }

    /**
     * @msg: 设置过期时间
     * @service: sip
     */
    public function setExpireTime()
    {
        $params = [
            "ID"=>"",
            "SelfTimeZone"=>"",
            "ExpireTime"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $expireTime = $params["ExpireTime"];
        $this->log->actionLog("#model#user#setExpireTime#id=$id;timeZone=$timeZone;expireTime=$expireTime");
        $expireTime =  \util\time\setTimeZone($expireTime, $timeZone, "", "-");
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $this->db->update2ListWID("PersonalAccount", [":ExpireTime"=>$expireTime,":ID"=>$id]);
        // 从账号
        $this->db->exec2ListWArray("update PersonalAccount set ExpireTime=:ExpireTime where ParentID = :ID and Role in (11,21)", [":ExpireTime"=>$expireTime,":ID"=>$id]);
        $this->services["sip"]->insertSipEnable($userData[0]["Account"]);
    }

    public function activeAccount()
    {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->models["order"]->activeAccount($id);
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $this->services["sip"]->insertSipEnable($userData[0]["Account"]);
    }

    public function afterUpdateEmail()
    {
        $this->deleteUserToken();
        $params = ["ID"=>""];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        // 重新生成密码
        $password = \util\string\generatePw(8);
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Passwd"=>md5($password)]);
        $account = $this->db->querySList(
            'select Account from PersonalAccount where ID=:ID',
            [':ID'=>$id]
        )[0]['Account'];
        $this->db->delete2ListWKey('AppPushToken', 'Account', $account);
        \util\computed\setGAppData(["Passwd"=>$password]);
    }
    // 个人主账户更新后
    public function afterUpdatePerMain()
    {
        // $params = [
        //     "ID"=>""
        // ];

        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $id = $params["ID"];
        // $data = $this->db->queryAllList("PersonalAccount",["equation"=>[":ID"=>$id]])[0];
        // $account  =$data["Account"];
        // $this->log->actionLog("#model#CUser#afterUpdatePerMain#account=$account");
        // personnalUpdateNodeNotify($account);
    }
    public function afterUpdatePerSub()
    {
    }

    public function afterUpdateComSub()
    {
    }

    // 社区主账户更新后
    public function afterUpdateComMain()
    {
        // $params = [
        //     "ID"=>""
        // ];

        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $id = $params["ID"];
        // $data = $this->db->queryAllList("PersonalAccount",["equation"=>[":ID"=>$id]])[0];
        // $account  =$data["Account"];
        // $this->log->actionLog("#model#CUser#afterUpdateComMain#account=$account");
        // communityUpdateNodeNotify($data[0]["Account"], $data[0]["ParentID"], $data[0]["UnitID"], 1);
    }
    

    public function afterUpdatePhone()
    {
        $params = [
            "Account"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneOld"=>"",
            "Phone2Old"=>"",
            "Phone3Old"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneOld = $params["PhoneOld"];
        $phone2Old = $params["Phone2Old"];
        $phone3Old = $params["Phone3Old"];
        // $this->log->actionLog("#model#CUser#afterUpdatePerMain#account=$account;phone=$phone;phone2=$phone2;phone3=$phone3;phoneOld=$phoneOld;phone2Old=$phone2Old;phone3Old=$phone3Old");
        // TODO
    }


    public function afterUpdatePerMName()
    {
        // $params = [
        //     "ID"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $id = $params["ID"];
        // $mainUser = $this->db->querySList("select Account from PersonalAccount ID=:ID",[":ID"=>$id])[0]["Account"];
        // $this->log->actionLog("#model#CUser#afterUpdatePerMName#id=$id;mainUser=$mainUser");
        // personnalUpdateNodeNotify($mainUser);
    }

    /**
     * @msg: 个人从账户name变更
     */
    public function afterUpdatePerSName()
    {
        // $params = [
        //     "ID"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $id = $params["ID"];
        // $mainUser = $this->db->querySList("select A.Account from PersonalAccount A join PersonalAccount B on A.ID=B.ParentID where B.ID=:ID",[":ID"=>$id])[0]["Account"];
        // $this->log->actionLog("#model#CUser#afterUpdatePerName#id=$id;mainUser=$mainUser");
        // personnalUpdateNodeNotify($mainUser);
    }

    /**
     * @msg: 社区从账户name变更
     */
    public function afterUpdateComSName()
    {
        // $params = [
        //     "ID"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $id = $params["ID"];
        // $mainData = $this->db->querySList("select A.Account,A.ParentID,A.UnitID from PersonalAccount A join PersonalAccount B on A.ID=B.ParentID where B.ID=:ID",[":ID"=>$id])[0];
        // $mainUser = $mainData["Account"];
        // $comMng = $mainData["ParentID"];
        // $unitId = $mainUser["UnitID"];
        // $this->actionLog("#model#CUser#afterUpdateComSName#id=$id;mainUser=$mainUser;comMng=$comMng;unitId=$unitId");
        // communityUpdateNodeNotify($mainUser,$comMng,$unitId,1);
    }

    public function afterChangePw()
    {
        // $params = [
        //     "Account"=>"",
        //     "Email"=>"",
        //     "Passwd"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $account = $params["Account"];
        // $email = $params["Email"];
        // $password = $params["Passwd"];
        // $this->actionLog("#model#CUser#afterPerChangePw#account=$account;email=$email");
        // perChangePwd($account, $password, $email);
    }

    public function updateAllUserForPM()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Name"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $email = $params["Email"];
        $mobile = $params["MobileNumber"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];
        $communityId = $params["userAliasId"];
        
        $userData = $this->db->querySList('select Role,ParentID,Account from PersonalAccount where ID=:ID', [":ID"=>$id])[0];
        if ($userData['Role'] == 21) {
            $mainData = $this->db->querySList('select Role,ParentID from PersonalAccount where ID=:ID', [":ID"=>$userData['ParentID']])[0];
            if ($mainData['ParentID']!= $communityId) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $bindArray = [
                ":ID"=>$id,
                ":Name"=>$name,
                ":FirstName"=>$firstName,
                ":LastName"=>$lastName,
                ":Email"=>$email,
                ":MobileNumber"=>$mobile,
                ":Phone"=>$phone,
                ":PhoneCode"=>$phoneCode
            ];
        } elseif ($userData['ParentID'] != $communityId) {
            $cMessage->echoErrorMsg(StateNotPermission);
        } else {
            $bindArray = [
                ":ID"=>$id,
                ":Name"=>$name,
                ":FirstName"=>$firstName,
                ":LastName"=>$lastName,
                ":Email"=>$email,
                ":MobileNumber"=>$mobile,
                ":Phone"=>$phone,
                ":Phone2"=>$phone2,
                ":Phone3"=>$phone3,
                ":PhoneCode"=>$phoneCode
            ];
        }

        $this->db->update2ListWID('PersonalAccount', $bindArray);
        \util\computed\setGAppData(["Account"=>$userData['Account']]);
    }

    // 刷新计费模型
    public function updateBillList()
    {
        /*@param Type 0 单住户；1 社区; 2 办公
         *ID 社区ID/installer单住户ID/办公ID
         *Step 0:获取刷新列表 1:刷新计费模型
        */
        $params = [
            "Type" => "",
            "ID" => "",
            "Step" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $id = $params["ID"];
        $step = $params["Step"];
        $chargeService = $this->services["billsysUtil"];
        if ($step == 0) {
            if ($type == 1) {
                // 已激活用户刷新过期时间列表
                list($updatinginfo, $total) = $chargeService->checkPlan($id, 0, $step);
                // 查询未激活主账号
                list($updatinginfo0, $total0) = $this->updateActiveUser($id, [COMENDMROLE]);
                $updatinginfo = array_merge($updatinginfo, $updatinginfo0);
                $total = $total + $total0;
            } elseif ($type == 0) {
                // 已激活用户刷新过期时间列表
                list($updatinginfo, $total) = $chargeService->checkPerPlan($id, $step);
                // 查询未激活主账号
                list($updatinginfo0, $total0) = $this->updateActiveUser($id, [PERENDMROLE]);
                $updatinginfo = array_merge($updatinginfo, $updatinginfo0);
                $total = $total + $total0;
            } elseif ($type == 2) {
                // 已激活用户刷新过期时间列表
                list($updatinginfo, $total) = $chargeService->checkPlan($id, 0, $step);
                // 查询未激活主账号
                list($updatinginfo0, $total0) = $this->updateActiveUser($id, OFFROLE);
                $updatinginfo = array_merge($updatinginfo, $updatinginfo0);
                $total = $total + $total0;
            }
            $data = ["row" => $updatinginfo, "detail" => $updatinginfo, "total" => $total];
            \util\computed\setGAppData(["data" => $data]);
        } else {
            if ($type == 1) {
                $this->updateActiveUser($id, [COMENDMROLE], $step);
                $chargeService->checkPlan($id, 0, $step);
            } elseif ($type == 0) {
                $this->updateActiveUser($id, [PERENDMROLE], $step);
                $chargeService->checkPerPlan($id, $step);
            } elseif ($type == 2) {
                $this->updateActiveUser($id, OFFROLE, $step);
                $chargeService->checkPlan($id, 0, $step);
            }
        }
    }

    public function updateActiveUser($id, $role, $step = 0)
    {
        $updatinginfo = [];
        $total = 0;
        $role = implode(',', $role);
        // 查询未激活主账号,住人才能激活
        $user = $this->db->querySList(
            "select ID from PersonalAccount where ParentID = :ParentID and Role in ($role) and Active = 0 and Special = 0",
            [":ParentID" => $id]
        );
        if ($step == 0) {
            foreach ($user as $val) {
                list($updatinginfo1, $total1) = $this->models["order"]->activeAccount($val["ID"], 0, 1);
                $updatinginfo = array_merge($updatinginfo, $updatinginfo1);
                $total = $total + $total1;
            }

            return [$updatinginfo, $total];
        } else {
            foreach ($user as $val) {
                $this->models["order"]->activeAccount($val["ID"], 1, 1);
                $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $val["ID"]]]);
                $this->services["sip"]->insertSipEnable($userData[0]["Account"]);
            }
        }
    }

    /**
     * kit注册个人主账户
     */
    public function registerPerMainUser()
    {
        global $cMessage;
        $params = [
            "Email"=>"",
            "PhoneCode"=>"",
            "MobileNumber"=>"",
            "Account"=>"",
            "Token"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $phoneCode = $params["PhoneCode"];
        $mobileNumber = $params["MobileNumber"];
        $account = $params["Account"];
        $token = $params["Token"];
        $this->log->actionLog("#model#user#registerPerMainUser#params=".json_encode($params));

        //防暴力破解
        \util\computed\checkCommonLimit('kit');
        // 验证
        $valid = $this->db->querySList("select ID from PendingRegUser where Account = :Account and Token = :Token", [
            ":Account" => $account,
            ":Token" => $token
        ]);
        if (count($valid) == 0) {
            \util\computed\addCommonLimit('kit');
            $cMessage->echoErrorMsg(StateQRCodeInValid);
        }

        // 更新信息
        $this->db->update2ListWKey("PersonalAccount", [":Account"=>$account, ":Email"=>$email, ":MobileNumber"=>$mobileNumber, ":PhoneCode"=>$phoneCode], "Account");
        $this->db->update2ListWKey("PendingRegUser", [":Account"=>$account, ":Status"=>1], "Account");
        // 填写邮箱，生成密码，发送邮件
        if ($email) {
            $this->deleteUserToken();
            // 生成密码
            $password = \util\string\generatePw(8);
            $this->db->update2ListWKey("PersonalAccount", [":Account"=>$account,":Passwd"=>md5($password)], "Account");
            \util\computed\setGAppData(["Passwd"=>$password]);
            // 发送邮件
            $this->models["notify"]->userCreateEmail();
        }
        $id = $this->db->querySList("select ID from PersonalAccount where Account = :Account", [":Account" => $account])[0]['ID'];
        \util\computed\setGAppData(["ID"=>$id]);
    }
}
