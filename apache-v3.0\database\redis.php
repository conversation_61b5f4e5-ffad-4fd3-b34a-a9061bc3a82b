<?php
/*
 * @Description: redis
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-20 15:21:42
 * @LastEditors  : kxl
 */
namespace database;
include_once __DIR__."/../config/base.php";
class CRedis {
    private static $instance;
    private function __construct () {
        if (ENABLE_REDIS_SENTINEL == 0) {
           self::$instance = $this->ConnectRedis(REDISIP, REDISSOCKET);
       }   
       else {
           $sentinels=explode(',',REDIS_SENTINEL_HOSTS);
           //随机打乱数组
           shuffle($sentinels);
           foreach ($sentinels as $val) {
               $sentinel_redis = new \Redis();
               $sentinel=explode(':', $val);
               //连接sentinel
               $ret = $sentinel_redis->connect($sentinel[0], $sentinel[1]);
               if ($ret) {
                    $result = $sentinel_redis->rawCommand('SENTINEL', 'masters');
                    $datas = $this->parseArrayResult($result);
                    //不考虑多主
                    $redis = $this->ConnectRedis($datas[0]["ip"], $datas[0]["port"]);
                    if ($redis)  {
                        //判断sentinel告知的是不是真正的主库
                        $result = $redis->rawCommand('INFO', 'Replication');
                        if (strstr($result, "role:master")) {
                            self::$instance = $redis;
                            break;
                        }
                        else
                        {
                            //从别的sentinel那里继续查找
                        }
                    }    
               }
           }
       }
    }

    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) new self();
        return self::$instance;
    }

    function connectRedis($ip, $port) {
       $redis = new \Redis();
       $ret = $redis->connect($ip, $port);
       if ($ret)
       {
            $redis->auth(REDISPW);
            return $redis;
       }
       return null;
    }
    
    //这个方法可以将以上sentinel返回的信息解析为数组
    function parseArrayResult(array $data)  {
        $result = array();
        $count = count($data);
        for ($i = 0; $i < $count;) {
            $record = $data[$i];
            if (is_array($record)) {
                $result[] = $this->parseArrayResult($record);
                $i++;
            } else {
                $result[$record] = $data[$i + 1];
                $i += 2;
            }
        }
        return $result;
    } 
}