import { EventEmitter } from 'events';
import util from 'util';
import dayjs from 'dayjs';
import config from '../../config';
import Log from '../share/log';
import database, { Conn, DbEvent } from '../share/database';
import NotifyCloudAdapt,
{
    activeEmail, renewId, updatePerConfig, featurePlan, communityChange, officeRenew,
    adaptHeaderFrom, officeFeaturePlanRenew, pmAccountActiveEmail, singleVideoStorageNotify, communityVideoStorageNotify,sendEmailNotify
}
from '../share/notify-cloud-adapt';
import { HttpClient, PhpAdaptNotify } from '../share/httpClient';
import { encryptData, decryptData } from '../share/security';
import { outputComputedCount } from '../share/util';
import http from "https";

const MaxExpireTime = '2299-12-31 23:59:59';
const SingleMainUser = 10;
const SingleSubUser = 11;
const MultipleMainUser = 20;
const MultipleSubUser = 21;
const NewOfficeUser = 32;
const OfficeUsers = [30, 31, 32];
const PmAppUser = 40;

const InstallToCommunity = 21;
const InstallToSingle = 22;
const InstallToOffice = 23;
const orderType = Object.freeze({
    active: 1 as 1,
    renewByMonth: 2 as 2,
    buyApp: 3 as 3,
    renewLandline: 4 as 4,
    featureOnce: 5 as 5,
    featureMonthly: 6 as 6,
    featureChange: 7 as 7,
    renewByDay: 8 as 8,
    autoRenew: 9 as 9,
    singleVideoStorage: 10 as 10,
    communityVideoStorage: 11 as 11,
    rentManagerRenew: 12 as 12,
    singleThirdLockActive: 14 as 14,
    communityThirdLockActive: 15 as 15,
    singleThirdLockRenew: 16 as 16,
    communityThirdLockRenew: 17 as 17,
});
const NoticeUrl = '';

interface RequestParams {
    timestamp: number;
    trace_id: string;
    data: {
        notice: string;
        id: string;
        timestamp: number;
        param: Object[]
    };
}

interface ThirdLockItem {
    communityName: string;
    unitName: string;
    roomName: string;
    lockName: string;
    expirationTime: string;
}

interface ThirdLockObj {
    email: string;
    user: string;
    email_type: string;
    language: string;
    expiration_time: string;
    lock_num: number;
    table_list: ThirdLockItem[];
}

function computedLastDate(time: string, month: number | string) {
    const oldEndDay = time.split(' ')[0].split('-')[2];
    const beginDate = dayjs(time).format('YYYY-MM-01');
    month = Number(month) + 1;

    const endTime = dayjs(beginDate).add(month, 'month').subtract(1, 'day').format('YYYY-MM-DD');

    const endDate = endTime.split(' ')[0].split('-');
    let endDay = endDate[2];

    endDay = Number(endDay) < Number(oldEndDay) ? endDay : oldEndDay;

    return `${endDate[0]}-${endDate[1]}-${endDay} ${time.split(' ')[1]}`;
}

function computedActiveExpireTime(chargeData: {
    ActiveFee: number;
    MonthlyFee: number;
    AddAppFee: number;
    AppNumber: number;
}) {
    if (chargeData.MonthlyFee === 0) return MaxExpireTime;
    const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
    return computedLastDate(now, 1);
}

function computedThirdLockActiveExpireTime(chargeData: {
    ActiveFee: number;
    MonthlyFee: number;
}) {
    if (chargeData.MonthlyFee === 0) return MaxExpireTime;
    const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
    return computedLastDate(now, 1);
}


/**
 * 获取十进制数在二进制表示中所有为 1 的位的位置。
 *
 * @param decimal - 要检查的十进制数。
 * @returns 一个数组，包含所有为 1 的位的位置（从 1 开始）。
 */
function getBitPositions(decimal: number): number[] {
    const positions: number[] = [];
    let position = 1;

    while (decimal > 0) {
        if ((decimal & 1) === 1) {
            positions.push(position);
        }
        decimal >>= 1;
        position++;
    }

    return positions;
}

class Deal {
    private log: Log;
    private event: EventEmitter;
    private conn: Conn | null = null;
    private httpClient: HttpClient;
    constructor(log: Log) {
        this.log = log;
        this.event = new EventEmitter();
        this.httpClient = new HttpClient(log);
        this.event.on('error', (error, isRelease = true) => {
            this.log.record('there is a error, see error.log');            
            if (isRelease) {
                const dbEvent = new DbEvent(this.conn!, this.log);
                dbEvent.emit('error', error);
            } else {
                if (error instanceof Error) {
                    error = util.format.apply(null, [error]);
                }
                this.log.error(error.message || error, 3);
            }
        })
    }

    public async check(all = false) {
        const time = dayjs().subtract(config.options.checkOrderTaskLongAgo, 'minute').format('YYYY-MM-DD HH:mm:ss');
        this.log.record(`deal order check start,time=${time}`);
        this.conn = await database.getConnection(this.log);
        try {
            let sql = this.conn.format('select ID from ToBeDealOrder where Status = 0 and CreateTime < ?', [time]);
            if (all) {
                sql = this.conn.format('select ID from ToBeDealOrder where Status = 0', []);
            }
            const result = await this.conn.query(sql);
            this.conn.release();
            this.log.record(`deal order check no complete task=${JSON.stringify(result)}`);
            // await this.timeOutOrder(); 启用新的定时来处理超时等情况
            for (let i = 0; i < result.length; i++) {
                await this.deal({ id: result[i]['ID'] })
            }
        } catch (err) {
            this.event.emit('error', err);
        }
    }

    public async checkBmOrder() {
        this.log.record(`deal order checkBmOrder start`);
        this.conn = await database.getConnection(this.log);
        try {
            let sql = this.conn.format('select * from OrderList where Status = 0', []);
            const result = await this.conn.query(sql);
            this.log.record(`deal order checkBmOrder no complete task=${JSON.stringify(result)}`);
            // 去计费系统查询订单状态
            for (let i = 0; i < result.length; i++) {
                try {
                    const bmOrderNumber = result[i]['BmOrderNumber'];
                    const webhookToken = result[i]['WebHookToken'];
                    const params = `OrderNumber=${bmOrderNumber}`;
                    const res = await this.postToBm(params, '/bmserver/queryOrder');
                    if (res.code === 0) {
                        const status : string = res.data.Status;
                        const bmOrderType = res.data.Type
                        const couponNumber = res.data.CouponNumber !== null ? res.data.CouponNumber : '';
                        this.log.record(`deal order checkBmOrder status=${status} bmOrderType=${bmOrderType}`);
                        if (status === "1" || status === "2" || status === "4") {
                            const BmStatus = status === "2" ? "failed" : "success";
                            if (bmOrderType === "13" || bmOrderType === "14" || bmOrderType === "17") {
                                this.log.record(`go into notifyGoOrderCapture`);
                                const finalPrice = (parseFloat(res.data.FinalPrice) - parseFloat(res.data.CouponCount)) / 100;
                                const price = {
                                    before: outputComputedCount(res.data.BeforeOncePrice),
                                    final: parseFloat(finalPrice.toFixed(2))
                                }
                                const creditNum = {
                                    before: outputComputedCount(res.data.CreditCount),
                                    final: outputComputedCount(res.data.CreditCount)
                                }
                                this.notifyGoOrderCapture(res.data.Number, webhookToken, res.data.PayPlatform, res.data.PayPlatEmail, res.data.PayPlatOrder,
                                    price, creditNum, couponNumber, outputComputedCount(res.data.CouponCount), BmStatus)

                            } else {
                                this.log.record(`go into notifyPhpOrderCapture`);
                                const price = (parseFloat(res.data.FinalPrice) - parseFloat(res.data.CouponCount)).toString()
                                this.notifyPhpOrderCapture(res.data.Number, webhookToken, res.data.PayPlatOrder,
                                    res.data.PayPlatEmail, res.data.PayPlatform, price, res.data.BeforeOncePrice,
                                    couponNumber, res.data.CouponCount, BmStatus)
                            }

                            // 计费系统成功，除了补偿机制重新走支付逻辑，还要更新计费系统状态
                            if (status === "4") {
                                this.log.record(`go into notifyBmOrderSuccess`);
                                this.notifyBmOrderSuccess(res.data.Number)
                            }

                            continue;
                        } else if (status === "3") { // 设置为当前订单为超时
                            this.log.record(`go into delete LockOrder`);
                            let delSql = this.conn.format('delete from LockOrder where OrderNumber =?', [result[i]['OrderNumber']]);
                            await this.conn.execute(delSql);
                            this.log.record(`go into set time out update`);
                            let updateSql = this.conn.format('update OrderList set Status=3 where BmOrderNumber=?', [bmOrderNumber]);
                            await this.conn.execute(updateSql);
                        }
                        continue;
                    }
                } catch (err) {
                    this.event.emit('error', err);
                    // 继续处理下一个订单而不是中断整个流程
                    continue;
                }
            }
        } catch (err) {
            this.event.emit('error', err);
        }  finally {
            if (this.conn) {
                this.conn.release();
            }
        }
    }

    private async timeOutOrder() {
        try {
            this.conn = await database.getConnection(this.log);
            await this.conn.beginTransaction();
            this.log.record('deal time out order');
            let sql = this.conn.format('select OrderNumber from OrderList where status=0 and TIMESTAMPDIFF(SECOND,CreateTime,now())>=900', []);
            const data = await this.conn.query(sql);
            const orderNumbers: string[] = [];
            const params: string[] = [];
            data.forEach((item) => {
                orderNumbers.push(item['OrderNumber']);
                params.push('?');
            });

            if (orderNumbers.length !== 0) {
                sql = this.conn.format(`delete from LockOrder where OrderNumber in (${params.join(',')})`, orderNumbers);
                await this.conn.execute(sql);

                sql = this.conn.format('update OrderList set Status=3 where status=0 and TIMESTAMPDIFF(SECOND,CreateTime,now())>=900', []);
                await this.conn.execute(sql);
            }

            this.conn.commit();
            this.conn.release();
        } catch (err) {
            this.event.emit('error', err);
        }
    }

    public async deal(data: { id: number }) {
        this.conn = await database.getConnection(this.log);
        await this.conn.beginTransaction();
        try {
            let sql = this.conn.format('select OrderNumber from ToBeDealOrder where ID=? and Status = 0', [data.id]);
            const toBeDeal = await this.conn.query(sql);
            if (toBeDeal.length === 0) {
                this.event.emit('error', { message: `id=${data.id} is complete or not exits` });
                this.conn.rollback();
                this.conn.release();
                return;
            }

            const orderNumber = toBeDeal[0]['OrderNumber'];

            sql = this.conn.format('select * from OrderList where OrderNumber=? and Status=6', [orderNumber]);
            const orderData = await this.conn.query(sql);
            // 不存在该笔进行中订单
            if (orderData.length === 0) {
                this.event.emit('error', { message: `orderNumber:${orderNumber} is not a valid order id` });
                this.conn.rollback();
                // 状态值置为2
                sql = this.conn.format('update ToBeDealOrder set Status=2 where ID=?', [data.id]);
                this.conn.execute(sql);
                this.conn.release();
                return;
            }

            this.log.record(`deal order task=${JSON.stringify(orderData)}`);
            const orderId = orderData[0]['ID'];
            //处理不同的订单类型
            const mixType = orderData[0]['MixType'];
            const mixOrderArr = getBitPositions(mixType)
            let hasExpired = true;

            //包含自动续费的独立处理
            if (mixOrderArr.includes(orderType.autoRenew)) {
                this.log.record(`deal diff type ,type=${orderType.autoRenew},mixType=${mixType}`);
                // rentManger的自动扣费和普通rent续费相同处理方式即可
                if (mixOrderArr.includes(orderType.rentManagerRenew)) {
                    await this.rentManagerMonthly(orderId, orderData[0]['Months']);
                } else {
                    await this.autoRenew(orderId, mixOrderArr);
                }
                await this.notify(orderId, orderType.autoRenew, {
                    hasExpired
                });
            } else {
                for (const type of mixOrderArr) {
                    const index = mixOrderArr.indexOf(type);
                    this.log.record(`deal diff type ,type=${type},mixType=${mixType}`);
                    // 处理不同类型的子订单
                    switch (type) {
                        case orderType.active:
                            await this.activeUser(orderId);
                            break;
                        case orderType.renewByMonth:
                            await this.renewUser(orderId);
                            break;
                        case orderType.buyApp:
                            await this.buyApp(orderId);
                            break;
                        case orderType.renewLandline:
                            await this.renewLandline(orderId);
                            break;
                        case orderType.featureOnce:
                            await this.communityOnce(orderData[0]['InstallID']);
                            break;
                        case orderType.featureMonthly:
                            const result = await this.communityMonthly(orderData[0]['InstallID'], orderData[0]['Months']);
                            if (result !== false) {
                                [, hasExpired] = result;
                            }
                            break;
                        case orderType.featureChange:
                            await this.communityChange(orderData[0]['InstallID'], orderId);
                            break;
                        case orderType.renewByDay:
                            await this.renewUserByDay(orderId);
                            break;
                        case orderType.rentManagerRenew:
                            await this.rentManagerMonthly(orderId, orderData[0]['Months']);
                            break;
                        case orderType.singleVideoStorage:
                            await this.renewSingleVideoStorage(orderId);
                            break;
                        case orderType.communityVideoStorage:
                            await this.renewCommunityVideoStorage(orderId);
                            break;
                        case orderType.singleThirdLockActive:
                        case orderType.communityThirdLockActive:
                            await this.activeThirdLock(orderId);
                            break;
                        case orderType.singleThirdLockRenew:
                        case orderType.communityThirdLockRenew:
                            await this.renewThirdLock(orderId);
                            break;
                        default:
                            this.event.emit('error', {message: `error order type ${type}`});
                            this.conn.rollback();
                            this.conn.release();
                            continue;
                    }


                    await this.notify(orderId, type, {
                        hasExpired
                    });

                }
            }

            this.log.record('after deal task');
            sql = this.conn.format('update OrderList set Status = 1 where ID=?', [orderId]);
            await this.conn.execute(sql);
            sql = this.conn.format('update ToBeDealOrder set Status = 1 where ID=?', [data.id]);
            await this.conn.execute(sql);
            this.log.record('after update task and order list');


            this.log.record('after notify cloud adapt');

            this.conn.commit();
            this.conn.release();
            this.log.record('commit and release');

            //发送锁激活、续费后的pin card，需要事务提交后下发，防止其他数据库连接获取不到更新后的数据被拦截
            if (mixOrderArr.includes(orderType.singleThirdLockActive) || mixOrderArr.includes(orderType.communityThirdLockActive)
                || mixOrderArr.includes(orderType.singleThirdLockRenew) || mixOrderArr.includes(orderType.communityThirdLockRenew)) {
                this.notifySmartLock(orderId)
                this.log.record('after notify cloud smartLock');
            }

            this.log.record('deal order end');
        } catch (err: any) {
            this.conn.rollback();
            this.conn.release();
            console.log(err);
            this.event.emit('error', err, 3);
        }
    }

    public sliceArr(arr: object[], size: number) {
        var newArr = [];
        for (var i = 0; i < Math.ceil(arr.length / size); i++) {
          var start = i * size;
          var end = start + size;
          newArr.push(arr.slice(start, end));
        }
        return newArr;
    }
  
    public checkIsSmartHomeCommunity(communitySwitch: number) {
        if ((communitySwitch & 16) === 16) {
            return true;
        } else {
            return false;
        }
    }

    private async notifySmartHome(userArr: object[]) {
        if (userArr.length === 0) {
            return true;
        } else if (userArr.length <= 500) {
            const requestParams: RequestParams = {
                timestamp: dayjs().valueOf(),
                trace_id: this.log.getTraceId(),
                data: {
                    notice: 'account_activated_batch',
                    timestamp: dayjs().valueOf(),
                    id: this.log.getTraceId(),
                    param: userArr}
                };
            this.httpClient.post(NoticeUrl, JSON.stringify(requestParams));
        } else {
            const newUserArr = this.sliceArr(userArr, 500);
            for(var i = 0; i < newUserArr.length; i++) {
                const requestParams: RequestParams = {
                    timestamp: dayjs().valueOf(),
                    trace_id: this.log.getTraceId(),
                    data: {
                        notice: 'account_activated_batch',
                        timestamp: dayjs().valueOf(),
                        id: this.log.getTraceId(),
                        param: newUserArr[i]}
                    };
                this.httpClient.post(NoticeUrl, JSON.stringify(requestParams));
            }
        }
        return true;
    }

    private async renewLandlineItem(id: number, now: string, month: number) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return [false, null];
        }
        let sql = this.conn.format('select PhoneExpireTime,UUID,EnableSmartHome,Role from PersonalAccount where ID=?', [id]);
        const userData = await this.conn.query(sql);

        // 某个用户不存在可继续执行，不需要归还数据库连接
        if (userData.length === 0) {
            this.event.emit('error', {message: `user (id=${id}) not exits`}, false);
            return [false, null];
        }

        let expireTime = dayjs(userData[0]['PhoneExpireTime']).format('YYYY-MM-DD HH:mm:ss');
        expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
        const nextExpireTime = computedLastDate(expireTime, month);

        let isNewBilling = 0;
        if (userData[0]['Role'] === SingleMainUser) {
            sql = this.conn.format('select IsNewBilling from PersonalAccountSingleInfo where PersonalAccountUUID=?', [userData[0]['UUID']]);
            const billData = await this.conn.query(sql);
            isNewBilling = billData[0]['IsNewBilling'];
        }

        // 查询是否新收费方案
        if (isNewBilling === 0) {
            sql = this.conn.format(
                'update PersonalAccount set PhoneExpireTime=?,PayStatus=2 where ID=?',
                [nextExpireTime, id]
            );
        } else {
            sql = this.conn.format(
                'update PersonalAccount set PhoneExpireTime=?,ExpireTime=?,PayStatus=2 where ID=?',
                [nextExpireTime, nextExpireTime, id]
            );
        }

        await this.conn.execute(sql);

        // 更新首次支付时间
        sql = this.conn.format(
            'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
            [now, id]
        );
        await this.conn.execute(sql);

        return [userData, nextExpireTime];
    }

    private async renewLandline(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into renewLandline');
        let sql = this.conn.format('select Months from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const month = orderData[0]['Months'];

        sql = this.conn.format('select AppID,Type from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        const userArr: Object[] = [];
        for (let i = 0; i < endList.length; i++) {
            const subType = endList[i]['Type'];
            // 5-落地费
            if (subType !== 5) {
                this.log.record(`subType=${subType} dont deal,continue`);
                continue;
            }

            const id = endList[i]['AppID'];

            const [userData, nextExpireTime] = await this.renewLandlineItem(id, now, month);

            if (userData === false) {
                continue;
            }

            // @ts-ignore
            if (userData[0]['EnableSmartHome'] === 1) {
                const userObj = {
                    'account_type': 'single_senior',
                    'activate_type': 'continue',
                    'activate_timestamp': 0,
                    // @ts-ignore
                    'home_id': userData[0]['UUID'],
                    // @ts-ignore
                    'user_id': userData[0]['UUID'],
                    // @ts-ignore
                    // @ts-ignore
                    // @ts-ignore
                    // @ts-ignore
                    'expired_timestamp': dayjs(nextExpireTime).valueOf()
                }
                userArr.push(userObj);
            }
        }
        this.notifySmartHome(userArr);
        return true;
    }

    private async renewUser(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into renewUser');
        let sql = this.conn.format('select Months from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const month = orderData[0]['Months'];

        sql = this.conn.format('select AppID,Type from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        // 社区都为按天续费
        for (let i = 0; i < endList.length; i++) {
            const subType = endList[i]['Type'];
            // 2:家庭续费(套餐内资费) 4:家庭续费额外app费用 10:pm app续费
            if (subType !== 2 && subType !== 4 && subType !== 10) {
                this.log.record(`subType=${subType} dont deal,continue`);
                continue;
            }

            const id = endList[i]['AppID'];
            sql = this.conn.format('select ExpireTime,Role,UUID,EnableSmartHome from PersonalAccount where ID=?', [id]);
            const userData = await this.conn.query(sql);

            // 某个用户不存在可继续执行，不需要归还数据库连接
            if (userData.length === 0) {
                this.event.emit('error', { message: `user (id=${id}) not exits` }, false);
                continue;
            }

            const role = userData[0]['Role'];
            let expireTime = dayjs(userData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');;
            console.log(expireTime);
            expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
            const nextExpireTime = computedLastDate(expireTime, month);

            // 单住户主账号续费通知不考虑，应为renewLandline
            if ([SingleMainUser, MultipleMainUser].includes(role)) {
                // 修改从账户和主账户的过期时间
                sql = this.conn.format(
                    'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=? or (ParentID=? and Role in (11, 21))',
                    [nextExpireTime, id, id]
                );
                await this.conn.execute(sql);
                this.log.record('renewUser：Role' + role + ',newExpireTime:' + nextExpireTime);             
                // 更新首次支付时间
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where (ID=? or (ParentID=? and Role in (11, 21))) and FirstPayTime is null',
                    [now, id, id]
                );
                await this.conn.execute(sql);
            } else if (OfficeUsers.includes(role) || role === PmAppUser) {
                sql = this.conn.format(
                    'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=?',
                    [nextExpireTime, id]
                );
                await this.conn.execute(sql);
                this.log.record('renewUser：Role' + role + ',newExpireTime' + nextExpireTime);
                // 更新首次支付时间
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                    [now, id]
                );
                await this.conn.execute(sql);
            } else {
                // 抛出错误，其他继续执行
                this.event.emit('error', { message: `error user data: id=${id}, role=${role}, userData=${JSON.stringify(userData)}` }, false);
            }

        }

        return true;
    }

    public async renewUserByDayItem(id: number, nextTime: Date, nextExpireTime: string) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return [false, null];
        }
        let sql = this.conn.format('select ExpireTime,Role,UUID,ParentID from PersonalAccount where ID=?', [id]);
        const userData = await this.conn.query(sql);

        // 某个用户不存在可继续执行，不需要归还数据库连接
        if (userData.length === 0) {
            this.event.emit('error', {message: `user (id=${id}) not exits`}, false);
            return [false, null];
        }

        const role = userData[0]['Role'];
        const expireTime = userData[0]['ExpireTime'];
        if (nextTime.getTime() < expireTime.getTime()) {
            this.event.emit('error', {message: `user (id=${id}) expireTime=${expireTime},but nextTime=${nextTime}`}, false);
            return [false, null];
        }

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        // 只有社区和办公按天续费
        if ([SingleMainUser, MultipleMainUser].includes(role)) {
            // 修改从账户和主账户的过期时间
            sql = this.conn.format(
                'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=? or (ParentID=? and Role in (11, 21))',
                [nextExpireTime, id, id]
            );
            await this.conn.execute(sql);
            this.log.record('renewUserByDay：Role' + role + ',newExpireTime:' + nextExpireTime);
            // 更新首次支付时间
            sql = this.conn.format(
                'update PersonalAccount set FirstPayTime=? where (ID=? or (ParentID=? and Role in (11, 21))) and FirstPayTime is null',
                [now, id, id]
            );
            await this.conn.execute(sql);
            sql = this.conn.format('select Switch from CommunityInfo where AccountID=?', [userData[0]['ParentID']]);
            const communityData = await this.conn.query(sql);
            return [true, communityData];
        } else if (OfficeUsers.includes(role) || role === PmAppUser) {
            sql = this.conn.format(
                'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=?',
                [nextExpireTime, id]
            );
            await this.conn.execute(sql);
            this.log.record('renewUserByDay：Role' + role + ',newExpireTime' + nextExpireTime);
            // 更新首次支付时间
            sql = this.conn.format(
                'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                [now, id]
            );
            await this.conn.execute(sql);
        } else {
            // 抛出错误，其他继续执行
            this.event.emit('error', {message: `error renewUserByDay user data: id=${id}, role=${role}, userData=${JSON.stringify(userData)}`}, false);
        }
        return [true, null];
    }

    public async renewUserByDay(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return false;
        }
        this.log.record('go into renewUserByDay');
        let sql = this.conn.format('select NextTime from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const nextTime = orderData[0]['NextTime'];
        const nextExpireTime = dayjs(nextTime).format('YYYY-MM-DD 23:59:59');

        sql = this.conn.format('select AppID,Type from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);
        const userArr: Object[] = [];
        for (let i = 0; i < endList.length; i++) {
            const subType= endList[i]['Type'];
            // 8 按日期续费 10=pm app续费
            if (subType !== 8 && subType !== 10) {
                this.log.record(`subType=${subType} dont deal,continue`);
                continue;
            }

            const id = endList[i]['AppID'];

            const [flag, userData] = await this.renewUserByDayItem(id, nextTime, nextExpireTime);
            if (!flag) {
                continue;
            }
            if (userData !== null) {
                const userObj = {
                    'account_type': 'account',
                    'activate_type': 'continue',
                    'activate_timestamp': 0,
                    // @ts-ignore
                    'home_id': userData[0]['UUID'],
                    // @ts-ignore
                    'user_id': userData[0]['UUID'],
                    'expired_timestamp': dayjs(nextExpireTime).valueOf()
                }
                userArr.push(userObj);
            }
        }
        this.notifySmartHome(userArr);
    }

    // 单住户视频存储
    public async renewSingleVideoStorage(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return false;
        }
        this.log.record('go into single videoStorage');
        let sql = this.conn.format('select Months from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const month = orderData[0]['Months'];

        sql = this.conn.format('SELECT SiteUUID,Type FROM OrderEndUserList WHERE OrderID =?', [orderId]);
        const endList = await this.conn.query(sql);

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        for (let i = 0; i < endList.length; i++) {
            const subType = endList[i]['Type'];
            //11=单住户视频存储付费
            if (subType !== 11) {
                this.log.record(`subType=${subType} dont deal,continue`);
                continue;
            }

            const siteUUID = endList[i]['SiteUUID'];
            const res = this.renewSingleVideoStorageItem(siteUUID, now, month);
            if (!res) {
                continue;
            }
        }
        return true;
    }

    public async renewSingleVideoStorageItem(siteUUID: string, now: string, month: number) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return false;
        }
        let sql = this.conn.format('SELECT UUID,ExpireTime,ProjectType FROM VideoStorage WHERE PersonalAccountUUID = ? AND ProjectType = 1', [siteUUID]);
        const userData = await this.conn.query(sql);

        // 某个用户不存在可继续执行，不需要归还数据库连接
        if (userData.length === 0) {
            this.event.emit('error', {message: `site (uuid=${siteUUID}) not exits`}, false);
            return false;
        }

        let expireTime = dayjs(userData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
        expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
        const nextExpireTime = computedLastDate(expireTime, month);
        sql = this.conn.format('update VideoStorage set ExpireTime=?,IsPaid=1 where UUID=?', [nextExpireTime, userData[0]['UUID']]);
        await this.conn.execute(sql);
        this.log.record('renew single VideoStorage' + siteUUID + ',newExpireTime' + nextExpireTime);
    }

    public async renewCommunityVideoStorageItem(siteUUID: string, nextTime: Date, nextExpireTime: string) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return false;
        }

        let sql = this.conn.format('SELECT UUID,ExpireTime,ProjectType FROM VideoStorage WHERE AccountUUID = ? AND ProjectType = 2', [siteUUID]);
        const userData = await this.conn.query(sql);

        // 某个用户不存在可继续执行，不需要归还数据库连接
        if (userData.length === 0) {
            this.event.emit('error', {message: `site (uuid=${siteUUID}) not exits`}, false);
            return false;
        }


        const expireTime = userData[0]['ExpireTime'];
        if (nextTime.getTime() < expireTime.getTime()) {
            this.event.emit('error', {message: `site (uuid=${siteUUID}) expireTime=${expireTime},but nextTime=${nextTime}`}, false);
            return false;
        }

        sql = this.conn.format(
            'update VideoStorage set ExpireTime=?,IsPaid=1 where UUID=?',
            [nextExpireTime, userData[0]['UUID']]
        );
        await this.conn.execute(sql);
        this.log.record('renew community VideoStorage' + siteUUID + ',newExpireTime' + nextExpireTime);
    }

    // 社区视频存储
    public async renewCommunityVideoStorage(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', {message: 'conn object is null'});
            return false;
        }
        this.log.record('go into community videoStorage');
        let sql = this.conn.format('select NextTime from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const nextTime = orderData[0]['NextTime'];
        const nextExpireTime = dayjs(nextTime).format('YYYY-MM-DD 23:59:59');

        sql = this.conn.format('SELECT SiteUUID,Type FROM OrderEndUserList WHERE OrderID =?', [orderId]);
        const endList = await this.conn.query(sql);
        for (let i = 0; i < endList.length; i++) {
            const subType= endList[i]['Type'];
            // 12=社区视频存储续费
            if (subType !== 12) {
                this.log.record(`subType=${subType} dont deal,continue`);
                continue;
            }

            const siteUUID = endList[i]['SiteUUID'];
            const res = this.renewCommunityVideoStorageItem(siteUUID, nextTime, nextExpireTime);
            if (!res) {
                continue;
            }
        }
    }

    private async activeUser(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into activeUser');
        let sql = this.conn.format('select * from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);
        
        const userArr: Object[] = [];
        for (let i = 0; i < endList.length; i++) {
            const item = endList[i];
            const id = item['AppID'];

            sql = this.conn.format('select Role,ParentID,UUID,ParentUUID,EnableSmartHome from PersonalAccount where ID=?', id);
            const userData = await this.conn.query(sql);
            const role = userData[0]['Role'];
            // 收费信息
            const chargeData = JSON.parse(item['ChargeData']);
            const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
            const now_timestamp = dayjs().valueOf();
            let expired_timestamp = 0;

            if (role === SingleMainUser) {
                // 查询是否新收费方案
                sql = this.conn.format('select IsNewBilling from PersonalAccountSingleInfo where PersonalAccountUUID=?', [userData[0]['UUID']]);
                const billData = await this.conn.query(sql);

                if (billData[0]['IsNewBilling'] === 0) {
                    sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?', [now, MaxExpireTime, id]);
                    await this.conn.execute(sql);
                    // 更新首次支付时间
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                    sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ParentID=? and Role = 11', [now, MaxExpireTime, id]);
                    await this.conn.execute(sql);
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ParentID=? and Role = 11 and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                    if (chargeData['MonthlyFee'] === 0) {
                        //7.1.4 月费为0的一次性激活打上标记
                        sql = this.conn.format('update PersonalAccount set PhoneExpireTime=?,PayStatus=1 where ID=?', [MaxExpireTime, id]);
                        await this.conn.execute(sql);
                        sql = this.conn.format('update PersonalAccount set PhoneExpireTime=? where ParentID=? and Role = 11', [MaxExpireTime, id]);
                        await this.conn.execute(sql);
                        expired_timestamp = 0;
                    } else {
                        sql = this.conn.format('update PersonalAccount set PayStatus=2 where ID=?', [id]);
                        await this.conn.execute(sql);
                        expired_timestamp = now_timestamp;
                    }
                } else {
                    // 新收费方案 创建激活/0激活费创建后，免费用1个月
                    const expireTime = computedActiveExpireTime(chargeData);
                    sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=?,PhoneExpireTime=? where ID=?', [now, expireTime, expireTime, id]);
                    await this.conn.execute(sql);
                    // 更新首次支付时间
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                    sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=?,PhoneExpireTime=? where ParentID=? and Role = 11', [now, expireTime, expireTime, id]);
                    await this.conn.execute(sql);
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ParentID=? and Role = 11 and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                    if (chargeData['MonthlyFee'] === 0) {
                        //7.1.4 月费为0的一次性激活打上标记
                        sql = this.conn.format('update PersonalAccount set PayStatus=1 where ID=?', [id]);
                        await this.conn.execute(sql);
                        expired_timestamp = 0;
                    } else {
                        sql = this.conn.format('update PersonalAccount set PayStatus=2 where ID=?', [id]);
                        await this.conn.execute(sql);
                        expired_timestamp = dayjs(expireTime).valueOf();
                    }
                }

                if (userData[0]['EnableSmartHome'] === 1) {
                    const userObj = {
                        'account_type': 'single_senior',
                        'activate_type': 'activate',
                        'activate_timestamp': now_timestamp,
                        'home_id': userData[0]['UUID'],
                        'user_id': userData[0]['UUID'],
                        'expired_timestamp': expired_timestamp
                    }
                    userArr.push(userObj);
                }
            } else if (role === MultipleMainUser) {
                const expireTime = computedActiveExpireTime(chargeData);

                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?', [now, expireTime, id]);
                await this.conn.execute(sql);
                // 更新首次支付时间
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                    [now, id]
                );
                await this.conn.execute(sql);
                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ParentID=? and Role = 21', [now, expireTime, id]);
                await this.conn.execute(sql);
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where ParentID=? and Role = 21 and FirstPayTime is null',
                    [now, id]
                );
                await this.conn.execute(sql);
                if (chargeData['MonthlyFee'] === 0) {
                    //7.1.4 月费为0的一次性激活打上标记
                    sql = this.conn.format('update PersonalAccount set PayStatus=1 where ID=?', [id]);
                    await this.conn.execute(sql);
                    expired_timestamp = 0;
                } else {
                    sql = this.conn.format('update PersonalAccount set PayStatus=2 where ID=?', [id]);
                    await this.conn.execute(sql);
                    expired_timestamp = dayjs(expireTime).valueOf();
                }

                sql = this.conn.format('select CreateTime from Account where ID=?', [userData[0]['ParentID']]);
                const communityTime = (await this.conn.query(sql))[0]['CreateTime'];
                if (!communityTime) {
                    sql = this.conn.format('update Account set CreateTime = ? where ID=?', [now, userData[0]['ParentID']]);
                    await this.conn.execute(sql);
                }
                sql = this.conn.format('select Switch from CommunityInfo where AccountID=?', [userData[0]['ParentID']]);
                const communityData = await this.conn.query(sql);
                if (this.checkIsSmartHomeCommunity(communityData[0]['Switch'])) {
                    const userObj = {
                        'account_type': 'account',
                        'activate_type': 'activate',
                        'activate_timestamp': now_timestamp,
                        'home_id': userData[0]['UUID'],
                        'user_id': userData[0]['UUID'],
                        'expired_timestamp': expired_timestamp
                    }
                    userArr.push(userObj);
                }
            } else if (OfficeUsers.includes(role)) {
                const expireTime = computedActiveExpireTime(chargeData);
                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?', [now, expireTime, id]);
                await this.conn.execute(sql);
                // 更新首次支付时间
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                    [now, id]
                );
                await this.conn.execute(sql);
                if (chargeData['MonthlyFee'] === 0) {
                    //7.1.4 月费为0的一次性激活打上标记
                    sql = this.conn.format('update PersonalAccount set PayStatus=1 where ID=?', [id]);
                    await this.conn.execute(sql);
                } else {
                    sql = this.conn.format('update PersonalAccount set PayStatus=2 where ID=?', [id]);
                    await this.conn.execute(sql);
                }

                sql = this.conn.format('select CreateTime from Account where ID=?', [userData[0]['ParentID']]);
                const officeTime = (await this.conn.query(sql))[0]['CreateTime'];
                if (!officeTime) {
                    sql = this.conn.format('update Account set CreateTime = ? where ID=?', [now, userData[0]['ParentID']]);
                    await this.conn.execute(sql);
                }

            } else if (role === PmAppUser) {
                // V6.4 增加PMApp
                const expireTime = computedActiveExpireTime(chargeData);
                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?', [now, expireTime, id]);
                await this.conn.execute(sql);
                // 更新首次支付时间
                sql = this.conn.format(
                    'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                    [now, id]
                );
                await this.conn.execute(sql);
                if (chargeData['MonthlyFee'] === 0) {
                    //7.1.4 月费为0的一次性激活打上标记
                    sql = this.conn.format('update PersonalAccount set PayStatus=1 where ID=?', [id]);
                    await this.conn.execute(sql);
                } else {
                    sql = this.conn.format('update PersonalAccount set PayStatus=2 where ID=?', [id]);
                    await this.conn.execute(sql);
                }
            } else if (role === SingleSubUser) {
                // 从账户
                sql = this.conn.format('select A.PhoneExpireTime,A.ExpireTime,A.EnableSmartHome,A.Account from PersonalAccount A join PersonalAccount B on A.ID = B.ParentID where B.ID=?', [id]);
                const mainData = (await this.conn.query(sql))[0];
                const expireTime = mainData.PhoneExpireTime;
                const appExpireTime = mainData.ExpireTime;
                // 从账号落地永不过期
                if (dayjs(expireTime).format('YYYY-MM-DD HH:mm:ss') === MaxExpireTime) {
                    expired_timestamp = 0;
                } else {
                    expired_timestamp = dayjs(expireTime).valueOf();
                }
                // 无论新旧的单住户收费方案，从账号的过期时间都是跟随主账号
                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,PhoneExpireTime=?,ExpireTime=? where ID=?', [now, expireTime, appExpireTime, id]);
                await this.conn.execute(sql);

                sql = this.conn.format('update PersonalAccountCnf set FreeAppCount=FreeAppCount+1 where Account=?', [mainData['Account']]);
                await this.conn.execute(sql);

                if (mainData.EnableSmartHome === 1) {
                    const userObj = {
                        'account_type': 'single_senior',
                        'activate_type': 'activate',
                        'activate_timestamp': now_timestamp,
                        'home_id': userData[0]['ParentUUID'],
                        'user_id': userData[0]['UUID'],
                        'expired_timestamp': expired_timestamp
                    }
                    userArr.push(userObj);
                }
            } else if (role === MultipleSubUser) {
                // 从账户
                sql = this.conn.format('select A.ExpireTime,A.ParentID,A.Account from PersonalAccount A join PersonalAccount B on A.ID = B.ParentID where B.ID=?', [id]);
                const mainData = (await this.conn.query(sql))[0];
                const expireTime = mainData.ExpireTime;
                // 从账号永不过期
                if (dayjs(expireTime).format('YYYY-MM-DD HH:mm:ss') === MaxExpireTime) {
                    expired_timestamp = 0;
                } else {
                    expired_timestamp = dayjs(expireTime).valueOf();
                }
                sql = this.conn.format('update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?', [now, expireTime, id]);
                await this.conn.execute(sql);

                sql = this.conn.format('update PersonalAccountCnf set FreeAppCount=FreeAppCount+1 where Account=?', [mainData['Account']]);
                await this.conn.execute(sql);

                sql = this.conn.format('select Switch from CommunityInfo where AccountID=?', [mainData.ParentID]);
                const communityData = await this.conn.query(sql);
                if (this.checkIsSmartHomeCommunity(communityData[0]['Switch'])) {
                    const userObj = {
                        'account_type': 'account',
                        'activate_type': 'activate',
                        'activate_timestamp': now_timestamp,
                        'home_id': userData[0]['ParentUUID'],
                        'user_id': userData[0]['UUID'],
                        'expired_timestamp': expired_timestamp
                    }
                    userArr.push(userObj);
                }
            } else {
                // 抛出错误，其他继续执行
                this.event.emit('error', { message: `error user data: id=${id}, role=${role}, userData=${JSON.stringify(userData)}` }, false);
            }
        }
        this.notifySmartHome(userArr);
        return true;
    }

    private async buyApp(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into buyApp');
        let account_type = 'account';
        let expired_timestamp = 0;
        let enableSmartHome = true;
        let sql = this.conn.format('select * from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);
        const id = endList[0]['AppID'];

        sql = this.conn.format('select ParentID,Role,UUID,ParentUUID from PersonalAccount where ID=?', [id]);
        const userData = await this.conn.query(sql);

        sql = this.conn.format(
            'select P.Account,P.ExpireTime,Pf.ID,Pf.FreeAppCount,P.PhoneExpireTime,P.ParentID,P.EnableSmartHome from PersonalAccount P join PersonalAccountCnf Pf on P.Account = Pf.Account where P.ID=?',
            userData[0]['ParentID']
        );
        const mainData = await this.conn.query(sql);

        sql = this.conn.format(
            'update PersonalAccountCnf set FreeAppCount=? where ID=?',
            [Number(mainData[0]['FreeAppCount']) + 1, mainData[0]['ID']]
        );
        await this.conn.execute(sql);

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        if (userData[0]['Role'] === SingleSubUser) {
            sql = this.conn.format(
                'update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=?,PhoneExpireTime=? where ID=?',
                [now, dayjs(mainData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss'), dayjs(mainData[0]['PhoneExpireTime']).format('YYYY-MM-DD HH:mm:ss'), id]
            );
        } else {
            sql = this.conn.format(
                'update PersonalAccount set Active=1,ActiveTime=?,ExpireTime=? where ID=?',
                [now, dayjs(mainData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss'), id]
            );
        }
        await this.conn.execute(sql);
        if (userData[0]['Role'] === SingleSubUser) {
            account_type = 'single_senior';
            if (dayjs(mainData[0]['PhoneExpireTime']).format('YYYY-MM-DD HH:mm:ss') === MaxExpireTime) {
                expired_timestamp = 0;
            } else {
                expired_timestamp = dayjs(mainData[0]['PhoneExpireTime']).valueOf();
            }
            enableSmartHome = mainData[0]['EnableSmartHome'] === 1 ? true:false;
        } else if (userData[0]['Role'] === MultipleSubUser) {
            account_type = 'account';
            if (dayjs(mainData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss') === MaxExpireTime) {
                expired_timestamp = 0;
            } else {
                expired_timestamp = dayjs(mainData[0]['ExpireTime']).valueOf();
            }
            sql = this.conn.format('select Switch from CommunityInfo where AccountID=?', [mainData[0]['ParentID']]);
            const communityData = await this.conn.query(sql);
            enableSmartHome = this.checkIsSmartHomeCommunity(communityData[0]['Switch'])
        }
        if (enableSmartHome) {
            const userArr = [{
                'account_type': account_type,
                'activate_type': 'activate',
                'activate_timestamp': dayjs(now).valueOf(),
                'home_id': userData[0]['ParentUUID'],
                'user_id': userData[0]['UUID'],
                'expired_timestamp': expired_timestamp
            }];
            this.notifySmartHome(userArr);
        }
        return true;
    }

    private async communityOnce(communityId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        let sql = this.conn.format('select Grade,UUID from Account where ID=?', [communityId]);
        const projectData = await this.conn.execute(sql);
        this.log.record('go into ProjectOnce Grade:' + projectData[0]['Grade']);
        if (projectData[0]['Grade'] === 21) {
            sql = this.conn.format('update CommunityInfo set FeatureExpireTime=? where AccountID=?', [MaxExpireTime, communityId]);
        } else if (projectData[0]['Grade'] === 23) {
            sql = this.conn.format('update OfficeInfo set FeatureExpireTime=? where AccountUUID=?', [MaxExpireTime, projectData[0]['UUID']]);
        }
        await this.conn.execute(sql);

        // 新办公高级功能激活、续费发送kafka
        if (projectData[0]['Grade'] === 23) {
            sql = this.conn.format('select IsNew from OfficeInfo where AccountUUID=? for update', [projectData[0]['UUID']]);
            const infoData = await this.conn.query(sql);
            if (infoData[0]['IsNew'] === 1) {
                const phpAdaptNotify = new PhpAdaptNotify(this.log);
                const params: Object = {
                    projectUUID: projectData[0]['UUID'],
                };
                phpAdaptNotify.renewFeaturePlan(JSON.stringify(params));
            }
        }
        return true;
    }

    private async rentManagerMonthly(orderId: number, month: number | string) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }

        let sql = this.conn.format('select RentManagerCustomerUUID from RentManagerOrderList where OrderID=?', [orderId]);
        const rentManagerCustomerUUIDList = await this.conn.execute(sql);

        const customerUuidList: string[] = [];
        const params: string[] = [];
        rentManagerCustomerUUIDList.forEach((item) => {
            customerUuidList.push(item['RentManagerCustomerUUID']);
            params.push('?');
        });

        if (customerUuidList.length !== 0) {
            sql = this.conn.format(`select ID,ExpiredTime from RentManagerCustomer where UUID in (${params.join(',')}) for update`, customerUuidList);
            const rentManagerCustomerData = await this.conn.execute(sql);

            let needSendEmail = false;

            for(let i : number = 0; i< rentManagerCustomerData.length; i++) {
                let expireTime = rentManagerCustomerData[i]['ExpiredTime'];
                let customerId = rentManagerCustomerData[i]['ID'];
                const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
                // 如果之前没有续费过（expireTime == null） 或者已经过了之前续费续费的时间，重新开始续费的（new Date(expireTime).getTime() < new Date().getTime(), 这两种情况都需要发送邮件给运营人员
                if (expireTime == null) {
                    expireTime = now;
                    needSendEmail = true;
                } else {
                    expireTime = dayjs(expireTime).format('YYYY-MM-DD HH:mm:ss');
                    // 如果已经过了之前的过期时间，则过期时间以当前时间开始计算
                    if (new Date(expireTime).getTime() < new Date().getTime()) {
                        expireTime = now;
                        needSendEmail = true;
                    }
                }

                let nextExpireTime = computedLastDate(expireTime, month);
                nextExpireTime = dayjs(nextExpireTime).format('YYYY-MM-DD 23:59:59');
                this.log.record('rentManagerMonthly newExpireTime:' + nextExpireTime);
                sql = this.conn.format('update RentManagerCustomer set ExpiredTime=? where ID=?', [nextExpireTime, customerId]);
                await this.conn.execute(sql);
            }

            this.log.record('Is need sent email to amin:' + needSendEmail);

            if (needSendEmail) {
                // 获取相关信息发送邮件
                sql = this.conn.format(
                    `select RC.RentManagerCompanyName as companyName,
                            RC.RentManagerCompanyCode as companyCode,
                            CONCAT(PI.FirstName, ' ', PI.LastName) as pmName,
                            AUI.LoginAccount          as pmEmail
                     from RentManagerOrderList RO
                              left join RentManagerCustomer RC on RC.UUID = RO.RentManagerCustomerUUID
                              left join Account AC on AC.UUID = RC.PmUUID
                              left join PropertyInfo PI on PI.AccountID = AC.ID
                              left join AccountMap AM on AM.AccountUUID = AC.UUID
                              left join AccountUserInfo AUI on AUI.UUID = AM.UserInfoUUID
                     where RO.OrderID=?`,
                    [orderId]
                );

                const rentManagerCustomerInfoList = await this.conn.query(sql);
                //获取Super填的RentManager运营人员的邮箱地址; 因为后台那边不支持一次性直接传所有的邮箱地址，所以需要循环发送;最多有五个邮箱地址
                sql = this.conn.format(
                    `select cs.EmailForRentManager
                     from CustomerService cs
                              left join Account ac on ac.Account = cs.MngAccount
                     where ac.Grade = 1`,
                    []
                );
                const rentManagerAdminEmail =  await this.conn.query(sql);
                const rentManagerAdminEmailList: string[] = rentManagerAdminEmail[0]['EmailForRentManager'].split(',');
                this.log.record('rentManagerAdminEmailList:' + JSON.stringify(rentManagerAdminEmailList));
                const phpAdaptNotify = new PhpAdaptNotify(this.log);
                for(let i : number = 0; i< rentManagerAdminEmailList.length; i++) {
                    let senEmailParams: Object = {
                        email: rentManagerAdminEmailList[i],
                        list: JSON.stringify(rentManagerCustomerInfoList),
                    };

                    await phpAdaptNotify.renewRentManagerEmail(JSON.stringify(senEmailParams));
                }
            }
        }
    }

    private async communityMonthly(communityId: number, month: number | string) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        let sql = this.conn.format('select Grade,UUID from Account where ID=?', [communityId]);
        const projectData = await this.conn.execute(sql);
        this.log.record('go into ProjectMonthly Grade:' + projectData[0]['Grade']);
        if (projectData[0]['Grade'] === 21) {
            sql = this.conn.format('select FeatureExpireTime from CommunityInfo where AccountID=? for update', [communityId]);
        } else if (projectData[0]['Grade'] === 23) {
            sql = this.conn.format('select FeatureExpireTime,IsNew from OfficeInfo where AccountUUID=? for update', [projectData[0]['UUID']]);
        }
        const infoData = await this.conn.query(sql);
        let expireTime = infoData[0]['FeatureExpireTime'];
        this.log.record('getExpireTime:' + expireTime);
        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        let hasExpired = false;
        if (expireTime == null) {
            expireTime = now;
            hasExpired = true;
        } else {
            expireTime = dayjs(expireTime).format('YYYY-MM-DD HH:mm:ss');
            if (new Date(expireTime).getTime() < new Date().getTime()) {
                expireTime = now;
                hasExpired = true;
            }
        }
        const nextExpireTime = computedLastDate(expireTime, month);
        if (projectData[0]['Grade'] === 21) {
            sql = this.conn.format('update CommunityInfo set FeatureExpireTime=? where AccountID=?', [nextExpireTime, communityId]);
        } else if (projectData[0]['Grade'] === 23) {
            sql = this.conn.format('update OfficeInfo set FeatureExpireTime=? where AccountUUID=?', [nextExpireTime, projectData[0]['UUID']]);
        }
        await this.conn.execute(sql);

        // 新办公高级功能续费发送kafka
        if (projectData[0]['Grade'] === 23 && infoData[0]['IsNew'] === 1) {
            const phpAdaptNotify = new PhpAdaptNotify(this.log);
            const params: Object = {
                projectUUID: projectData[0]['UUID'],
            };
            phpAdaptNotify.renewFeaturePlan(JSON.stringify(params));
        }

        return [true, hasExpired];
    }

    private async communityChange(communityId: number, orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }

        this.log.record('go into communityChange');

        let sql = this.conn.format('select AppID as FeatureID, ChargeData from OrderEndUserList where OrderID=?', [orderId])
        const data = await this.conn.query(sql);
        const featureId = data[0]['FeatureID'];
        this.log.record(`ChargeData=${data[0]['ChargeData']}`);
        const featureFeeType = Number(JSON.parse(data[0]['ChargeData']).FeatureFeeType);
        if (featureFeeType === 1) {
            this.log.record(`communityChange featureFeeType=${featureFeeType}`);
            sql = this.conn.format('select FeatureExpireTime,ID from CommunityInfo where AccountID = ?', [communityId]);
            const communityData = await this.conn.query(sql);
            const featureExpireTime = communityData[0].FeatureExpireTime;
            if (dayjs(featureExpireTime).format('YYYY-MM-DD HH:mm:ss') === MaxExpireTime) {
                const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
                const nextExpireTime = computedLastDate(now, 1);
                sql = this.conn.format('update CommunityInfo set FeatureExpireTime=? where ID=?', [nextExpireTime, communityData[0].ID]);
                await this.conn.execute(sql);
            }
        }

        sql = this.conn.format('update ManageFeature set FeatureID = ? where AccountID = ?', [featureId, communityId]);
        await this.conn.execute(sql);

        return true;
    }

    private async notify(orderId: number, type: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 14 | 15 | 16 | 17, options: { hasExpired: boolean }) {
        const notifyArr: number[] = [12];
        if(notifyArr.includes(type)){
            return
        }

        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record(`deal order notify orderId=${orderId}, type=${type}`);
        const notifyCloudAdapt = new NotifyCloudAdapt(this.log);

        // const endListType = type === 4 ? 5 : type;
        // 排除视频存储
        let sql = this.conn.format('select AppID, ChargeData,SiteUUID from OrderEndUserList where OrderID=? and Type != 11 and Type != 12', [orderId]);
        const endList = await this.conn.query(sql);

        if (type === 2 || type === 4 || type === 8) {
            // 目前按日续费只有社区，因此将类型统一为社区的过期时间续费类型，才不会发送落地续费邮件
            type = type === 8 ? 2 : type;
            const userSips: string[][] = [];
            const users: string[] = [];
            let index = 0;
            for (let i = 0; i < endList.length; i++) {
                if (userSips.length <= index) userSips[index] = [];
                if (userSips[index].length >= 100) {
                    index += 1;
                    userSips[index] = [];
                }

                const id = endList[i]['AppID'];
                sql = this.conn.format('select Account from PersonalAccount where ID=?', [id]);
                const userData = await this.conn.query(sql);
                userSips[index].push(userData[0]['Account']);
                users.push(userData[0]['Account']);
            }

            // 6.2新增用户续费邮件开关
            sql = this.conn.format('select A.SendRenew,A.Grade from Account A join OrderList O on A.ID=O.InstallID where O.ID = ?', [orderId]);
            const data = await this.conn.query(sql);
            const sendRenew = data[0]['SendRenew'];
            const grade = data[0]['Grade'];
            this.log.record(`deal order notify sendRenew=${sendRenew}`);
            if (sendRenew === 1) {
                if (grade == InstallToCommunity || grade == InstallToSingle) {
                    for (let i = 0; i < userSips.length; i++) {
                        this.log.record('RenewServerNotify begin');
                        notifyCloudAdapt.send({
                            uid: userSips[i].join(';'),
                            type: type === 2 ? 0 : 1
                        }, renewId, 'RenewServerNotify')
                    }
                } else if (grade == InstallToOffice) {
                    this.log.record('PMOfficeAccountRenew begin');
                    const officeUserArr: Object[] = [];
                    sql = this.conn.format(
                        `select B.Location, AUF.Email, E.FirstName, E.LastName, OI.IsNew, D.Language from PersonalAccount A 
                        left join Account B on B.ID = A.ParentID left join PropertyMngList C on C.CommunityID = A.ParentID 
                        left join OfficeInfo OI on OI.AccountUUID = B.UUID  
                        left join Account D on D.ID = C.PropertyID left join PropertyInfo E on E.AccountID = C.PropertyID
                        left join AccountMap AM on AM.AccountUUID = D.UUID
                        left join AccountUserInfo AUF on AUF.UUID = AM.UserInfoUUID
                        where A.ID = ?`,
                        [endList[0]['AppID']]
                    );
                    const officeInfo = await this.conn.query(sql);

                    this.log.record(`PMOfficeAccountRenew, office Isnew = ${officeInfo[0]['IsNew']}`);
                    // 新办公的走kafka发送，并且查询字段不同
                    if (officeInfo[0]['IsNew'] == 1) {
                        for (let i = 0; i < endList.length; i++) {
                            if (i == 20) {
                                // 超过20条的不发送
                                break;
                            }
                            sql = this.conn.format(
                                `select OC.Name as CompanyName, P.Name, P.ExpireTime,OP.IdNo from PersonalAccount P 
                            left join OfficePersonnel OP on P.UUID = OP.PersonalAccountUUID
                            left join OfficeCompany OC on OC.UUID = OP.OfficeCompanyUUID where P.ID =? AND P.Role = ${NewOfficeUser}`,
                                [endList[i]['AppID']]
                            );
                            const officeUser = await this.conn.query(sql);
                            const officeUserObj = {
                                "Name": decryptData(officeUser[0]['Name']),
                                "CompanyName": officeUser[0]['CompanyName'],
                                "ID": officeUser[0]['IdNo'],
                                "Next Expiration Time": dayjs(officeUser[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss')
                            }
                            officeUserArr.push(officeUserObj);
                        }

                        const phpAdaptNotify = new PhpAdaptNotify(this.log);
                        const params: Object = {
                            language: officeInfo[0]['Language'],
                            officeName: officeInfo[0]['Location'],
                            email: decryptData(officeInfo[0]['Email']),
                            pmName: `${officeInfo[0]['FirstName']} ${officeInfo[0]['LastName']}`,
                            accountNum: endList.length.toString(),
                            list: JSON.stringify(officeUserArr)
                        };
                        phpAdaptNotify.renewEmail(JSON.stringify(params));
                    } else {
                        for (let i = 0; i < endList.length; i++) {
                            if (i == 20) {
                                // 超过20条的不发送
                                break;
                            }
                            sql = this.conn.format(
                                `select U.UnitName, P.Name, P.ExpireTime,POI.EmployeeID from PersonalAccount P 
                            left join PersonalAccountOfficeInfo POI on P.UUID = POI.PersonalAccountUUID
                            left join CommunityUnit U on P.UnitID = U.ID where P.ID =? AND P.Role in (${OfficeUsers})`,
                                [endList[i]['AppID']]
                            );
                            const officeUser = await this.conn.query(sql);
                            const officeUserObj = {
                                "Name": decryptData(officeUser[0]['Name']),
                                "Department": officeUser[0]['UnitName'],
                                "ID": officeUser[0]['EmployeeID'],
                                "Next Expiration Time": dayjs(officeUser[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss')
                            }
                            officeUserArr.push(officeUserObj);
                        }
                        notifyCloudAdapt.send({
                            community: officeInfo[0]['Location'],
                            email: decryptData(officeInfo[0]['Email']),
                            pmName: `${officeInfo[0]['FirstName']} ${officeInfo[0]['LastName']}`,
                            accountNum: endList.length,
                            list: JSON.stringify(officeUserArr)
                        }, {
                            id: officeRenew,
                            from: adaptHeaderFrom.projectTypeOffice
                        }, 'PMOfficeAccountRenew')
                    }
                }

            }

            if (type === 4) {
                // 移入群响铃
                for (let i = 0; i < users.length; i++) {
                    // sql = this.conn.format(
                    //     'insert into LocalSipTransaction (Sip, Message) value(?,?)',
                    //     [users[i], JSON.stringify({
                    //         messageType:'1',
                    //         sip:users[i].toString(),
                    //         groupring: '0'
                    //     })]
                    // );
                    // await this.conn.execute(sql);
                    notifyCloudAdapt.send({
                        macList: [],
                        changeType: 1012,
                        node: users[i].toString(),
                        installerId: 0
                    }, updatePerConfig, 'WebPersonalModifyNotify')
                }
            }
        } else if (type === 1 || type === 3) {
            for (let i = 0; i < endList.length; i++) {
                const id = endList[i]['AppID'];
                sql = this.conn.format('select A.Name,AUF.Email,A.Role,A.Account,A.ParentID from PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.ID=?', [id]);
                const userData = await this.conn.query(sql);
                // 只有激活需要对邮件做特殊处理，需判断月费是否为0
                let subscription = 2; //通用模板
                let communityAccountID = userData[0]['ParentID'];
                
                // 收费信息
                const chargeData = JSON.parse(endList[i]['ChargeData']);

                if ([MultipleSubUser, MultipleMainUser].includes(userData[0]['Role'])) {
                    if (MultipleSubUser === userData[0]['Role']) {
                        sql = this.conn.format('select ParentID from PersonalAccount where ID=?', [userData[0]['ParentID']]);
                        communityAccountID = (await this.conn.query(sql))[0]['ParentID'];
                    }
                    // 判断新小区
                    sql = this.conn.format('select SendExpireEmailType,ChargeMode from Account where ID=?', [communityAccountID]);
                    const emailControl = (await this.conn.query(sql))[0];
                    sql = this.conn.format('select IsNew from CommunityInfo where AccountID=?', [communityAccountID]);
                    const communityData = await this.conn.query(sql);
                    // 满足：新小区，月费非0，chargeMode为1，sendExpireEmailType关闭邮件才不发
                    if (communityData[0]['IsNew'] === 1 && chargeData['MonthlyFee'] > 0 &&
                        (emailControl['SendExpireEmailType'] === 1 || emailControl['SendExpireEmailType'] === 3) && emailControl['ChargeMode'] === 1) {
                        continue;
                    }
                }

                if (type === 1) {
                    // 月费非0,只有社区主账号和PM app才需要
                    if ([PmAppUser, MultipleMainUser].includes(userData[0]['Role'])) {
                        if (chargeData['MonthlyFee'] > 0) {
                            subscription = 1;
                        } else {
                            subscription = 0;
                        }
                    }
                }
                // PM app的邮箱为其PM的邮箱，本身不存邮箱
                if (PmAppUser === userData[0]['Role']) {
                    notifyCloudAdapt.send({
                        active: 1,
                        account: userData[0]['Account'],
                        subscription: subscription
                    }, pmAccountActiveEmail, 'PmAccountActiveEmailNotify')
                }
                if (userData[0]['Email']) {
                    notifyCloudAdapt.send({
                        active: 1,
                        userName: decryptData(userData[0]['Name']),
                        email: decryptData(userData[0]['Email']) || '',
                        subscription: subscription
                    }, activeEmail, 'AccountActiveNotify')
                } else {
                    this.log.record(`email ${decryptData(userData[0]['Email'])} will be not sended`);
                }
            }
        } else if (type === 10) {
            let sql = this.conn.format('select SiteUUID from OrderEndUserList where OrderID=? and Type = 11', [orderId]);
            const endList = await this.conn.query(sql);

            //单住户视频存储
            for (let i = 0; i < endList.length; i++) {
                const uuid = endList[i]['SiteUUID'];
                let sql = this.conn.format('select Account from PersonalAccount where UUID=?', [uuid]);
                const user = await this.conn.query(sql);
                notifyCloudAdapt.send({
                    macList: [],
                    changeType: singleVideoStorageNotify,
                    node: user[0]['Account'].toString(),
                    installerId: 0
                }, updatePerConfig, 'WebPersonalModifyNotify')
            }
        } else if (type === 11) {
            let sql = this.conn.format('select SiteUUID from OrderEndUserList where OrderID=? and Type = 12', [orderId]);
            const endList = await this.conn.query(sql);
            //社区视频存储
            for (let i = 0; i < endList.length; i++) {
                const uuid = endList[i]['SiteUUID'];
                let sql = this.conn.format('select ID from Account where UUID=?', [uuid]);
                const community = await this.conn.query(sql);
                notifyCloudAdapt.send({
                    macList: [],
                    changeType: communityVideoStorageNotify,
                    node: '',
                    communityId: community[0]['ID'],
                    unitId: 0
                }, communityChange, 'WebCommunityModifyNotify');
            }
        } else if (type === 9) {
            //自动续费,只查出视频存储的数据
            let sql = this.conn.format('select SiteUUID,Type from OrderEndUserList where OrderID=? and Type In(11,12)', [orderId]);
            const siteList = await this.conn.query(sql);

            for (let i = 0; i < siteList.length; i++) {
                const itemType = Number(siteList[i]['Type']);
                const uuid = siteList[i]['SiteUUID'];
                if (itemType === 12) {
                    let sql = this.conn.format('select ID from Account where UUID=?', [uuid]);
                    const community = await this.conn.query(sql);
                    notifyCloudAdapt.send({
                        macList: [],
                        changeType: communityVideoStorageNotify,
                        node: '',
                        communityId: community[0]['ID'],
                        unitId: 0
                    }, communityChange, 'WebCommunityModifyNotify');
                } else {
                    let sql = this.conn.format('select Account from PersonalAccount where UUID=?', [uuid]);
                    const user = await this.conn.query(sql);
                    notifyCloudAdapt.send({
                        macList: [],
                        changeType: singleVideoStorageNotify,
                        node: user[0]['Account'].toString(),
                        installerId: 0
                    }, updatePerConfig, 'WebPersonalModifyNotify')
                }
            }
        } else if (type === 14 || type === 15 || type === 16 || type === 17) {
            //三方锁
            let sql = this.conn.format('select * from OrderThirdLockList where OrderID=?', [orderId]);
            const endList = await this.conn.query(sql);
            const userArr: { [key: string]: ThirdLockObj } = {};
            const pmUserArr: { [key: string]: ThirdLockObj } = {};
            const phpAdaptNotify = new PhpAdaptNotify(this.log);
            for (let i = 0; i < endList.length; i++) {
                const item = endList[i];
                const lockUUID = item['LockUUID'];
                let sql = this.conn.format('select * from ThirdLockRelateInfo where LockUUID=?', [lockUUID]);
                const thirdLockData = await this.conn.query(sql);
                const expireTime = dayjs(thirdLockData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                const projectUUID = item['ProjectUUID'];
                const uuid = item['PersonalAccountUUID'];

                if (type === 14 || type === 16) { //单住户三方锁激活、续费
                    sql = this.conn.format('select A.Name,A.Language,AUF.Email,A.Role,A.Account,A.UUID,A.TimeZone from PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.UUID=?', [uuid]);
                    const user = await this.conn.query(sql);
                    // 如果邮箱为空或null不需要发送
                    if (user[0]['Email'] === '' || user[0]['Email'] === null) {
                        continue;
                    }
                    //续费时，查询project SendRenew开关, 为关时不需要发送
                    if (type === 16) {
                        sql = this.conn.format('select * from Account where UUID=?', [projectUUID]);
                        const projectInfo = await this.conn.query(sql);
                        if (projectInfo[0]['SendRenew'] === 0) {
                            continue;
                        }
                    }
                    const chargeData = JSON.parse(item['ChargeData']);
                    let emailType = ""
                    if (type === 14) { //激活
                        // 区分免费还是付费
                        emailType = chargeData.MonthlyFee === 0 ? 'personal_third_party_locks_active_no_monthly_fee' : 'personal_third_party_locks_active_with_monthly_fee';
                    } else if (type === 16){ //续费
                        emailType = "personal_third_party_locks_renew"
                    }

                    let param = {
                        time: [expireTime],
                        timeZone: user[0]['TimeZone'],
                    }
                    let response = await phpAdaptNotify.singleConvertFromServerToClientTimezone(JSON.stringify(param)) as any;
                    let convertExpireTime = expireTime;
                    // 判断 code 是否为 0
                    if (response.code === 0) {
                        if (response.data.list[0].isMaxTime === true) {
                            convertExpireTime = "--"
                        } else {
                            convertExpireTime = response.data.list[0].after
                        }

                    }
                    this.log.record(`single param:${JSON.stringify(param)}, response:${JSON.stringify(response)}, convertExpireTime:${convertExpireTime}`);

                    let tableItem: ThirdLockItem = {
                        communityName: "",
                        unitName: "",
                        roomName: item['AptName'] || '', //防止为null的保护
                        lockName: item['LockName'] || '',
                        expirationTime: convertExpireTime,
                    }
                    let Obj: ThirdLockObj = {
                        email: decryptData(user[0]['Email']),
                        user: decryptData(user[0]['Name']),
                        email_type: emailType,
                        language: user[0]['Language'],
                        expiration_time: convertExpireTime,
                        lock_num: 1,
                        table_list: [tableItem],
                    };
                    if (userArr[uuid]) {
                        userArr[uuid].lock_num += 1;
                        userArr[uuid].table_list.push(tableItem);
                    } else {
                        userArr[uuid] = Obj;
                    }
                } else if (type === 15 || type === 17){ //社区三方锁激活、续费
                    sql = this.conn.format('select * from Account where UUID=?', [projectUUID]);
                    const projectInfo = await this.conn.query(sql);
                    //续费时，查询project SendRenew开关, 为关时不需要发送
                    if (type === 17 && projectInfo[0]['SendRenew'] === 0) {
                        continue;
                    }

                    const chargeData = JSON.parse(item['ChargeData']);
                    let emailType = ""
                    if (type === 15) { //激活
                        // 区分免费还是付费
                        emailType = chargeData.MonthlyFee === 0 ? 'community_third_party_locks_active_no_monthly_fee' : 'community_third_party_locks_active_with_monthly_fee';
                    } else if (type === 17){ //续费
                        // 区分有没有绑定到房间
                        emailType = uuid !== '' && uuid !== null ? "community_third_party_locks_renew_to_enduser" :"community_third_party_locks_renew_to_pm"
                    }

                    // 判断是否绑定到房间(只有续费的绑了房间的是发给终端用户)
                    if (uuid !== '' && uuid !== null && type === 17) { //绑定了房间，发送给终端用户
                        sql = this.conn.format('select A.Name,A.Language,AUF.Email,A.Role,A.Account,A.UUID from PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.UUID=?', [uuid]);
                        const user = await this.conn.query(sql);
                        // 如果邮箱为空不需要发送
                        this.log.record(`uuid:${uuid} user:${JSON.stringify(user)}`);
                        if (user[0]['Email'] === '' || user[0]['Email'] === null) {
                            continue;
                        }

                        let param = {
                            time: [expireTime],
                            timeZone: projectInfo[0]['TimeZone'],
                        }
                        let response = await phpAdaptNotify.communityConvertFromServerToClientTimezone(JSON.stringify(param)) as any;
                        let convertExpireTime = expireTime;
                        // 判断 code 是否为 0
                        if (response.code === 0) {
                            if (response.data.list[0].isMaxTime === true) {
                                convertExpireTime = "--"
                            } else {
                                convertExpireTime = response.data.list[0].after
                            }

                        }
                        this.log.record(`param:${JSON.stringify(param)}, response:${JSON.stringify(response)}, convertExpireTime:${convertExpireTime}`);

                        let tableItem: ThirdLockItem = {
                            communityName: item['ProjectName'] || '', //防止为null的保护
                            unitName: item['UnitName'] || '',
                            roomName: item['AptName'] || '',
                            lockName: item['LockName'] || '',
                            expirationTime: convertExpireTime,
                        }
                        let Obj: ThirdLockObj = {
                            email: decryptData(user[0]['Email']),
                            user: decryptData(user[0]['Name']),
                            email_type: emailType,
                            language: user[0]['Language'],
                            expiration_time: convertExpireTime,
                            lock_num: 1,
                            table_list: [tableItem],
                        };
                        this.log.record(`Obj:${JSON.stringify(Obj)}`);
                        if (userArr[uuid]) {
                            userArr[uuid].lock_num += 1;
                            userArr[uuid].table_list.push(tableItem);
                        } else {
                            userArr[uuid] = Obj;
                        }
                    } else { //未绑定房间,或者激活类型，发送给PM
                        //查询社区绑定的PM信息
                        sql = this.conn.format(
                            `select A.Location, AUF.Email, AUF.UUID, E.FirstName, E.LastName, D.Language from Account A 
                        left join PropertyMngList C on C.CommunityID = A.ID 
                        left join Account D on D.ID = C.PropertyID left join PropertyInfo E on E.AccountID = C.PropertyID
                        left join AccountMap AM on AM.AccountUUID = D.UUID
                        left join AccountUserInfo AUF on AUF.UUID = AM.UserInfoUUID
                        where A.UUID = ?`,
                            [projectUUID]
                        );
                        const pmInfo = await this.conn.query(sql);
                        this.log.record(`pmInfo:${JSON.stringify(pmInfo)}`);
                        // 如果邮箱为空不需要发送
                        if (pmInfo[0]['Email'] === '' || pmInfo[0]['Email'] === null) {
                            continue;
                        }
                        let pmUUID = pmInfo[0]['UUID'];

                        let param = {
                            time: [expireTime],
                            timeZone: projectInfo[0]['TimeZone'],
                        }
                        let response = await phpAdaptNotify.communityConvertFromServerToClientTimezone(JSON.stringify(param)) as any;
                        let convertExpireTime = expireTime;
                        // 判断 code 是否为 0
                        if (response.code === 0) {
                            if (response.data.list[0].isMaxTime === true) {
                                convertExpireTime = "--"
                            } else {
                                convertExpireTime = response.data.list[0].after
                            }

                        }
                        this.log.record(`param:${JSON.stringify(param)}, response:${JSON.stringify(response)}, convertExpireTime:${convertExpireTime}`);

                        let tableItem: ThirdLockItem = {
                            communityName: item['ProjectName'] || '', //防止为null的保护
                            unitName: item['UnitName'] || '',
                            roomName: item['AptName'] || '',
                            lockName: item['LockName'] || '',
                            expirationTime: convertExpireTime,
                        }
                        let Obj: ThirdLockObj = {
                            email: decryptData(pmInfo[0]['Email']),
                            user: `${pmInfo[0]['FirstName']} ${pmInfo[0]['LastName']}`,
                            email_type: emailType,
                            language: pmInfo[0]['Language'],
                            expiration_time: convertExpireTime,
                            lock_num: 1,
                            table_list: [tableItem],
                        };
                        this.log.record(`Obj:${JSON.stringify(Obj)}`);
                        if (pmUserArr[pmUUID]) {
                            pmUserArr[pmUUID].lock_num += 1;
                            pmUserArr[pmUUID].table_list.push(tableItem);
                        } else {
                            pmUserArr[pmUUID] = Obj;
                        }
                    }
                }
            }


            // 记录userArr和pmUserArr到日志
            this.log.record(`userArr: ${JSON.stringify(userArr)}`);
            this.log.record(`pmUserArr: ${JSON.stringify(pmUserArr)}`);

            // 发送给终端用户
            if (Object.keys(userArr).length > 0) {
                for (let key in userArr) {
                    notifyCloudAdapt.sendEmailNotify(userArr[key], userArr[key].email);
                }
            }
            // 发送给pm
            if (Object.keys(pmUserArr).length > 0) {
                for (let key in pmUserArr) {
                    notifyCloudAdapt.sendEmailNotify(pmUserArr[key], pmUserArr[key].email);
                }
            }
        }
        else {
            const sql = this.conn.format('select * from OrderList where ID=?', [orderId]);
            const orderData = await this.conn.query(sql);
            const communityId = orderData[0]['InstallID'];
            if (!(type === 6 && options.hasExpired === false)) {
                const sql1 = this.conn.format('select Grade from Account where ID=?', [communityId]);
                const grade = await this.conn.query(sql1);
                if (grade[0]['Grade'] == InstallToOffice) {
                    notifyCloudAdapt.send({
                        macList: [],
                        changeType: featurePlan,
                        node: '',
                        communityId: communityId,
                        unitId: 0
                    }, {
                        id: officeFeaturePlanRenew,
                        from: adaptHeaderFrom.projectTypeOffice
                    }, 'OfficeFeaturePlanRenewNotify')
                } else {
                    notifyCloudAdapt.send({
                        macList: [],
                        changeType: featurePlan,
                        node: '',
                        communityId: communityId,
                        unitId: 0
                    }, communityChange, 'WebCommunityModifyNotify');
                }

            }

        }
    }

    //通知三方锁下发pin card
    private async notifySmartLock(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record(`deal order notifySmartLock orderId=${orderId}`);
        const phpAdaptNotify = new PhpAdaptNotify(this.log);
        const params: Object = {
            OrderID: orderId,
        };
        phpAdaptNotify.sendPinCardByOrderID(JSON.stringify(params));
    }

    //通知php重新走支付成功失败逻辑
    private async notifyPhpOrderCapture(OrderNumber: string, Token: string, PlatformOrder: string, Email: string, Type: string, Price: string, BeforeOnePrice: string, CouponNumber: string, CouponCount: string, Status: string ) {
        const phpAdaptNotify = new PhpAdaptNotify(this.log);
        const params: Object = {
            OrderNumber: OrderNumber,
            Token: Token,
            PlatformOrder: PlatformOrder,
            Email: Email,
            Type: Type,
            Price: Price,
            BeforeOnePrice: BeforeOnePrice,
            CouponNumber: CouponNumber,
            CouponCount: CouponCount,
            Status: Status
        };
        this.log.record(`deal order notifyPhpOrderCapture params:${JSON.stringify(params)}`);
        phpAdaptNotify.sendPhpOrderCapture(JSON.stringify(params));
    }

    //通知go重新走支付成功失败逻辑
    private async notifyGoOrderCapture(OrderNumber: string, Token: string, PlatformType: string, PlatformEmail: string, PlatformOrder: string, Price: any, CreditNum: any, CouponNumber: string, CouponCount: number, Status: string ) {
        const phpAdaptNotify = new PhpAdaptNotify(this.log);
        const payFormTypeValue = PlatformType === '0' ? 'paypal' : (PlatformType === '1' ? 'stripe' : 'credit');
        const params: Object = {
            couponAmount: CouponCount,
            couponNumber: CouponNumber,
            creditNum: CreditNum,
            orderNumber: OrderNumber,
            platformEmail: PlatformEmail,
            platformOrder: PlatformOrder,
            platformType: payFormTypeValue,
            price: Price,
            status: Status,
            token: Token,
        };
        this.log.record(`deal order notifyGoOrderCapture params:${JSON.stringify(params)}`);
        phpAdaptNotify.sendGoOrderCapture(JSON.stringify(params));
    }

    private async notifyBmOrderSuccess(OrderNumber: string) {
        const params = `OrderNumber=${OrderNumber}`;
        this.postToBm(params, '/bmserver/setOrderSuccess').then(res => {
            // 如果失败则重试
            if (res.code !== 0) {
                this.postToBm(params, '/bmserver/setOrderSuccess');
            }
        })
    }

    // 自动扣费续费
    private async autoRenew(orderId: number, mixOrderArr: number[]) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into autoRenew');
        let sql = this.conn.format('select * from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const days = Number(orderData[0]['Days']);

        sql = this.conn.format('select AppID,SiteUUID,Type from OrderEndUserList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);
        const userArr: Object[] = [];
        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        for (let i = 0; i < endList.length; i++) {
            const type = endList[i]['Type'];
            // 2=>家庭续费(套餐内资费) 4=>家庭续费额外app费用 5=>落地费 8=按日期续费 10=pm app续费 11=单住户视频存储付费 12=社区视频存储续费
            if (type !== 2 && type !== 4 && type !== 5 && type !== 8 && type !== 10 && type !== 11 && type !== 12) {
                this.log.record(`subType=${type} dont deal,continue`);
                continue;
            }

            if (type == 11) {
                //单住户视频存储，更新过期时间
                const siteUUID = endList[i]['SiteUUID'];
                sql = this.conn.format('SELECT ID,UUID,ExpireTime from VideoStorage WHERE PersonalAccountUUID=?', [siteUUID]);
                const videoStorage = await this.conn.query(sql);

                // 某个用户不存在可继续执行，不需要归还数据库连接
                if (videoStorage.length === 0) {
                    this.event.emit('error', {message: `user (uuid=${siteUUID}) not exits`}, false);
                    continue;
                }
                //计算下次过期时间，并判断下次过期时间是否大于2299年，最大只能2299年
                let expireTime = dayjs(videoStorage[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                let nextExpireTime = dayjs(expireTime).add(days, 'day').format('YYYY-MM-DD 23:59:59');
                nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                sql = this.conn.format(
                    'update VideoStorage set ExpireTime=? where ID=?',
                    [nextExpireTime, videoStorage[0]['ID']]
                );
                await this.conn.execute(sql);
                this.log.record('autoRenew：single video storage' + videoStorage[0]['ID'] + ',newExpireTime:' + nextExpireTime);
            } else if (type == 12) {
                //社区视频存储，更新过期时间
                const siteUUID = endList[i]['SiteUUID'];
                sql = this.conn.format('SELECT ID,UUID,ExpireTime from VideoStorage WHERE AccountUUID=?', [siteUUID]);
                const videoStorage = await this.conn.query(sql);

                // 某个用户不存在可继续执行，不需要归还数据库连接
                if (videoStorage.length === 0) {
                    this.event.emit('error', {message: `community (uuid=${siteUUID}) not exits`}, false);
                    continue;
                }
                //计算下次过期时间，并判断下次过期时间是否大于2299年，最大只能2299年
                let expireTime = dayjs(videoStorage[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                let nextExpireTime = dayjs(expireTime).add(days, 'day').format('YYYY-MM-DD 23:59:59');
                nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                sql = this.conn.format(
                    'update VideoStorage set ExpireTime=? where ID=?',
                    [nextExpireTime, videoStorage[0]['ID']]
                );
                await this.conn.execute(sql);
                this.log.record('autoRenew：community video storage' + videoStorage[0]['ID'] + ',newExpireTime:' + nextExpireTime);
            } else {
                const id = endList[i]['AppID'];
                sql = this.conn.format('select PhoneExpireTime,ExpireTime,Role,UUID,EnableSmartHome,ParentID from PersonalAccount where ID=?', [id]);
                const userData = await this.conn.query(sql);

                // 某个用户不存在可继续执行，不需要归还数据库连接
                if (userData.length === 0) {
                    this.event.emit('error', { message: `user (id=${id}) not exits` }, false);
                    continue;
                }

                const role = userData[0]['Role'];

                //计算下次过期时间，单住户续费用PhoneExpireTime，其余用ExpireTime
                let expireTime = dayjs(userData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                if ([SingleMainUser, SingleSubUser].includes(role)) {
                    expireTime = dayjs(userData[0]['PhoneExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                }
                //当前时间大于原过期时间，以当前时间为准续费
                expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                let nextExpireTime = dayjs(expireTime).add(days, 'day').format('YYYY-MM-DD 23:59:59');

                //判断下次过期时间是否大于2299年，最大只能2299年
                nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;

                if ([SingleMainUser, SingleSubUser].includes(role)) { //单住户
                    sql = this.conn.format(
                        'update PersonalAccount set PhoneExpireTime=?,PayStatus=2 where ID=?',
                        [nextExpireTime, id]
                    );
                    await this.conn.execute(sql);
                    this.log.record('autoRenew：Role' + role + ',newExpireTime:' + nextExpireTime);
                    // 更新首次支付时间
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                    let mainUserData = userData
                    if (role === SingleSubUser) {
                        sql = this.conn.format('select PhoneExpireTime,ExpireTime,Role,UUID,EnableSmartHome,ParentID from PersonalAccount where ID=?', [userData[0]['ParentID']]);
                        mainUserData = await this.conn.query(sql);
                    }
                    sql = this.conn.format('select IsNewBilling from PersonalAccountSingleInfo where PersonalAccountUUID=?', [mainUserData[0]['UUID']]);
                    const billData = await this.conn.query(sql);
                    const isNewBilling = billData[0]['IsNewBilling'];
                    // 新收费方案，ExpireTime也需要同步更新成和PhoneExpireTime一致
                    if (isNewBilling === 1) {
                        sql = this.conn.format(
                            'update PersonalAccount set ExpireTime=? where ID=?',
                            [nextExpireTime, id]
                        );
                        await this.conn.execute(sql);
                        this.log.record('autoRenew：Role' + role + ',newAppExpireTime:' + nextExpireTime);
                    }

                    //通知家居
                    if (userData[0]['EnableSmartHome'] === 1) {
                        const userObj = {
                            'account_type': 'single_senior',
                            'activate_type': 'continue',
                            'activate_timestamp': 0,
                            'home_id': userData[0]['UUID'],
                            'user_id': userData[0]['UUID'],
                            'expired_timestamp': dayjs(nextExpireTime).valueOf()
                        }
                        userArr.push(userObj);
                    }
                } else if ([MultipleMainUser].includes(role)) { //社区
                    // 修改从账户和主账户的过期时间
                    sql = this.conn.format(
                        'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=? or (ParentID=? and Role = 21)',
                        [nextExpireTime, id, id]
                    );
                    await this.conn.execute(sql);
                    this.log.record('autoRenew：Role' + role + ',newExpireTime' + nextExpireTime);
                    // 更新首次支付时间
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where (ID=? or (ParentID=? and Role = 21)) and FirstPayTime is null',
                        [now, id, id]
                    );
                    await this.conn.execute(sql);
                    //通知家居
                    sql = this.conn.format('select Switch from CommunityInfo where AccountID=?', [userData[0]['ParentID']]);
                    const communityData = await this.conn.query(sql);
                    if (this.checkIsSmartHomeCommunity(communityData[0]['Switch'])) {
                        const userObj = {
                            'account_type': 'account',
                            'activate_type': 'continue',
                            'activate_timestamp': 0,
                            'home_id': userData[0]['UUID'],
                            'user_id': userData[0]['UUID'],
                            'expired_timestamp': dayjs(nextExpireTime).valueOf()
                        }
                        userArr.push(userObj);
                    }
                }  else if (OfficeUsers.includes(role) || role === PmAppUser) { //办公、PMAPP
                    sql = this.conn.format(
                        'update PersonalAccount set ExpireTime=?,PayStatus=2 where ID=?',
                        [nextExpireTime, id]
                    );
                    await this.conn.execute(sql);
                    this.log.record('autoRenew：Role' + role + ',newExpireTime' + nextExpireTime);
                    // 更新首次支付时间
                    sql = this.conn.format(
                        'update PersonalAccount set FirstPayTime=? where ID=? and FirstPayTime is null',
                        [now, id]
                    );
                    await this.conn.execute(sql);
                } else {
                    // 抛出错误，其他继续执行
                    this.event.emit('error', { message: `error user data: id=${id}, role=${role}, userData=${JSON.stringify(userData)}` }, false);
                }
            }
        }

        // 如果自动扣费的有包含三方锁续费，进行三方锁续费逻辑
        if (mixOrderArr.includes(orderType.singleThirdLockRenew) || mixOrderArr.includes(orderType.communityThirdLockRenew)) {
            sql = this.conn.format('select * from OrderThirdLockList where OrderID=?', [orderId]);
            const endList = await this.conn.query(sql);
            const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
            for (let i = 0; i < endList.length; i++) {
                const item = endList[i];
                const subType= item['ServiceType'];
                // 收费信息
                const lockUUID = item['LockUUID'];
                sql = this.conn.format('select * from ThirdLockRelateInfo where LockUUID=?', [lockUUID]);
                const thirdLockData = await this.conn.query(sql);

                // 单住户三方锁续费
                if (subType === 3) {
                    let expireTime = dayjs(thirdLockData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                    expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                    let nextExpireTime = dayjs(expireTime).add(days, 'day').format('YYYY-MM-DD 23:59:59');
                    nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                    sql = this.conn.format(
                        'update ThirdLockRelateInfo set ExpireTime=? where LockUUID=?', [nextExpireTime, lockUUID]
                    );
                    await this.conn.execute(sql);
                    this.log.record('renewThirdLock Single：uuid' + lockUUID + ',newExpireTime:' + nextExpireTime);
                } else if (subType === 4) {
                    //社区三方锁续费
                    let expireTime = thirdLockData[0]['ExpireTime'];
                    expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                    let nextExpireTime = dayjs(expireTime).add(days, 'day').format('YYYY-MM-DD 23:59:59');
                    nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                    sql = this.conn.format(
                        'update ThirdLockRelateInfo set ExpireTime=? where LockUUID=?', [nextExpireTime, lockUUID]
                    );
                    await this.conn.execute(sql);
                    this.log.record('renewThirdLock Community：uuid' + lockUUID + ',newExpireTime:' + nextExpireTime);
                }
            }
        }

        if (userArr.length > 0) {
            this.notifySmartHome(userArr);
        }

        return true;
    }

    //激活三方锁
    private async activeThirdLock(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into activeThirdLock');
        let sql = this.conn.format('select * from OrderThirdLockList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);

        for (let i = 0; i < endList.length; i++) {
            const item = endList[i];
            // 收费信息
            const chargeData = JSON.parse(item['ChargeData']);
            const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
            const expireTime = computedThirdLockActiveExpireTime(chargeData);
            const lockUUID = item['LockUUID'];
            sql = this.conn.format('update ThirdLockRelateInfo set Active=1,ActiveTime=?,ExpireTime=? where LockUUID=?', [now, expireTime, lockUUID]);
            await this.conn.execute(sql);
            this.log.record('activeThirdLock ：uuid' + lockUUID + ',expireTime:' + expireTime);
        }

        return true;
    }

    //续费三方锁
    private async renewThirdLock(orderId: number) {
        if (this.conn === null) {
            this.event.emit('error', { message: 'conn object is null' });
            return false;
        }
        this.log.record('go into renewThirdLock');
        let sql = this.conn.format('select Months,NextTime,ProjectType from OrderList where ID=?', [orderId]);
        const orderData = await this.conn.query(sql);
        const month = orderData[0]['Months'];
        const nextTime = orderData[0]['NextTime'];

        sql = this.conn.format('select * from OrderThirdLockList where OrderID=?', [orderId]);
        const endList = await this.conn.query(sql);

        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        for (let i = 0; i < endList.length; i++) {
            const item = endList[i];
            const subType= item['ServiceType'];
            // 收费信息
            const lockUUID = item['LockUUID'];
            sql = this.conn.format('select * from ThirdLockRelateInfo where LockUUID=?', [lockUUID]);
            const thirdLockData = await this.conn.query(sql);

            // 单住户三方锁续费
            if (subType === 3) {
                let expireTime = dayjs(thirdLockData[0]['ExpireTime']).format('YYYY-MM-DD HH:mm:ss');
                expireTime = new Date(expireTime).getTime() > new Date().getTime() ? expireTime : now;
                let nextExpireTime = computedLastDate(expireTime, month);
                nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                sql = this.conn.format(
                    'update ThirdLockRelateInfo set ExpireTime=? where LockUUID=?', [nextExpireTime, lockUUID]
                );
                await this.conn.execute(sql);
                this.log.record('renewThirdLock Single：uuid' + lockUUID + ',newExpireTime:' + nextExpireTime);
            } else if (subType === 4) {
                //社区三方锁续费
                let expireTime = thirdLockData[0]['ExpireTime'];
                if (nextTime.getTime() < expireTime.getTime()) {
                    this.event.emit('error', {message: `thirdLock (uuid=${lockUUID}) expireTime=${expireTime},but nextTime=${nextTime}`}, false);
                    continue;
                }
                let nextExpireTime = dayjs(nextTime).format('YYYY-MM-DD 23:59:59');
                nextExpireTime = new Date(nextExpireTime).getTime() > new Date(MaxExpireTime).getTime() ? MaxExpireTime : nextExpireTime;
                sql = this.conn.format(
                    'update ThirdLockRelateInfo set ExpireTime=? where LockUUID=?', [nextExpireTime, lockUUID]
                );
                await this.conn.execute(sql);
                this.log.record('renewThirdLock Community：uuid' + lockUUID + ',newExpireTime:' + nextExpireTime);
            }
        }

        return true;
    }

    private postToBm(params: string, url: string) {
        const p = new Promise<{code: number, msg: string, data: {
                ID : string,
                Number : string,
                Code : string,
                Type : string,
                Status : string,
                FinalPrice : string,
                BeforeOncePrice : string,
                TotalPrice : string,
                Payer : string,
                PayerEmail : string,
                CouponNumber : string | null,
                CouponCount : string,
                PayPlatform : string,
                PayPlatOrder : string,
                PayPlatEmail : string,
                UUID : string,
                SubscriptionUUID : string,
                EventID : string,
                CreditCount : string,
        }}>((resolve, reject)=>{
            const options = {
                host: config.bm.server,
                path: url,
                port: config.bm.port,
                method: 'POST',
                timeout: 5000,
                rejectUnauthorized: false, // 跳过证书验证
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            };

            const req = http.request(options, (res) => {
                res.setEncoding('utf-8');
                let response = '';
                res.on('data', (data) => {
                    this.log.record(data);
                    response += data;
                });

                res.on ('end', () => {
                    try {
                        const resData = JSON.parse(response);
                        resolve(resData);
                    } catch(e) {
                        reject(e)
                    }
                });
            });
            req.write(params);
            req.end();
        });

        return p;
    }
}

export default Deal;