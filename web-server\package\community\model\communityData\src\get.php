<?php
namespace package\community\model\communityData\src;

trait Get
{
    public function getSmartHomeUri()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->loadUtil('account', true);

        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $projectUuid = $projectData['UUID'];
        $this->db->delete2ListWKey(PROXY_TABLES['smartHomeTmpToken'], 'AccountUUID', $projectUuid);
        $token = $this->share->util->randString(54);
        $this->db->insert2List(PROXY_TABLES['smartHomeTmpToken'], [
            ':UUID' => $this->share->util->uuid(),
            ':AccountUUID' => $projectUuid,
            ':Token' => $token,
            ':CreateTime' => $this->share->util->getNow(),
            ':Type' => 1
        ]);
        $manageUrl = '';
        $realUrl = SMART_HOME_HOST2;
        $this->loadUtil('manage', true);
        list($middlePlatformUUID, $unityToken) = $this->notifySmartHome->collect(['Manage', 'notifyGetSmartHomeData'], [$projectData['ManageGroup']], 6520, true);
        $manageUrl = "unity_id=$middlePlatformUUID&unity_token=$unityToken&";
        $url = explode('.', SMART_HOME_HOST2, 2);
        $realUrl = 'https://'.$url[1];

        return ['Url' => "$realUrl/portal/installer/#/engineering/project_creation?$manageUrl"."token=$token&type=community"];
    }

    /**
     * @Author: chenpl
     * @Description: 获取社区下设备支持的能开门的所有门
     * @Params:
     * @Return:
     * @Date: 2025/3/11
     */
    public function getProjectEmergencyGroupDoorList()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->loadUtil('device');
        $devices = $this->utils->device->getAllEmergencyDoorDeviceList($projectId);

        return ['data' => $devices];
    }

    /**
     * @description:获取周统计数据,指旧社区
     * @author:lwj 2023-02-08 18:20:43 V6.6
     * @lastEditor:lwj 2023-02-08 18:20:43 V6.6
     * @param:
     * @return array
     */
    public function getWeekData()
    {
        $params = [PROXY_ROLE['projectId'], USER_ATTR['timeZone']];
        list($projectId, $timeZone) = $this->getParams($params);

        //时区计算
        $now = $this->share->util->getNow();
        $now = $this->share->util->setTimeZone($now, $timeZone, '', '-');
        $today = explode(' ', $now)[0] . ' 00:00:00';

        $week = date('w', strtotime($today));
        $week = $week === 0 ? 7 : $week;
        $week -= 1;
        $startTime = date('Y-m-d H:i:s', strtotime("$today - $week day"));
        $data = [];
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $this->loadProvider('captureUtil');
        $captureTables = $this->services->captureUtil->getCaptureTables($projectData['UUID']);
        // 获取一周内的表只可能是当月的两张，和前一个月的一张
        $captureTables = array_slice($captureTables, 0, 3);

        //开门统计
        $sql = "select count(*) from %s where MngAccountID = :MngAccountID and CaptureType < 102 and CaptureTime >=:CreateTime";
        $bindArray = [':MngAccountID' => $projectId, ':CreateTime' => $startTime];
        $capCountData = $this->services->captureUtil->getCaptureResult($captureTables, $sql, $bindArray);

        //通话统计
        $this->loadProvider('callHistoryUtil');
        $callTables = $this->services->callHistoryUtil->getCallHistoryTables($projectData['UUID']);
        $callTables = array_slice($callTables, 0, 3);
        $sql = "select count(*) from %s where  MngAccountID = :MngAccountID and StartTime >=:CreateTime";
        $bindArray = [':MngAccountID' => $projectId, ':CreateTime' => $startTime];
        $callCountData = $this->services->callHistoryUtil->getCallHistoryResult($callTables, $sql, $bindArray);
        for ($i=0; $i<3; $i++) {
            $data['door'] += $capCountData[$i]['count(*)'];
            $data['call'] += $callCountData[$i]['count(*)'];
        }

        //住户统计
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $comEndMRole = COMENDMROLE;
        $comEndSRole = COMENDSROLE;
        $sqlMaster = "select count(*) from $personalAccountTable 
            where ParentID = :ParentID and Role = $comEndMRole and Special = 0 and CreateTime >=:CreateTime";
        $sqlMember = "select count(*) from $personalAccountTable P join PersonalAccount P2 on P.ParentID = P2.ID 
            where P2.ParentID = :ParentID and P2.Special = 0 and P.Role = $comEndSRole and P.CreateTime >=:CreateTime";
        $bindArray = [':ParentID' => $projectId, ':CreateTime' => $startTime];
        $totals = $this->db->querySList(
            "$sqlMaster union all $sqlMember",
            $bindArray
        );
        $data['users'] = $totals[0]['count(*)'] + $totals[1]['count(*)'];

        //临时密码
        $personalAppTmpKeyTable = PROXY_TABLES['personalAppTmpKey'];
        $pubAppTmpKeyTable = PROXY_TABLES['pubAppTmpKey'];
        $sqlPersonalAppTmp = "select count(*) from $personalAppTmpKeyTable 
            where MngAccountID = :MngAccountID and BeginTime >=:BeginTime";

        $sqlPubAppTmp = "select count(*) from $pubAppTmpKeyTable 
            where MngAccountID = :MngAccountID and BeginTime >=:BeginTime";
        $bindArray = [':MngAccountID' => $projectId, ':BeginTime' => $startTime];
        $totals = $this->db->querySList(
            "$sqlPersonalAppTmp union all $sqlPubAppTmp",
            $bindArray
        );
        $data['tmpKey'] = $totals[0]['count(*)'] + $totals[1]['count(*)'];

        return ['data' => $data];
    }

    /**
     * @description:pm获取首页统计数据
     * @author:lwj 2023-01-16 18:11:54 V6.6
     * @lastEditor:lwj 2023-01-16 18:11:54 V6.6
     * @param:
     * @return array
     */
    public function getStatData()
    {
        $params = [PROXY_ROLE['projectId'], 'TimeZone', PROXY_ROLE['pmId']];
        list($projectId, $timeZone, $pmId) = $this->getParams($params);
        $deviceTable = PROXY_TABLES['devices'];
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $communityUnitTable = PROXY_TABLES['communityUnit'];
        $communityRoomTable = PROXY_TABLES['communityRoom'];
        $commPerPrivateKeyTable = PROXY_TABLES['commPerPrivateKey'];
        $commPerRfKeyTable = PROXY_TABLES['commPerRfKey'];
        $faceMngTable = PROXY_TABLES['faceMng'];
        $pubAppTmpKeyTable = PROXY_TABLES['pubAppTmpKey'];
        $personalAppTmpKeyTable = PROXY_TABLES['personalAppTmpKey'];
        $deliveryTable = PROXY_TABLES['delivery'];
        $staffTable = PROXY_TABLES['staff'];
        ini_set('memory_limit', '512M');

        // offline devicess
        $offlineDevices = $this->db->querySList(
            "select count(*) as total from $deviceTable where MngAccountID =:MngAccountID and Status =:Status",
            [':MngAccountID' => $projectId, ':Status' => 0]
        )[0]['total'];

        $this->loadUtil('account', true);
        $projectInfo = $this->utils->_common->account->getManagerInfo($projectId);

        $mainUserRole= COMENDMROLE;
        $subUserRole= COMENDSROLE;
        $sqlMaster = "from $personalAccountTable where ParentUUID = :ParentUUID and Role = $mainUserRole and Special = 0";
        $sqlMember = "from $personalAccountTable P join $personalAccountTable P1 on P.ParentID = P1.ID 
            where P1.ParentUUID = :ParentUUID and P.Role = $subUserRole";

        // inactivated residents
        $total = $this->db->querySList(
            "select count(*) $sqlMaster and Active = 0 
            union all select count(*) $sqlMember and P.Active = 0",
            [':ParentUUID' => $projectInfo['UUID']]
        );
        $inactivatedResidents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // expiring residents
        $expiringTime = date('Y-m-d H:i:s', strtotime('+7 day'));
        $nowTime = $this->share->util->getNow();
        $total = $this->db->querySList(
            "select count(*) $sqlMaster and Active = 1 and ExpireTime < :ExpiringTime and ExpireTime > :NowTime 
            union all select count(*) $sqlMember and P.Active = 1 and P.ExpireTime < :ExpiringTime and P.ExpireTime > :NowTime",
            [':ParentUUID' => $projectInfo['UUID'],':ExpiringTime' => $expiringTime, ':NowTime' => $nowTime]
        );

        $expiringResidents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // expired residents
        $total = $this->db->querySList(
            "select count(*) $sqlMaster and Active = 1 and ExpireTime < :ExpiringTime 
            union all select count(*) $sqlMember and P.Active = 1 and P.ExpireTime < :ExpiringTime",
            [':ParentUUID' => $projectInfo['UUID'], ':ExpiringTime' => $nowTime]
        );

        $expiredResidents = $total[0]['count(*)'] + $total[1]['count(*)'];

        // buidings
        $buildings = $this->db->querySList(
            "select count(*) as total from $communityUnitTable where MngAccountID =:MngAccountID",
            [':MngAccountID' => $projectId]
        )[0]['total'];
        // apartments
        $apartments = $this->db->querySList(
            "select count(*) as total from $communityRoomTable R join $communityUnitTable U on R.UnitID = U.ID 
            where U.MngAccountID = :MngAccountID",
            [':MngAccountID' => $projectId]
        )[0]['total'];
        // residents
        $total = $this->db->querySList(
            "select count(*) $sqlMaster union all select count(*) $sqlMember",
            [':ParentUUID' => $projectInfo['UUID']]
        );
        $residents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // devices
        $devices = $this->db->querySList(
            "select count(*) as total from $deviceTable where MngAccountID =:MngAccountID",
            [':MngAccountID' => $projectId]
        )[0]['total'];

        // resident access
        $residentPin = $this->db->querySList(
            "select count(*) as total from $commPerPrivateKeyTable where CommunityID = :CommunityID and Code is not null",
            [':CommunityID' => $projectId]
        )[0]['total'];
        $residentRfCard = $this->db->querySList(
            "select count(*) as total from $commPerRfKeyTable where CommunityID = :CommunityID and Code is not null",
            [':CommunityID' => $projectId]
        )[0]['total'];
        $residentFace = $this->db->querySList(
            "select count(*) as total from $faceMngTable where MngAccountID =:MngAccountID",
            [':MngAccountID' => $projectId]
        )[0]['total'];
        $total = $this->db->querySList(
            "select count(*) $sqlMaster and Initialization = 1 
            union all select count(*) $sqlMember and P.Initialization = 1",
            [':ParentUUID' => $projectInfo['UUID']]
        );
        $residentApp = $total[0]['count(*)'] + $total[1]['count(*)'];

        // tempkey access
        $tempKeyTotal = $this->db->querySList(
            "select count(*) from $pubAppTmpKeyTable Pu where Pu.MngAccountID = :MngAccountID
            union all select count(*) from $personalAppTmpKeyTable Pe where Pe.MngAccountID = :MngAccountID",
            [':MngAccountID' => $projectId]
        );
        $tempKeys = $tempKeyTotal[0]['count(*)'] + $tempKeyTotal[1]['count(*)'];

        // delivery access
        $deliveryPin = $this->db->querySList(
            "select count(*) as total from $deliveryTable where PinCode is not null and PinCode != '' and CommunityID = :CommunityID",
            [':CommunityID' => $projectId]
        )[0]['total'];
        $deliveryRfCard = $this->db->querySList(
            "select count(*) as total from $deliveryTable where CardCode is not null and CardCode != '' and CommunityID = :CommunityID",
            [':CommunityID' => $projectId]
        )[0]['total'];

        // staff access
        $staffRfCard = $this->db->querySList(
            "select count(*) as total from $staffTable where CardCode is not null and CardCode != '' and CommunityID = :CommunityID",
            [':CommunityID' => $projectId]
        )[0]['total'];


        $data = [
            'Building' => intval($buildings), 'Apt' => intval($apartments),
            'Device' => [
                'All' => intval($devices), 'Offline' => intval($offlineDevices)
            ],
            'App' => [
                'Inactivated' => $inactivatedResidents, 'Expiring' => $expiringResidents,
                'Expired' => $expiredResidents, 'All' => $residents
            ],
            'Key' => [
                'Resident' => [intval($residentPin), intval($residentRfCard), intval($residentFace), intval($residentApp)],
                'TempKey' => [intval($tempKeys)],
                'Delivery' => [intval($deliveryPin), intval($deliveryRfCard)],
                'Staff' => [intval($staffRfCard)]
            ],
        ];
        return ['data' => $data];
    }

    /**
     * @description:pm今日开门统计
     * @author:lwj 2023-01-17 18:11:54 V6.6
     * @lastEditor:lwj 2023-01-17 18:11:54 V6.6
     * @param:
     * @return array
     */
    public function getTodayDoorForPM()
    {
        $params = [PROXY_ROLE['projectId'], 'TimeZone'];
        list($projectId, $timeZone) = $this->getParams($params);

        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $hashKey = $projectData['UUID'];
        $now = $this->share->util->setTimeZone($this->share->util->getNow(), $timeZone, '', '+');
        $day = explode(' ', $now)[0];
        $startDay = $day . ' 00:00:00';
        $startDay = $this->share->util->setTimeZone($startDay, $timeZone, '', '-');
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($hashKey);
        $tables = [$tables[0], $tables[1]];
        $baseSql = "select * from %s where MngAccountID = :MngAccountID and Response = 0 
           and CaptureTime >=:StartDay";
        $bindArray = [':MngAccountID' => $projectId, ':StartDay' => $startDay];
        $captures = $this->services->captureUtil->getCaptureResult($tables, $baseSql, $bindArray);
        // V6.4 kxl 一次查询，通过循环来统计条数
        $pinCount = 0;
        $rfCardCount = 0;
        $faceCount = 0;
        $callCount = 0;
        $appCount = 0;
        foreach ($captures as $capture) {
            $captureType = $capture['CaptureType'];
            if ($captureType === strval(CAPTURE_TYPES['localKey'])) {
                $pinCount ++;
            } elseif ($captureType === strval(CAPTURE_TYPES['rfCard'])) {
                $rfCardCount ++;
            } elseif ($captureType === strval(CAPTURE_TYPES['face'])) {
                $faceCount++;
            } elseif ($captureType === strval(CAPTURE_TYPES['remoteOpenDoor'])) {
                $appCount++;
            } elseif ($captureType === strval(CAPTURE_TYPES['call']) || ($captureType > CAPTURE_TYPES['remoteOpenDoor'] && $captureType < CAPTURE_TYPES['pmUnlock'])) {
                $callCount++;
            }
        }
        return [$pinCount, $rfCardCount, $faceCount, $callCount, $appCount];
    }

    /**
     * @description:pm近一个月的开门统计
     * @author:lwj 2023-01-17 18:11:54 V6.6
     * @lastEditor:lwj 2023-01-17 18:11:54 V6.6
     * @param:
     * @return array
     */
    public function getMonthDoorForPM()
    {
        $params = [PROXY_ROLE['projectId'], 'TimeZone'];
        list($projectId, $timeZone) = $this->getParams($params);

        $now = $this->share->util->setTimeZone($this->share->util->getNow(), $timeZone, '', '+');
        $today = explode(' ', $now)[0]. ' 00:00:00';
        $startTime = date("Y-m-d H:i:s", strtotime("$today - 30 day"));
        $startTime = $this->share->util->setTimeZone($startTime, $timeZone, '', '-');
        
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($projectData['UUID']);
        $currTables = array_slice($tables, 0, 3);
        $baseSql = 'select CaptureType from %s where MngAccountID = :MngAccountID and Response = 0 and CaptureTime >=:StartTime';
        $bindArray = [':MngAccountID' => $projectId, ':StartTime' => $startTime];
        $captures = $this->services->captureUtil->getCaptureResult($currTables, $baseSql, $bindArray);
        // V6.4 kxl 一次查询，通过循环来统计条数
        $pinCount = 0;
        $rfCardCount = 0;
        $faceCount = 0;
        $callCount = 0;
        $appCount = 0;
        foreach ($captures as $capture) {
            $captureType = $capture['CaptureType'];
            if ($captureType === strval(CAPTURE_TYPES['localKey'])) {
                $pinCount ++;
            } elseif ($captureType === strval(CAPTURE_TYPES['rfCard'])) {
                $rfCardCount ++;
            } elseif ($captureType === strval(CAPTURE_TYPES['face'])) {
                $faceCount++;
            } elseif ($captureType === strval(CAPTURE_TYPES['remoteOpenDoor'])) {
                $appCount++;
            } elseif ($captureType === strval(CAPTURE_TYPES['call']) || ($captureType > CAPTURE_TYPES['remoteOpenDoor'] && $captureType < CAPTURE_TYPES['pmUnlock'])) {
                $callCount++;
            }
        }
        return [$pinCount, $rfCardCount, $faceCount, $callCount, $appCount];
    }


    /*
     *@description 获取旧小区首页信息
     *<AUTHOR> 2023-05-23 17:58:38 V6.6.0
     *@lastEditor cj 2023-05-23 17:58:38 V6.6.0
     *@param {*} PROXY_ROLE['projectId']
     *@param {*} TimeZone
     *@param {*} CustomizeForm
     *@return data
     */
    public function getStatDataForOldCom()
    {
        $params = [
            PROXY_ROLE['projectId'],
            'TimeZone'
        ];
        list($projectId, $timeZone) = $this->getParams($params);
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $hashKey = $projectData['UUID'];
        //build
        $this->loadUtil('communityUnit', true);
        $builds = $this->utils->_common->communityUnit->getUnitInfoByArr([['MngAccountID', $projectId]], 'count(*)')[0]['count(*)'];
        //room
        $rooms = $this->utils->_common->account->personalAccountSelectByArray([['ParentID', $projectId], ['Role', COMENDMROLE]], 'count(*)')[0]['count(*)'];
        // 未激活房间
        $inactiveUsers = $this->utils->_common->account->personalAccountSelectByArray([['ParentID', $projectId], ['Role', COMENDMROLE], ['Active', 0]], 'count(*)')[0]['count(*)'];
        // 已过期房间
        $expriUsers = $this->utils->_common->account->personalAccountSelectByArray([['ParentID', $projectId], ['Role', COMENDMROLE], ['Active', 1], ['ExpireTime', $this->share->util->getNow(), '<']], 'count(*)')[0]['count(*)'];

        //door phones
        $this->loadUtil('device', true);
        $doors = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => [0, 1]]));
        //offline door
        $offDoors = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => [0, 1], 'Status' => 0]));
        //indoor
        $indoors = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => 2]));
        //offindoor
        $offIndoors = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => 2, 'Status' => 0]));


        //access
        $access = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => 50]));
        //offaccess
        $offAccess = count($this->utils->_common->device->getDevicesInfo(['MngAccountID' => $projectId, 'Type' => 50, 'Status' => 0]));

        $this->loadUtil('user');
        list($endUsers,$endRUsers,$endURUser,$apps,$phones)= $this->utils->user->getResidentCountInfo($projectId); // 获取用户的统计信息
        $cLog = \share\util\getLog();
        $cLog->debug("get resident count info={data}", ['data' => [$endUsers,$endRUsers,$endURUser,$apps,$phones]]);
        

        // 未激活主账号，非空房间
        $inactive = $this->utils->_common->account->personalAccountSelectByArray([['ParentID', $projectId], ['Role', COMENDMROLE], ['Active', 0], ['Special', 0]], 'count(*)')[0]['count(*)'];
        
        $this->loadUtil('key', true);
        $pubTmpKeyCount = $this->utils->_common->key->getPubAppTmpKeyInfoByKey([['MngAccountID', $projectId]], 'count(*)')[0]['count(*)'];
        $perTmpKeyCount = $this->utils->_common->key->getPersonalAppTmpKeyInfoByKey([['MngAccountID', $projectId]], 'count(*)')[0]['count(*)'];
        $tmpkeys = $pubTmpKeyCount + $perTmpKeyCount;
        $pubCardCount = $this->utils->_common->key->getPubRfcardKeyInfoByKey([['MngAccountID', $projectId]], 'count(*)')[0]['count(*)'];
        $perCardCount = $this->utils->_common->key->getPersonalRfcardKeyInfoByKey([['MngAccountID', $projectId]], 'count(*)')[0]['count(*)'];
        $rfcards = $pubCardCount + $perCardCount;

        //calls
        $this->loadProvider('callHistoryUtil');
        $callTables = $this->services->callHistoryUtil->getCallHistoryTables($hashKey);
        $callTables = array_slice($callTables, 0, 3);
        $sql = "select ID from %s where  MngAccountID = :MngAccountID";
        $bindArray = [':MngAccountID' => $projectId];
        $calls = count($this->services->callHistoryUtil->getCallHistoryResult($callTables, $sql, $bindArray));

       
        $data = ["builds" => $builds, "rooms" => $rooms, "inactiveUsers" => $inactiveUsers, "expriUsers" => $expriUsers, "doors" => $doors, "offdoors" => $offDoors, "indoors" => $indoors, "offindoor" => $offIndoors, "access" => $access, "offaccess" => $offAccess,
            "endusers" => $endUsers, "endrusers" => $endRUsers, "endurusers" => $endURUser, "apps" => $apps, "phones" => $phones, "inactive" => $inactive,
            "tmpkeys" => $tmpkeys, "rfcards" => $rfcards, "calls" => $calls];
        return ["data" => $data];
    }


    
    /*
     *@description 获取当天个时段的开门记录次数
     *<AUTHOR> 2023-05-23 17:59:18 V6.6.0
     *@lastEditor cj 2023-05-23 17:59:18 V6.6.0
     *@param {*} PROXY_ROLE['projectId']
     *@param {*} TimeZone
     *@return [$data, $count]
     */
    public function geTodayDoor()
    {
        $params = [
            PROXY_ROLE['projectId'],
            'TimeZone'
        ];
        list($projectId, $timeZone) = $this->getParams($params);

        
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $hashKey = $projectData['UUID'];
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($hashKey);
        $tables = [$tables[0], $tables[1]];
        $now = $this->share->util->getNow();
        $now = $this->share->util->setTimeZone($now, $timeZone, '', '+');
        $day = explode('', $now)[0];
        $zero = $day . " 00:00:00";
        $two = $day . " 02:00:00";
        $four = $day . " 04:00:00";
        $six = $day . " 06:00:00";
        $eight = $day . " 08:00:00";
        $ten = $day . " 10:00:00";
        $twelve = $day . " 12:00:00";
        $fourteen = $day . " 14:00:00";
        $sixteen = $day . " 16:00:00";
        $eighteen = $day . " 18:00:00";
        $twty = $day . " 20:00:00";
        $twtytwo = $day . " 22:00:00";
        $twtyfour = $day . " 24:00:00";
        $times = [$zero, $two, $four, $six, $eight, $ten, $twelve, $fourteen, $sixteen, $eighteen, $twty, $twtytwo, $twtyfour];
        $data = [];
        for ($i = 0; $i < count($times) - 1; $i++) {
            $sql = "select ID from %s where MngAccountID = :MngAccountID and CaptureType < 102 and CaptureTime >= :BeginCaptureTime and CaptureTime <= :EndCaptureTime";
            $bindArray = [':MngAccountID' => $projectId, ':BeginCaptureTime' => $this->share->util->setTimeZone($times[$i], $timeZone, "", "-"), ':EndCaptureTime' => $this->share->util->setTimeZone($times[$i + 1], $timeZone, "", "-")];
            array_push($data, count($this->services->captureUtil->getCaptureResult($tables, $sql, $bindArray)));
        }
        $count = 0;
        foreach ($data as $val) {
            $count += $val;
        }

        return [$data, $count];
    }

    /*
     *@description 获取每月开门记录次数
     *<AUTHOR> 2023-05-23 17:59:18 V6.6.0
     *@lastEditor cj 2023-05-23 17:59:18 V6.6.0
     *@param {*} PROXY_ROLE['projectId']
     *@return [$data, $count]
     */
    public function getMonDoor()
    {
        $params = [
            PROXY_ROLE['projectId']
        ];
        list($projectId) = $this->getParams($params);

        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $hashKey = $projectData['UUID'];
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($hashKey);
        $tables = [$tables[0], $tables[1]];
        $sql = "select count(*) from %s where MngAccountID = :MngAccountID and CaptureType < 102";
        $bindArray = [':MngAccountID' => $projectId];
        $count = $this->services->captureUtil->getCaptureResult($tables, $sql, $bindArray)[0]['count(*)'];
        $now = $this->share->util->getNow();
        $day = explode(" ", $now)[0];
        $year = explode("-", $day)[0];
        $month = explode("-", $day)[1];
        $data = [];
        for ($i = 1; $i < intval($month); $i++) {
            if ($i < 10) {
                $i = "0$i";
            }
            $time = $year . $i;
            $captureCount = $this->db->querySList("select C.* from MonthCaptureCounted C join MonthSliceTableName T on C.MonthSliceTableNameID = T.ID where T.YearMonth = :YearMonth and C.MngAccountID = :MngAccountID", [":YearMonth" => $time, ":MngAccountID" => $projectId]);
            $doorCount = 0;
            foreach ($captureCount as $val) {
                $doorCount += intval($val['DoorCount']);
            }
            array_push($data, $doorCount);
        }
        array_push($data, intval($count));
        while (count($data) < 12) {
            array_push($data, 0);
        }

        $count = 0;
        foreach ($data as $val) {
            $count += $val;
        }

        return [$data, $count];
    }

    public function getBillingInfo()
    {
        $params = ['ProjectUUID'];
        list($projectUUID) = $this->getParams($params);

        $this->loadProvider('billsysUtil');
        $this->loadUtil('account', true);
        $projectInfo = $this->utils->_common->account->accountSelectByKey('UUID', $projectUUID)[0];
        $insAccount = $this->utils->_common->account->accountSelectByKey('ID', $projectInfo['ManageGroup'], 'Account')[0]['Account'];
        $disAccount = $this->utils->_common->account->accountSelectByKey('UUID', $projectInfo['ParentUUID'], 'Account')[0]['Account'];
        $chargeData = $this->services->billsysUtil->getRentManagerChargePlan($disAccount, $insAccount);
        $rentManageFee = floatval($chargeData['MonthlyFee']);

        $community = $this->utils->_common->account->accountSelectByKey('UUID', $projectUUID)[0];
        // 查询社区是否有开启视频存储
        $videoStorage = $this->dao->videoStorage->selectByKey('AccountUUID', $projectUUID)[0];
        $videoStorageFee = [];
        if ($videoStorage['IsEnable'] == 1) {
            $this->loadUtil('order', true);
            $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo([$community['ID']], PAY_TYPE_MULTIPLE);

            $videoCharges = $this->services->billsysUtil->getVideoStorageCharge($videoStorageInfo);
            $model = array_values($videoCharges)[0]['Model'];
            $videoStorageFee = [
                ['Type' => 'all', 'Fee' => floatval($model['MonthlyFee'])]
            ];
            if (!empty($model['MonthlyFeeRoleConfig'])) {
                foreach ($model['MonthlyFeeRoleConfig'] as $val) {
                    switch ($val['Type']) {
                        case '0':
                            $videoStorageFee[] = ['Type' => PAYER_ROLE_DIS, 'Fee' => floatval($val['Fee'])];
                            break;
                        case '1':
                            $videoStorageFee[] = ['Type' => PAYER_ROLE_SUBDIS, 'Fee' => floatval($val['Fee'])];
                            break;
                        case '2':
                            $videoStorageFee[] = ['Type' => PAYER_ROLE_INS, 'Fee' => floatval($val['Fee'])];
                            break;
                        case '3':
                            $isPmFee = $this->dao->communityInfo->selectByKey('AccountUUID', $projectUUID)[0]['IsPMFee'];
                            if ($isPmFee == '1') {
                                $videoStorageFee[] = ['Type' => PAYER_ROLE_PM, 'Fee' => floatval($val['Fee'])];
                            }
                            break;
                    }
                }
            }
        }

        return [
            'data' => [
                'VideoStorageFee' => $videoStorageFee,
                'RentManageFee' => $rentManageFee
            ]
        ];
    }
}
