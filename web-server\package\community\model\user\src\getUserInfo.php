<?php
/**
 * @description 获取用户信息
 * <AUTHOR>
 * @date 2022-04-02 10:13:39
 * @version V6.4
 * @lastEditor kxl
 * @lastEditTime 2024-12-21 10:46:13
 * @lastVersion V6.7.0
 */

namespace package\community\model\user\src;

use package\community\model\user\config\Code;

trait GetUserInfo
{
    /**
     * @description: 获取社区待激活用户列表
     * @author: shoubin.chen 2024/11/21 17:20:22 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/21 17:20:22  V7.1.0
     */
    public function getActiveUserList()
    {
        $params = ['ProjectUUID', 'Type', 'InstallerUUID', 'Active', 'IsBatch', PROXY_ROLE['projectId'], 'page'];
        list($projectUUID, $type, $installerUUID, $active, $isBatch, $projectId, $page) = $this->getParams($params);
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $this->getParamsLimitAndSearch();

        $row = 3000;
        $page = intval($page);
        $page = $page >= 1 ? $page : 1; //保护，防止攻击
        $offset = ($page - 1) * $row;

        $this->loadUtil('account', true);
        $this->loadUtil('common', true);
        $installerId = $this->dao->account->selectByUUID($installerUUID, 'ID')[0]['ID'];
        $this->loadProvider('billsysUtil');
        if ($isBatch === 'true') {
            if ($projectUUID === '') {
                $communityIds = $this->dao->account->selectByArray([['Grade', COMMUNITYGRADE], ['ManageGroup', $installerId]], 'ID');
                $communityIds = array_column($communityIds, 'ID');

            } else {
                $communityUUIDs = explode(';', $projectUUID);
                // 多社区支付验证Community是否在installer下
                $communityIds = $this->dao->account->selectByArray([['Grade', COMMUNITYGRADE], ['ManageGroup', $installerId], ['UUID', $communityUUIDs]], 'ID');
                if (count($communityIds) !== count($communityUUIDs)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_INS]);
                }
                $communityIds = array_column($communityIds, 'ID');
            }
        } else {
            $communityIds = [$projectId];
        }
        $communityData = [];
        if (count($communityIds) === 0) {
            return ['data' => ['row' => [], 'total' => 0]];
        }

        $this->loadUtil('rentManager');
        foreach ($communityIds as $val) {
            // 刷新一次过期时间,批量支付避免刷新时间过久导致进程被杀死
            // $this->services->billsysUtil->checkPlan($val);
            // 提前查出所选社区的时区，避免后续每个都进行查询
            $communityData[$val] = $this->utils->_common->common->getAccountInfoWithID($val, ['TimeZone', 'CustomizeForm', 'Location', 'UUID']);
        }
        // 多选社区时时区保持一致
        $communityTimeZone = $communityData[$communityIds[0]]['TimeZone'];
        $communityIds = implode(',', $communityIds);
        $bindArray = [':Active' => $active,
            ':TimeZone' => $communityTimeZone, ':Type' => $type, ':CommunityIds' => $communityIds];

        $feeType = 'ActiveFee';
        $saveFeeType = 'ActiveSaveFee';

        // 获取待激活住户列表
        list($userData, $total) = $this->dao->personalAccount->getActiveCommunityUserList($bindArray, $offset, $row, $sortField, $sort);

        if ($total === 0 or empty($userData)) {
            return ['data' => ['row' => [], 'total' => $total]];
        }

        $infoList = [];
        $unitIds = [];
        $roomIds = [];
        $unitUUids = []; // 三方锁绑定在楼栋公共时，只有unitUUIDs，关联不到用户查询到unitId
        $endUserData = $thirdLockData = [];

        foreach ($userData as $val) {
            if (!empty($val['RoomID'])) {
                array_push($roomIds, $val['RoomID']);
            }
            if (!empty($val['UnitID'])) {
                array_push($unitIds, $val['UnitID']);
            }
            if (!empty($val['CommunityUnitUUID'])) {
                array_push($unitUUids, $val['CommunityUnitUUID']);
            }
            if ($val['ServiceType'] == 'ThirdLock') {
                array_push($thirdLockData, $val);
            } else {
                array_push($endUserData, $val);
            }
        }

        //获取三方锁的计费信息
        $lockChargeData = [];
        if (!empty($thirdLockData)) {
            $projectUUIDs = array_unique(array_column($thirdLockData, 'ProjectUUID'));
            $projectInfos = $this->utils->_common->account->accountSelectByArray([['UUID', $projectUUIDs]], 'ID,UUID,Location');
            $tmpChargeData = [];
            $insInfo = $this->utils->_common->account->getManagerInfoByKey('UUID', $installerUUID);
            $disInfo = $this->utils->_common->account->getManagerInfoByKey('UUID', $insInfo['ParentUUID']);
            foreach ($projectInfos as $val) {
                $tmpChargeData[] = ['Instance' => ['Distributor' => $disInfo['Account'], 'Installer' => $insInfo['Account'], 'Project' => $val['Location'], 'Type' => 1, 'ProjectUUID' => $val['UUID']]];
            }
            $thirdLockCharges = $this->services->billsysUtil->getThirdLockCharge($tmpChargeData);
            foreach ($projectInfos as $key => $val) {
                $projectInfos[$key]['Charge'] = $thirdLockCharges[$key];
            }
            $lockChargeData = array_column($projectInfos, null, 'UUID');
        }

        $chargeUserData['all'] = $endUserData;

        $unitName = $unitUuidName = ['0' => '--'];
        if (!empty($unitIds)) {
            $unitArray = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityUnit'], [
                'ID', 'UUID', 'UnitName'], ['ID' => $unitIds]);
            foreach ($unitArray as $val) {
                $unitName[$val['ID']] = $val['UnitName'];
                $unitUuidName[$val['UUID']] = $val['UnitName'];
            }
        }
        if (!empty($unitUUids)) {
            $unitArray = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityUnit'], [
                'ID', 'UUID', 'UnitName'], ['UUID' => $unitUUids]);
            foreach ($unitArray as $val) {
                $unitName[$val['ID']] = $val['UnitName'];
                $unitUuidName[$val['UUID']] = $val['UnitName'];
            }
        }
        $msgText = $this->share->util->getMsg()->getMsgText();

        // 获取激活/续费费用
        $charges = $this->services->billsysUtil->getCharge('multiple', null, $chargeUserData, $active);
        $this->loadUtil('account', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('itec', true);
        foreach ($userData as $val) {
            if (!empty($unitName[$val['UnitID']])) {
                $val['UnitName'] = $unitName[$val['UnitID']];
            } else if (!empty($unitUuidName[$val['CommunityUnitUUID']])) {
                $val['UnitName'] = $unitUuidName[$val['CommunityUnitUUID']];
            } else {
                $val['UnitName'] = '--';
            }
            if ($val['Floor'] !== '' && $val['Floor'] !== null) {
                $val['RoomName'] = $val['RoomName'] . ' (' . $msgText['floor'] . ' ' . $val['Floor'] . ')';
            }
            $amount = $feeType;
            $saveFee = $saveFeeType;
            if (intval($val['Role']) === COMENDSROLE) {
                $amount = 'AddAppFee';
                $saveFee = 'AddAppSaveFee';
            }

            if ($val['ServiceType'] == 'ThirdLock') {
                // 三方锁绑定的用户信息和锁的名称需要单独查询 锁品牌类型：0=Qrio,1=Yale,2=BSI,3=Dormakaba,4=SL20,5=Salto,6=Itec,7=TT锁
                if ($val['Brand'] == '3') {
                    $name = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $val['UUID']]])[0]['Name'];
                } else if ($val['Brand'] == '6') {
                    $name = $this->utils->_common->itec->getItecLock([['UUID', $val['UUID']]])[0]['Name'];
                }
                $projectID = $lockChargeData[$val['ProjectUUID']]['ID'];
                $amountVal = $lockChargeData[$val['ProjectUUID']]['Charge']['ThirdLock'][$val['Brand']]['ActiveFee'];
                $saveFeeVal = 0;
                $introduction = $activityId = null;
            } else {
                $name = $val['Name'];
                $projectID =  $val['ProjectId'];
                $amountVal = $charges[$val['ID']][$amount];
                $saveFeeVal = $charges[$val['ID']][$saveFee];
                $introduction = $charges[$val['ID']]['Introduction'];
                $activityId = $charges[$val['ID']]['ActivityId'];
            }

            if ($val['ServiceType'] == 'SubUser') {
                $serviceType = "2";
            } elseif ($val['ServiceType'] == 'PmApp') {
                $serviceType = "3";
            } elseif ($val['ServiceType'] == 'ThirdLock') {
                $serviceType = "4";
            } else {
                $serviceType = "1";
            }

            $bindArray = [
                'ID' => $val['ID'],
                'UUID' => $val['UUID'],
                'Role' => $val['Role'],
                'UnitName' => $val['UnitName'],
                'RoomName' => $val['RoomName'],
                'RoomNumber' => $val['RoomNumber'],
                'Email' => $val['Email'],
                'Name' => $name,
                'ProjectID' => $projectID,
                'Amcount' => $amountVal,
                'SaveFee' => $saveFeeVal,
                'Introduction' => $introduction,
                'ActivityId' => $activityId,
                'CommunityName' => $communityData[$projectID]['Location'],
                'CustomizeForm' => $communityData[$projectID]['CustomizeForm'],
                'ServiceType' => $serviceType
            ];

            if (intval($val['Role']) === PMENDMROLE) {
                $bindArray['Email'] = $this->utils->_common->account->getManagerListByArray([['UUID', $val['Email']], ['Grade', PROPERTYMANAGE]])[0]['Email'];
                $bindArray['RoomName'] = '--';
                $bindArray['RoomNumber'] = '--';
            }
            array_push($infoList, $bindArray);
        }

        return ['data' => ['row' => $infoList, 'total' => $total]];
    }

    /**
     * @description: 获取待激活/待续费列表
     * @author: cj 2022/04/02 10:20
     * @LastEditors: cj 2022/04/19 09:40
     * @param {string} ProjectUUID:指社区UUID
     * @param {string} Type:all指全部类型 0:指房间主账号 1:指PmApp 2:指额外App 3:视频存储
     * @return {*}
     */
    public function getActiveOrRenewUserInfo()
    {
        $params = ['ProjectUUID', 'Type', 'InstallerUUID', 'BeginTime', 'EndTime', 'Active', 'IsBatch', PROXY_ROLE['projectId'], 'page'];
        list($projectUUID, $type, $installerUUID, $beginTime, $endTime, $active, $isBatch, $projectId, $page) = $this->getParams($params);
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $this->getParamsLimitAndSearch();

        $row = 3000;
        $offset = ($page - 1) * $row;
        // kxl临时改动，6.5应该更改为使用order中的checkChargeMode
        $this->loadUtil('account', true);
        // $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        // $disId = $projectData['ParentID'];
        // $disChargeMode = $this->utils->_common->account->getManagerInfo($disId)['ChargeMode'];
        // $chargeMode = intval($disChargeMode);
        // if ($chargeMode === 1) {
        //     $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_DIS_CHARGE_MODE_INVALID]);
        // }
        $installer = $this->utils->_common->account->getInstallerByProjectID($projectId);
        $this->loadUtil('common', true);
        $installerId = $this->dao->account->selectByUUID($installerUUID, 'ID')[0]['ID'];
        $this->loadUtil('videoStorage', true);
        $videoStorageCommunityList = $this->utils->_common->videoStorage->videoStorageSelectByArray([['InstallerUUID', $installerUUID], ['ProjectType', 0], ['IsEnable', 1]]);
        $videoStorageCommunityList = $this->share->util->arrayColumnAsKey($videoStorageCommunityList, 'AccountUUID');

        $this->loadProvider('billsysUtil');
        if ($isBatch === 'true') {
            if ($projectUUID === '') {
                // 续费
                if ($active === 1) {
                    return ['data' => ['row' => [], 'total' => 0]];
                } else {
                    $communityIds = $this->dao->account->selectByArray([['Grade', COMMUNITYGRADE], ['ManageGroup', $installerId]], 'ID');
                    $communityIds = array_column($communityIds, 'ID');
                }
            } else {
                $communityUUIDs = explode(';', $projectUUID);
                // 多社区支付验证Community是否在installer下
                $communityIds = $this->dao->account->selectByArray([['Grade', COMMUNITYGRADE], ['ManageGroup', $installerId], ['UUID', $communityUUIDs]], 'ID');
                if (count($communityIds) !== count($communityUUIDs)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_INS]);
                }
                $communityIds = array_column($communityIds, 'ID');
            }
        } else {
            $communityIds = [$projectId];
        }
        $communityData = [];
        if (count($communityIds) === 0) {
            return ['data' => ['row' => [], 'total' => 0]];
        }
        
        $this->loadUtil('rentManager');
        foreach ($communityIds as $val) {
            // 刷新一次过期时间,批量支付避免刷新时间过久导致进程被杀死
            // $this->services->billsysUtil->checkPlan($val);
            // 提前查出所选社区的时区，避免后续每个都进行查询
            $communityData[$val] = $this->utils->_common->common->getAccountInfoWithID($val, ['TimeZone', 'CustomizeForm', 'Location', 'UUID']);
        }
        // 多选社区时时区保持一致
        $communityTimeZone = $communityData[$communityIds[0]]['TimeZone'];
        $communityIds = implode(',', $communityIds);
        $bindArray = [':Active' => $active, ':BeginTime' => $beginTime, ':EndTime' => $endTime,
            ':TimeZone' => $communityTimeZone, ':Type' => $type, ':CommunityIds' => $communityIds, ':InsID' => $installer['ID']];
        if ($active === 0) {
            $feeType='ActiveFee';
            $saveFeeType='ActiveSaveFee';
        } elseif ($active === 1) {
            $feeType='MonthlyFee';
            $saveFeeType='MonthlySaveFee';
        }
        // 获取待激活/续费社区住户列表(包含app，videoStorage)
        list($userData, $total) = $this->dao->personalAccount->getActiveOrRenewCommunityUserInfo($bindArray, $offset, $row, $sortField, $sort);

        if ($total === 0 or empty($userData)) {
            return ['data' => ['row' => [], 'total' => $total]];
        }
        $mixChargeData = $chargeUserData = [];
        $videoSiteIDs = [];
        foreach ($userData as $item) {
            if ($item['ServiceType'] != 'VideoStorage') {
                $chargeUserData['all'][] = $item;
            } else {
                $videoSiteIDs[] = $item['ID'];
            }
            $mixChargeData['all'][] = $item;
        }

        $infoList = [];
        $unitIds = [];
        $roomIds = [];

        foreach ($userData as $val) {
            array_push($roomIds, $val['RoomID']);
            array_push($unitIds, $val['UnitID']);
        }
        $unitArray = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityUnit'], [
            'ID','UnitName'], ['ID' => $unitIds]);
        $unitName = ['0' => '--'];
        foreach ($unitArray as $val) {
            $unitName[$val['ID']] = $val['UnitName'];
        }
        $msgText = $this->share->util->getMsg()->getMsgText();

        // 获取激活/续费费用
        $charges = $this->services->billsysUtil->getCharge('multiple', null, $chargeUserData, $active);
        $this->loadUtil('order', true);
        $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoSiteIDs, PAY_TYPE_MULTIPLE);
        $videoCharges = $this->services->billsysUtil->getVideoStorageCharge($videoStorageInfo);
        $videoCharges = $this->share->util->arrayColumnAsKey($videoCharges, 'UUID');
        $this->loadUtil('account', true);
        foreach ($mixChargeData['all'] as $val) {
            $val['UnitName'] = $unitName[$val['UnitID']];
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.$msgText['floor'].' '.$val['Floor'].')';
            }
            $amount = $feeType;
            $saveFee = $saveFeeType;
            if (intval($val['Role']) === COMENDSROLE) {
                $amount = 'AddAppFee';
                $saveFee = 'AddAppSaveFee';
            }
            $bindArray = [
                'ID' => $val['ID'],
                'UUID' => $val['UUID'],
                'Role' => $val['Role'],
                'UnitName' => $val['UnitName'],
                'RoomName' => $val['RoomName'],
                'RoomNumber' => $val['RoomNumber'],
                'Email' => $val['Email'],
                'Name' => $val['Name'],
                'ProjectID' => $val['ProjectId'],
                'Amcount' => $charges[$val['ID']][$amount],
                'SaveFee' => $charges[$val['ID']][$saveFee],
                'Introduction' => $charges[$val['ID']]['Introduction'],
                'ActivityId' => $charges[$val['ID']]['ActivityId'],
                'CommunityName' => $communityData[$val['ProjectId']]['Location'],
                'CustomizeForm' => $communityData[$val['ProjectId']]['CustomizeForm'],
                'ServiceType' => $this->utils->self->dealServiceTypeToInt($val['ServiceType'])
            ];

            if (intval($val['Role']) === PMENDMROLE) {
                $bindArray['Email'] = $this->utils->_common->account->getManagerListByArray([['UUID', $val['Email']], ['Grade', PROPERTYMANAGE]])[0]['Email'];
                $bindArray['RoomName'] = '--';
                $bindArray['RoomNumber'] = '--';
            }
            if ($active === 1) {
                $bindArray['TimeZoneExpireTime'] = $this->share->util->setTimeZone($val['ExpireTime'], $communityTimeZone, '3');
                $bindArray['ExpireTime'] = $this->share->util->setTimeZone($val['ExpireTime'], $communityTimeZone, $communityData[$val['ProjectId']]['CustomizeForm']);
                if ($val['ServiceType'] == 'VideoStorage'){
                    $bindArray['Amcount'] = $videoCharges[$val['UUID']]['MonthlyFee'];
                }
                //V6.7.1 csc 续费列表增加时区及是否创建了订阅标识
                $bindArray['TimeZone'] = $communityTimeZone;
            }
            $bindArray['VideoStorageUUID'] = '';
            if (isset($videoStorageCommunityList[$val['UUID']]) && $bindArray['ServiceType'] == '3') {
                $bindArray['VideoStorageUUID'] = $videoStorageCommunityList[$val['UUID']]['UUID'];
            }
            array_push($infoList, $bindArray);
        }

        return ['data' => ['row' => $infoList, 'total' => $total]];
    }

    /**
     * @description: 获取社区续费用户列表
     * @author: shoubin.chen 2024/11/21 17:20:22 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/21 17:20:22  V7.1.0
     */
    public function getRenewUserList()
    {
        $params = ['ProjectUUID', 'Type', 'InstallerUUID', 'BeginTime', 'EndTime', 'Active', 'IsBatch', PROXY_ROLE['projectId'], 'page'];
        list($projectUUID, $type, $installerUUID, $beginTime, $endTime, $active, $isBatch, $projectId, $page) = $this->getParams($params);
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $this->getParamsLimitAndSearch();

        $row = 3000;
        $page = intval($page);
        $page = $page >= 1 ? $page : 1; //保护，防止攻击
        $offset = ($page - 1) * $row;
        // kxl临时改动，6.5应该更改为使用order中的checkChargeMode
        $this->loadUtil('account', true);
        $this->loadUtil('common', true);
        $installerId = $this->dao->account->selectByUUID($installerUUID, 'ID')[0]['ID'];
        $this->loadProvider('billsysUtil');
        if ($isBatch === 'true') {
            if ($projectUUID === '') {
                // 续费
                if ($active === 1) {
                    return ['data' => ['row' => [], 'total' => 0]];
                }
            } else {
                $communityUUIDs = explode(';', $projectUUID);
                // 多社区支付验证Community是否在installer下
                $communityIds = $this->dao->account->selectByArray([['Grade', COMMUNITYGRADE], ['ManageGroup', $installerId], ['UUID', $communityUUIDs]], 'ID');
                if (count($communityIds) !== count($communityUUIDs)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_INS]);
                }
                $communityIds = array_column($communityIds, 'ID');
            }
        } else {
            $communityIds = [$projectId];
        }
        $communityData = [];
        if (count($communityIds) === 0) {
            return ['data' => ['row' => [], 'total' => 0]];
        }

        $this->loadUtil('rentManager');
        foreach ($communityIds as $val) {
            // 刷新一次过期时间,批量支付避免刷新时间过久导致进程被杀死
            // $this->services->billsysUtil->checkPlan($val);
            // 提前查出所选社区的时区，避免后续每个都进行查询
            $communityData[$val] = $this->utils->_common->common->getAccountInfoWithID($val, ['TimeZone', 'CustomizeForm', 'Location', 'UUID']);
            $communityData[$val]['ServiceType'] = strval(count($this->utils->rentManager->getCommunityInfo($communityData[$val]['UUID'])));
        }

        // 多选社区时时区保持一致
        $communityTimeZone = $communityData[$communityIds[0]]['TimeZone'];
        $communityIds = implode(',', $communityIds);
        $bindArray = [':Active' => $active, ':BeginTime' => $beginTime, ':EndTime' => $endTime,
            ':TimeZone' => $communityTimeZone, ':Type' => $type, ':CommunityIds' => $communityIds, ':InsID' => $installerId];

        $feeType = 'MonthlyFee';
        $saveFeeType = 'MonthlySaveFee';

        // 获取待激活/续费社区住户列表
        list($renewList, $total) = $this->dao->personalAccount->getRenewCommunityUserList($bindArray, $offset, $row, $sortField, $sort);
        //过滤出落地费还是视频存储、三方锁
        $videoSites = $userData = $thirdLockData = [];
        foreach ($renewList as $item) {
            if ($item['ServiceType'] == 'VideoStorage') {
                $videoSites[] = $item['ID'];
            } else if ($item['ServiceType'] == 'ThirdLock') {
                $thirdLockData[] = $item;
            } else {
                $userData[] = $item;
            }
        }

        if ($total === 0 or empty($renewList)) {
            return ['data' => ['row' => [], 'total' => $total]];
        }
        $chargeUserData['all'] = $userData;
        $infoList = $unitIds = $roomIds = [];

        foreach ($userData as $val) {
            array_push($roomIds, $val['RoomID']);
            array_push($unitIds, $val['UnitID']);
        }
        $unitName = $unitUuidName = ['0' => '--'];
        if (!empty($unitIds)) {
            $unitArray = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityUnit'], [
                'ID', 'UUID', 'UnitName'], ['ID' => $unitIds]);
            foreach ($unitArray as $val) {
                $unitName[$val['ID']] = $val['UnitName'];
                $unitUuidName[$val['UUID']] = $val['UnitName'];
            }
        }
        $unitUUids = array_filter(array_column($thirdLockData, 'CommunityUnitUUID'));
        if (!empty($unitUUids)) {
            $unitArray = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityUnit'], [
                'ID', 'UUID', 'UnitName'], ['UUID' => $unitUUids]);
            foreach ($unitArray as $val) {
                $unitName[$val['ID']] = $val['UnitName'];
                $unitUuidName[$val['UUID']] = $val['UnitName'];
            }
        }

        $msgText = $this->share->util->getMsg()->getMsgText();

        // 获取激活/续费费用
        if (!empty($chargeUserData['all'])) {
            $userCharges = $this->services->billsysUtil->getCharge('multiple', null, $chargeUserData, $active);
        }
        // 获取视频存储费用
        $this->loadUtil('order', true);
        $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoSites, PAY_TYPE_MULTIPLE);
        $videoCharges = $this->services->billsysUtil->getVideoStorageCharge($videoStorageInfo);
        // 获取三方锁费用
        $lockChargeData = [];
        if (!empty($thirdLockData)) {
            $projectUUIDs = array_unique(array_column($thirdLockData, 'ProjectUUID'));
            $projectInfos = $this->utils->_common->account->accountSelectByArray([['UUID', $projectUUIDs]], 'ID,UUID,Location');
            $tmpChargeData = [];
            $insInfo = $this->utils->_common->account->getManagerInfoByKey('UUID', $installerUUID);
            $disInfo = $this->utils->_common->account->getManagerInfoByKey('UUID', $insInfo['ParentUUID']);
            foreach ($projectInfos as $val) {
                $tmpChargeData[] = ['Instance' => ['Distributor' => $disInfo['Account'], 'Installer' => $insInfo['Account'], 'Project' => $val['Location'], 'Type' => 1, 'ProjectUUID' => $val['UUID']]];
            }
            $thirdLockCharges = $this->services->billsysUtil->getThirdLockCharge($tmpChargeData);
            foreach ($projectInfos as $key => $val) {
                $projectInfos[$key]['Charge'] = $thirdLockCharges[$key];
            }
            $lockChargeData = array_column($projectInfos, null, 'UUID');
        }


        $this->loadUtil('account', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('itec', true);
        foreach ($renewList as $val) {
            if (!empty($unitName[$val['UnitID']])) {
                $val['UnitName'] = $unitName[$val['UnitID']];
            } else if (!empty($unitUuidName[$val['CommunityUnitUUID']])) {
                $val['UnitName'] = $unitUuidName[$val['CommunityUnitUUID']];
            } else {
                $val['UnitName'] = '--';
            }

            if ($val['Floor'] !== '' && $val['Floor'] !== null) {
                $val['RoomName'] = $val['RoomName'] . ' (' . $msgText['floor'] . ' ' . $val['Floor'] . ')';
            }
            $amount = $feeType;
            $saveFee = $saveFeeType;
            if (intval($val['Role']) === COMENDSROLE) {
                $amount = 'AddAppFee';
                $saveFee = 'AddAppSaveFee';
            }
            $bindArray = [
                'ID' => $val['ID'],
                'UUID' => $val['UUID'],
                'Role' => $val['Role'],
                'UnitName' => $val['UnitName'],
                'RoomName' => $val['RoomName'],
                'RoomNumber' => $val['RoomNumber'],
                'Email' => $val['Email'],
                'Name' => $val['Name'],
                'ProjectID' => $val['ProjectId'],
                'CommunityName' => $communityData[$val['ProjectId']]['Location'],
                'CustomizeForm' => $communityData[$val['ProjectId']]['CustomizeForm'],
                'ServiceType' => $this->utils->self->dealServiceTypeToInt($val['ServiceType'])
            ];
            if ($val['ServiceType'] == 'VideoStorage') {
                $bindArray['Amcount'] = $videoCharges[$val['UUID']]['MonthlyFee'];
            } else if ($val['ServiceType'] == 'ThirdLock') {
                // 三方锁绑定的用户信息和锁的名称需要单独查询 锁品牌类型：0=Qrio,1=Yale,2=BSI,3=Dormakaba,4=SL20,5=Salto,6=Itec,7=TT锁
                if ($val['Brand'] == '3') {
                    $bindArray['Name'] = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $val['UUID']]])[0]['Name'];
                } else if ($val['Brand'] == '6') {
                    $bindArray['Name'] = $this->utils->_common->itec->getItecLock([['UUID', $val['UUID']]])[0]['Name'];
                }
                $bindArray['ProjectID'] = $val['ProjectId'] = $lockChargeData[$val['ProjectUUID']]['ID'];
                $bindArray['CommunityName'] = $communityData[$bindArray['ProjectID']]['Location'];
                $bindArray['CustomizeForm'] = $communityData[$bindArray['ProjectID']]['CustomizeForm'];
                $bindArray['Amcount'] = $lockChargeData[$val['ProjectUUID']]['Charge']['ThirdLock'][$val['Brand']]['MonthlyFee'];
                $bindArray['SaveFee'] = 0;
                $bindArray['Introduction'] = $bindArray['ActivityId'] = null;
            } else {
                $bindArray['Amcount'] = $userCharges[$val['ID']]['MonthlyFee'];
                $bindArray['SaveFee'] = $userCharges[$val['ID']][$saveFee];
                $bindArray['Introduction'] = $userCharges[$val['ID']]['Introduction'];
                $bindArray['ActivityId'] = $userCharges[$val['ID']]['ActivityId'];
            }
            if (intval($val['Role']) === PMENDMROLE) {
                $bindArray['Email'] = $this->utils->_common->account->getManagerListByArray([['UUID', $val['Email']], ['Grade', PROPERTYMANAGE]])[0]['Email'];
                $bindArray['RoomName'] = '--';
                $bindArray['RoomNumber'] = '--';
            }

            $bindArray['TimeZoneExpireTime'] = $this->share->util->setTimeZone($val['ExpireTime'], $communityTimeZone, '3');
            $bindArray['ExpireTime'] = $this->share->util->setTimeZone($val['ExpireTime'], $communityTimeZone, $communityData[$val['ProjectId']]['CustomizeForm']);

            //V6.7.1 csc 续费列表增加时区及是否创建了订阅标识
            $bindArray['TimeZone'] = $communityTimeZone;

            array_push($infoList, $bindArray);
        }

        return ['data' => ['row' => $infoList, 'total' => $total]];
    }

    /**
     * @description: 获取社区收费计划
     * @author: cj 2022/4/24 14:05 V6.4
     * @param ID 指主账号ID
     * @param projectId 指社区
     * @param Type 支付类型
     * @return data
     * @LastEditor: cj 2022/4/24 14:05 V6.4
     */
    public function getCommunityChargeData()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['projectId'], 'Type'];
        list($id, $projectId, $type) = $this->getParams($params);
        $this->loadUtil('billingInfo', true);
        list($data, $userDataCnf) = $this->utils->_common->billingInfo->getChargeInfo($id, $projectId, $type);
        $data['amcount'] = $data['AddAppFee'];
        $this->loadUtil('common', true);
        // 主账号下全部从账号数目
        $addedAllNumber = $this->dao->personalAccount->selectByArray([['ParentID', $id],
        ['Role', COMENDSROLE]], 'count(*)')[0]['count(*)'];
        // app加入的从账号数目
        $addedNumber = $this->dao->aPPSpecial->selectByKey('Node', $userDataCnf['Account'], 'count(*)')[0]['count(*)'];
        // 高级功能家庭数目限制
        $featureExpireTime = $this->dao->communityInfo->selectByAccountID($projectId, 'FeatureExpireTime')[0]['FeatureExpireTime'];
        $featureId = $this->dao->manageFeature->selectByKey('AccountID', $projectId, 'FeatureID')[0]['FeatureID'];
        $item = $this->dao->featurePlan->selectByID($featureId, 'Item')[0]['Item'];
        $isFamilyLimit = $this->share->util->getSpecifyBitLE($item, 4);
        $sysMaxSubCount = $this->dao->systemExtremum->getSystemConfig()['MaxApps'];
        // 6.2新增家庭从账户限制
        if ($addedAllNumber >= ($sysMaxSubCount -1)) {
            $data['FamilyLimit'] = 1;
        }
        $now = $this->share->util->getNow();
        // 高级功能开启且未过期
        if ($isFamilyLimit === 1 && $this->share->util->getSpecifyBitLE($userDataCnf['Flags'], 1) === 1 && strtotime($featureExpireTime) > strtotime($now)) {
            if ($data['AppNumber'] > ($userDataCnf['AllowCreateSlaveCnt'] + 1)) {
                $data['AppNumber'] = $userDataCnf['AllowCreateSlaveCnt'] + 1;
            }
            if ($addedNumber >= $userDataCnf['AllowCreateSlaveCnt']) {
                $data['FamilyLimit'] = 1;
            }
        } elseif ($data['AppNumber'] > $sysMaxSubCount) {
            $data['AppNumber'] = $sysMaxSubCount;
        }
        return ['data' => $data];
    }

    public function getSubList()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm'];
        list($id, $timeZone, $customizeForm) = $this->getParams($params);

        $this->loadUtil('account', true);
        $users = $this->utils->_common->account->getSubList($id, $timeZone, $customizeForm);

        return ['data' => $users];
    }

    /**
     * @description: 获取社区从账号收费信息
     * @author: cj
     * @param mainUserId 主账号ID projectId installerID
     * @return data
     * @LastEditor: cj
     */
    public function getCommunitySubPay()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['projectId']];
        list($id, $projectId) = $this->getParams($params);
        $this->loadUtil('billingInfo', true);
        $data = $this->utils->_common->billingInfo->getSubPay($id, $projectId, 2);
        return ['data' => $data];
    }

    /*
     *@description 获取用户详情,区分社区和PM
     *<AUTHOR> 2022-06-07 10:41:01 V6.5
     *@lastEditor kxl 2022-06-07 10:41:01 V6.5
     *@param {*} ID 主从账户id皆可
     *@param {*} TimeZone
     *@param {*} CustomizeForm
     *@return
     */
    public function getUserInfo()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm', PROXY_ROLE['pmId']];
        list($id, $timeZone, $customizeForm, $pmId) = $this->getParams($params);
        $this->loadUtil('account', true);

        // 获取基本信息
        $userInfo = $this->utils->_common->account->getUserInfo($id, 'all');
        $userInfo = array_merge($userInfo['conf'], $userInfo['basic']);
        //InstallerApp获得原始时间
        $userInfo = $this->share->util->setOriginTime($userInfo);

        $userInfo = $this->share->util->setQueryTimeZone([$userInfo], $timeZone, $customizeForm, ['ExpireTime'])[0];
        $this->loadUtil('account', true);
        list($userInfo['ActiveName'], $userInfo['ExpireTime']) = $this->utils->_common->account->dealAccountStatus($userInfo['Active'], $userInfo['ExpireTime'],$timeZone,$customizeForm);
        $userInfo['ValidTime'] = $userInfo['ExpireTime'];

        if (COMENDSROLE === intval($userInfo['Role'])) {
            $mainData = $this->utils->_common->account->getUserInfoByUUID($userInfo['ParentUUID']);
            $mainAccount = $mainData['Account'];
            $mainUuid = $mainData['UUID'];
            $mainName = $mainData['Name'];
            $projectUuid = $mainData['ParentUUID'];
            $roomId = $mainData['RoomID'];
            $aptName = $mainData['RoomNumber'];
            $mainEmail = $mainData['Email'];
        } else {
            $mainAccount = $userInfo['Account'];
            $mainName = $userInfo['Name'];
            $mainUuid = $userInfo['UUID'];
            $projectUuid = $userInfo['ParentUUID'];
            $roomId = $userInfo['RoomID'];
            $aptName = $userInfo['RoomNumber'];
            $mainEmail = $userInfo['Email'];
        }

        $userEtraInfo = $this->dao->personalAccountCommunityInfo->selectByPersonalAccountUUID($userInfo['UUID'], 'AccessFloor,Remark');
        if (count($userEtraInfo) === 0) {
            $userInfo['AccessFloor'] = '';
        } else {
            $userInfo['AccessFloor'] = $userEtraInfo[0]['AccessFloor'] === '129' ? 'all' : $userEtraInfo[0]['AccessFloor'];
        }

        $userInfo['Remark'] = $userEtraInfo[0]['Remark'];

        $userInfo['MainUserEmail'] = $mainEmail;
        $userInfo['SipGroup'] = $this->dao->sipGroup2->selectByAccount($mainAccount, 'SipGroup')[0]['SipGroup'];
        $userInfo['FamilyMaster'] = $mainName;

        // 查询installer
        $manageGroup = $this->dao->account->selectByUUID($projectUuid, 'ManageGroup')[0]['ManageGroup'];
        $userInfo['Installer'] = $this->dao->account->selectByID($manageGroup, 'Account')[0]['Account'];

        // 获取房间信息
        $roomData = $this->dao->communityRoom->selectByID($roomId, 'RoomName,UnitID,Floor')[0];
        $floor = $roomData['Floor'];
        $msgText = $this->share->util->getMsg()->getMsgText();
        // pm且Floor不为空需要拼接
        if (!empty($pmId) && $floor !== '') {
            $userInfo['RoomName'] = $roomData['RoomName'].' ('.$msgText['floor'].' '.$floor.')';
        } else {
            $userInfo['RoomName'] = $roomData['RoomName'];
        }
        $userInfo['RoomID'] = $roomId;
        $userInfo['Floor'] = $floor;
        $userInfo['AptName'] = $aptName;

        // 获取楼栋信息
        $this->loadUtil('building');
        $buildingData = $this->utils->building->getInfo($roomData['UnitID']);
        $userInfo['UnitName'] = $buildingData['UnitName'];
        $userInfo['UnitID'] = $buildingData['ID'];
        $userInfo['WebRelayID'] = $userInfo['WebRelay'];
        $userInfo['MainAccount'] = $mainAccount;

        $this->loadUtil('device', true);
        $devicesSpecial = $this->utils->_common->device->getDevicesSpecial($mainAccount);
        $userInfo['devicesSpecial'] = count($devicesSpecial) === 0 ? '' : $devicesSpecial[0]['MAC'];
        $userInfo['IsMulti'] = $this->utils->_common->account->checkAppLinkStatus($userInfo['UUID']);
        // 获取 sequence call 相关信息
        $this->loadModel("sequence", false);
        $sequenceCallInfo = $this->models->sequence->getSequenceCallInfo($userInfo['UUID']);
        $userInfo['AptCallType'] = $sequenceCallInfo["AptCallType"]? $sequenceCallInfo["AptCallType"] : '0';
        unset($sequenceCallInfo["AptCallType"]);
        unset($sequenceCallInfo["PersonalAccountUUID"]);
        unset($sequenceCallInfo["Version"]);
        unset($sequenceCallInfo["CreateTime"]);
        $userInfo['SequenceCall']  = $sequenceCallInfo;

        return ['data' => $userInfo];
    }

    /*
     *@description ins获取房间列表
     *<AUTHOR> 2022-06-15 16:22:49 V6.5
     *@lastEditor kxl 2022-06-15 16:22:49 V6.5
     *@param {*} Active
     *@param {*} Build
     *@param {*} Status
     *@param {*} PROXY_ROLE['projectId']
     *@return
     */
    public function getRoomListByIns()
    {
        $params = ['Active', 'Build', 'Status', PROXY_ROLE['projectId']];
        list($active, $build, $status, $projectId) = $this->getParams($params);

        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $projectUUID = $projectData['UUID'];
        $timeZone = $projectData['TimeZone'];
        $customizeForm = $projectData['CustomizeForm'];
        
        $bindArray = [':ParentUUID' => $projectUUID, ':Status' => $status, ':Build' => $build, ':Active' => $active];
        list($data, $total) = $this->dao->personalAccount->getRoomListByIns($bindArray, $this->getParamsLimitAndSearch());
        $data = $this->share->util->setQueryTimeZone($data, $timeZone, $customizeForm, ["ExpireTime"]);

        $this->loadUtil('account', true);
        $rows = $data;
        $msgText = $this->share->util->getMsg()->getMsgText();
        foreach ($rows as &$val) {
            $val['Status'] = $val['Initialization'] === '1' ? $msgText['registered'] : $msgText['unregistered'];
            list($val['Active'], $val['ExpireTime']) = $this->utils->_common->account->dealAccountStatus(
                intval($val['Active']),
                $val['ExpireTime'],
                $timeZone,
                $customizeForm
            );
            $val['OriginRoomName'] = $val['RoomName'];//原始房间号
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.$msgText['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);

        return ['data' => ['total' => $total, 'row' => $rows, 'detail' => $data]];
    }

    /*
     *@description 新pm获取用户列表
     *<AUTHOR> 2022-06-15 16:24:06 V6.5
     *@lastEditor kxl 2022-06-15 16:24:06 V6.5
     *@param {*} Build
     *@param {*} Room
     *@param {*} Status
     *@param {*} Active
     *@param {*} Role
     *@param {*} TimeZone
     *@param {*} CustomizeForm
     *@param {*} PROXY_ROLE['projectId']
     *@return
     */
    public function getListByNewPm()
    {
        $params = ['Build', 'Room', 'Status', 'Active',
        'Role', 'TimeZone', 'CustomizeForm', PROXY_ROLE['projectId']];
        list($build, $room, $status, $active,
            $role, $timeZone, $customizeForm, $projectId) = $this->getParams($params);


        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $bindArray = [':ParentUUID' => $projectData['UUID'], ':Build' => $build, ':Room' => $room,
        ':Status' => $status, ':Active' => $active, ':Role' => $role];
        list($data, $total) = $this->dao->personalAccount->getListByNewPm($bindArray, $this->getParamsLimitAndSearch());
        $msgText = $this->share->util->getMsg()->getMsgText();
        foreach ($data as &$val) {
            if (strtotime($val["ExpireTime"]) < time() && $val["Active"] === '1') {
                // 表示已过期的用户
                $val["Active"] = '2';
            }
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.$msgText['floor'].' '.$val['Floor'].')';
            }
        }

        unset($val);
        $data = $this->share->util->setQueryTimeZone($data, $timeZone, $customizeForm);
        return ['data' => ["row" => $data, "detail" => $data, "total" => $total]];
    }

    /*
     *@description 新pm获取房间列表
     *<AUTHOR> 2022-06-15 16:29:04 V6.5
     *@lastEditor kxl 2022-06-15 16:29:04 V6.5
     *@param {*} Build
     *@param {*} Apt
     *@param {*} Key
     *@param {*} PROXY_ROLE['projectId']
     *@return
     */
    public function getAptListByNewPm()
    {
        $params = ['Build', 'Apt', 'Key', PROXY_ROLE['projectId']];
        list($build, $apt, $key, $projectId) = $this->getParams($params);

        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($projectId);
        $bindArray = [':ParentUUID' => $projectData['UUID'], ':Build' => $build, ':Apt' => $apt, ':Key' => $key];
        
        list($data, $total) = $this->dao->personalAccount->getAptListByNewPm($bindArray, $this->getParamsLimitAndSearch());

        // 获取社区高级功能，此处限制QRCode
        $this->loadUtil('featurePlan');
        list($featureExpireTime, $item, $featureID) = $this->utils->featurePlan->getInfo($projectId);

        $this->loadUtil('featurePlan', true);
        $featureItem = $this->utils->_common->featurePlan->checkFeaturePlan(
            $featureExpireTime,
            $item,
            $featureID
        );

        $msgText = $this->share->util->getMsg()->getMsgText();
        $this->loadUtil('communalFee');
        foreach ($data as &$row) {
            if ($row['Floor'] !== '') {
                $row['AptNumber'] = $row['AptNumber'].' ('.$msgText['floor'].' '.$row['Floor'].')';
            }
            $row['LimitCreateQRcode'] = intval($featureItem[2]);
            //家庭成员控制
            $row['LimitFamilyMember'] = intval($featureItem[3]);
            $row['FamilyMemberControl'] = $this->share->util->getSpecifyBitLE($row['Flags'], 1);
            //face
            $row['LimitRegisterFace'] = intval($featureItem[5]);
            //ID-Access
            $row['LimitCreateIDAccess'] = intval($featureItem[7]);
            // RF Card control
            $row['LimitRFCardControl'] = intval($featureItem[9]);

            $row['RegisterFaceControl'] = $this->share->util->getSpecifyBitLE($row['Flags'], 2);
            $row['EnableIpDirect'] = intval($row['EnableIpDirect']);
            $row['TempKeyPermission'] = intval($row['TempKeyPermission']);
            $row['AllowCreateIDAccess'] = $this->share->util->getSpecifyBitLE($row['Flags'], 3);
            $row['AllowCreateRfCard'] = $this->share->util->getSpecifyBitLE($row['Flags'], 4);

            if ($row['Special'] == 1) {
                $row['Residents'] = 0;
            } else {
                $row['Residents'] = $this->dao->personalAccount->selectByArray([['ParentID', $row['ID']],
                ['Role', COMENDSROLE]], 'count(*)')[0]['count(*)'] + 1;
            }
            $row['Devices'] = intval($this->dao->devices->selectByKey('Node', $row['Account'], 'count(*)')[0]['count(*)']);

            $row['CallType'] = intval($this->dao->personalAccountCnf->selectByAccount($row['Account'], 'CallType')[0]['CallType']);
            $roomCommunalFeeInfo = $this->utils->communalFee->getRoomCommunalFeeInfo($row['UUID']);
            $row['IsDefaultCommunalFee'] = $roomCommunalFeeInfo['IsDefaultCommunalFee'];
            $row['Area'] = $roomCommunalFeeInfo['Area'];
            $row['AutoSendBills'] = $roomCommunalFeeInfo['AutoSendBills'];
            $row['CommunalFee'] = $roomCommunalFeeInfo['CommunalFee'];
            $row['EnableEditCommunalFee'] = $roomCommunalFeeInfo['EnableEditCommunalFee'];
            // 获取 sequence call 相关信息
            $this->loadModel("sequence", false);
            $sequenceCallInfo = $this->models->sequence->getSequenceCallInfo($row['UUID']);
            $row['AptCallType'] = $sequenceCallInfo["AptCallType"]? $sequenceCallInfo["AptCallType"] : "0";
            unset($sequenceCallInfo["AptCallType"]);
            unset($sequenceCallInfo["PersonalAccountUUID"]);
            unset($sequenceCallInfo["Version"]);
            unset($sequenceCallInfo["CreateTime"]);
            $row['SequenceCall']  = $sequenceCallInfo;

        }
        unset($row);

        return ['data' => ['total' => $total, 'row' => $data, 'detail' => $data]];
    }


    public function getAptInfoDetailList()
    {
        $params = ['ProjectId'];
        list($projectId) = $this->getParams($params);

        $data = [];
        if (!empty($projectId)) {
            $this->loadUtil('account', true);
            $projectData = $this->utils->_common->account->getManagerInfo($projectId);

            $bindArray = [':ParentUUID' => $projectData['UUID']];
            $data = $this->dao->personalAccount->getAllAptInfoList($bindArray);
            if (count($data) > 0) {
                $msgText = $this->share->util->getMsg()->getMsgText();
                foreach ($data as &$row) {
                    if ($row['Floor'] !== '') {
                        $row['AptNumber'] = $row['AptNumber'].' ('.$msgText['floor'].' '.$row['Floor'].')';
                    }

                    if ($row['Special'] == 1) {
                        $row['Residents'] = 0;
                    } else {
                        $row['Residents'] = $this->dao->personalAccount->selectByArray([['ParentID', $row['ID']],
                                ['Role', COMENDSROLE]], 'count(*)')[0]['count(*)'] + 1;
                    }

                    $row['Devices'] = intval($this->dao->devices->selectByKey('Node', $row['Account'], 'count(*)')[0]['count(*)']);
                }
            }
        }

        return $data;
    }


    /*
     *@description 新pm获取房间详情
     *<AUTHOR> 2022-06-15 16:29:22 V6.5
     *@lastEditor kxl 2022-06-15 16:29:22 V6.5
     *@param {*} ID
     *@param {*} PROXY_ROLE['projectId']
     *@return
     */
    public function getAptInfoByNewPm()
    {
        $params = ['ID', PROXY_ROLE['projectId']];
        list($id, $projectId) = $this->getParams($params);

        $data = $this->dao->personalAccount->getAptInfoByNewPm($id);
        $msgText = $this->share->util->getMsg()->getMsgText();
        if ($data['Floor'] !== '') {
            $data['AptNumber'] = $data['AptNumber'].' ('.$msgText['floor'].' '.$data['Floor'].')';
        }
        // 获取社区高级功能，此处限制QRCode
        $this->loadUtil('featurePlan');
        list($featureExpireTime, $item, $featureID) = $this->utils->featurePlan->getInfo($projectId);

        $this->loadUtil('featurePlan', true);
        $featureItem = $this->utils->_common->featurePlan->checkFeaturePlan(
            $featureExpireTime,
            $item,
            $featureID
        );
        //前端接收的类型
        $data['CallType'] = intval($data['CallType']);
        $data['EnableIpDirect'] = intval($data['EnableIpDirect']);

        $data['TempKeyPermission'] = intval($data['TempKeyPermission']);
        $data['LimitCreateQRcode'] = intval($featureItem[2]);
        $data['LimitFamilyMember'] = intval($featureItem[3]);
        $data['FamilyMemberControl'] = $this->share->util->getSpecifyBitLE($data['Flags'], 1);
        //face
        $data['LimitRegisterFace'] = intval($featureItem[5]);
        $data['RegisterFaceControl'] = $this->share->util->getSpecifyBitLE($data['Flags'], 2);
        $data['AllowCreateSlaveCnt'] = intval($data['AllowCreateSlaveCnt']);
        $data['AllowCreateIDAccess'] = $this->share->util->getSpecifyBitLE($data['Flags'], 3);
        $data['AllowCreateRfCard'] = $this->share->util->getSpecifyBitLE($data['Flags'], 4);

        $this->loadUtil('communalFee');
        $roomCommunalFeeInfo = $this->utils->communalFee->getRoomCommunalFeeInfo($data['UUID']);
        $data['Area'] = $roomCommunalFeeInfo['Area'];
        $data['FinalCommunalFee'] = $roomCommunalFeeInfo['FinalCommunalFee'];
        $data['CommunalFee'] = $roomCommunalFeeInfo['CommunalFee'];
        $data['AutoSendBills'] = $roomCommunalFeeInfo['AutoSendBills'];
        $data['IsDefaultCommunalFee'] = $roomCommunalFeeInfo['IsDefaultCommunalFee'];
        $data['EnableEditCommunalFee'] = $roomCommunalFeeInfo['EnableEditCommunalFee'];
        if (empty($roomCommunalFeeInfo['EffectTime'])) {
            $data['ExpireTime'] = ''; // 为了前端能计算正确的价格展示
        } else {
            $data['ExpireTime'] = $roomCommunalFeeInfo['ExpireTime'];
        }
        // 获取 sequence call 相关信息
        $this->loadModel("sequence", false);
        $sequenceCallInfo = $this->models->sequence->getSequenceCallInfo($data['UUID']);
        $data['AptCallType'] = $sequenceCallInfo["AptCallType"]? $sequenceCallInfo["AptCallType"] : "0";
        unset($sequenceCallInfo["AptCallType"]);
        unset($sequenceCallInfo["PersonalAccountUUID"]);
        unset($sequenceCallInfo["Version"]);
        unset($sequenceCallInfo["CreateTime"]);
        $data['SequenceCall']  = $sequenceCallInfo;

        return ['data' => $data];
    }

    /*
     *@description 获取家庭下所有用户
     *<AUTHOR> 2022-06-15 16:21:14 V6.5
     *@lastEditor kxl 2022-06-15 16:21:14 V6.5
     *@param {*} ID 主账户ID
     *@param {*} TimeZone
     *@param {*} CustomizeForm
     *@return array
     */
    public function getFamilyApp()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm'];
        list($id, $timeZone, $customizeForm) = $this->getParams($params);

        $this->loadUtil('account', true);
        $users = $this->utils->_common->account->getFamilyUsers($id);

        foreach ($users as &$value) {
            if ($value['Active'] === '0') {
                $value['Mark'] = 'Inactivated';
            } elseif (strtotime($value['ExpireTime']) > time()) {
                $value['Mark'] = 'Activation';
            } else {
                $value['Mark'] = 'Expired';
            }
        }
        unset($value);
        $users = $this->share->util->setQueryTimeZone($users, $timeZone, $customizeForm);

        return ['data' => $users];
    }

    /**
     * @description: PM获取新小区详情
     * @author: cj 2022/10/18 16:19 V6.5.2
     * @param ID 指用户ID(包括主从)
     * @param TimeZone、CustomizeForm 指社区时区时制
     * @param pmId 指pmId
     * @return array
     * @LastEditor: cj 2022/10/18 16:19 V6.5.2
     */
    public function getInfoForNewPm()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm', PROXY_ROLE['pmId']];
        list($id, $timeZone, $customizeForm, $pmId) = $this->getParams($params);
        $this->loadModel('user', false, ['dataContainer' => [
            'ID' => $id,
            'TimeZone' => $timeZone,
            'CustomizeForm' => $customizeForm,
            PROXY_ROLE['pmId'] => $pmId
        ]]);
        $data = $this->models->user->getUserInfo()['data'];
        $msgText = $this->share->util->getMsg()->getMsgText();
        $data['InitializationName'] = [$msgText['unregistered'], $msgText['registered']][$data['Initialization']];
        $this->loadUtil('account', true);
        //调用getUserInfo已经转换过时区和获取过ActiveName了
        $this->loadUtil('common', true);
        $data['Device'] = $this->dao->devices->selectByKey('Node', $data['MainAccount'], 'ID,MAC,Location,Type');
        $data['HasAccessDevice'] = '0';
        foreach ($data['Device'] as $val) {
            if (in_array($val['Type'], OUT_DOOR_DEVICE)) {
                $data['HasAccessDevice'] = '1';
                break;
            }
        }
        return ['data' => $data];
    }


    /**
     * @description: 获取super/dis的终端用户列表
     * @param role all/familyMaster/familyMember
     * @author: cj 2022/10/19 9:32 V6.5.2
     * @return {*}
     * @LastEditor: cj 2022/10/19 9:32 V6.5.2
     */
    public function getListForManage()
    {
        $params = ['SelfCustomizeForm', 'SelfTimeZone', 'role', PROXY_ROLE['distributorId'], PROXY_ROLE['subDistributorId']];
        list($customizeForm, $timeZone, $role, $disId, $subDisId) = $this->getParams($params);
        if (empty($disId)) {
            //super
            $userId = '';
        } elseif (empty($subDisId)) {
            //dis
            $userId = $disId;
        } else {
            //sub dis
            $userId = $subDisId;
        }
        list($data, $total) = $this->dao->personalAccount->getListForManage($role, $userId, $this->getParamsLimitAndSearch());
        $data = $this->share->util->setQueryTimeZone($data, $timeZone, $customizeForm);
        $this->loadUtil('account', true);
        foreach ($data as &$value) {
            list($value['Active'], $value['ExpireTime']) = $this->utils->_common->account->dealUserActiveStatus($value['Active'], $value['ExpireTime']);
            $subDisUUID = $this->dao->subDisMngList->selectByInstallerUUID($value['InstallerUUID'], 'DistributorUUID')[0]['DistributorUUID'];
            $value['SubDis'] = $subDisUUID === null ? '--':$this->dao->account->selectByUUID($subDisUUID, 'Account')[0]['Account'];
        }
        unset($value);

        return ['data' => ['total' => $total, 'row' => $data, 'detail' => $data]];
    }

    /**
     * @description: sup获取社区用户列表
     * @param: {type}
     * @return array[]
     * @author: shoubin.chen 2024-04-03 11:31:53 v6.8.0
     * @lastEditor: shoubin.chen 2024-04-03 11:31:53 v6.8.0
     */
    public function getListForSup()
    {
        $params = ['SelfCustomizeForm', 'SelfTimeZone', 'role', PROXY_ROLE['distributorId'], PROXY_ROLE['subDistributorId'], "DisUUID", "SubDisUUID", "InsUUID", "Active", "ExpireTimeBegin", "ExpireTimeEnd", 'CreateBeginTime', 'CreateEndTime'];
        list($customizeForm, $timeZone, $role, $disId, $subDisId, $disUUID, $subDisUuid, $insUUID, $active, $expireTimeBegin, $expireTimeEnd, $createBeginTime, $createEndTime) = $this->getParams($params);
        if (empty($disId)) {
            //super
            $userId = '';
        } elseif (empty($subDisId)) {
            //dis
            $userId = $disId;
        } else {
            //sub dis
            $userId = $subDisId;
        }

        $superSearch = [
            "DisUUID" => empty($disUUID) ? null : $disUUID,
            "SubDisUUID" => empty($subDisUuid) ? null : $subDisUuid,

            "InsUUID" => empty($insUUID) ? null : $insUUID,
            "Active" => !is_numeric($active) ? null : (int)$active,
            "ExpireTimeBegin" => empty($expireTimeBegin) ? null : $expireTimeBegin,
            "ExpireTimeEnd" => empty($expireTimeEnd) ? null : $expireTimeEnd,
            'CreateStartTime' => empty($createBeginTime) ? null : $createBeginTime,
            'CreateEndTime' => empty($createEndTime) ? null : $createEndTime
        ];

        list($data, $total) = $this->dao->personalAccount->getListForManage($role, $userId, $this->getParamsLimitAndSearch(), $superSearch);
        $data = $this->share->util->setQueryTimeZone($data, $timeZone, $customizeForm);
        $this->loadUtil('account', true);
        foreach ($data as &$value) {
            list($value['Active'], $value['ExpireTime']) = $this->utils->_common->account->dealUserActiveStatus($value['Active'], $value['ExpireTime']);
            $subDisUUID = $this->dao->subDisMngList->selectByInstallerUUID($value['InstallerUUID'], 'DistributorUUID')[0]['DistributorUUID'];
            $value['SubDistributor'] = $subDisUUID === null ? '--':$this->dao->account->selectByUUID($subDisUUID, 'Account')[0]['Account'];
            //匹配dis
            $disUUID = $this->utils->_common->account->accountSelectByKey('UUID',$value['InstallerUUID'],'ParentUUID')[0]['ParentUUID'];
            $value['Distributor'] = $this->utils->_common->account->getManagerInfoUUID($disUUID)['LoginAccount'];
        }
        unset($value);

        return ['data' => ['total' => $total, 'row' => $data, 'detail' => $data]];
    }

    /**
     * @description: pm获取小区下楼栋，房间，用户等信息
     * @param projectId 指社区ID
     * @author: cj 2022/10/19 20:09 V6.5.2
     * @return {*}
     * @LastEditor: cj 2022/10/19 20:09 V6.5.2
     */
    public function getRoomResidentForPm()
    {
        $params = [PROXY_ROLE['projectId']];
        list($communityId) = $this->getParams($params);
        $builds = $this->db->queryAllList(PROXY_TABLES['communityUnit'], ['equation' => [':MngAccountID' => $communityId]]);
        $rooms = [];
        $users = [];
        if (count($builds) > 0) {
            $this->loadUtil('common', true);
            $allRooms = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityRoom'], '*', ['UnitID' => array_column($builds, 'ID')]);
            $allUsers = $this->dao->personalAccount->getAptUserListByNewPm($communityId);
            foreach ($allRooms as $val) {
                if (!array_key_exists($val['UnitID'], $rooms)) {
                    $rooms[$val['UnitID']] = [];
                }
                $temp = $val;
                foreach ($allUsers as $val2) {
                    if (intval($val2['Role']) === COMENDMROLE && $val2['RoomID'] === $val['ID']) {
                        $temp["RoomNumber"] = $val2['RoomNumber'];
                    }
                }
                array_push($rooms[$val['UnitID']], $temp);
            }
            foreach ($allUsers as $val) {
                if ($val['Special'] === '0') {
                    if (!array_key_exists($val['RoomID'], $users)) {
                        $users[$val['RoomID']] = [];
                    }
                    array_push($users[$val['RoomID']], ['ID' => $val['ID'], 'Name' => $val['Name']]);
                }
            }
        }
        return ['data' => ['build' => $builds, 'room' => $rooms, 'user' => $users]];
    }

    public function getListForMessage()
    {
        $params = [PROXY_ROLE['projectId'], 'Key', 'Build'];
        list($projectId, $key, $build) = $this->getParams($params);
        $key = $key ?: '';
        $build = $build ?: 'all';

        $data = $this->dao->personalAccount->getListForMessage($projectId, $key, $build);
        $msgText = $this->share->util->getMsg()->getMsgText();

        foreach ($data as &$val) {
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.$msgText['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);
        return ['data' => $data];
    }

    /**
     * @description:用户获取用户信息
     * @author:lwj 2023-02-13 14:49:53 V6.6
     * @lastEditor:lwj 2023-02-13 14:49:53 V6.6
     * @param:{int} ID 主、从、pmapp皆可
     * @return mixed
     */
    public function getUserInfoForApp()
    {
        $params = ['ID', PROXY_ROLE['distributorId']];
        list($id, $disId) = $this->getParams($params);
        $this->loadUtil('account', true);
        //获取基本信息
        $userInfo = $this->utils->_common->account->getUserInfo($id);

        $this->loadUtil('common', true);
        $communityId = $this->utils->_common->common->getProjectID($userInfo['ID']);
        // 获取小区信息
        $community = $this->utils->_common->common->getAccountInfoWithID(
            $communityId,
            ["TimeZone", "CreateTime", "CustomizeForm", "ManageGroup"]
        );
        $userInfo['CustomizeForm'] = $community['CustomizeForm'];
        
        $func = [
            COMENDMROLE => 'getMainUserInfoForApp',
            COMENDSROLE => 'getSubUserInfoForApp',
            PMENDMROLE => 'getPmUserInfoForApp',
        ];
        $userInfo = $this->callSelfFunc($func[$userInfo['Role']], [$userInfo]);
        $userInfo['IsMulti'] = $this->utils->_common->account->checkAppLinkStatus($userInfo['UUID']);

        $userInfo['AllowCreateRfCard'] = true;
        //对于社区主用户，需要增加判断
        if ($userInfo['Role'] != PMENDMROLE ) {
            $this->loadUtil('manage', true);
            $isEnableRfCardControl = $this->utils->_common->manage->getDisInfo($disId)['info']['IsEnableRfCardControl'];

            if ($userInfo['Role'] == COMENDMROLE) {
                $flags = $this->dao->personalAccountCnf->selectByKey('Account',  $userInfo['Account'], 'Flags')[0]['Flags'];
            } else {
                $mainUserInfo = $this->dao->personalAccount->selectByID($userInfo['ParentID'])[0];
                $flags = $this->dao->personalAccountCnf->selectByKey('Account',  $mainUserInfo['Account'], 'Flags')[0]['Flags'];
            }

            if ($isEnableRfCardControl === '0') {
                $userInfo['AllowCreateRfCard'] = false;
            } else if ($isEnableRfCardControl === '1'){
                // 如果Dis有打开rfCard的开关，则再判断当前用户的房间，PM是否有打开rf Card的权限
                $allowRFCard = $this->share->util->getSpecifyBitLE($flags, 4);
                if ($allowRFCard == '0') {
                    $userInfo['AllowCreateRfCard'] = false;
                }
            }

            $allowRecordFace = $this->share->util->getSpecifyBitLE($flags, 2);
            $userInfo['PmFaceController'] = !($allowRecordFace == '0');
        }

        return ['data' => $userInfo];
    }

    /**
     * @description:主账号获取用户信息
     * @author:lwj 2023-02-13 14:49:53 V6.6
     * @lastEditor:lwj 2023-02-13 14:49:53 V6.6
     * @param:{array} UserInfo 主账号info
     * @return mixed
     */
    public function getMainUserInfoForApp()
    {
        $params = ['UserInfo'];
        list($userInfo) = $this->getParams($params);
        $this->loadUtil('communityRoom', true);
        $roomData = $this->utils->_common->communityRoom->getRoomInfoByKey('ID', $userInfo['RoomID']);
        $userInfo['RoomNumber'] = $roomData['RoomName'];
        if ($roomData['Floor'] !== '') {
            $msgText = $this->share->util->getMsg()->getMsgText();
            $userInfo['RoomNumber'] = $roomData['RoomName'].' ('.$msgText['floor'].' '.$roomData['Floor'].')';
        }
        $this->loadUtil('communityData');
        $projectInfo = $this->utils->communityData->getCommunityInfoByKey('AccountID', $userInfo['ParentID']);
        $userInfo['EnableLandline'] = $this->share->util->getSpecifyBit($projectInfo['Switch'], 0);
        $userInfo['EnableSmartHome'] = $this->share->util->getSpecifyBitLE($projectInfo['Switch'], 5);
        return $userInfo;
    }

    /**
     * @description:从账号获取用户信息
     * @author:lwj 2023-02-13 14:49:53 V6.6
     * @lastEditor:lwj 2023-02-13 14:49:53 V6.6
     * @param:{array} UserInfo 主账号info
     * @return mixed
     */
    public function getSubUserInfoForApp()
    {
        $params = ['UserInfo'];
        list($userInfo) = $this->getParams($params);
        $this->loadUtil('account', true);
        $mainData = $this->utils->_common->account->getUserInfoByUUID($userInfo['ParentUUID']);
        $userInfo['MainUserEmail'] = $mainData['Email'];
        $userInfo['MainUserPhone'] = $mainData['Phone'];
        $this->loadUtil('communityRoom', true);
        $roomData = $this->utils->_common->communityRoom->getRoomInfoByKey('ID', $mainData['RoomID']);
        $userInfo['RoomNumber'] = $roomData['RoomName'];
        if ($roomData['Floor'] !== '') {
            $msgText = $this->share->util->getMsg()->getMsgText();
            $userInfo['RoomNumber'] = $roomData['RoomName'].' ('.$msgText['floor'].' '.$roomData['Floor'].')';
        }
        $this->loadUtil('communityData');
        $projectInfo = $this->utils->communityData->getCommunityInfoByKey('AccountID', $mainData['ParentID']);
        $userInfo['EnableLandline'] = $this->share->util->getSpecifyBit($projectInfo['Switch'], 0);
        $userInfo['EnableSmartHome'] = $this->share->util->getSpecifyBitLE($projectInfo['Switch'], 5);
        return $userInfo;
    }

    /**
     * @description:pm账号获取用户信息
     * @author:lwj 2023-02-13 14:49:53 V6.6
     * @lastEditor:lwj 2023-02-13 14:49:53 V6.6
     * @param:{array} UserInfo 主账号info
     * @return mixed
     */
    public function getPmUserInfoForApp()
    {
        $params = ['UserInfo'];
        list($userInfo) = $this->getParams($params);
        // 如果是pmapp的用户，需要取对应pm的邮箱
        $pmAccountMapTable = PROXY_TABLES['pmAccountMap'];
        $pmMap = $this->db->querySList(
            "select * from $pmAccountMapTable where PersonalAccountUUID = :PersonalAccountUUID",
            [':PersonalAccountUUID' => $userInfo['UUID']]
        )[0];
        $this->loadUtil('account', true);
        $pmAccountData = $this->utils->_common->account->getManagerInfoUUID($pmMap['AccountUUID']);
        $userInfo['Email'] = $pmAccountData['Email'];

        $this->loadUtil('communityData');
        $projectInfo = $this->utils->communityData->getCommunityInfoByKey('AccountID', $userInfo['ParentID']);
        $userInfo['EnableLandline'] = $this->share->util->getSpecifyBit($projectInfo['Switch'], 0);
        return $userInfo;
    }

    /**
     * @description:获取主账号的配置信息
     * @author:lwj 2023-04-10 09:41:55 V6.6
     * @lastEditor:lwj 2023-04-10 09:41:55 V6.6
     * @param:
     * @return array
     */
    public function getUserCnf()
    {
        $params = [PROXY_ROLE['mainUserId']];
        list($userId) = $this->getParams($params);
        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getUserInfo($userId, 'all');
        $data = [
            'EnableRobinCall' => $userData['conf']['EnableRobinCall'],
            'RobinCallVal' => $userData['conf']['RobinCallVal'],
            'RobinCallTime' => $userData['conf']['RobinCallTime'],
        ];
        return ['data' => $data];
    }

    /**
     * @description:获取主账号创建tempKey的权限
     * @author:lwj 2023-04-23 16:41:55 V6.6
     * @lastEditor:lwj 2023-04-23 16:41:55 V6.6
     * @param:
     * @return array
     */
    public function getTempKeyPermission()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['projectId']];
        list($userId, $projectId) = $this->getParams($params);
        $this->loadUtil('account', true);
        $userInfo = $this->utils->_common->account->getUserInfo($userId);
        $tempKeyPermission = $userInfo['TempKeyPermission'];
        $now = $this->share->util->getNow();
        $this->loadUtil('communityData');
        $communityInfo = $this->utils->communityData->getCommunityInfoByKey('AccountID', $projectId);

        // 高级功能是否开启tmpkey权限控制，旧小区由于没有高级功能，FeatureID是0，因此查出的item是null，经过getSpecifyBitLE转换是0
        $featureId = $this->dao->manageFeature->selectByKey('AccountID', $projectId, 'FeatureID')[0]['FeatureID'];
        $item = $this->dao->featurePlan->selectByID($featureId, 'Item')[0]['Item'];
        $isTempKeyLimit = $this->share->util->getSpecifyBitLE($item, 3);

        if($isTempKeyLimit === 0 || strtotime($communityInfo['FeatureExpireTime']) < strtotime($now)){
            $tempKeyPermission = '1';
        }
        return ['data' => ['TempKeyPermission' => $tempKeyPermission]];
    }

    /**
     * @description:获取用户的电话信息
     * @author:lwj 2023-04-10 18:20:48 V6.6
     * @lastEditor:lwj 2023-04-10 18:20:48 V6.6
     * @param:
     * @return array
     */
    public function getUserPhoneData()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['subUserId']];
        list($mainUserId, $subUserId) = $this->getParams($params);
        $userId = empty($subUserId) ? $mainUserId : $subUserId;
        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getUserInfo($userId);
        $data = [
            'PhoneCode' => $userData['PhoneCode'],
            'Phone' => $userData['Phone'],
        ];
        return ['data' => $data];
    }

    /**
     * @description:获取用户信息
     * @author:lwj 2023-04-11 11:19:47 V6.6
     * @lastEditor:lwj 2023-04-11 11:19:47 V6.6
     * @param:
     * @return array
     */
    public function getUserInfoData()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['subUserId'], PROXY_ROLE['projectId']];
        list($mainUserId, $subUserId, $projectId) = $this->getParams($params);
        $userId = empty($subUserId) ? $mainUserId : $subUserId;
        $this->loadUtil('account', true);
        //获取基本信息
        $mainUserData = $this->utils->_common->account->getUserInfo($mainUserId, 'all');
        $mainBasicData = $mainUserData['basic'];
        $mainConfData = $mainUserData['conf'];
        $this->loadUtil('communityRoom', true);
        $roomName = $this->utils->_common->communityRoom->getRoomInfoByKey('ID', $mainBasicData['RoomID'])['RoomName'];
        $data = [
            'FirstName' => $mainBasicData['FirstName'],
            'LastName' => $mainBasicData['LastName'],
            'Name' => $mainBasicData['Name'],
            'SipAccount' => $mainBasicData['SipAccount'],
            'Phone' => $mainBasicData['Phone'],
            'Phone2' => $mainBasicData['Phone2'],
            'Phone3' => $mainBasicData['Phone3'],
            'PhoneCode' => $mainBasicData['PhoneCode'],
            'Email' => $mainBasicData['Email'],
            'TimeZone' => $mainBasicData['TimeZone'],
            'RoomName' => $roomName,
            'CallType' => $mainConfData['CallType']
        ];
        // V6.5 兼容app从账号初始化Pin的欢迎用户
        $userInfo = $this->utils->_common->account->getUserInfo($userId);
        if ($userInfo['Role'] === strval(COMENDSROLE)) {
            $data['FirstName'] = $userInfo['FirstName'];
            $data['LastName'] = $userInfo['LastName'];
            $data['Name'] = $userInfo['Name'];
            $data['Email'] = $userInfo['Email'];
        }
        $this->utils->_common->account->getManagerInfo($projectId);
        $communityInfo = $this->dao->communityInfo->selectByAccountID($projectId)[0];
        if($communityInfo['IsNew'] === '0'){
            $where = [['AccountID', $userId], ['Special',1]];
            $keyData = $this->dao->personalPrivateKey->selectByArray($where);
        }else{
            $where = [['Account', $userInfo['Account']], ['Special',1]];
            $keyData = $this->dao->commPerPrivateKey->selectByArray($where);
        }
        $data['Code'] = count($keyData) === 0 ? '' : $keyData[0]['Code'];
        return ['data' => $data];
    }

    /**
     * @description: 获取指定楼栋下的所有房间
     * @param: {string} BuildUUID 楼栋UUID
     * @return array[]
     * @author: shoubin.chen 2024-03-29 18:23:19 v6.8.0
     * @lastEditor: shoubin.chen 2024-03-29 18:23:19 v6.8.0
     */
    public function getAllRoomByManager()
    {
        $params = ['BuildUUID:uuid'];
        list($buildUUID) = $this->getParams($params);
        //校验build
        $this->loadUtil('building');
        $build = $this->utils->building->checkBuildExist('UUID', $buildUUID, '*');
        //获取build下所有房间
        $searchArray = [['UnitID', $build['ID']], ['Role', COMENDMROLE]];
        $this->loadUtil('account', true);
        $roomList = $this->utils->_common->account->personalAccountSelectByArray($searchArray, 'ID,UUID,Account,RoomNumber,RoomID');
        $data = [];

        foreach ($roomList as $item) {
            $tmp['ID'] = $item['ID'];
            $tmp['UUID'] = $item['UUID'];
            $info = $this->utils->building->getBuildApt($item['Account']);
            $tmp['AptNumber'] = $info['RoomName'];
            $tmp['AptName'] = $item['RoomNumber'];
            $data[] = $tmp;
        }

        return ['data' => $data];
    }

    /**
     * @description:获取住户列表
     * @param: {type}
     * @return array[]
     * @author: shoubin.chen 2024-04-03 15:16:15 v6.8.0
     * @lastEditor: shoubin.chen 2024-04-03 15:16:15 v6.8.0
     */
    public function getResidentList()
    {
        $params = [PROXY_ROLE_CHECK['projectId'], 'Build:string', 'Room:string', 'Status:enum("all","0","1")', 'SelfTimeZone', 'SelfCustomizeForm'];
        list($projectID, $build, $room, $status, $timeZone, $customizeForm) = $this->getParams($params);

        list($offset, $rows, $searchKey, $searchValue,$sortField, $sort) = $this->getParamsLimitAndSearch();
        $where = "";
        $bindArray = [":ParentID" => $projectID];
        switch ($searchKey) {
            case 'Name':
                $where.= ' and md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key)';
                $bindArray[':Key'] = "%$searchValue%";
                break;
            case 'Email':
                $where .= " and md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key)";
                $bindArray[":Key"] = "%$searchValue%";
                break;
            case 'Phone':
                $where = " and md5(P.Phone) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :Key)";
                $bindArray[":Key"] = "%$searchValue%";
                break;
            case 'Room':
                $where = " and R.RoomName like :Key";
                $bindArray[":Key"] = "%$searchValue%";
                break;
            default:
                break;
        }

        if ($build != "all") {
            $where .= " and P.UnitID = :UnitID";
            $bindArray[":UnitID"] = $build;
        }

        if ($room != "all") {
            $where .= " and P.RoomID = :RoomID";
            $bindArray[":RoomID"] = $room;
        }

        if ($status != "all") {
            $where .= " and P.Initialization = :Initialization";
            $bindArray[":Initialization"] = $status;
        }

        $orderBySql = ' order by P.CreateTime desc,P.ID desc';
        if (empty($sort)){
            $sort = "asc";
        }
        if(in_array($sortField, ['RoomName', 'RoomNumber', 'Name']) && in_array($sort, ['asc', 'desc'])) {
            $orderBySql = " order by P.$sortField $sort,P.ID desc";

            if($sortField === 'RoomNumber'){
                //房间号
                $orderBySql = " order by CONVERT(R.RoomName, UNSIGNED) $sort,P.ID desc";
            }else if($sortField === 'RoomName') {
                //房间名
                $orderBySql = " order by P.RoomNumber $sort,P.ID desc";
            }else if($sortField === 'Name') {
                //房间名
                $orderBySql = " order by DeColumn $sort,P.ID desc";
            }

        }

        $field = "P.ID,P.RoomNumber,PU.MobileNumber,P.Name,PU.Email,P.ExpireTime,P.Phone,P.Initialization,P.Active,P.ActiveTime,P.CreateTime,R.RoomName,R.Floor,U.UnitName,ANM.DeColumn";
        $table = "PersonalAccount P join CommunityUnit U on P.UnitID = U.ID join CommunityRoom R on P.RoomID = R.ID
                left join PersonalAccountUserInfo PU on PU.UUID = P.UserInfoUUID
                left join AKCSMapping.NameMapping ANM on ANM.EnColumn = P.Name and ANM.EnColumnMd5 = md5(P.Name)";
        $where = "P.ParentID = :ParentID and Role = 20 and Special = 0 $where ";

        $total = $this->db->querySList("select count(*) from $table where $where",$bindArray)[0]["count(*)"];
        $data = $this->db->querySList("SELECT $field FROM $table WHERE $where $orderBySql limit $offset,$rows",$bindArray);


        $msgText = $this->share->util->getMsg()->getMsgText();

        $register = ["0" => $msgText["unregistered"], "1" => $msgText["registered"]];
        $actives = [$msgText["inactivated"], $msgText["normal"], $msgText["expired"]];

        foreach ($data as &$val) {
            $expritime = strtotime($val["ExpireTime"]);
            if ($val["Active"] == 1 && time() > $expritime) {
                $val["Active"] = 2;
            }
            $val["Active"] = $actives[$val["Active"]];
            $val["Initialization"] = $register[$val["Initialization"]];
            $val["SubUsers"] = $this->db->querySList(
                "select PU.Email from PersonalAccount P left join PersonalAccountUserInfo PU on PU.UUID = P.UserInfoUUID where ParentID = :ID and Role = 21",
                [":ID" => $val["ID"]]
            );
            $val["SubCount"] = count($val["SubUsers"]);
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.$msgText['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);
        $data = $this->share->util->setQueryTimeZone($data, $timeZone, $customizeForm);

        //数据解密
        foreach ($data as $key => $item) {
            $data[$key] = $this->dao->personalAccount->dataArrDecode($item, ['Email', 'Name', 'Phone', 'MobileNumber']);
        }

        return ['data' => ['total' => $total, 'row' => $data, 'detail' => $data]];
    }

    /**
     * @description: 获得楼栋下的房间列表
     * @param: BuildUUID 楼栋UUID
     * @param: Sort 排序
     * @return mixed
     * @author: shoubin.chen 2024/6/11 14:22:50 V6.8.0
     * @lastEditor: shoubin.chen 2024/6/11 14:22:50  V6.8.0
     */
    public function getBuildAptAndSort()
    {
        $params = ['BuildUUID:uuid', 'Sort:enum("asc","desc")'];
        list($buildUUID, $sort) = $this->getParams($params);
        $bindArray[':CommunityUnitUUID'] = $buildUUID;
        //先按RoomNumber排序，然后按RoomName排序
        $sql = "
            SELECT PA.ID,PA.UUID,PA.RoomNumber,CR.RoomName
            FROM PersonalAccount PA
            JOIN CommunityRoom CR
            ON PA.CommunityRoomUUID = CR.UUID
            WHERE PA.CommunityUnitUUID = :CommunityUnitUUID
            ORDER BY 
                 CASE
                    WHEN RoomNumber != '' THEN RoomNumber
                    ELSE RoomName
                 END $sort;
        ";
        $data = $this->db->querySList($sql, $bindArray);
        //数据解密
        foreach ($data as $key => $item) {
            $data[$key] = $this->dao->personalAccount->dataArrDecode($item, ['Email', 'Name', 'Phone', 'MobileNumber']);
        }
        return $data;
    }

    public function getBuildApt()
    {
        $params = ['BuildUUID:uuid', 'Field'];
        list($buildUUID, $field) = $this->getParams($params);
        $bindArray[':CommunityUnitUUID'] = $buildUUID;
        $field = empty($field) ? "PA.ID,PA.UUID,PA.RoomNumber as RoomName,CR.RoomName as RoomNumber" : $field;
        $sql = "
            SELECT $field
            FROM PersonalAccount PA
            JOIN CommunityRoom CR
            ON PA.CommunityRoomUUID = CR.UUID
            WHERE PA.CommunityUnitUUID = :CommunityUnitUUID
        ";
        $data = $this->db->querySList($sql, $bindArray);
        //数据解密
        foreach ($data as $key => $item) {
            $data[$key] = $this->dao->personalAccount->dataArrDecode($item, ['Email', 'Name', 'Phone', 'MobileNumber']);
        }
        return $data;
    }

    /**
     * @description: 获得社区所有房间
     * @param: CommunityUUID
     * @author: shoubin.chen 2024/7/11 11:49:47 V6.8.1
     * @lastEditor: shoubin.chen 2024/7/11 11:49:47  V6.8.1
     */
    public function getCommunityAllRoom()
    {
        $params = ['CommunityUUID:uuid'];
        list($buildUUID) = $this->getParams($params);

        $bindArray[':CommunityUnitUUID'] = $buildUUID;

        $sql = "
            SELECT PA.ID,PA.UUID,PA.CommunityUnitUUID,PA.UnitID,PA.RoomNumber,CR.RoomName,'Apt'
            FROM PersonalAccount PA
            JOIN CommunityRoom CR
            ON PA.CommunityRoomUUID = CR.UUID
            WHERE PA.ParentUUID = :CommunityUnitUUID;
        ";
        $data = $this->db->querySList($sql, $bindArray);
        //数据解密
        foreach ($data as $key => $item) {
            $data[$key] = $this->dao->personalAccount->dataArrDecode($item, ['Email', 'Name', 'Phone', 'MobileNumber']);
        }
        return $data;
    }



    public function getUserAllList()
    {
        $params = [PROXY_ROLE_CHECK['projectUUID'],'Building?:uuid','Room?:uuid'];
        list($projectUUID,$buildingUUID,$roomUUID) = $this->getParams($params);
        $uuidList=[]; // 用户的uuid
        if (empty($roomUUID)){
            // 查询楼栋下的所有房间，不能包括空房间
            $bindArray=[['CommunityUnit',$buildingUUID],['ParentUUID',$projectUUID],['Special',0]];
            $infoList=$this->dao->personalAccount->selectByArray($bindArray);
            $uuidList=array_column($infoList,"UUID");
        }else{
            $bindArray=[['UUID',$roomUUID],['ParentUUID',$projectUUID],['Special',0]];
            $infoList=$this->dao->personalAccount->selectByArray($bindArray);
            $uuidList=array_column($infoList,"UUID");
        }

        // 查询房间下的所有人员
        $data=[];
        foreach($uuidList as $uuid){
           $info=$this->dao->personalAccount->getCommunityFamilyUsers($uuid);
            foreach($info as $item){
                $data[]=["Name"=>$item['Name'],"UUID"=>$item['UUID']];
            }
        }
        return ['data'=>$data];
    }
}
