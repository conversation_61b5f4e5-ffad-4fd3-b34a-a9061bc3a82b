<?php
require_once(dirname(__FILE__).'/../../config/dynamic_config.php');
const FACE_FILE_PREFIX = '/var/www/download/face';
const FACE_FDFS_GROUP = 'group2';
function storage_pic_to_fdfs($faceFilePath, $beforePath)
{
    $ret = false;
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker) {
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store(FACE_FDFS_GROUP);
    if (!$storage) {
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server) {
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    if ($beforePath) {
        $beforeFile = explode('/', $beforePath, 3); //拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg
        if (strstr($beforeFile[1], 'group')) {  //如果存在fdfs的图片链接，则对应删除
            fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);    //param1:group param2:path
        }
    }
    
    $file_info = fastdfs_storage_upload_by_filename($faceFilePath, null, array(), null, $tracker, $storage);
    if ($file_info) {
        $group_name = $file_info['group_name'];
        $remote_filename = $file_info['filename'];
        $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}
function getDB()
{
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = DATABASEIP;
    $dbport = DATABASEPORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}
function uploadFaceMng($oldFaceUrl)
{
    $db = getDB();
    try {
        $sth = $db->prepare("select ID from FaceMng where FaceUrl = :FaceUrl");
        $sth->bindParam(':FaceUrl', $oldFaceUrl, PDO::PARAM_STR);
        $sth->execute();
        $data = $sth->fetch(PDO::FETCH_ASSOC);
        $fdfs_ret = '';
        if ($data) {
            $fdfs_ret = storage_pic_to_fdfs(FACE_FILE_PREFIX.$oldFaceUrl, $data['FaceUrl']);
            if ($fdfs_ret) {
                $sth = $db->prepare("update FaceMng set FaceUrl = :FaceUrl where ID = :ID");
                $sth->bindParam(':FaceUrl', $fdfs_ret, PDO::PARAM_STR);
                $sth->bindParam(':ID', $data['ID'], PDO::PARAM_INT);
                $sth->execute();
            }
        } else {
            echo "FaceUrl error\n";
        }
    } catch (PDOException $e) {
        echo "db exception=" . $e->getMessage() . "\n";
        return false;
    }

    echo "uploadFaceMng fdfs_ret:".$fdfs_ret . "\n";
    return true;
}

if ($argc < 2) {
    echo "usage: php face_reupload.php 原FaceUrl'\n";
    echo "such as: php face_reupload.php '/31/31/35/ee3879116311b336ef71dfa191fcb86a.jpg'\n";
    exit;
}

uploadFaceMng($argv[1]);
