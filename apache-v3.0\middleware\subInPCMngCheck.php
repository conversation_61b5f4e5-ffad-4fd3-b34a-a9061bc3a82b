<?php
/*
 * @Description: 检测主账户在不在个个人和社区管理员下
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-17 15:01:21
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";
include_once __DIR__."/../database/main.php";

class CSubInPCMngCheck implements IMiddleware {
    public $id;
    function handle (\Closure $next) {
        global $gApp,$cLog,$cMessage;
        $userId = $gApp["userAliasId"];
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        $db = \database\CDatabase::getInstance();
        $count = $db->querySList("select count(*) from PersonalAccount P1 join PersonalAccount P2 on P1.ParentID = P2.ID where P1.Role in (".PERENDSROLE.",".COMENDSROLE.") and P2.ParentID = :ParentID and P1.ID = :ID",[":ParentID"=>$userId,":ID"=>$id])[0]["count(*)"];
        $cLog->actionLog("#middle#mainUserInPCMngCheck#id=$id;mngId=$userId;haveUser=$count");
        if($count == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}