<?php
namespace plan\process;

const ALARM_PROCESS = [
    "getAlarmForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserTimeZone"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"alarm.queryForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAlarmForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserTimeZone"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"alarm.queryForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "dealAlarm"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"alarm.deal"
        ],[
            "type"=>"echo",
            "code"=>StateDealSuccess,
        ],[
            "type"=>"model",
            "model"=>"notify.alarmDeal"
        ],
    ],
    "getUndealAlarmNum"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"alarm.queryUnDealNum"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];
