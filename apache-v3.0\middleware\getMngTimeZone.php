<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 17:40:20
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";

class CGetMngTimeZone implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        // 时区要用自己的Id
        $id = $gApp["userId"];
        $db = \database\CDatabase::getInstance();
        $data = $db->querySList("select TimeZone,CustomizeForm from Account where ID = :ID",[":ID"=>$id])[0];
        $timeZone = $data["TimeZone"];
        $customizeForm = $data["CustomizeForm"];
        $cLog->actionLog("#middle#getMngTimeZone#id=$id;timeZone=$timeZone;CustomizeForm=$customizeForm");
        \util\computed\setGAppData(["SelfTimeZone"=>$timeZone,"SelfCustomizeForm"=>$customizeForm]);
        $next();
    }
}