<?php
  const MSGTEXT = [ 

"accountExits"=>"Esta conta já existe",
"accountNotExit"=>"Conta não existe",
"accountIncorrect"=>"Nome de usuário ou senha inválidos",
"accountNIncorrect"=>"Conta inválida",
"activeEmpty"=>"O valor ativo é necessário",
"addFail"=>"Adicionando falhas",
"addSuccess"=>"Adicionando sucesso.",
"addSuccessPw"=>"Adicionando sucesso, a senha é '%s'.",
"addTmpKeyFail"=>"Adicione a chave da temperatura falhou, tente novamente",
"aptDuplicated"=>"Apartamento %s é duplicado",
"aptDigits"=>"O apartamento %s é inválido, que deve estar entre números de 1 e 6 dígitos",
"aptExit"=>"O Apartamento %já existe",
"abnormal"=>"Anormal",
"activation"=>"Ativação",
"additionalApp"=>"Aplicativo adicional",
"bindDevice"=>"Exclua todos os dispositivos sob esta conta",
"bindMAClibrary"=>"Exclua Mac na Mac Library",
"bindUser"=>"Exclua usuários nesta conta",
"buildingBindDevice"=>"Exclua dispositivos neste edifício",
"buildingBindUser"=>"Exclua usuários neste edifício",
"buildingDigits"=>"Construção inválida %s, que deve estar entre números de 1 e 2 dígitos",
"buildingExit"=>"Edifício já existe",
"BindingDeviceFailed"=>"O dispositivo de ligação falhou, o dispositivo pode estar ligado a outro usuário ou não adicionado à biblioteca Mac",
"chcekMacExits"=>"Adicionando falha, endereço MAC inválido ou já existe",
"changePasswdFail"=>"Modificar a senha falhou",
"changePasswdPEmail"=>"Modificando a senha do sucesso, verifique no email %s",
"community"=>"Comunidade",
"deleteFail"=>"Excluir falhou",
"deleteSuccess"=>"Excluir com sucesso",
"deviceTypeEmpty"=>"O tipo de dispositivo é necessário",
"deviceNotFindUser"=>"Dispositivo não encontrado, entre em contato com seu administrador",
"dealSuccess"=>"Definir sucesso",
"doorUnit"=>"Unidade da porta",
"emailExits"=>"e-mail já existe",
"emailPExit"=>"O email %s já existe",
"emailNotExits"=>"Este email não existe.",
"emailDuplicated"=>"Email %s é duplicado",
"errorVersion"=>"Versão de erro",
"emailOrAccountNotExit"=>"Este email não existe.",
"firstNameEmpty"=>"O primeiro nome é necessário",
"failed"=>"fail",
"family"=>"Família",
"guardPhone"=>"Telefone de guarda",
"incorrectSipAccount"=>"Não há mais conta SIP disponível",
"incorrectSipAccountGroup"=>"Não há mais grupo SIP disponível",
"importDataSuccess"=>"Importar sucesso de dados",
"importFailMACExit"=>"A importação falhou, verifique se o endereço MAC existe ou é válido:\r\n%s",
"invaildDC"=>"Código do dispositivo inválido",
"InvalidFile"=>"Arquivo inválido",
"invalidPEmail"=>"Email inválido %s",
"invalidPName"=>"Nome de usuário inválido %s",
"invalidPCalltype"=>"Tipo de chamada inválida %s",
"invalidPPin"=>"Pino inválido %s",
"invalidPActive"=>"Valor ativo inválido",
"invalidPage"=>"Página inválida",
"invalidPDeviceType"=>"Tipo de dispositivo inválido %s",
"invaildVerCode"=>"Código de verificação inválido",
"invalidIdentity"=>"Informações de identidade inválidas!",
"indoorMonitor"=>"Monitor interno",
"inactivated"=>"inactivate",
"normal"=>"Normal",
"expired"=>"expire",
"lastNameEmpty"=>"O sobrenome é necessário",
"locationEmpty"=>"É necessário localização",
"locationPLoog"=>"%s Localização muito longa",
"locationLoog"=>"Localização muito longa",
"loginError"=>"Erro de login",
"loginFail"=>"Falha no login",
"loginSuccess"=>"Sucesso de login",
"limitIP"=>"Você está tentando com muita frequência, tente novamente em 5 minutos",
"limitDevice"=>"O número do seu dispositivo atingiu o limite máximo",
"MAC2PLibrary"=>"Endereço MAC:%S é inválido, verifique sua biblioteca Mac.",
"MAC2Library"=>"Endereço MAC inválido, verifique sua biblioteca Mac",
"macExits"=>"O endereço MAC já existe",
"MACLength"=>"O comprimento do endereço MAC deve ser de 12 dígitos.",
"modifySuccess"=>"Modificar sucesso",
"modifyFailed"=>"Modificar falhas",
"maxHouse"=>"O número dos usuários atingiu o limite máximo, entre em contato com o administrador",
"modifyAptFail"=>"Salvando falhou! O número de APT já existe, você deve excluí -lo primeiro.",
"nameloog"=>"Nome do usuário por muito tempo, o nome de usuário pode conter até 64 caracteres",
"nameExit"=>"O nome de usuário já existe",
"notPermission"=>"Você não tem permissão de operação",
"noSip"=>"Não há mais conta SIP",
"passwordIncorrect"=>"Senha incorreta",
"passwdChangeSuccess"=>"Alterar a senha do sucesso",
"passwordResetSuccess"=>"Redefinir o sucesso da senha",
"passwordReset2"=>"A senha foi redefinida para '%s'.",
"payTimeOut"=>"Pagar um tempo limite",
"payFailed"=>"O pagamento falhou",
"processing"=>"Em processamento",
"paySuccess"=>"Pagamento bem -sucedido",
"redirectedOnRPS"=>"Este endereço MAC é redirecionado no RPS.",
"registerFailed"=>"Registro falhou",
"registerSuccess"=>"Registrar sucesso",
"roomNotExit"=>"Este usuário não existe!",
"RFCardExit"=>"Este cartão de RF já existe",
"registered"=>"register",
"PrivateKeyExists"=>"Esta chave privada existe",
"passwordCorrect"=>"senha inválida",
"timeLessCurrent"=>"Tempo de atualização inválido",
"timeZoneChangeSuccess"=>"Alterar o fuso horário de sucesso",
"timeOut"=>"Tempo esgotado",
"unbindMACUser"=>"Por favor, desative %s com o usuário primeiro",
"unKnowDT"=>"Tipo de dispositivo desconhecido",
"userBindUser"=>"Exclua usuários nesta conta primeiro",
"userNotExit"=>"Este usuário não existe",
"userMaxPLimt"=>"Criar fracassado, você só pode adicionar os membros da família de %s",
"unregistered"=>"Não registrado",
"validMAC"=>"Insira um endereço MAC válido",
"versionExit"=>"A versão já existe",
"versionNameNumberExit"=>"Nome ou número da versão já existe",
"sipStatus"=>"A atribuição de conta SIP falhou, tente novamente",
"sentCodeLater"=>"Enviamos um código de verificação para você, tente novamente mais tarde",
"setSuccess"=>"Sucesso de configuração",
"sendEmailSuccess"=>"Envie sucesso por e -mail",
"SetFailed"=>"A configuração falhou",
"stairPhone"=>"Telefone da escada",
"successed"=>"Ter sucesso",
"subscription"=>"Renovar",
"wallPhone"=>"Telefone de parede",
"emailMaxLen"=>"O email deve ter menos de 64 caracteres.",
"serverUpgradeTips"=>"A atualização do servidor está concluída, atualize a página.",
"ActiveFamilyAccount"=>"Ative a conta do mestre da família primeiro.",
"weekly"=>"Semanalmente",
"daily"=>"Diária",
"never"=>"Nunca",
"calltypeEmpty"=>"O tipo de chamada é necessário",
"addOutApt"=>"Você só pode adicionar os quartos de %s",
"call"=>"Chamar",
"unlock"=>"Desbloquear",
"tryUnlockCall"=>"Ligue para o desbloqueio falhou",
"tryUnlockKey"=>"Desbloqueio do código PIN Falha",
"tryUnlockCard"=>"Desbloqueio do cartão de RF falhou",
"tryUnlockFace"=>"Desbloqueio de rosto falhou",
"unlockApp"=>"Desbloqueio SmartPlus",
"unlockIndoor"=>"Desbloqueio do monitor interno",
"unlockNFC"=>"NFC Desbloqueio",
"unlockBluetooth"=>"Desbloqueio Bluetooth",
"unlockCard"=>"Cartão de RF Desbloqueio",
"unlockPrivateKey"=>"Desbloqueio do código PIN",
"unlockTempKey"=>"TEMP KEY Desbloquear",
"alarmDoorUnlock"=>"Desbloqueio da porta",
"alarmInfrared"=>"Infravermelho",
"alarmSmoke"=>"Fumaça",
"alarmGas"=>"Gás",
"alarmUrgency"=>"Urgência",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Adulteração",
"alarmGate"=>"Portão",
"alarmDoor"=>"Porta",
"alarmBedroom"=>"Quarto",
"alarmGuestRoom"=>"Quarto de hóspedes",
"alarmHall"=>"Salão",
"alarmWindow"=>"Janela",
"alarmBalcony"=>"Sacada",
"alarmKitchen"=>"Cozinha",
"alarmStudy"=>"Estudar",
"alarmBathroom"=>"Banheiro",
"alarmArea"=>"Área",
"RFCardExit2"=>"Cartão de RF %s já existe",
"RFCardDuplicated"=>"Cartão de RF %s é duplicado",
"notMacBind"=>"O usuário '%s' não tem permissão para abrir a porta de conexão com o dispositivo '%s'.",
"accountNumLet"=>"Conta deve consistir em números e letras",
"networkUnavailable"=>"Rede indisponível.",
"notForModel"=>"Não para este modelo.",
"upgradeDevVersion"=>"Atualize para a versão mais recente primeiro.",
"unavailableService"=>"O serviço está temporariamente indisponível, tente novamente mais tarde.",
"cantDeletePin"=>"Você não pode excluir o pino %s",
"residentInRoom"=>"Já existem residentes na sala %s",
"noAnswer"=>"Sem resposta",
"indoorAndApp"=>"Monitor interno e aplicativo",
"indoorMonitorOnly"=>"Somente monitor interno",
"appOnly"=>"Apenas aplicativo",
"endThanStart"=>"O horário final não pode ser mais cedo do que o horário de início.",
"endThanStartFile"=>"Dia ou hora inválida na fila '%s'.",
"doorRelease"=>"Liberação da porta",
"success"=>"Sucesso",
"unlockFACE"=>"Desbloqueio de rosto",
"unlockBLE"=>"Desbloqueio Bluetooth",
"captureSmartPlus"=>"Capture no SmartPlus",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"Falhou em desbloquear",
"deviceDisconnected"=>"O dispositivo foi desconectado.",
"low"=>"Baixo",
"motion"=>"Movimento",
"capture"=>"Capturar",
"failedImport"=>"A importação falhou",
"notValidMobile"=>"%s não é um número de celular válido.",
"mobileExits"=>"Número de celular já existe",
"mobileExits2"=>"Número de celular %s já existe",
"mobileDuplicated"=>"Número de celular %s é duplicado",
"mobileNumberExist"=>"O número de celular não existe.",
"codeIncorrect"=>"Código inválido",
"sendCodeSuccess"=>"Enviar código de verificação com sucesso",
"codeCorrect"=>"Correta",
"mobileNumberEmpty"=>"Por favor, insira seu número de telefone.",
"invalidUser"=>"Usuário inválido %s",
"locationExits"=>"O endereço de localização já existe",
"smartPlusIndoor"=>"SmartPlus e monitores internos",
"phoneIndoor"=>"Telefone e monitores internos",
"smartPlusIndoorBackup"=>"SmartPlus e monitores internos, com telefone como backup",
"smartPlusBackup"=>"Monitores internos com smartplus como backup",
"indoorPhoneBackup"=>"Monitores internos com telefone como backup",
"indoorSmartPlusPhone"=>"Monitores internos com smartplus como backup, finalmente o telefone",
"endUser"=>"Usuário final",
"installer"=>"Instaladora",
"distributor"=>"Distribuidora",
"pm"=>"PM",
"superManage"=>"Supermanage",
"loginManagement"=>"Gerenciamento de login",
"accessControl"=>"Controle de acesso",
"userManagement"=>"Gerenciamento de usuários",
"deviceManagement"=>"Gerenciamento de dispositivo",
"communityManagement"=>"Gestão da comunidade",
"auditLogin"=>"Faça login: web",
"auditLogout"=>"Log Out: Web",
"auditAddTempKey"=>"Adicione a chave temporária: {0}",
"auditEditTempKey"=>"Editar chave temporária: {0}",
"auditDeleteTempKey"=>"Exclua a chave temporária: {0}",
"auditAddRFCard"=>"Adicione o cartão RF: {0}",
"auditEditRFCard"=>"Editar cartão de RF: {0}",
"auditDeleteRFCard"=>"Excluir cartão de RF: {0}",
"auditAddDis"=>"Adicionar distribuidor: {0}",
"auditEditDis"=>"Editar distribuidor: {0}",
"auditDeleteDis"=>"Excluir distribuidor: {0}",
"auditAddInstaller"=>"Adicionar instalador: {0}",
"auditEditInstaller"=>"Editar instalação: {0}",
"auditDeleteInstaller"=>"Excluir instalador: {0}",
"auditAddPM"=>"Adicionar pm: {0}",
"auditEditPM"=>"Editar pm: {0}",
"auditDeletePM"=>"Delete pm: {0}",
"auditAddEndUser"=>"Adicionar usuário final: {0}",
"auditEditEndUser"=>"Editar o usuário final: {0}",
"auditDeleteEndUser"=>"Exclua o usuário final: {0}",
"auditSetOwnerTime"=>"Defina o próprio fuso horário {0}",
"auditSetOwnPassword"=>"Defina a própria senha",
"auditAddPIN"=>"Adicionar pino: {0}",
"auditEditPIN"=>"Editar pino: {0}",
"auditDeletePIN"=>"Excluir pino: {0}",
"auditImportFace"=>"Importar face: {0}",
"auditDeleteFace"=>"Excluir face: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Defina o tipo de chamada SmartPlus e Monitores internos: {0} \\u0026 {1}",
"auditSetCallTypePhoneIndoor"=>"Defina o tipo de chamada de telefone e monitores internos: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Defina o tipo de chamada SmartPlus e os monitores internos, com telefone como backup: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusBackup"=>"Defina o tipo de chamada monitores internos com o SmartPlus como backup: {0} \\u0026 {1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Defina o tipo de chamada monitores internos com telefone como backup: {0} \\u0026 {1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Defina o tipo de chamada monitores internos com o SmartPlus como backup, finalmente o telefone: {0} \\u0026 {1}",
"auditDeleteDevice"=>"Excluir dispositivo: {0}",
"auditSetAPTCount"=>"Defina o número de apartamentos {0}",
"auditEnableLandline"=>"Habilite o serviço de telefone fixo",
"auditDisableLandline"=>"Desativar o serviço de telefone fixo",
"auditSetSubTime"=>"Definir fuso horário {0}",
"auditSetChargeModeInstall"=>"Defina o Modelo de Charagem Pay pelo Instalador",
"auditSetChargeModeUser"=>"Definir o modelo de carregamento paga por usuário/pm",
"auditSetConnectTypeDefault"=>"Definir o tipo de conexão padrão",
"auditSetConnectTypeTCP"=>"Definir tipo de conexão TCP",
"auditSetConnectTypeUDP"=>"Definir tipo de conexão UDP",
"auditSetConnectTypeTLS"=>"Definir tipo de conexão TLS",
"auditAddCommunity"=>"Adicionar comunidade: {0}",
"auditDeleteCommunity"=>"Excluir comunidade: {0}",
"auditImportCommunity"=>"Comunidade de importação: {0}",
"auditSetAPTNumber"=>"Set {0} número da sala {1}",
"auditSetEmail"=>"Defina o email {0}: {1}",
"auditSetMobile"=>"Defina o número de telefone {0}: {1}",
"auditDeviceTypeStair"=>"Set Type Multi-Tenants Doorphone: {0}",
"auditDeviceTypeDoor"=>"Definir tipo de porta de inquilino único: {0}",
"auditDeviceTypeIndoor"=>"Definir tipo de monitor interno: {0}",
"auditDeviceTypeGuardPhone"=>"Set Type Guard Telefone: {0}",
"auditDeviceTypeAccessControl"=>"Set Type Access Control: {0}",
"auditSetNetGroup"=>"Definir grupo de rede {0}: {1}",
"auditEditCommunity"=>"Editar comunidade",
"deliveryMsg"=>"Você tem %de itens que foram entregues a você, verifique o tempo.",
"deliveryTitle"=>"Você tem um novo pacote!",
"rfcardDuplicatedLines"=>"Número de cartão RFID duplicado na linha %s!",
"rfcardNameInvalid"=>"Nome do cartão RF inválido na linha %s!",
"rfcardExistLines"=>"Os cartões de RF já existem na linha %s.",
"importFailMacExistLines"=>"O endereço MAC existe ou válido na linha %s.",
"exportExcelCountNull"=>"Nenhum log para exportar na data!",
"keyIsEqualRoom"=>"A chave de entrega não pode ser idêntica ao número APT!",
"visitor"=>"Visitante",
"CommunityNameExist"=>"O nome da comunidade já existe",
"unlockGuardPhone"=>"Desbloqueio do telefone de guarda",
"auditLoginApp"=>"Faça login: aplicativo",
"auditLogoutApp"=>"Log Out: App",
"timeForYesterday"=>"Ontem",
"exportExcelDataBefore"=>"Dados muito grandes!",
"tempkeyUsed"=>"Tempkey usado",
"tempkeyContent"=>"%s usou o tempkey.",
"accessNameExist"=>"O nome do grupo de acesso já existe",
"addFaceFail"=>"Por favor, importe uma foto clara do rosto.",
"userInvalid"=>"Usuário inválido na linha %s.",
"groupsInvalid"=>"Grupo de acesso inválido na linha %s.",
"BuildAccessName"=>"%De construção de residentes s",
"auditCodeLogEditApt"=>"Editar apartamento: {0}",
"invalidTimeInLine"=>"Tempo inválido na linha %s.",
"cancel"=>"Cancelar",
"cancelSuccess"=>"Cancelar conseguiu.",
"payOutstanding"=>"Verifique se há pedidos não pagos, se não, entre em contato com o seu provedor de serviços",
"featureDeleteError"=>"Plano de recurso estar vinculado.",
"beyondFamilyMember"=>"Você não pode criar mais contas de membros da família, entre em contato com seu provedor de serviços para criá -lo.",
"indoorMonitorRequired"=>"Pelo menos um monitor interno é necessário para cada apartamento.",
"featureActivationFee"=>"Recurso (taxa única)",
"systemProcessing"=>"Processamento do sistema",
"featureMonthlyFee"=>"Recurso (taxa mensal)",
"featurePriceDifferences"=>"Recurso (diferenças de preço)",
"updatingSuccess"=>"A atualização foi bem -sucedida!",
"featureNameBasic"=>"Chemistry",
"featureNamePremium"=>"Prêmio",
"indoorMacNotCorrect"=>"Por favor, insira o MAC do monitor interno correto.",
"off"=>"Desligada",
"enterValidAccount"=>"Por favor, insira uma conta válida",
"invalidKitImportMAC"=>"Verifique se o Mac existe ou válido: %s",
"importLessData"=>"Por favor, importe menos que os dados de %s.",
"invalidQRCode"=>"Identificando falhado, digitalize um código QR correto.",
"cannotCreateFamilyMember"=>"Você não pode criar mais contas de membros da família.",
"importProcessing"=>"Importando, tente novamente mais tarde",
"departmentAccessName"=>"Grupo de Acesso %S",
"idExistsLine"=>"Id já existe na linha %s",
"enterFirstNameLine"=>"Por favor, insira o primeiro nome na linha %s",
"enterLastNameLine"=>"Por favor, insira o sobrenome na linha %s",
"departmentExist"=>"Departamento já existe",
"idExist"=>"Id já existe",
"layoutIdInvalid"=>"O layout é inválido",
"unlockAppHome"=>"Olhos desbloqueados",
"officeNameExist"=>"O nome do escritório já existe",
"departmentExit"=>"Departamento já existe.",
"importOutTask"=>"Você só pode importar um modelo de cada vez.",
"idDuplicated"=>"Id %s é duplicado",
"aptInvalidLine"=>"APT inválido na linha %s.",
"buildInvalidLine"=>"Edifício inválido na linha %s.",
"departmentInvalidLine"=>"Departamento inválido na linha %s.",
"idInvalidLine"=>"ID inválido na linha %s.",
"propertyManager"=>"Gerente da propriedade",
"departmentBindDevice"=>"Exclua dispositivos sob este departamento.",
"departmentBindUser"=>"Exclua usuários neste departamento.",
"smartPlusValidLine"=>"Recurso de intercomunicação SmartPlus inválido na linha %s.",
"identityValidLine"=>"Identidade inválida na linha %s.",
"eachDoorCount"=>"Um único plano para abrir cada porta uma vez",
"textUpgradeMsg1"=>"Atualize a versão do aplicativo para continuar.",
"textUpgradeMsg2"=>"falha no login",
"deleteCodeGetLimitTimes"=>"Key inválido. Por favor, tente novamente 24 horas depois.",
"deleteCodeOverLimitTimes"=>"Por favor, tente novamente 24 horas depois.",
"deleteCodeError"=>"Chave inválida",
"textUpgradeMsg"=>"1. otimizou a função Tempkey.; 2. ADED a função de cancelamento da conta.; 3.Fixou alguns erros.",
"paramsError"=>"Erro de parâmetro",
"pmappStatusInvalid"=>"Por favor, ative o aplicativo PM primeiro.",
"delivery_description"=>"Chave da temperatura da entrega",
"webRelayIDInvalidLine"=>"ID de relé da Web inválido na linha %s.",
"relayInvalid"=>"Relé inválido na linha %s.",
"cancelError"=>"Cancelar falhou.",
"textUpgradeMsgForComRole"=>"Atualizar o papel da comunidade",
"textUpgradeMsgForPerRole"=>"Atualizar o papel do pessoal",
"textUpgradeMsgForOffRole"=>"Atualizar o papel do escritório",
"textUpgradeMsgForPMRole"=>"papel de pm pm",
"lockApp"=>"SmartPlus Lock",
"lock"=>"Trancar",
"versionLogMaxLen"=>"O log de versão não pode ser maior que os caracteres de %s",
"autoLock"=>"Bloqueio automático",
"pinAndRFcardNotNullLines"=>"Pelo menos uma das placas PIN e RF na linha %s precisa ser preenchida!",
"pinExistLines"=>"O PIN já existiu na linha %s.",
"pinInvalidLines"=>"Pino inválido na linha %s!",
"pinDuplicatedLines"=>"PIN duplicado na linha %s!",
"FaceImportLength"=>"O tamanho do arquivo de importação de face não pode ser maior que %s",
"landlineServerNotActivate"=>"Esta comunidade não ativou o serviço de telefone fixo.",
"importFailDisNotExist"=>"O distribuidor não existe",
"importFailNotPermission"=>"Você não tem permissão para adicionar este endereço MAC.",
"importFailTooManyAdd"=>"A importação falhou, apenas para distribuidor único.",
"importFailAdded"=>"Este endereço MAC já foi adicionado por outro usuário.",
"macAssignToLimit"=>"Você só pode atribuir até 10 distribuidores",
"macNumToLimit"=>"Você só pode fazer upload de até 1000 endereços MAC por vez.",
"addOutFloor"=>"Por favor, insira um número entre 1 ~ 128.",
"floor"=>"Chão",
"PostalCodeInvalid"=>"Por favor, insira carta ou número.",
"onceCodeInvalid"=>"O código uma vez deve ter 4-5 dígitos.",
"permanentCodeInvalid"=>"O código permanente deve ter 6 dígitos.",
"onceCodeOutNum"=>"Você só pode adicionar até 10 código uma vez.",
"permanentCodeOutNum"=>"Você só pode adicionar até 10 código permanente.",
"onceCodeExist"=>"O código outrora já existe.",
"permanentCodeExist"=>"O código permanente já existe.",
"addOutFloorLine"=>"Número do piso inválido na linha %s.",
"auditManuallyUnlock"=>"Desbloquear manualmente",
"auditManuallyLock"=>"Trava manualmente",
"automaticallyUnlock"=>"Desbloquear automaticamente",
"doorClose"=>"Porta perto",
"PostalCodeNotEmpty"=>"Por favor, insira pelo menos uma carta ou número.",
"emergencyAlarm"=>"Alarme de emergência",
"doorSensor"=>"Sensor da porta",
"yaleBatteryWarning"=>"Aviso de bateria de Yale",
"auditCodeManuallyUnlock"=>"Desbloquear manualmente",
"auditCodeManuallyLock"=>"Trava manualmente",
"2weekBatteryWarning"=>"%S - Tempo de bateria estimado restante: 2 semanas.",
"1weekBatteryWarning"=>"%s - tempo estimado do tempo da bateria: 1 semana.",
"replaceBatteryWarning"=>"%S - O nível da bateria é extremamente baixo, substitua imediatamente.",
"open"=>"Abrir",
"close"=>"Fechar",
"addContactFavoriteNum"=>"Adicionando aos favoritos falhou. Você só pode adicionar até 300 apartamentos favoritos.",
"addContactBlockNum"=>"Adicionando à lista de blocos falhou. Você só pode adicionar até 100 apartamentos para a lista de bloqueios.",
"voiceTitle"=>"Mensagem de voz",
"voiceContent"=>"Você tem uma mensagem de voz de %s",
"voiceMsgInvalid"=>"A mensagem de voz expirou.",
"toggleFeaturePlan"=>"Você não pode alterar o plano de recursos.",
"rtspAddresEmpty"=>"Por favor, insira o endereço RTSP.",
"rtspAddresInvalid"=>"Endereço RTSP inválido.",
"rtspPortEmpty"=>"Por favor, insira a porta.",
"rtspPortInvalid"=>"Porta inválida.",
"rtspPassWdEmpty"=>"Por favor, digite a senha.",
"rtspPassWdInvalid"=>"Senha muito longa, a senha pode conter até 63 caracteres.",
"cameraExist"=>"A câmera já existe.",
"errorOnRPS"=>"Falha ao solicitar o servidor RPS",
"faceImportErrorSystem"=>"Erro no sistema",
"faceImportErrorView"=>"Não é a vista frontal",
"faceImportErrorWearMask"=>"Máscara detectada",
"faceImportErrorLowResolution"=>"Resolução é muito baixa",
"faceImportErrorWrongFormat"=>"Erro de formato de arquivo",
"faceImportErrorNoFace"=>"Nenhuma face detectada",
"faceImportErrorFileLarge"=>"O arquivo é muito maior",
"faceImportErrorFaceLarge"=>"O rosto é muito maior",
"faceImportErrorFaceSmall"=>"O rosto é muito pequeno",
"faceImportErrorMultiFaces"=>"Mais de um rosto",
"faceImportErrorWrongName"=>"Nome do arquivo é erro.",
"faceImportErrorEmptyName"=>"O nome do residente está vazio.",
"faceImportErrorNoAccountInfo"=>"Obtenha um erro de informação da conta pessoal.",
"faceImportErrorAccountInactive"=>"A conta pessoal não está ativa.",
"changeHomeFeatureInvalid"=>"Operação falhou!",
"changeInterComFeatureInvalid"=>"Operação falhou!",
"offline"=>"Falha: offline",
"allFloors"=>"Todos os pisos",
"uploadOversize"=>"O tamanho do arquivo de upload não pode ser maior que %s",
"uploadInvalidType"=>"O tipo de arquivo carregado não é suportado",
"uploadFailed"=>"Falha no upload, tente mais tarde",
"uploadScreenSaverImgTooMuch"=>"As imagens de tela de tela não podem ser mais do que %S!",
"screenSaverImgTooLittle"=>"As imagens de tela de tela não podem ser inferiores a %S!",
"screenSaverImgTooMuch"=>"As imagens de tela de tela não podem ser mais do que %S!",
"screenSaverDevicesOffline"=>"Falha ao salvar.",
"saveFailed"=>"Falha ao salvar.",
"importingInProgress"=>"Importação em andamento, tente novamente mais tarde.",
"importBuildingInvalidLine"=>"Edifício inválido na linha %s",
"importAptInvalidLine"=>"Inválido apt na linha %s",
"importAccountTypeInvalidLine"=>"Tipo de conta inválida na linha %s",
"importFirstNameInvalidLine"=>"Primeiro nome inválido na linha %s",
"importLastNameInvalidLine"=>"Sobrenome inválido na linha %s",
"importKeyInvalidLine"=>"Chave inválida na linha %s",
"importKeyExistsLine"=>"PIN existe na linha %s",
"importCardInvalidLine"=>"Cartão de RF inválido na linha %s",
"importCardExistsLine"=>"Cartão de RF existe na linha %s",
"importAccessGroupInvalidLine"=>"ID do grupo de acesso inválido na linha %s",
"importAccessGroupNoPermissionLine"=>"Sem permissão ID do grupo de acesso à linha %s",
"importExceededNumberLine"=>"Excedeu o número de membro da família na linha %s",
"importNoActiveMasterLine"=>"A importação falhou na linha %s, ative o Matser da família primeiro.",
"importMasterExistsLine"=>"O mestre da família já existe na linha %s.",
"importNoCreateMasterLine"=>"A importação falhou na linha %s, crie o Matser da família primeiro.",
"PrivateKeysDataExist"=>"A chave privada %s já existe.",
"PrivateKeyDataExists"=>"A chave privada %já existe.",
"landLineOpenToClosedFail"=>"Falha ao salvar.",
"limitWithIp"=>"Você está tentando com muita frequência, por favor tente novamente em 5 minutos. (IP:%s)",
"subDistributor"=>"Sub distribuidor",
"faceImportErrorNotClear"=>"A imagem importada não está clara.",


  ];
