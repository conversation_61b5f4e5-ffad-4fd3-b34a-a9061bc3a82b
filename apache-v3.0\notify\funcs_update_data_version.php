<?php

require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/utility.php');
require_once(dirname(__FILE__) . '/adapt_define.php');

function updateAllAccountDataVersion($mngid)
{
    global $cLog;
    global $db;
    $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where  Role=20 and ParentID=:MngID', [":MngID"=> $mngid]);

    $datas = $db->querySList("select P.ID as SlaveID From PersonalAccount P left join PersonalAccount PP on P.ParentID=PP.ID  where PP.ParentID=:MngID and PP.Role=20", [":MngID"=>$mngid]);
    foreach ($datas as $row => $data) {
        $ID = $data['SlaveID'];
        $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where  ID=:ID', [":ID"=> $ID]);
    }
    $cLog->TRACE("[updateAllAccountDataVersion] mngid=[" . $mngid . "]");
}

function updateAccountDataVersion($accounts)
{
    global $cLog;
    global $db;
    foreach ($accounts as $value) {
        $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where Account=:Account', [":Account"=> $value]);
        $cLog->TRACE("[updateAccountDataVersion] Account=[" . $value . "]");
    }
}

function getNodeAccounts($node, &$accounts)
{
    global $cLog;
    if (!$node) {
        $cLog->TRACE("[updateNodeDataVersion] node=[" . $node . "] is null");
        return;
    }
    array_push($accounts, $node);
    global $db;
    $datas = $db->querySList("select P.Account  From PersonalAccount P left join PersonalAccount PP on PP.ID=P.ParentID where PP.Account=:Account", [":Account"=>$node]);
    foreach ($datas as $row => $data) {
        $Account = $data['Account'];
        array_push($accounts, $Account);
    }
}

function updateAccountDataVersionByAccountID($account_ids)
{
    global $cLog;
    global $db;
    $account_ids = implode(",", $account_ids);
    $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where ID in(:ID)', [":ID"=> $account_ids]);
    $cLog->TRACE("[updateAccountDataVersionByAccountID] Account ID=[" . $account_ids . "]");
}

function updateStaffDeliverDataVersion($staff_ids, $deliver_ids)
{
    global $cLog;
    global $db;
    $staff_ids = implode(",", $staff_ids);
    $deliver_ids = implode(",", $deliver_ids);

    $db->exec2ListWArray('update Staff set Version=UNIX_TIMESTAMP() where ID in(:ID)', [":ID"=> $staff_ids]);
    $cLog->TRACE("[updateStaffDeliverDataVersion] Staff ID=[" . $staff_ids . "]");
    
    $db->exec2ListWArray('update Delivery set Version=UNIX_TIMESTAMP() where ID in(:ID)', [":ID"=> $deliver_ids]);
    $cLog->TRACE("[updateStaffDeliverDataVersion] Delivery ID=[" . $deliver_ids . "]");
}

function updateDataVersionByAccessGroupID($ag_id)
{
    global $cLog;
    global $db;  
	if (strlen($ag_id) == 0 )
	{
		$cLog->TRACE("[updateDataVersionByAccessGroupID]  AccessGroupID ID=[$ag_id] is null"); 
		return 0;
	}
    $cLog->TRACE("[updateDataVersionByAccessGroupID] PersonalAccount/StaffAccess/DeliveryAccess: AccessGroupID ID=[$ag_id]"); 

    $datas = $db->querySList("select StaffID from StaffAccess where AccessGroupID in ($ag_id)", []);
	$ids = DbData2Ids($datas, "StaffID");
    if (strlen($ids) > 0 )
    {
        $db->exec2ListWArray("update Staff set Version=UNIX_TIMESTAMP() where ID in($ids)");
    }

    $datas = $db->querySList("select DeliveryID from DeliveryAccess where AccessGroupID in ($ag_id)", []);
	$ids = DbData2Ids($datas, "DeliveryID");
    if (strlen($ids) > 0 )
    {
        $db->exec2ListWArray("update Delivery set Version=UNIX_TIMESTAMP() where ID in($ids)");
    }

    $datas = $db->querySList("select Account from AccountAccess where AccessGroupID in ($ag_id)", []);
	$accounts = DbData2Ids($datas, "Account");
    if (strlen($accounts) > 0 )
    {
        $sth = $db->exec2ListWArray("update PersonalAccount set Version=UNIX_TIMESTAMP() where Account in ($accounts)");
    }
}

//关联这个设备的权限组（包括默认权限组）和对应的用户数据版本都有修改
function updateDataVersionByUnitMac($unitid, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateDataVersionByUnitMac] unitid=$unitid mac=$mac");
    global $db;
    $datas = $db->querySList("select ID From AccessGroup where UnitID=:UnitID union select AccessGroupID AS ID From AccessGroupDevice where MAC=:MAC", [":UnitID"=> $unitid, ":MAC"=> $mac]);
    
    updateDataVersionByAccessGroupID(DbData2Ids($datas, "ID"));
}

function updateDataVersionByPubMac($communityid, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateDataVersionByPubMac] communityid=$communityid mac=$mac");
    global $db;
    $datas = $db->querySList("select ID From AccessGroup where UnitID != 0 and CommunityID=:CommunityID union select AccessGroupID AS ID From AccessGroupDevice where MAC=:MAC", [":CommunityID"=> $communityid, ":MAC"=> $mac]);

    updateDataVersionByAccessGroupID(DbData2Ids($datas, "ID"));
}

/*更新住户下所有用户的数据版本*/
function updateDataVersionByPerMac($node, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateDataVersionByPerMac] node=$node mac=$mac");
    global $db;
    $datas = $db->querySList("select Account  From PersonalAccount where ParentID=(select ID from PersonalAccount where Account=:node) and Role = 21", [":node"=> $node]);
    $ids = DbData2Ids($datas, "Account");
    if (strlen($ids)) {
        $ids = "$ids,$node";
    } else {
        $ids = $node;
    }
    $sth = $db->exec2ListWArray("update PersonalAccount set Version=UNIX_TIMESTAMP() where Account in(:IDS)", [":IDS"=> $ids]);
}
