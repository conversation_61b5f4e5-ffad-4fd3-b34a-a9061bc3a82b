<?php
/*
 * @Description: mac检查
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors  : cj
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
class CMacCheck implements IMiddleware {
    public function handle(\Closure $next) {
        global $cMessage;
        global $cLog;
        $params = ["MAC"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $mac = $params["MAC"];

        $cLog->actionLog("#middle#macCheck#mac=".$mac);
        if ($mac === null) {
            $next();
            return;
        }
        if(\util\string\checkMAC($mac)) {
            $cMessage->echoErrorMsg(StateMACLength);
        }
        $db = \database\CDatabase::getInstance();
        if($db->isExistFiled("Devices",[":MAC"=>$mac],null) || $db->isExistFiled("PersonalDevices",[":MAC"=>$mac],null)) {
            $cMessage->echoErrorMsg(StateMacExits);
        }
        $next();
    }
}