<?php

namespace plan\process;

const KEY_PROCESS = [
    "getTmpKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryTmp"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "addPrivateKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.addPri"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyPerUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "editPrivateKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.modPri"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyPerUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "addRFCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.addRF"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardPerUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "editRFCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.modRF"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardPerUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "addComPrivateKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.addPriFCom"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "editComPrivateKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.modPriFC"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "addComRFCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.addRFFCom"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "editComRFCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.modRFFC"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "addTempKeyForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserId"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type"=>"database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "key.addTemp"
        ],
        [
            "type" => "model",
            "model" => "notify.tempKeyAdd"
        ],
        [
            "type"=>"database",
            "method" => "commit"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "delPrivateKeyForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.deletePri"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyPerUpdate"
        ],
    ],
    "delRFCardForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.deleteRF"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardPerUpdate"
        ],
    ],
    "delTmpKeyForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.deleteTmp"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
        [
            "type" => "model",
            "model" => "notify.tempKeyDelete"
        ]
    ],
    "delComPrivateKeyForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.deletePriFc"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
        [
            "type" => "model",
            "model" => "notify.tempKeyDelete"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ]
    ],
    "delComRFCardForUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.deleteRFFc"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ]
    ],
    "getTmpKeyApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryTmpForApp"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getPrivateKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryPri"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getRfCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryRF"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getPrivateKeyFromCom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryPriFC"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getRfCardFromCom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryRFFC"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getTempKeyFromCom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ],
        [
            "type" => "model",
            "model" => "key.queryTmpFC"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "addTempKeyForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "getUserId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "tempKey.add"
        ],
        [
            "type" => "model",
            "model" => "notify.tempKeyAddForPM"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "queryTempKeyForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "tempKey.queryForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryTempKeyInfoForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "tempKey.getInfo"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "delTempKeyForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "tempKey.delete"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "tempKey.afterDelete"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete,
            "options" => ["data" => "data"]
        ],
    ],
    "addPMAPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "addKeyIsEqualRoom"]
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addAPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "addPMARKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addARKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "addPMSPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addSPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "addPMSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addSRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "addPMRPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addRPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "addPMRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.addRRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ],
    ],
    "editPMAPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "updateKeyIsEqualRoom"]
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modAPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editPMARKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modARKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editPMSPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modSPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editPMSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modSRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editPMRPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modRPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editPMRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.modRRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "delPMAPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delAPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMARKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delARKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMSPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delSPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delSRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMAllSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "manageData.checkPw"
        ],
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delAllSRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMRPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delRPKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delRRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delPMAllRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "manageData.checkPw"
        ],
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delAllRRKey"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "getPMAPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selAPKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getPMARKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selARKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getPMSPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selSPKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getPMSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selSRKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getPMRPKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selRPKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getPMRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selRRKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "importPMRRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.import"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdate"
        ],
        [
            "type" => "echo",
            "code" => SateSuccessImport
        ],
    ],
    "importPMSRKey" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.importsr"
        ],
        [
            "type" => "model",
            "model" => "notify.rfCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => SateSuccessImport
        ],
    ],
    "getAllTypePinForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selAllPin"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getAllTypeCardForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.selAllCard"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "editPinForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.editAllPin"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.pinOrCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "editCardForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.editAllCard"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.pinOrCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "delPinForAll" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delAllPin"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.pinOrCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "delCardForAll" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "pmKey.delAllCard"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "notify.pinOrCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "importResRfCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.importResRfCard"
        ],
        [
            "type" => "model",
            "model" => "notify.importCardForPM"
        ],
        [
            "type" => "echo",
            "code" => SateSuccessImport
        ],
    ],
    "importStaffCard" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ],
        [
            "type" => "model",
            "model" => "pmKey.importStaffCard"
        ],
        [
            "type" => "model",
            "model" => "notify.pinOrCardComUpdateForPM"
        ],
        [
            "type" => "echo",
            "code" => SateSuccessImport
        ],
    ],
    //pm app获取pm tempkey列表
    "queryTempKeyForPMApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],
        [
            "type" => "model",
            "model" => "tempKey.queryForPM"
        ],
        [
            "type"=>"model",
            "model"=>"tempKey.formatPmAppTempKey"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    //pm app新增tempkey
    "addTempKeyForPMApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAppAlias"],
                ["name" => "getAliasId"],
                ["name" => "getPMAppUserId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "tempKey.add"
        ],
        [
            "type" => "model",
            "model" => "notify.tempKeyAddForPM"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    //pm app删除pm tempkey
    "delTempKeyForPMApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAppAlias"],
                ["name" => "getAliasId"],
            ]
        ],
        [
            "type" => "database",
            "method" => "begin"
        ],
        [
            "type" => "model",
            "model" => "tempKey.delete"
        ],
        [
            "type" => "database",
            "method" => "commit"
        ],
        [
            "type" => "model",
            "model" => "tempKey.afterDelete"
        ],
        [
            "type" => "echo",
            "code" => StateSuccessDelete,
            "options" => ["data" => "data"]
        ],
    ],
];
