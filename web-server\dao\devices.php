<?php

namespace dao;
use framework\BasicDao;

class Devices extends BasicDao
{
    //当前表名
    public $table = 'Devices';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    
    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["Grade", 11], ["ManageGroup", 0, "!="]]
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/07 11:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 11:21 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2023/03/09 10:20 V6.5.4
     * @lastEditors: systemCreator 2023/03/09 10:20 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }
    
    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    /**
     * @description:super获取可以升级的社区设备列表
     * @author:lwj 2023-03-22 09:41:06 V6.5.4
     * @lastEditor:lwj 2023-03-22 09:41:06 V6.5.4
     * @param:
     * @return array
     */
    public function getAllForSuperCommunityUpgrade($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $where = ' D.Firmware != :Firmware AND D.Status = 1 AND A.ManageGroup = :ManageGroup AND D.Firmware like :Model';
        $projectGrade = COMMUNITYGRADE.','.PERSONGRADE;
        $where.= " and A.Grade in($projectGrade)";
        if($searchValue !== null && $searchValue !== ''){
            switch ($searchKey){
                case 'ProjectName':
                    $where.= ' and A.Location like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'UnitName':
                    $where.= ' and CU.UnitName like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'APT':
                    $where.= ' and CR.RoomName like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'MAC':
                    $where.= ' and D.MAC like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'Location':
                    $where.= ' and D.Location like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                default:
                    break;
            }
        }
        $deviceTable = PROXY_TABLES['devices'];
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $communityUnitTable = PROXY_TABLES['communityUnit'];
        $communityRoomTable = PROXY_TABLES['communityRoom'];
        $sql = "select D.ID,D.MAC,D.Location,D.Firmware,D.Status,PA.Name,CU.UnitName,CR.RoomName as APT,CR.Floor,A.Location as ProjectName 
                from $deviceTable D 
                join Account A on D.MngAccountID = A.ID 
                left join $personalAccountTable PA on PA.Account = D.Node 
                left join $communityUnitTable CU on CU.ID = D.UnitID 
                left join $communityRoomTable CR on CR.ID = PA.RoomID 
                where $where order by D.ID desc";
        $deviceList = $this->execute($sql, $bindArray);
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /**
     * @description:super获取可以升级的办公设备列表
     * @author:lwj 2023-03-22 09:41:06 V6.5.4
     * @lastEditor:lwj 2023-03-22 09:41:06 V6.5.4
     * @param:
     * @return array
     */
    public function getAllForSuperOfficeUpgrade($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $where = ' D.Firmware != :Firmware AND D.Status = 1 AND A.ManageGroup = :ManageGroup AND D.Firmware like :Model';
        $projectGrade = OFFICEGRADE;
        $where.= " and A.Grade in($projectGrade)";
        if($searchValue !== null && $searchValue !== ''){
            switch ($searchKey){
                case 'ProjectName':
                    $where.= ' and A.Location like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'UnitName':
                    $where.= ' and CU.UnitName like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'Name':
                    $where.= ' and md5(PA.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue)';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'MAC':
                    $where.= ' and D.MAC like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'Location':
                    $where.= ' and D.Location like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                default:
                    break;
            }
        }
        $deviceTable = PROXY_TABLES['devices'];
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $communityUnitTable = PROXY_TABLES['communityUnit'];
        $sql = "select D.ID,D.MAC,D.Location,D.Firmware,D.Status,PA.Name,CU.UnitName,A.Location as ProjectName 
                from $deviceTable D 
                join Account A on D.MngAccountID = A.ID 
                left join $personalAccountTable PA on PA.Account = D.Node 
                left join $communityUnitTable CU on CU.ID = D.UnitID 
                where $where";
        $deviceList = $this->execute($sql, $bindArray);
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }


    /**
     * @description:获取室外设备类型列表
     * @author:lwj 2023-04-24 18:15:56 V6.6
     * @lastEditor:lwj 2023-04-24 18:15:56 V6.6
     * @param {array} $bindArray MngAccountID:项目ID,UnitID:单元ID(如楼栋ID)
     * @return array
     * @throws \Exception
     */
    public function getDoorDeviceList($bindArray)
    {
        $deviceTable = $this->table;
        $communityUnitTable = $this->dao->communityUnit->table;
        $pubDevMngListTable = $this->dao->pubDevMngList->table;
        $deviceComGrade = DEVICE_GRADE_COM;
        $deviceUnitGrade = DEVICE_GRADE_UNIT;
        $deviceFamilyGrade = DEVICE_GRADE_FAMILY;
        $outType = implode(',', DEVICE_TYPE);
        // 选择楼栋时，只能选择有管理这栋楼的设备
        $sql = "select D.MAC,D.Location,U.UnitName,D.Status,D.Relay,D.SecurityRelay,D.Grade 
            from $deviceTable D 
            left join $communityUnitTable U on D.UnitID = U.ID 
            where D.MngAccountID = :MngAccountID and D.Type in ($outType) and D.Grade != $deviceFamilyGrade";
        $whereBind = [
            ':MngAccountID' => $bindArray['MngAccountID'],
        ];
        if(!empty($bindArray['UnitID'])){
            $whereBind[':UnitID'] = $bindArray['UnitID'];
            $sql = "select MAC,Location,'' as UnitName,Status,Relay,SecurityRelay,Grade 
            from $deviceTable
            where (
                ID in (select DevicesID from $pubDevMngListTable where UnitID = :UnitID)
                or Flags & 8 = 8
            )
            and MngAccountID = :MngAccountID and Type in ($outType)
            and Grade = $deviceComGrade
            union 
            select D.MAC,D.Location,U.UnitName,D.Status,D.Relay,D.SecurityRelay,D.Grade 
            from $deviceTable D 
            left join $communityUnitTable U on D.UnitID = U.ID 
            where D.MngAccountID = :MngAccountID and D.Type in ($outType) and D.Grade = $deviceUnitGrade and D.UnitID = :UnitID";
        }
        $list = $this->execute($sql, $whereBind);

        return $list;
    }

    /*
     *@description 获取项目下的可升级设备列表
     *<AUTHOR> 2023-06-12 17:15:26 V6.6.0
     *@lastEditor cj 2023-06-12 17:15:26 V6.6.0
     *@param {*} bindArray
     *@return $deviceList
     */
    public function getAllForUpgradeForProject($bindArray)
    {
        $sql = 'select D.ID,D.MAC,D.Location,D.Firmware,D.Status,A.Name from ' .PROXY_TABLES['devices'].' D 
        left join ' .PROXY_TABLES['personalAccount'].' A on A.Account = D.Node 
        where (D.MAC like :MAC OR md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Node) OR D.Location like :Location) 
        AND D.Firmware != :Firmware AND D.Firmware like :Model AND D.Status = 1 AND D.MngAccountID = :ParentID';
        $deviceList = $this->execute($sql, $bindArray);
        // 数据解密
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /**
     * @description
     * @param
     * @param $notFirmwares
     * @param $projectIDs
     * @return array
     * @throws \Exception
     * <AUTHOR> 2025/2/17 18:04 v7.1.0
     * @lastEditor clay 2025/2/17 18:04 v7.1.0
     */
    public function getAllForUpgradeForProjects($notFirmwares, $projectIDs)
    {
        if (count($projectIDs) == 0) {
            return [];
        }
        $bindArray = [];
        if (count($notFirmwares) == 0) {
            $notFirmwareConditionStr = '';
        }else {
            list($notFirmwareStr, $bindArray) = $this->share->util->getImplodeData($notFirmwares, 'Firmwares');
            $notFirmwareConditionStr = ' AND D.Firmware NOT IN ('.$notFirmwareStr.') ';
        }
        list($projectIDsStr, $bindArrayProject) = $this->share->util->getImplodeData($projectIDs, 'ParentID');
        $bindArray = array_merge($bindArray, $bindArrayProject);
        $sql = 'SELECT
	D.ID,
	D.UnitID,
	A.UUID as UserUUID,
	D.MAC,
	D.Location,
	D.Firmware,
	D.Status,
	D.MngAccountID,
	A.Name 
FROM
	' .PROXY_TABLES['devices'].' D
	LEFT JOIN ' .PROXY_TABLES['personalAccount'].' A ON A.Account = D.Node 
WHERE
    D.Type != 2
	'.$notFirmwareConditionStr.'
	AND D.Status = 1 
	AND D.MngAccountID IN ('.$projectIDsStr.')';
        $deviceList = $this->execute($sql, $bindArray);
        // 数据解密
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /**
     * @description
     * @param
     * @param $bindArray
     * @return array
     * @throws \Exception
     * <AUTHOR> 2025/2/17 14:36 v7.1.0
     * @lastEditor clay 2025/2/17 14:36 v7.1.0
     */
    public function getDeviceUpgradeInfo($bindArray)
    {
        $sql = 'SELECT
	D.UnitID,
	A.UUID as UserUUID,
	U.MAC,
	U.UpgradeRomVerID,
	U.`Status`
FROM
    ' .PROXY_TABLES['upgradeRomDevices'].' as U
	join ' .PROXY_TABLES['devices'].' D on D.MAC=U.MAC
	LEFT JOIN ' .PROXY_TABLES['personalAccount'].' A ON A.Account = D.Node 
WHERE
	U.UpgradeRomVerID IN (:VersionIDs)';
        return $this->execute($sql, $bindArray);
    }

    /*
     *@description 获取可添加屏保的设备列表
     *<AUTHOR> 2023-07-10 10:24:25 V6.6.0
     *@lastEditor cj 2023-07-10 10:24:25 V6.6.0
     *@param {*} deviceModel 设备型号
     *@param {*} uuid 设备UUID
     *@param {*} projectId 项目Id
     *@param {*} field 要获取的信息字段
     *@return $deviceList 设备列表
     */
    public function getScreenSaverDeviceList($deviceModel, $uuid, $projectId, $field)
    {
        //拼接可用的设备型号
        $deviceSupport = [];
        foreach ($deviceModel as $name) {
            $deviceSupport[] = "Firmware like '{$name}.%'";
        }
        $deviceSupport = '('.join(' or ', $deviceSupport) . ')';

        $bindArray = [':MngAccountID' => $projectId];
        $uuidCond = '';
        if (!empty($uuid)) {
            $uuidCond = ' and UUID = :UUID ';
            $bindArray[':UUID'] = $uuid;
        }

        $sql = "select $field from ". $this->table ." where MngAccountID = :MngAccountID and Status = 1 and Grade in (".DEVICE_GRADE_COM.",".DEVICE_GRADE_UNIT.")  
            and $deviceSupport $uuidCond and MAC not in (select MAC from ". PROXY_TABLES['screenSaver'] ." where MngAccountID = :MngAccountID)";
        $deviceList = $this->execute($sql, $bindArray);
        return $deviceList;
    }
    /*
     *@description 根据楼栋ID，获取所有该楼栋管理设备
     *<AUTHOR> 2023-09-13 17:57:48 V6.7.0
     *@lastEditor kxl 2023-09-13 17:57:48 V6.7.0
     *@param {*} communityId
     *@param {*} buildingId
     *@return
     */
    public function getBuildManageDev($communityId, $buildingId)
    {
        $sql = 'select * from Devices where Flags&8=8 and MngAccountID=:MngAccountID and Grade=1
        union select D.* from Devices D join PubDevMngList P on D.ID=P.DevicesID where P.UnitID=:UnitID and Grade=1';

        $list = $this->execute($sql, [':MngAccountID'=>$communityId, ':UnitID'=>$buildingId]);
        return $list;
    }

    /**
     * @description: 搜索指定的设备数据
     * @param {array} $searchArr
     * $searchArr = [
        'device' => $device,
        'communityIds' => $communityIds,
        'officeIds' => $officeIds,
        'singIds' => $singIds,
        'offset' => $offset,
        'row' => $row,
        ];
     * @param {enum} $status 设备状态 all|0|1 所有|离线|在线
     * @param {string} $node 单住户主账号Node
     * @return array
     * @throws \Exception
     * @author: csc 2023/9/18 16:02 V6.7.0
     * @lastEditors: csc 2023/9/18 16:02 V6.7.0
     */
    public function getDevicesInfoList($searchArr, $status = 'all', $node = '')
    {
        $sql = [];

        $device = $searchArr['device'];
        $offset = $searchArr['offset'];
        $row = $searchArr['row'];

        $bindArray = [
            ':Device' => "%$device%"
        ];
        $deviceStatus = '';
        $deviceNode = '';
        $brand = '';
        $status = is_numeric($status) ? intval($status) : $status;
        $projectIds = array_merge($searchArr['communityIds'], $searchArr['officeIds']);
        //查询社区三方摄像头(不在线的不查)
        if (!empty($searchArr['communityIds']) && $status !== 0) {
            list($ids, $bind) = $this->share->util->getImplodeData($searchArr['communityIds'], 'communityId');
            $sql[] = "(select D.ID, D.UUID, D.MAC, D.Location as DeviceName, A.ID as ProjectID, 1 as DeviceStatus, 1 as IsCamera, 0 as Brand,NULL as Type,NULL as Grade,NULL as UnitID,NULL as Node, A.Location as SiteName
from ThirdPartCamera D left join Account A on A.UUID = D.ProjectUUID where A.ID in ($ids) and D.Location like :Device $deviceStatus)";
            $bindArray = array_merge($bindArray, $bind);
        }
        //查询所有单住户三方摄像头（不在线的不查；有指定具体的单住户，也不查）
        if (!empty($searchArr['singIds']) && $status !== 0 && empty($node)) {
            list($ids, $bind) = $this->share->util->getImplodeData($searchArr['singIds'], 'singIdCamera');
            $sql[] = "(select D.ID, D.UUID, D.MAC, D.Location as DeviceName, P.ParentID as ProjectID, 1 as DeviceStatus, 1 as IsCamera, 0 as Brand,NULL as Type,NULL as Grade,NULL as UnitID,NULL as Node, IF(P.RoomNumber='', AKCSMapping.NameMapping.DeColumn, P.RoomNumber) as SiteName
 from PersonalThirdPartCamera D left join PersonalAccount P on P.UUID = D.PersonalAccountUUID 
 left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = P.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(P.Name) where P.ParentID in ($ids) and D.Location like :Device $deviceStatus)";
            $bindArray = array_merge($bindArray, $bind);
        }
        //查询社区、办公设备
        if (!empty($projectIds)) {
            list($ids, $bind) = $this->share->util->getImplodeData($projectIds, 'projId');
            if ($status !== 'all' && in_array($status, [0, 1])) {
                $deviceStatus = "And D.Status=:Status";
                $bindArray[':Status'] = $status;
            }
            $sql[] = "(select D.ID, D.UUID, D.MAC, D.Location as DeviceName, D.MngAccountID as ProjectID, D.Status as DeviceStatus, 0 as IsCamera, D.Brand, D.Type,D.Grade,D.UnitID,D.Node, A.Location as SiteName
from Devices D left join Account A on A.ID = D.MngAccountID where D.MngAccountID in ($ids) and (D.MAC like :Device or D.Location Like :Device) $deviceStatus)";
            $bindArray = array_merge($bindArray, $bind);
        }
        //查询用户设备
        if (!empty($searchArr['singIds'])) {
            $deviceStatus1 = '';
            list($ids, $bind) = $this->share->util->getImplodeData($searchArr['singIds'], 'singId');
            if ($status !== 'all' && in_array($status, [0, 1])) {
                $deviceStatus1 = "And D.Status=:Status";
                $bindArray[':Status'] = $status;
            }
            if (!empty($node)) {
                $deviceNode = "And D.Node=:Node";
                $bindArray[':Node'] = $node;
                //单住户只查ak的设备
                $brand = "And D.Brand=0";
            }
            $sql[] = "(select D.ID, D.UUID, D.MAC, D.Location as DeviceName, P.ParentID as ProjectID, D.Status as DeviceStatus, 0 as IsCamera, D.Brand, D.Type,NULL as Grade,NULL as UnitID,D.Node, IF(P.RoomNumber='', AKCSMapping.NameMapping.DeColumn, P.RoomNumber) as SiteName
 from PersonalDevices D left join PersonalAccount P on D.Node = P.Account 
 left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = P.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(P.Name) where P.ParentID in ($ids) and P.Role = 10 and (D.MAC like :Device or D.Location Like :Device) $deviceStatus1 $deviceNode $brand)";
            $bindArray = array_merge($bindArray, $bind);
        }

        $sql = join(' union ', $sql);
        if (empty($sql)) {
            return [0, []];
        }

        $totalSql = "select count(1) from ($sql) as combined_tables";
        $total = intval($this->execute($totalSql, $bindArray)[0]['count(1)']);
        if (empty($total)) {
            return [0, []];
        }

        $dataSql = "select * from ($sql) as combined_tables order by MAC asc limit $offset, $row;";
        $data = $this->execute($dataSql, $bindArray);
        return [$total, $data];
    }


    /**
     * @description:查询Devices表社区共享设备（Grade=1）并且设备是不是管理该楼栋，Flags获取第四位管理机身份
     * @param $parentId 社区管理员ID
     * @param $unitId  单元ID
     * @return array
     * @author: shoubin.chen 2023-11-27 15:24:57 v6.7.1
     * @lastEditor: shoubin.chen 2023-11-27 15:24:57 v6.7.1
     */
    public function getComDevicesAndNotManageUnit($parentId, $unitId)
    {
        $sql = "select ID,MAC,Status,Relay,SecurityRelay,Location,Type,'' as UnitName 
        from Devices where Grade = 1 and MngAccountID = :MngAccountID and Type in (0,1,50)
        and (ID in (select DevicesID from PubDevMngList where UnitID = :UnitID) or Flags & 8 = 8)";
        $bindArray = [":MngAccountID" => $parentId, ":UnitID" => $unitId];
        $data = $this->execute($sql, $bindArray);
      
        return $data;
    }

    /**
     * @description:查找社区下所有个人设备
     * @param:
     * @return array
     * @author: shoubin.chen 2024/8/28 10:19:37 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/28 10:19:37  V6.8.1
     */
    public function getCommunityPerDevices($communityUUID)
    {
        $sql = "SELECT
                  D.AccountUUID AS Community,
                  CU.UnitName,
                  CU.UnitName AS Unit,
                  CR.RoomName AS RoomNumber,
                  D.MAC 
                FROM
                  Devices D
                  LEFT JOIN CommunityUnit CU ON D.CommunityUnitUUID = CU.UUID
                  LEFT JOIN PersonalAccount PA ON PA.Account = D.Node
                  LEFT JOIN CommunityRoom CR ON PA.CommunityRoomUUID = CR.UUID 
                WHERE
                  D.AccountUUID = :Community 
                  AND D.Grade = 3";
        $bindArray = [":Community" => $communityUUID];
        $data = $this->execute($sql, $bindArray);

        return $data;
    }

}