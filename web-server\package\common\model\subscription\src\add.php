<?php
/**
 * @description:
 * @author: csc 2023/12/15 13:52 V6.7.1
 * @lastEditors: csc 2023/12/15 13:52 V6.7.1
 */

namespace package\common\model\subscription\src;

use package\common\model\subscription\config\Code;

trait Add
{
    /**
     * @description: 创建订阅
     * @param {array} Users 续费的用户列表,getSubscribeUserInfo获取的数据
     * @param {int} IntervalType 周期类型 0:月 1:季 2:年 3:天
     * @param {int} Cycles 周期数 0：不限
     * @param {string} StartTime 开始时间
     * @param {string} TotalPrice 每期总金额
     * @param {string} SubscriptionUUID 旧订阅的订阅ID，有旧的订阅ID代表着编辑（取消后新建）
     * @param {int} Type 订阅类型 1: 单住户续费 2: 社区续费 3: 办公续费
     * @param {string} PayerUUID 付款人UUID
     * @param {string} PayerType 支付人类型；0=>终端用户,1=>物业,2=>install,3=>dis,4=>sub dis
     * @param {string} TimeZone 创建时使用的时区
     * @return void
     * @throws \Exception
     * @author: csc 2023/12/15 14:14 V6.7.1
     * @lastEditors: csc 2023/12/15 14:14 V6.7.1
     */
    public function createSubscription()
    {
        //获取参数
        $params = ['Users', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string', 'TotalPrice:string',
            'SubscriptionUUID?:uuid', 'Type:enum(1,2,3)', 'PayerUUID', 'PayerType', 'TimeZone', 'VideoStorageInfo',
            PROXY_ROLE['distributorUUID'], PROXY_ROLE['installerUUID'], 'ThirdLockInfo'];
        list($users, $intervalType, $cycles, $startTime, $totalPrice, $subscriptionUUID, $type, $payerUUID, $payerType, $timeZone, $videoStorageInfo, $disUUID, $insUUID, $thirdLockInfo) = $this->getParams($params);

        $projectType = 0;
        if ($type == SUBSCRIBE_TYPE['singleRenew']) {
            $projectType = 1;
        } elseif ($type == SUBSCRIBE_TYPE['communityRenew']) {
            $projectType = 2;
        } elseif ($type == SUBSCRIBE_TYPE['officeRenew']) {
            $projectType = 3;
        }
        //V6.7.0暂不增加自定义开始时间
        $startTime = date('Y-m-d H:i:s');

        //如果存在SubscriptionUUID(重新编辑订阅-检测条件)
        if ($subscriptionUUID) {
            //判断原订阅是否存在,不存在则报错
            $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
            if (empty($subscription)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
            }

            //判断原订阅是否当前用户创建，否则报错
            if ($subscription[0]['PayerUUID'] != $payerUUID) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_BELONG_PAYER_ERROR]);
            }

            // 先注释掉，原先单类型，后面改为混合类型，此处必定报错，反之同样
            //判断原订阅类型是否和当前类型一致，否则报错
//            if ($subscription[0]['Type'] != $type) {
//                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_SAME_ERROR]);
//            }
        }

        //检测apt项目是否已经在有效订阅中
        $personalAccountUUIDs = array_column($users['all'], 'UUID');
        $existsItem = $this->utils->self->checkItemInSubscription($personalAccountUUIDs);
        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的apt是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的用户，并且不在当前订阅中
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }
            if (!empty($existsItem)) {
                $this->loadUtil('account');
                $result = $this->utils->account->personalAccountSelectByArray([['UUID', $existsItem]], 'Name');
                $personalAccountName = implode(',', array_column($result, 'Name'));
                $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
            }
        }

        //检测视频存储项目是否已经在有效订阅中
        $siteUUIDs = array_column($videoStorageInfo['site'], 'UUID');
        $existsItem = $this->utils->self->checkVideoStorageItemInSubscription($siteUUIDs);
        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的视频存储是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的用户，并且不在当前订阅中
                $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
                if (empty($subscription)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
                }
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkVideoStorageInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }

            if (!empty($existsItem)) {
                if ($type == 1) {
                    $this->loadUtil('account');
                    $result = $this->utils->account->personalAccountSelectByArray([['UUID', $existsItem]], 'Name');
                    $personalAccountName = implode(',', array_column($result, 'Name'));
                    $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
                } elseif ($type == 2) {
                    $this->loadUtil('account');
                    $result = $this->utils->account->accountSelectByArray([['UUID', $existsItem]], 'Location');
                    $personalAccountName = implode(',', array_column($result, 'Location'));
                    $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
                }
            }
        }

        //检测三方锁是否已经在有效订阅中
        $thirdLockUUIDs = array_column($thirdLockInfo['config'], 'LockUUID');
        $existsItem = $this->utils->self->checkThirdLockItemInSubscription($thirdLockUUIDs);
        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的视频存储是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的三方锁，并且不在当前订阅中
                $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
                if (empty($subscription)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
                }
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkThirdLockInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }

            if (!empty($existsItem)) {
                $this->loadUtil('dormakaba', true);
                $this->loadUtil('itec', true);
                $dormakabaName = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $existsItem]]);
                $itecName = $this->utils->_common->itec->getItecLock([['UUID', $existsItem]]);
                $name = implode(',', array_merge(array_column($dormakabaName, 'Name'), array_column($itecName, 'Name')));
                $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$name]);
            }
        }

        //检测订阅总金额
        $this->loadProvider('billsysUtil');
        $chargeService = $this->services->billsysUtil;
        $userPrice = $videoPrice = $thirdLockPrice = 0;
        if (!empty($users['all'])) {
            list($userCharges, $userPrice) = $chargeService->getSubscriptionChargeDataAndPrice($users, $intervalType, $type);
        }
        if (!empty($videoStorageInfo['site'])){
            list($videoCharges, $videoPrice) = $chargeService->getVideoStorageSubscriptionChargeDataAndPrice($videoStorageInfo, $intervalType, $type);
        }
        if (!empty($thirdLockInfo['config'])) {
            list($thirdLockCharges, $thirdLockPrice) = $chargeService->getThirdLockSubscriptionChargeDataAndPrice($thirdLockInfo, $intervalType, $type);
        }

        $price = \share\util\outputComputedCount(\share\util\inputComputedCount($userPrice + $videoPrice + $thirdLockPrice));
        if ($price != $totalPrice) {
            $this->output->echoErrorMsg(STATE_PARAMS_ERROR, ['externalErrorObj' => Code::EXT_STATE_PRICE_ERROR]);
        }

        $mixArr = [];
        $mixCount = 0;
        if ($projectType == 1) {
            if (!empty($userCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['singleRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['singleRenew'];
            }
            if (!empty($videoCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['singleVideoStorageRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['singleVideoStorageRenew'];
            }
            if (!empty($thirdLockCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['singleThirdLockRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['singleThirdLockRenew'];
            }
            if ($mixCount > 1) {
                $type = SUBSCRIBE_TYPE['mixRenew'];
            }
        } elseif ($projectType == 2) {
            if (!empty($userCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['communityRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['communityRenew'];
            }
            if (!empty($videoCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['communityVideoStorageRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['communityVideoStorageRenew'];
            }
            if (!empty($thirdLockCharges)) {
                $mixCount++;
                $type = SUBSCRIBE_TYPE['communityThirdLockRenew'];
                $mixArr[] = SUBSCRIBE_TYPE['communityThirdLockRenew'];
            }
            if ($mixCount > 1) {
                $type = SUBSCRIBE_TYPE['mixRenew'];
            }
        }
        $mixArr = empty($mixArr) ? $type : $mixArr;
        $mixType = $this->share->util->getDecimalFromBits($mixArr);

        //如果存在SubscriptionUUID(重新编辑订阅-取消原订阅)
        if ($subscriptionUUID) {
            //判断状态是否正常或者进行中的，才需要取消原订阅
            if (intval($subscription[0]['Status']) === 0 or intval($subscription[0]['Status']) === 1) {
                $chargeService->cancelSubscription($subscriptionUUID, SUBSCRIPTION_END_REASON_ARRAY[5]);
            }
        }

        //整理数据
        $this->loadUtil('account');
        $subscriptionNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $payerData = $this->utils->account->getManagerInfoUUID($payerUUID);
        $this->loadUtil('billingInfo');
        $payerData = $this->utils->billingInfo->getManagerBillingInfo($payerData['ID']);

        //查询付款用户的stripe帐号
        $subscriptionUser = $this->dao->subscriptionUsers->selectByPayerUUID($payerUUID);
        $payPlatUserID = !empty($subscriptionUser) ? $subscriptionUser[0]['PayPlatID'] : '';

        //格式：云端type =》 计费系统type
        //云端type：//1-单住户续费 2-社区续费 3-办公续费 4-RentManager续费 5-单住户视频存储续费 6-社区视频存储续费 7-多类型订单混合付费 8-单住户三方锁续费 9-社区三方锁续费
        //计费系统type：1=单住户高级功能续费;2=社区apt、pm app续费;3=办公用户续费 4=rent manager续费 5=视频存储续费 6=多订单（父子订单）合并续费 7=三方锁续费
        $typeMap = [
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 5,
            7 => 6,
            8 => 7,
            9 => 7,
        ];
        $realBmType = $typeMap[intval($type)];

        //向计费系统请求订单
        $payParams = [
            'TotalPrice' => $totalPrice,
            'Type' => $realBmType,
            'Payer' => $payerData['Account'],
            'PayerEmail' => $payerData['Email'],
            'PayerType' => $payerType,
            'IntervalType' => $intervalType,
            'Cycles' => $cycles,
            'PayPlatUserID' => $payPlatUserID,
            'StartTime' => $startTime,
        ];
        $result = $chargeService->createSubscription($payParams);

        //插入subscriptionList、subscriptionEndUserList表
        $uuid = $this->share->util->uuid();

        list($projectUUID, $isBatch) = $this->determineProjectUUIDAndBatch($projectType, $videoStorageInfo, $users, $thirdLockInfo);

        $id = $this->dao->subscriptionList->insert([
            'SubscriptionNumber' => $subscriptionNumber,
            'Status' => 0,
            'TotalPrice' => $totalPrice * 100,
            'Discount' => 100,
            'Type' => $type,
            'PayerUUID' => $payerUUID,
            'PayerEmail' => $payerData['Email'],
            'PayerType' => $payerType,
            'DisUUID' => $disUUID,
            'InsUUID' => $insUUID,
            'ProjectUUID' => $projectUUID,
            'IsBatch' => $isBatch,
            'IntervalType' => $intervalType,
            'Cycles' => $cycles,
            'StartTime' => $startTime,
            'TimeZone' => $timeZone,
            'WebHookToken' => $result['Token'],
            'BmSubscriptionNumber' => $result['Number'],
            'PayCode' => $result['Code'],
            'UUID' => $uuid, 'MixType' => $mixType
        ]);

        $userCharges = array_column($userCharges, null, 'ID');

        if ($projectType == 1) {
            $endUserType = SUBSCRIBE_END_USER_TYPE['singleRenew'];
        } elseif ($projectType == 2) {
            $endUserType = SUBSCRIBE_END_USER_TYPE['communityRenew'];
        } else {
            $endUserType = SUBSCRIBE_END_USER_TYPE['officeRenew'];
        }

        // 插入用户续费信息
        foreach ($users['all'] as $user) {
            $endUserUUID = $this->share->util->uuid();
            $this->dao->subscriptionEndUserList->insert([
                'SubscriptionUUID' => $uuid,
                'Amount' => $userCharges[$user['ID']]['SubscriptionPrice'] * 100,
                'Discount' => 100,
                'PersonalAccountUUID' => $user['UUID'],
                'ChargeData' => json_encode($userCharges[$user['ID']]),
                'ProjectUUID' => $user['ProjectUUID'],
                'ProjectName' => $user['ProjectName'],
                'UUID' => $endUserUUID,
                'SiteUUID' => null, 'Type' => $endUserType
            ]);
        }

        if ($projectType === 1) {
            $endUserType = SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew'];
        } elseif ($projectType === 2) {
            $endUserType = SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew'];
        }

        // 插入视频存储付费信息
        $videoCharges = array_column($videoCharges, null, 'UUID');
        foreach ($videoStorageInfo['site'] as $site) {
            $personalAccountUUID = null;
            if ($projectType == 1) {
                $personalAccountUUID = $site['UUID'];
            }
            $endUserUUID = $this->share->util->uuid();
            $this->dao->subscriptionEndUserList->insert([
                'SubscriptionUUID' => $uuid,
                'Amount' => $videoCharges[$site['UUID']]['SubscriptionPrice'] * 100,
                'Discount' => 100,
                'PersonalAccountUUID' => $personalAccountUUID,
                'ChargeData' => json_encode($videoCharges[$site['UUID']]['Model']),
                'ProjectUUID' => $site['ProjectUUID'],
                'ProjectName' => $site['ProjectName'],
                'UUID' => $endUserUUID,
                'SiteUUID' => $site['UUID'], 'Type' => $endUserType
            ]);
        }

        if ($projectType === 1) {
            $endUserType = SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'];
        } else {
            $endUserType = SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew'];
        }
        // 插入三方锁付费信息
        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLockInfo['site'],'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLockInfo['config'],'LockUUID');
        foreach ($thirdLockCharges as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];

            $insertData = [
                'SubscriptionUUID' => $uuid,
                'Amount' => $this->share->util->inputComputedCount($item['SubscriptionPrice']),
                'Discount' => 100,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'ChargeData' => json_encode($item['Model']),
                'ProjectUUID' => $thirdLockSite[$siteUUID]['ProjectUUID'],
                'ProjectName' => $thirdLockSite[$siteUUID]['ProjectName'],
                'UUID' => $this->share->util->uuid(),
                'SiteUUID' => null, 'Type' => $endUserType, 'Brand' => $config['Brand'], 'LockUUID' => $lockUUID
            ];

            $this->dao->subscriptionEndUserList->insert($insertData);
        }

        return [
            'ID' => $id,
            'UUID' => $uuid,
            'bmUrl' => BMSUBSCRIPTIONURL. '?subscriptionNumber='.$result['Number'].'&token='.$result['Token'].'&code='.$result['Code']
        ];
    }


    /**
     * @description: RentManager创建订阅
     * @param {int} Cycles 周期数 0：不限
     * @param {string} TotalPrice 每期总金额
     * @param {string} StartTime 开始时间
     * @param {string} SubscriptionUUID 旧订阅的订阅ID，有旧的订阅ID代表着编辑（取消后新建）
     * @param {string} PayerUUID 付款人UUID
     * @param {string} TimeZone 创建时使用的时区
     * @param {array} RentManagerCustomerList 续费的RentManagerUUID列表
     * @return void
     * @throws \Exception
     * @author: cpl 2024/11/25 14:14 V7.1.0
     */
    public function createSubscriptionForRentManager()
    {
        $params = ['Cycles:between-with-scope("0","180")', 'TotalPrice:string', 'SubscriptionUUID?:uuid', 'PayerUUID',  'TimeZone', 'RentManagerCustomerList','DistributorUUID', 'InstallerUUID'];
        list($cycles, $totalPrice, $subscriptionUUID, $payerUUID, $timeZone, $rentManagerCustomerList, $disUUID, $insUUID) = $this->getParams($params);

        //如果存在SubscriptionUUID(重新编辑订阅-检测条件)
        if ($subscriptionUUID) {
            //判断原订阅是否存在,不存在则报错
            $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
            if (empty($subscription)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
            }

            //判断原订阅是否当前用户创建，否则报错
            if ($subscription[0]['PayerUUID'] != $payerUUID) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_BELONG_PAYER_ERROR]);
            }

            //判断原订阅类型不是rentManager订阅，否则报错
            if ($subscription[0]['Type'] != SUBSCRIBE_TYPE['rentManagerRenew']) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_SAME_ERROR]);
            }
        }

        //检测rentManagerCompany是否已经在有效订阅中
        $existsItem = $this->utils->self->checkRentManagerInSubscription($rentManagerCustomerList);

        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的rentManager是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的rentManager，并且不在当前订阅中
                $existsItem = $this->dao->rentManagerSubscriptionList->checkInOtherSubscription($existsItem, $subscription[0]['UUID']);
            }
            if (!empty($existsItem)) {
                $this->loadUtil('rentManager', true);
                $result = $this->utils->_common->rentManager->getRentManagerCustomerCompanyList($existsItem);
                if (count($result) > 0) {
                    $companyName = implode(',', array_column($result, 'CompanyName'));
                    $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$companyName]);
                }
            }
        }

        //检测订阅总金额
        $this->loadUtil('rentManager', true);
        $targetTotalPrice = $this->utils->_common->rentManager->computedPriceForRentManager($rentManagerCustomerList, RENT_MANAGER_COMPUTED_SUBSCRIPTION_TOTAL_PRICE_TYPE, 1, $disUUID, $insUUID);
        $monthlyFee = $this->utils->_common->rentManager->getRentManagerMonthlyFee($disUUID, $insUUID);

        if ($this->share->util->inputComputedCount($targetTotalPrice) != $this->share->util->inputComputedCount($totalPrice)) {
            $this->output->echoErrorMsg(STATE_PARAMS_ERROR, ['externalErrorObj' => Code::EXT_STATE_PRICE_ERROR]);
        }

        //如果存在SubscriptionUUID(重新编辑订阅-取消原订阅)
        if ($subscriptionUUID) {
            //判断状态是否正常或者进行中的，才需要取消原订阅
            if (intval($subscription[0]['Status']) === 0 or intval($subscription[0]['Status']) === 1) {
                $this->loadProvider('billsysUtil');
                $chargeService = $this->services->billsysUtil;
                $chargeService->cancelSubscription($subscriptionUUID, SUBSCRIPTION_END_REASON_ARRAY[5]);
            }
        }

        //整理数据
        $this->loadUtil('account');
        $subscriptionNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $payerData = $this->utils->account->getManagerInfoUUID($payerUUID);
        $this->loadUtil('billingInfo');
        $payerData = $this->utils->billingInfo->getManagerBillingInfo($payerData['ID']);

        //查询付款用户的stripe帐号
        $subscriptionUser = $this->dao->subscriptionUsers->selectByPayerUUID($payerUUID);
        $payPlatUserID = !empty($subscriptionUser) ? $subscriptionUser[0]['PayPlatID'] : '';

        //rentManager都是INS付费
        $payerType = 2;
        // rentManager都是按月计费
        $intervalType = 0;

        $startTime = date('Y-m-d H:i:s');

        $this->loadProvider('billsysUtil');
        //向计费系统请求订单
        $payParams = [
            'TotalPrice' => $totalPrice,
            'Type' => SUBSCRIBE_TYPE['rentManagerRenew'],
            'Payer' => $payerData['Account'],
            'PayerEmail' => $payerData['Email'],
            'PayerType' => $payerType,
            'IntervalType' => $intervalType,
            'Cycles' => $cycles,
            'PayPlatUserID' => $payPlatUserID,
            'StartTime' => $startTime,
        ];
        $result = $this->services->billsysUtil->createSubscription($payParams);

        //插入subscriptionList、rentManagerSubscriptionList
        $uuid = $this->share->util->uuid();
        $mixType = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
        $id = $this->dao->subscriptionList->insert([
            'SubscriptionNumber' => $subscriptionNumber,
            'Status' => 0,
            'TotalPrice' => $totalPrice * 100,
            'Discount' => 100,
            'Type' => SUBSCRIBE_TYPE['rentManagerRenew'],
            'PayerUUID' => $payerUUID,
            'PayerEmail' => $payerData['Email'],
            'PayerType' => $payerType,
            'DisUUID' => $disUUID,
            'InsUUID' => $insUUID,
            'ProjectUUID' => "",
            'IsBatch' => 1,
            'IntervalType' => $intervalType,
            'Cycles' => $cycles,
            'StartTime' => $startTime,
            'TimeZone' => $timeZone,
            'WebHookToken' => $result['Token'],
            'BmSubscriptionNumber' => $result['Number'],
            'PayCode' => $result['Code'],
            'UUID' => $uuid, 'MixType' => $mixType
        ]);

        $rentManagerCustomerInfos = $this->utils->_common->rentManager->getRentManagerCustomerByArray([['UUID',$rentManagerCustomerList]]);

        foreach ($rentManagerCustomerInfos as $customerInfo) {
            $insertData = [
                'UUID' =>  $this->share->util->uuid(),
                'SubscriptionUUID' => $uuid,
                'RentManagerCustomerUUID' => $customerInfo['UUID'],
                'MonthlyFee' => $this->share->util->inputComputedCount($monthlyFee),
                'ChargeData' => json_encode($customerInfo)
            ];
            $this->dao->rentManagerSubscriptionList->insert($insertData);
        }

        return [
            'ID' => $id,
            'UUID' => $uuid,
            'bmUrl' => BMSUBSCRIPTIONURL. '?subscriptionNumber='.$result['Number'].'&token='.$result['Token'].'&code='.$result['Code']
        ];
    }

    /**
     * @description: 处理得到项目UUID和是否跨社区支付
     * @param {int}     $projectType        项目类型,1=单住户，2=社区，3=办公
     * @param {array}   $videoStorageInfo
     * @param {array}   $users
     * @param {array}   $thirdLockInfo
     * @return array    [项目UUID，是否跨项目]
     * @author: shoubin.chen 2024/11/6 14:07:59 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/6 14:07:59  V7.1.0
     */
    private function determineProjectUUIDAndBatch($projectType, $videoStorageInfo, $users, $thirdLockInfo)
    {
        $videoProjectUUIDs = array_values(array_unique(array_column(
            isset($videoStorageInfo['site']) ? $videoStorageInfo['site'] : [],
            'ProjectUUID'
        )));

        $userProjectUUIDs = array_values(array_unique(array_column(
            isset($users['all']) ? $users['all'] : [],
            'ProjectUUID'
        )));

        $thirdLockProjectUUIDs = array_values(array_unique(array_column(
            isset($thirdLockInfo['site']) ? $thirdLockInfo['site'] : [],
            'ProjectUUID'
        )));

        // 确保合并的数组不是 null
        $videoProjectUUIDs = $videoProjectUUIDs !== null ? $videoProjectUUIDs : [];
        $userProjectUUIDs = $userProjectUUIDs !== null ? $userProjectUUIDs : [];
        $thirdLockProjectUUIDs = $thirdLockProjectUUIDs !== null ? $thirdLockProjectUUIDs : [];
        $projectUUIDs = array_values(array_unique(array_merge($videoProjectUUIDs, $userProjectUUIDs, $thirdLockProjectUUIDs)));

        $projectUUID = "";
        $isBatch = 0;

        if ($projectType == 1) {
            //单住户的ProjectUUID为ins的uuid
            $projectUUID = $projectUUIDs[0];
        } elseif ($projectType == 2) {
            //社区如果是同一个
            if (count($projectUUIDs) == 1) {
                $projectUUID = $projectUUIDs[0];
            }
        } elseif ($projectType == 3) {
            //办公只会存在同个项目的
            $projectUUID = $projectUUIDs[0];
        }

        if (count($projectUUIDs) > 1 && $projectType != 1) {
            $isBatch = 1;
        }
        return [$projectUUID, $isBatch];
    }

}

