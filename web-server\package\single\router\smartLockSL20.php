<?php

namespace package\single\router;

use framework\BasicRouter;

class smartLockSL20 extends BasicRouter
{
    public function exec()
    {
        $this->setRouterName('v3', 'app', 'single', 'smartLockSL20', 'getLinkDeviceList')
            ->setMethod('GET')
            ->addParams()
            ->addRoles(RPERENDMROLE)
            ->setAuth('app')
            ->setControl('smartLockSL20\\getLinkDeviceListForApp');

        return $this->getRouters();
    }
}