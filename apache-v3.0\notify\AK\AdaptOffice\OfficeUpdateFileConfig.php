<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.AdaptOffice.proto

namespace AK\AdaptOffice;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.AdaptOffice.OfficeUpdateFileConfig</code>
 */
class OfficeUpdateFileConfig extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated string dev_uuid_list = 1;</code>
     */
    private $dev_uuid_list;
    /**
     *在设备被删除时候需要传递
     *
     * Generated from protobuf field <code>string mac = 2;</code>
     */
    private $mac = '';

    public function __construct() {
        \GPBMetadata\AKAdaptOffice::initOnce();
        parent::__construct();
    }

    /**
     * Generated from protobuf field <code>repeated string dev_uuid_list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDevUuidList()
    {
        return $this->dev_uuid_list;
    }

    /**
     * Generated from protobuf field <code>repeated string dev_uuid_list = 1;</code>
     * @param string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDevUuidList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->dev_uuid_list = $arr;

        return $this;
    }

    /**
     *在设备被删除时候需要传递
     *
     * Generated from protobuf field <code>string mac = 2;</code>
     * @return string
     */
    public function getMac()
    {
        return $this->mac;
    }

    /**
     *在设备被删除时候需要传递
     *
     * Generated from protobuf field <code>string mac = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setMac($var)
    {
        GPBUtil::checkString($var, True);
        $this->mac = $var;

        return $this;
    }

}

