<?php
/*
 * @Description: 根据从账户ID，管理员设置代理为主账户
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
class CSetMngToMainAliasWSubId implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp,$cMessage;
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setMngToMainAliasWSubId#");
        $data = $db->querySList("select A.ID,A.Account from PersonalAccount A join PersonalAccount B on A.ID = B.ParentID where B.ID = :ID and B.Role in(".PERENDSROLE.",".COMENDSROLE.")",[":ID"=>$id]);
        if(count($data) == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $data = $data[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}