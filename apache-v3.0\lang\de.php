<?php
  const MSGTEXT = [ 

"accountExits"=>"Dieser Account existiert bereits",
"accountNotExit"=>"Konto existiert nicht",
"accountIncorrect"=>"Ungültiger Benutzername oder Passwort",
"accountNIncorrect"=>"Ungültiger Account",
"activeEmpty"=>"Aktiver Wert ist erforderlich",
"addFail"=>"Hinzufügen fehlgeschlagen",
"addSuccess"=>"Hinzufügen erfolgreich.",
"addSuccessPw"=>"Das Hinzufügen erfolgreich ist das Passwort '%s'.",
"addTmpKeyFail"=>"Fügen Sie den Temperaturschlüssel hinzu, das fehlgeschlagen ist, bitte versuchen Sie es erneut",
"aptDuplicated"=>"Apartment %S wird dupliziert",
"aptDigits"=>"Apartment %S ist ungültig, was zwischen 1 und 6 Ziffernszahlen liegen muss",
"aptExit"=>"Apartment %s existiert bereits",
"abnormal"=>"Abnormal",
"activation"=>"Aktivierung",
"additionalApp"=>"Zusätzliche App",
"bindDevice"=>"Bitte löschen Sie alle Geräte unter diesem Konto",
"bindMAClibrary"=>"Bitte löschen Sie Mac in der Mac -Bibliothek",
"bindUser"=>"Bitte löschen Sie Benutzer unter diesem Konto",
"buildingBindDevice"=>"Bitte löschen Sie Geräte unter diesem Gebäude",
"buildingBindUser"=>"Bitte löschen Sie Benutzer unter diesem Gebäude",
"buildingDigits"=>"Ungültige Gebäude %S, die zwischen 1 und 2 Ziffern liegen müssen",
"buildingExit"=>"Gebäude existiert bereits",
"BindingDeviceFailed"=>"Bind -Gerät fehlgeschlagen, das Gerät kann an einen anderen Benutzer gebunden oder nicht zur Mac -Bibliothek hinzugefügt werden",
"chcekMacExits"=>"Hinzufügen fehlgeschlagen, MAC -Adresse ungültig oder bereits existiert",
"changePasswdFail"=>"Das Ändern des Passworts fehlgeschlagen",
"changePasswdPEmail"=>"Das Ändern des Passworts erfolgreich. Bitte überprüfen Sie bei E -Mail %s",
"community"=>"Gemeinschaft",
"deleteFail"=>"Löschen fehlgeschlagen",
"deleteSuccess"=>"Erfolgreich löschen",
"deviceTypeEmpty"=>"Gerätetyp ist erforderlich",
"deviceNotFindUser"=>"Gerät nicht gefunden, wenden Sie sich bitte an Ihren Administrator",
"dealSuccess"=>"Erfolg setzen",
"doorUnit"=>"Türeinheit",
"emailExits"=>"E-Mail existiert bereits",
"emailPExit"=>"E -Mail %s existiert bereits",
"emailNotExits"=>"Diese E -Mail existiert nicht.",
"emailDuplicated"=>"E -Mail %S wird dupliziert",
"errorVersion"=>"Fehlerversion",
"emailOrAccountNotExit"=>"Diese E -Mail existiert nicht.",
"firstNameEmpty"=>"Vorname ist erforderlich",
"failed"=>"Fehlgeschlagen",
"family"=>"Familie",
"guardPhone"=>"Wachphot",
"incorrectSipAccount"=>"Kein verfügbares SIP -Konto mehr",
"incorrectSipAccountGroup"=>"Keine verfügbare SIP -Gruppe mehr",
"importDataSuccess"=>"Datenerfolg importieren",
"importFailMACExit"=>"",
"invaildDC"=>"Ungültiger Gerätecode",
"InvalidFile"=>"Ungültige Datei",
"invalidPEmail"=>"Ungültige E -Mail %s",
"invalidPName"=>"Ungültiger Benutzername %s",
"invalidPCalltype"=>"Ungültiger Anruftyp %s",
"invalidPPin"=>"Ungültiger Pin %s",
"invalidPActive"=>"Ungültiger aktiver Wert %s",
"invalidPage"=>"Ungültige Seite",
"invalidPDeviceType"=>"Ungültiger Geräte Typ %s",
"invaildVerCode"=>"Ungültiger Bestätigungscode",
"invalidIdentity"=>"Ungültige Identitätsinformationen!",
"indoorMonitor"=>"Innenmonitor",
"inactivated"=>"Inaktiviert",
"normal"=>"Normal",
"expired"=>"Abgelaufen",
"lastNameEmpty"=>"Nachname ist erforderlich",
"locationEmpty"=>"Ort ist erforderlich",
"locationPLoog"=>"%s zu langer Ort",
"locationLoog"=>"Zu langer Ort",
"loginError"=>"Login Fehler",
"loginFail"=>"Fehler bei der Anmeldung",
"loginSuccess"=>"Einloggen erfolgreich",
"limitIP"=>"Sie versuchen zu oft, bitte versuchen Sie es in 5 Minuten erneut",
"limitDevice"=>"Ihre Geräteummer hat die maximale Grenze erreicht",
"MAC2PLibrary"=>"MAC -Adresse:%S ist ungültig. Bitte überprüfen Sie Ihre Mac -Bibliothek.",
"MAC2Library"=>"MAC -Adresse ungültig, bitte überprüfen Sie Ihre Mac -Bibliothek",
"macExits"=>"MAC -Adresse existiert bereits",
"MACLength"=>"Die MAC -Adresslänge muss 12 Ziffern betragen.",
"modifySuccess"=>"Erfolg ändern",
"modifyFailed"=>"Modify fehlgeschlagen",
"maxHouse"=>"Die Nummer des Benutzers hat die maximale Begrenzung erreicht. Bitte wenden Sie sich an den Administrator",
"modifyAptFail"=>"Speichern fehlgeschlagen! Die Apt Nr. Es existiert bereits, Sie sollten es zuerst löschen.",
"nameloog"=>"Der Benutzername zu lang, der Benutzername kann bis zu 64 Zeichen enthalten",
"nameExit"=>"Benutzername existiert bereits",
"notPermission"=>"Sie haben keine Erlaubnis zur Verfügung",
"noSip"=>"Kein SIP -Account mehr",
"passwordIncorrect"=>"Falsches Passwort",
"passwdChangeSuccess"=>"Passwort ändern",
"passwordResetSuccess"=>"Passwort Erfolg zurücksetzen",
"passwordReset2"=>"Das Passwort wurde auf '%s' zurückgesetzt.",
"payTimeOut"=>"Auszeit zahlen",
"payFailed"=>"Bezahlung fehlgeschlagen",
"processing"=>"wird bearbeitet",
"paySuccess"=>"Bezahlung erfolgreich",
"redirectedOnRPS"=>"Diese MAC -Adresse wird auf RPS umgeleitet.",
"registerFailed"=>"Register fehlgeschlagen",
"registerSuccess"=>"Erfolg registrieren",
"roomNotExit"=>"Dieser Benutzer existiert nicht!",
"RFCardExit"=>"Diese HF -Karte existiert bereits",
"registered"=>"Eingetragen",
"PrivateKeyExists"=>"Dieser private Schlüssel existiert",
"passwordCorrect"=>"Ungültiges Passwort",
"timeLessCurrent"=>"Ungültige Upgrade -Zeit",
"timeZoneChangeSuccess"=>"Zeitzone ändern erfolgreich",
"timeOut"=>"Auszeit",
"unbindMACUser"=>"Bitte ungebrund %s mit dem Benutzer zuerst entbinden",
"unKnowDT"=>"Unbekannter Gerätetyp",
"userBindUser"=>"Bitte löschen Sie Benutzer zuerst unter diesem Konto",
"userNotExit"=>"Dieser Benutzer existiert nicht",
"userMaxPLimt"=>"Fehlgeschlagen erstellen, können Sie nur %der Familienmitglieder summieren",
"unregistered"=>"Nicht registriert",
"validMAC"=>"Bitte geben Sie eine gültige MAC -Adresse ein",
"versionExit"=>"Die Version existiert bereits",
"versionNameNumberExit"=>"Der Versionsname oder die Versionsnummer existiert bereits",
"sipStatus"=>"SIP -Kontozuweisung ist fehlgeschlagen, bitte versuchen Sie es erneut",
"sentCodeLater"=>"Wir haben Ihnen einen Verifizierungscode gesendet. Bitte versuchen Sie es später erneut",
"setSuccess"=>"Erfolgserfolg",
"sendEmailSuccess"=>"E -Mail -Erfolg senden",
"SetFailed"=>"Einstellung fehlgeschlagen",
"stairPhone"=>"Treppe Telefon",
"successed"=>"Erfolgreich",
"subscription"=>"Erneuern",
"wallPhone"=>"Wandtelefon",
"emailMaxLen"=>"Die E -Mail muss weniger als 64 Zeichen betragen.",
"serverUpgradeTips"=>"Das Server -Upgrade ist abgeschlossen. Bitte aktualisieren Sie die Seite.",
"ActiveFamilyAccount"=>"Bitte aktivieren Sie zuerst das Konto des Familienmasterkontos.",
"weekly"=>"Wöchentlich",
"daily"=>"Täglich",
"never"=>"Niemals",
"calltypeEmpty"=>"Anruftyp ist erforderlich",
"addOutApt"=>"Sie können nur %s Zimmer addieren",
"call"=>"Forderung",
"unlock"=>"Freischalten",
"tryUnlockCall"=>"Rufen Sie die Entsperren fehlgeschlagen an",
"tryUnlockKey"=>"Pin -Code -Entsperren fehlgeschlagen",
"tryUnlockCard"=>"HF -Karte entsperrt fehlgeschlagen",
"tryUnlockFace"=>"Face Unlock scheiterte",
"unlockApp"=>"SmartPlus entsperren",
"unlockIndoor"=>"Innenmonitor freischalten",
"unlockNFC"=>"NFC -Entsperren",
"unlockBluetooth"=>"Bluetooth entsperren",
"unlockCard"=>"HF -Karte entsperren",
"unlockPrivateKey"=>"Pin -Code entsperren",
"unlockTempKey"=>"TEMP -Schlüssel entsperren",
"alarmDoorUnlock"=>"Tür entsperren",
"alarmInfrared"=>"Infrarot",
"alarmSmoke"=>"Rauch",
"alarmGas"=>"Gas",
"alarmUrgency"=>"Dringlichkeit",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Manipuliert",
"alarmGate"=>"Tor",
"alarmDoor"=>"Tür",
"alarmBedroom"=>"Schlafzimmer",
"alarmGuestRoom"=>"Gästezimmer",
"alarmHall"=>"Saal",
"alarmWindow"=>"Fenster",
"alarmBalcony"=>"Balkon",
"alarmKitchen"=>"Küche",
"alarmStudy"=>"Lernen",
"alarmBathroom"=>"Badezimmer",
"alarmArea"=>"Bereich",
"RFCardExit2"=>"RF Card %S existiert bereits",
"RFCardDuplicated"=>"RF Card %S wird dupliziert",
"notMacBind"=>"Der Benutzer \\",
"accountNumLet"=>"Konto muss aus Zahlen und Buchstaben bestehen",
"networkUnavailable"=>"Netzwerk nicht verfügbar.",
"notForModel"=>"Nicht für dieses Modell.",
"upgradeDevVersion"=>"Bitte upgrade zuerst auf die neueste Version.",
"unavailableService"=>"Der Service ist vorübergehend nicht verfügbar. Bitte versuchen Sie es später erneut.",
"cantDeletePin"=>"Sie können Pin %s nicht löschen",
"residentInRoom"=>"Es gibt bereits im Zimmer %s",
"noAnswer"=>"Keine Antwort",
"indoorAndApp"=>"",
"indoorMonitorOnly"=>"Nur Innenmonitor",
"appOnly"=>"Nur App",
"endThanStart"=>"Die Endzeit kann nicht früher als die Startzeit liegen.",
"endThanStartFile"=>"Ungültiger Tag oder Zeit in Zeile '%s'.",
"doorRelease"=>"Türfreigabe",
"success"=>"Erfolg",
"unlockFACE"=>"Gesichts Entsperrung",
"unlockBLE"=>"Bluetooth entsperren",
"captureSmartPlus"=>"Capture on SmartPlus",
"drmagnet"=>"DRMAGNET",
"failedUnlock"=>"Versäumt zu entsperren",
"deviceDisconnected"=>"Das Gerät wurde getrennt.",
"low"=>"Niedrig",
"motion"=>"Bewegung",
"capture"=>"Ergreifen",
"failedImport"=>"Import fehlgeschlagen",
"notValidMobile"=>"%S ist keine gültige Handynummer.",
"mobileExits"=>"Mobiltelefonnummer existiert bereits",
"mobileExits2"=>"Mobilfunkzahlen %S existiert bereits",
"mobileDuplicated"=>"Mobilfunknummer %S wird dupliziert",
"mobileNumberExist"=>"Handynummer existiert nicht.",
"codeIncorrect"=>"Ungültiger Code",
"sendCodeSuccess"=>"Überprüfungscode erfolgreich senden",
"codeCorrect"=>"Richtig",
"mobileNumberEmpty"=>"Bitte geben sie ihre Mobiltelefonnummer ein.",
"invalidUser"=>"Ungültiger Benutzer %s",
"locationExits"=>"Standortadresse existiert bereits",
"smartPlusIndoor"=>"SmartPlus- und Innenmonitore",
"phoneIndoor"=>"Telefon- und Innenmonitore",
"smartPlusIndoorBackup"=>"SmartPlus- und Indoor -Monitore mit Telefon als Backup",
"smartPlusBackup"=>"Innenmonitore mit SmartPlus als Backup",
"indoorPhoneBackup"=>"Innenmonitore mit Telefon als Backup",
"indoorSmartPlusPhone"=>"Innenmonitore mit SmartPlus als Backup, endlich das Telefon",
"endUser"=>"Endbenutzer",
"installer"=>"Installateurin",
"distributor"=>"Verteilerin",
"pm"=>"PM",
"superManage"=>"Supermanage",
"loginManagement"=>"Login -Management",
"accessControl"=>"Zugangskontrolle",
"userManagement"=>"Benutzerverwaltung",
"deviceManagement"=>"Geräteverwaltung",
"communityManagement"=>"Community Management",
"auditLogin"=>"Melden Sie sich an: Web",
"auditLogout"=>"Melden Sie sich an: Web",
"auditAddTempKey"=>"Temporärer Schlüssel hinzufügen: {0}",
"auditEditTempKey"=>"Temporäre Schlüssel bearbeiten: {0}",
"auditDeleteTempKey"=>"Temporäre Schlüssel löschen: {0}",
"auditAddRFCard"=>"RF -Karte hinzufügen: {0}",
"auditEditRFCard"=>"RF -Karte bearbeiten: {0}",
"auditDeleteRFCard"=>"RF -Karte löschen: {0}",
"auditAddDis"=>"Distributor hinzufügen: {0}",
"auditEditDis"=>"Distributor bearbeiten: {0}",
"auditDeleteDis"=>"Distributor löschen: {0}",
"auditAddInstaller"=>"Installationsprogramm hinzufügen: {0}",
"auditEditInstaller"=>"Installation bearbeiten: {0}",
"auditDeleteInstaller"=>"Installer löschen: {0}",
"auditAddPM"=>"PM hinzufügen: {0}",
"auditEditPM"=>"Bearbeiten pm: {0}",
"auditDeletePM"=>"PM löschen: {0}",
"auditAddEndUser"=>"Endbenutzer hinzufügen: {0}",
"auditEditEndUser"=>"Endbenutzer bearbeiten: {0}",
"auditDeleteEndUser"=>"Endbenutzer löschen: {0}",
"auditSetOwnerTime"=>"Setzen Sie die eigene Zeitzone {0}",
"auditSetOwnPassword"=>"Setzen Sie das eigene Passwort",
"auditAddPIN"=>"Pin hinzufügen: {0}",
"auditEditPIN"=>"",
"auditDeletePIN"=>"Pin löschen: {0}",
"auditImportFace"=>"Gesicht importieren: {0}",
"auditDeleteFace"=>"Gesicht löschen: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Setzen Sie den Anruftyp SmartPlus und Innenmonitore: {0} \\u0026 {1}",
"auditSetCallTypePhoneIndoor"=>"Ruftyp -Telefon- und Innenmonitore einstellen: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Setzen Sie den Anruftyp SmartPlus und Innenmonitore mit Telefon als Sicherung: {0} \\u0026 {1}",
"auditSetCallTypeSmartPlusBackup"=>"Setzen Sie den Ruftyp Innenmonitore mit SmartPlus als Sicherung: {0} \\u0026 {1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Setzen Sie den Ruftyp Indoor -Monitore mit Telefon als Sicherung: {0} \\u0026 {1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Setzen Sie den Ruftyp Innenmonitore mit SmartPlus als Sicherung, schließlich das Telefon: {0} \\u0026 {1}",
"auditDeleteDevice"=>"Gerät löschen: {0}",
"auditSetAPTCount"=>"Stellen Sie die Anzahl der Wohnungen fest {0}",
"auditEnableLandline"=>"Festnetzbetreuung aktivieren",
"auditDisableLandline"=>"Festnetzdienst deaktivieren",
"auditSetSubTime"=>"Setzen Sie TimeZone {0}",
"auditSetChargeModeInstall"=>"Stellen Sie das Lademodell bezahlen nach Installationsprogramm",
"auditSetChargeModeUser"=>"Legen Sie das Lademodell Pay nach Benutzer/PM fest",
"auditSetConnectTypeDefault"=>"Setzen Sie den Standardtyp Standardeinstellung",
"auditSetConnectTypeTCP"=>"Setzen Sie den Verbindungstyp TCP",
"auditSetConnectTypeUDP"=>"Setzen Sie den UDP des Verbindungsarts",
"auditSetConnectTypeTLS"=>"Setzen Sie den Verbindungstyp TLS",
"auditAddCommunity"=>"Community hinzufügen: {0}",
"auditDeleteCommunity"=>"Community löschen: {0}",
"auditImportCommunity"=>"Community importieren: {0}",
"auditSetAPTNumber"=>"Setzen Sie {0} Zimmernummer {1}",
"auditSetEmail"=>"Setzen Sie E -Mail {0}: {1}",
"auditSetMobile"=>"Setzen Sie die Telefonnummer {0}: {1}",
"auditDeviceTypeStair"=>"Geben Sie ein Multi-Mieter-Türphone ein: {0}",
"auditDeviceTypeDoor"=>"Geben Sie ein einzelnes Tenantentür ein: {0}",
"auditDeviceTypeIndoor"=>"Setzen Sie den Innenmonitor Typ: {0}",
"auditDeviceTypeGuardPhone"=>"Geben Sie Guard Phone ein: {0}",
"auditDeviceTypeAccessControl"=>"Geben Sie den Typ Access Control fest: {0}",
"auditSetNetGroup"=>"Setzen Sie die Netzwerkgruppe {0}: {1}",
"auditEditCommunity"=>"Community bearbeiten",
"deliveryMsg"=>"Sie haben %s Artikel, die Ihnen geliefert wurden, bitte überprüfen Sie rechtzeitig.",
"deliveryTitle"=>"Du hast ein neues Paket!",
"rfcardDuplicatedLines"=>"Doppelte RFID -Kartennummer in Line %s!",
"rfcardNameInvalid"=>"Ungültiger HF -Kartenname in Zeile %s!",
"rfcardExistLines"=>"HF -Karten existierten bereits in Zeilen %s.",
"importFailMacExistLines"=>"Die MAC -Adresse existiert oder gültig in Linie %s.",
"exportExcelCountNull"=>"Kein Protokoll zum Exportieren am Datum!",
"keyIsEqualRoom"=>"Der Lieferschlüssel kann nicht mit der passenden Nummer identisch sein!",
"visitor"=>"Besucherin",
"CommunityNameExist"=>"Community -Name existiert bereits",
"unlockGuardPhone"=>"Wachphotendelefon entsperren",
"auditLoginApp"=>"Melden Sie sich an: App",
"auditLogoutApp"=>"Melden Sie sich an: App",
"timeForYesterday"=>"Gestern",
"exportExcelDataBefore"=>"Zu große Daten!",
"tempkeyUsed"=>"Tempey verwendet",
"tempkeyContent"=>"%S hat den Tempey verwendet.",
"accessNameExist"=>"Zugriffsgruppenname gibt es bereits",
"addFaceFail"=>"Bitte importieren Sie ein klares Foto des Gesichts.",
"userInvalid"=>"Ungültiger Benutzer in Zeile %s.",
"groupsInvalid"=>"Ungültige Zugriffsgruppe in Zeile %s.",
"BuildAccessName"=>"Resident Building %s",
"auditCodeLogEditApt"=>"Apartment bearbeiten: {0}",
"invalidTimeInLine"=>"Ungültige Zeit in Zeile %s.",
"cancel"=>"Stornieren",
"cancelSuccess"=>"Abbrechen erfolgreich.",
"payOutstanding"=>"Bitte überprüfen Sie, ob unbezahlte Bestellungen vorhanden sind. Wenn nicht, wenden Sie sich bitte an Ihren Dienstanbieter",
"featureDeleteError"=>"Feature -Plan gebunden sein.",
"beyondFamilyMember"=>"Sie können nicht mehr Familienmitgliedskonten erstellen. Bitte wenden Sie sich an Ihren Dienstanbieter, um ihn zu erstellen.",
"indoorMonitorRequired"=>"Für jede Wohnung ist mindestens ein Indoor -Monitor erforderlich.",
"featureActivationFee"=>"Funktion (einmalige Gebühr)",
"systemProcessing"=>"Systemverarbeitung",
"featureMonthlyFee"=>"Feature (monatliche Gebühr)",
"featurePriceDifferences"=>"Funktion (Preisunterschiede)",
"updatingSuccess"=>"Aktualisieren erfolgreich!",
"featureNameBasic"=>"Basic",
"featureNamePremium"=>"Prämie",
"indoorMacNotCorrect"=>"Bitte geben Sie den richtigen Innenmonitor -Mac ein.",
"off"=>"Aus",
"enterValidAccount"=>"Bitte geben Sie ein gültiges Konto ein",
"invalidKitImportMAC"=>"Bitte überprüfen Sie, ob der Mac existiert oder gültig ist: %s",
"importLessData"=>"Bitte importieren Sie weniger als %s Daten.",
"invalidQRCode"=>"Wenn Sie fehlgeschlagen identifizieren, scannen Sie bitte einen korrekten QR -Code.",
"cannotCreateFamilyMember"=>"Sie können nicht mehr Familienmitgliedskonten erstellen.",
"importProcessing"=>"Importieren, bitte versuchen Sie es später erneut",
"departmentAccessName"=>"%s Zugangsgruppe",
"idExistsLine"=>"ID existiert bereits in Linie %s",
"enterFirstNameLine"=>"Bitte geben Sie den Vornamen in Zeile %s ein",
"enterLastNameLine"=>"Bitte geben Sie den Nachnamen in Zeile %s ein",
"departmentExist"=>"Abteilung existiert bereits",
"idExist"=>"Ich werde bereits existieren",
"layoutIdInvalid"=>"Layout ist ungültig",
"unlockAppHome"=>"Augen entsperren",
"officeNameExist"=>"Der Büroname existiert bereits",
"departmentExit"=>"Abteilung existiert bereits.",
"importOutTask"=>"Sie können jeweils nur eine Vorlage importieren.",
"idDuplicated"=>"Id %S wird dupliziert",
"aptInvalidLine"=>"Ungültige Apt in Linie %s.",
"buildInvalidLine"=>"Ungültiges Gebäude in Linie %s.",
"departmentInvalidLine"=>"Ungültige Abteilung in Linie %s.",
"idInvalidLine"=>"Ungültige ID in Zeile %s.",
"propertyManager"=>"Hausverwaltung",
"departmentBindDevice"=>"Bitte löschen Sie Geräte unter dieser Abteilung.",
"departmentBindUser"=>"Bitte löschen Sie Benutzer unter dieser Abteilung.",
"smartPlusValidLine"=>"Ungültige SmartPlus intercom -Funktion in Linie %s.",
"identityValidLine"=>"Ungültige Identität in Zeile %s.",
"eachDoorCount"=>"Ein einzelner Plan, jede Tür einmal zu öffnen",
"textUpgradeMsg1"=>"Bitte aktualisieren Sie die App -Version, um fortzufahren.",
"textUpgradeMsg2"=>"Fehler bei der Anmeldung",
"deleteCodeGetLimitTimes"=>"Ungültige Taste. Bitte versuchen Sie es 24 Stunden später erneut.",
"deleteCodeOverLimitTimes"=>"Bitte versuchen Sie es 24 Stunden später noch einmal.",
"deleteCodeError"=>"Ungültiger Schlüssel",
"textUpgradeMsg"=>"1.Optimierte die Tempkey -Funktion.; 2. Die Konto -Stornierungsfunktion wurde einige Fehler festgelegt.",
"paramsError"=>"Parameterfehler",
"pmappStatusInvalid"=>"Bitte aktivieren Sie die PM -App zuerst.",
"delivery_description"=>"Liefertemperaturschlüssel",
"webRelayIDInvalidLine"=>"Ungültige Web -Relay -ID in Zeile %s.",
"relayInvalid"=>"Ungültiges Relais in Zeile %s.",
"cancelError"=>"Abbrechen fehlgeschlagen.",
"textUpgradeMsgForComRole"=>"Aufrüstung der Community -Rolle",
"textUpgradeMsgForPerRole"=>"Personalrolle aufbauen",
"textUpgradeMsgForOffRole"=>"Bürorolle aufbauen",
"textUpgradeMsgForPMRole"=>"PM -Rolle aktualisieren",
"lockApp"=>"SmartPlus Lock",
"lock"=>"Sperren",
"versionLogMaxLen"=>"Das Versionsprotokoll kann nicht größer als %s Zeichen sein",
"autoLock"=>"Automatische Sperre",
"pinAndRFcardNotNullLines"=>"Mindestens einer der Pin- und HF -Karte in Line %S muss ausgefüllt werden!",
"pinExistLines"=>"Pin hat bereits in Linie %s existiert.",
"pinInvalidLines"=>"Ungültiger Pin in Linie %s!",
"pinDuplicatedLines"=>"Duplizierter Pin in Linie %s!",
"FaceImportLength"=>"Die Größe der Face Import -Datei kann nicht größer sein als %s",
"landlineServerNotActivate"=>"Diese Community hat keinen Festnetzdienst aktiviert.",
"importFailDisNotExist"=>"Distributor existiert nicht",
"importFailNotPermission"=>"Sie haben nicht die Erlaubnis, diese MAC -Adresse hinzuzufügen.",
"importFailTooManyAdd"=>"Import ist fehlgeschlagen, nur für Einzelverteiler.",
"importFailAdded"=>"Diese MAC -Adresse wurde bereits von einem anderen Benutzer hinzugefügt.",
"macAssignToLimit"=>"Sie können nur bis zu 10 Händler zuweisen",
"macNumToLimit"=>"Sie können jeweils nur bis zu 1000 MAC -Adressen hochladen.",
"addOutFloor"=>"Bitte geben Sie eine Nummer zwischen 1 ~ 128 ein.",
"floor"=>"Boden",
"PostalCodeInvalid"=>"Bitte geben Sie Brief oder Nummer ein.",
"onceCodeInvalid"=>"Der einmalige Code muss 4-5 Ziffern betragen.",
"permanentCodeInvalid"=>"Der permanente Code muss 6 Ziffern betragen.",
"onceCodeOutNum"=>"Sie können nur nach Code addieren.",
"permanentCodeOutNum"=>"Sie können nur 10 dauerhafte Code addieren.",
"onceCodeExist"=>"Der einmalige Code existiert bereits.",
"permanentCodeExist"=>"Der dauerhafte Code existiert bereits.",
"addOutFloorLine"=>"Ungültige Bodenzahl in Linie %s.",
"auditManuallyUnlock"=>"Manuell freischalten",
"auditManuallyLock"=>"Manuell sperren",
"automaticallyUnlock"=>"Automatisch entsperren",
"doorClose"=>"Tür in der Nähe",
"PostalCodeNotEmpty"=>"Bitte geben Sie mindestens einen Brief oder eine Nummer ein.",
"emergencyAlarm"=>"Notalarm",
"doorSensor"=>"Türsensor",
"yaleBatteryWarning"=>"Yale Batteriewarnung",
"auditCodeManuallyUnlock"=>"Manuell freischalten",
"auditCodeManuallyLock"=>"Manuell sperren",
"2weekBatteryWarning"=>"%s - Geschätzte Batteriezeit verbleibend: 2 Wochen.",
"1weekBatteryWarning"=>"%s - Geschätzte Batteriezeit verbleibend: 1 Woche.",
"replaceBatteryWarning"=>"%S - Batteriespiegel ist extrem niedrig. Bitte ersetzen Sie sofort.",
"open"=>"Offen",
"close"=>"Schließen",
"addContactFavoriteNum"=>"Zu den Favoriten fehlgeschlagen. Sie können nur bis zu 300 Lieblingswohnungen addieren.",
"addContactBlockNum"=>"Hinzufügen zur Blockliste fehlgeschlagen. Sie können nur bis zu 100 Wohnungen zur Blockliste hinzufügen.",
"voiceTitle"=>"Sprachnachricht",
"voiceContent"=>"Sie haben eine Sprachnachricht von %s",
"voiceMsgInvalid"=>"Die Sprachnachricht ist abgelaufen.",
"toggleFeaturePlan"=>"Sie können den Funktionsplan nicht ändern.",
"rtspAddresEmpty"=>"Bitte geben Sie die RTSP -Adresse ein.",
"rtspAddresInvalid"=>"Ungültige RTSP -Adresse.",
"rtspPortEmpty"=>"Bitte geben Sie Port ein.",
"rtspPortInvalid"=>"Ungültiger Port.",
"rtspPassWdEmpty"=>"Bitte Passwort eingeben.",
"rtspPassWdInvalid"=>"Passwort zu lang, das Passwort kann bis zu 63 Zeichen enthalten.",
"cameraExist"=>"Die Kamera existiert bereits.",
"errorOnRPS"=>"RPS -Server nicht anfordern",
"faceImportErrorSystem"=>"Systemfehler",
"faceImportErrorView"=>"Keine Frontansicht",
"faceImportErrorWearMask"=>"Maske erkannt",
"faceImportErrorLowResolution"=>"Die Auflösung ist zu niedrig",
"faceImportErrorWrongFormat"=>"Dateiformatfehler",
"faceImportErrorNoFace"=>"Kein Gesicht entdeckt",
"faceImportErrorFileLarge"=>"Die Datei ist zu größer",
"faceImportErrorFaceLarge"=>"Das Gesicht ist zu größer",
"faceImportErrorFaceSmall"=>"Das Gesicht ist zu klein",
"faceImportErrorMultiFaces"=>"Mehr als ein Gesicht",
"faceImportErrorWrongName"=>"Dateiname ist Fehler.",
"faceImportErrorEmptyName"=>"Der Name des Bewohners ist leer.",
"faceImportErrorNoAccountInfo"=>"Holen Sie sich einen PersonalAccount -Info -Fehler.",
"faceImportErrorAccountInactive"=>"Der Personal Account ist nicht aktiv.",
"changeHomeFeatureInvalid"=>"Operation fehlgeschlagen!",
"changeInterComFeatureInvalid"=>"Operation fehlgeschlagen!",
"offline"=>"Fehlgeschlagen: Offline",
"allFloors"=>"Alle Böden",
"uploadOversize"=>"Die Größe der Upload -Datei kann nicht größer sein als %s",
"uploadInvalidType"=>"Der hochgeladene Dateityp wird nicht unterstützt",
"uploadFailed"=>"Upload fehlgeschlagen, bitte versuchen Sie es später",
"uploadScreenSaverImgTooMuch"=>"Bildschirmschoner Bilder können nicht mehr als %s sein!",
"screenSaverImgTooLittle"=>"Bildschirmschoner Bilder können nicht weniger als %s sein!",
"screenSaverImgTooMuch"=>"Bildschirmschoner Bilder können nicht mehr als %s sein!",
"screenSaverDevicesOffline"=>"Speichern fehlgeschlagen.",
"saveFailed"=>"Speichern fehlgeschlagen.",
"importingInProgress"=>"Wenn Sie in Arbeit importieren, versuchen Sie es später erneut.",
"importBuildingInvalidLine"=>"Ungültiges Gebäude in Linie %s",
"importAptInvalidLine"=>"Ungültige APT in Linie %s",
"importAccountTypeInvalidLine"=>"Ungültiger Konto Typ in Linie %s",
"importFirstNameInvalidLine"=>"Ungültiger Vorname in Zeile %s",
"importLastNameInvalidLine"=>"Ungültiger Nachname in Zeile %s",
"importKeyInvalidLine"=>"Ungültiger Schlüssel in Zeile %s",
"importKeyExistsLine"=>"Pin existiert in Linie %s",
"importCardInvalidLine"=>"Ungültige HF -Karte in Linie %s",
"importCardExistsLine"=>"HF -Karte existiert in Linie %s",
"importAccessGroupInvalidLine"=>"Ungültige Zugriffsgruppen -ID in Zeile %s",
"importAccessGroupNoPermissionLine"=>"Keine Berechtigungszugriffsgruppen -ID in Linie %s",
"importExceededNumberLine"=>"Überschritten die Anzahl des Familienmitglieds in Linie %s",
"importNoActiveMasterLine"=>"Das Import ist in Line %s fehlgeschlagen. Bitte aktivieren Sie zuerst die Familie Matser.",
"importMasterExistsLine"=>"Familienmeister existiert bereits in Linie %s.",
"importNoCreateMasterLine"=>"Das Import ist in Line %s fehlgeschlagen. Bitte erstellen Sie zuerst die Familie Matser.",
"PrivateKeysDataExist"=>"Die privaten Key %s existieren bereits.",
"PrivateKeyDataExists"=>"Die privaten Key %s existieren bereits.",
"landLineOpenToClosedFail"=>"Speichern fehlgeschlagen.",
"limitWithIp"=>"Sie versuchen zu oft, bitte versuchen Sie es in 5 Minuten erneut. (IP: %s)",
"subDistributor"=>"Subverteiler",
"faceImportErrorNotClear"=>"Das importierte Bild ist nicht klar.",


  ];
