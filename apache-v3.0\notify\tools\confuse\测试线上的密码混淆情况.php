<?php

require_once(dirname(__FILE__) . '/../../funcs_password_confuse.php');

/*
1、把线上的Devices，PersonalDevices, userinfo 导入到本地数据库
2、新增两个字段
alter table Devices add ConfuseSip char(64) NOT NULL Default '';
alter table Devices add ConfuseRtsp char(64) NOT NULL Default '';

alter table PersonalDevices add ConfuseSip char(64) NOT NULL Default '';
alter table PersonalDevices add ConfuseRtsp char(64) NOT NULL Default '';


alter table userinfo add ConfuseSip char(64) NOT NULL Default '';

ALTER TABLE PersonalDevices modify SipPwd char(40) DEFAULT '';
ALTER TABLE PersonalDevices modify RtspPwd char(40) DEFAULT '';
ALTER TABLE Devices modify SipPwd char(40) DEFAULT '';
ALTER TABLE Devices modify RtspPwd char(40) DEFAULT '';


3、执行脚本
*/

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const TMPLOG = "/tmp/refresh_akcs_sip_rtsp.log";
function logWrite($content)
{
    file_put_contents(TMPLOG, $content, FILE_APPEND);
    file_put_contents(TMPLOG, "\n", FILE_APPEND);
}
logWrite("===start===");

$db = getDB();
$sth = $db->prepare("select ID,Mac,SipPwd,RtspPwd From Devices;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $id = $value["ID"];
    $mac = $value["Mac"];
    $sip = $value["SipPwd"];
    $rtsp = $value["RtspPwd"];
    if (strlen($sip) == 32 && strlen($rtsp) == 32) {
        continue;
    }

    $sip = PasswdEncode($sip);
    $rtsp = PasswdEncode($rtsp);
    logWrite("mac:$mac, sip:$sip, rtsp:$rtsp");

    $sth1 = $db->prepare("Update Devices SET ConfuseSip=:sip,ConfuseRtsp=:rtsp WHERE ID = :id");
    $sth1->bindParam(':id', $id, PDO::PARAM_INT);
    $sth1->bindParam(':sip', $sip, PDO::PARAM_STR);
    $sth1->bindParam(':rtsp', $rtsp, PDO::PARAM_STR);
    $sth1->execute();

    if ($row % 500 == 0) {
        sleep(1);
    }
}


$sth = $db->prepare("select ID,Mac,SipPwd,RtspPwd From PersonalDevices;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $id = $value["ID"];
    $mac = $value["Mac"];
    $sip = $value["SipPwd"];
    $rtsp = $value["RtspPwd"];
    if (strlen($sip) == 32 && strlen($rtsp) == 32) {
        continue;
    }

    logWrite("permac:$mac, sip:$sip, rtsp:$rtsp");

    $sip = PasswdEncode($sip);
    $rtsp = PasswdEncode($rtsp);

    $sth1 = $db->prepare("Update PersonalDevices SET ConfuseSip=:sip,ConfuseRtsp=:rtsp WHERE ID = :id");
    $sth1->bindParam(':id', $id, PDO::PARAM_INT);
    $sth1->bindParam(':sip', $sip, PDO::PARAM_STR);
    $sth1->bindParam(':rtsp', $rtsp, PDO::PARAM_STR);
    $sth1->execute();

    if ($row % 500 == 0) {
        sleep(1);
    }
}


$sth = $db->prepare("select username,password From userinfo;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $username = $value["username"];
    $sip = $value["password"];
    if (strlen($sip) == 32) {
        continue;
    }

    logWrite("permac:$username, sip:$sip");

    $sip = PasswdEncode($sip);

    $sth1 = $db->prepare("Update userinfo SET ConfuseSip=:sip WHERE username = :username");
    $sth1->bindParam(':username', $username, PDO::PARAM_STR);
    $sth1->bindParam(':sip', $sip, PDO::PARAM_STR);
    $sth1->execute();

    if ($row % 500 == 0) {
        sleep(1);
    }
}


$sth = $db->prepare("select ID,Mac,SipPwd,RtspPwd,ConfuseSip,ConfuseRtsp From Devices;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $id = $value["ID"];
    $mac = $value["Mac"];
    $sip = $value["SipPwd"];
    $rtsp = $value["RtspPwd"];

    $ConfuseSip = $value["ConfuseSip"];
    $ConfuseRtsp = $value["ConfuseRtsp"];
    if (strlen($sip) == 32 && strlen($rtsp) == 32) {
        continue;
    }

    $sip1 = PasswdDecode($ConfuseSip);
    $rtsp1 = PasswdDecode($ConfuseRtsp);
    if ($sip1 != $sip || $rtsp != $rtsp1) {
        echo "error $id\n";
        exit(1);
    }

    if ($row % 500 == 0) {
        sleep(1);
    }
}

$sth = $db->prepare("select ID,Mac,SipPwd,RtspPwd,ConfuseSip,ConfuseRtsp From PersonalDevices;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $id = $value["ID"];
    $mac = $value["Mac"];
    $sip = $value["SipPwd"];
    $rtsp = $value["RtspPwd"];

    $ConfuseSip = $value["ConfuseSip"];
    $ConfuseRtsp = $value["ConfuseRtsp"];
    if (strlen($sip) == 32 && strlen($rtsp) == 32) {
        continue;
    }

    $sip1 = PasswdDecode($ConfuseSip);
    $rtsp1 = PasswdDecode($ConfuseRtsp);
    if ($sip1 != $sip || $rtsp != $rtsp1) {
        echo "error $id\n";
        exit(1);
    }

    if ($row % 500 == 0) {
        sleep(1);
    }
}

$sth = $db->prepare("select password, ConfuseSip From userinfo;");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);
foreach ($list as $row => $value) {
    $sip = $value["password"];
    $ConfuseSip = $value["ConfuseSip"];
    if (strlen($sip) == 32) {
        continue;
    }

    $sip1 = PasswdDecode($ConfuseSip);
    if ($sip1 != $sip) {
        echo "error $id\n";
        exit(1);
    }

    if ($row % 500 == 0) {
        sleep(1);
    }
}
