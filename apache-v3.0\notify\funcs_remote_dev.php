<?php

require_once(dirname(__FILE__) . '/../config/dynamic_config.php');
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/funcs_common.php');
require_once(dirname(__FILE__) . '/utility.php');

const REMOTECONFIG_ERR_CODE_REQ_SUCCESS = '0';
const REMOTECONFIG_ERR_CODE_REQ_FAILED = '1000200001';
const REMOTECONFIG_ERR_CODE_NO_VALID_PORT = '1000200002';
const REMOTECONFIG_ERR_CODE_DEV_OFFLINE = '1000200003';
const REMOTECONFIG_ERR_CODE_DEV_VERSION_NOT_SUPPORT = '1000200004';
const REMOTECONFIG_ERR_CODE_CONNECTING = "1000200005"; //连接中 未登录

function isSupportRemoteAccessByDeviceVersion($dev_firmware, $dev_ver)
{
    if (strncmp($dev_firmware, "29.", strlen("29.")) == 0) {
        if ($dev_ver < 5200) {
            return false;
        } else {
            return true;
        }
    } elseif (strncmp($dev_firmware, "227.", strlen("227.")) == 0) {
        //日本客户要求的，中途设备先出版本加入的，会出现部分设备支持部分不支持
        if ($dev_ver < 5400) {
            return false;
        } else {
            return true;
        }
    } elseif (strncmp($dev_firmware, "28.", strlen("28.")) == 0) {
        //日本客户要求的，中途设备先出版本加入的，会出现部分设备支持部分不支持
        if ($dev_ver < 5400) {
            return false;
        } else {
            return true;
        }
    } elseif (strncmp($dev_firmware, "915.", strlen("915.")) == 0) {
        //915在5.4已经支持
        if ($dev_ver < 5400) {
            return false;
        } else {
            return true;
        }
    } elseif (strncmp($dev_firmware, "916.", strlen("916.")) == 0) {
        //916在5.4已经支持
        if ($dev_ver < 5400) {
            return false;
        } else {
            return true;
        }
    } else {
        if ($dev_ver < 6000) {
            return false;
        } else {
            return true;
        }
    }
}

function isSupportRemoteAccessByFirmware($dev_firmware)
{
    foreach (SUPPORT_REMOTE_ACCESS_DEVICE_FIRMWARES as $key => $value) {
        if (strncmp($dev_firmware, $value, strlen($value)) == 0) {
            return true;
        }
    }

    return false;
}

function isSupportDistributeRemoteConfigAddr($function)
{
    return switchHandle($function, FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT);
}

function requestRegisterRemoteConfigAddr($remoteDevConfigUrl, $dev)
{ 
    $url = sprintf("%s/register?mac=%s&devip=%s&serverip=%s", $remoteDevConfigUrl, $dev['MAC'], $dev['outerIP'], WEB_DOMAIN);

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    $curl_data = curl_exec($curl);
    curl_close($curl);
    
    return $curl_data;
}

function requestRemoteConnect($remoteDevConfigUrl, $traceid)
{ 
    $url = sprintf("%s/check_connect?traceid=%s", $remoteDevConfigUrl, $traceid);

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    $curl_data = curl_exec($curl);
    curl_close($curl);
    
    return $curl_data;
}

function registerRemoteConfigAddr($dev, &$dev_remote_domain)
{
    $curl_data = array();
    $primaryUrl = REMOTE_CONFIG_PRIMARY_API_URL;
    $backupUrl = REMOTE_CONFIG_SECONDARY_API_URL;
    $dev_remote_domain = REMOTE_CONIFG_PRIMARY_DOMAIN;
    $cLog = \share\util\getLog();

    if (isSupportDistributeRemoteConfigAddr($dev['Function'])) {
        $cLog->TRACE("[createRemoteDevConfig] support distribute remote config addr. mac=" . $dev['MAC']);
    }

    // 检查设备是否支持下发到备用地址
    if (strlen($backupUrl) > strlen("https://x:1301") && isSupportDistributeRemoteConfigAddr($dev['Function'])) {
        // 尝试使用备用地址
        $curl_data = requestRegisterRemoteConfigAddr($backupUrl, $dev);

        // 如果注册失败（result != 0），则回退到第一台地址
        $curl_data_decode = json_decode($curl_data, true);
        if ($curl_data == false || $curl_data_decode['datas']['result'] != 0) {
            // 回退到第一台地址
            $curl_data = requestRegisterRemoteConfigAddr($primaryUrl, $dev);
        }
        else
        {
            $dev_remote_domain = REMOTE_CONIFG_SECONDARY_DOMAIN;
        }
    } else {
        // 直接下发到第一台地址
        $curl_data = requestRegisterRemoteConfigAddr($primaryUrl, $dev);
    }

    
    $cLog->TRACE("[createRemoteDevConfig] curl_data =" . json_encode($curl_data));
    return $curl_data;
}

function getRemoteConfigDevInfo($mac)
{
    $db = \share\util\getDatabase();
    return $db->querySList("select outerIP,DclientVer,Status,Firmware,MAC,Function from Devices where Mac=:MAC union all select outerIP,DclientVer,Status,Firmware,MAC,Function from PersonalDevices where Mac=:MAC;", [":MAC" => $mac])[0];
}


//todo: 一开始协议没有定好 导致代码写的必然冗余
function checkRemoteDevConnectRequest($traceid, $domain)
{
    $curl_data = array();
    $cLog = \share\util\getLog();

    $curl_data = requestRemoteConnect($domain, $traceid);

    $ret_status = 0;
    $curl_data_decode = json_decode($curl_data, true);

    $cLog->TRACE("[checkRemoteDevConnectRequest] , traceid=$traceid requestRemoteConnect ret=$curl_data");

    if ($curl_data == false || $curl_data_decode['datas']['result'] != 0) {
        $ret_status = REMOTECONFIG_ERR_CODE_REQ_FAILED;
    }
    else 
    {
        $err_code = $curl_data_decode['datas']['err_code'];
        if ($err_code != 0)
        {
            $ret_status = $err_code;
        }
        else
        {
            $status = $curl_data_decode['datas']['status'];
            if ($status == 1)
            {
                $ret_status = 0;
            }
            else
            {
                $ret_status = REMOTECONFIG_ERR_CODE_CONNECTING;
            }
        }
    }
    
    $cLog->TRACE("[checkRemoteDevConnectRequest] , traceid=$traceid status=$ret_status (status=0 is connected)");
    return $ret_status;
}

function getRemoteApiKey($domain)
{
    $apiRemoteDevUrlMapping = [
        'primary' => REMOTE_CONIFG_PRIMARY_DOMAIN,
        'secondary' => REMOTE_CONIFG_SECONDARY_DOMAIN,
    ];
    return array_search($domain, $apiRemoteDevUrlMapping);
}

function getRemoteMappingTraceID($traceid, $domain)
{    
    return $traceid . "###" . getRemoteApiKey($domain);
}

function parseRemoteMappingTraceID($str, &$traceid, &$domain)
{
    $apiInnerRemoteDevUrlMapping = [
        'primary' => REMOTE_CONFIG_PRIMARY_API_URL,
        'secondary' => REMOTE_CONFIG_SECONDARY_API_URL,
    ];
    // 使用 explode 将字符串分割为数组
    $parts = explode("###", $str);

    if (count($parts) === 2) {
        $traceid = $parts[0];
        $parsedApiKey = $parts[1];
        $domain = $apiInnerRemoteDevUrlMapping[$parsedApiKey];

        return true; 
    }
    return false;
}
