<?php
/*
 * @Description: 事件容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-31 14:03:59
 * @LastEditors  : kxl
 */
namespace executor;
include_once __DIR__."/basic.php";
include_once __DIR__."/../interfaces/executor/main.php";

class CEventHandler extends \executor\Basic implements \interfaces\executor\main\IExecutor {
    public $event;//array["name"=>"event","action"=>enum(emit,on)]
    private $name;
    private $method;
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    public function parse () {
        $this->name = $this->event["name"];
        $this->method = $this->event["action"];
    }
    public function exec () {
        include_once __DIR__."/../event/".$this->name.".php";
        $method = $this->method;
        $event = $this->name->getInstance();
        $event->$method();
    }
}
