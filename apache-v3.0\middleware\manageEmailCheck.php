<?php
/*
 * @Description: 终端用户邮件重复性验证
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:51:45
 * @LastEditors  : kxl
 */

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";

include_once __DIR__."/../database/main.php";
class CManageEmailCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $cMessage;
        global $cLog;
        $params = ["ID"=>"","Email"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $email = $params["Email"];
        $cLog->actionLog("#middle#manageEmailCheck#email=".$email);
        if($email != null || $email != "") {
            $db = \database\CDatabase::getInstance();
            if($id)
                $data = $db->querySList("select ID from Account where Email = :Email and ID != :ID",[":Email"=>$email,":ID"=>$id]);
            else
                $data = $db->querySList("select ID from Account where Email = :Email",[":Email"=>$email]);
            $cLog->actionLog("#middle#manageEmailCheck#exits=".count($data));
            if(count($data) > 0) $cMessage->echoErrorMsg(StateEmailExits);
        }
        $next();
    }
}