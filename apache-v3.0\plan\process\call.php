<?php
namespace plan\process;
const CALL_PROCESS = [
    "queryCallForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"call.queryForPM"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取Call history列表
    "getCallForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],[
            "type"=>"model",
            "model"=>"call.queryForPM"
        ],[
            "type"=>"model",
            "model"=>"call.formatPmAppCall"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];