<?php

namespace package\common\model\dormakaba\method;
trait Get
{
    /**
     * @description: 获取DormakabaLock信息
     * @author: shoubin.chen 2024/5/16 11:14:10 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/16 11:14:10  V6.8.0
     */
    public function getDormakabaLock()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->dormakabaLock->selectByArray($array, $fields);
    }

    public function getCommunityDormakabaLockInfo()
    {
        $params = ['LockUUID:uuid', 'Virtual'];
        list($lockUuid, $virtual) = $this->getParams($params);
        if (empty($virtual)) {
            $virtual = false;
        }

        $dormakabaLock = $this->dao->dormakabaLock->selectByArray([['UUID',$lockUuid]])[0];
        $this->loadUtil('communityUnit', true);
        $unitInfo = $this->utils->_common->communityUnit->getUnitInfoByKey('UUID', $dormakabaLock['CommunityUnitUUID']);
        $dormakabaInfo = [
            'Building' => $unitInfo['UnitName'],
            'Name' => $dormakabaLock['Name'],
            'UUID' => $dormakabaLock['UUID'],
            'Lock' => $dormakabaLock['ThirdUUID'],
            'Brand' => THIRD_PARTY_LOCK['Dormakaba']
        ];
        $this->loadUtil('thirdLock', true);
        if ($virtual) {
            $uuid = $this->utils->_common->thirdLock->getVirtualLockUUID($dormakabaLock['UUID'], THIRD_PARTY_LOCK['Dormakaba']);
            $dormakabaInfo['UUID'] = $uuid;
        }

        return $dormakabaInfo;
    }


    /**
     * @description: 获取指定设备UUID和MAC的设备的被使用的Relay
     * @param: DeviceUUID 设备UUID
     * @param: MAC 设备MAC
     * @return array 已被使用的Relay
     * @author: shoubin.chen 2024/5/16 14:06:09 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/16 14:06:09  V6.8.0
     */
    public function getRelayInUse()
    {
        $params = ['DeviceUUID', 'MAC'];
        list($deviceUUID, $mac) = $this->getParams($params);

        $this->loadUtil('smartLock');
        $bindLockRelayList = $this->utils->smartLock->getThirdPartyLockDevice([['MAC', $mac]], "Relay");
        $relayInUse = [];

        $this->loadUtil('device');
        foreach (array_column($bindLockRelayList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }

        $this->loadUtil('dormakaba', true);
        $this->loadUtil('salto', true);
        $this->loadUtil('iTec',true);
        $this->loadUtil('ttLock', true);
        $this->loadUtil('smartLockSL20', true);
        $bindDormakabaList = $this->callSelfFunc('getDormakabaLock', [[['DeviceUUID', $deviceUUID]], "Relay"]);
        $bindSaltoList = $this->utils->_common->salto->getSaltoLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindITecList = $this->utils->_common->iTec->getITecLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindTtLockList = $this->utils->_common->ttLock->getTtLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindSL20List = $this->utils->_common->smartLockSL20->getSL20Lock([['DeviceUUID', $deviceUUID]], "Relay");
        foreach (array_column($bindDormakabaList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSaltoList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindITecList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindTtLockList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSL20List, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        return array_map('intval', $relayInUse);
    }


    /**
     * @description: 获取DormakabaAccount的信息
     * @author: shoubin.chen 2024/5/22 18:08:33 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/22 18:08:33  V6.8.0
     */
    public function getDormakabaAccount()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->dormakabaAccount->selectByArray($array, $fields);
    }

    /**
     * @description: 获取终端用户绑定的Dormakaba锁个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: shoubin.chen 2024/5/30 10:19:45 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/30 10:19:45  V6.8.0
     */
    public function getDormakabaLockNumByEndUser()
    {
        //根据权限组展示数量
        $params = ['PersonalAccountUUID:uuid','ProjectID'];
        list($personalAccountUUID,$projectId) = $this->getParams($params);

        $this->loadUtil('account');
        $mainUser = $this->utils->account->personalAccountSelectByKey('UUID', $personalAccountUUID)[0];
        $lockNum = 0;
        $mainUserUUID = $mainUser['UUID'];

        if ($mainUser['Role'] == PERENDMROLE) {
            $lockNum = $this->callSelfFunc('getDormakabaLockNumBySingleMainUser', [$mainUserUUID]);
        } else if ($mainUser['Role'] == COMENDMROLE) {
            $lockNum = $this->callSelfFunc('getDormakabaLockNumByComMainUser', [$mainUserUUID,$projectId]);
        }

        return $lockNum;
    }

    /**
     * @description: 获取社区主账户绑定的Dormakaba锁个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: shoubin.chen 2024/5/30 10:19:45 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/30 10:19:45  V6.8.0
     */
    public function getDormakabaLockNumByComMainUser()
    {
        $params = ['PersonalAccountUUID:uuid','ProjectID'];
        list($personalAccountUUID,$projectId) = $this->getParams($params);

        $this->loadUtil('account');
        $this->loadUtil('smartLock',true);
        $mainUser = $this->utils->account->personalAccountSelectByKey('UUID', $personalAccountUUID)[0];
        $projectUUID = $mainUser['ParentUUID'];
        // apt下的
        $aptLockNum = (int)$this->dao->dormakabaLock->selectByArray([['PersonalAccountUUID', $personalAccountUUID], ['Grade', DORMAKABALOCK_GRADE['family']]], 'count(*)')[0]['count(*)'];
        // 社区公共
        
        $comLockDatas = $this->dao->dormakabaLock->selectByArray([['AccountUUID', $projectUUID], ['Grade', DORMAKABALOCK_GRADE['community']]]);
        $comLockLinkDoorDatas = [];
        $comLockLinkWithoutDoorDatas = [];
        foreach ($comLockDatas as $comLockData) {
            if (!empty($comLockData['DeviceUUID'])&&!empty($comLockData['Relay'])) {
                $comLockLinkDoorDatas[] = $comLockData;
            }else{
                $comLockLinkWithoutDoorDatas[] = $comLockData;
            }
        }
        $comLockNum = count($comLockLinkDoorDatas);
        foreach ($comLockLinkWithoutDoorDatas as $comLockData) {
            $isAuth = $this->utils->_common->smartLock->checkThirdLockAuth($comLockData['UUID'],THIRD_PARTY_LOCK['Dormakaba'],$mainUser['Account'],$projectId);
            if ($isAuth){
                $comLockNum++;
            }
        }
        
        //所属楼栋公共
        
        $unitID = $mainUser['UnitID'];
        $this->loadUtil('communityUnit');
        $unitUUID = $this->utils->communityUnit->getUnitInfoByKey('ID', $unitID)['UUID'];
        $unitLockDatas = $this->dao->dormakabaLock->selectByArray([['CommunityUnitUUID', $unitUUID], ['Grade', DORMAKABALOCK_GRADE['unit']]]);
        $unitLockLinkDoorDatas = [];
        $unitLockLinkWithoutDoorDatas = [];
        foreach ($unitLockDatas as $unitLockData) {
            if (!empty($unitLockData['DeviceUUID'])&&!empty($unitLockData['Relay'])) {
                $unitLockLinkDoorDatas[] = $unitLockData;
            }else{
                $unitLockLinkWithoutDoorDatas[] = $unitLockData;
            }
        }
        $unitLockNum = count($unitLockLinkDoorDatas);
        foreach ($unitLockLinkWithoutDoorDatas as $unitLockData){
            $isAuth = $this->utils->_common->smartLock->checkThirdLockAuth($unitLockData['UUID'],THIRD_PARTY_LOCK['Dormakaba'],$mainUser['Account'],$projectId);
            if ($isAuth){
                $unitLockNum++;
            }
        }
        return $aptLockNum + $comLockNum + $unitLockNum;
    }

    /**
     * @description: 获取单住户主账户绑定的Dormakaba锁个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: shoubin.chen 2024/5/30 10:19:45 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/30 10:19:45  V6.8.0
     */
    public function getDormakabaLockNumBySingleMainUser()
    {
        $params = ['PersonalAccountUUID:uuid'];
        list($personalAccountUUID) = $this->getParams($params);

        // apt下的
        $aptLockNum = (int)$this->dao->dormakabaLock->selectByArray([['PersonalAccountUUID', $personalAccountUUID]], 'count(*)')[0]['count(*)'];

        return $aptLockNum;
    }


    /**
     * @description: 获取社区dormakaba锁列表，未被link的
     * @param:
     * @return mixed
     * @author: kzr 2025/3/4 11:36:48 V7.1.0
     * @lastEditor: kzr 2025/3/4 11:36:48  V7.1.0
     */

     public function getCommunityDormakabaLockList(){
        $params = [PROXY_ROLE_CHECK['projectUUID']];
        list($projectUUID) = $this->getParams($params);
        $array = [
            ['Grade', [1, 2]],
            ['ProjectType', 2],
            ['AccountUUID', $projectUUID]
        ];

        $dormakabaLockList = $this->dao->dormakabaLock->selectByArray($array);

        $formatLockList =  $this->share->util->arrayColumnAsKey($dormakabaLockList,'UUID');
        $communityUnitUUIDList = array_column($dormakabaLockList, 'CommunityUnitUUID');

        $this->loadUtil('communityUnit', true);
        $unitNameList = $this->utils->_common->communityUnit->getUnitInfoByArr([['UUID', $communityUnitUUIDList]], 'UnitName,UUID');
        $unitNameList = $this->share->util->arrayColumnAsKey($unitNameList, 'UUID');

        $dormakabaListRes=[];
        foreach($dormakabaLockList as $dormakaba){
            if (!empty($dormakaba['DeviceUUID'])) {
                continue;
            }
            $communityUnitUUID = $formatLockList[$dormakaba['UUID']]['CommunityUnitUUID'];
            $item['UUID'] = $dormakaba['UUID'];
            $item['Name'] = $dormakaba['Name'];
            $item['Building'] = $unitNameList[$communityUnitUUID]['UnitName'];
            $item['LockID'] = $dormakaba['LockId'];
            $item['Brand'] = THIRD_PARTY_LOCK['Dormakaba'];
            $dormakabaListRes[] = $item;
        }

        return $dormakabaListRes;
    }
}