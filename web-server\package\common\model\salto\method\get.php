<?php

namespace package\common\model\salto\method;
trait Get
{
    /**
     * @description: 获取saltoLock信息
     * @author: kzr 2024/10/26 11:14:10 V7.0.1
     * @lastEditor: kzr 2024/10/26 11:14:10 V7.0.1
     */
    public function getSaltoLock()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->saltoLock->selectByArray($array, $fields);
    }

        /**
     * @description: 获取saltoIQ信息
     * @author: kzr 2024/10/26 11:14:10 V7.0.1
     * @lastEditor: kzr 2024/10/26 11:14:10 V7.0.1
     */
    public function getSaltoIQ()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->saltoIQ->selectByArray($array, $fields);
    }


    /**
     * @description: 获取指定设备UUID和MAC的设备的被使用的Relay
     * @param: DeviceUUID 设备UUID
     * @param: MAC 设备MAC
     * @return array 已被使用的Relay
     * @author: kzr 2024/10/26 11:14:10 V7.0.1
     * @lastEditor: kzr 2024/10/26 11:14:10 V7.0.1
     */
    public function getRelayInUse()
    {
        $params = ['DeviceUUID', 'MAC'];
        list($deviceUUID, $mac) = $this->getParams($params);

        $this->loadUtil('smartLock');
        $bindLockRelayList = $this->utils->smartLock->getThirdPartyLockDevice([['MAC', $mac]], "Relay");
        $relayInUse = [];
        $this->loadUtil('device');
        foreach (array_column($bindLockRelayList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }

        $this->loadUtil('salto', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('iTec',true);
        $this->loadUtil('ttLock', true);
        $this->loadUtil('smartLockSL20', true);
        $bindSaltoList = $this->callSelfFunc('getSaltoLock', [[['DeviceUUID', $deviceUUID]], "Relay"]);
        $bindDormakabaList = $this->utils->_common->dormakaba->getDormakabaLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindITecList = $this->utils->_common->iTec->getITecLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindTtLockList = $this->utils->_common->ttLock->getTtLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindSL20List = $this->utils->_common->smartLockSL20->getSL20Lock([['DeviceUUID', $deviceUUID]], "Relay");
        foreach (array_column($bindDormakabaList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSaltoList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindITecList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindTtLockList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSL20List, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        return array_map('intval', $relayInUse);
    }


    /**
     * @description: 获取SaltoSaltoAccount的信息
     * @author: kzr 2024/10/26 11:14:10 V7.0.1
     * @lastEditor: kzr 2024/10/26 11:14:10 V7.0.1
     */
    public function getSaltoAccount()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->saltoAccount->selectByArray($array, $fields);
    }

    /**
     * @description: 获取终端用户绑定的salto锁个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: kzr 2024/10/18 10:19:45 V7.0.1
     * @lastEditor: kzr 2024/10/18 10:19:45  V7.0.1
     */
    public function getSaltoLockNumByEndUser()
    {
        $params = ['PersonalAccountUUID:uuid'];
        list($personalAccountUUID) = $this->getParams($params);

        $this->loadUtil('account');
        $mainUser = $this->utils->account->personalAccountSelectByKey('UUID', $personalAccountUUID)[0];
        $lockNum = 0;
        $mainUserUUID = $mainUser['UUID'];

        if ($mainUser['Role'] == PERENDMROLE) {
            $bindArray=[
                'PersonalAccountUUID'=>$mainUserUUID
            ];
            $lockNum = $this->dao->saltoLock->selectLockCountByEndUser($bindArray);
        } else if ($mainUser['Role'] == COMENDMROLE) {
            //所属社区
            $projectUUID = $mainUser['ParentUUID'];
            //所属楼栋
            $unitID = $mainUser['UnitID'];
            $this->loadUtil('communityUnit');
            $unitUUID = $this->utils->communityUnit->getUnitInfoByKey('ID', $unitID)['UUID'];
            $bindArray=[
                'AccountUUID'=>$projectUUID,
                'CommunityUnitUUID'=>$unitUUID,
                'PersonalAccountUUID'=>$mainUserUUID
            ];
            $lockNum = $this->dao->saltoLock->selectLockCountByComMainUser($bindArray);
        }

        return $lockNum;
    }
}