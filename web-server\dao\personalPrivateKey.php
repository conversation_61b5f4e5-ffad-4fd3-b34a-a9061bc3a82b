<?php

namespace dao;
use framework\BasicDao;

class PersonalPrivateKey extends BasicDao
{
    //当前表名
    public $table = 'PersonalPrivateKey';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    
    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["Grade", 11], ["ManageGroup", 0, "!="]]
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }
    
    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/08 16:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/08 16:21 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 获取旧社区用户个人pin列表（关联PersonalPrivateKey、PersonalAccount、PersonalAccount、CommunityRoom、CommunityUnit）
     * @param {array} $searchArrU CommunityUnit搜索条件
     * @param {array} $searchArrR CommunityRoom搜索条件
     * @param {array} $searchArrB PersonalAccount搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getUserPinList($searchArrU, $searchArrR, $searchArrP, $searchArrB, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrU)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityUnit->makeSearchCondition($searchArrU, true, 'U');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrR)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityRoom->makeSearchCondition($searchArrR, true, 'R');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrP)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrP, true, 'P');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrB)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->personalAccount->makeSearchCondition($searchArrB, true, 'B');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        //关联查询
        $sql = "select $fields from PersonalPrivateKey P join PersonalAccount A on P.AccountID = A.ID  
            join PersonalAccount B on B.Account = P.Node join CommunityRoom R on B.RoomID = R.ID   
            join CommunityUnit U on B.UnitID = U.ID ";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

}