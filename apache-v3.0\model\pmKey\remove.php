<?php
namespace model\pmKey\remove;

const PINTYPE_IDX = 0;
const PINID_IDX = 1;
const RESIDENT = '0';
const DELIVERY = '1';
const STAFF = '2';
const OFFICEPERSONNEL = '30';
const OFFICESTAFF = '31';

namespace model\pmKey;

trait remove
{
    public function delete($deleteAll = false)
    {
        global $cMessage;
        $params = $deleteAll?[
            "userAliasId"=>"",
        ]:[
            "ID"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $macs = [];
        $accounts = [];

        if (!$deleteAll) {
            $id = $params["ID"];
            $id = explode(";", $id);

            $checkRes = $this->db->querySList("select count(*) from ".($this->owernType == 2?$this->personalTables[$this->type]:$this->tables[$this->type])." where MngAccountID = :MngAccountID and ID in (".implode(",", $id).")", [":MngAccountID"=>$userId])[0]["count(*)"];
            if ($checkRes != count($id)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
        } else {
            // 一键删除rfcard获取ID号
            if ($this->owernType == 2) {
                $resData = $this->db->querySList("select ID from ".$this->personalTables[$this->type]." where MngAccountID = :MngAccountID", [":MngAccountID"=>$userId]);
            } else {
                $resData = $this->db->querySList("select ID from ".$this->tables[$this->type]." where MngAccountID = :MngAccountID and OwnerType = :OwnerType", [":MngAccountID"=>$userId, ":OwnerType"=>$this->owernType]);
            }
            $id = [];
            foreach ($resData as $val) {
                array_push($id, $val['ID']);
            }
        }

        $dis = \util\role\getDisForInstaller($userId);
        foreach ($id as $val) {
            if ($this->owernType == 2) {
                //czx 数据变化通知
                $data = $this->db->queryAllList($this->personalTables[$this->type], ["equation"=>[":ID"=>$val]]);
                foreach ($data as $accountId) {
                    // V5.4
                    // V6.5.1 避免出现主账号已删除但Key未删的情况，允许删除
                    $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$accountId["AccountID"]]])[0];
                    if ($accountId["Special"] == 1) {
                        // 说明账号已经不存在,从账号为账号删除，主账号为房间未住人/删除
                        if (empty($userData['Account']) || $userData['Special'] === '1') {
                            break;
                        }
                        if ($dis['IsEncryptPin'] == ENCRYPTION) {
                            $cMessage->echoErrorMsg(StateCantDeletePin, [], ['']);
                        } else {
                            $cMessage->echoErrorMsg(StateCantDeletePin, [], [$accountId["Code"]]);
                        }
                    }
                    if (in_array($userData["Role"], SUBROLE)) {
                        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userData["ParentID"]]])[0];
                    }
                    array_push($accounts, $userData["Account"]);
                    // communityUpdateNodeNotify($userData["Account"],$userData["ParentID"],$userData["UnitID"],1);
                }

                $this->db->delete2ListWID($this->personalTables[$this->type], $val);
                $this->db->delete2ListWKey($this->personalListTables[$this->type], "KeyID", $val);
            } else {
                //czx 数据变化通知
                $data = $this->db->queryAllList($this->listTables[$this->type], ["equation"=>[":KeyID"=>$val]]);
                foreach ($data as $mac) {
                    // communityUpdatePubKeyNotify($mac['MAC']);
                    array_push($macs, $mac['MAC']);
                }

                $data = $this->db->queryAllList($this->tables[$this->type], ["equation"=>[":ID"=>$val]]);
                $this->db->delete2ListWID($this->tables[$this->type], $val);
                $this->db->delete2ListWKey($this->listTables[$this->type], "KeyID", $val);
            }

            $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
            $this->auditLog->setLog(
                $this->type == 0 ? AuditCodeDeletePin : AuditCodeDeleteRf,
                $this->env,
                [$data[0]["Code"]],
                $account
            );
        }
        $macs = implode(";", $macs);
        \util\computed\setGAppData(["macs"=>$macs,"Account"=>$accounts,"DeleteAllRFCard"=>$deleteAll]);
    }

    public function afterDelete()
    {
        $params = [
            "macs"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $macs = $params["macs"];
        // foreach($macs as $mac) {
        //     communityUpdatePubKeyNotify($mac);
        // }
    }

    public function delAPKey()
    {
        $this->owernType = 1;
        $this->type = 0;
        $this->delete();
    }

    public function delARKey()
    {
        $this->owernType = 1;
        $this->type = 1;
        $this->delete();
    }

    public function delSPKey()
    {
        $this->owernType = 0;
        $this->type = 0;
        $this->delete();
    }

    public function delSRKey()
    {
        $this->owernType = 0;
        $this->type = 1;
        $this->delete();
    }

    public function delAllSRKey()
    {
        $this->owernType = 0;
        $this->type = 1;
        $this->delete(true);
    }

    public function delRPKey()
    {
        $this->owernType = 2;
        $this->type = 0;
        $this->delete();
    }

    public function delRRKey()
    {
        $this->owernType = 2;
        $this->type = 1;
        $this->delete();
    }

    public function delAllRRKey()
    {
        $this->owernType = 2;
        $this->type = 1;
        $this->delete(true);
    }

    // 新pm删除pin或card
    public function delAllPinOrCard()
    {
        global $cMessage;
        $params=[
            "userAliasId"=>"",
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = explode(';', $params["ID"]);
        $accounts= [];  //resident id号对应account
        $perIds = [];   //resident对应id号
        $deliveryIds = [];   //delivery对应id号
        $staffIds = []; //staff对应id号
        $deleteCode = [];   //审计日志删除对应号
        foreach ($id as $val) {
            $idType = explode('_', $val)[remove\PINTYPE_IDX];
            if ($idType == remove\RESIDENT || $idType == remove\OFFICEPERSONNEL || $idType == remove\OFFICESTAFF) {
                array_push($perIds, explode('_', $val)[remove\PINID_IDX]);
            } elseif ($idType == remove\DELIVERY) {
                array_push($deliveryIds, explode('_', $val)[remove\PINID_IDX]);
            } elseif ($idType == remove\STAFF) {
                array_push($staffIds, explode('_', $val)[remove\PINID_IDX]);
            }
        }
        if (count($perIds) != 0) {
            $checkRes = $this->db->querySList("select count(*) from ".$this->commPerTables[$this->type]." where CommunityID = :MngAccountID and ID in (".implode(",", $perIds).")", [":MngAccountID"=>$userId])[0]["count(*)"];
            if ($checkRes != count($perIds)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            foreach ($perIds as $val) {
                $data = $this->db->querySList("select Account, Code from ".$this->commPerTables[$this->type]." where ID = :ID", [":ID" => $val])[0];
                array_push($accounts, $data['Account']);
                array_push($deleteCode, $data['Code']);
                $this->db->delete2ListWID($this->commPerTables[$this->type], $val);
            }
        }
        if (count($deliveryIds) != 0) {
            $checkRes = $this->db->querySList("select count(*) from Delivery where CommunityID = :MngAccountID and ID in (".implode(",", $deliveryIds).")", [":MngAccountID"=>$userId])[0]["count(*)"];
            if ($checkRes != count($deliveryIds)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $code = $this->type == 0 ? ":PinCode" : ":CardCode";
            foreach ($deliveryIds as $val) {
                $data = $this->db->querySList("select PinCode, CardCode from Delivery where ID = :ID", [":ID" => $val])[0];
                array_push($deleteCode, $this->type == 0 ? $data['PinCode'] : $data['CardCode']);
                $this->db->update2ListWID("Delivery", [":ID"=>$val, $code=>null]);
            }
        }
        if (count($staffIds) != 0) {
            $checkRes = $this->db->querySList("select count(*) from Staff where CommunityID = :MngAccountID and ID in (".implode(",", $staffIds).")", [":MngAccountID"=>$userId])[0]["count(*)"];
            if ($checkRes != count($staffIds)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            foreach ($staffIds as $val) {
                $data = $this->db->querySList("select CardCode from Staff where ID = :ID", [":ID" => $val])[0];
                array_push($deleteCode, $data['CardCode']);
                $this->db->update2ListWID("Staff", [":ID"=>$val, ":CardCode"=>null]);
            }
        }
        \util\computed\setGAppData(["communityId"=>$userId, "Account"=>$accounts, "deliveryIds"=>$deliveryIds, "staffIds"=>$staffIds, "accessGroupsId"=>[]]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        if (count($deleteCode) != 0 && $this->type===0) {
            foreach ($deleteCode as $val) {
                $this->auditLog->setLog(AuditCodeDeletePin, $this->env, [$val], $account);
            }
        }
        if (count($deleteCode) != 0 && $this->type===1) {
            foreach ($deleteCode as $val) {
                $this->auditLog->setLog(AuditCodeDeleteRf, $this->env, [$val], $account);
            }
        }
    }
    public function delAllPin()
    {
        $this->type = 0;
        $this->delAllPinOrCard();
    }

    public function delAllCard()
    {
        $this->type = 1;
        $this->delAllPinOrCard();
    }
}
