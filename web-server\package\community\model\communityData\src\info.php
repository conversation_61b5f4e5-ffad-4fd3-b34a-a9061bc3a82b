<?php

namespace package\community\model\communityData\src;


trait Info
{
    /**
     * @description:app用户获取项目信息
     * @author:lwj 2023-04-26 10:21:51 V6.6
     * @lastEditor:lwj 2023-04-26 10:21:51 V6.6
     * @param:
     * @return array
     */
    public function getProjectInfoForApp()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->loadUtil('account', true);
        $basicData = $this->utils->_common->account->getManagerInfo($projectId);
        $tempkeyValidityPeriod = intval($this->utils->self->getInfo($projectId)['info']['TempkeyValidityPeriod']);
        return ['data' => ['Location' => $basicData['Location'], 'TempkeyValidityPeriod' => $tempkeyValidityPeriod]];
    }

    
    /*
     *@description 根据ID获取社区详细信息
     *<AUTHOR> 2023-05-10 15:18:26 V6.6.0
     *@lastEditor cj 2023-05-10 15:18:26 V6.6.0
     *@param {*} ID社区ID
     *@return ['data' => ['IsMulti' => 1]] IsMulti 0:无多套房1有多套房
     */
    public function getCommunityInfo()
    {
        $params = [PROXY_ROLE['projectId'],PROXY_ROLE_CHECK['installerId']];
        list($projectId,$insId) = $this->getParams($params);
        $this->loadUtil('user');
        $data = $this->dao->communityInfo->selectByAccountID($projectId)[0];
        $this->loadUtil('account', true);

        $community = $this->utils->_common->account->getManagerInfo($projectId);
        $data['CustomizeForm'] = $community['CustomizeForm'];
        $data['UUID'] = $community['UUID'];
        $data['Location']=$community['Location'];
        $data['EnableSmartHome'] = $this->share->util->getSpecifyBitLE($data['Switch'], 5);
        $data['EnableLandline'] = $this->share->util->getSpecifyBitLE($data['Switch'], 1);
        $this->loadUtil('featurePlan');
        $item = intval($this->utils->featurePlan->getInfo($projectId)[1]);
        $data['ThirdPartyDevices'] = $this->share->util->getSpecifyBitLE($item, 7);
        $data['IsComMonitor'] = $this->share->util->getSpecifyBitLE($item, 5);

        // 是否有多套房, 变更：V6.7.1去掉该字段判断，防止社区过大导致判断慢，单独接口处理
        // $data['IsMulti'] = $this->callSelfFunc('getCommunityIsMulti',[$projectId]);

        //V6.7.1 新增返回IsAutoRenew，subins的开关状态跟随ins
        $insData = $this->utils->_common->account->getManagerInfo($insId);
        $data['IsAutoRenew'] = intval($insData['IsAutoRenew']);

        //7.1.0 增加rentManager的IsAccessFollow字段
        $this->loadUtil('rentManager');
        $rentManagerCommunityInfo=$this->utils->rentManager->getCommunityInfo($data['AccountUUID']);
        if (count($rentManagerCommunityInfo)!=0 && $rentManagerCommunityInfo[0]['IsAccessFollow']==1){
            $data['IsAccessFollow']=true;
        }else{
            $data['IsAccessFollow']=false;
        }
        
        return ['data' => $data];
    }

    /**
     * @description:根据ID获取社区是否有用户具有多套房
     * @param: {type} _projectId
     * @author: shoubin.chen 2023-10-10 15:58:10 v6.7
     * @lastEditor: shoubin.chen 2023-10-10 15:58:10 v6.7
     */
    public function getCommunityIsMulti()
    {
        $params = [PROXY_ROLE_CHECK['projectId']];
        list($projectId) = $this->getParams($params);
        $this->loadUtil('user');
        $isMulti = 0;
        $allUserData = $this->utils->user->getAllResident($projectId);
        foreach ($allUserData as $userData) {
            $count = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userData['UserInfoUUID'], 'count(*)')[0]['count(*)'];
            if ($count > 1) {
                $isMulti = 1;
                break;
            }
        }
        return $isMulti;

    }


    /*
     *@description 检测社区是否是多套房社区
     *<AUTHOR> 2024-01-24 15:00:21 V6.7.0
     *@lastEditor cj 2024-01-24 15:00:21 V6.7.0
     *@param {*} PROXY_ROLE['projectId']
     *@return ['data' => ['IsMulti' => 1]] IsMulti 0:无多套房1有多套房
     */
    public function checkCommunityIsMulti()
    {
        $params = [PROXY_ROLE_CHECK['projectId']];
        list($projectId) = $this->getParams($params);
        $isMulti = $this->callSelfFunc('getCommunityIsMulti',[$projectId]);
        return ['data' => ['IsMulti' => $isMulti]];
    }


    /*
     *@description 获取PM管理的社区信息
     *<AUTHOR> 2024-02-28 20:07:29 V6.7.0
     *@lastEditor cj 2024-02-28 20:07:29 V6.7.0
     *@param {*} PROXY_ROLE_CHECK['projectId']
     *@return data
     */
    public function getDetailForPM()
    {
        $params = [PROXY_ROLE_CHECK['projectId'], PROXY_ROLE_CHECK['pmId'], PROXY_ROLE_CHECK['projectUUID']];
        list($projectId, $pmId, $projectUUID) = $this->getParams($params);

        $communityData = $this->utils->self->getInfo($projectId);
        $this->loadUtil('account', true);
        $insData = $this->utils->_common->account->getManagerInfo($communityData['ManageGroup']);
        $communityBasic = $communityData['basic'];
        $data = $communityData['info'];

        $insChargeMode = intval($insData['ChargeMode']);
        $communityChargeMode = intval($communityBasic['ChargeMode']);
        if($insChargeMode != $communityChargeMode){
            $communityChargeMode = strval(CHARGEMODE_NOPERMISSION);
        }else{
            $communityChargeMode = strval($communityChargeMode);
        }


        $this->loadUtil('manage', true);
        $pmMngInfo = $this->utils->_common->manage->getPmMngInfo($pmId, $projectId);
        $data['IsHaveAccessArea'] = 1;
        if($data['IsNew'] === '1'){
            //只有新社区有pm权限门禁控制
            $data['IsHaveAccessArea'] = $this->share->util->getSpecifyBitLE($data['Switch'], 7);
        }
        $data['EnableDeleteAccount'] = intval($pmMngInfo['EnableDeleteAccount']);
        $data['EnableShowLog'] = intval($pmMngInfo['EnableShowLog']);
        $data['AccountUUID'] = $communityBasic['UUID'];
        $data['Location'] = $communityBasic['Location'];
        $data['TimeZone'] = $communityBasic['TimeZone'];
        $data['CustomizeForm'] = $communityBasic['CustomizeForm'];
        $data['Grade'] = $communityBasic['Grade'];
        $data['ChargeMode'] = $communityChargeMode;
        $data['ParentID'] = $communityBasic['ParentID'];

        $data['TriggerAction'] = $this->share->util->getSpecifyBitLE($data['Switch'], 6);
        $data["EnableSIMWarning"] = $this->share->util->getSpecifyBitLE($data["Switch"], 4);
        $data["EnableUserPin"] = $this->share->util->getSpecifyBitLE($data["Switch"], 3);
        $data["DevOfflineNotify"] = $this->share->util->getSpecifyBitLE($data["Switch"], 2);
        $data["EnableLandline"] = $this->share->util->getSpecifyBitLE($data["Switch"], 1);
        $data["IsSendEmergencyNotifications"] = $this->share->util->getSpecifyBitLE($data["Switch"], 8);
        //v6.1 修改为了获取对应dis对pin是否加密
        $dis = $this->share->util->getDisForInstaller($projectId);
        $data['IsEncryptPin'] = $dis['IsEncryptPin'];

        $featureExpireTime = $data['FeatureExpireTime'];

        $this->loadUtil('featurePlan', true);
        $featureData = $this->utils->_common->featurePlan->getInfoByProject($projectId);
        $item = $featureData['Item'];
        $featureItem = $this->utils->_common->featurePlan->checkFeaturePlan(
            $featureExpireTime,
            $item,
            $featureData['FeatureID']
        );
        $data['LimitCreatePin'] = $featureItem[1];
        $data['LimitQRCode'] = $featureItem[2];
        // 高级功能，第七位社区第三方设备
        $data['ThirdPartyDevices'] = $this->share->util->getSpecifyBitLE($item, 7);
        // 判断是否展示三方中控设备
        $this->loadUtil('device');
        $data['ControlDevices'] = $this->utils->device->getListByArray([['MngAccountID', $projectId], ['Brand', 1]], 'count(*)')[0]['count(*)'] > 0 ? 1: 0;
        $this->loadUtil('system', true);
        $cnfInfo = $this->utils->_common->system->getCnfInfo();
        if (intval($cnfInfo['IsSmartHomeStatus']) === '1') {
            $switch = $data['Switch'];
            $smartHomeSwitch = $this->share->util->getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch === 1) {
                $data['SmartHomeUUID'] = $this->dao->smartHomeManageMap->selectByAccount($communityBasic['Account'], 'SmartHomeUUID')[0]['SmartHomeUUID'];
            }
        }
        
        //V6.7.1 新增返回IsAutoRenew，pm开关跟随ins
        $this->loadUtil('account', true);
        $insData = $this->utils->_common->account->getManagerInfo($communityBasic['ManageGroup']);
        $dis = $this->utils->_common->account->getManagerInfoUUID($insData['ParentUUID']);
        $disInfo = $this->utils->_common->account->getDistributorInfo([['Account', $dis['LoginAccount']]])[0];
        //dis是否开启ID-Access
        $data['EnableIDAccess'] = intval($disInfo['IDAccess']);
        $data['EnableRFCard'] = intval($disInfo['IsEnableRfCardControl']);
        $data['IsAutoRenew'] = intval($insData['IsAutoRenew']);
        $this->loadUtil('communalFee');
        $communalFeeInfo = $this->utils->communalFee->getCommunityBasicCommunalFeeInfo($communityBasic['UUID']);
        $data['CommunalFee'] = $communalFeeInfo['CommunalFee'];
        $data['FeatureSwitch'] = $communalFeeInfo['FeatureSwitch'];
        $data['DueDate'] = $communalFeeInfo['DueDate'];

        //6.8.1增加返回是否Premium功能方案以及是否有booking数据字段
        $data['IsHaveBooking'] = $this->share->util->getSpecifyBitLE($item, 9);
        $this->loadUtil('amenity');
        $hasAmenityData = $this->utils->amenity->checkAmenityDataByProject($projectUUID);
        $data['IsHaveAmenityData'] = intval($hasAmenityData);

        //6.8.1增加返回是否Premium功能方案以及是否有ID-Access数据字段
        $data['IsHaveIDAccess'] = $this->share->util->getSpecifyBitLE($item, 8);

        //7.1.0 增加返回是否Premium功能方案是否有RF Card数据字段，
        $data['IsHaveRFCard'] = intval($featureItem[3]);

        //7.1.0 增加rentManager的IsAccessFollow字段
        $this->loadUtil('rentManager');
        $rentManagerCommunityInfo=$this->utils->rentManager->getCommunityInfo($data['AccountUUID']);
        if (count($rentManagerCommunityInfo)!=0 && $rentManagerCommunityInfo[0]['IsAccessFollow']==1){
            $data['IsAccessFollow']=true;
        }else{
            $data['IsAccessFollow']=false;
        }

        $data['TempkeyValidityPeriod'] = intval($data['TempkeyValidityPeriod']);
        
        $data['PackageDetection'] = $data['EnablePackageDetection'];
        $data['CreateTenantsDate']=intval($rentManagerCommunityInfo[0]['CreateTenantsDate'])? intval($rentManagerCommunityInfo[0]['CreateTenantsDate']) : 0;
        $data['DeleteTenantsDate']=intval($rentManagerCommunityInfo[0]['DeleteTenantsDate'])? intval($rentManagerCommunityInfo[0]['DeleteTenantsDate']): 0;
        $this->loadUtil('version', true);
        $data['OpenAllDoorsModel'] = $this->utils->_common->version->getOpenAllDoorsModel();
        $data['IsRentManager']=count($rentManagerCommunityInfo)!=0;
        $data['SyncDoorMethodToThirdPartyLock'] = $this->share->util->getBitPositions($data['SyncDoorMethodToThirdPartyLock']);
        $this->loadUtil('thirdLock');
        $data['HasITec'] = $this->utils->thirdLock->checkCommunityHasITecLock($projectUUID);
        $this->loadUtil('access', true);
        $data['HasDormakaba'] = $this->utils->thirdLock->checkCommunityHasDormakabaLock($projectUUID);
        $data['EmergencyDoorGroup'] = [];
        if ($data['IsAllEmergencyDoor'] == '0') {
            // 非全选，则需要返回所配置选配置的Emergency door
            $data['EmergencyDoorGroup'] = $this->dao->communityEmergencyDoorGroup->selectByKey('AccountUUID', $data['AccountUUID'], 'ID,UUID,DevicesUUID,RelayIndex,RelayType');
        }

        return ['data' => $data];
    }

    /*
     *@description 获取社区管理的全部房间列表信息
     *<AUTHOR> 2024-03-07 16:29:33 V6.7.0
     *@lastEditor cj 2024-03-07 16:29:33 V6.7.0
     *@param {*} ProjectUUID:uuid
     *@return $data
     */
    public function getMngAllRoom()
    {
        $params = ['ProjectUUID:uuid'];
        list($projectUUID) = $this->getParams($params);
        $this->loadUtil('account', true);
        $personalAccountList = $this->utils->_common->account->personalAccountSelectByArray([['ParentUUID', $projectUUID], ['Role', COMENDMROLE]], 'RoomID,UUID');
        $personalAccountRoomArr = array_column($personalAccountList, 'UUID', 'RoomID');
        $this->loadUtil('communityRoom', true);
        $roomList = $this->utils->_common->communityRoom->getRoomInfoByArr([['ID', array_column($personalAccountList, 'RoomID')]], 'ID,RoomName as RoomNumber');
        foreach ($roomList as &$val) {
            $val['PersonalAccountUUID'] = $personalAccountRoomArr[$val['ID']];
        }
        unset($val);
        return ['data' => $roomList];
    }
}