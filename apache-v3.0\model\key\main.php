<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;
include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

include_once __DIR__."/add.php";
include_once __DIR__."/afterAdd.php";
include_once __DIR__."/remove.php";
include_once __DIR__."/update.php";
include_once __DIR__."/query.php";
class CKey {
	private $type;//访问接口类型 
	private $tabelName = ['PersonalAppTmpKey','PersonalRfcardKey','PersonalPrivateKey'];
	
	use \model\key\add;
    use \model\key\remove;
    use \model\key\update;
    use \model\key\query;
}