<?php

namespace dao;
use framework\BasicDao;

class PersonalDevices extends BasicDao
{
    //当前表名
    public $table = 'PersonalDevices';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    
    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["Grade", 11], ["ManageGroup", 0, "!="]]
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }
    
    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据MAC的值查询对应数据
     * @param {string} $mac MAC的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByMAC($mac, $fields = '*')
    {
        return $this->selectByKey('MAC', $mac, $fields);
    }

    /**
     * @description: 根据MAC的值查询对应数据
     * @param {array} $macs MAC的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByMACWArray($macs, $fields = '*')
    {
        return $this->selectByKeyWArray('MAC', $macs, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 20:25 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 20:25 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    /**
     * @description:super获取可以升级的设备列表
     * @author:lwj 2023-03-22 09:41:06 V6.5.4
     * @lastEditor:lwj 2023-03-22 09:41:06 V6.5.4
     * @param:
     * @return array
     */
    public function getAllForUpgrade($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $where = ' D.Firmware != :Firmware AND D.Firmware like :Model AND Status = 1 AND PA.ParentUUID = :ParentUUID';
        if($searchValue !== null && $searchValue !== ''){
            switch ($searchKey){
                case 'Name':
                    $where.= ' and md5(PA.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue)';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'MAC':
                    $where.= ' and D.MAC like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                case 'Location':
                    $where.= ' and D.Location like :SearchValue';
                    $bindArray[':SearchValue'] = "%$searchValue%";
                    break;
                default:
                    break;
            }
        }
        $deviceTable = PROXY_TABLES['personalDevices'];
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $sql = "select D.ID,D.MAC,D.Location,D.Firmware,D.Status,PA.Name
                from $deviceTable D 
                left join $personalAccountTable PA on PA.Account = D.Node 
                where $where";
        $deviceList = $this->execute($sql, $bindArray);
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /*
     *@description 获取项目下的可升级设备列表
     *<AUTHOR> 2023-06-12 17:15:26 V6.6.0
     *@lastEditor cj 2023-06-12 17:15:26 V6.6.0
     *@param {*} bindArray
     *@param {*} searchValue
     *@return $deviceList
     */
    public function getAllForUpgradeForProject($bindArray)
    {
        $node = $bindArray[':Node'];
        $where = " (D.MAC like :MAC OR D.Location like :Location) ";
        if ($node !== '' && !is_null($node)) {
            $where = " (D.MAC like :MAC OR md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Node) OR D.Location like :Location) ";
        }
        $sql = 'select D.ID,D.MAC,D.Location,D.Firmware,D.Status,A.Name from ' .PROXY_TABLES['personalDevices'].' D 
        left join ' .PROXY_TABLES['personalAccount'].' A on A.Account = D.Node 
        where '.$where.' AND D.Firmware != :Firmware AND D.Firmware like :Model AND Status = 1 AND A.ParentUUID = :ParentUUID';
        $deviceList = $this->execute($sql, $bindArray);
        // 数据解密
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /**
     * @description
     * @param
     * @param $notFirmwares
     * @param $userUUIDss
     * @return array
     * @throws \Exception
     * <AUTHOR> 2025/2/17 18:05 v7.1.0
     * @lastEditor clay 2025/2/17 18:05 v7.1.0
     */
    public function getAllForUpgradeForNodes($notFirmwares, $userUUIDs)
    {
        if (count($userUUIDs) == 0) {
            return [];
        }
        $bindArray = [];
        if (count($notFirmwares) == 0) {
            $notFirmwareConditionStr = '';
        }else {
            list($notFirmwareStr, $bindArray) = $this->share->util->getImplodeData($notFirmwares, 'Firmwares');
            $notFirmwareConditionStr = ' AND D.Firmware NOT IN ('.$notFirmwareStr.') ';
        }
        list($userUUIDsStr, $bindArrayUser) = $this->share->util->getImplodeData($userUUIDs, 'UserUUID');
        $bindArray = array_merge($bindArray, $bindArrayUser);
        $sql = 'SELECT
	D.ID,
	"" AS UnitID,
	A.UUID AS userUUID,
	D.MAC,
	D.Location,
	D.Firmware,
	D.Status,
	"" As ProjectUUID,
	A.Name
FROM
	' .PROXY_TABLES['personalDevices'].' D
	LEFT JOIN ' .PROXY_TABLES['personalAccount'].' A ON A.Account = D.Node 
WHERE
    D.Type != 2 '.$notFirmwareConditionStr.'
	AND Status = 1 
	AND A.UUID IN ('.$userUUIDsStr.')';
        $deviceList = $this->execute($sql, $bindArray);
        // 数据解密
        foreach ($deviceList as $key => $data) {
            $deviceList[$key] = $this->dataArrDecode($data, ['Name']);
        }
        return $deviceList;
    }

    /**
     * @description
     * @param
     * @param $bindArray
     * @return array
     * @throws \Exception
     * <AUTHOR> 2025/2/17 14:37 v7.1.0
     * @lastEditor clay 2025/2/17 14:37 v7.1.0
     */
    public function getDeviceUpgradeInfo($bindArray)
    {
        $sql = 'SELECT
	"" AS UnitID,
	A.UUID as UserUUID,
	U.MAC,
	U.UpgradeRomVerID,
	U.`Status`
FROM
    ' .PROXY_TABLES['upgradeRomDevices'].' as U
	join ' .PROXY_TABLES['personalDevices'].' D on D.MAC=U.MAC
	LEFT JOIN ' .PROXY_TABLES['personalAccount'].' A ON A.Account = D.Node 
WHERE
	U.UpgradeRomVerID IN (:VersionIDs)';
        return $this->execute($sql, $bindArray);
    }

    /*
     *@description 获取全部的离线设备列表，包括单住户、社区、办公
     *<AUTHOR> 2023-09-21 11:53:47 V6.7.0
     *@lastEditor cj 2023-09-21 11:53:47 V6.7.0
     *@param {*} projectIds 社区、办公ID array
     *@param {*} perAccount 单住户管理员Account
     *@param {*} limitAndSearch
     *@return deviceList
     */
    public function getAllOfflineDeviceList($projectIds, $perAccount, $limitAndSearch)
    {
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $limitAndSearch;
        $orderBySql = " order by LastDisConn desc";
        if (in_array($sortField, ['Location', 'LastDisConn']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort";
        }
        $limitOrderSql = $orderBySql." limit $offset,$row";
        $searchWhere = '';
        $bindArray = [':Community' => $perAccount];
        if ($searchValue !== '') {
            $searchWhere =  "and (Location like :SearchValue or MAC like :SearchValue)";
            $bindArray[':SearchValue'] = "%$searchValue%";
        }
        if (count($projectIds) === 0) {
            $fromWhere = "from {$this->table} where Community = :Community and Status = 0 and Flag = 0 $searchWhere";
            $sql = "select ID,UUID,MAC,Location,Community as SiteID,Node as SiteNode,LastDisConn,'2' as ProjectType,Type,Status $fromWhere $limitOrderSql";
            $countSql = "select count(*) $fromWhere";
            $total = $this->execute($countSql, $bindArray)[0]['count(*)'];
        } else {
            list($idBindStr, $idBindArray) = $this->share->util->getImplodeData($projectIds);
            $deviceTable = PROXY_TABLES['devices'];
            $bindArray = array_merge($bindArray, $idBindArray);
            $fromWhere1 = "from $deviceTable where MngAccountID in ($idBindStr) and Status = 0 and Brand = 0 $searchWhere";
            $fromWhere2 = "from {$this->table} where Community = :Community and Status = 0 and Brand = 0 and Flag = 0 $searchWhere";
            
            $sql = "select * from (select ID,UUID,MAC,Location,MngAccountID as SiteID,Node as SiteNode,LastDisConn,ProjectType,Type,Status $fromWhere1 union all
            select ID,UUID,MAC,Location,Community as SiteID,Node as SiteNode,LastDisConn,'2' as ProjectType,Type,Status $fromWhere2) as DeviceTable $limitOrderSql";
            $countSql = "select count(*) $fromWhere1 union all select count(*) $fromWhere2";
            $count = $this->execute($countSql, $bindArray); 
            $total = $count[0]['count(*)'] + $count[1]['count(*)'];
        }
        $deviceList = $this->execute($sql, $bindArray);

        return [$deviceList, intval($total)];
    }

}