<?php
namespace package\single\model\message\method;

trait Get
{
    /**
     * @description:消息内容(如转换词条)
     * @author:lwj 2023-04-17 09:27:31 V6.6
     * @lastEditor:lwj 2023-04-17 09:27:31 V6.6
     * @param:{array} Data message的字段内容
     * @return mixed
     */
    public function getContent()
    {
        $params = ['Data'];
        list($data) = $this->getParams($params);
        $msgText = $this->share->util->getMsg()->getMsgText();
        $this->loadUtil('message', true);
        $batteryWarningMsg = $this->utils->_common->message->getBatteryWarningMsg();
        if ($data['Type'] === MESSAGE_TYPE['delivery']) {
            $data['Content'] = vsprintf($msgText['deliveryMsg'], $data['Content']);
            $data['Title'] = $msgText['deliveryTitle'];
        } elseif ($data['Type'] === MESSAGE_TYPE['tempKey']) {
            $data['Title'] = $msgText['tempkeyUsed'];
            $data['Content'] = vsprintf($msgText['tempkeyContent'], $data['Content']);
        } elseif ($data['Type'] === MESSAGE_TYPE['jp']) {
            $content = json_decode($data['Content'], true);
            $aptNum = $content['AptNum'];
            $boxNum = $content['BoxNum'];
            $boxPwd = $content['BoxPwd'];
            $data['Content'] = vsprintf('%s号室様。宅配ボックス%sに荷物をお預けしました。暗証番号は、%sです。', [$aptNum, $boxNum, $boxPwd]);
            $data['Title'] = '新しい荷物があります！';
        } elseif (array_key_exists($data['Type'], $batteryWarningMsg)) {
            $data['Title'] = $msgText['yaleBatteryWarning'];
            // Content如果type=4,5,6时代表电量剩余情况 4-剩余two week 5-剩余one weeks 6-需更换电池
            $data['Content'] = vsprintf($batteryWarningMsg[$data['Type']], $data['Content']);
        }else if ($data['Type'] === MESSAGE_TYPE['dormakabaBatteryWarning']) {
            $data['Title'] = $msgText['dormakabaBatteryWarning'];
            $data['Content'] = vsprintf($msgText['dormakabaLowBattery'], $data['Content']);
        }else if ($data['Type'] === MESSAGE_TYPE['iTecBatteryWarningThreshold15']) {
            $data['Title'] = $msgText['iTecBatteryWarningThreshold'];
            $data['Content'] = vsprintf($msgText['iTecBatteryWarningThreshold15Content'], $data['Content']);
        } else if ($data['Type'] === MESSAGE_TYPE['iTecBatteryWarningThreshold10']) {
            $data['Title'] = $msgText['iTecBatteryWarningThreshold'];
            $data['Content'] = vsprintf($msgText['iTecBatteryWarningThreshold10Content'], $data['Content']);
        } else if ($data['Type'] === MESSAGE_TYPE['iTecBatteryWarningThreshold5']) {
            $data['Title'] = $msgText['iTecBatteryWarningThreshold'];
            $data['Content'] = vsprintf($msgText['iTecBatteryWarningThreshold5Content'], $data['Content']);
        } else if ($data['Type'] === MESSAGE_TYPE['akubelaLockBatteryLevel']) {
            $data['Title'] = $msgText['AkubelaLockBatteryLevelTitle'];
            // http://192.168.10.102:8071/pages/viewpage.action?pageId=78941439#v7.0.1应用后台研发设计-1.3.3云内部交互
            // {lock_name_no_need_translation} - Lock battery is running low. Remaining battery level: {battery_level_no_need_translation}%.
            $translateContent = $msgText['AkubelaLockBatteryLevelContent'];
            $translateContent = str_replace("{lock_name_no_need_translation}", $data['Content'], $translateContent);
            $extensionFieldJson = $data['ExtensionField'];
            $tmpDecode = json_decode($extensionFieldJson, true);
            $lastBattery = $tmpDecode['last_battery'];
            $translateContent = str_replace("{battery_level_no_need_translation}", $lastBattery, $translateContent);
            $data['Content'] = $translateContent;
        } else if ($data['Type'] === MESSAGE_TYPE['akubelaLockTrailErrorNotice']) {
            $data['Title'] = $msgText['AkubelaLockTrailErrorNoticeTitle'];
            // {lock_name_no_need_translation} - The lock has had three incorrect password entries. Please check its status.
            $translateContent = $msgText['AkubelaLockTrailErrorNoticeContent'];
            $translateContent = str_replace("{lock_name_no_need_translation}", $data['Content'], $translateContent);
            $data['Content'] = $translateContent;
        } else if ($data['Type'] === MESSAGE_TYPE['akubelaLockDoorbellEventNotification']) {
            $data['Title'] = $msgText['AkubelaLockDoorbellEventNotificationTitle'];
            // {lock_name_no_need_translation} - Someone rang your doorbell, please check in time.
            $translateContent = $msgText['AkubelaLockDoorbellEventNotificationContent'];
            $translateContent = str_replace("{lock_name_no_need_translation}", $data['Content'], $translateContent);
            $data['Content'] = $translateContent;
        }
        return $data;
    }
}