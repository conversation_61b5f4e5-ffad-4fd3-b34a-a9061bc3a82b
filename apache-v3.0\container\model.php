<?php
/*
 * @Description: 模块容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-02 15:34:14
 * @LastEditors  : kxl
 */
namespace container;

include_once __DIR__."/../interfaces/container/main.php";
include_once __DIR__."/../container/data.php";
include_once __DIR__."/../util/string.php";
include_once __DIR__."/../database/main.php";

class model implements \interfaces\container\main\modelContainer
{
    protected $binds = [];
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // 创建model
    public function make($model, array $params=[])
    {
        // 已经存在的model直接返回
        if (array_key_exists($model, $this->binds)) {
            $bind = $this->binds[$model];
            // 刷新必要属性
            foreach ($params as $key=>$value) {
                $bind->$key = $value;
            }
            return $bind;
        }
        return $this->binds[$model] = $this->resolve($model, $params);
    }

    // model解析
    public function resolve($model, array $params=[])
    {
        global $cLog,$cAuditLog,$db;
        require_once __DIR__."/../model/$model/main.php";
        $model = "\\model\\".\util\string\getClassName($model);
        // 实例化model，设置log和db等属性
        $model = new $model();
        $model->log = $cLog;
        $model->auditLog = $cAuditLog;
        $model->db = $db;
        foreach ($params as $key=>$value) {
            $model->$key = $value;
        }
        return $model;
    }

    // 注入数据容器
    public function setDataContainer($model, \interfaces\container\main\dataContainer $dataContainer)
    {
        $model->dataContainer = $dataContainer;
    }

    // 为model添加models数组
    public function addModels($model, $modelName, $methodName, array $params = [])
    {
        global $gApp;
        // 子模块已经在models里面，无需重新绑定
        if (array_key_exists($modelName, $model->models)) {
            $subModel = $model->models[$modelName];
        } else {
            $newModel = $this->make($modelName, $params);
            // 获取数据容器
            $dataContainerName = array_key_exists("dataContainer", MODELS[$modelName]) ? $model["dataContainer"] : "common";
            $dataContainer = \container\setData($gApp["plan"]["data"], $dataContainerName);
            // 添加数据容器
            self::getInstance()->setDataContainer($newModel, $dataContainer);

            $subModel = new subModel($newModel);
            $model->models[$modelName] = $subModel;
        }
        $subModel->_setMethod($methodName);
        return $subModel;
    }
}

// 子模块，注入
class subModel
{
    // 方法组，要调用其他模块方法必须先在此声明
    private $methods = [];
    public $model = null;

    // 初始化设置model
    public function __construct($model)
    {
        $this->model = $model;
    }

    public function __call($name, $arguments)
    {
        if (!in_array($name, $this->methods)) {
            throw new \Exception("Please register model's method:$name first");
        } else {
            return $this->model->$name(...$arguments);
        }
    }

    // 设置方法组
    public function _setMethod($name)
    {
        if (!in_array($name, $this->methods)) {
            array_push($this->methods, $name);
        }
    }
}
