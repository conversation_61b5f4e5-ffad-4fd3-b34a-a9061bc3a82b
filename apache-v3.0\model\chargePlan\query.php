<?php

namespace model\chargePlan;

trait query
{

    /**
     * @msg: 查询个人收费的计划
     * @param:
     * @services: billsysUtil
     */
    public function queryPersonal()
    {
        $params = [
            "ID" => "", //只接受单个id
            "userAliasId" => "", //installer Id
            "Type" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $type = $params["Type"];
        if (!in_array($type, [PAYACTIVE, PAYSUBDRCIPTION, PAYADDAPP, PAYLANDLINE])) {
            throw new \Exception("error value of type:$type");
        }
        $this->log->actionLog("#model#chargePlan#queryPersonal#userAliasId=$userId;id=$id;type=$type");
        $data = $this->services["billsysUtil"]->getCharge('single', $userId, [$id], $type);
        $data = $data[$id];
        // 加上主账号购买的App
        $freeAppNumber = $this->db->querySList(
            'select PF.FreeAppCount from PersonalAccountCnf PF join PersonalAccount P on P.Account = PF.Account where P.ID = :ID',
            [":ID" => $id]
        )[0]['FreeAppCount'];
        $data['LandlineFee'] = $data['MonthlyFee'];
        $data['AppNumber'] += $freeAppNumber;

        // 最大账号限制
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"];
        if ($data['AppNumber'] > $sysMaxSubCount) {
            $data['AppNumber'] = $sysMaxSubCount;
        }
        \util\computed\setGAppData(["data" => $data]);
    }
    /**
     * @name: 查询小区收费计划
     * @service： charge
     */
    public function queryCommunity()
    {
        $params = [
            "ID" => "", //只接受单个id
            "userAliasId" => "", //社区Id
            "Type" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $type = $params["Type"];
        if (!in_array($type, [PAYACTIVE, PAYSUBDRCIPTION, PAYADDAPP, PAYLANDLINE]) || $type === null || $type === '') {
            throw new \Exception("error value of type:$type");
        }
        $this->log->actionLog("#model#chargePlan#queryPersonal#userAliasId=$userId;id=$id;type=$type");
        $data = $this->services["billsysUtil"]->getCharge('multiple', $userId, [$id], $type);
        $data = $data[$id];
        // 加上主账号购买的App
        $freeAppNumber = $this->db->querySList(
            'select PF.FreeAppCount,PF.AllowCreateSlaveCnt,PF.Flags from PersonalAccountCnf PF join PersonalAccount P on P.Account = PF.Account where P.ID = :ID',
            [":ID" => $id]
        )[0];
        $data['amcount'] = $data['AddAppFee'];
        $data['AppNumber'] += $freeAppNumber['FreeAppCount'];
        $data['FamilyLimit'] = $this->queryFeaturePlanLimitMember($id);

        // 高级功能家庭数目限制
        $FeatureExpireTime = $this->db->querySList('select FeatureExpireTime from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['FeatureExpireTime'];
        $Item = $this->db->querySList('select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID', [':ID' => $userId])[0]['Item'];
        $isFamilyLimit = \util\computed\getSpecifyBitLE($Item, 4);
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"];
        $now = \util\computed\getNow();
        if ($isFamilyLimit == 1 && $freeAppNumber['Flags'] % 2 == 1 && strtotime($FeatureExpireTime) > strtotime($now)) {
            if ($data['AppNumber'] > ($freeAppNumber['AllowCreateSlaveCnt'] + 1)) {
                $data['AppNumber'] = $freeAppNumber['AllowCreateSlaveCnt'] + 1;
            }
        } elseif ($data['AppNumber'] > $sysMaxSubCount) {
            $data['AppNumber'] = $sysMaxSubCount;
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 6.2新增家庭从账户限制
     */
    public function queryFeaturePlanLimitMember($userId)
    {
        $familyMember = $this->db->querySList("select Pc.AllowCreateSlaveCnt,Pc.Flags,Pa.Account,Pa.ParentID from PersonalAccount Pa join PersonalAccountCnf Pc on Pa.Account = Pc.Account where Pa.ID = :ID", [":ID" => $userId])[0];
        $addedAllNumber = $this->db->querySList('select count(*) from PersonalAccount where ParentID = :ParentID and role = 21', [':ParentID' => $userId])[0]['count(*)'];
        $addedNumber = $this->db->querySList("select count(*) from APPSpecial where Node = :Node", [":Node" => $familyMember['Account']])[0]["count(*)"];
        $now = \util\computed\getNow();
        $FeatureExpireTime = $this->db->querySList('select FeatureExpireTime from CommunityInfo where AccountID = :AccountID', [':AccountID' => $familyMember['ParentID']])[0]['FeatureExpireTime'];
        // 判断最大用户数
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"] - 1;
        if (($addedAllNumber + 1) > $sysMaxSubCount) {
            return 1;
        }
        // 判断高级功能是否开启
        $Item = $this->db->querySList('select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID', [':ID' => $familyMember['ParentID']])[0]['Item'];
        $isFamilyLimit = \util\computed\getSpecifyBitLE($Item, 4);
        if ($isFamilyLimit == 1 && $familyMember['Flags'] % 2 == 1 && strtotime($FeatureExpireTime) > strtotime($now)) {
            if (($addedNumber + 1) > $familyMember['AllowCreateSlaveCnt']) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * @name: 处理收费计划，获取个人每月收费价格
     * @param：data:收费计划
     */
    public function queryPerMonthlyPay()
    {
        $params = [
            "data" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $amcount = $data["LandlineFee"];
        \util\computed\setGAppData(["amcount" => $amcount]);
    }

    public function queryComMonthlyPay()
    {
        $params = [
            "data" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $amcount = $data["MonthlyFee"];
        \util\computed\setGAppData(["amcount" => $amcount]);
    }

    /**
     * @name: 查询个人从账户收费情况
     * @service: charge(在querySubPay中引用)
     */
    public function queryPerUserSubPay()
    {
        $params = [
            "userAliasId" => "",
            // 可能是从账户
            "userId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        // $perMngId = $this->db->querySList("select ParentID from PersonalAccount where ID = :ID",[":ID"=>$userId])[0]["ParentID"];
        $this->querySubPay($userId, 1);
    }

    /**
     * @name: 查询社区从账户收费情况
     * @service: charge(在querySubPay中引用)
     */
    public function queryComUserSubPay()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        // $comMngId = $this->db->querySList("select ParentID from PersonalAccount where ID = :ID",[":ID"=>$userId])[0]["ParentID"];
        $this->querySubPay($userId, 2);
    }


    /**
     * @name: 从账户激活费
     * @param: $userId 主账户id
     * @service：charge
     */
    private function querySubPay($userId, $type = 1)
    {
        $params = [
            "userId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $myId = $params["userId"];
        $pcMngId = $this->db->querySList("select ParentID from PersonalAccount where ID=:ID", [":ID" => $userId])[0]["ParentID"];
        $charges = $this->services["billsysUtil"]->getCharge($type == 1 ? 'single' : 'multiple', $pcMngId, [$userId], PAYADDAPP);
        $subMoney = $charges[$userId]["AddAppFee"];
        $introduction = $charges[$userId]["Introduction"];
        $addAppSaveFee = $charges[$userId]["AddAppSaveFee"];
        // App上从账户自己购买需要知道ID
        $data = ["money" => $subMoney, "month" => "", "day" => "", "addAppFee" => $subMoney, "amcount" => $subMoney, "ID" => $myId, "introduction" => $introduction, "addAppSaveFee" => $addAppSaveFee];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @name: 系统最大值设置查询
     */
    public function querySysSet()
    {
        $data = $this->db->queryAllList("SystemExtremum")[0];
        $data["MaxEntryFee"] = $this->outputComputedCount($data["MaxEntryFee"]);
        $data["MaxMonthlyFee"] = $this->outputComputedCount($data["MaxMonthlyFee"]);
        $data["MaxFeeApps"] = $this->outputComputedCount($data["MaxFeeApps"]);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryStripeKey()
    {
        \util\computed\setGAppData(["PubKey" => STRIPE_CLIENT_ID]);
    }

    // 查询终端用户的计费模型
    public function userBillingInfo()
    {
        $params = [
            "EnduserID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $enduserId = $params["EnduserID"];
        $res = $this->db->querySList(
            "SELECT Role,ParentID as pcMngId,ID as userId FROM PersonalAccount WHERE ID = :ID",
            [":ID" => $enduserId]
        )[0];

        if ($res['Role'] == PERENDSROLE || $res['Role'] == COMENDSROLE) {
            $res = $this->db->querySList(
                "SELECT Pa.Role,Pb.ParentID as pcMngId,Pa.ParentID as userId FROM PersonalAccount Pa join PersonalAccount Pb on Pa.ParentID = Pb.ID WHERE Pa.ID=:ID 
                AND (Pa.Role = " . PERENDSROLE . " or Pa.Role = " . COMENDSROLE . ")",
                [":ID" => $res['userId']]
            )[0];
        }

        $role = $res['Role'];
        $pcMngId  = $res['pcMngId'];
        $userId  = $res['userId'];
        if ($role == COMENDMROLE || $role == COMENDSROLE) {
            $type = "multiple";
        } else if ($role == OFFPERSONNELROLE || $role == OFFSTAFFROLE) {
            $type = "office";
        } else {
            $type = "single";
        }
        list($data, $newUser) = $this->services["billsysUtil"]->getInstanceUserData($type, $pcMngId, [$userId], PAYSUBDRCIPTION);
        // 向计费系统查询计划
        $result = $this->services["billsysUtil"]->queryChargePlan(["ChargeData" => json_encode(["Instance" => $data, "User" => $newUser])]);
        $result = $result["data"];
        \util\computed\setGAppData(["data" => $result]);
    }
}
