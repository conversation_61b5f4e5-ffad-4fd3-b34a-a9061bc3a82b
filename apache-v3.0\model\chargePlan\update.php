<?php
namespace model\chargePlan;
trait update {
    function setSup () {
        
    }


    function addPer () {
        
    }

    function setPer () {
        
    }

    function addCom () {
        
    }

    function setManage () {
        
    }

    function setControl ($entrFee,$monthlyFee,$appsNumber,$addAppsFee,$check=true) {
       
    }

    function setPersonalControl ($entrFee,$landlineFee,$appsNumber,$addAppsFee,$id=1) {
        
    }

    function setSys ($maxEntryFee,$maxMonthlyFee,$maxFeeApps,$maxApps) {

    }

    function isVaildChargeFee ($entryFee,$monthlyFee,$appsNumber,$addAppsFee,$isManage = true) {
      
    }

    function afterSetMonthFee () {
        
    }

    function delete () {
        
    }
}