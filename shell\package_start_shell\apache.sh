#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-21
# Description   :   apache 构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3              #需要部署的中间件

PKG_ROOT=$RSYNC_PATH
HTML_ROOT=/var/www/html

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

###################### 开始安装、读取配置 ######################
echo "Begin to install apache"
echo '读取配置'
source "$PKG_ROOT"/shell/package_start_shell/source.sh $PKG_ROOT

###################### 替换配置文件 ######################
echo '替换配置文件的配置'
if [ ! -d "$PKG_ROOT"/web_scripts ];then mkdir -p "$PKG_ROOT"/web_scripts; fi
# 生成配置文件 dynamic_config.php
create_php_config "$PKG_ROOT"/web_scripts/dynamic_config.php
mkdir -p $CONFWATCH_EXEC
if [ -f "$PKG_ROOT/web_scripts/change_web_conf_by_etcd.php" ];then
  cp -f "$PKG_ROOT"/web_scripts/change_web_conf_by_etcd.php "$CONFWATCH_EXEC"/
fi

###################### 复制安装包的文件 ######################
echo '复制apache安装包的文件'
mkdir -p $HTML_ROOT
if [ ! -d "$PKG_ROOT"/webroot/apache-v3.0/config/ ];then mkdir -p "$PKG_ROOT"/webroot/apache-v3.0/config/; fi
cp -f "$PKG_ROOT"/web_scripts/dynamic_config.php "$PKG_ROOT"/webroot/apache-v3.0/config/

if [ -d $HTML_ROOT/apache-v3.0 ]; then
    rm -rf $HTML_ROOT/apache-v3.0
fi
cp -rf "$PKG_ROOT"/webroot/apache-v3.0 $HTML_ROOT/

chmod 755 -R $HTML_ROOT
###################### 创建存放日志的文件夹 ######################
echo "创建存放日志的文件夹"
# php 日志
mkdir -p /var/log/php
touch /var/log/php/php-error.log
touch /var/log/php/php-error-detail.log
touch /var/log/php/php-person-action.log
touch /var/log/php/php-pay-action.log
touch /var/log/php/php-business.log

# php 和后台通信日志文件
touch /var/log/php/csadapt-interface.log
chown nobody:nogroup /var/log/php/csadapt-interface.log
chmod 666 /var/log/php/*.log

