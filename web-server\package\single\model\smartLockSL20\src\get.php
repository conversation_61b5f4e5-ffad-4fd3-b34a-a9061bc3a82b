<?php

namespace package\single\model\smartLockSL20\src;

trait Get
{
    public function getLinkDeviceListForApp()
    {
        $params = [PROXY_ROLE_CHECK['mainUserUUID']];
        list($mainUserUUID) = $this->getParams($params);

        $this->loadUtil('account', true);
        $node = $this->utils->_common->account->personalAccountSelectByKey('UUID', $mainUserUUID, 'Account')[0]['Account'];

        $this->loadUtil('device', true);
        $device = [DEVICE_TYPE['multipleDoor'], DEVICE_TYPE['singleDoor'], DEVICE_TYPE['accessControl']];
        $bindDeviceList = $this->utils->_common->device->getPersonalDevicesInfo(
            ['Node' => $node, 'Type' => $device], 'ID,UUID,Location,MAC,Relay,Firmware'
        );

        //获取设备的Relay使用情况
        $this->loadUtil('smartLockSL20', true);
        $this->loadUtil('device', true);
        foreach ($bindDeviceList as $index => &$device) {
            $device['LinkedRelayList'] = $this->utils->_common->smartLockSL20->getRelayInUse($device['UUID'], $device['MAC']);
        }
        unset($device);
        $bindDeviceList = array_values($bindDeviceList);

        return ['data' => $bindDeviceList];
    }
}