<?php
namespace model;

const IS_MESSAGE = 0;
const NOT_SENT_YET = 0;
const UNREAD = 0;
const INDOOR_AND_APP = 0;
const INDOOR_MONITOR_ONLY = 1;
const APP_ONLY = 2;

function addComControl($message, $title, $device, $clientType, $userId)
{
    global $db;
    global $cMessage;
    if (strlen($message)>MESSAGE_CONTENT_LENGTH || strlen($title)>MESSAGE_TITLE_LENGTH) {
        $cMessage->echoErrorMsg(StateAddFail);
    }
    $now = \util\computed\getNow();
    $names = [];
    $device = json_decode($device, true);
    foreach ($device as $val) {
        $userData = $db->querySList("select Name,RoomID,UnitID from PersonalAccount where Account = :Account", [":Account"=>$val])[0];
        $name = $userData['Name'];
        // V6.4 Receiver是空房间，展示楼号+Room号
        if (empty($name)) {
            $buildName = $db->querySList('select UnitName from CommunityUnit where ID = :ID', [':ID' => $userData['UnitID']])[0]['UnitName'];
            $roomData = $db->querySList('select RoomName,Floor from CommunityRoom where ID = :ID', [':ID' => $userData['RoomID']])[0];
            $roomName = $roomData['RoomName'];
            if ($roomData['Floor'] === '') {
                $name = $buildName.'-'.$roomName;
            } else {
                $name = $buildName.'-'.$roomData['Floor'].'-'.$roomName;
            }
        }
        array_push($names, $name);
    }
    $db->insert2List("Message", [":Content"=>$message,
    ":CreateTime"=>$now,
    ":AccountID"=>$userId,
    ":Title"=>$title,
    ":Type"=>IS_MESSAGE,
    ":Status"=>NOT_SENT_YET,
    ":NickNames"=>implode(';', $names),
    ":ReceiverType"=>$clientType
    ]);
    $messageID = $db->lastInsertId();

    if ($clientType == INDOOR_MONITOR_ONLY) {
        foreach ($device as $value) {
            sendMsgToDevice($value, $messageID);
        }
    } elseif ($clientType == APP_ONLY) {
        foreach ($device as $value) {
            sendMsgToApp($value, $userId, $messageID);
        }
    } elseif ($clientType == INDOOR_AND_APP) {
        foreach ($device as $value) {
            sendMsgToDevice($value, $messageID);
            sendMsgToApp($value, $userId, $messageID);
        }
    }
}

// 室内机，发送给家庭主账号
function sendMsgToDevice($value, $messageID)
{
    global $db;
    //插入发送方表
    $db->insert2List("MessageAccountList", [
        ":ClientType"=>INDOOR_MONITOR_ONLY,
        ":Account"=>$value,
        ":Status"=>UNREAD,
        ":MessageID"=>$messageID]);
}

// app，发送给家庭所有成员
function sendMsgToApp($value, $userId, $messageID)
{
    global $db;
    $allAccount = [];
    // 获取所有主从账号
    array_push($allAccount, $value);
    $mainId = $db->querySList("select ID from PersonalAccount where Account = :Account", [":Account"=>$value])[0]['ID'];
    $subId = $db->querySList('select Account from PersonalAccount where ParentID = :ParentID and Role in (11, 21)', [":ParentID"=>$mainId]);
    foreach ($subId as $subAccount) {
        array_push($allAccount, $subAccount['Account']);
    }
    // 发送给每一个成员
    foreach ($allAccount as $account) {
        //插入发送方表
        $db->insert2List("MessageAccountList", [
            ":ClientType"=>APP_ONLY,
            ":Account"=>$account,
            ":Status"=>UNREAD,
            ":MessageID"=>$messageID]);
    }
}

function deleteComControl($id, $userId)
{
    global $db;
    global $cMessage;
    $data = $db->querySList("select ID from Message where ID = :ID AND AccountID = :AccountID", [":ID"=>$id,":AccountID"=>$userId]);
    if (count($data) == 0) {
        $cMessage->echoErrorMsg(StateNotPermission);
    }
    $db->delete2ListWID("Message", $id);
    $db->delete2ListWKey("MessageAccountList", "MessageID", $id);
}

function betchDeleteControl($ids, $userId)
{
    global $db;
    foreach ($ids as $id) {
        $msgListId = $db->querySList("select ID from MessageAccountList where MessageID = :ID and Account = :User and ClientType = 2", [":ID"=>$id, ":User"=>$userId])[0];
        $db->delete2ListWID("MessageAccountList", $msgListId['ID']);
    }
}

/**
 * @description: 语音留言删除 v6.5.2
 * @author:lwj 2022/9/26 15:30
 * @lastEditor: lwj 2022/9/26 15:30
 * @param $ids
 * @param $userId
 * @return:
 * @throws \Exception
 */
function batchDeleteVoiceMsgControl($ids, $user)
{
    global $db, $cMessage;
    $userInfo = $db->querySList("select UUID from PersonalAccount where Account =:User", [':User' => $user])[0];
    if(empty($userInfo)){
        $cMessage->echoErrorMsg(StateNotPermission);
    }
    //去除空元素
    $ids = array_filter($ids);
    if(empty($ids)){
        return true;
    }

    list($idsBind, $bindArr) = \util\arr\getImplodeData($ids, 'ID');
    if (empty($idsBind) or empty($bindArr)) {
        return true;
    }
    $bindArr[':PersonalAccountUUID'] = $userInfo['UUID'];

    $msgList = $db->querySList(
        "select ID from PersonalVoiceMsgList where PersonalAccountUUID =:PersonalAccountUUID and ID in ($idsBind)",
        $bindArr
    );
    foreach ($msgList as $value) {
        $db->delete2ListWID('PersonalVoiceMsgList', $value['ID']);
    }
}
