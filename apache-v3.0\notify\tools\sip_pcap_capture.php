<?php
error_reporting(0);

require_once(dirname(__FILE__) . '/../socket.php');
require_once(dirname(__FILE__) . '/../funcs_kafka.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');


function pcapCaptureControl($type, $uuid, $sip) 
{
    $data[] = $type;
    $data[] = $uuid;
    $data[] = $sip;

    $socket = new CSipPcapCaptureControlSocket();
    $socket->setMsgID(MSG_C2S_SIP_PCAP_CAPTURE_CONTROL);
    $socket->copy($data);
}

//使用说明：① php sip_pcap_capture.php $sip $type(1开启,0结束)
function cmd_usage($cmd)
{
    echo("usage: php ". $cmd . " <sip> <type> \n");
    echo("type: 1 start, 0 stop  \n");
    echo("download: php sip_pcap_download.php <callid> \n");
    echo("export: sz <callid>.tar.gz \n");
    exit(0);
}

$sip = $argv[1];
$type = $argv[2];

pcapCaptureControl($type, "1234444", $sip);