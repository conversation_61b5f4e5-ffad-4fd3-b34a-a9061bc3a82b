<?php
ini_set('date.timezone','Asia/Shanghai');
require_once (dirname(__FILE__).'/dynamic_config.php');

const AREA_NODE_TYPE_NONE = 0;
const AREA_NODE_TYPE_AREA = 1;
const AREA_NODE_TYPE_BUILDING = 2;
const AREA_NODE_TYPE_UNIT = 3;
const AREA_NODE_TYPE_FLOOR = 4;
const AREA_NODE_TYPE_ROOM = 5;
const AREA_NODE_TYPE_MAX = 6;
//设备类型
const DEVICE_TYPE_STAIR = 0;
const DEVICE_TYPE_DOOR = 1;
const DEVICE_TYPE_INDOOR = 2;
const DEVICE_TYPE_MANAGEMENT = 3;
const DEVICE_TYPE_WALL = 4;

//added by chenyc,个人终端用户操作类型
const UPDATE_ADD_USER = 0;
const UPDATE_DEL_USER = 1;

const OPEN_ALL_DOOR = 1;
const CLOSE_ALL_DOOR = 0;

//added by chenyc,小区增加或者删除
const UPDATE_ADD_COMMUNITY = 0;
const UPDATE_DEL_COMMUNITY = 1;
const LOG_FILE = "/var/log/php/csadapt-interface.log";
const ROOT = 'root';
//added by czw,2020-01-06,区分是否是国内云环境，0否、1是
const CHINA_CLOUD = 0; 

//Dclient 6000 版本支持的远程访问的固件开头
const SUPPORT_REMOTE_ACCESS_DEVICE_FIRMWARES = array('18.','105.','116.','101.','102.','103.','104.','105.','106.','92.','112.','115.','117.','110.','111.','12.','116.','21.','221.','80.','81.','82.','83.','20.','220.','26.','226.','27.','227.','28.','29.','48.','49.','915.','916.','933.','17.','119.');

const DIS_OEM_TYPE_AKUVOX = 0;
const DIS_OEM_TYPE_HAGER = 1;

function TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(LOG_FILE, $Now." ".$content, FILE_APPEND);
	@file_put_contents(LOG_FILE, "\n", FILE_APPEND);
}

?>
