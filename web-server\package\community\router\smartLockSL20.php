<?php

namespace package\community\router;

use framework\BasicRouter;

class smartLockSL20 extends BasicRouter
{
    public function exec()
    {
        $this->setRouterName('v3', 'app', 'community', 'smartLockSL20', 'getLinkDeviceList')
            ->setMethod('GET')
            ->addParams()
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('smartLockSL20\\getLinkDeviceListForApp');

        return $this->getRouters();
    }
}