<?php
/*
 * @Description: 验证码登陆
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-10 10:55:48
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;

class CLoginCodeCheck implements IMiddleware {
    public function handle(\Closure $next) {
        global $cMessage,$cLog;
        $params = ["MobileNumber"=>"","Code"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $mobile = $params["MobileNumber"];
        $code = $params["Code"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#loginCodeCheck#mobile={$mobile};code={$code}");
        
        $data = $db->querySList("select Account from PersonalAccount where MobileNumber = :MobileNumber",[":MobileNumber"=>$mobile]);
        if(count($data) == 0) $cMessage->echoErrorMsg(StateMobileNumberNotExist);
        $account = $data[0]["Account"];
        //ip是否被限制
        $ip = \util\computed\getIp();;
        $limit = \util\computed\getLimitIp($account,$ip);
        if($limit) $cMessage->echoErrorMsg(StateAccountIncorrect2,['time'=>$limit]);
        
        $data = $db->querySList("select * from VerificationCode where Account=:Account and Code = :Code and CreateTime >:CreateTime",[":Account"=>$account,":Code"=>$code,":CreateTime"=>date("Y-m-d H:i:s",time()-CODEVAILDTIME)]);
        if(count($data) == 0) {
            include_once __DIR__."/../util/computed.php";
            // TODO记录IP和次数
            $ip = \util\computed\getIp();;
            list($times,$number) = \util\computed\recordAccountIp($account,$ip);

            if($times === false) {
                $num = $number >= 3 ? (5 - $number) : -1;
                $cMessage->echoErrorMsg(StateCodeIncorrect);
            }else{
                $cMessage->echoErrorMsg(StateCodeIncorrect,['time'=>$times]);
            }
        }

        // 验证通过后删除这个验证码
        $db->delete2ListWKey("VerificationCode","Account",$account);

        \util\computed\setGAppData([
            'Account'=>$account,
        ]);
        $next();
    }
}