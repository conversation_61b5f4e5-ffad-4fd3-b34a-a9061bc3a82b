<?php

namespace package\bm\model\subscription\src;

use package\bm\model\subscription\config\Code;

trait Operate
{

    /**
     * @description:订阅到期结束
     * @param: {string} BmSubscriptionNumber 计费系统订单号
     * @author: shoubin.chen 2023-12-26 15:44:11 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-26 15:44:11 v6.7.1
     */
    public function operateEndSubscription()
    {
        $params = ['BmSubscriptionNumber:string-required'];
        list($bmSubscriptionNumber) = $this->getParams($params);
        $subscription = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber)[0];

        //订阅到期
        $update = [
            'Status' => SUBSCRIBE_STATUS['expired'],
            'EndReason' => SUBSCRIPTION_END_REASON_ARRAY[3],
            'UUID' => $subscription['UUID'],
        ];
        $this->dao->subscriptionList->update($update, 'UUID');
        $this->log->debug("End Subscription.Subscription_UUID = {UUID}", ["UUID" => $subscription['UUID']]);
    }

    /**
     * @description:取消订阅，通知发送邮件
     * @param: {string} BmSubscriptionNumber bm订单号
     * @param: {string} Type 取消订阅的类型，0是在三方平台取消自动扣费，1是扣费失败取消订阅。（分别对应SubscriptionList表的EndReason的15和16）
     * @author: shoubin.chen 2023-12-27 10:15:40 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-27 10:15:40 v6.7.1
     */
    public function operateCancelSubscription()
    {
        $params = ['BmSubscriptionNumber:string-required', 'Type:enum(0,1)'];
        list($bmSubscriptionNumber, $type) = $this->getParams($params);
        $subscription = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber)[0];

        if (intval($subscription['Status']) === SUBSCRIBE_STATUS['cancel']) {
            return true;
        }

        //只有订阅中状态的订阅才可以取消
        if (intval($subscription['Status']) !== SUBSCRIBE_STATUS['active']) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_IS_NOT_ACTIVE]);
        }

        $endReason = $type === 0 ? SUBSCRIPTION_END_REASON_ARRAY[16] : SUBSCRIPTION_END_REASON_ARRAY[15];

        //取消订阅
        $update = [
            'Status' => SUBSCRIBE_STATUS['cancel'],
            'EndReason' => $endReason,
            'UUID' => $subscription['UUID'],
        ];
        $this->dao->subscriptionList->update($update, 'UUID');

        //调用应用后台发送取消订阅的邮件
        $this->notifyEvent->collect(
            $subscription['UUID'] . '_AutoPayNotify',
            'AutoPayNotify',
            [$subscription['UUID'], SUBSCRIBE_SEND_EMAIL_TYPE['cancel']]
        );

        $this->log->debug("Cancel Subscription.Send email, Subscription_UUID = {UUID},Status = {Status}", ["UUID" => $subscription['UUID'], "Status" => SUBSCRIBE_SEND_EMAIL_TYPE['cancel']]);
    }

    /**
     * @description:订阅支付成功，生成一条支付成功的订单记录
     * @param: {string} BmSubscriptionNumber bm订阅号
     * @param: {string} BmOrderNumber bm订单号
     * @param: {string} Price 实际付费价格
     * @author: shoubin.chen 2023-12-27 10:08:58 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-27 10:08:58 v6.7.1
     */
    public function operateCreateSuccess()
    {
        $params = ['BmSubscriptionNumber:string-required', 'BmOrderNumber:string-required', 'Price:string', 'NextPayTime:or-rule("string","string-empty")', 'PayPlatOrder:string'];
        list($bmSubscriptionNumber, $bmOrderNumber, $price, $nextPayTime, $payPlatOrder) = $this->getParams($params);
        //查询原订阅信息
        $subscription = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber)[0];
        $now = $this->share->util->getNow();
        //更新下次付费时间和上次付费时间
        $this->dao->subscriptionList->update(['NextPayTime' => $nextPayTime, 'UUID' => $subscription['UUID'], 'LastPayTime' => $now], 'UUID');

        $subscriptionList = $this->dao->subscriptionEndUserList->selectByKey('SubscriptionUUID', $subscription['UUID']);
        foreach ($subscriptionList as $item) {
            if ($item['Type'] == SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew']) {
                $this->dao->videoStorage->update(['PersonalAccountUUID' => $item['SiteUUID'], 'IsPaid' => 1], 'PersonalAccountUUID');
            } elseif ($item['Type'] == SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew']) {
                $this->dao->videoStorage->update(['AccountUUID' => $item['SiteUUID'], 'IsPaid' => 1], 'AccountUUID');
            }
        }

        //生成一条支付成功的订单记录
        $order = [
            'Status' => ORDER_STATUS_TYPE['systemProcessing'],
            'BmOrderNumber' => $bmOrderNumber,
            'TotalPrice' => $price,
            'PayPlatOrder' => $payPlatOrder
        ];

        $this->db->begin();
        list($orderId, $orderNumber) = $this->createOrderItem($subscription, $order);

        // 插入待处理任务表
        $taskId = $this->dao->toBeDealOrder->insert(['OrderNumber' => $orderNumber, 'CreateTime' => $this->share->util->getNow(), 'Status' => 0]);

        //调用应用后台发送支付完成的邮件
        $this->notifyEvent->collect(
            $subscription['UUID'] . '_AutoPayNotify',
            'AutoPayNotify',
            [$subscription['UUID'], SUBSCRIBE_SEND_EMAIL_TYPE['payCompleted']]
        );

        if (intval($subscription['Type']) == SUBSCRIBE_TYPE['rentManagerRenew']) {
            //获取订阅的RentManagerCustomerUUIDList
            $rentManagerSubscriptionList = $this->dao->rentManagerSubscriptionList->selectByKey('SubscriptionUUID', $subscription['UUID'], 'RentManagerCustomerUUID');
            $rentManagerCustomerUUIDList = array_column($rentManagerSubscriptionList, 'RentManagerCustomerUUID');

            // 如果当前订单中涉及的RentManagerCustomer符合过期后再重新续费的，需要发送邮件给运营人员
            $this->loadUtil('rentManager', true);
            $rentManagerCustomerInfos = $this->utils->_common->rentManager->getRentManagerCustomerByArray([['UUID',$rentManagerCustomerUUIDList]]);
            $isNeedSendEmail = false;
            foreach ($rentManagerCustomerInfos as $customerInfo) {
                if (empty($customerInfo['ExpiredTime']) || (strtotime($customerInfo['ExpiredTime']) < strtotime($now))) {
                    $isNeedSendEmail = true;
                    break;
                }
            }

            $this->log->debug("Is need to send email to admin for rentManager renew subscription, isNeedSendEmail = {isNeedSendEmail}", ["isNeedSendEmail" => $isNeedSendEmail]);
            if ($isNeedSendEmail) {
                // 发送邮件
                $this->notifyEvent->collect(
                    $orderId . '_RentEnableNotify',
                    'RentEnableNotify',
                    [$orderId, SUBSCRIBE_SEND_EMAIL_TYPE['payCompleted']]
                );
            }
        }

        $this->db->commit();

        //调用webtask前先commit db，否则webtask先执行，会导致查不到数据
        $this->loadProvider('webTaskNotify');
        $this->services->webTaskNotify->sendDealOrderTask($taskId);
    }

    /**
     * @description:订阅支付成功，生成一条支付成功的订单记录
     * @param: {string} BmSubscriptionNumber bm订阅号
     * @param: {string} BmOrderNumber bm订单号
     * @param: {string} Price 实际付费价格
     * @author: shoubin.chen 2023-12-27 10:08:58 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-27 10:08:58 v6.7.1
     */
    public function operateCreateFail()
    {
        $params = ['BmSubscriptionNumber:string-required', 'BmOrderNumber:string-required', 'Price:string'];
        list($bmSubscriptionNumber, $bmOrderNumber, $price) = $this->getParams($params);
        //查询原订阅信息
        $subscription = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber)[0];

        //查询是否已经存在支付失败的订单了
        $array = [
            ['Status', ORDER_STATUS_TYPE['payFailed']],
            ['SubscriptionUUID', $subscription['UUID']]
        ];
        $result = $this->dao->orderList->selectByArray($array);
        if (count($result) > 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_FAIL_ORDER_ALREADY_EXIST]);
        }

        //生成一条支付失败的订单记录
        $order = [
            'Status' => ORDER_STATUS_TYPE['payFailed'],
            'BmOrderNumber' => $bmOrderNumber,
            'TotalPrice' => $price
        ];

        $this->createOrderItem($subscription, $order);

        //调用应用后台发送支付失败的邮件
        $this->notifyEvent->collect(
            $subscription['UUID'] . '_AutoPayNotify',
            'AutoPayNotify',
            [$subscription['UUID'], SUBSCRIBE_SEND_EMAIL_TYPE['failed']]
        );
    }

    /**
     * @description:创建一条订单记录
     * @param array $subscription 订阅记录
     * @param array $order 订单信息，必须包括Status、BmOrderNumber、Type
     * @author: shoubin.chen 2023-12-26 17:43:33 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-26 17:43:33 v6.7.1
     */
    private function createOrderItem(array $subscription, array $order)
    {
        $subscriptionType = $subscription['Type'];
        if (intval($subscriptionType) == SUBSCRIBE_TYPE['rentManagerRenew']) {
            // rentManager订单创建流程
            return $this->createRentManagerOrderItem($subscription, $order);

        } else {
            // 社区订单流程
            return $this->createCommunityOrderItem($subscription, $order);
        }
    }

    private function createRentManagerOrderItem(array $subscription, array $order)
    {
        $this->loadUtil('account', true);
        $this->loadUtil('order', true);

        $installerInfo = $this->utils->_common->account->getManagerInfoUUID($subscription['InsUUID']);
        $payPlatOrder = isset($order['PayPlatOrder']) ? $order['PayPlatOrder'] : '';

        //获取订阅的RentManagerCustomerUUIDList
        $rentManagerSubscriptionList = $this->dao->rentManagerSubscriptionList->selectByKey('SubscriptionUUID', $subscription['UUID'], 'RentManagerCustomerUUID');
        $rentManagerCustomerUUIDList = array_column($rentManagerSubscriptionList, 'RentManagerCustomerUUID');
        $this->loadUtil('rentManager', true);
        $rentManagerCustomerInfos = $this->utils->_common->rentManager->getRentManagerCustomerByArray([['UUID',$rentManagerCustomerUUIDList]]);
        foreach ($rentManagerCustomerInfos as &$customerInfo) {
            $customerInfo['MonthlyFee'] = intval($order['TotalPrice']) / count($rentManagerCustomerUUIDList);
        }
        unset($customerInfo);

        $orderParams = [
            'PayerId' => $installerInfo['ID'], //INS 的 id
            'Token' => $subscription['WebHookToken'],
            'TotalPrice' => $order['TotalPrice'],
            'Payer' => $installerInfo['Account'],
            'PayerType' => $subscription['PayerType'],
            'AreaManageID' => $installerInfo['ParentID'],
            'Months' => 1, //自动订阅是一个月一个月续费的，所以月数传1
            'BmOrderNumber' =>  $order['BmOrderNumber'],
            'IsBatch' => $subscription['IsBatch'],
            'PayCode' => $subscription['PayCode'],
            'SubscriptionUUID' => $subscription['UUID'],
            'PaypalOrder' => $payPlatOrder,
            'RentManagerCustomerInfoList' => $rentManagerCustomerInfos,
            'Status' => $order['Status'],
            'MonthlyFee' => $this->share->util->outputComputedCount($order['TotalPrice'])
        ];

        return $this->utils->_common->order->createRentManagerOrder($orderParams);
    }

    private function createCommunityOrderItem(array $subscription, array $order)
    {
        $bmOrderNumber = $order['BmOrderNumber'];
        $status = $order['Status'];
        $totalPrice = $order['TotalPrice'];
        $payPlatOrder = isset($order['PayPlatOrder']) ? $order['PayPlatOrder'] : '';
        $this->loadUtil('account', true);
        $this->loadUtil('order', true);

        $payerData = $this->utils->_common->account->getManagerInfoUUID($subscription['PayerUUID']);
        $subscriptionList = $this->dao->subscriptionEndUserList->selectByKey('SubscriptionUUID', $subscription['UUID']);

        $endUser = $videoSite = $videoIDs = $lockInfo = $thirdLockUUID = [];
        foreach ($subscriptionList as $item) {
            if ($item['Type'] == SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew']) {
                $videoIDs[] = $this->utils->_common->account->personalAccountSelectByKey('UUID', $item['SiteUUID'], 'ID')[0]['ID'];
                $videoSite[] = $item;
            } elseif ($item['Type'] == SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew']) {
                $videoIDs[] = $this->utils->_common->account->accountSelectByKey('UUID', $item['SiteUUID'], 'ID')[0]['ID'];
                $videoSite[] = $item;
            } elseif (in_array($item['Type'], [SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'], SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew']])) {
                $thirdLockUUID[] = $item['LockUUID'];
                $lockInfo[] = $item;
            } else {
                $endUser[] = $item;
            }
        }

        $videoStorageInfo = $userChargeList = $orderMixTypeArr = $thirdLockInfo = [];
        // 获取
        if (!empty($endUser)){
            $userCharge = array_column($endUser,'ChargeData');
            $userChargeList = [];
            $users = [];
            foreach ($userCharge as $item){
                $decodeItem = json_decode($item,true);
                $userChargeList[] = $decodeItem;
                $users[] = $decodeItem['ID'];
            }

            $usersInfo = $this->utils->_common->order->getSubscribeUserInfo($users);
        }

        $mixTypeArr = $this->share->util->getBitPositions($subscription['MixType']);
        //自动续费，统一选上第9位
        $orderMixTypeArr[] = PAY_TYPE['autoRenew'];

        foreach ($mixTypeArr as $type) {
            if ($type == SUBSCRIBE_TYPE['singleRenew']) {
                $projectType = PAY_TYPE_SINGLE;
                $orderMixTypeArr[] = PAY_TYPE['landline'];
            } elseif ($type == SUBSCRIBE_TYPE['communityRenew']) {
                $projectType = PAY_TYPE_MULTIPLE;
                $orderMixTypeArr[] = PAY_TYPE['renewToDay'];
            } elseif ($type == SUBSCRIBE_TYPE['officeRenew']) {
                $orderMixTypeArr[] = PAY_TYPE['renewToMonth'];
                $projectType = PAY_TYPE_OFFICE;
            } elseif ($type == SUBSCRIBE_TYPE['singleVideoStorageRenew']) {
                $projectType = PAY_TYPE_SINGLE;
                $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoIDs, PAY_TYPE_SINGLE);
                $orderMixTypeArr[] = PAY_TYPE['singleVideoStorage'];
            } elseif ($type == SUBSCRIBE_TYPE['communityVideoStorageRenew']) {
                $projectType = PAY_TYPE_MULTIPLE;
                $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoIDs, PAY_TYPE_MULTIPLE);
                $orderMixTypeArr[] = PAY_TYPE['communityVideoStorage'];
            } elseif ($type == SUBSCRIBE_TYPE['rentManagerRenew']) {
                $projectType = PAY_TYPE_MULTIPLE;
                $orderMixTypeArr[] = PAY_TYPE['rentManagerIntegration'];
            } elseif ($type == SUBSCRIBE_TYPE['singleThirdLockRenew']) {
                $projectType = PAY_TYPE_SINGLE;
                $thirdLockInfo = $this->utils->_common->order->getSubscribeThirdLockInfo($thirdLockUUID, $projectType);
                $orderMixTypeArr[] = PAY_TYPE['singleThirdLockRenew'];
            } elseif ($type == SUBSCRIBE_TYPE['communityThirdLockRenew']) {
                $projectType = PAY_TYPE_MULTIPLE;
                $thirdLockInfo = $this->utils->_common->order->getSubscribeThirdLockInfo($thirdLockUUID, $projectType);
                $orderMixTypeArr[] = PAY_TYPE['communityThirdLockRenew'];
            }
        }
        $orderMixTypeArr = array_unique($orderMixTypeArr);
        $orderMixType = $this->share->util->getDecimalFromBits($orderMixTypeArr);

        //订单的InstallID，应该是单个项目的是项目ID，多个项目的是InsID
        if ($subscription['ProjectUUID'] === null || $subscription['ProjectUUID'] === ''){
            $installerId = $this->utils->_common->account->getManagerInfoUUID($subscription['InsUUID'])['ID'];
        }else{
            $installerId = $this->utils->_common->account->getManagerInfoUUID($subscription['ProjectUUID'])['ID'];
        }

        //ins角色支付下判断实际支付人
        if (intval($subscription['PayerType']) == 2) {
            if ($subscription['ProjectUUID'] != null && $subscription['ProjectUUID'] !== '') {
                //办公和社区非混合支付的payerId是项目ID
                $payerData = $this->utils->_common->account->getManagerInfoUUID($subscription['ProjectUUID']);
            } else {
                //社区混合支付的payerId是项目ID
                $payerData = $this->utils->_common->account->getManagerInfoUUID($subscription['InsUUID']);
            }
        }

        $insertData = [
            'payerId' => $payerData['ID'],
            'token' => $subscription['WebHookToken'],
            'totalPrice' => $totalPrice / 100,
            'payType' => PAY_TYPE['autoRenew'],
            'payerType' => $subscription['PayerType'],
            'installerId' => $installerId,
            'isBatch' => $subscription['IsBatch'],
            'bmOrderNumber' => $bmOrderNumber,
            'code' => $subscription['PayCode'],
            'userCharge' => $userChargeList,
            'usersInfo' => $usersInfo['all'],
            'NextTime' => $subscription['NextPayTime'],
            'Status' => $status,
            'Discount' => $subscription['Discount'],
            'PayPlatform' => $subscription['PayPlatform'],
            'SubscriptionUUID' => $subscription['UUID'],
            'PaypalOrder' => $payPlatOrder,
            'videoStorages' => $videoStorageInfo,
            'videoCharge' => $videoSite,
            'thirdLockCharge' => $lockInfo,
            'ThirdLocks' => $thirdLockInfo,
            'projectType' => $projectType,
            'MixType' => $orderMixType
        ];

        return $this->utils->_common->order->createOrder($insertData);
    }

    /**
     * @description: 计费系统创建订阅记录成功回调更新订阅信息
     * @param {string} UUID 订阅记录UUID
     * @param {string} BmSubscriptionNumber 计费系统订阅号
     * @param {string} PayPlatUserID 支付平台用户ID
     * @param {string} PayPlatform 支付平台
     * @param {string} StartTime 开始时间
     * @param {string} Status 状态
     * @param {string} NextPayTime 下次支付时间
     * @return void
     * @throws \Exception
     * @author: csc 2023/12/30 11:21 V6.7.1
     * @lastEditors: csc 2023/12/30 11:21 V6.7.1
     */
    public function operateSubscriptionCreateSuccess()
    {
        $params = ['BmSubscriptionNumber:string-required', 'PayPlatUserID:string', 'PayPlatform:enum("0","1")',
            'StartTime?:string', 'Status:enum("1")', 'NextPayTime:time'];
        list($bmSubscriptionNumber, $payPlatUserID, $payPlatform, $startTime, $status, $nextPayTime) = $this->getParams($params);
        //查询原订阅信息
        $subscription = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber)[0];

        //判断订阅状态是否正常
        switch ($subscription['Status']) {
            case SUBSCRIBE_STATUS['active']:
                $this->log->debug("Subscription is already pay success, do nothing");
                return;
            case SUBSCRIBE_STATUS['failed']:
                $this->log->debug("Subscription is already pay failed");
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_ALREADY_FAILED]);
            case SUBSCRIBE_STATUS['timeout']:
                $this->log->debug("Subscription is already timeout");
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_ALREADY_TIMEOUT]);
            case SUBSCRIBE_STATUS['exception']:
                $this->log->debug("Subscription is already exception");
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_ALREADY_EXCEPTION]);
            case SUBSCRIBE_STATUS['cancel']:
                $this->log->debug("Subscription is already cancel");
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_ALREADY_CANCEL]);
            case SUBSCRIBE_STATUS['expired']:
                $this->log->debug("Subscription is already expired");
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_ALREADY_EXPIRED]);
        }

        //更新订阅状态
        $this->dao->subscriptionList->update(
            [
                'Status' => $status,
                'PayPlatform' => $payPlatform,
                'StartTime' => $startTime,
                'NextPayTime' => $nextPayTime,
                'UUID' => $subscription['UUID'],
            ], 'UUID'
        );

        //如果是新建的用户，需要进行记录
        if ($payPlatUserID) {
            //查询付款用户的stripe帐号
            $subscriptionUser = $this->dao->subscriptionUsers->selectByPayerUUID($subscription['PayerUUID']);
            //如果原始是没有对应用户，则需要记录
            if (empty($subscriptionUser)) {
                $uuid = $this->share->util->uuid();
                $this->dao->subscriptionUsers->insert([
                    'PayPlatform' => $payPlatform,
                    'PayPlatID' => $payPlatUserID,
                    'Email' => $subscription['PayerEmail'],
                    'PayerUUID' => $subscription['PayerUUID'],
                    'UUID' => $uuid,
                ]);
            }
        }

        //调用应用后台发送订阅成功的邮件
        $this->notifyEvent->collect(
            $subscription['UUID'] . '_AutoPayNotify',
            'AutoPayNotify',
            [$subscription['UUID'], SUBSCRIBE_SEND_EMAIL_TYPE['active']]
        );
    }
}
