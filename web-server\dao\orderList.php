<?php

namespace dao;
use framework\BasicDao;
use function util\string\uuid;

class OrderList extends BasicDao
{
    //当前表名
    public $table = 'OrderList';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';


    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function insert(array $data = [])
    {
        if(empty($data['UUID'])){
            $data['UUID'] = \share\util\uuid();
        }
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }
    
    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据OrderNumber的值查询对应数据
     * @param {string} $ordernumber OrderNumber的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByOrderNumber($ordernumber, $fields = '*')
    {
        return $this->selectByKey('OrderNumber', $ordernumber, $fields);
    }

    /**
     * @description: 根据OrderNumber的值查询对应数据
     * @param {array} $ordernumbers OrderNumber的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByOrderNumberWArray($ordernumbers, $fields = '*')
    {
        return $this->selectByKeyWArray('OrderNumber', $ordernumbers, $fields);
    }

    /*
     *@description 为导出excel查询数据
     *<AUTHOR> 2023-05-15 16:36:24 V6.6.0
     *@lastEditor kxl 2023-05-15 16:36:24 V6.6.0
     *@param {*} condition ['Begin'=>开始时间, 'End'=>结束时间, 'Status'=>订单状态, 'DisId'=>disid, 'InsId'=>insId, 'Type'=>订单类型]，注意这里的dis和ins不能作为付款者来过滤
     *@param {*} offset=0
     *@return 
     */
    public function selectForExport($condition, $offset=0)
    {
        $begin = $condition['Begin'];
        $end = $condition['End'];
        $status = $condition['Status'];
        $dis = $condition['DisId'];
        $ins = $condition['InsId'];
        $typeWhere = $condition['TypeWhere'];

        $where = '';
        $bindArray = [];
        if ($begin !== '') {
            $where .= ' and CreateTime > :Begin';
            $bindArray[':Begin'] = $begin;
        }
        if ($end !== '') {
            $where .= ' and CreateTime < :End';
            $bindArray[':End'] = $end;
        }
        if ($status !== 'all') {
            $where .= ' and Status = :Status';
            $bindArray[':Status'] = $status;
        }
        if ($dis !== 'all') {
            $where .= ' and AreaManageID = :AreaManageID';
            $bindArray[':AreaManageID'] = $dis;
        }
        if ($ins !== 'all') {
            $ids = implode(',', $ins);
            $where .= " and InstallID in ( $ids )";
        }
        $where .= $typeWhere;

        $data = $this->execute(
            "select ID,OrderNumber,TotalPrice,Discount,Type,Payer,PayerType,CreateTime,Status,InstallID,CouponCount,
       FinalPrice,AccountID,AreaManageID,ProjectType,MixType,UUID,PayPlatform,CreditCount,CreditUnitPrice from OrderList where ID is not null $where limit $offset,1000",
            $bindArray
        );
        foreach ($data as &$value) {
            $endData = $this->execute(
                'select ProjectName,AppID,SiteUUID from OrderEndUserList where OrderID = :OrderID And Type Not In(11,12)',
                [':OrderID' => $value['ID']]
            );
            $batchProject = implode(',', array_unique(array_column($endData, 'ProjectName')));
            $value['batchProject'] = $batchProject;
            $value['AppFee'] = $this->share->util->outputComputedCount(
                $this->share->util->computedDiscount($value["TotalPrice"], $value["Discount"])
            );
            $value['VideoStorageFee'] = 0;

            //直接查询从表是否包含视频存储
            $array = [
                ['Type', [PAY_SUB_TYPE['singleVideoStorage'], PAY_SUB_TYPE['communityVideoStorage']]],
                ['OrderID', $value['ID']]
            ];
            $videos = $this->dao->orderEndUserList->selectByArray($array);
            if (!empty($videos)) {
                $allVideoFee = 0;
                foreach ($videos as $item) {
                    $allVideoFee += $this->share->util->outputComputedCount(
                        $this->share->util->computedDiscount($item["Amount"], $item["Discount"])
                    );
                }
                $value['VideoStorageFee'] = $allVideoFee;
                $value['AppFee'] = $value['AppFee'] - $allVideoFee;
            }
            //查询从表是否包含三方锁
            $value['ThirdLockFee'] = 0;
            $array = [
                ['OrderID', $value['ID']]
            ];
            $thirdLocks = $this->dao->orderThirdLockList->selectByArray($array);
            if (!empty($thirdLocks)) {
                $allThirdLockFee = 0;
                foreach ($thirdLocks as $item) {
                    $allThirdLockFee += $this->share->util->outputComputedCount($item["Amount"]);
                }
                $value['ThirdLockFee'] = $allThirdLockFee;
                $value['AppFee'] = $value['AppFee'] - $allThirdLockFee;
            }
            //查询从表是否包含额外app
            $value['AddtionalAppFee'] = 0;
            $array = [
                ['Type', PAY_SUB_TYPE['buyOutApp']],
                ['OrderID', $value['ID']]
            ];
            $additionApps = $this->dao->orderEndUserList->selectByArray($array);
            if (!empty($additionApps)) {
                $allAppFee = 0;
                foreach ($additionApps as $item) {
                    $allAppFee += $this->share->util->outputComputedCount(
                        $this->share->util->computedDiscount($item["Amount"], $item["Discount"])
                    );
                }
                $value['AddtionalAppFee'] = $allAppFee;
                $value['AppFee'] = $value['AppFee'] - $allAppFee;
            }
            $newOfficeItems = $this->dao->orderListOffice->selectByKey("OrderListUUID", $value['UUID']);
            $this->log->debug("order data:{order} ", ['order' => $value]);

            // credit 支付的时候，需要计算实际的credit价值产生的单价，在OrderList表里的CreditUnitPrice
            $creditUnitPrice = $this->share->util->outputComputedCount($value['CreditUnitPrice']);
            if(!empty($newOfficeItems)){
                $doorFee = 0;
                $personnelAppFee = 0;
                $adminFee = 0;
                $premiumPlanFee = 0;

                $officeNames = [];
                $platformType = intval($value['PayPlatform']);
                foreach ($newOfficeItems as $item) {
                    $officeNames[] = $item['AccountLocation'];
                    $unitPrice = $creditUnitPrice;
                    if($platformType != ORDER_PAY_PLATFORM_CREDIT){
                        // 非credit支付的时候，OrderListOffice表里的CreditUnitPrice为实际的单价
                        $unitPrice = $this->share->util->outputComputedCount($item["CreditUnitPrice"]);
                    }
                    $totalCredit = $this->share->util->outputComputedCount($item['TotalCredit']);
                    $price = $this->share->util->roundToTwoDecimal($unitPrice * $totalCredit);

                    $serviceType = intval($item['ServiceType']);
                    if($serviceType == PAY_SUB_NEW_OFFICE_TYPE['door']){
                        $doorFee += $price;
                    }else if($serviceType == PAY_SUB_NEW_OFFICE_TYPE['personnelApp']) {
                        $personnelAppFee += $price;
                    }else if ($serviceType == PAY_SUB_NEW_OFFICE_TYPE['adminApp']) {
                        $adminFee += $price;
                    }else if ($serviceType == PAY_SUB_NEW_OFFICE_TYPE['premiumPlan']) {
                        $premiumPlanFee += $price;
                    }
                }
                if ($platformType != ORDER_PAY_PLATFORM_CREDIT){
                    // 非credit支付的时候，不展示credit数量
                    $value['CreditCount'] = 0;
                }else{
                    $value['CreditCount'] = $this->share->util->outputComputedCount($value['CreditCount']);
                }
                $this->log->debug("add office platform:{platformType} credit:{totalCredit}  doorFee:{doorFee},
                 personnelAppFee:{personnelAppFee}, adminFee:{adminFee}, premiumPlanFee:{premiumPlanFee} ",[
                 "doorFee" => $doorFee,
                 "personnelAppFee" => $personnelAppFee,
                 "adminFee" => $adminFee,
                 "premiumPlanFee" => $premiumPlanFee,
                 "platformType" => $platformType,
                 "totalCredit" =>  $value['CreditCount'],
                ]);
                $value['DoorFee'] = $this->share->util->roundToTwoDecimal($doorFee);
                $value['PersonnelAppFee'] = $this->share->util->roundToTwoDecimal($personnelAppFee);
                $value['AdminFee'] = $this->share->util->roundToTwoDecimal($adminFee);
                $value['PremiumPlanFee'] = $this->share->util->roundToTwoDecimal($premiumPlanFee);
                $value['AppFee'] = 0;
                $value['batchProject'] = implode(',', array_unique($officeNames));
            }
            $mixTypeArr = $this->share->util->getBitPositions($value['MixType']);
            if (!empty($mixTypeArr) && $mixTypeArr[0] === PAY_TYPE['credit']) {
                $value['AppFee'] = 0;
                $value['CreditCount'] = $this->share->util->outputComputedCount($value['CreditCount']);
            }
        }
        unset($value);

        return $data;
    }

    public function getNewOfficeOrderNum($projectUUID)
    {
        $sql = "select count(1) from OrderList where Status = 1 and UUID in (select OrderListUUID from OrderListOffice where AccountUUID = :ProjectUUID)";
        $data = $this->execute($sql, [':ProjectUUID' => $projectUUID]);
        return $data[0]['count(1)'];
    }
}