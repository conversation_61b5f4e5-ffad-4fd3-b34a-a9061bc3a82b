<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";


include_once __DIR__."/../basic/user.php";
class CMacLibrary
{
    public function queryCom($id, $type)
    {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $columns = ["1"=>"MngID","2"=>"PerMngID"][$type];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        
        $where = "";
        $bindArray = [];
        switch ($serchKey) {
            case 'MAC':
                $where = "$where AND D.MAC LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }
        $sql = "select D.*,B.Account as Installer,C.Account as Distributor from DeviceForRegister D left join Account A on D.PerMngID = A.ID left join Account B on A.ManageGroup = B.ID left join Account C on C.ID = D.MngID where D.ID is not null $where";
        if ($id) {
            $sql = "select D.*,B.Account as Installer from DeviceForRegister D left join Account A on D.PerMngID = A.ID left join Account B on A.ManageGroup = B.ID where D.$columns = :MngID $where";
            $bindArray[":MngID"] = $id;
        }
        $total = count($this->db->querySList($sql, $bindArray));

        $sql .= " order by ID desc limit $offset,$rows";

        $data = $this->db->querySList($sql, $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data"=>["total"=>$total,"detail"=>$data,"row"=>$data]]);
    }

    public function queryForSup()
    {
        $params = [
            "ID"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->queryCom($id, 1);
    }

    public function queryForArea()
    {
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $this->queryCom($userId, 1);
    }

    public function queryForInstall()
    {
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $this->queryCom($userId, 2);
    }

    

    public function addCom($id)
    {
        global $cMessage;
        $params = [
            "MAC"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $this->log->actionLog("#model#macLibrary#addCom#id=$id;mac=$mac");
        $data = $this->db->queryAllList("DeviceForRegister", ["equation"=>[":MAC"=>$mac]]);
        if (count($data) != 0) {
            $cMessage->echoErrorMsg(StateMacExits);
        }
        
        $this->services["rps"]->addMap([$mac], $id);
    }

    /**
     * @msg: 超级管理员添加到mac library
     * @services: rps(在addCom中所引用)
     */
    public function addForSup()
    {
        $params = [
            "ID"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->addCom($id);
    }

    /**
     * @msg: 区域管理员添加到mac library
     * @services: rps(在addCom中所引用)
     */
    public function addArea()
    {
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $this->addCom($userId);
    }

    /**
     * @msg: installer添加到mac library
     * @services: rps(在importPerDevice中所引用)
     */
    public function addPC()
    {
        $params = [
            "userAliasId"=>"",
            "userAlias"=>"",
            "MAC"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $parentID = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0]["ParentID"];
        $user = $params["userAlias"];
        $mac = $params["MAC"];
        $this->importPerDevice($mac, $parentID, $userId);
    }

    public function importPerDevice($mac, $parentID, $userId)
    {
        // 导入个人设备
        //验证rps上mac地址
        global $cMessage;
        $this->log->actionLog("#model#macLibrary#importPerDevice#areamngID=$parentID;permngID=$userId;mac=$mac");
        $testMac = $this->services["rps"]->testMAC([$mac]);
            
        if (count($testMac) != 0 || strtoupper($mac) == '0C1105000000') {
            $cMessage->echoErrorMsg(StateRedirectedOnRPS);
        }
        $data = $this->db->queryAllList("DeviceForRegister", ["equation"=>[":MAC"=>$mac,":MngID"=>$parentID]]);
        
        $res = $this->services["rps"]->modifyMap(["mac"=>[$mac],
                        "type"=>2,
                        "mngID"=>$parentID,
                        "permngID"=>$userId]);
        if (!$res) {
            $cMessage->echoErrorMsg(StateChcekMacExits);
        }

        $macs = [$mac];
        //添加到rps
        $res = $this->services["rps"]->addRPSDevice($macs);
        // 添加RPS失败
        if (!$res) {
            $cMessage->echoErrorMsg(StateRedirectedOnRPS);
        }
    }

    /**
     * @msg: 删除mac library，installer是清空自己的数据
     * @services: rps
     */
    public function delete()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        if (is_string($id)) {
            $id = [$id];
        }
        $this->log->actionLog("#model#macLibrary#delete#userId=$userId;id=$id");
        
        $ids = [];
        $bindArray = [];
        foreach ($id as $key => $val) {
            array_push($ids, ":ID$key");
            $bindArray[":ID$key"] = $val;
        }
        $ids = implode(",", $ids);
        $simtArray = $this->db->querySList("select MAC from DeviceForRegister where ID in ($ids)", $bindArray);
        $mac = [];
        $where = "";
        foreach ($simtArray as $key => $val) {
            array_push($mac, $val["MAC"]);
            if ($key == 0) {
                $where .= "MAC = "."'".$val["MAC"]."'";
            } else {
                $where .= "OR MAC = "."'".$val["MAC"]."'";
            }
        }
        $grade = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0]["Grade"];
        if ($where !== "") {
            $simtArray = $this->db->querySList("select MAC from PersonalDevices where $where union select MAC from Devices where $where");
            
            if (count($simtArray) != 0) {
                $tmpMACs = [];
                foreach ($simtArray as $value) {
                    array_push($tmpMACs, $value["MAC"]);
                }
                $cMessage->echoErrorMsg(StateUnbindMACUser, [], [implode(";", $tmpMACs)]);
            }
            if ($grade == SUPERGRADE || $grade == AREAGRADE) {
                $simtArray = $this->db->querySList("select MAC from DeviceForRegister where ($where) and PerMngID != 0");
                
                if (count($simtArray) != 0) {
                    $tmpMACs = [];
                    foreach ($simtArray as $value) {
                        array_push($tmpMACs, $value["MAC"]);
                    }
                    $this->services["rps"]->deleteRps($tmpMACs);
                }
            }
        }

        if ($grade == SUPERGRADE || $grade == AREAGRADE) {
            $this->services["rps"]->deleteMap($id);
        } else {
            $this->services["rps"]->deleteRps($mac);
            $this->services["rps"]->modifyMap(["mac"=>$mac,"type"=>6]);
        }
    }

    /**
     * @msg: 上传CSV文件，解析其中数据
     */
    private function uploadFileMac()
    {
        global $cMessage;
        $file = fopen($_FILES['Devices']['tmp_name'], 'r');
        //遍历文件行
        while ($data = fgetcsv($file)) {
            $fileList[] = $data;
        }
        $this->log->actionLog("#model#macLibrary#fileList=".json_encode($fileList));
        //获取键值
        $keys = $fileList[0];
        unset($fileList[0]);
        $macIndex = "";
        //获取下标
        foreach ($keys as $key=>$value) {
            if ($value == "MAC") {
                $macIndex = $key;
            }
        }

        if ($macIndex === "") {
            $cMessage->echoErrorMsg(StateInvalidFile);
        }

        $macInvalid = [];//收集无效mac
        $mac = [];
        
        foreach ($fileList as $value) {
            if (!\util\string\checkMAC($value[$macIndex])) {
                array_push($mac, $value[$macIndex]);
            } else {
                array_push($macInvalid, $value[$macIndex]);
            }
        }

        if (count($mac) == 0) {
            $cMessage->echoErrorMsg(StateImportFailMACExit, [], [implode(",", $macInvalid)]);
        }
        $this->log->actionLog("#model#macLibrary#mac=".json_encode($mac));
        return [$mac,$macInvalid];
    }

    /**
     * @msg: 上传CSV文件，在deviceForRegister指定installer
     * @services: rps
     */
    public function uploadFilePCMng()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $myData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]]);
        list($mac, $macInvalid) = $this->uploadFileMac();
        $testMac = $this->services["rps"]->testMAC($mac);
        foreach ($testMac as $value) {
            array_push($macInvalid, $value["MAC"]);
            foreach ($mac as $key=>$value1) {
                if ($value1 == $value["MAC"]) {
                    unset($mac[$key]);
                }
            }
        }
        $macInvalid = array_unique($macInvalid);
        if (count($macInvalid) !== 0) {
            $cMessage->echoErrorMsg(StateImportFailMACExit, [], [implode(",", $macInvalid)]);
        }
        $mac = array_unique($mac);
        foreach ($mac as $val) {
            $this->importPerDevice($val, $myData[0]["ParentID"], $userId);
        }
    }

    private function uploadFileSAMng($id)
    {
        global $cMessage;
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        list($mac, $macInvalid) = $this->uploadFileMac();
        foreach ($mac as $value) {
            if ($this->services["rps"]->testMap($value) !== false) {
                array_push($macInvalid, $value);
            }
        }
        if (count($macInvalid) !== 0) {
            $cMessage->echoErrorMsg(StateImportFailMACExit, [], [implode(",", $macInvalid)]);
        }
        $mac = array_unique($mac);
        $this->services["rps"]->addMap($mac, $id);
    }

    /**
     * @msg: 上传CSV文件
     * @services: rps(uploadFileSAMng)
     */
    public function uploadFileSup()
    {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->uploadFileSAMng($id);
    }

    /**
     * @msg: 上传CSV文件
     * @services: rps(uploadFileSAMng)
     */
    public function uploadFileArea()
    {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $this->uploadFileSAMng($userId);
    }
}
