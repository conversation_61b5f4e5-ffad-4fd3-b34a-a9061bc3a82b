<?php
namespace package\common\model\order\src;

use package\common\model\order\config\Code;
trait Get
{

    /**
     * @description:super/dis获取order订单列表
     * @author:lwj 2022-12-14 18:13 18:13:29 V6.6
     * @lastEditor:lwj 2022-12-14 18:13:29 V6.6
     * @param:
     * @return array
     */
    public function getListForSuperAndDis()
    {
        $params = [
            PROXY_ROLE['distributorId'], 'Installer', 'Community', 'Type:payment.type', 'Status', 'Key',
            'BeginTime', 'EndTime', 'TimeZone', 'CustomizeForm', PROXY_ROLE['subDistributorId'], PROXY_ROLE['subDistributorUUID']
        ];
        list($distributorId, $installer, $community, $type, $status, $key, $beginTime, $endTime, $timeZone, $customizeForm, $subDisId, $subDisUUID)
            = $this->getParams($params);
        list($offset, $rows) = $this->getParamsLimitAndSearch();

        $this->loadUtil('account', true);

        $where = "";
        if (!empty($subDisId)) {
            $where = 'where AccountID = :AccountID and IsDelete = 0 and OrderNumber like :Key and PayerType = :PayerType ';
            $bindArray = [':AccountID' => $subDisId, ':PayerType' => PAYER_TYPE_SUB_DIS, ':Key' => "%$key%"];
        } elseif (!empty($distributorId)){
            $where = 'where AccountID = :AccountID and IsDelete = 0 and OrderNumber like :Key and PayerType = :PayerType ';
            $bindArray = [':AccountID' => $distributorId, ':PayerType' => PAYER_TYPE_DIS, ':Key' => "%$key%"];
        }else{
            //付款人记录的是Account，PM展示和搜索条件需要替换成LoginAccount
            $loginAccountFlag = false;
            if (!empty($key)) {
                $managerList = $this->utils->_common->account->getManagerListByArray([['LoginAccount', $key, '%']]);
                $payerAccounts = array_filter(array_column($managerList, 'Account'));
                if (!empty($payerAccounts)) {
                    list($payerAccounts, $payerAccountsBindArray) = $this->share->util->getImplodeData($payerAccounts, 'Payer');
                    $where = "where (OrderNumber like :Key or Payer like :Key or Payer in ($payerAccounts))";
                    $bindArray = [':Key' => "%$key%"];
                    $bindArray = array_merge($bindArray, $payerAccountsBindArray);
                    $loginAccountFlag = true;
                }
            }

            if (!$loginAccountFlag) {
                $where = "where (OrderNumber like :Key or Payer like :Key)";
                $bindArray = [':Key' => "%$key%"];
            }
        }

        if ($beginTime) {
            $beginTime = $this->share->util->setTimeZone($beginTime, $timeZone, "", "-");
            $where .= " and CreateTime >= :BeginTime";
            $bindArray[":BeginTime"] = $beginTime;
        }

        if ($endTime) {
            $endTime = $this->share->util->setTimeZone($endTime, $timeZone, "", "-");
            $where .= " and CreateTime <= :EndTime";
            $bindArray[":EndTime"] = $endTime;
        }

        $typeArr = explode(',', $type);
        $typeWhere = [];
        foreach ($typeArr as $type) {
            if ($type !== 'all' && $type !== null) {
                //获得MixType
                if ($type == PAYMENT_SEARCH_TYPE['AutoRenewVideoStorage']) {
                    // 包含自动续费,且必须存在视频存储(10,11)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 9)) != 0 OR (MixType & (1 << 10)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewApp']) {
                    // 包含自动续费，且必须包含(2,4,6,8)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 1)) != 0 OR (MixType & (1 << 3)) != 0 OR (MixType & (1 << 7)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewRentManager']) {
                    // 包含自动续费，且必须包含rentmanage(12)
                    $tmpWhere = "SubscriptionUUID != '' And (MixType & (1 << 11)) != 0";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewThirdLock']) {
                    // 包含自动续费，且必须包含三方锁(14,15,16,17)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 13)) != 0 OR (MixType & (1 << 14)) != 0 OR (MixType & (1 << 15)) != 0 OR (MixType & (1 << 16)) != 0)";
                }  else {
                    // 排除自动续费
                    $tmpWhere = "SubscriptionUUID = ''";
                }
                //获得MixType
                $mixType = $this->utils->self->getOrderListType($type);
                $tmpWhere .= $this->utils->self->getMixTypeSqlQuery($mixType);
                $typeWhere[] = '(' . $tmpWhere . ')';
            }
        }
        if (!empty($typeWhere)) {
            $where .= ' and (' . implode(' or ', $typeWhere) . ')';
        }

        if ($status !== 'all' && $status !== null) {
            $where .= ' and Status = :Status';
            $bindArray[':Status'] = $status;
        }

        $orderListTable = PROXY_TABLES['orderList'];
        $orderEndUserListTable = PROXY_TABLES['orderEndUserList'];
        $thirdLockListTable = PROXY_TABLES['orderThirdLockList'];
        $accountTable = PROXY_TABLES['account'];
        $orderListOfficeTable = PROXY_TABLES['orderListOffice'];

        if ($installer != "all" && $installer !== null){
            if($community === 'all'){
                $manages = $this->db->querySList(
                    "select ID from $accountTable where ManageGroup = :ManageGroup",
                    [':ManageGroup' => $installer]
                );
                $manageId = array_column($manages, 'ID');
                if (count($manages) !== 0) {
                    $manageId = implode(',', $manageId);
                    $where .= " and InstallID in ($manageId)";
                } else {
                    $where .= ' and 1=0';
                }
            }else{
                // V6.5.0 兼容批量支付小区检索包括ins,dis和super
                $this->loadUtil('account', true);
                $manageInfo = $this->utils->_common->account->getManagerInfo($community);
                $orderIds = $this->db->querySList(
                    "select OrderID from $orderEndUserListTable where ProjectUUID = :ProjectUUID",
                    [':ProjectUUID' => $manageInfo['UUID']]
                );
                $thirdLockOrderIds = $this->db->querySList(
                    "select OrderID from $thirdLockListTable where ProjectUUID = :ProjectUUID",
                    [':ProjectUUID' => $manageInfo['UUID']]
                );
                // 新办公订单
                $orderUuids = $this->db->querySList(
                    "select OrderListUUID from $orderListOfficeTable where AccountUUID = :ProjectUUID",
                    [':ProjectUUID' => $manageInfo['UUID']]
                );
                $orderUuids = array_column($orderUuids, 'OrderListUUID');
                $orderIds = array_merge($orderIds, $thirdLockOrderIds);
                if (count($orderIds) > 0) {
                    $orderIds = implode(',', array_unique(array_column($orderIds, 'OrderID')));
                    if(count($orderUuids) > 0){
                        $orderUuids = $this->share->util->implodeWithQuotation($orderUuids);
                        $where .= " and (InstallID = :InstallID or ID in ($orderIds) or UUID in($orderUuids))";
                    }else{
                        $where .= " and (InstallID = :InstallID or ID in ($orderIds))";
                    }
                } else if(count($orderUuids) > 0){
                    $orderUuids = $this->share->util->implodeWithQuotation($orderUuids);
                    $where .= " and (InstallID = :InstallID or UUID in($orderUuids))";
                } else {
                    $where .= ' and InstallID = :InstallID';
                }
                $bindArray[':InstallID'] = $community;
            }
        }

        $total = $this->db->querySList(
            "select count(*) from $orderListTable $where",
            $bindArray
        )[0]['count(*)'];
        $orderDatas = $this->db->querySList(
            "select * from $orderListTable $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        $statusMsgList = $this->utils->self->getStatusMsgList();
        $payTypeMsgList = $this->utils->self->getPayTypeMsgList();
        $payerMsgList = $this->utils->self->getPayerMsgList();
        $chargeMode = CHARGEMODE_NORMAL;
        if(!empty($subDisUUID)) {
            // subDis需要判断支付权限
            $chargeMode = $this->dao->account->getChargeModeByUuid($subDisUUID);
        }
        foreach ($orderDatas as &$value) {
            $projectType = intval($value['ProjectType']);
            $value['Projects'] = '';
            if ($projectType === ORDER_PROJECT_TYPE_NEW_OFFICE){
                // 新办公
                $newOfficeItems = $this->db->querySList(
                    "select AccountLocation from $orderListOfficeTable where OrderListUUID = :OrderListUUID",
                    [':OrderListUUID' => $value['UUID']]
                );
                $newOfficeNames = array_unique(array_column($newOfficeItems, 'AccountLocation'));
                if(count($newOfficeNames) > 0){
                    $value['Projects'] = implode(',', $newOfficeNames);
                }
            }else{
                $endData = $this->db->querySList(
                    "select ID,ProjectUUID from $thirdLockListTable where OrderID = :OrderID union all 
                 select ID,ProjectUUID from $orderEndUserListTable where OrderID = :OrderID",
                    [':OrderID' => $value['ID']]
                );

                $projectUUID = array_unique(array_column($endData, 'ProjectUUID'));
                $count = count($projectUUID);
                if ($count > 0) {
                    list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($projectUUID);
                    $projects = [];
                    if(!empty($uuidBindStr) && !empty($uuidBindArray)){
                        $projects = $this->db->querySList(
                            "select Location from $accountTable where UUID in ($uuidBindStr)",
                            $uuidBindArray
                        );
                    }
                    $value['Projects'] = implode(',', array_column($projects, 'Location'));
                }
            }

            $value['AptNumber'] = count($endData);
            $value['Installer'] = '';
            $value['Community'] = '';
            $pcMngData = $this->db->querySList(
                "select Account,Grade,ManageGroup,Location from $accountTable where ID = :ID",
                [':ID' => $value['InstallID']]
            );
            if (count($pcMngData) !== 0) {
                $pcMngData = $pcMngData[0];
                $value['Community'] = $pcMngData['Location'];
                $value["Installer"] = $this->db->querySList(
                    "select Account from $accountTable where ID = :ID",
                    [":ID" => $pcMngData["ManageGroup"]]
                )[0]["Account"];
            }
            $value['Projects'] = $value['Projects'] === '' ? $value['Community'] : $value['Projects'];
            if ($value['PayerType'] === strval(PAYER_TYPE_INS)) {
                $insInfo = $this->db->querySList(
                    "select A.Account,A.ID from $accountTable A
                 join $accountTable B on A.ID=B.ManageGroup where B.ID=:ID",
                    [':ID' => $value['AccountID']]
                )[0];
                $value['Payer'] = $insInfo['Account'];
                //单住户不需要展示project
                if(($value['InstallID'] === $insInfo['ID'] && intval($value['IsBatch']) === 0) || intval($value['ProjectType']) === 1){
                    $value['Projects'] = $value['Community'] = '';
                }
            }
            // 单住户的项目展示''
            if ($value['PayerType'] === strval(PAYER_TYPE_DIS) || $value['PayerType'] === strval(PAYER_TYPE_SUB_DIS)) {
                $insInfo = $this->utils->_common->account->accountSelectByArray([['ID', $value['InstallID']], ['ManageGroup', '= ID', 'origin']], 'ID,Account')[0];
                if(($value['InstallID'] === $insInfo['ID'] && intval($value['IsBatch']) === 0) || intval($value['ProjectType']) === 1){
                    $value['Projects'] = $value['Community'] = '';
                }
            }
            //PM订单展示成LoginAccount
            if ($value['PayerType'] === strval(PAYER_TYPE_PM)) {
                $value['Payer'] = $this->utils->_common->account->getManagerListByArray([['Account', $value['Payer']]])[0]['LoginAccount'];
            }
            $this->loadUtil('order', true);
            $mixTypeArr = $this->share->util->getBitPositions($value['MixType']);
            $value['TypeStr'] = $this->utils->_common->order->getPaymentMixTypeStr($mixTypeArr, $value['SubscriptionUUID']);

            $platform = intval($value['PayPlatform']);
            if ($platform === ORDER_PAY_PLATFORM_CREDIT){
                // credit支付时，列表显示价格为0
                $value['TotalPriceNum'] = 0;
                $value['TotalPrice'] = '0';
                $value['FinalPrice'] = '';
            }else{
                $disCount = $this->share->util->computedDiscount($value['FinalPrice'], $value['Discount']);
                $value['TotalPriceNum'] = $this->share->util->outputComputedCount($disCount);
                $value['TotalPrice'] = '$' . $value['TotalPriceNum'];
            }
            $value['StatusEnum'] = $value['Status'];
            $value['Status'] = $statusMsgList[$value['Status']];
            $value['Type'] = $value['Type'] === strval(PAY_TYPE['landline']) ? PAY_TYPE['renewToMonth'] : $value['Type'];
            $value['TypeEnum'] = $value['Type'];
            $value['Type'] = $payTypeMsgList[$value['Type']];
            $value['PayerTypeName'] = $payerMsgList[$value['PayerType']];

            if($chargeMode === CHARGEMODE_CREDIT){
                $value['bmurl'] = BMCREDITPAYURL . '?order=' . $value['BmOrderNumber'] . '&token=' . $value['WebHookToken'] . '&code=' . $value['PayCode'];
            }else{
                $value['bmurl'] = BMAPYURL . '?order=' . $value['BmOrderNumber'] . '&token=' . $value['WebHookToken'] . '&code=' . $value['PayCode'];
            }
        }
        $orderDatas = $this->share->util->setQueryTimeZone($orderDatas, $timeZone, $customizeForm);
        return [
            'data' => ['total' => $total, 'row' => $orderDatas, 'detail' => $orderDatas]
        ];
    }

    /**
     * @description:order订单详情
     * @author:lwj 2022-12-14 18:13 18:13:29 V6.6
     * @lastEditor:lwj 2022-12-14 18:13:29 V6.6
     * @param:
     * @return array
     */
    public function getInfo()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm', PROXY_ROLE['distributorId'], PROXY_ROLE['subDistributorId']];
        list($id, $timeZone, $customizeForm, $disId, $subDisId) = $this->getParams($params);
        if (!empty($subDisId)) {
            $isValidOrder = $this->utils->self->checkOrderIdInManage($id, $subDisId);
        } elseif (!empty($disId)) {
            $isValidOrder = $this->utils->self->checkOrderIdInManage($id, $disId);
        }
        if ($isValidOrder === false) {
            return ['data' => []];
        }
        $orderData = $this->utils->self->getInfo($id, $timeZone, $customizeForm);
        return ['data' => $orderData];
    }

    public function getReceiptPdf()
    {
        $params = ['ID', PROXY_ROLE['superId'], PROXY_ROLE['subSuperId'], PROXY_ROLE['distributorId'], PROXY_ROLE['subDistributorId'], PROXY_ROLE['projectId'], PROXY_ROLE['pmId']];
        list($id, $superId, $subSuperId, $disId, $subDisId, $projectId, $pmId) = $this->getParams($params);

        $userId = null;
        $payerId = [];
        if ($this->share->util->isPropertyManager()) {
            $userId = $pmId;
            $payerId = [$userId];
        }else if ($this->share->util->isDisManager()) {
            $userId = $disId;
            $payerId = [$userId];
        }else if ($this->share->util->isSubDisManager()) {
            $userId = $subDisId;
            $payerId = [$userId];
        }else if ($this->share->util->isSuperManager()) {
            $userId = $superId;
            $payerId = [$superId];
        }else if ($this->share->util->isSubSuperManager()){
            $userId = $subSuperId;
            $payerId = [$subSuperId];
        }else {
            $userId = $projectId;
            $this->loadUtil('manage', true);
            $projects = $this->utils->_common->manage->getAllProjectByProjId($projectId);
            $payerId = array_column($projects, 'ID');
        }

        // ID传进来可能是UUID，这里先用uuid查一下
        $orderListData = $this->dao->orderList->selectByKey('UUID', $id);
        if(!empty($orderListData)){
            $id = $orderListData[0]['ID'];
        }

        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getManagerInfo($userId);
        $timeZone = $userData['TimeZone'];
        $customizeForm = $userData['CustomizeForm'];
        $orderData = $this->utils->self->getInfo($id, $timeZone, $customizeForm);

        if ($this->share->util->isSuperManager() || $this->share->util->isSubSuperManager()) {
            $userId = $orderData['AccountID'];
        }

        // 除了super，其他角色只能下载支付成功订单的发票
        if(!$this->share->util->isSuperManager() && intval($orderData['StatusEnum']) !== 1) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_ORDER_NOT_COMPLETE]);
        }

        // 权限检验，除了super或者subSuper，只能下载自己的发票
        if(!($this->share->util->isSuperManager() || $this->share->util->isSubSuperManager()) && !in_array($orderData['AccountID'], $payerId)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_ORDER_PAY_ID_NO_EQ_USER]);
        }

        $this->loadUtil('billingInfo', true);
        $data = $this->utils->_common->billingInfo->getManagerBillingInfo($userId);
        $receiptData['TotalPrice'] = $orderData['OriginalPrice'];
        $receiptData['Time'] = $orderData['CreateTime'];
        $receiptData['OrderNumber'] = $orderData['OrderNumber'];
        $receiptData['BuyerInfo'] = $data['Name'];
        $receiptData['BillingTitle'] = $data['BillingTitle'];
        $receiptData['Attn'] = $data['Contactor'];
        $receiptData['Addr'] = $data['Street'] . ' ' . $data['Postcode'] . ' ' . $data['City'];
        $receiptData['Tel'] = $data['TelePhone'];
        $receiptData['Fax'] = $data['Fax'];
        $receiptData['CountryCode'] = $data['Country'];
        $receiptData['PlatformType'] = $orderData['PayPlatform'];

        $optimizeActiveTypeArray = [PAY_TYPE['active'], PAY_TYPE['buyOutApp'], PAY_TYPE['singleThirdLockActive'],
            PAY_TYPE['communityThirdLockActive'], PAY_TYPE['activeDoor'], PAY_TYPE['activePersonnelApp'],
            PAY_TYPE['activeAdminApp'], PAY_TYPE['activePremiumPlan']];
        $optimizeRenewTypeArray = [PAY_TYPE['renewToMonth'], PAY_TYPE['landline'], PAY_TYPE['renewToDay'],
            PAY_TYPE['autoRenew'], PAY_TYPE['singleVideoStorage'], PAY_TYPE['communityVideoStorage'],
            PAY_TYPE['singleThirdLockRenew'], PAY_TYPE['communityThirdLockRenew'], PAY_TYPE['renewDoor'], PAY_TYPE['renewPersonnelApp'],
            PAY_TYPE['renewAdminApp'], PAY_TYPE['renewPremiumPlan']];

        $children = $orderData['Children'];
        $childrenThirdLock = $orderData['ChildrenThirdLock'];
        $childrenNewOffice = $orderData['NewOfficeChildren'];

        $projectUUidList = array_column($children, 'ProjectUUID');
        $thirdLockProjectUUidList = array_column($childrenThirdLock, 'ProjectUUID');
        $newOfficeProjectUUidList = array_unique(array_column($childrenNewOffice, 'AccountUUID'));

        $projectUUidList = array_merge($projectUUidList, $thirdLockProjectUUidList);
        $projectUUidList = array_merge($projectUUidList, $newOfficeProjectUUidList);
        $projectUUidList = array_unique($projectUUidList);

        $tableInfo = [];

        $insInfo  = $this->utils->_common->account->accountSelectByArray([['ID',$orderData['InstallID']], ['ManageGroup', '= ID', 'origin']])[0];
        $isIns = !empty($insInfo);
        $isSingleFamily = $orderData['ProjectType'] == '1' || ($orderData['IsBatch'] == '0' && $isIns);

        $platformType = intval($orderData['PayPlatform']);
        if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
            $receiptData['TotalPrice'] = $orderData['CreditCount'];
        }
        //判断是混合激活还是混合续费
        $isMixActive = $isMixRenew = false;
        if (intval($orderData['TypeEnum']) === PAY_TYPE['mix']) {
            if (array_intersect($orderData['MixTypeArr'], $optimizeActiveTypeArray)) {
                $isMixActive = true;
            } else if (array_intersect($orderData['MixTypeArr'], $optimizeRenewTypeArray)) {
                $isMixRenew = true;
            }
        }

//        $this->log->debug("getreceipt order data:{order} newoffice item:{items} project uuids:{uuids} isActive:$isMixActive
//         isRenew:$isMixRenew isSingleFamily:$isSingleFamily", [
//             "order"=>$orderData,
//            "items" => $childrenNewOffice,
//            "uuids" => $projectUUidList,
//        ]);
        if (!empty($orderData['MixTypeArr']) && intval($orderData['MixTypeArr'][0]) === PAY_TYPE['credit']){
            // credit发票
            $temp = [];
            $temp['CreditCount'] = $orderData['CreditCount'];
            $tableInfo[] = $temp;
            $receiptData['TableInfo'] = $tableInfo;
            $this->callSelfFunc('downLoadActiveReceipt', [$receiptData]);
        }else if (in_array(intval($orderData['TypeEnum']), $optimizeActiveTypeArray) or $isMixActive) {
            // 激活类型发票
            if (!$isSingleFamily) {
                // 非单住户， 根据项目显示
                foreach ($projectUUidList as $projectUUID) {
                    $userPrice = 0;
                    $userQty = 0;
                    $pmPrice = 0;
                    $pmQty = 0;
                    $additionalPrice = 0;
                    $additionalQty = 0;
                    $thirdLockPrice = 0;
                    $thirdLockQty = 0;
                    $projectName = "";
                    $doorQty = 0;
                    $doorPrice = 0;
                    $personnelAppQty = 0;
                    $personnelAppPrice = 0;
                    $adminAppQty = 0;
                    $adminAppPrice = 0;
                    $attendancePremiumQty = 0;
                    $attendancePremiumPrice = 0;
                    foreach ($children as $item) {
                        if ($item['ProjectUUID'] == $projectUUID) {
                            if ($item['Type'] == '1') {
                                $userQty ++;
                                $userPrice = $userPrice + $this->share->util->inputComputedCount($item['Amount']);
                            } elseif ($item['Type'] == '3') {
                                $additionalQty ++;
                                $additionalPrice = $additionalPrice + $this->share->util->inputComputedCount($item['Amount']);
                            } elseif ($item['Type'] == '9') {
                                $pmQty ++;
                                $pmPrice = $pmPrice + $this->share->util->inputComputedCount($item['Amount']);
                            }

                            $projectName = $item['ProjectName'];
                        }
                    }
                    foreach ($childrenThirdLock as $item) {
                        if ($item['ProjectUUID'] == $projectUUID) {
                            if ($item['Type'] == PAY_SUB_LOCK_TYPE['communityThirdLockActive']) {
                                $thirdLockPrice += $this->share->util->inputComputedCount($item['Amount']);
                                $thirdLockQty++;
                            }

                            $projectName = $item['ProjectName'];
                        }
                    }
                    foreach ($childrenNewOffice as $item) {
                        if ($item['AccountUUID'] == $projectUUID) {
                            $price = $item['Price'];
                            $serviceType = intval($item['Type']);
                            if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['door']){
                                $doorQty++;
                                $doorPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['personnelApp']){
                                $personnelAppQty++;
                                $personnelAppPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['adminApp']){
                                $adminAppQty++;
                                $adminAppPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['premiumPlan']){
                                $attendancePremiumQty++;
                                $attendancePremiumPrice += $price;
                            }

                            $projectName = $item['OfficeName'];
                        }
                    }
                    $temp = [];
                    $temp['ProjectName'] = $projectName;
                    $temp['UserPrice'] = $this->share->util->outputComputedCount($userPrice);
                    $temp['UserQty'] = $userQty;
                    $temp['PmPrice'] = $this->share->util->outputComputedCount($pmPrice);
                    $temp['PmQty'] = $pmQty;
                    $temp['AdditionalPrice'] = $this->share->util->outputComputedCount($additionalPrice);
                    $temp['AdditionalQty'] = $additionalQty;
                    $temp['ThirdLockPrice'] = $this->share->util->outputComputedCount($thirdLockPrice);
                    $temp['ThirdLockQty'] = $thirdLockQty;
                    $temp['DoorQty'] = $doorQty;
                    $temp['DoorPrice'] = $doorPrice;
                    $temp['PersonnelAppQty'] = $personnelAppQty;
                    $temp['PersonnelAppPrice'] = $personnelAppPrice;
                    $temp['AdminAppQty'] = $adminAppQty;
                    $temp['AdminAppPrice'] = $adminAppPrice;
                    $temp['AttendancePremiumQty'] = $attendancePremiumQty;
                    $temp['AttendancePremiumPrice'] = $attendancePremiumPrice;
                    $tableInfo [] = $temp;
                }
            } else {
                // 单住户， 根据单住户名称显示
                $userPrice = 0;
                $userQty = 0;
                $pmPrice = 0;
                $pmQty = 0;
                $additionalPrice = 0;
                $additionalQty = 0;
                $thirdLockPrice = 0;
                $thirdLockQty = 0;
                foreach ($children as $item) {
                    if ($item['Type'] == '1') {
                        $userQty ++;
                        $userPrice = $userPrice + $this->share->util->inputComputedCount($item['Amount']);
                    } elseif ($item['Type'] == '3') {
                        $additionalQty ++;
                        $additionalPrice = $additionalPrice + $this->share->util->inputComputedCount($item['Amount']);
                    } elseif ($item['Type'] == '9') {
                        $pmQty ++;
                        $pmPrice = $pmPrice + $this->share->util->inputComputedCount($item['Amount']);
                    }
                }
                foreach ($childrenThirdLock as $item) {
                    if ($item['Type'] == PAY_SUB_LOCK_TYPE['singleThirdLockActive']) {
                        $thirdLockPrice += $this->share->util->inputComputedCount($item['Amount']);
                        $thirdLockQty++;
                    }
                }
                // 单住户不显示项目名
                $temp['ProjectName'] = "";
                $temp['UserPrice'] = $this->share->util->outputComputedCount($userPrice);
                $temp['UserQty'] = $userQty;
                $temp['PmPrice'] = $this->share->util->outputComputedCount($pmPrice);
                $temp['PmQty'] = $pmQty;
                $temp['AdditionalPrice'] = $this->share->util->outputComputedCount($additionalPrice);
                $temp['AdditionalQty'] = $additionalQty;
                $temp['ThirdLockPrice'] = $this->share->util->outputComputedCount($thirdLockPrice);
                $temp['ThirdLockQty'] = $thirdLockQty;
                $tableInfo [] = $temp;
            }
            $receiptData['TableInfo'] = $tableInfo;
            $this->callSelfFunc('downLoadActiveReceipt', [$receiptData]);
        } else if (in_array(intval($orderData['TypeEnum']), $optimizeRenewTypeArray) or $isMixRenew) {
            // 续费类型发票
            if (!$isSingleFamily) {
                // 非单住户， 根据项目显示
                foreach ($projectUUidList as $projectUUID) {
                    $userPrice = 0;
                    $userQty = 0;
                    $pmPrice = 0;
                    $pmQty = 0;
                    $videoStoragePrice = 0;
                    $videoStorageQty = 0;
                    $thirdLockPrice = 0;
                    $thirdLockQty = 0;
                    $projectName = "";
                    $doorQty = 0;
                    $doorPrice = 0;
                    $personnelAppQty = 0;
                    $personnelAppPrice = 0;
                    $adminAppQty = 0;
                    $adminAppPrice = 0;
                    $attendancePremiumQty = 0;
                    $attendancePremiumPrice = 0;
                    foreach ($children as $item) {
                        if ($item['ProjectUUID'] == $projectUUID) {
                            if ($item['Type'] == '10') {
                                // PM 付费
                                $pmQty ++;
                                $pmPrice = $pmPrice + $this->share->util->inputComputedCount($item['Amount']);
                            } elseif ($item['Type'] == '11' || $item['Type'] == '12') {
                                // 视频存储付费
                                $videoStoragePrice = $videoStoragePrice + $this->share->util->inputComputedCount($item['Amount']);
                                $videoStorageQty ++;
                            } else {
                                $userQty ++;
                                $userPrice = $userPrice + $this->share->util->inputComputedCount($item['Amount']);
                            }

                            $projectName = $item['ProjectName'];
                        }
                    }
                    foreach ($childrenThirdLock as $item) {
                        if ($item['ProjectUUID'] == $projectUUID) {
                            if ($item['Type'] == PAY_SUB_LOCK_TYPE['communityThirdLockRenew']) {
                                $thirdLockPrice += $this->share->util->inputComputedCount($item['Amount']);
                                $thirdLockQty++;
                            }

                            $projectName = $item['ProjectName'];
                        }
                    }
                    foreach ($childrenNewOffice as $item) {
                        if ($item['AccountUUID'] == $projectUUID) {
                            $price = $item['Price'];

                            $serviceType = intval($item['Type']);
                            if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['door']){
                                $doorQty++;
                                $doorPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['personnelApp']){
                                $personnelAppQty++;
                                $personnelAppPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['adminApp']){
                                $adminAppQty++;
                                $adminAppPrice += $price;
                            }else if($serviceType === PAY_SUB_NEW_OFFICE_TYPE['premiumPlan']){
                                $attendancePremiumQty++;
                                $attendancePremiumPrice += $price;
                            }

                            $projectName = $item['OfficeName'];
                        }
                    }
                    $temp['ProjectName'] = $projectName;
                    $temp['UserPrice'] = $this->share->util->outputComputedCount($userPrice);
                    $temp['UserQty'] = $userQty;
                    $temp['PmPrice'] = $this->share->util->outputComputedCount($pmPrice);
                    $temp['PmQty'] = $pmQty;
                    $temp['VideoStoragePrice'] = $this->share->util->outputComputedCount($videoStoragePrice);
                    $temp['VideoStorageQty'] = $videoStorageQty;
                    $temp['ThirdLockPrice'] = $this->share->util->outputComputedCount($thirdLockPrice);
                    $temp['ThirdLockQty'] = $thirdLockQty;
                    if ($orderData['Months'] != '0') {
                        $temp['SubscriptionDurationTitle'] = "SUBSCRIPTION DURATION";
                        // 按月续费, 展示续费的月数
                        $monthWord = intval($orderData['Months']) > 1 ? ' Months' : ' Month';
                        $temp['SubscriptionDuration'] = $orderData['Months'] . $monthWord;
                    } else {
                        $temp['SubscriptionDurationTitle'] = "SUBSCRIPTION END DATA";
                        // 按天续费，展示续费到某一天
                        $temp['SubscriptionDuration'] = $orderData['NextTime'];
                    }
                    $temp['DoorQty'] = $doorQty;
                    $temp['DoorPrice'] = $doorPrice;
                    $temp['PersonnelAppQty'] = $personnelAppQty;
                    $temp['PersonnelAppPrice'] = $personnelAppPrice;
                    $temp['AdminAppQty'] = $adminAppQty;
                    $temp['AdminAppPrice'] = $adminAppPrice;
                    $temp['AttendancePremiumQty'] = $attendancePremiumQty;
                    $temp['AttendancePremiumPrice'] = $attendancePremiumPrice;
                    $tableInfo [] = $temp;
                }
            } else {
                // 单住户， 根据单住户
                $userPrice = 0;
                $userQty = 0;
                $pmPrice = 0;
                $pmQty = 0;
                $videoStoragePrice = 0;
                $videoStorageQty = 0;
                $thirdLockPrice = 0;
                $thirdLockQty = 0;
                foreach ($children as $item) {
                    if ($item['Type'] == '10') {
                        // PM 付费
                        $pmQty ++;
                        $pmPrice = $pmPrice + $this->share->util->inputComputedCount($item['Amount']);
                    } elseif ($item['Type'] == '11' || $item['Type'] == '12') {
                        // 视频存储付费
                        $videoStoragePrice = $videoStoragePrice + $this->share->util->inputComputedCount($item['Amount']);
                        $videoStorageQty ++;
                    } else {
                        $userQty ++;
                        $userPrice = $userPrice + $this->share->util->inputComputedCount($item['Amount']);
                    }
                }
                foreach ($childrenThirdLock as $item) {
                    if ($item['Type'] == PAY_SUB_LOCK_TYPE['singleThirdLockRenew']) {
                        $thirdLockPrice += $this->share->util->inputComputedCount($item['Amount']);
                        $thirdLockQty++;
                    }
                }
                // 单住户不显示项目名
                $temp['ProjectName'] = "";
                $temp['UserPrice'] = $this->share->util->outputComputedCount($userPrice);
                $temp['UserQty'] = $userQty;
                $temp['PmPrice'] = $this->share->util->outputComputedCount($pmPrice);
                $temp['PmQty'] = $pmQty;
                $temp['VideoStoragePrice'] = $this->share->util->outputComputedCount($videoStoragePrice);
                $temp['VideoStorageQty'] = $videoStorageQty;
                $temp['ThirdLockPrice'] = $this->share->util->outputComputedCount($thirdLockPrice);
                $temp['ThirdLockQty'] = $thirdLockQty;
                if ($orderData['Months'] != '0') {
                    $temp['SubscriptionDurationTitle'] = "SUBSCRIPTION DURATION";
                    // 按月续费, 展示续费的月数
                    $monthWord = intval($orderData['Months']) > 1 ? ' Months' : ' Month';
                    $temp['SubscriptionDuration'] = $orderData['Months'] . $monthWord;
                } else {
                    $temp['SubscriptionDurationTitle'] = "SUBSCRIPTION END DATA";
                    // 按天续费，展示续费到某一天
                    $temp['SubscriptionDuration'] = $orderData['NextTime'];
                }
                $tableInfo [] = $temp;
            }
            $receiptData['TableInfo'] = $tableInfo;
            $this->callSelfFunc('downLoadRenewReceipt', [$receiptData]);
        } else {
            // 其他类型，如RentManager，保持原来的发票格式不变
            $this->callSelfFunc('downOriginalReceipt', [$receiptData]);
        }
    }

    public function downLoadRenewReceipt()
    {
        $params = ['Data'];
        list($data) = $this->getParams($params);

        $orderNumber = $data['OrderNumber'];
        $time = $data['Time'];
        $buyerInfo = $data['BuyerInfo'];
        $billingTitle = $data['BillingTitle'];
        $countryCode = $data['CountryCode'];
        $attn = $data['Attn'];
        $addr = $data['Addr'];
        $tel = $data['Tel'];
        $fax = $data['Fax'];
        $totalPrice = $data['TotalPrice'];
        $platformType = intval($data['PlatformType']);
        $isNewOfficeOrder = false;
        $duration = $data['TableInfo'][0]['SubscriptionDurationTitle'];

        $rows = '';
        foreach ($data['TableInfo'] as $row) {
            $subscriptionDuration = htmlspecialchars($row["SubscriptionDuration"]);
            $projectName = htmlspecialchars($row["ProjectName"]);
            $pmPrice = htmlspecialchars($row["PmPrice"]);
            $pmQty = htmlspecialchars($row["PmQty"]);
            $userPrice = htmlspecialchars($row["UserPrice"]);
            $userQty = htmlspecialchars($row["UserQty"]);
            $videoStoragePrice = htmlspecialchars($row["VideoStoragePrice"]);
            $videoStorageQty = htmlspecialchars($row["VideoStorageQty"]);
            $thirdLockPrice = htmlspecialchars($row["ThirdLockPrice"]);
            $thirdLockQty = htmlspecialchars($row["ThirdLockQty"]);
            $doorQty = htmlspecialchars($row["DoorQty"]);
            $personnelAppQty = htmlspecialchars($row["PersonnelAppQty"]);
            $adminAppQty = htmlspecialchars($row["AdminAppQty"]);
            $attendancePremiumQty = htmlspecialchars($row["AttendancePremiumQty"]);
            $doorPrice = htmlspecialchars($row["DoorPrice"]);
            $personnelAppPrice = htmlspecialchars($row["PersonnelAppPrice"]);
            $adminAppPrice = htmlspecialchars($row["AdminAppPrice"]);
            $attendancePremiumPrice = htmlspecialchars($row["AttendancePremiumPrice"]);

            if (!empty($row["PmQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">PM App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$pmQty</td>
                    <td>US$$pmPrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["UserQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">User App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$userQty</td>
                    <td>US$$userPrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["VideoStorageQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Video Storage&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$videoStorageQty</td>
                    <td>US$$videoStoragePrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["ThirdLockQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Third Party Lock&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$thirdLockQty</td>
                    <td>US$$thirdLockPrice</td>
                </tr>
                EOD;
            }

            if(!empty($row["DoorQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$doorPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$doorPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Door&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$doorQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["PersonnelAppQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$personnelAppPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$personnelAppPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Personnel App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$personnelAppQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["AdminAppQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$adminAppPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$adminAppPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Admin App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$adminAppQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["AttendancePremiumQty"])) {
                $isNewOfficeOrder = true;
                $priceStr = "US$$attendancePremiumPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$attendancePremiumPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Premium Plan&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td style="border-left: none">$subscriptionDuration</td>
                    <td>$attendancePremiumQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }
        }

        if($isNewOfficeOrder){
            $duration = 'SUBSCRIPTION END DATA';
        }
        $totalPriceStr = "US$$totalPrice";
        if($platformType == ORDER_PAY_PLATFORM_CREDIT){
            $totalPriceStr = "$totalPrice Credit";
        }

        ini_set('memory_limit', '256M');
        require_once __DIR__.'/../../../../../share/plugin/tcpdf_min/tcpdf.php';
        $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $html = <<<EOD
            <div style="text-align:center;font-size:14px;line-height:18px;margin:0;padding:0;line-height:18px;">
                <b>
                SMART-PLUS PTE. LTD.
                </b>
            </div>
            <div style="text-align:center;font-size:10px;;margin:0;padding:0;line-height:18px;">6 RAFFLES QUAY #14-06, Singapore(048580)</div>
            <div style="text-align:center;font-size:10px;">Tel: +(65) 6717 0088 &nbsp;&nbsp;&nbsp;&nbsp; VAT No.: 202116626G &nbsp;&nbsp;&nbsp;&nbsp; Fax: +(65) 6717 0088</div>
            <div style="text-align:center;font-size:14px;line-height:18px;">
                <b>
                    INVOICE
                </b>
            </div>
            <div style="border-top: 1px solid #000;"></div>

            <table style="font-size:10px;">
                <tr style="line-height:20px;">
                    <td>INVOICE NO.:</td>
                    <td>$orderNumber</td>
                    <td>DATE:</td>
                    <td>$time</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>BUYER INFORMATION:</td>
                    <td colspan="3">$buyerInfo</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>COMPANY NAME:</td>
                    <td>$billingTitle</td>
                    <td>COUNTRY:</td>
                    <td>$countryCode</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ATTN:</td>
                    <td colspan="3">$attn</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ADD:</td>
                    <td colspan="3">$addr</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>TEL:</td>
                    <td>$tel</td>
                    <td>FAX:</td>
                    <td>$fax</td>
                </tr>
            </table>
            <br><br>
            <table style="font-size:10px;text-align:center;" border="1" cellspacing="0" cellpadding="0">
                <tr style="line-height:25px;">
                    <th><b>COMMODIFY DESCRIPTION</b></th>
                     <th><b>$duration</b></th>
                    <th><b>QTY</b></th>
                    <th><b>AMOUNT</b></th>
                </tr>
                $rows
                <tr>
                    <td style="text-align:right;"  colspan="3">TOTAL</td>
                    <td>$totalPriceStr</td>
                </tr>
            </table>
EOD;
        $pdf->SetPrintHeader(false);
        $pdf->SetFont('arialuni', '', 14, true);
        $pdf->AddPage();
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->setHtmlVSpace(
            [
                'div' => [
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1]
                ]
            ]
        );
        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->lastPage();
        //改为兼容webman下载方法
        $filePath = "/tmp/$orderNumber.pdf";
        $pdf->Output($filePath, 'F');

        $outPut = $this->share->util->getOutput();
        $headers = [
            'Content-Type' => 'application/pdf',
            'Cache-Control' => 'private, must-revalidate, post-check=0, pre-check=0, max-age=1',
            'Pragma' => 'public',
            'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s').' GMT',
            'Content-Disposition' => 'inline; filename="'.basename($filePath).'"',
        ];
        $outPut->outputFile($filePath, $headers);
    }

    public function downOriginalReceipt()
    {
        $params = ['Data'];
        list($data) = $this->getParams($params);

        $orderNumber = $data['OrderNumber'];
        $time = $data['Time'];
        $buyerInfo = $data['BuyerInfo'];
        $billingTitle = $data['BillingTitle'];
        $countryCode = $data['CountryCode'];
        $attn = $data['Attn'];
        $addr = $data['Addr'];
        $tel = $data['Tel'];
        $fax = $data['Fax'];
        $price = $data['TotalPrice'];

        ini_set('memory_limit', '256M');
        require_once __DIR__.'/../../../../../share/plugin/tcpdf_min/tcpdf.php';
        $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $html = <<<EOD
            <div style="text-align:center;font-size:14px;line-height:18px;margin:0;padding:0;line-height:18px;">
                <b>
                SMART-PLUS PTE. LTD.
                </b>
            </div>
            <div style="text-align:center;font-size:10px;;margin:0;padding:0;line-height:18px;">6 RAFFLES QUAY #14-06, Singapore(048580)</div>
            <div style="text-align:center;font-size:10px;">Tel: +(65) 6717 0088 &nbsp;&nbsp;&nbsp;&nbsp; VAT No.: 202116626G &nbsp;&nbsp;&nbsp;&nbsp; Fax: +(65) 6717 0088</div>
            <div style="text-align:center;font-size:14px;line-height:18px;">
                <b>
                    INVOICE
                </b>
            </div>
            <div style="border-top: 1px solid #000;"></div>

            <table style="font-size:10px;">
                <tr style="line-height:20px;">
                    <td>INVOICE NO.:</td>
                    <td>$orderNumber</td>
                    <td>DATE:</td>
                    <td>$time</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>BUYER INFORMATION:</td>
                    <td colspan="3">$buyerInfo</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>COMPANY NAME:</td>
                    <td>$billingTitle</td>
                    <td>COUNTRY:</td>
                    <td>$countryCode</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ATTN:</td>
                    <td colspan="3">$attn</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ADD:</td>
                    <td colspan="3">$addr</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>TEL:</td>
                    <td>$tel</td>
                    <td>FAX:</td>
                    <td>$fax</td>
                </tr>
            </table>
            <br><br>
            <table style="font-size:10px;text-align:center;" border="1" cellspacing="0" cellpadding="0">
                <tr style="line-height:25px;">
                    <td><b>COMMODIFY DESCRIPTION</b></td>
                    <td><b>AMOUNT</b></td>
                </tr>
                <tr>
                    <td>Akuvox Cloud Service Ordered</td>
                    <td>US$$price</td>
                </tr>
                <tr>
                    <td style="text-align:right;">TOTAL</td>
                    <td>US$$price</td>
                </tr>
            </table>
EOD;
        $pdf->SetPrintHeader(false);
        $pdf->SetFont('arialuni', '', 14, true);
        $pdf->AddPage();
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->setHtmlVSpace(
            [
                'div' => [
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1]
                ]
            ]
        );
        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->lastPage();
        //改为兼容webman下载方法
        $filePath = "/tmp/$orderNumber.pdf";
        $pdf->Output($filePath, 'F');

        $outPut = $this->share->util->getOutput();
        $headers = [
            'Content-Type' => 'application/pdf',
            'Cache-Control' => 'private, must-revalidate, post-check=0, pre-check=0, max-age=1',
            'Pragma' => 'public',
            'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s').' GMT',
            'Content-Disposition' => 'inline; filename="'.basename($filePath).'"',
        ];
        $outPut->outputFile($filePath, $headers);
    }

    public function downLoadActiveReceipt()
    {
        $params = ['Data'];
        list($data) = $this->getParams($params);

        $orderNumber = $data['OrderNumber'];
        $time = $data['Time'];
        $buyerInfo = $data['BuyerInfo'];
        $billingTitle = $data['BillingTitle'];
        $countryCode = $data['CountryCode'];
        $attn = $data['Attn'];
        $addr = $data['Addr'];
        $tel = $data['Tel'];
        $fax = $data['Fax'];
        $totalPrice = $data['TotalPrice'];
        $platformType = intval($data['PlatformType']);
        $isNewOfficeOrder = false;

        $totalPriceStr = "US$$totalPrice";
        if($platformType == ORDER_PAY_PLATFORM_CREDIT){
            $totalPriceStr = "$totalPrice Credit";
        }

        $rows = '';
        foreach ($data['TableInfo'] as $row) {
            $projectName = htmlspecialchars($row["ProjectName"]);
            $pmPrice = htmlspecialchars($row["PmPrice"]);
            $pmQty = htmlspecialchars($row["PmQty"]);
            $userPrice = htmlspecialchars($row["UserPrice"]);
            $userQty = htmlspecialchars($row["UserQty"]);
            $additionalPrice = htmlspecialchars($row["AdditionalPrice"]);
            $additionalQty = htmlspecialchars($row["AdditionalQty"]);
            $thirdLockPrice = htmlspecialchars($row["ThirdLockPrice"]);
            $thirdLockQty = htmlspecialchars($row["ThirdLockQty"]);
            $doorQty = htmlspecialchars($row["DoorQty"]);
            $personnelAppQty = htmlspecialchars($row["PersonnelAppQty"]);
            $adminAppQty = htmlspecialchars($row["AdminAppQty"]);
            $attendancePremiumQty = htmlspecialchars($row["AttendancePremiumQty"]);
            $doorPrice = htmlspecialchars($row["DoorPrice"]);
            $personnelAppPrice = htmlspecialchars($row["PersonnelAppPrice"]);
            $adminAppPrice = htmlspecialchars($row["AdminAppPrice"]);
            $attendancePremiumPrice = htmlspecialchars($row["AttendancePremiumPrice"]);
            $creditQty = htmlspecialchars($row["CreditCount"]);

            if (!empty($row["PmQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td>PM App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$pmQty</td>
                    <td>US$$pmPrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["UserQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td>User App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$userQty</td>
                    <td>US$$userPrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["AdditionalQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td>Additional App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$additionalQty</td>
                    <td>US$$additionalPrice</td>
                </tr>
                EOD;
            }

            if (!empty($row["ThirdLockQty"])) {
                $rows .= <<<EOD
                <tr>
                    <td>Third Party Lock&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$thirdLockQty</td>
                    <td>US$$thirdLockPrice</td>
                </tr>
                EOD;
            }

            if(!empty($row['CreditCount'])){
                // credit购买只有一项，所以价格和总价一致
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Credit</td>
                    <td>$creditQty</td>
                    <td>$totalPriceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["DoorQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$doorPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$doorPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Door&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$doorQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["PersonnelAppQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$personnelAppPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$personnelAppPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Personnel App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$personnelAppQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["AdminAppQty"])){
                $isNewOfficeOrder = true;
                $priceStr = "US$$adminAppPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$adminAppPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Admin App&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$adminAppQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }

            if(!empty($row["AttendancePremiumQty"])) {
                $isNewOfficeOrder = true;
                $priceStr = "US$$attendancePremiumPrice";
                if ($platformType == ORDER_PAY_PLATFORM_CREDIT){
                    $priceStr = "$attendancePremiumPrice Credit";
                }
                $rows .= <<<EOD
                <tr>
                    <td style="border-right: none">Premium Plan&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$projectName</td>
                    <td>$attendancePremiumQty</td>
                    <td>$priceStr</td>
                </tr>
                EOD;
            }
        }

        ini_set('memory_limit', '256M');
        require_once __DIR__.'/../../../../../share/plugin/tcpdf_min/tcpdf.php';
        $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $html = <<<EOD
            <div style="text-align:center;font-size:14px;line-height:18px;margin:0;padding:0;line-height:18px;">
                <b>
                SMART-PLUS PTE. LTD.
                </b>
            </div>
            <div style="text-align:center;font-size:10px;;margin:0;padding:0;line-height:18px;">6 RAFFLES QUAY #14-06, Singapore(048580)</div>
            <div style="text-align:center;font-size:10px;">Tel: +(65) 6717 0088 &nbsp;&nbsp;&nbsp;&nbsp; VAT No.: 202116626G &nbsp;&nbsp;&nbsp;&nbsp; Fax: +(65) 6717 0088</div>
            <div style="text-align:center;font-size:14px;line-height:18px;">
                <b>
                    INVOICE
                </b>
            </div>
            <div style="border-top: 1px solid #000;"></div>

            <table style="font-size:10px;">
                <tr style="line-height:20px;">
                    <td>INVOICE NO.:</td>
                    <td>$orderNumber</td>
                    <td>DATE:</td>
                    <td>$time</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>BUYER INFORMATION:</td>
                    <td colspan="3">$buyerInfo</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>COMPANY NAME:</td>
                    <td>$billingTitle</td>
                    <td>COUNTRY:</td>
                    <td>$countryCode</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ATTN:</td>
                    <td colspan="3">$attn</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ADD:</td>
                    <td colspan="3">$addr</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>TEL:</td>
                    <td>$tel</td>
                    <td>FAX:</td>
                    <td>$fax</td>
                </tr>
            </table>
            <br><br>
            <table style="font-size:10px;text-align:center;" border="1" cellspacing="0" cellpadding="0">
                <tr style="line-height:25px;">
                    <th><b>COMMODIFY DESCRIPTION</b></th>
                    <th><b>QTY</b></th>
                    <th><b>AMOUNT</b></th>
                </tr>
                $rows
                <tr>
                    <td style="text-align:right;"  colspan="2">TOTAL</td>
                    <td>$totalPriceStr</td>
                </tr>
            </table>
EOD;
        $pdf->SetPrintHeader(false);
        $pdf->SetFont('arialuni', '', 14, true);
        $pdf->AddPage();
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->setHtmlVSpace(
            [
                'div' => [
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1],
                    ['h' => 1]
                ]
            ]
        );
        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->lastPage();
        //改为兼容webman下载方法
        $filePath = "/tmp/$orderNumber.pdf";
        $pdf->Output($filePath, 'F');

        $outPut = $this->share->util->getOutput();
        $headers = [
            'Content-Type' => 'application/pdf',
            'Cache-Control' => 'private, must-revalidate, post-check=0, pre-check=0, max-age=1',
            'Pragma' => 'public',
            'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate('D, d M Y H:i:s').' GMT',
            'Content-Disposition' => 'inline; filename="'.basename($filePath).'"',
        ];
        $outPut->outputFile($filePath, $headers);
    }

    public function export()
    {
        $params = ['BeginTime', 'EndTime', 'Status', 'DisID', 'InstallID', 'Type:payment.type', 'userId'];
        list($begin, $end, $stauts, $disId, $insId, $type, $userId) = $this->getParams($params);

        $this->loadUtil('manage', true);
        if ($insId !== 'all') {
            $projects = $this->utils->_common->manage->getAllProjectByProjId($insId);
            $insId = array_column($projects, 'ID');
        }

        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getManagerInfo($userId);
        $timeZone = $userData['TimeZone'];
        $customizeForm = $userData['CustomizeForm'];

        $condition = [
            'Begin'=>$begin,
            'End'=>$end,
            'Status'=>$stauts,
            'DisId'=>$disId,
            'InsId'=>$insId,
        ];

        require_once __DIR__.'/../../../../../share/plugin/PHPExcel/PHPExcel.php';
        $objPHPExcel = new \PHPExcel();//实例化PHPExcel类 (相当于创建了一个excel表格)
        $objSheet = $objPHPExcel->getActiveSheet();//获取当前活动sheet的操作对象
        $objSheet->setTitle('Statistics');
        $msgText = $this->share->util->getMsg()->getMsgText();

        $statusArray = [
            $msgText['processing'],
            $msgText['successed'],
            $msgText['failed'],
            $msgText['timeOut'],
            $msgText['abnormal'],
            $msgText['cancel'],
            $msgText['systemProcessing']
        ];
        $typesArray = [
            '',
            $msgText['activation'],
            $msgText['subscription'],
            $msgText['additionalApp'],
            $msgText['subscription'],
            $msgText['featureActivationFee'],
            $msgText['featureMonthlyFee'],
            $msgText['featurePriceDifferences'],
            $msgText['subscription']
        ];
        $payerArray = [$msgText['family'], $msgText['community']];
        $payerRoleArray = [
            $msgText['endUser'],
            $msgText['propertyManager'],
            $msgText['installer'],
            $msgText['distributor'],
            $msgText['subDistributor']
        ];

        $objSheet->setCellValue('A1', 'Order Number')
            ->setCellValue('B1', 'Total Price')
            ->setCellValue('C1', 'Type')
            ->setCellValue('D1', 'Project')
            ->setCellValue('E1', 'Payer Role')
            ->setCellValue('F1', 'Payer')
            ->setCellValue('G1', 'Create Time')
            ->setCellValue('H1', 'Status')
            ->setCellValue('I1', 'Final Price')
            ->setCellValue('J1', 'Coupon')
            ->setCellValue('K1', 'Distributor')
            ->setCellValue('L1', 'Project Type')
            ->setCellValue('M1', 'App Fee')
            ->setCellValue('N1', 'Video Storage Fee')
            ->setCellValue('O1', 'Third Party Lock Fee')
            ->setCellValue('P1', 'Additional App Fee')
            ->setCellValue('Q1', 'Credit')
            ->setCellValue('R1', 'Personnel App Fee')
            ->setCellValue('S1', 'Admin App Fee')
            ->setCellValue('T1', 'Premium Plan Fee')
            ->setCellValue('U1', 'Door Fee');

        $read = true;
        $offset = 0;
        $index = 2;
        $typeArr = explode(',', $type);
        $typeWhere = [];
        foreach ($typeArr as $type) {
            if ($type !== 'all' && $type !== null) {
                //获得MixType
                if ($type == PAYMENT_SEARCH_TYPE['AutoRenewVideoStorage']) {
                    // 包含自动续费,且必须存在视频存储(10,11)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 9)) != 0 OR (MixType & (1 << 10)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewApp']) {
                    // 包含自动续费，且必须包含(2,4,6,8)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 1)) != 0 OR (MixType & (1 << 3)) != 0 OR (MixType & (1 << 7)) != 0)";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewRentManager']) {
                    // 包含自动续费，且必须包含rentmanage(12)
                    $tmpWhere = "SubscriptionUUID != '' And (MixType & (1 << 11)) != 0";
                } elseif ($type == PAYMENT_SEARCH_TYPE['AutoRenewThirdLock']) {
                    // 包含自动续费，且必须包含三方锁(14,15,16,17)
                    $tmpWhere = "SubscriptionUUID != '' And ((MixType & (1 << 13)) != 0 OR (MixType & (1 << 14)) != 0 OR (MixType & (1 << 15)) != 0 OR (MixType & (1 << 16)) != 0)";
                }  else {
                    // 排除自动续费
                    $tmpWhere = "SubscriptionUUID = ''";
                }
                //获得MixType
                $mixType = $this->utils->self->getOrderListType($type);
                $tmpWhere .= $this->utils->self->getMixTypeSqlQuery($mixType);
                $typeWhere[] = '(' . $tmpWhere . ')';
            }
        }
        $condition['TypeWhere'] = "";
        if (!empty($typeWhere)) {
            $condition['TypeWhere'] = ' and (' . implode(' or ', $typeWhere) . ')';
        }

        while ($read) {
            $data = $this->dao->orderList->selectForExport($condition, $offset);
            foreach($data as $val) {
                $orderNumber = $val['OrderNumber'] . ' ';
                $totalPrice = $this->share->util->outputComputedCount(
                    $this->share->util->computedDiscount($val["TotalPrice"], $val["Discount"])
                );
                $finalPrice = $this->share->util->outputComputedCount(
                    $this->share->util->computedDiscount($val["FinalPrice"], $val["Discount"])
                );
                $couponCount =$this->share->util->outputComputedCount($val["CouponCount"]);

                $payer = $val['Payer'];
                $payerType = $payerArray[$val['PayerType']];
                $payerRole = $payerRoleArray[$val['PayerType']];
                $createTime = $val['CreateTime'];
                $status = $statusArray[$val['Status']];

                $projectData = $this->utils->_common->account->getManagerInfo($val['InstallID']);
                $disData = $this->utils->_common->account->getManagerInfo($val['AreaManageID']);
                if (empty($projectData)) {
                    $project = '';
                } else {
                    $project = intval($projectData['Grade']) === PERSONGRADE ? '' : $projectData['Location'];
                }

                $this->loadUtil('order', true);
                $mixTypeArr = $this->share->util->getBitPositions($val['MixType']);
                $typeStr = $this->utils->_common->order->getPaymentMixTypeStr($mixTypeArr, $val['SubscriptionUUID']);

                $projectTypeMap = [
                    0 => '',//0代表旧数据
                    1 => 'Villa',//1=单住户
                    2 => 'Community',//2=社区
                    3 => 'Office',//3=办公
                    4 => "Office"//4=办公
                ];
                $projectType = $projectTypeMap[intval($val['ProjectType'])];

                $batchProject = $val['batchProject'];
                $project = $batchProject === '' ? $project : $batchProject;
                if ($val['PayerType'] === '2') {
                    $payer = $this->utils->_common->manage->getInsInfo($val['AccountID'])['LoginAccount'];
                }
                //PM订单展示成LoginAccount
                if ($val['PayerType'] === strval(PAYER_TYPE_PM)) {
                    $payer = $this->utils->_common->account->getManagerListByArray([['Account', $payer]])[0]['LoginAccount'];
                }

                $creditCount = $val['CreditCount'];
                if(empty($creditCount)) {
                    $creditCount = 0;
                }
                $personnelAppFee = $val['PersonnelAppFee'];
                if(empty($personnelAppFee)) {
                    $personnelAppFee = 0;
                }
                $adminFee = $val['AdminFee'];
                if(empty($adminFee)) {
                    $adminFee = 0;
                }
                $premiumPlanFee = $val['PremiumPlanFee'];
                if(empty($premiumPlanFee)) {
                    $premiumPlanFee = 0;
                }
                $doorFee = $val['DoorFee'];
                if(empty($doorFee)) {
                    $doorFee = 0;
                }

                $objSheet->setCellValue("A$index", $orderNumber)->setCellValue(
                    "B$index",
                    $totalPrice
                )->setCellValue(
                    "C$index",
                    $typeStr
                )->setCellValue("D$index", $project)->setCellValue("E$index", $payerRole)->setCellValue(
                    "F$index",
                    $payer
                )->setCellValue("G$index", $createTime)->setCellValue("H$index", $status)->setCellValue(
                    "I$index",
                    $finalPrice
                )->setCellValue("J$index", $couponCount)->setCellValue("K$index", $disData['Account'])
                ->setCellValue("L$index", $projectType)
                ->setCellValue("M$index", $val['AppFee'])
                ->setCellValue("N$index", $val['VideoStorageFee'])
                ->setCellValue("O$index", $val['ThirdLockFee'])
                ->setCellValue("P$index", $val['AddtionalAppFee'])
                ->setCellValue("Q$index", $creditCount)
                ->setCellValue("R$index", $personnelAppFee)
                ->setCellValue("S$index", $adminFee)
                ->setCellValue("T$index", $premiumPlanFee)
                ->setCellValue("U$index", $doorFee);
                $index++;
            }

            if (count($data) < 1000) {
                break;
            }
            $offset += 1000;
        }

        $outPut = $this->share->util->getOutput();

        $fileName = $this->share->util->getNow() . ".xlsx";
        $filePath = '/tmp/'. time() . '_' . rand(100, 999) . $fileName;
        //按照指定的格式生成excel文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        $objWriter->save($filePath);
        $headers = [
            'Content-Type' => 'application/vnd.ms-excel',
            'Content-Disposition' => 'attachment;filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];
        $outPut->output($filePath, $fileName, $headers);
    }
}