<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 14:24:47
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CEditAptPinType {
    private $aptPinType;
    function on ($id) {
        global $cLog;
        $db = \database\CDatabase::getInstance();
        $aptPinType = $db->querySList("select AptPinType from CommunityInfo where AccountID=:ID",[":ID"=>$id])[0]["AptPinType"];
        $cLog->actionLog("#event#editAptPinType.on#id=$id;aptPinType=$aptPinType");
        $this->aptPinType = $aptPinType;
    }
    function emit ($aptPinType) {
        global $cLog;
        $cLog->actionLog("#event#editAptPinType.emit#aptPinType=$aptPinType");
        if($aptPinType != $this->aptPinType) {
            include_once __DIR__."/../util/computed.php";
            \util\computed\setGAppBranch("changeAptPinType");
        }
    }
}