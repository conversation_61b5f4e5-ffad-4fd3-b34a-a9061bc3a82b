<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 11:32:58
 * @LastEditors  : kxl
 */
namespace container;
include_once __DIR__."/../interfaces/container/main.php";
include_once __DIR__."/../util/string.php";
class event implements \interfaces\container\main\eventContainer {
    private $events = [];
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    function on ($eventName,$params,$dataContainer) {
        global $gApp,$cLog;
        include_once __DIR__."/../event/$eventName.php";
        $event = "\\event\\".\util\string\getClassName($eventName);
        $event = new $event();
        $this->events[$eventName] = $event;
        if(is_string($params)) $params = [$params];

        $temParam = [];
        foreach($params as $param) array_push($temParam,$dataContainer->get($param));
        $event->on(...$temParam);
    }

    function emit ($eventName,$params,$dataContainer) {
        global $gApp;
        if(!array_key_exists($eventName,$this->events))
            throw new \Exception("Please register event:$eventName first");
        else{
            $temParam = [];
            if(is_string($params)) $params = [$params];
            foreach($params as $param) array_push($temParam,$dataContainer->get($param));
            $this->events[$eventName]->emit(...$temParam);
        }
    }
}