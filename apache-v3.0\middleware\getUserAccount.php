<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 16:59:44
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";

include_once __DIR__."/../util/computed.php";
class CGetUserAccount implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog;
        $params = ["ID"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $db = \database\CDatabase::getInstance();
        $account = $db->querySList("select Account from PersonalAccount where ID = :ID",[":ID"=>$id])[0]["Account"];
        $cLog->actionLog("#middle#getUserAccount#id=$id;account=$account");
        \util\computed\setGAppData(["Account"=>$account]);
        $next();
    }
}