<?php
/*
 * @Description: 设备是否在个人终端管理员下 参数ID
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 17:52:34
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
class CPcDevInPerMngCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $gApp,$cMessage;
        global $cLog;
        $user = $gApp["userAlias"];
        $userId = $gApp["userAliasId"];

        $params = ["ID"=>"","MAC"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $mac = $params["MAC"];

        $db = \database\CDatabase::getInstance();
        if($id) {
            $data = $db->querySList("select ID from PersonalDevices where Community = :Community and ID = :ID",[":Community"=>$user,":ID"=>$id]);
            if(!count($data)) {
                $data = $db->querySList("select ID from Devices where MngAccountID = :MngAccountID and ID = :ID",[":MngAccountID"=>$userId,":ID"=>$id]);
            }
        }
        //id 和 mac 只会传一个进来
        if($mac) {
            $data = $db->querySList("select ID from PersonalDevices where Community = :Community and MAC = :MAC",[":Community"=>$user,":MAC"=>$mac]);
            if(!count($data)) {
                $data = $db->querySList("select ID from Devices where MngAccountID = :MngAccountID and MAC = :MAC",[":MngAccountID"=>$userId,":MAC"=>$mac]);
            }
        }
        
        $cLog->actionLog("#middle#perDevInPerMngCheck#");
        if(!count($data)) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}