<?php
namespace model\order;

use function PHPSTORM_META\type;

trait update
{
    public function deleteOrder()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "userId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $myID = $params["userId"];
        $ids = $params["ID"];
        $this->log->payLog("#deleteOrder#userId=$myID;ids=$ids");
        $ids = explode(";", $ids);
        $myData = $this->db->querySList('select ManageGroup,Grade from Account where ID = :ID', [':ID' => $myID])[0];
        $myManageGroup = $myData['ManageGroup'];
        $grade = $myData['Grade'];
        
        foreach ($ids as $id) {
            $orderData = $this->db->querySList("select AccountID from OrderList where ID = :ID", [':ID'=>$id])[0];
            if (in_array($grade, [COMMUNITYGRADE, PERSONGRADE, OFFICEGRADE])) {
                $payerManageGroup = $this->db->querySList("select ManageGroup from Account where ID = :ID", [':ID'=>$orderData['AccountID']])[0]['ManageGroup'];
                if ($myManageGroup !== $payerManageGroup) {
                    $cMessage->echoErrorMsg(StateNotPermission);
                }
            } elseif ($orderData['AccountID'] !== $myID) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            
            $this->db->update2ListWID("OrderList", [":ID"=>$id,":IsDelete"=>1,":Status"=>5]);
            // 删除订单资源锁定表
            $orderNumber = $this->db->querySList("select OrderNumber from OrderList where ID = :ID", [":ID"=>$id])[0]['OrderNumber'];
            $this->db->delete2ListWKey('LockOrder', 'OrderNumber', $orderNumber);
        }
    }

    // function setDiscount () {
    //     global $cMessage;
    //     $params = [
    //         "ID"=>"",
    //         "Discount"=>""
    //     ];
    //     $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
    //     $discount = $params["Discount"];
    //     $id = $params["ID"];
    //     if($discount < 0) $cMessage->echoErrorMsg(StateNotPermission);
    //     $this->db->update2ListWID("OrderList",[":ID"=>$id,":Discount"=>$discount]);
    // }

    public function afterPay($type, $orderID)
    {
        $type2 = $type == 4 ? 5 : $type;
        // 子订单type不一样
        $users = $this->db->queryAllList("OrderEndUserList", ["equation"=>[":OrderID"=>$orderID,":Type"=>$type2]]);
        $this->log->payLog("#afterPay#users=".json_encode($users));
        if ($type == 2) {
            $userids = [];
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                array_push($userids, $userData[0]["Account"]);
                $this->services["sip"]->insertSipEnable($userData[0]["Account"]);

                // 续费和落地续费需要记录营销活动使用情况
                if ($user["ActivityUUID"]) {
                    $this->db->insert2List("UserActivityUsed", [":Activity"=>$user["ActivityUUID"], ":PersonalAccountID"=>$user["AppID"]]);
                }
            }
        } elseif ($type == 4) {
            $userSips = [];
            $userids = [];
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                array_push($userSips, ["sip"=>$userData[0]["Account"],"enableGroup"=>0]);
                array_push($userids, $userData[0]["Account"]);

                // 续费和落地续费需要记录营销活动使用情况
                if ($user["ActivityUUID"]) {
                    $this->db->insert2List("UserActivityUsed", [":Activity"=>$user["ActivityUUID"], ":PersonalAccountID"=>$user["AppID"]]);
                }
            }
            $this->services["sip"]->addMul2Freeswish($userSips);
        } else {
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                $this->services["sip"]->insertSipEnable($userData[0]["Account"]);
            }
        }
    }

    // 订单验证
    public function priceCheck($bmOrder)
    {
        global $cMessage;
        $orderData = $this->db->queryAllList("OrderList", ["equation"=>[":BmOrderNumber"=>$bmOrder,":Status"=>0]]);
        if (count($orderData) === 0) {
            $this->log->payLog("#priceCheck# pay failed because order that bmOrder=$bmOrder&&status=0 is not exits.");
            $cMessage->echoErrorMsg(StatePayFailed);
        }
        $orderData = $orderData[0];
        $orderID = $orderData["ID"];

        $type = $orderData["Type"];
        $active = ($type == 2 || $type == 4 || $type == 8) ? 1 : 0;
        $orderEnds = $this->db->queryAllList("OrderEndUserList", ["equation"=>[":OrderID"=>$orderID]]);

        // 2021-10-12#zyc#6.2#5,6,7为高级付费功能,判断社区是否存在
        if ($type == 5 || $type == 6 || $type == 7) {
            $user = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$orderData["InstallID"]]]);
            // 无效的订单
            if (count($user) === 0) {
                $this->log->payLog("#priceCheck# pay failed because user that id=".$orderData["InstallID"]." is not exits.");
                $this->setOrderAbnormal($orderID);
                $cMessage->echoErrorMsg(StatePayFailed);
            }
        } else {
            foreach ($orderEnds as $orderEnd) {
                $user = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$orderEnd["AppID"],":Active"=>$active]]);
                // 无效的订单
                if (count($user) === 0) {
                    $this->log->payLog("#priceCheck# pay failed because user that id=".$orderEnd["AppID"]."&& active=$active is not exits.");
                    $this->setOrderAbnormal($orderID);
                    $cMessage->echoErrorMsg(StatePayFailed);
                }
            }
        }
        return ["type" => $type,"users" => $orderEnds, 'number' => $orderData['OrderNumber']];
    }

    public function setOrderFail($orderID)
    {
        $params = [
            "Type"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $this->db->update2ListWID("OrderList", [":ID"=>$orderID,":Status"=>2,":PayPlatform"=>$type]);
    }

    public function setOrderAbnormal($orderID)
    {
        $this->db->update2ListWID("OrderList", [":ID"=>$orderID,":Status"=>4]);
    }

    // 续费
    public function renewal($orderID, $orderEnds)
    {
        $orderData = $this->db->queryAllList("OrderList", ["equation"=>[":ID"=>$orderID,":Status"=>0]]);
        $month = $orderData[0]["Months"];
        $appsNumber = $orderData[0]["AppNumbers"];
        // 除去主账户
        $appsSubNumber = $appsNumber - 1;
        foreach ($orderEnds as $orderEnd) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$orderEnd["AppID"]]])[0];
            $expireTime = $userData["ExpireTime"];
            $now = \util\computed\getNow();
            $expireTime = strtotime($expireTime) > strtotime($now) ? $expireTime : $now;
            $newExpireTime = \util\computed\computedLastDate($expireTime, $month);
            $this->db->update2ListWID("PersonalAccount", [":ID"=>$orderEnd["AppID"],":ExpireTime"=>$newExpireTime]);
            // 修改所有已激活的从账户过期时间
            $subUsers = $this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and (Role = 11 or Role = 21) and Active = 1", [":ParentID"=>$orderEnd["AppID"]]);
            foreach ($subUsers as $subUser) {
                $this->db->update2ListWID("PersonalAccount", [":ID"=>$subUser["ID"],":ExpireTime"=>$newExpireTime]);
            }
        }
    }

    // 激活 TODO 是否会存在激活时存在从账户的情况
    public function familyActive($orderID, $orderEnds)
    {
        foreach ($orderEnds as $orderEnd) {
            $this->activeAccount($orderEnd["AppID"]);
        }
    }

    // id:激活账号id; isUpdate:是否激活; isSuper:是否是超级管理员刷新计费模型
    public function activeAccount($id, $isUpdate = 1, $isSuper = 0)
    {
        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]]);
        $role = $userData[0]["Role"];
        $now = \util\computed\getNow();
        $updatinginfo = [];
        $total = 0;
        if ($role == PERENDMROLE) {
            if ($isSuper == 1) {
                $data0 = $this->services["billsysUtil"]->getCharge("single", $userData[0]["ParentID"], [$id], PAYACTIVE);
                $data0 = $data0[$id];
                if ($data0["ActiveFee"] == 0) {
                    $isSuper = 0;
                }
            }
            $data1 = $this->services["billsysUtil"]->getCharge("single", $userData[0]["ParentID"], [$id], PAYSUBDRCIPTION);
            $data1 = $data1[$id];
            // list(,$expireTime) = $this->services["billsysUtil"]->computedExTimeActive($data, $id);
            $expireTime = $now;

            // 是否激活，默认执行激活，否则就是获取激活列表
            if ($isUpdate == 1) {
                if ($isSuper == 0) {
                    $this->db->update2ListWID("PersonalAccount", [":ID" => $id, ":Active" => 1, ":ActiveTime" => $now, ":ExpireTime" => DEFAULTEXPIRETIME]);
                    $subUsers = $this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and Role = 11", [":ParentID" => $id]);
                    foreach ($subUsers as $subUser) {
                        $this->db->update2ListWID("PersonalAccount", [":ID" => $subUser["ID"], ":Active" => 1, ":ActiveTime" => $now, ":ExpireTime" => DEFAULTEXPIRETIME]);
                        if ($data1["MonthlyFee"] == 0) {
                            $this->db->update2ListWKey("PersonalAccount", [":ID" => $subUser["ID"], ":PhoneExpireTime" => DEFAULTEXPIRETIME]);
                        }
                    }
                    if ($data1["MonthlyFee"] == 0) {
                        $this->db->update2ListWID("PersonalAccount", [":ID" => $id, ":PhoneExpireTime" => DEFAULTEXPIRETIME]);
                    }
                }
            } else {
                if ($data0["ActiveFee"] == 0) {
                    $users = $this->db->querySList("select Name,SipAccount,Phone,Email from PersonalAccount where ID = :ID and Role = 10
                    union select Name,SipAccount,Phone,Email from PersonalAccount where ParentID = :ID and Role = 11", [":ID" => $id]);
                    foreach ($users as $user) {
                        array_push($updatinginfo, $user);
                        ++$total;
                    }
                }

                return [$updatinginfo, $total];
            }
        } elseif ($role == COMENDMROLE) {
            if ($isSuper == 1) {
                $data0 = $this->services["billsysUtil"]->getCharge("multiple", $userData[0]["ParentID"], [$id], PAYACTIVE);
                $data0 = $data0[$id];
                if ($data0["ActiveFee"] == 0) {
                    $isSuper = 0;
                }
            }
            // 社区收费计划,获取月租情况
            $data1 = $this->services["billsysUtil"]->getCharge("multiple", $userData[0]["ParentID"], [$id], PAYSUBDRCIPTION);
            list(, $expireTime) = $this->services["billsysUtil"]->computedExTimeActive($data1, $id);

            // 是否激活，默认执行激活，否则就是获取激活列表
            if ($isUpdate == 1) {
                if ($isSuper == 0) {
                    $this->db->update2ListWID("PersonalAccount", [":ID" => $id, ":Active" => 1, ":ActiveTime" => $now, ":ExpireTime" => $expireTime]);
                    $subUsers = $this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and Role = 21", [":ParentID" => $id]);
                    foreach ($subUsers as $subUser) {
                        $this->db->update2ListWID("PersonalAccount", [":ID" => $subUser["ID"], ":Active" => 1, ":ActiveTime" => $now, ":ExpireTime" => $expireTime]);
                    }
                    $communityTime = $this->db->querySList("select CreateTime from Account where ID=:ID", [":ID" => $userData[0]["ParentID"]])[0]["CreateTime"];
                    if (!$communityTime) {
                        $this->log->actionLog("#model#order#activeAccount#update community create time");
                        $this->db->update2ListWID("Account", [":ID" => $userData[0]["ParentID"], ":CreateTime" => $now]);
                    }
                }
            } else {
                if ($data0["ActiveFee"] == 0) {
                    $users = $this->db->querySList("select Name,SipAccount,Phone,Email from PersonalAccount where ID = :ID and Role = 20
                    union select Name,SipAccount,Phone,Email from PersonalAccount where ParentID = :ID and Role = 21", [":ID" => $id]);
                    foreach ($users as $user) {
                        array_push($updatinginfo, $user);
                        ++$total;
                    }
                }

                return [$updatinginfo, $total];
            }
        } elseif (in_array($role, OFFROLE)) {
            if ($isSuper == 1) {
                $data0 = $this->services["billsysUtil"]->getCharge("office", $userData[0]["ParentID"], [$id], PAYACTIVE);
                $data0 = $data0[$id];
                if ($data0["ActiveFee"] == 0) {
                    $isSuper = 0;
                }
            }
            // 办公收费计划,获取月租情况
            $data1 = $this->services["billsysUtil"]->getCharge("office", $userData[0]["ParentID"], [$id], PAYSUBDRCIPTION);
            list(, $expireTime) = $this->services["billsysUtil"]->computedExTimeActive($data1, $id);

            // 是否激活，默认执行激活，否则就是获取激活列表
            if ($isUpdate == 1) {
                if ($isSuper == 0) {
                    $this->db->update2ListWID("PersonalAccount", [":ID" => $id, ":Active" => 1, ":ActiveTime" => $now, ":ExpireTime" => $expireTime]);
                    $officeTime = $this->db->querySList("select CreateTime from Account where ID=:ID", [":ID" => $userData[0]["ParentID"]])[0]["CreateTime"];
                    if (!$officeTime) {
                        $this->log->actionLog("#model#order#activeAccount#update office create time");
                        $this->db->update2ListWID("Account", [":ID" => $userData[0]["ParentID"], ":CreateTime" => $now]);
                    }
                }
            } else {
                if ($data0["ActiveFee"] == 0) {
                    $officeRole = implode(',', OFFROLE);
                    $users = $this->db->querySList("select Name,SipAccount,Phone,Email from PersonalAccount where ID = :ID and Role in ($officeRole)", [":ID" => $id]);
                    foreach ($users as $user) {
                        array_push($updatinginfo, $user);
                        ++$total;
                    }
                }

                return [$updatinginfo, $total];
            }
        }
    }

    // 购买额外app
    public function buyApp($orderID)
    {
        // global $cMessage;
        $orderData = $this->db->queryAllList("OrderEndUserList", ["equation"=>[":OrderID"=>$orderID]])[0];
        $id = $orderData["AppID"];
        // app是否存在
        $user = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        // if (count($user) === 0) {
        //     $cMessage->echoErrorMsg(StatePayFailed);
        //     $this->db->commit();
        // }

        // 更改app 状态
        $parentID = $user[0]["ParentID"];
        $mainData = $this->db->querySList("select P.Account,Pf.ID,Pf.FreeAppCount from PersonalAccount P join PersonalAccountCnf Pf on P.Account = Pf.Account where P.ID = :ID", [":ID"=>$parentID ])[0];
        $this->db->update2ListWID("PersonalAccountCnf", [":FreeAppCount"=>$mainData["FreeAppCount"]+1,":ID"=>$mainData["ID"]]);
        $expireTime = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$parentID]])[0]["ExpireTime"];
        $this->db->update2ListWID("PersonalAccount", [":ID"=>$id,":Active"=>1,":ActiveTime"=>\util\computed\getNow(),":ExpireTime"=>$expireTime]);
        return true;
    }

    // 落地号码续费
    public function buyLandline($orderID, $orderEnds)
    {
        $orderData = $this->db->queryAllList("OrderList", ["equation"=>[":ID"=>$orderID,":Status"=>0]]);
        $month = $orderData[0]["Months"];
        foreach ($orderEnds as $orderEnd) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$orderEnd["AppID"]]])[0];
            $expireTime = $userData["PhoneExpireTime"];
            $now = \util\computed\getNow();
            $expireTime = strtotime($expireTime) > strtotime($now) ? $expireTime : $now;
            $newExpireTime = \util\computed\computedLastDate($expireTime, $month);
            $this->db->update2ListWID("PersonalAccount", [":ID"=>$orderEnd["AppID"],":PhoneExpireTime"=>$newExpireTime]);
        }
    }


    // 高级功能一次性付费
    public function featurePlanOnce($orderID)
    {
        $orderData = $this->db->queryAllList("OrderList", ["equation"=>[":ID"=>$orderID,":Status"=>0]])[0];
        $accountID = $orderData['AccountID'];
        $this->db->update2ListWKey("CommunityInfo", [":AccountID"=>$accountID,":FeatureExpireTime"=>DEFAULTEXPIRETIME], "AccountID");
    }

    // 高级功能月费
    public function featurePlanMonthly($orderID)
    {
        $orderData = $this->db->queryAllList("OrderList", ["equation"=>[":ID"=>$orderID,":Status"=>0]])[0];
        $accountID = $orderData['AccountID'];
        $month = $orderData["Months"];
        $expireTime = $this->db->querySList("select FeatureExpireTime from CommunityInfo where AccountID = :AccountID", [":AccountID"=>$accountID]);
        $now = \util\computed\getNow();
        $expireTime = strtotime($expireTime) > strtotime($now) ? $expireTime : $now;
        $newExpireTime = \util\computed\computedLastDate($expireTime, $month);
        $this->db->update2ListWKey("CommunityInfo", [":AccountID"=>$accountID,":FeatureExpireTime"=>$newExpireTime], "AccountID");
    }

    public function capture()
    {
        global $cMessage;
        $params = [
            "OrderNumber"=>"",
            "Token"=>"",
            "Type"=>"",
            "Email"=>"",
            "Price"=>"",
            "PlatformOrder"=>"",
            "BeforeOnePrice"=>"",
            "CouponNumber"=>"",
            "CouponCount"=>"",
            "Status"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $bmOrderNumber = $params["OrderNumber"];
        $token = $params["Token"];
        $type = $params["Type"];
        $email = $params["Email"];
        // 价格需要更新,因为存在一口价情况
        $price = $params["Price"];
        $platformOrder = $params["PlatformOrder"];
        $beforeOnePrice = $params["BeforeOnePrice"];
        $couponNumber = $params["CouponNumber"];
        $couponCount = $params["CouponCount"];
        $status = $params["Status"];

        // 成功状态改成6，系统处理中
        $status = $status == 'success' ? 6 : 2;
        
        $this->db->begin();
        $data = $this->db->querySList("select ID from OrderList where BmOrderNumber = :BmOrderNumber and WebHookToken = :WebHookToken and Status = 0 for update", [":BmOrderNumber"=>$bmOrderNumber, ":WebHookToken"=>$token]);
        if (count($data) == 0) {
            $successOrders = $this->db->querySList("select ID from OrderList where BmOrderNumber = :BmOrderNumber and WebHookToken = :WebHookToken and Status in (1,6)", [":BmOrderNumber"=>$bmOrderNumber, ":WebHookToken"=>$token]);
            // 系统处理中或者支付成功的订单，都算做成功不进行报错
            if (!empty($successOrders)) {
                $this->db->commit();
                return;
            }
            $cMessage->echoErrorMsg(StatePayFailed);
        }
        $this->log->payLog("#capture#token is valid");
        $data = $data[0];
        $result = $this->priceCheck($bmOrderNumber);
        $this->db->update2ListWID("OrderList", [":ID"=>$data["ID"], ":PaypalOrder"=>$platformOrder, ":PaypalEmail"=>$email, ":PayPlatform"=>$type, ":FinalPrice"=>$price,
        ":BeforeOncePrice"=>$beforeOnePrice, ":CouponNumber"=>$couponNumber, ":CouponCount"=>$couponCount]);
        $orderID = $data["ID"];
        $type = $result["type"];

        // 删除订单资源锁定表
        $this->db->delete2ListWKey('LockOrder', 'OrderNumber', $result['number']);
        if ($status == 2) {
            $this->db->update2ListWID("OrderList", [":ID"=>$orderID,":Status"=>$status]);
            $this->db->commit();
            return;
        }

        // 检测前移
        if ($type == 3) {
            $orderData = $this->db->queryAllList("OrderEndUserList", ["equation"=>[":OrderID"=>$data["ID"]]])[0];
            $id = $orderData["AppID"];
            // app是否存在
            $user = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
            if (count($user) === 0) {
                $cMessage->echoErrorMsg(StatePayFailed);
                $this->db->commit();
            }
        }
        
        // 插入待处理任务表
        $this->db->insert2List(
            'ToBeDealOrder',
            [':OrderNumber' => $result['number'], ':CreateTime' => \util\computed\getNow(), ':Status' => 0]
        );
        $taskId = $this->db->lastInsertId();
        // 更改
        $this->db->update2ListWID("OrderList", [":ID"=>$orderID,":Status"=>$status]);
        $this->db->commit();
        // $this->afterPay($type, $orderID);
        \util\computed\sendDealOrderTask($taskId);
        \util\computed\setGAppData(["type"=>$type, "OrderID"=>$orderID]);
    }
}
