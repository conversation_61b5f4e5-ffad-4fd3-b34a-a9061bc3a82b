<?php
// 需要被注入的models需要在这里声明,由model和需要调用的主要方法构成，和主要方法组成一个业务的方法不写在这
const MODELS = [
    "user.addPerSubApp" => ["providers" => ["sip", "billsysUtil"]],
    "user.addComSubUser" => ["providers" => ["sip", "billsysUtil"]],
    "user.addComMainForPM" => ["providers" => ["sip", "billsysUtil"]],
    "user.addComMainUser" => ["providers" => ["sip", "billsysUtil"]],
    "user.addAppComSubUser" => ["providers" => ["sip", "billsysUtil"]],

    "user.addPerMainUser" => ["providers" => ["sip", "billsysUtil"]],
    "user.addRoom" => ["providers" => ["sip", "billsysUtil", "rps"], "models" => ["deviceCommunity.add", "notify.devComAddForManage"]],
    "user.afterDeleteMain" => ["providers" => ["callHistoryUtil"]],
    "user.afterDelete" => ["providers" => ["sip"]],
    "user.afterDeleteSub" => ["providers" => ["billsysUtil"]],
    "user.deleteEndUserForPM"=> ["providers" => ["billsysUtil", "sip", "callHistoryUtil", "captureUtil"]],

    "user.betchDeleteComMainUser" => ["providers" => ["sip", "billsysUtil", "callHistoryUtil", "captureUtil"]],
    "user.deleteRoom" => ["providers" => ["sip"], "models" => ["deviceCommunity.delete", "deviceCommunity.afterDelete"]],
    "user.deleteComMainUser" => ["providers" => ["sip", "billsysUtil", "callHistoryUtil", "captureUtil"]],
    "user.delete" => ["models" => ["devicePersonal.deletePer", "notify.devPerDelete"]],

    "user.getComActiveOrUn" => ["providers" => ["billsysUtil"]],
    "user.getPerActiveOrUn" => ["providers" => ["billsysUtil"]],
    "user.getComNextExpireTimeUser" => ["providers" => ["billsysUtil"]],
    "user.getLandlineExpireTimeUsers" => ["providers" => ["billsysUtil"]],

    "user.setExpireTime" => ["providers" => ["sip"]],
    "user.queryComUser" => ["models" => ["system.getSmartHomeCnf"]],
    "user.queryComUserInfoForMng" => ["models" => ["deviceCommunity.getownerDevices"]],
    "user.updateRoom" => ["providers" => ["sip", "billsysUtil"], "models" => ["deviceCommunity.edit"]],
    "user.updateComMainUser" => ["providers" => ["sip", "billsysUtil"]],
    "user.updateComMainUserForPM" => ["providers" => ["sip", "billsysUtil"]],
    "user.addNewRoom" => ["providers" => ["sip"]],
    "user.activeAccount" => ["models" => ["order.activeAccount"], "providers" => ["sip"]],
    "user.addComMainUsreCom" => ["providers" => ["billsysUtil"]],
    "user.updatePerMain" => ["providers" => ["sip","billsysUtil"]],
    "user.importKit"=> ["models" => ["user.addPerMainUser", 'devicePersonal.addForMng']],
    "user.registerPerMainUser" => ["models" => ["notify.userCreateEmail"]],

    "user.updateBillList" => ["models" => ["order.activeAccount"], "providers" => ["billsysUtil","sip"]],
    "user.updateActiveUser" => ["models" => ["order.activeAccount"], "providers" => ["sip"]],
    "user.setParamsAndDelSub" => ["models" => ["notify.userSubDelete", "notify.newSetUser", "devicePersonal.deletePer", "notify.devPerDelete"], "providers" => ["sip", "billsysUtil"]],
    "user.delSingleSubAccount" => ["models" => ["notify.userSubDelete", "notify.newSetUser", "devicePersonal.deletePer", "notify.devPerDelete"], "providers" => ["sip", "billsysUtil"]],

    "userData.queryCallHistory" => ["providers" => ["callHistoryUtil"]],
    "userData.queryCallHistoryForWeb" => ["providers" => ["callHistoryUtil"]],
    "userData.queryActiveUnRead" => ["providers" => ["callHistoryUtil", "captureUtil"]],
    "userData.getChargeMode" => ["providers" => ["billsysUtil"]],
    "userData.closeAccount" => ["providers" => ["callHistoryUtil", "sip", "rps"]],
    "userData.setPhoneRead" => ["providers" => ["callHistoryUtil"]],
    "userData.setMotionRead" => ["providers" => ["captureUtil"]],
    "userData.setInit" => ["providers" => ["sip"]],
    "userData.setData" => ["providers" => ["sip"]],
    "userData.setCall" => ["providers" => ["sip"]],
    "userData.queryFreeSubApp" => ["providers" => ["billsysUtil"]],
    "userData.changePerData" => ["providers" => ["sip"]],

    "call.queryForPM" => ["providers" => ["callHistoryUtil"]],

    "capture.queryCaptureForApp" => ["providers" => ["captureUtil"]],
    "capture.queryCaptureForWeb" => ["providers" => ["captureUtil"]],
    "capture.queryMotionForApp" => ["providers" => ["captureUtil"]],
    "capture.queryMotionForWeb" => ["providers" => ["captureUtil"]],
    "capture.queryDoorLogForApp" => ["providers" => ["captureUtil"]],
    "capture.queryDoorLogForApp2" => ["providers" => ["captureUtil"], "models" => ["system.getSmartHomeCnf"]],
    "capture.deleteCaptrueForApp" => ["providers" => ["captureUtil"]],
    "capture.deleteCaptrueForWeb" => ["providers" => ["captureUtil"]],
    "capture.deleteMotionForApp" => ["providers" => ["captureUtil"]],
    "capture.deleteMotionForWeb" => ["providers" => ["captureUtil"]],
    "capture.queryCaptureForPM" => ["providers" => ["captureUtil"]],
    "capture.deleteForCommunity" => ["providers" => ["captureUtil"]],
    "capture.queryDoorLogForPM" => ["providers" => ["captureUtil"], "models" => ["system.getSmartHomeCnf"]],

    "chargePlan.queryPersonal" => ["providers" => ["billsysUtil"]],
    "chargePlan.queryManage" => ["providers" => ["billsysUtil"]],
    "chargePlan.queryCommunity" => ["providers" => ["billsysUtil"]],
    "chargePlan.queryPerUserSubPay" => ["providers" => ["billsysUtil"]],
    "chargePlan.queryComUserSubPay" => ["providers" => ["billsysUtil"]],
    "chargePlan.userBillingInfo" => ["providers" => ["billsysUtil"]],

    // "chargePlan.queryComUserSubPay"=>["providers"=>["callHistoryUtil","captureUtil"]],

    "deviceCommunity.add" => ["providers" => ["sip", "rps", "billsysUtil"], "models" => ["macLibrary.importPerDevice"]],
    "deviceCommunity.delete" => ["providers" => ["sip", "rps", "billsysUtil", "captureUtil"]],
    "deviceCommunity.batchDelete" => ["providers" => ["sip", "rps", "billsysUtil","captureUtil"]],
    "deviceCommunity.edit" => ["providers" => ["sip", "billsysUtil", "rps", "captureUtil"], "models" => ["macLibrary.importPerDevice", "notify.devComDel", "notify.devComAddForManage", "notify.devComUpdate"]],
    "deviceCommunity.queryDevWithType" => ["models" => ["devicePersonal.queryAllDevWithType"]],

    "devicePersonal.addDevWCode" => ["providers" => ["sip", "rps"]],
    "devicePersonal.addForMng" => ["providers" => ["rps", "sip"], "models" => ["macLibrary.importPerDevice"]],
    "devicePersonal.deletePer" => ["providers" => ["rps", "sip", "captureUtil"]],
    "devicePersonal.editForPerMng" => ["providers" => ["rps", "sip"], "models" => ["notify.devPerDelete", "devicePersonal.deletePer", "devicePersonal.addForMng", "userData.afterPerAddDev", "notify.devPerAddForManage", "notify.devPerUpdate"]],

    "login.endUserLogin" => ["providers" => ["billsysUtil", "activeAuth"]],
    "login.register" => ["providers" => ["sip", "billsysUtil"]],
    "login.registerFromSB" => ["providers" => ["sip", "billsysUtil", "aes"]],

    "macLibrary.addForSup" => ["providers" => ["rps"]],
    "macLibrary.addArea" => ["providers" => ["rps"]],
    "macLibrary.addPC" => ["providers" => ["rps"]],
    "macLibrary.delete" => ["providers" => ["rps"]],
    "macLibrary.uploadFilePCMng" => ["providers" => ["rps"]],
    "macLibrary.uploadFileArea" => ["providers" => ["rps"]],
    "macLibrary.uploadFileSup" => ["providers" => ["rps"]],
    "macLibrary.importPerDevice" => ["providers" => ["rps"]],
    "macLibrary.queryForSup" => ["providers" => ["rps"]],
    "macLibrary.queryForArea" => ["providers" => ["rps"]],
    "macLibrary.queryForInstall" => ["providers" => ["rps"]],

    "manage.add" => ["providers" => ["sip","billsysUtil"], "models" => ["chargePlan.addCom", "chargePlan.addPer"]],
    "manage.delete" => ["models" => ["chargePlan.delete"]],
    "manage.edit" => ["models" => ["chargePlan.setManage", "chargePlan.setPer"]],
    "manage.setCommunity" => ["models" => ["chargePlan.setManage"], 'providers' => ['billsysUtil']],
    "manage.deleteCommunity" => ["providers" => ["rps", "callHistoryUtil"],
        "models" => ["user.deleteRoom", "user.afterDeleteRoom", "user.afterDeleteMain", "user.afterDelete", "user.afterDeleteRoomNotify", "deviceCommunity.delete", "deviceCommunity.afterDelete"]
    ],
    "manage.queryArea" => ["providers" => ["billsysUtil"]],
    "manage.addCommunity" => ["providers" => ["billsysUtil"]],

    "manageData.importCommunity" => ["providers" => ["rps", "sip"], "models" => ["deviceCommunity.add", "user.addNewRoom", "user.addComMainUsreCom", "access.addUser", "system.getSmartHomeCnf"]],

    "order.createdCommunityOrder" => ["providers" => ["billsysUtil"]],
    "order.createUserOrder" => ["providers" => ["billsysUtil"]],
    "order.createInstallerOrder" => ["providers" => ["billsysUtil"]],
    "order.createAreaInstallOrder" => ["providers" => ["billsysUtil"]],
    "order.createAreaCommunityOrder" => ["providers" => ["billsysUtil"]],
    "order.createAreaOfficeOrder" => ["providers" => ["billsysUtil"]],
    "order.pay" => ["providers" => ["billsysUtil", "sip"]],
    "order.activeAccount" => ["providers" => ["billsysUtil", "sip"]],
    "order.capture" => ["providers" => ["billsysUtil", "sip"]],
    "order.createDifferenceOrder" => ["providers" => ["billsysUtil"]],

    "communityData.getWeekData" => ["providers" => ["captureUtil", "callHistoryUtil"]],
    "communityData.getAllCount" => ["providers" => ["captureUtil", "callHistoryUtil"]],
    "communityData.getLastFiveDoorLog" => ["providers" => ["captureUtil"], "models" => ["system.getSmartHomeCnf"]],
    "communityData.getPMAllData" => ["providers" => ["captureUtil"]],
    "communityData.getLastFifteenDoorLog" => ["providers" => ["captureUtil"], "models"=>["capture.setCaptureIp"]],
    "communityData.getBuildRoomForDis" => ["models" => ["manage.queryAllInstaller"]],
    "communityData.queryInfo" => ["models" => ["system.getSmartHomeCnf"]],
    "communityData.getCommunityDetail" => ["models" => ["system.getSmartHomeCnf"]],
    "communityData.getBuildRoom" => ["models" => ["system.getSmartHomeCnf"]],

    "propertyData.getBill" => ["providers" => ["billsysUtil"]],
    "propertyData.modifyBill" => ["providers" => ["billsysUtil"]],

    "notify.pay" => ["providers" => ["sip"]],

    "notify.exportPmLog" => ["providers" => ["captureUtil", "callHistoryUtil"]],
    "capture.getExportTime" => ["providers" => ["captureUtil", "callHistoryUtil"]],
    "capture.insertPMExportLog" => ["providers" => ["captureUtil", "callHistoryUtil"]],

    "apartment.modifyPMApt" => ["providers" => ["sip"]]
];
