<?php

namespace package\community\router;

use \framework\BasicRouter;

class Subscription extends BasicRouter
{
    public function exec()
    {
        /**
        * ins、pm创建订阅
        */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'createSubscription')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string',
                'TotalPrice:string', 'VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription\\createSubscription');

        /**
        * ins、pm编辑订阅
        */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'editSubscription')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string',
                'TotalPrice:string', 'SubscriptionUUID:uuid','VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(R_PROJECT_ROLE,RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription\\createSubscription');

        /**
         * ins创建RentManager订阅
         */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'createSubscriptionForRentManager')
            ->setMethod('POST')
            ->addParams('Cycles:between-with-scope("0","180")', 'RentManagerCustomerList:string-required', 'TotalPrice:string')
            ->addRoles(R_PROJECT_ROLE)
            ->setAuth('web')
            ->setControl('subscription\\createSubscriptionForRentManager');

        /**
         * ins编辑RentManager订阅
         */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'editSubscriptionForRentManager')
            ->setMethod('POST')
            ->addParams('Cycles:between-with-scope("0","180")', 'RentManagerCustomerList:string-required', 'TotalPrice:string', 'SubscriptionUUID:uuid')
            ->addRoles(R_PROJECT_ROLE)
            ->setAuth('web')
            ->setControl('subscription\\createSubscriptionForRentManager');

        /**
        * dis、subdis创建订阅
        */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'createSubscriptionForDis')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string',
                'TotalPrice:string', 'InstallerUUID:uuid', 'VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(RAREAGRADE,RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription\\createSubscription');

        /**
        * dis、subdis编辑订阅
        */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'editSubscriptionForDis')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string',
                'TotalPrice:string', 'SubscriptionUUID:uuid', 'InstallerUUID:uuid','VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(RAREAGRADE,RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription\\createSubscription');

        //根据订阅UUID，取消社区订阅
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'cancelSubscription')
            ->setMethod('POST')
            ->addParams('UUID:uuid')
            ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE, RAREAGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription', 'cancelSubscription');

        //根据BmSubscriptionNumber，取消社区订阅
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'cancelSubscriptionByBmNumber')
            ->setMethod('POST')
            ->addParams('BmSubscriptionNumber:string-required')
            ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE, RAREAGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription', 'cancelSubscriptionByBmNumber');

        //删除订阅列表
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'delSubscription')
            ->setMethod('POST')
            ->addParams('UUID:uuid')
            ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE, RAREAGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription', 'delSubscription');

        /**
         * ins、pm计算订阅续费价格
         */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'computedPrice')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'SubscriptionUUID?:or-rule("uuid","string-empty")', 'VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription\\computedPrice');

        /**
         * dis、subdis计算订阅续费价格
         */
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'computedPriceForDis')
            ->setMethod('POST')
            ->addParams('Users:object', 'IntervalType:enum("0","1","2","3")', 'InstallerUUID:uuid', 'SubscriptionUUID?:or-rule("uuid","string-empty")', 'VideoSites:object', 'ThirdLockUUIDs?:object')
            ->addRoles(RAREAGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('subscription\\computedPrice');


        //ins获取社区订阅列表
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'getListForIns')
            ->setMethod('GET')
            ->addParams('IsBatch:enum("0","1")', 'row:string-number', 'page:string-number')
            ->addRoles(RCOMMUNITYGRADE, RPERSONGRADE)
            ->setAuth('web')
            ->setControl('subscription', 'getListForIns');

        //pm获取社区订阅列表
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'getListForPm')
            ->setMethod('GET')
            ->addParams('row:string-number', 'page:string-number')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription', 'getListForPm');

        //ins获取社区订阅详情
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'getInfo')
            ->setMethod('GET')
            ->addParams('UUID:uuid', 'IsBatch:enum("0","1")')
            ->addRoles(RAREAGRADE, RSUBDISTRIBUTOR, R_PROJECT_ROLE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription', 'getInfo');

        //ins获取视频存储订阅/订单详情
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'getVideoStorageSubscriptionInfo')
            ->setMethod('GET')
            ->addParams('UUID:uuid')//社区uuid
            ->addRoles(R_PROJECT_ROLE, RAREAGRADE, RSUBDISTRIBUTOR, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription', 'getVideoStorageSubscriptionInfo');

        //获得视频存储订阅是否有自动续费
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'getVideoStorageInfo')
            ->setMethod('GET')
            ->addParams('UUID:uuid')
            ->addRoles(R_PROJECT_ROLE, RAREAGRADE, RSUBDISTRIBUTOR, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('subscription', 'getVideoStorageInfo');

        //go服务取消自动续费，TODO 后续取消订阅需要go做
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'cancelSubscriptionByGo')
            ->setMethod('POST')
            ->addParams('Community:uuid')
            ->addRoles()
            ->setAuth(false)
            ->setControl('subscription', 'cancelSubscriptionByGo');

        //取消三方锁的订阅
        $this->setRouterName('v3', 'web', 'community', 'subscription', 'cancelSubscriptionByLock')
            ->setMethod('POST')
            ->addParams('UUID:or-rule("uuid","is-array")','Brand')
            ->addRoles()
            ->setAuth(false)
            ->setControl('subscription', 'cancelSubscriptionByLock');

        return $this->getRouters();
    }
}
