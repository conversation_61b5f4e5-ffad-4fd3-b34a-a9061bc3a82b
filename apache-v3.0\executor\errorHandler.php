<?php
/*
 * @Description: 请求错误处理者(@单例)
 * @version:Re1.0 
 * @Author: kxl
 * @Date: 2019-12-19 16:51:23
 * @LastEditors  : kxl
 */
namespace executor;
include_once __DIR__."/basic.php";
include_once __DIR__."/../interfaces/executor/main.php";

class CErrorHandler extends \executor\Basic implements \interfaces\executor\main\IExecutor {
    public $errorCode;
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    public function parse () {
        // TODO 无效值抛出异常
    }
    public function exec () {
        switch ($this->errorCode) {
            case State404:
                echo '404 Not Found.';
                header($_SERVER['SERVER_PROTOCOL'] . ' 404 Not Found', true, 404);
                break;
            
            case State405:
                header($_SERVER['SERVER_PROTOCOL'] . ' 405 Method Not Allow', true, 405);
                echo '405 Method Not Allow.';
                break;
            
            case State500:
                header($_SERVER['SERVER_PROTOCOL'] . ' 500 service error', true,500);
                echo '500 service error.';
                break;
        }
        // 错误，脚本退出
        exit;
    }
}
