<?php
namespace package\community\model\featurePlan\method;

trait Info
{
    public function getInfo()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);

        // 获取社区高级功能过期时间
        $featureExpireTime = $this->db->querySList(
            'select FeatureExpireTime from '.PROXY_TABLES['communityInfo'].' where AccountID = :ID',
            [':ID' => $projectId]
        )[0]['FeatureExpireTime'];
        // 获取社区的高级功能方案
        $data = $this->db->querySList(
            'select F.Item, F.Name from '.PROXY_TABLES['manageFeature'].' M join '.PROXY_TABLES['featurePlan']
            .' F on M.FeatureID = F.ID where M.AccountID = :ID',
            [':ID' => $projectId]
        )[0];
        $item = $data['Item'];
        $featureName = $data['Name'];
        // 获取社区的高级功能的ID，旧小区可能是0
        $featureID = $this->db->querySList(
            'select FeatureID from '.PROXY_TABLES['manageFeature'].' where AccountID = :ID',
            [':ID' => $projectId]
        )[0]['FeatureID'];

        return [$featureExpireTime, $item, $featureID, $featureName];
    }
}
