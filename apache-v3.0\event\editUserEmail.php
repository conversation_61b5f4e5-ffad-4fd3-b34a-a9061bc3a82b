<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 14:24:47
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CEditUserEmail {
    private $email;
    function on ($id) {
        global $cLog;
        $db = \database\CDatabase::getInstance();
        $email = $db->querySList("select Email from PersonalAccount where ID=:ID",[":ID"=>$id])[0]["Email"];
        $cLog->actionLog("#event#editUserEmail.on#id=$id;email=$email");
        $this->email = $email;
    }
    function emit ($id,$email) {
        global $cLog;
        $cLog->actionLog("#event#editUserEmail.emit#id=$id;email=$email");
        if($email != $this->email) {
            include_once __DIR__."/../util/string.php";
            include_once __DIR__."/../util/computed.php";
            $password = \util\string\generatePw(8);
            $db = \database\CDatabase::getInstance();
            $db->update2ListWID("PersonalAccount",[":ID"=>$id,":Passwd"=>md5($password)]);
            \util\computed\setGAppBranch("changeEmail");
            \util\computed\setGAppData(["Passwd"=>$password]);
        }
    }
}