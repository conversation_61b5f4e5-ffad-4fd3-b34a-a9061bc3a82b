#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-21
# Description   :   openapi 构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

###################### 定义变量 ######################
RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3              #需要部署的中间件
IMAGE_ACR="${4}"           #镜像仓库地址

PKG_ROOT=$RSYNC_PATH
OPENAPI=/var/www/html-api/openapi

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

###################### 开始安装、读取配置 ######################
echo "Begin to install openapi"
echo '读取配置'
source "$PKG_ROOT"/shell/package_start_shell/source.sh $PKG_ROOT

###################### 替换配置文件 ######################
echo '替换配置文件的配置'
if [ ! -d "$PKG_ROOT"/web_scripts ];then mkdir -p "$PKG_ROOT"/web_scripts; fi
# 生成配置文件 dynamic_config.php
create_php_config "$PKG_ROOT"/web_scripts/dynamic_config.php
mkdir -p $CONFWATCH_EXEC
if [ -f "$PKG_ROOT/web_scripts/change_web_conf_by_etcd.php" ];then
  cp -f "$PKG_ROOT"/web_scripts/change_web_conf_by_etcd.php "$CONFWATCH_EXEC"/
elif [ -f "$PKG_ROOT/openapi_scripts/change_web_conf_by_etcd.php" ];then
  cp -f "$PKG_ROOT"/openapi_scripts/change_web_conf_by_etcd.php "$CONFWATCH_EXEC"/
fi

###################### 复制安装包的文件 ######################
echo '复制openapi安装包的文件'
cp -f "$PKG_ROOT"/web_scripts/dynamic_config.php "$PKG_ROOT"/openapi/v4/config/
#share/lang下的文件夹常量改为数组（防止重新打了词条库忘记修改的情况）
sedGrepFile 'const MSGTEXT =' 'return' "$PKG_ROOT/openapi/v4/share/lang"
# notify下的global变量修改 config配置路径修改
sedGrepFile 'global $db;' '$db = \\share\\util\\getDatabase();' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile 'global $cLog;' '$cLog = \\share\\util\\getLog();' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile 'global $cAuditLog;' '$cAuditLog = \\share\\util\\getAuditLog();' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile ''\''\/..\/config\/dynamic_config.php' ''\''\/..\/..\/config\/dynamic_config.php' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile '\\util\\string' '\\share\\util' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile '\\util\\time' '\\share\\util' "$PKG_ROOT/openapi/v4/share/notify"
sedGrepFile '\\util\\computed' '\\share\\util' "$PKG_ROOT/openapi/v4/share/notify"

cd "$PKG_ROOT"/openapi_scripts
bash -x install.sh $INSTALL_CONF $OPENAPI $APP_BACKEND_INSTALL_CONF

###################### 创建存放日志的文件夹 ######################
echo "创建存放日志的文件夹"
# php 日志
mkdir -p /var/log/php
touch /var/log/php/php-error.log
touch /var/log/php/php-error-detail.log
touch /var/log/php/php-person-action.log
touch /var/log/php/php-pay-action.log
touch /var/log/php/php-business.log
touch /var/log/php/web-request.log

# php 和后台通信日志文件
touch /var/log/php/csadapt-interface.log
touch /var/log/php/openapi.log
chown nobody:nogroup /var/log/php/csadapt-interface.log
chown nobody:nogroup /var/log/php/openapi.log
chmod 666 /var/log/php/*.log

###################### 启动服务，请求先转发到容器内php-fpm7.4的9000端口处理 ######################
echo "启动 openapi docker 进程"
IMAGE_NAME="$IMAGE_ACR"/ak_system/web-php-fpm:5.6
CONTAINER_NAME=web_openapi
#docker仓库拉取镜像
docker pull $IMAGE_NAME
#enrtypoint.sh脚本增加执行权限
cp -f "$PKG_ROOT"/app_backend_install.conf /var/www/html-api/
cp -f "$PKG_ROOT"/shell/entrypoint/entrypoint_openapi.sh /var/www/html-api/
chmod +x /var/www/html-api/entrypoint_openapi.sh

if [ `docker ps -a | grep $CONTAINER_NAME | wc -l` -gt 0 ];then docker stop $CONTAINER_NAME;docker rm $CONTAINER_NAME;fi
docker run -d -e TZ=Asia/Shanghai --entrypoint "/var/www/html-api/entrypoint_openapi.sh" --restart=always -p 8796:9000 -v /var/www/html-api:/var/www/html-api -v /var/log/php:/var/log/php -v /var/adapt_sock:/var/adapt_sock  -v /var/async-task.sock:/var/async-task.sock -v /var/www/download:/var/www/download -v /usr/local/akcs:/usr/local/akcs -v /var/www/upload:/var/www/upload -v /download/tmp_key_qrcode:/download/tmp_key_qrcode -v /etc/ip:/etc/ip -v /home/<USER>/home/<USER>/etc/oss_install.conf:/etc/oss_install.conf --name $CONTAINER_NAME $IMAGE_NAME
