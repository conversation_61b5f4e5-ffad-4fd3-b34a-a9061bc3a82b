<?php

require_once "../framework/autoLoad.php";
require_once "../config/base.php";
require_once "../share/util/class.php";

if (php_sapi_name() != 'cli') {
    header($_SERVER['SERVER_PROTOCOL'] . ' 404 Not Found', true, 404);
    echo '404 Not Found.';
    exit;
}

$config = getopt('t:k:');
if (empty($config['t'])) {
    echo "命令行使用格式为: php createDao -t 表名 -k 字段1 -k 字段2 \n\n其中 -t为表名，是必选项， -k为可选项，需要自动生成哪些字段的查询方法。不加-k时自动以数据库中的主键和唯一键生成查询方法\n\n例如: php createDao -t Account \n";
    exit;
}

if (is_array($config['t'])) {
    echo '单次仅支持生成一个表, 多表生成请使用initAllDao';
    exit;
}
$table = $config['t'];

$db = \share\util\getDatabase();
$sql = 'SHOW FULL COLUMNS FROM ' . $table;
$res = $db->querySList($sql);
$allColumn = $uniKeys = [];
$prikey = '';
foreach ($res as $column) {
    $allColumn[] = $column['Field'];
    if (in_array($column['Key'], ['UNI', 'PRI'])) {
        $uniKeys[] = $column['Field'];
    }
    if ($column['Key'] == 'PRI') {
        $priKey = $column['Field'];
    }
}

$fileName = lcfirst($table);
if (file_exists("../dao/{$fileName}.php")) {
    echo "已经存在了/common/dao/{$fileName}.php文件，是否覆盖重建？重建后原本文件中自定义方法将丢失. 1-是，2-否\n";
    fscanf(STDIN, "%d\n", $number); // reads number from STDIN
    if ($number != 1) {
        echo "退出\n";
        exit;
    }
}

$keys = !isset($config['k']) ? $uniKeys : (is_array($config['k']) ? $config['k'] : [$config['k']]);

$funcContent = '';

$date = date("Y/m/d H:i");
if (!empty($keys)) {
    foreach ($keys as $key) {
        if (!in_array($key, $allColumn)) {
            throw new \Exception('undefined column '. $key);
        }
        $k = strtolower($key);
        $funcContent .= <<<EOF
    /**
     * @description: 根据{$key}的值查询对应数据
     * @param {string} \$$k {$key}的值
     * @param {string} \$fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function selectBy$key(\$$k, \$fields = '*')
    {
        return \$this->selectByKey('$key', \$$k, \$fields);
    }

    /**
     * @description: 根据{$key}的值查询对应数据
     * @param {array} \${$k}s {$key}的值(数组)
     * @param {string} \$fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function selectBy{$key}WArray(\${$k}s, \$fields = '*')
    {
        return \$this->selectByKeyWArray('$key', \${$k}s, \$fields);
    }


EOF;
    }
}

$content = <<<EOF
<?php

namespace dao;
use framework\BasicDao;

class $table extends BasicDao
{
    //当前表名
    public \$table = '$table';

    //需要数据混淆的字段
    public \$confusionField = [];

    //主键
    protected \$primaryKey = '$priKey';

    public function __construct()
    {
        parent::__construct(\$this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array \$data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function insert(array \$data = [])
    {
        return parent::insert(\$data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array \$data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string \$key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function update(array \$data, \$key = 'ID')
    {
        return parent::update(\$data, \$key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} \$val 字段值
     * @param {string} \$key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function delete(\$val, \$key = 'ID')
    {
        parent::delete(\$val, \$key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} \$key 字段名
     * @param {*} \$val 字段值
     * @param {string} \$fields 查询的字段
     * @param {bool} \$debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function selectByKey(\$key, \$val, \$fields = '*', \$debugSql = false, \$autoDecode = true)
    {
        return parent::selectByKey(\$key, \$val, \$fields, \$debugSql, \$autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} \$key 字段名
     * @param {array} \$val 字段值 使用wherein条件拼接字段
     * @param {string} \$fields 查询的字段
     * @param {bool} \$debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function selectByKeyWArray(\$key, \$val, \$fields = '*', \$debugSql = false, \$autoDecode = true)
    {
        return parent::selectByKeyWArray(\$key, \$val, \$fields, \$debugSql, \$autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] \$array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} \$fields 查询的字段 不填默认为全部
     * @param {bool} \$debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function selectByArray(\$array, \$fields = '*', \$debugSql = false, \$autoDecode = true)
    {
        return parent::selectByArray(\$array, \$fields, \$debugSql, \$autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} \$orderby order的条件，例如： ID ASC
     * @return \$this
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function orderBy(\$orderby = '') {
        return parent::orderBy(\$orderby);
    }

    /**
     * @description: limit限制
     * @param {string} \$limit limit的条件， 例如 10 或者 10,20
     * @return \$this
     * @author: systemCreator $date V6.5.4
     * @lastEditors: systemCreator $date V6.5.4
     */
    public function limit(\$limit = '') {
        return parent::limit(\$limit);
    }

$funcContent
}
EOF;
;

file_put_contents("../dao/{$fileName}.php", $content);
echo "生成成功\n";