# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
import csv
import time

from bs4 import BeautifulSoup as soup

# coding:utf-8
''''' 
@author: jsjxy 
'''
import re
from bs4 import BeautifulSoup
from distutils.filelist import findall
import urllib.request

phone_list = list()
phone_url_list = list()

resp=urllib.request.urlopen('https://www.haomais.com/haoma/chaxun/haomais/haoduan/139/xiamen.html')
html=resp.read()


soup = BeautifulSoup(html, "html.parser")
for tag in soup.find_all('div', class_='hd_result'):
    for div in tag.find_all('div', class_='hd_number'):
        for a in div.find_all('a'):
            phone_url_list.append("https://www.haomais.com/haoma/chaxun/haomais/haoduan/%s/xiamen.html" %(a.contents[0].replace('厦门','')))

for url in phone_url_list:
    resp = urllib.request.urlopen(url)
    html = resp.read()

    # print(contents)
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all('ul', class_='hd-city'):
        # print tag
        if 'hd-city-bt' in tag.attrs['class']:
            continue
        for li in tag.find_all('li', class_='hd-city01'):
            phone_list.append(li.contents[0].contents[0])
    time.sleep(3)
with open('xiamen_phone.php', 'w') as f:
    phone_str = ""
    for phone in phone_list:
        phone_str = phone_str+ "'" + phone + "',"
    php = """<?php 

const XiaMenPhone=[%s];

function IsXiamenPhone($phone)
{   
	$phone_head = substr($phone, 0, 7);
    return in_array($phone_head, XiaMenPhone);
}

    """ % (phone_str)
    f.write("%s" % (php))  # 文件的写操作