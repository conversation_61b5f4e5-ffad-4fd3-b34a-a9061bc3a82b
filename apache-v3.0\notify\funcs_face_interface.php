<?php

require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/utility.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/../service/aesForData.php');
require_once(dirname(__FILE__) . '/../../dao/account.php');
require_once(dirname(__FILE__) . '/../../dao/accountUserInfo.php');
require_once(dirname(__FILE__) . '/../../dao/personalAccount.php');
require_once(dirname(__FILE__) . '/../../dao/personalAccountUserInfo.php');
require_once(dirname(__FILE__) . '/../../dao/insAppFeedback.php');
require_once(dirname(__FILE__) . '/../../dao/subscriptionList.php');
require_once(dirname(__FILE__) . '/../../dao/subscriptionUsers.php');


const FILE_FULL_NAME = 'fileFullName';
const FILE_NAME = 'fileName';
const FILE_ENC_NAME = 'fileEncName';
const FILE_FULL_DETECT_NAME = 'fileFullDetectName';
const FACE_FILE_MD5 = 'faceFileMD5';
const FACE_FILE_URL = 'faceUrl';
const FACE_FILE_PREFIX = '/var/www/download/face';
const FACE_FDFS_GROUP = 'group2';
const AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED = "alarm.fdfs.face.upload_failed";
const IMPORT_FACE_TASK_ALL_SUCCESS = 1;
const IMPORT_FACE_TASK_SOME_FAILURE = 2;
const IMPORT_FACE_TASK_PROGRESSING = 3;
const IMPORT_FACE_TASK_ERROR = 4;

//人脸检测错误码
const UPLOAD_FACEPIC_ERROR_SYSTEM = -1;      //System Error：系统错误，包括解码失败，重命名图片失败等
const UPLOAD_FACEPIC_SUCCESS = 0;
const UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW = 100;      //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
const UPLOAD_FACEPIC_ERROR_WEAR_MASK = 101;      //Mask detected：检测到口罩
const UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION = 102;      //Resolution is too low.：人脸分辨率太小
const UPLOAD_FACEPIC_ERROR_WRONG_FORMAT = 103;      //File format error.：人脸格式错误
const UPLOAD_FACEPIC_ERROR_NO_FACE= 104;      //No face dectected.：图片中未检测到人脸
const UPLOAD_FACEPIC_ERROR_FILE_LARGE = 105;      //The file is too larger：图片大于10MB
const UPLOAD_FACEPIC_ERROR_FACE_LARGE = 106;      //The face is too larger.：图片中人脸过大
const UPLOAD_FACEPIC_ERROR_FACE_SMALL= 107;      //The face is too small：图片中人脸过小
const UPLOAD_FACEPIC_ERROR_MULTI_FACES= 108;      //More than one face：图片中人脸不止1个
const UPLOAD_FACEPIC_ERROR_WRONG_NAME= 109;      //File name is error.：文件名错误
const UPLOAD_FACEPIC_ERROR_EMPTY_NAME= 110;      //Resident's name is empty.：住户名字为空
const UPLOAD_FACEPIC_ERROR_NO_ACCOUNT_INFO= 111;      //Get PersonalAccount Info error.：无用户信息
const UPLOAD_FACEPIC_ERROR_ACCOUNT_INACTIVE= 112;      //The PersonalAccount is not active.：账户未激活
const UPLOAD_FACEPIC_ERROR_NOT_CLEAR= 113;      //Face not clear enough.: 图片中人脸不清晰

const CSFACECUT_UPLOAD_FACEPIC_SUCCESS = 0;
const CSFACECUT_UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW = **********;      //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
const CSFACECUT_UPLOAD_FACEPIC_ERROR_WEAR_MASK = **********;      //Mask detected：检测到口罩
const CSFACECUT_UPLOAD_FACEPIC_ERROR_NO_FACE= **********;      //No face dectected.：图片中未检测到人脸
const CSFACECUT_UPLOAD_FACEPIC_ERROR_FACE_LARGE = **********;      //The face is too larger.：图片中人脸过大
const CSFACECUT_UPLOAD_FACEPIC_ERROR_FACE_SMALL= **********;      //The face is too small：图片中人脸过小
const CSFACECUT_UPLOAD_FACEPIC_ERROR_MULTI_FACES= **********;      //More than one face：图片中人脸不止1个
const CSFACECUT_UPLOAD_FACEPIC_ERROR_NOT_CLEAR= **********;      //Face not clear enough.: 图片中人脸不清晰


function updateFailStat($uniqueID, $retJson)
{
    $tokenTime = TOKENVALIDITYTIME * 24;
    $redis = \framework\database\CRedis::getInstance();
    $redis->select(0);
    $redis->setex($uniqueID, $tokenTime, json_encode($retJson));
}

function updateDBFailStat($uniqueID, $retJson)
{
    global $cLog;
    $cLog->TRACE("updateDBFailStat begin");
    global $db;

    try {
        $db->exec2ListWArray("update ImportFaceTask set Status = :status, Reason = :reason where UUID = :uuid", [":status"=>$retJson[RESULT],":reason"=>$retJson[MESSAGE],":uuid"=>$uniqueID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("updateDBFailStat success.");
    return true;
}

function recordDetailDBSuccessStat($uniqueID, $fileResult)
{
    global $cLog;
    $cLog->TRACE("recordDetailDBSuccessStat begin");
    global $db;

    try {
        $db->insert2List("ImportFaceTaskDetail", [":ImportTaskUUID"=>$uniqueID,":PicFileName"=>$fileResult[FILE_NAME],":ImportReslut"=>$fileResult[IMPORT_RESULT],":ImportErrMsg"=>$fileResult[IMPORT_ERR_MESSAGE]]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("recordDetailDBSuccessStat success.");
    return true;
}

function recordDetailDBStat($uniqueID, $fileResult)
{
    global $cLog;
    $cLog->TRACE("recordDetailDBFailStat begin");
    global $db;

    try {
        $db->insert2List("ImportFaceTaskDetail", [":ImportTaskUUID"=>$uniqueID,":PicFileName"=>$fileResult[FILE_NAME],":ImportReslut"=>$fileResult[IMPORT_RESULT],":ImportErrMsg"=>$fileResult[IMPORT_ERR_MESSAGE]]);
        $db->exec2ListWArray("update ImportFaceTask set ErrorTaskNum = ErrorTaskNum + 1 where UUID = :uuid", [":uuid"=>$uniqueID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("recordDetailDBFailStat success.");
    return true;
}

function updateProgressStat($uniqueID, &$retJson)
{
    $totalCnt = $retJson['count'];
    $processedCnt = $retJson[PROCESSED_CNT];
    $percent = $retJson[PERCENT];
    $tokenTime = TOKENVALIDITYTIME * 24;

    if ($processedCnt < $totalCnt * $percent) {
        return;
    }

    $realPercent = round($processedCnt / (float) $totalCnt, 2);
    $retJson['realPercent'] = $realPercent;
    $redis = \framework\database\CRedis::getInstance();
    $redis->select(0);
    $redis->setex($uniqueID, $tokenTime, json_encode($retJson));
    $percent = $realPercent + 0.1;
    $retJson[PERCENT] = $percent;
}

function updateFaceTaskNum($uniqueID, $retJson)
{
    global $cLog;
    $cLog->TRACE("updateFaceTaskNum begin");
    global $db;

    $totalCnt = $retJson['count'];
    try {
        $db->exec2ListWArray("update ImportFaceTask set TaskNum = :count where UUID = :uuid", [":count"=>$totalCnt, ":uuid"=>$uniqueID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("updateFaceTaskNum success.");
    return true;
}

function updateDBProgressStat($uniqueID, &$retJson)
{
    global $cLog;
    $cLog->TRACE("updateDBProgressStat begin");
    global $db;

    $totalCnt = $retJson['count'];
    $processedCnt = $retJson[PROCESSED_CNT];
    $percent = $retJson[PERCENT];

    if ($processedCnt < $totalCnt * $percent) {
        return;
    }

    $realPercent = round($processedCnt / (float) $totalCnt, 2);
    $retJson['realPercent'] = $realPercent;
    $progress =  $realPercent*100;

    try {
        $db->exec2ListWArray("update ImportFaceTask set Status = :status, Progress = :progress where UUID = :uuid", [":status"=>$retJson[RESULT],":progress"=>$progress,":uuid"=>$uniqueID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    //$percent = $realPercent + 0.1;
    $retJson[PERCENT] = $percent;

    //成功的任务，不需 要展示任务列表
    if ($retJson[RESULT] == IMPORT_FACE_TASK_ALL_SUCCESS) {
        $db->exec2ListWArray("update ImportFaceTask set IsDeal = 1 where UUID = :uuid", [":uuid"=>$uniqueID]);
    }

    $cLog->TRACE("updateDBProgressStat success.");
    return true;
}

function unzipFile($zipFile, $extractPath)
{
    global $cLog;
    $archive = new PclZip($zipFile);
    $list = $archive->extract(PCLZIP_OPT_PATH, $extractPath);
    $cLog->TRACE("extract list=" . json_encode($list));
    return;
}

function faceDetect($extractPath)
{
    global $cLog;
    $command = '/usr/local/akcs/csadapt/bin/FaceDetect DetectDir ' . $extractPath;
    exec($command, $output, $result);
    $cLog->TRACE("faceDetect exec command=[" . $command . "],result=" . $result . ",output=" . json_encode($output));
    return;
}

function faceDetectV2($extractPath, $fileName, &$errMsg = "")
{
    global $cLog;

    $command = '/usr/local/akcs/csadapt/bin/FaceDetectV2 ' ."'".$extractPath . '/' . $fileName."'" . " " .$extractPath;
    exec($command, $output, $result);
    switch ($result) {
        case UPLOAD_FACEPIC_SUCCESS:
            $errMsg = "success";
            break;
        case UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW:
            $errMsg = "Not front view";
            break;
        case UPLOAD_FACEPIC_ERROR_WEAR_MASK:
            $errMsg = "Mask detected";
            break;
        case UPLOAD_FACEPIC_ERROR_NO_FACE:
            $errMsg = "No face dectected";
            break;
        case UPLOAD_FACEPIC_ERROR_FACE_LARGE:
            $errMsg = "The face is too larger";
            break;
        case UPLOAD_FACEPIC_ERROR_FACE_SMALL:
            $errMsg = "The face is too small";
            break;
        case UPLOAD_FACEPIC_ERROR_MULTI_FACES:
            $errMsg = "More than one face";
            break;
        case UPLOAD_FACEPIC_ERROR_NOT_CLEAR:
            $errMsg = "Face not clear enough";
            break;
        default:
            $errMsg = "System Error";
            break;
    }
    $cLog->TRACE("faceDetect exec command=[" . $command . "],result=" . $result . ",errMsg=" . $errMsg);
    return $result;
}

// 转换人脸错误码为对外错误码
function convertOutFaceErrorInfo($result)
{
    // 映射表
    $errorMap = [
        CSFACECUT_UPLOAD_FACEPIC_SUCCESS => [
            'code' => UPLOAD_FACEPIC_SUCCESS,
            'msg' => "success"
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW => [
            'code' => UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW,
            'msg' => "Please use the front view of your face."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_WEAR_MASK => [
            'code' => UPLOAD_FACEPIC_ERROR_WEAR_MASK,
            'msg' => "Please select another photo without mask."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_NO_FACE => [
            'code' => UPLOAD_FACEPIC_ERROR_NO_FACE,
            'msg' => "No face were detected in this photo."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_FACE_LARGE => [
            'code' => UPLOAD_FACEPIC_ERROR_FACE_LARGE,
            'msg' => "The face in the photo is too large."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_FACE_SMALL => [
            'code' => UPLOAD_FACEPIC_ERROR_FACE_SMALL,
            'msg' => "The face in the photo is too small."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_MULTI_FACES => [
            'code' => UPLOAD_FACEPIC_ERROR_MULTI_FACES,
            'msg' => "More than one face were detected in this photo."
        ],
        CSFACECUT_UPLOAD_FACEPIC_ERROR_NOT_CLEAR => [
            'code' => UPLOAD_FACEPIC_ERROR_NOT_CLEAR,
            'msg' => "The face was not clear enough. Please make sure your face is not in backlight."
        ],
    ];

    if (isset($errorMap[$result])){
        return $errorMap[$result];
    }

    //未匹配到，返回其他错误
    return [
        'code' => UPLOAD_FACEPIC_ERROR_SYSTEM,
        'msg' => "Invalid face."
    ];
}

function faceUpload($filePath, $account)
{
    global $cLog;

    $curl = curl_init();
    $csFaceCutAddr = CSFACECUT_ADDR;
    $url = "http://$csFaceCutAddr/face/upload";
    $postFields = [
        'account' => $account,
        'face' => new \CURLFile($filePath) 
    ];
    curl_setopt($curl, CURLOPT_TIMEOUT, 6);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $postFields);       

    $output = curl_exec($curl);
    if ($output == false) {
        $cLog->TRACE("Error: ".curl_error($curl));
    }
    else {
        $cLog->TRACE("faceDetect result=" . $output);
        $output = json_decode($output, true);
    }
    curl_close($curl);
    return $output;
}

function movePicFiles($path, $destPath)
{
    global $cLog;
    if (is_dir($path)) {
        $opendir = opendir($path);
        while ($file = readdir($opendir)) {
            if ($file != '.' && $file != '..') {
                movePicFiles($path . '/' . $file, $destPath);
            }
        }
        closedir($opendir);
        if ($path !== $destPath) {
            rmdir($path);
        }
    } else {

        #$fileName = basename($path); //这个在中文开头时候会有问题
        $fileName = preg_replace('/^(.+[\\/])|(\\/)/', '', $path);
        $result = rename($path, $destPath . "/" . $fileName);
        chown($destPath . "/" . $fileName, 'nobody');
        chgrp($destPath . "/" . $fileName, 'nobody');
        if ($result) {
            $cLog->TRACE("Move TempFiles=" . $path . " to DestPath=" . $destPath . " Success");
        } else {
            $cLog->TRACE("Move TempFiles=" . $path . " to DestPath=" . $destPath . " Failed");
        }
    }
}

//获取某目录下所有文件、目录名（不包括子目录下文件、目录名）
function getTempFiles($dir)
{
    global $cLog;
    $files = [];
    $handler = opendir($dir);
    while (($filename = readdir($handler)) !== false) {//务必使用!==，防止目录下出现类似文件名“0”等情况
        if ($filename != "." && $filename != ".." && $filename != "") {
            $files[] = $filename;
            $cLog->TRACE("getTempFiles=" . $filename);
        }
    }

    closedir($handler);
    return $files;
}

function checkPicExtension($extension)
{
    $array = ["jpg", "JPG", "png", "PNG", "bmp", "BMP"];
    if (in_array($extension, $array)) {
        return true;
    }
    return false;
}

function checkPicDetectExtension($extension)
{
    $array = ["jpg-detect", "JPG-detect", "png-detect", "PNG-detect"];
    if (in_array($extension, $array)) {
        return true;
    }
    return false;
}

function checkEndsWithDetect($extension)
{
    $needle = "-detect";
    $length = strlen($needle);
    return substr($extension, -$length) === $needle;
}

function getJpgFiles($allFiles, &$fileResults, &$totalCnt) {
    $faceFiles = array();
    $totalCnt = 0;

    foreach ($allFiles as $file) {
        $extension = pathinfo($file, PATHINFO_EXTENSION);
        ++$totalCnt;
        if (checkPicExtension($extension)) {
            $faceFiles[] = $file;
        } else {
            $fileResult = array();
            $fileResult[FILE_NAME] = $file;
            $fileResult[IMPORT_RESULT] = false;
            $fileResult[IMPORT_ERR_MESSAGE] = 'this is not a picture';
            array_push($fileResults, $fileResult);
        }
    }

    return $faceFiles;
}

function checkFilesBeforeDetect($uniqueID, $extractPath, $allFiles, &$fileResults, &$totalCnt)
{
    $faceFiles = array();
    $totalCnt = 0;

    foreach ($allFiles as $file) {
        ++$totalCnt;

        //检查后缀，只能选择jpg/png/bmp格式的图片
        $extension = pathinfo($file, PATHINFO_EXTENSION);
        if (!checkPicExtension($extension)) {
            $fileResult = array();
            $fileResult[FILE_NAME] = $file;
            $fileResult[IMPORT_RESULT] = UPLOAD_FACEPIC_ERROR_WRONG_FORMAT;
            $fileResult[IMPORT_ERR_MESSAGE] = 'File format error';
            array_push($fileResults, $fileResult);
            recordDetailDBStat($uniqueID, $fileResult);
            continue;
        }
        //检查文件大小
        $size = filesize($extractPath. "/" .$file);
        if ($size > FACE_FILE_SIZE_LIMIT) {
            $fileResult = array();
            $fileResult[FILE_NAME] = $file;
            $fileResult[IMPORT_RESULT] = UPLOAD_FACEPIC_ERROR_FILE_LARGE;
            $fileResult[IMPORT_ERR_MESSAGE] = 'The file is too larger.';
            array_push($fileResults, $fileResult);
            recordDetailDBStat($uniqueID, $fileResult);
            continue;
        }
        //检查文件像素
        list($width, $height) = getimagesize($extractPath. "/" .$file);
        if ($width < 250 || $height < 250) {
            $fileResult = array();
            $fileResult[FILE_NAME] = $file;
            $fileResult[IMPORT_RESULT] = UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION;
            $fileResult[IMPORT_ERR_MESSAGE] = 'Resolution is too low.';
            array_push($fileResults, $fileResult);
            recordDetailDBStat($uniqueID, $fileResult);
            continue;
        }

        $faceFiles[] = $file;
    }

    return $faceFiles;
}

function getEmployeeAccountInfoByFile($fileName, $community, $creatorUUID, &$errMsg)
{
    global $cLog;
    $cLog->TRACE("handleAFile begin:" . $fileName);
    $fileNameAttr = explode(".", $fileName);
    if (count($fileNameAttr) != 2) {
        $errMsg = "File name is error.";
        return false;
    }

    $employeeID = $fileNameAttr[0];
    $file_format = $fileNameAttr[1];
    $personalAccountInfo = getOfficeAccount($employeeID, $community);
    $cLog->TRACE("getPersonalAccount:" . json_encode($personalAccountInfo));
    if (!$personalAccountInfo) {
        $errMsg = "Get PersonalAccount Info error.";
        return false;
    }
    // if (!$personalAccountInfo["Active"]) {
    //     $errMsg = "The PersonalAccount is not active.";
    //     return false;
    // }
    $personalAccountInfo['CreatorUUID'] = $creatorUUID;

    $cLog->TRACE("personalAccountInfo=" . json_encode($personalAccountInfo));
    return $personalAccountInfo;
}

function handleEmployeePic($fileInfo, $personalAccountInfo, &$errMsg, &$notifyArray)
{
    global $cLog;
    $cLog->TRACE("fileInfo=" . json_encode($fileInfo));

    $mngAccountID = $personalAccountInfo['ParentID'];
    if (!mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)) {
        $cLog->TRACE("mergeFaceMng falied!");
        $errMsg = "DB error when write table FaceMng";
        return false;
    }

    $notifyArray[] = $personalAccountInfo['Account'];
    $cLog->TRACE("handleAFile sucess.");
    return true;
}

/**
 * 文件格式：building name+APT Number+resident's name
 * @global type $cLog
 * @param type $extractPath
 * @param type $fileName
 * @param type $mngAccountID
 * @param array $notifyArray
 * @return boolean
 */
function getOldCommAccountInfoByFile($fileName, $community, $creatorUUID, &$errMsg)
{
    global $cLog;
    $cLog->TRACE("handleAFile begin:" . $fileName);
    $fileNameAttr = preg_split("/[+.]+/", $fileName);
    $cLog->TRACE("preg_split:" . json_encode($fileNameAttr));
    if (count($fileNameAttr) != 4) {
        $errMsg = "File name is error.";
        return false;
    }

    $buildingName = $fileNameAttr[0];
    $roomName = $fileNameAttr[1];
    $name = $fileNameAttr[2];
    $file_format = $fileNameAttr[3];
    if ($name == "") {
        $errMsg = "Resident's name is empty";
        return false;
    }
    $personalAccountInfo = getPersonalAccount($roomName, $name, $community, $buildingName);

    if (!$personalAccountInfo) {
        $errMsg = "Get PersonalAccount Info error.";
        return false;
    }
    // if (!$personalAccountInfo["Active"]) {
    //     $errMsg = "The PersonalAccount is not active.";
    //     return false;
    // }
    $personalAccountInfo['CreatorUUID'] = $creatorUUID;

    $cLog->TRACE("getPersonalAccount:" . json_encode($personalAccountInfo));
    return $personalAccountInfo;
}
function handleAFile($fileInfo, $personalAccountInfo, &$errMsg, &$notifyArray)
{
    global $cLog;  
    $cLog->TRACE("fileInfo=" . json_encode($fileInfo));

    $mngAccountID = $personalAccountInfo['ParentID'];

    if (!mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)) {
        $cLog->TRACE("mergeFaceMng falied!");
        $errMsg = "DB error when write table FaceMng";
        return false;
    }

    $notifyArray[] = $personalAccountInfo['Account'];

    $cLog->TRACE("handleAFile sucess.");
    return true;
}

function getCommAccountInfoByFile($fileName, $community, $creatorUUID, &$errMsg, &$personalAccountInfo)
{
    global $cLog;
    $cLog->TRACE("handleAFile begin:" . $fileName);
    $fileNameAttr = preg_split("/[+.]+/", $fileName);
    $cLog->TRACE("preg_split:" . json_encode($fileNameAttr));
    if (count($fileNameAttr) != 4) {
        $errMsg = "File name is error.";
        return UPLOAD_FACEPIC_ERROR_WRONG_NAME;
    }

    $buildingName = $fileNameAttr[0];
    $roomName = $fileNameAttr[1];
    $name = $fileNameAttr[2];
    $file_format = $fileNameAttr[3];
    if ($name == "") {
        $errMsg = "Resident's name is empty";
        return UPLOAD_FACEPIC_ERROR_EMPTY_NAME;
    }

    $personalAccountInfo = getPersonalAccount($roomName, $name, $community, $buildingName);
    if (!$personalAccountInfo) {
        $errMsg = "Get PersonalAccount Info error.";
        return UPLOAD_FACEPIC_ERROR_NO_ACCOUNT_INFO;
    }
    $personalAccountInfo['CreatorUUID'] = $creatorUUID;    

    $cLog->TRACE("personalAccountInfo=" . json_encode($personalAccountInfo));
    return 0;
}
function handleAFileV65($fileInfo, $personalAccountInfo, &$errMsg, &$notifyArray)
{
    global $cLog;
    $cLog->TRACE("fileInfo=" . json_encode($fileInfo));

    $mngAccountID = $personalAccountInfo['ParentID'];


    if (!mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)) {
        $cLog->TRACE("mergeFaceMng falied!");
        $errMsg = "DB error when write table FaceMng";
        return UPLOAD_FACEPIC_ERROR_SYSTEM;
    }

    $notifyArray[] = $personalAccountInfo['Account'];

    $cLog->TRACE("handleAFile sucess.");
    return UPLOAD_FACEPIC_SUCCESS;
}

function updateFaceMng($personalAccountInfo, $fileInfo, $faceMngID)
{
    global $cLog;
    $cLog->TRACE("updateFaceMng begin");
    $faceUrl = $fileInfo[FACE_FILE_URL];
    $fileName = $fileInfo[FILE_NAME];
    $faceMD5 = $fileInfo[FACE_FILE_MD5];
    $creatorType = FACE_CREATOR_TYPE_PM;
    $creatorUUID = $personalAccountInfo['CreatorUUID'];

    global $db;
    try {
        $db->exec2ListWArray("update FaceMng set FaceUrl = :FaceUrl, FileName = :FileName, FaceMD5 = :FaceMD5, CreatorType = :CreatorType, CreatorUUID = :CreatorUUID where  ID = :ID", [":FaceUrl"=>$faceUrl,":FileName"=>$fileName,":ID"=>$faceMngID,":FaceMD5"=>$faceMD5,":CreatorType"=>$creatorType,":CreatorUUID"=>$creatorUUID ]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("updateFaceMng success.");
    return true;
}

function insertFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)
{
    global $cLog;
    $cLog->TRACE("insertFaceMng begin");
    $fileName = $fileInfo[FILE_NAME];
    $faceUrl = $fileInfo[FACE_FILE_URL];
    $faceMD5 = $fileInfo[FACE_FILE_MD5];
    $node = $personalAccountInfo['Node'];
    $unitID = $personalAccountInfo['UnitID'];
    $personalAccountID = $personalAccountInfo['PersonalAccountID'];
    $creatorType = FACE_CREATOR_TYPE_PM;
    $creatorUUID = $personalAccountInfo['CreatorUUID'];
    
    global $db;
    try {
        $db->insert2List("FaceMng", [":MngAccountID"=>$mngAccountID,":UnitID"=>$unitID ,":PersonalAccountID"=>$personalAccountID,":FileName"=>$fileName,":FaceUrl"=>$faceUrl,":Node"=>$node,":FaceMD5"=>$faceMD5,":CreatorType"=>$creatorType,":CreatorUUID"=>$creatorUUID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("insertFaceMng success.");
    return true;
}

function mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)
{
    global $cLog;
    $cLog->TRACE("mergeFaceMng begin");
    global $db;
    try {
        $data = $db->querySList("select ID,FaceUrl from FaceMng where  PersonalAccountID = :PersonalAccountID", [":PersonalAccountID"=>$personalAccountInfo['PersonalAccountID'] ])[0];
        if ($data) {
            //清除之前的人脸图片
            fdfs_del_pic_by_url($data['FaceUrl']);
            updateFaceMng($personalAccountInfo, $fileInfo, $data['ID']);
        } else {
            insertFaceMng($personalAccountInfo, $fileInfo, $mngAccountID);
        }
        //删除本地文件
        if (unlink($fileInfo[FILE_FULL_NAME])) {
            $cLog->TRACE("remove file:" . $fileInfo[FILE_FULL_NAME] . " success!");
        } else {
            $cLog->TRACE("remove file:" . $fileInfo[FILE_FULL_NAME] . " failed!");
        }
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("mergeFaceMng success.");
    return true;
}

function uploadFaceNotify($userConf)
{
    $role = $userConf['Role'];
    $changeType = 0;
    $mac = "";
    $node = $userConf['Node'];
    if ($role == 10 || $role == 11) {  //个人主账号,个人从账号
        $changeType = \model\notify\WEB_PER_UPLOAD_FACE_PIC;
        $insatllID = 0;
        webPersonalModifyNotify($changeType, $node, $mac, $insatllID);
    } elseif ($role == 20 || $role == 21) { //社区主账号,社区从账号
        $changeType = \model\notify\WEB_COMM_UPLOAD_FACE_PIC;
        $communitid = $userConf['MngID'];
        $unitid = $userConf['UnitID'];
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    } else {
        return;
    }
}

function getOfficeAccountByAccount($account)
{
    global $db;
    $accountInfo = $db->querySList("SELECT ID,Role,ParentID,UnitID,SipType,Codec,Active FROM PersonalAccount  WHERE Account = :account", [":account"=>$account])[0];

    $data['NodeID'] = $accountInfo['ID'];
    $data['Node'] = $account;
    $data['MngID'] = $accountInfo['ParentID'];
    $data['UnitID'] = $accountInfo['UnitID'];
    $data['UserAccount'] = $account;
    $data['PersonalAccountID'] = $accountInfo['ID'];
    $data['Role'] = $accountInfo['Role'];
    $data['SipType'] = $accountInfo['SipType'];
    $data['Codec'] = $accountInfo['Codec'];
    $data['Active'] = $accountInfo['Active'];

    return $data;
}

function getPersonalAccountByAccount($account)
{
    global $db;
    $accountInfo = $db->querySList("SELECT ID,Role,ParentID,UnitID,SipType,Codec,Active FROM PersonalAccount  WHERE Account = :account", [":account"=>$account])[0];

    if ($accountInfo['Role'] == '10' || $accountInfo['Role'] == '20') { //社区、个人主账号
        $data['NodeID'] = $accountInfo['ID'];
        $data['Node'] = $account;
        $data['MngID'] = $accountInfo['ParentID'];
    } else { //从账号 再查主账号
        $resultAccount = $db->querySList("select ID,Account,ParentID from PersonalAccount where ID = :ID", [":ID"=>$accountInfo['ParentID'] ])[0];
        $data['NodeID'] = $resultAccount['ID'];
        $data['Node'] = $resultAccount['Account'];
        $data['MngID'] = $resultAccount['ParentID'];
    }
    $data['UnitID'] = $accountInfo['UnitID'];
    $data['UserAccount'] = $account;
    $data['PersonalAccountID'] = $accountInfo['ID'];
    $data['Role'] = $accountInfo['Role'];
    $data['SipType'] = $accountInfo['SipType'];
    $data['Codec'] = $accountInfo['Codec'];
    $data['Active'] = $accountInfo['Active'];

    return $data;
}

function faceDetectOneFile($fileName)
{
    global $cLog;
    $command = '/usr/local/akcs/csadapt/bin/FaceDetect DetectFile ' . $fileName;
    $result = exec($command);
    $cLog->TRACE("faceDetect exec command=[" . $command . "],result=" . $result);
    return $fileName . '-detect';
}

function getRelativePath($mngAccountID, $node)
{
    return '/' . (int) $mngAccountID % 100 . '/' . $mngAccountID . '/' . (int) $node % 64;
}

function getParentNode($parentID)
{
    global $cLog;
    $cLog->TRACE("getParentNode begin,parentID=" . $parentID);
    global $db;
    $data = $db->querySList("select P.ParentID, P.Account, b.RoomName, u.UnitName from PersonalAccount P "
            . "left join CommunityRoom b on P.RoomID = b.ID "
            . "left join CommunityUnit u on b.UnitID = u.ID where P.ID =:ID", [":ID"=>$parentID])[0];
    $cLog->TRACE("getParentNode end" . json_encode($data));
    return $data;
}

function queryPersonalAccount($personalAccountID)
{
    global $cLog;
    $cLog->TRACE("queryPersonalAccount begin,PersonalAccountID=" . $personalAccountID);
    global $db;
    try {
        $data = $db->querySList("select P.Account, P.Role, P.UnitID, P.ParentID CommunitID, PP.ParentID as ParentID,PP.Account as ParentAccount from PersonalAccount P left join PersonalAccount PP on P.ParentID=PP.ID  where P.ID=:ID", [":ID"=>$personalAccountID])[0];
        $cLog->TRACE("queryPersonalAccount result:" . json_encode($data));
        if ($data) {
            if ($data['Role'] == 21 || $data['Role'] == 11) { //社区从账号，个人从账号
                $data['Node'] = $data['ParentAccount'];
                $data['CommunitID'] = $data['ParentID'];
            } else {
                $data['Node'] = $data['Account'];
            }
        }
        return $data;
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return null;
    }
}

function getOfficeAccount($employeeID, $community)
{
    global $cLog;
    $cLog->TRACE("getOfficeAccount begin,emplyeeID=$employeeID,communitId=$community");
    global $db;
    try {
        $data = $db->querySList("select a.ID PersonalAccountID,a.Account,a.Account Node,a.Role,a.UnitID,a.ParentID,a.Active from PersonalAccount a "
                . " join PersonalAccountOfficeInfo b on a.UUID = b.PersonalAccountUUID "
                . " and b.EmployeeID = :EmployeeID and a.ParentID = :communityID", [":EmployeeID"=>$employeeID, ":communityID"=>$community])[0];
        return $data;
    } catch (Exception $ex) {
        $cLog->TRACE("db exception=" . $ex->getMessage());
        return null;
    }
}

function getPersonalAccount($roomName, $name, $community, $buildingName)
{
    global $cLog;
    $cLog->TRACE("getPersonalAccountInfo begin,roomName=" . $roomName . ';name=' . $name . ';buildingName=' . $buildingName);

    // name加密
    $encryptName = \share\service\AesForData::getInstance()->encrypt($name);

    global $db;
    try {
        $datas = $db->querySList("select a.ID PersonalAccountID,a.Account,a.Role,a.UnitID,a.ParentID,b.RoomName,a.Active,u.UnitName from PersonalAccount a "
                . "left join CommunityRoom b on a.RoomID = b.ID "
                . "left join CommunityUnit u on b.UnitID = u.ID "
                . "where a.Name = :name", [":name"=>$encryptName]);
        $cLog->TRACE("getPersonalAccountInfo result:" . json_encode($datas));
        if ($datas && count($datas) > 0) {
            foreach ($datas as $row => $data) {
                if ($data['Role'] == 21 || $data['Role'] == 11) { //社区从账号
                    $parent = getParentNode($data['ParentID']);
                    if ($parent) {
                        $data['Node'] = $parent['Account'];
                        $data['ParentID'] = $parent['ParentID'];
                        $data['RoomName'] = $parent['RoomName'];
                        $data['UnitName'] = $parent['UnitName'];
                    } else {
                        $cLog->TRACE("getParentNode failed,ID=" . $data['ParentID']);
                        continue;
                    }
                } else {
                    $data['Node'] = $data['Account'];
                }

                if ($data['ParentID'] == $community && $data['RoomName'] == $roomName && $data['UnitName'] == $buildingName) {
                    return $data;
                }
            }
        }
        return null;
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return null;
    }
}

function queryOfficeNotifyInfo($personalAccountIDs)
{
    $notifyArray = array();
    foreach ($personalAccountIDs as $personalAccountID) {
        $personalAccountInfo = queryPersonalAccount($personalAccountID);
        if ($personalAccountInfo) {
            $account = $personalAccountInfo['Account'];
            $officeid = $personalAccountInfo['CommunitID'];
            $notifyArray[] = $account. '_' . $officeid;
        }
    }

    return $notifyArray;
}

function queryNotifyInfo($personalAccountIDs)
{
    $notifyArray = array();
    foreach ($personalAccountIDs as $personalAccountID) {
        $personalAccountInfo = queryPersonalAccount($personalAccountID);
        if ($personalAccountInfo) {
            $node = $personalAccountInfo['Node'];
            $communitid = $personalAccountInfo['CommunitID'];
            $unitid = $personalAccountInfo['UnitID'];
            $account = $personalAccountInfo['Account'];
            $notifyArray[] = $node . '_' . $communitid . '_' . $unitid. '_' . $account;
        }
    }
    return $notifyArray;
}

function faceNotify($notifyArray, $changeType)
{
    global $cLog;
    $cLog->TRACE("faceNotify begin:" . json_encode($notifyArray));
    if (count($notifyArray) == 0) {
        $cLog->TRACE("faceNotify end, count is 0");
        return;
    }

    //去重操作
    $notifyArray = array_flip($notifyArray);
    $notifyArray = array_flip($notifyArray);
    $cLog->TRACE("faceNotify flip:" . json_encode($notifyArray));

    $mac = "";
    foreach ($notifyArray as $notify) {
        $singleArray = explode("_", $notify);
        $node = $singleArray[0];
        $communitid = $singleArray[1];
        $unitid = $singleArray[2];
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    }
    $cLog->TRACE("faceNotify end.");
}

function getStaffAccountInfoByUUID($staffUUID)
{
    global $db;
    $accountInfo = $db->querySList("SELECT ID,CommunityID,Version,FaceUrl,FaceMD5 FROM Staff WHERE UUID = :uuid", [":uuid"=>$staffUUID])[0];

    $data['UUID'] = $staffUUID;
    $data['NodeID'] = $accountInfo['ID'];
    $data['MngID'] = $accountInfo['CommunityID'];
    $data['FaceUrl'] = $accountInfo['FaceUrl'];
    $data['FaceMD5'] = $accountInfo['FaceMD5'];

    return $data;
}

function updateStaffFaceInfo($userInfo, $fileInfo)
{
    global $cLog;
    $cLog->TRACE("updateStaffFaceInfo begin");

    //删除fdfs中的旧图片
    fdfs_del_pic_by_url($userInfo['FaceUrl']);

    //删除本地图片
    if (unlink($fileInfo[FILE_FULL_NAME])) {
        $cLog->TRACE("remove file:" . $fileInfo[FILE_FULL_NAME] . " success!");
    } else {
        $cLog->TRACE("remove file:" . $fileInfo[FILE_FULL_NAME] . " failed!");
    }

    //更新Staff人脸相关字段
    global $db;
    $faceUrl = $fileInfo[FACE_FILE_URL];
    $fileName = $fileInfo[FILE_NAME];
    $faceMD5 = $fileInfo[FACE_FILE_MD5];
    $staffUUID = $userInfo['UUID'];
    try {
        $db->exec2ListWArray("update Staff set FaceUrl = :FaceUrl, FileName = :FileName, FaceMD5 = :FaceMD5 where UUID = :UUID", [":FaceUrl"=>$faceUrl,":FileName"=>$fileName,":FaceMD5"=>$faceMD5,":UUID"=>$staffUUID]);
    } catch (PDOException $e) {
        $cLog->TRACE("db exception=" . $e->getMessage());
        return false;
    }

    $cLog->TRACE("updateStaffFaceInfo success.");
    return true;
}

function moveFileToDownloadpath(&$fileInfo, $extractPath, $filePath, $fileName)
{
    mkDirs($extractPath);
    $fileInfo[FILE_NAME] = $fileName;
    $fileInfo[FILE_FULL_NAME] = $extractPath . '/' . $fileName;
    $fileInfo[FILE_ENC_NAME] = $extractPath . '/' . $fileName . '.enc';
    rename($filePath . '' . $fileName, $fileInfo[FILE_FULL_NAME]);
    chown($fileInfo[FILE_FULL_NAME], 'nobody');
    chgrp($fileInfo[FILE_FULL_NAME], 'nobody');
}

function isValidFlieFormat(&$fileInfo, $extractPath, $fileName, $account)
{
    global $cLog;
    //检查后缀，只能选择jpg/png/bmp格式的图片
    $extension = pathinfo($fileInfo[FILE_FULL_NAME], PATHINFO_EXTENSION);
    if (!checkPicExtension($extension)) {
        $cLog->TRACE("File format error, extension is $extension");
        unlink($fileInfo[FILE_FULL_NAME]);
        return UPLOAD_FACEPIC_ERROR_WRONG_FORMAT;
    }
    //检查图片大小
    $size = filesize($fileInfo[FILE_FULL_NAME]);
    if ($size > FACE_FILE_SIZE_LIMIT) {
        $cLog->TRACE("The file is too larger.");
        unlink($fileInfo[FILE_FULL_NAME]);
        return UPLOAD_FACEPIC_ERROR_FILE_LARGE;
    }
    //检查图片像素
    list($width, $height) = getimagesize($fileInfo[FILE_FULL_NAME]);
    if ($width < 250 || $height < 250) {
        $cLog->TRACE("Resolution is too low.");
        unlink($fileInfo[FILE_FULL_NAME]);
        return UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION;
    }

    //检测人脸
    $uploadRet = faceUpload($fileInfo[FILE_FULL_NAME], $account);
    $innerCode = $uploadRet ? $uploadRet['code'] : UPLOAD_FACEPIC_ERROR_SYSTEM;
    $outErrorInfo = convertOutFaceErrorInfo($innerCode);
    $result = $outErrorInfo['code'];
    $errmsg = $outErrorInfo['msg'];
    if ($result != UPLOAD_FACEPIC_SUCCESS) {
        $cLog->TRACE("faceUpload Error, result:".$errmsg);
        unlink($fileInfo[FILE_FULL_NAME]);
        return $result;
    }

    $fileInfo[FACE_FILE_MD5] = $uploadRet['data']['face_file_md5'];
    $fileInfo[FACE_FILE_URL] = $uploadRet['data']['face_file_url'];
    $cLog->TRACE("fileInfo=" . json_encode($fileInfo));

    if (!is_file($fileInfo[FILE_FULL_NAME])) {
        $cLog->TRACE("Please upload face image");
        unlink($fileInfo[FILE_FULL_NAME]);
        return $result;
    }

    return 0;
}

function encryptImageFile(&$fileInfo, $faceEncFileRelativePath, $account)
{
    global $cLog;
    $aes = new MYAES128CBC();
    $key = $aes->getKey($account);
    $cLog->TRACE("encryptFile key=" . $key);
    $ret = $aes->encryptFile($fileInfo[FILE_FULL_NAME], $key, $fileInfo[FILE_ENC_NAME]);
    if (!$ret) {
        $cLog->TRACE("encryptFile:" . $fileInfo[FILE_FULL_NAME] . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return false;
    } 

    $fileInfo[FACE_FILE_MD5] = md5_file($fileInfo[FILE_ENC_NAME]);
    $fileInfo[FACE_FILE_URL] = $faceEncFileRelativePath . '/' . $fileInfo[FACE_FILE_MD5] . '.jpg';
    $faceEncFilePath = FACE_FILE_PREFIX . $fileInfo[FACE_FILE_URL];
    if (!rename($fileInfo[FILE_ENC_NAME], $faceEncFilePath)) {
        $cLog->TRACE("rename File:" . $fileInfo[FILE_ENC_NAME] . " to File:" . $faceEncFilePath . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return false;
    }
    chown($faceEncFilePath, 'nobody');
    chgrp($faceEncFilePath, 'nobody');
    $cLog->TRACE("fileInfo=" . json_encode($fileInfo));
    
    return true;
}