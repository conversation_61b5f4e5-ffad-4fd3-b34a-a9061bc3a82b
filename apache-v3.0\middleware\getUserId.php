<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 16:08:34
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/computed.php";
class CGetUserId implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        $cLog->actionLog("#middle#getUserId#");
        \util\computed\setGAppData(["userId"=>$gApp["userId"],"user"=>$gApp["user"]]);
        $next();
    }
}