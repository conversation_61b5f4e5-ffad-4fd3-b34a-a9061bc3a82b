<?php
/**
 * @description:
 * @author: csc 2023/12/15 13:52 V6.7.1
 * @lastEditors: csc 2023/12/15 13:52 V6.7.1
 */

namespace package\community\model\subscription\src;

use package\community\model\subscription\config\Code;

trait Add
{
    /**
     * @description: 创建订阅
     * @param {array} Users 续费的用户列表
     * @param {int} IntervalType 周期类型 0:月 1:季 2:年 3:天
     * @param {int} Cycles 周期数 0：不限
     * @param {string} StartTime 开始时间
     * @param {string} TotalPrice 总价
     * @param {string} SubscriptionUUID 旧订阅的订阅ID，有旧的订阅ID代表着编辑（取消后新建）
     * @return void
     * @throws \Exception
     * @author: csc 2023/12/15 14:14 V6.7.1
     * @lastEditors: csc 2023/12/15 14:14 V6.7.1
     */
    public function createSubscription()
    {
        //获取参数
        $params = ['Users:object', 'IntervalType:enum("0","1","2","3")', 'Cycles:between-with-scope("0","180")', 'StartTime?:string', 'TotalPrice:string',
            'SubscriptionUUID?:uuid', 'userId', 'role', PROXY_ROLE['installerUUID'], 'VideoSites:object', 'ThirdLockUUIDs?:object'];
        list($users, $intervalType, $cycles, $startTime, $totalPrice, $subscriptionUUID, $userId, $role, $insUUID, $videoSites, $thirdLockUUIDs) = $this->getParams($params);


        $this->loadUtil('account', true);
        $this->loadUtil('order', true);

        $usersArr = json_decode($users, true);
        $videoSitesArr = json_decode($videoSites, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);
        $usersInfo = $this->utils->_common->order->getSubscribeUserInfo($usersArr);
        $videoStorageInfo = $this->utils->_common->order->getSubscribeVideoStorageInfo($videoSitesArr, PAY_TYPE_MULTIPLE);
        $thirdLockInfo = $this->utils->_common->order->getSubscribeThirdLockInfo($thirdLockUUIDsArr, PAY_TYPE_MULTIPLE);
        if (empty($usersInfo['all']) && empty($videoStorageInfo['site']) && empty($thirdLockInfo['site'])) {
            $this->output->echoErrorMsg(STATE_PARAMS_ERROR, ['externalErrorObj' => Code::EXT_STATE_USER_NOT_EXIST]);
        }

        $payerData = $this->utils->_common->account->getManagerInfo($userId);
        $payerUUID = $payerData['UUID'];

        if (in_array($role, R_PROJECT_ROLE)) {
            //ins支付的记录ins的UUID
            $payerUUID = $insUUID;
        }
        $this->loadUtil('subscription', true);
        $payerType = $this->utils->_common->subscription->getPayerType($role);

        $projectIds = [];
        if (!empty($usersInfo['all'])) {
            $projectIds = $this->utils->_common->order->checkPayPermission($usersInfo, $userId, $payerType);
        }
        //校验视频存储支付权限
        if (!empty($videoStorageInfo['site'])){
            $projectIds = array_merge($projectIds, array_column($videoStorageInfo['site'],'ID'));
            $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $userId, $payerType);
        }
        //校验三方锁支付权限
        if (!empty($thirdLockInfo['site'])){
            $thirdLockProjectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $userId, $payerType);
            $projectIds = array_merge($projectIds, $thirdLockProjectIds);
        }

        //检测是否同一时区
        $projectIds = array_unique($projectIds);
        $this->utils->_common->order->checkProjectInSameTimeZone($projectIds);

        //转换时区为东八区
        $projectData = $this->utils->_common->account->getManagerInfo($projectIds[0]);
        $startTime = $this->share->util->setTimeZone($startTime, $projectData['TimeZone'], 3, '-');

        //设置Type等参数
        $this->loadModel('subscription', true, ['dataContainer'=> [
            'Type' => SUBSCRIBE_TYPE['communityRenew'],
            'StartTime' => $startTime,
            'Users' => $usersInfo,
            'TimeZone' => $projectData['TimeZone'],
            'PayerUUID' => $payerUUID,
            'PayerType' => $payerType,
            'VideoStorageInfo' => $videoStorageInfo,
            'ThirdLockInfo' => $thirdLockInfo,
            'IntervalType' => $intervalType
        ]]);
        $result = $this->models->_common->subscription->createSubscription();

        return ['data' => ['ID' => $result['ID'], 'UUID' => $result['UUID'], 'bmurl' => $result['bmUrl']]];
    }

    /**
     * @Author: chenpl
     * @Description: 创建订阅的对象为RentManagerCustomer; customer = company + INS
     * @Params:
     * @Return:
     * @Date: 2024/11/12
     */
    public function createSubscriptionForRentManager()
    {
        $params = ['Cycles:between-with-scope("0","180")', 'SubscriptionUUID?:uuid', 'RentManagerCustomerList:string-required', 'TotalPrice', 'userId', PROXY_ROLE['distributorUUID'], PROXY_ROLE['installerUUID']];
        list($cycles, $subscriptionUUID, $rentManagerCustomerList, $totalPrice, $userId, $disUUID, $insUUID) = $this->getParams($params);
        $rentManagerCustomerList = explode(",", $rentManagerCustomerList);
        //参数校验
        if (empty($rentManagerCustomerList)) {
            $this->output->echoErrorMsg(STATE_USER_NOT_EXITS, ['externalErrorObj' => Code::EXT_STATE_RENT_MANAGER_COMPANY_IS_EMPTY]);
        }

        // 校验支付权限（校验这些rentManagerCompany是否都是在支付的这个INS下）
        $this->loadUtil('order', false);
        $this->utils->order->checkRentManagerPayPermission($rentManagerCustomerList, $userId, $insUUID);

        //获取当前付费的INS用户的时区
        $this->loadUtil('account', true);
        $timeZone = $this->utils->_common->account->accountSelectByKey('ID', $userId)[0]['TimeZone'];

        //设置Type等参数
        $this->loadModel('subscription', true, ['dataContainer'=> [
            'Cycles' => $cycles,
            'TotalPrice' => $totalPrice,
            'SubscriptionUUID' => $subscriptionUUID,
            'PayerUUID' => $insUUID,
            'TimeZone' => $timeZone,
            'RentManagerCustomerList' => $rentManagerCustomerList,
            'DistributorUUID' => $disUUID,
            'InstallerUUID' => $insUUID
        ]]);
        $result = $this->models->_common->subscription->createSubscriptionForRentManager();

        return ['data' => ['ID' => $result['ID'], 'UUID' => $result['UUID'], 'Bmurl' => $result['bmUrl']]];
    }
}

