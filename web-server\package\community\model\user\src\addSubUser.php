<?php

namespace package\community\model\user\src;

use package\community\model\user\config\Code;
use Exception;

trait AddSubUser
{
    /**
     * @msg: 添加社区从账户
     * @services: sip,charge(在getSubActive中所引用)
     * @param ID指主账号ID
     */
    public function addComSubUser()
    {
        $params = [
            PROXY_ROLE['mainUserId'],
            'FirstName',
            'LastName',
            'Name',
            'MobileNumber',
            'Email',
            'PhoneCode',
            'Phone',
            'Phone2',
            'Phone3',
            'IsAdmin',
            PROXY_ROLE['projectId'],
            'AccessFloor',
            'Remark',
            'DormakabaRfId'
        ];
        list(
            $mainUserId,
            $firstName,
            $lastName,
            $name,
            $mobile,
            $email,
            $phoneCode,
            $phone,
            $phone2,
            $phone3,
            $isAdmin,
            $communityId,
            $accessFloor,
            $remark,
            $dormakabaRfId
        ) = $this->getParams($params);
        $this->log->debug('mainUserId={mainUserId}', ["mainUserId" => $mainUserId]);

        // 1.用户相关信息获取及检测
        // 默认创建从账号通知家居

        // V6.5.2.5 access floor兼容其他入口传值，例如openapi
        $accessFloor = $accessFloor === null ? '' : $accessFloor;
        $accessFloor = $this->utils->self->checkAccessFloor($accessFloor);

        // 获取主账号信息
        $this->loadUtil('account', true);
        $mainData = $this->utils->_common->account->getUserInfo($mainUserId);
        // 根据主账号CallType判断落地
        $mainUserCallType = intval($this->utils->_common->account->getUserInfo($mainUserId, 'conf')['CallType']);
        // 创建从账号前提是主账号已经激活完成以及房间已住人
        if ($mainData['Active'] === '0' || $mainData['Special'] === '1') {
            $this->output->echoErrorMsg(STATE_ACTIVE_FAMILY_ACCOUNT, ['externalErrorObj' => Code::EXT_STATE_MAIN_USER_NOT_ACTIVE]);
        }
        $this->loadUtil('common', true);
        $familyMemberNumber = $this->dao->personalAccount->selectByArray([['ParentID', $mainUserId], ['Role', COMENDSROLE]], 'count(*)')[0]['count(*)'];
        // 检查从账号数目
        $this->loadUtil('account', true);
        $this->utils->_common->account->checkSubNumber($familyMemberNumber);
        // 6.4 增加ParentUUID字段
        $mainUUID = $mainData['UUID'];

        // 获取小区信息
        $this->loadUtil('common', true);
        $community = $this->utils->_common->common->getAccountInfoWithID(
            $communityId,
            ["TimeZone", "CreateTime", "CustomizeForm", "ManageGroup","UUID"]
        );


        $now = $this->share->util->getNow();
        $password = $this->share->util->generatePw(8);
        $userInfoUUID = $this->share->util->uuid(\dao\PersonalAccountUserInfo::UUID_PREFIX);
        $userInfo = [
            'Role' => COMENDSROLE,
            'ParentID' => $mainUserId,
            'FirstName' => $firstName,
            'LastName' => $lastName,
            'Name' => $name,
            'Phone' => $phone,
            'Phone2' => $phone2,
            'Phone3' => $phone3,
            'PhoneCode' => $phoneCode,
            'Language' => $mainData['Language'],
            'RoomID' => 0,
            'UnitID' => $mainData['UnitID'],
            'CommunityUnitUUID' => $mainData['CommunityUnitUUID'],
            'ParentUUID' => $mainUUID,
            'EnableIpDirect' => 1,
            'PhoneStatus' => $mainUserCallType === 1 ? 1:0,
            'UserInfoUUID' => $userInfoUUID,
            'Email' => $email, // 需要给用户发送密码
            'Passwd' => $password
        ];
        $this->loadUtil('account', true);
        $data = $this->utils->_common->account->addUser($userInfo);
        $unitId = $mainData['UnitID'];
        $sip = $data['Sip'];
        $sipGroup = $data['SipGroup'];
        $sipPw = $data['SipPwd'];
        $uuid = $data['UUID'];

        if (!empty($dormakabaRfId)){
            $this->dao->dormakabaLockCardCode->insert([
                'UUID'=>$this->share->util->uuid(),
                'AccountUUID' => $community['UUID'],
                'PersonalAccountUUID' => $uuid,
                'Code' => $dormakabaRfId,
                'CreateType'=>1,
                'Type'=>1,
            ]);
        }
        // 添加终端用户信息表
        $this->dao->personalAccountUserInfo->insert(['Email' => $email, 'MobileNumber' => $mobile,
        'AppMainUserAccount' => $sip, 'Passwd' => $this->share->util->getSaltPwd($password), 'UUID' => $userInfoUUID, 'CreateTime' => $now]);
        // 设置自动激活激活时间
        $this->loadUtil('account', true);
        $activeData = $this->utils->_common->account->addAutoActive($data['ID']);
        // V6.4 创建从账号提示语
        $tipCode = $this->utils->_common->account->getTipForAddSub($activeData['Active'], $mainData, $mobile, $email, COMMUNITYGRADE);

        // 群响铃设置
        $this->loadProvider('sip');
        $res = $this->services->sip->add2Freeswish($sip, [
            'type' => 6,
            'node' => "1.$unitId.0.0.$mainUserId",
            'group' => $sipGroup,
            'community' => $communityId,
            'communityType' => 0,
            'enableGroup' => 1,//室内机群响铃
            'sip' => $sip,
            'passwd' => $sipPw
        ]);
        if (!$res) {
            $this->output->echoErrorMsg(STATE_SIP_STATUS, ['externalErrorObj' => Code::EXT_STATE_ADD_SIP_FAIL]);
        }
        // 移入移出群响铃
        $this->loadUtil('account', true);
        $this->utils->_common->account->setCallTypeGrouping($mainUserCallType, $data['Account']);

        // V6.5.2.5 新增社区用户PersonalAccountCommunityInfo的记录
        $remark = $remark == null ? '' : $remark;
        $this->dao->personalAccountCommunityInfo->insert([
            ':UUID'=>$this->share->util->uuid(),
            ':PersonalAccountUUID' => $uuid,
            ':AccessFloor' => $accessFloor,
            ':Remark' => $remark
        ]);

        // 记录用户操作到数据库
        $logInfo = [
            "mainUserId" => $mainUserId,
            "name" => $name,
            'mobile' => $mobile,
            "email" => $email,
            "sip" => $sip,
            "id" => $data['ID'],
        ];
        $this->utils->_common->account->addUserToLog($logInfo);

        // 智能家居，增加用户权限
        // $info = json_encode(['isAdmin' => $isAdmin]);
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        // $this->share->util->setSmartHomeTask(['Type' => 7, 'Key' => $sip, 'Info' => $info, 'RoomSip'=>$mainData['Account']]);

        // 通知家居新增账号
        $this->notifySmartHome->collect(['User', 'notifyCreateSubUser'], [$mainUUID], 6530);

        return ['ID' => $data['ID'], 'UUID' => $uuid, 'Active' => $activeData['Active'],
        'TipCode' => $tipCode,'account' => $email, 'passwd' => $data['Passwd'], 'MainID' => $mainUserId, 'Sip'=>$sip];
    }

    /**
     * @msg: App添加社区从账户,新增家庭从账户限制
     */
    public function addAppComSubUser()
    {
        $params = [
            PROXY_ROLE['mainUserId'],
            'FirstName',
            'LastName',
            'Name',
            'MobileNumber',
            'Email',
            'PhoneCode',
            'Phone',
            'Phone2',
            'Phone3',
            'IsAdmin',
            PROXY_ROLE['projectId'],
            'Remark'
        ];
        list($mainUserId, $firstName, $lastName, $name, $mobile, $email, $phoneCode, $phone, $phone2, $phone3, $isAdmin, $communityId, $remark) = $this->getParams($params);
        $familyMaster = $this->dao->personalAccount->selectByID($mainUserId, 'Account,ParentID')[0];
        $familyMasterCnf = $this->dao->personalAccountCnf->selectByAccount($familyMaster['Account'], 'AllowCreateSlaveCnt,Flags')[0];
        // APP/endUser添加从账号数目，用于高级功能限制数目
        $addedAppNumber = $this->dao->aPPSpecial->selectByKey('Node', $familyMaster['Account'], 'count(*)')[0]['count(*)'];
        $now = $this->share->util->getNow();
        // 高级功能过期时间
        $featureExpireTime = $this->dao->communityInfo->selectByAccountID($familyMaster['ParentID'], 'FeatureExpireTime')[0]['FeatureExpireTime'];
        $featureId = $this->dao->manageFeature->selectByKey('AccountID', $familyMaster['ParentID'], 'FeatureID')[0]['FeatureID'];
        $item = $this->dao->featurePlan->selectByID($featureId, 'Item')[0]['Item'];
        // 是否有高级功能
        $isFamilyLimit = $this->share->util->getSpecifyBitLE($item, 4);
        $sysMaxSubCount = $this->dao->systemExtremum->getSystemConfig()['MaxApps'] - 1;
        if ($familyMasterCnf['AllowCreateSlaveCnt'] != $sysMaxSubCount && $isFamilyLimit == 1 && $familyMasterCnf['Flags'] % 2 == 1 && strtotime($featureExpireTime) > strtotime($now)) {
            if (($addedAppNumber + 1) > $familyMasterCnf['AllowCreateSlaveCnt']) {
                $this->output->echoErrorMsg(STATE_FAMILY_MEMBER_BEYOND, ['externalErrorObj' => Code::EXT_STATE_ADD_SUB_USER_NUM_LIMIT]);
            }
        }
        $remark = $remark == null ? '' : $remark;
        $this->loadModel('user', false, [
            'dataContainer' => [
                PROXY_ROLE['mainUserId'] => $mainUserId,
                'FirstName' => $firstName,
                'LastName' => $lastName,
                'Name' => $name,
                'MobileNumber' => $mobile,
                'Email' => $email,
                'PhoneCode' => $phoneCode,
                'Phone' => $phone,
                'Phone2' => $phone2,
                'Phone3' => $phone3,
                'IsAdmin' => $isAdmin,
                PROXY_ROLE['projectId'] => $communityId,
                'Remark' => $remark
            ]
        ]);
        $subInfo = $this->models->user->addComSubUser();
        $subID = $subInfo["ID"];
        $subAccount = $this->dao->personalAccount->selectByID($subID. 'Account')[0]['Account'];
        $bindArray = [
            ':Node' => $familyMaster['Account'],
            ':Account' => $subAccount,
            ':CreateTime' => $now,
        ];
        $this->dao->aPPSpecial->insert($bindArray);
        $this->log->debug('bindArray={bindArray}', ["bindArray" => json_encode($bindArray)]);
        return $subInfo;
    }
}
