<?php
/*
 * @Description: 根据小区或Installer设置代理为Installer
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
use \interfaces\middleware\main\IMiddleware;
class CSetPCMngToInstaller implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        $userAliasId = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setPCMngToInstaller#");
        $data = $db->querySList("select A.Account,A.ID from Account A join Account B on A.ID = B.ManageGroup where B.ID = :ID",[":ID"=>$userAliasId])[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}