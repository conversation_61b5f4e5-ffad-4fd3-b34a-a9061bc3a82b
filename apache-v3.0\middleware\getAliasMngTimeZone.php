<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 17:40:20
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";

class CGetAliasMngTimeZone implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        
        $id = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $timeZone = $db->querySList("select TimeZone from Account where ID = :ID",[":ID"=>$id])[0]["TimeZone"];
        $cLog->actionLog("#middle#getAliasMngTimeZone#id=$id;timeZone=$timeZone");
        \util\computed\setGAppData(["SelfTimeZone"=>$timeZone]);
        $next();
    }
}