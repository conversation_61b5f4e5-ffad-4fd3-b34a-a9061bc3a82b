<?php

namespace model\notify;
//个人
/*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
const WEB_PER_NODE_UPDATE = 1000;
const WEB_PER_ADD_USER = 1001;
const WEB_PER_DEL_USER = 1002;
const WEB_PER_MODIFY_USER = 1003;
const WEB_PER_ADD_SLAVE_USER = 1004;
const WEB_PER_DEL_SLAVE_USER = 1005;
const WEB_PER_MODIFY_SLAVE_USER = 1006;
const WEB_PER_UPDATE_RF = 1007;
const WEB_PER_UPDATE_PIN = 1008;
const WEB_PER_ADD_DEV = 1009;
const WEB_PER_DEL_DEV = 1010;
const WEB_PER_MODIFY_DEV = 1011;
const WEB_PER_PHONE_PAY_SUCC = 1012;
const WEB_PER_UPLOAD_FACE_PIC = 1013;
const WEB_PER_UPDATE_TIMEZONE = 1015;
const WEB_PER_UPDATE_MAC_CONFIG = 1016;

//社区
/*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
const WEB_COMM_NODE_UPDATE = 2000;
const WEB_COMM_ADD_USER = 2001;
const WEB_COMM_DEL_USER = 2002;
const WEB_COMM_MODIFY_USER = 2003;
const WEB_COMM_ADD_SLAVE_USER = 2004;
const WEB_COMM_DEL_SLAVE_USER = 2005;
const WEB_COMM_MODIFY_SLAVE_USER = 2006;
const WEB_COMM_UPDATE_RF = 2007;
const WEB_COMM_UPDATE_PIN = 2008;
const WEB_COMM_ADD_DEV = 2009;
const WEB_COMM_DEL_DEV = 2010;
const WEB_COMM_MODIFY_DEV = 2011;
const WEB_COMM_UPLOAD_FACE_PIC = 2103;
const WEB_COMM_DEL_FACE_PIC = 2104; //网页删除人脸

const WEB_COMM_UPDATE_MAC_CONFIG = 2105;

//社区单元设备
const WEB_COMM_UNIT_UPDATE_RF = 3001;
const WEB_COMM_UNIT_UPDATE_PIN = 3002;
const WEB_COMM_UNIT_ADD_DEV = 3003;
const WEB_COMM_UNIT_DEL_DEV = 3004;
const WEB_COMM_UNIT_MODIFY_DEV = 3005;

//社区最外围设备
const WEB_COMM_PUB_UPDATE_RF = 4001;
const WEB_COMM_PUB_UPDATE_PIN = 4002;
const WEB_COMM_PUB_ADD_DEV = 4003;
const WEB_COMM_PUB_DEL_DEV = 4004;
const WEB_COMM_PUB_MODIFY_DEV = 4005;

//社区信息
const WEB_COMM_INFO = 5001;
const WEB_COMM_APT_PIN = 5002;
const WEB_COMM_MOTION = 5003;
const WEB_COMM_IMPORT_COMMUNITY = 5004;
const WEB_COMM_ADD_BUILDING = 5005;
const WEB_COMM_DEL_BUILDING = 5006;
const WEB_COMM_MODIFY_BUILDING = 5007;
const WEB_COMM_MODIFY_TIME = 5008;
const WEB_COMM_DELETE_COMMUNITY = 5009;
const WEB_COMM_IMPORT_FACE_PIC = 5010; //网页导入人脸
const WEB_COMM_DELETE_ALL_FACE_PIC = 5011;
const WEB_COMM_DELETE_ALL_RF_CARD = 5012;
const WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT = 5013; //通知设备开启流量超额上报
const WEB_COMM_PUB_OPEN_ALL_DOOR = 5014;
const WEB_COMM_PUB_CLOSE_ALL_DOOR = 5015;
const WEB_COMM_ALLOW_CREATE_PIN = 5016;
const WEB_COMM_FEATURE_PLAN_RENEW = 5017;

