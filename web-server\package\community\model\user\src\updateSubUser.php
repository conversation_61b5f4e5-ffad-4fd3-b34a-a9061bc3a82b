<?php
namespace package\community\model\user\src;

use package\community\model\user\config\Code;

trait UpdateSubUser
{
    
    /**
     * @msg: 修改社区从账号信息
     */
    public function updateComSub()
    {
        $params = [
            'ID',
            'Name',
            'FirstName',
            'LastName',
            'PhoneCode',
            'Phone',
            'Email',
            'MobileNumber',
            PROXY_ROLE['projectId'],
            'AccessFloor',
            'Remark'
        ];
        list($id,$name,$firstName,$lastName,$phoneCode,$phone,$email,$mobile,$communityId,$accessFloor,$remark) = $this->getParams($params);
        $this->log->debug('ID={ID},Name={Name},FirstName={FirstName},LastName={LastName},PhoneCode={PhoneCode},
        Phone={Phone}', ["ID"=>$id,"Name"=>$name,"FirstName"=>$firstName,"LastName"=>$lastName,"PhoneCode"=>$phoneCode,"Phone"=>$phone]);

        //1.获取用户相关信息
        $this->loadUtil('common', true);
        $getCommunityId = $this->utils->_common->common->getProjectID($id);
        // V6.5.2.5 access floor兼容其他入口传值，例如openapi
        $accessFloor = $accessFloor === null ? '' : $accessFloor;
        $accessFloor = $this->utils->self->checkAccessFloor($accessFloor);

        // 判断获取的社区和账号的社区是否一致
        if ($getCommunityId != $communityId) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_ID_NOT_SAME]);
        }
        // 6.4 从账号能修改邮箱和手机号
        // $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, ['Email', 'Account']);
        $tipCode = 0;//无特殊提示，1-邮箱提示

        // 2.修改用户信息
        $bindArray = [
            ":ID"=>$id,
            ":Name"=>$name,
            ":FirstName"=>$firstName,
            ":LastName"=>$lastName,
            ":PhoneCode"=>$phoneCode,
            ":Phone"=>$phone
        ];
        // 6.4 从账号能修改邮箱和手机号
        // 无关联多套房才可修改邮箱手机号
        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, ['UserInfoUUID', 'Account', 'UUID', 'ParentUUID']);
        $userData['Email'] = $this->dao->personalAccountUserInfo->selectByUUID($userData['UserInfoUUID'], 'Email')[0]['Email'];
        $linkNum = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userData['UserInfoUUID'], 'count(*)')[0]['count(*)'];
        $tipCode = 0;//无特殊提示，1-邮箱提示
        if ($linkNum === '1') {
            $userInfoArr = ['Email' => $email, 'MobileNumber' => $mobile, 'UUID' => $userData['UserInfoUUID']];
            if ($email !== $userData['Email']) {
                $this->loadUtil('account', true);
                $this->utils->_common->account->deleteUserToken($userData['Account']);
                // 事件收集：邮箱改变发送邮件
                $password = $this->share->util->generatePw(8);
                $this->notifyEvent->collect(
                    $email . '_communityCreateUser',
                    'communityCreateUser',
                    [$userData['Account'], $password, $email]
                );
                $userInfoArr['Passwd'] = $this->share->util->getSaltPwd($password);
                $tipCode = 1;
            }
            $this->dao->personalAccountUserInfo->update($userInfoArr, 'UUID');
        }
        $this->updateSubAccount($bindArray);
        // V6.5.3.5 PersonalCommunityInfo 修改，兼容旧数据。PersonalCommunityInfo是新增表，旧数据没有刷
        $uuid = $userData['UUID'];

        // 如果落地号码置空，删除对应的Sequence Call 数据
        if ($phone === "" || $phone === null) {
            $this->loadModel('sequence', false);
            $this->models->sequence->deleteSpecificOption($uuid, SEQUENCE_CALL_CALLEE_TYPE['Phone']);
        }

        $userExtraInfo = $this->dao->personalAccountCommunityInfo->selectByPersonalAccountUUID($uuid);
        $remark = $remark == null ? '' : $remark;
        if (count($userExtraInfo) === 0) {
            $this->dao->personalAccountCommunityInfo->insert([
                ':UUID'=>$this->share->util->uuid(),
                ':PersonalAccountUUID' => $uuid,
                ':AccessFloor' => $accessFloor,
                ':Remark' => $remark
            ]);
        } else {
            $this->dao->personalAccountCommunityInfo->update([
                ':PersonalAccountUUID' => $uuid,
                ':AccessFloor' => $accessFloor,
                ':Remark' => $remark
            ], 'PersonalAccountUUID');
            // 新增部分不需要更新，因为原始没有保存accessFloor的话，都是默认跟随web的，APP用户没有自己选accessFloor
            // 更新AccessFloor后，对于已经创建的TempKey，如果有楼层移除，需要将已创建的TempKey的楼层也一起移除掉
            $this->loadModel('tempKey', 'community');
            $parentAccount = $this->dao->personalAccount->selectByUUID($userData['ParentUUID'], 'Account')[0]['Account'];
            $this->models->_community->tempKey->updateUserAccessFloor($userData['Account'], $accessFloor, $parentAccount);
        }
        $this->notifySmartHome->collect(['User', 'notifyUpdateUser'], [$userData['ParentUUID'], $userData['UUID']], 6710);
        return ['data' => ['TipCode' => $tipCode]];
    }

    /**
     * @name: updateSubAccount
     * @msg:  修改从账号信息，并添加到日志
     *
     * @param $bindArray: 需要修改的信息; $userId: 代理的账号ID
     * @return:
     */
    public function updateSubAccount($bindArray, $userId = null)
    {
        $this->loadUtil('account', true);
        $account = $this->utils->_common->account->getUserInfo($bindArray[":ID"])['Account'];
        $this->dao->personalAccount->update($bindArray);
        $this->log->endUserLog(2, null, "edit user:".$bindArray[":Name"], $userId);
        $this->auditLog->setLog(AUDIT_CODE_EDIT_FAMILY_MEMBER, $this->env, ['Account' => $account, 'ID' => $bindArray[":ID"]], $account);
    }

    /**
     * @description: PM更新主从账号信息
     * @author: cj 2022-03-22 19:00:01 V6.4
     * @LastEditors: cj
     * @param {*}
     * @return {*}
     */
    public function updateAllUserForPM()
    {
        $params = [
            'ID',
            'Name',
            'FirstName',
            'LastName',
            'Phone',
            'Phone2',
            'Phone3',
            'PhoneCode',
            'Email',
            'MobileNumber',
            PROXY_ROLE['projectId'],
            'Remark'
        ];
        list($id, $name, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $email, $mobile, $communityId,$remark) = $this->getParams($params);
        $this->loadUtil('common', true);
        $getCommunityId = $this->utils->_common->common->getProjectID($id);
        // 判断获取的社区和账号的社区是否一致
        if ($getCommunityId != $communityId) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_ID_NOT_SAME]);
        }
        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, ['Role','Account', 'UserInfoUUID', 'UUID', 'ParentUUID']);
        $userData['Email'] = $this->dao->personalAccountUserInfo->selectByUUID($userData['UserInfoUUID'], 'Email')[0]['Email'];
        $bindArray = [
            ':ID'=>$id,
            ':Name'=>$name,
            ':FirstName'=>$firstName,
            ':LastName'=>$lastName,
            ':Phone'=>$phone,
            ':PhoneCode'=>$phoneCode
        ];
        // 无关联多套房才可修改邮箱手机号
        $linkNum = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userData['UserInfoUUID'], 'count(*)')[0]['count(*)'];
        if ($linkNum === '1') {
            $userInfoArr = ['Email' => $email, 'MobileNumber' => $mobile, 'UUID' => $userData['UserInfoUUID']];
            if ($email !== $userData['Email']) {
                $this->loadUtil('account', true);
                $this->utils->_common->account->deleteUserToken($userData['Account']);
                // 事件收集：邮箱改变发送邮件
                $password = $this->share->util->generatePw(8);
                $this->notifyEvent->collect(
                    $email . '_communityCreateUser',
                    'communityCreateUser',
                    [$userData['Account'], $password, $email]
                );
                $userInfoArr['Passwd'] = $this->share->util->getSaltPwd($password);
            }
            $this->dao->personalAccountUserInfo->update($userInfoArr, 'UUID');
        }
        if (intval($userData['Role']) !== COMENDSROLE) {
            $bindArray[':Phone2'] = $phone2;
            $bindArray[':Phone3'] = $phone3;
            $mainUserUUID = $userData['ParentUUID'];
            $this->auditLog->setLog(AUDIT_CODE_EDIT_FAMILY_MEMBER, $this->env, ['Account' => $userData['Account'], 'ID' => $id, 'Email' => $email, 'MobileNumber' => $mobile], $userData['Account']);
        } else {
            $mainUserUUID = $userData['UUID'];
            $this->auditLog->setLog(AUDIT_CODE_EDIT_FAMILY_MASTER, $this->env, ['Account' => $userData['Account'], 'ID' => $id, 'Email' => $email, 'MobileNumber' => $mobile], $userData['Account']);
        }
        $this->dao->personalAccount->update($bindArray);
        //修改remark字段
        $remark = $remark == null ? '' : $remark;
        $updateArray = [
            'PersonalAccountUUID' => $userData['UUID'],
            'Remark' => $remark
        ];
        $this->dao->personalAccountCommunityInfo->update($updateArray, 'PersonalAccountUUID');
        $this->notifySmartHome->collect(['User', 'notifyUpdateUser'], [$mainUserUUID, $userData['UUID']], 6710);
        return ['Account'=>$userData['Account']];
    }
}
