<?php
require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');

function getDB()
{
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbhost = DATABASEIP;
	$dbport = DATABASEPORT;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function fdfs_del_pic_by_url($url)
{
    $beforeFile = explode('/', $url, 3); ///group1/M00/07/26/rBIp3GR5PImADI11AA1cVW85ElE03.pcap
    //如果存在fdfs的链接，则对应删除
    if (strstr($beforeFile[1], 'group')) {
        fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);
        return true;
    } else {
        return false;
    }
}

function checkPcapFileExpire()
{
	$db = getDB();
	$sth = $db->prepare("select FileUrl from PcapCaptureControlList where UpdateTime <= DATE_SUB(NOW(), INTERVAL 1 DAY)");
    $ret = $sth->execute();
	$expireFileList = $sth->fetchALL(PDO::FETCH_ASSOC);
	foreach ($expireFileList as $expireFile) {
		fdfs_del_pic_by_url($expireFile['FileUrl']);
	}
	
}

checkPcapFileExpire();
