<?php
/*
 * @Description: 版本限制检查
 * @version: 
 * @Author: lwj
 * @Date: 2020-07-29 10:10:10
 * @LastEditors  : lwj
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";

class CVersionLogCheck implements IMiddleware {
    public $name;
    public function handle(\Closure $next) {
        global $cMessage;
        global $cLog;
        $params = ["Log"=>""];
        $log = \util\model\getParamsFromContainer($params,$this->dataContainer)["Log"];
        
        $cLog->actionLog("#middle#version#Log=".$log);
        if(mb_strlen($log) > 5000){
            $cMessage->echoErrorMsg(StateVersionLogMaxLen,[], [5000]);
        }
        $next();
    }
}