<?php

namespace dao;
use framework\BasicDao;

class RBACDataGroup extends BasicDao
{
    //当前表名
    public $table = 'RBACDataGroup';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/05/24 08:58 V6.5.4
     * @lastEditors: systemCreator 2024/05/24 08:58 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    public function getDisDataGroup($uuid)
    {
        //dis DataGroup为： superuuid;disuuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID) as DataGroup, Dis.UUID from Account Dis
    left join Account Super on Super.UUID = Dis.ParentUUID where Dis.Grade = 11 and Dis.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getSubDisDataGroup($uuid)
    {
        //subdis DataGroup为： superuuid;disuuid;subdisuuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID, ';', SubDis.UUID) as DataGroup, SubDis.UUID from Account SubDis
    left join Account Dis on Dis.UUID = SubDis.ParentUUID
    left join Account Super on Super.UUID = Dis.ParentUUID where SubDis.Grade = 12 and SubDis.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getSubSuperDataGroup($uuid)
    {
        //subsuper DataGroup为： superuuid;subsuperuuid
        $sql = "select concat(Super.UUID, ';' , SubSuper.UUID) as DataGroup, SubSuper.UUID from Account SubSuper
    left join Account Super on Super.UUID = SubSuper.ParentUUID where SubSuper.Grade = 2 and SubSuper.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getProjectDataGroup($uuid)
    {
        //project DataGroup为： superuuid;disuuid;insuuid;projectuuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID, ';', Ins.UUID, ';', Project.UUID) as DataGroup, Project.UUID from Account Project
    left join Account Ins on Ins.ID = Project.ManageGroup 
    left join Account Dis on Dis.UUID = Project.ParentUUID 
    left join Account Super on Super.UUID = Dis.ParentUUID where Project.Grade in (21,22,23) and Project.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getPmDataGroup($uuid)
    {
        //pm DataGroup为： superuuid;disuuid;insuuid;pmuuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID, ';', Ins.UUID, ';', Pm.UUID) as DataGroup, Pm.UUID from Account Pm
    left join Account Ins on Ins.ID = Pm.ManageGroup
    left join Account Dis on Dis.UUID = Ins.ParentUUID
    left join Account Super on Super.UUID = Dis.ParentUUID where Pm.Grade = 31 and Pm.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getMainUserDataGroup($uuid)
    {
        //终端用户主账号or空房间（空房间也需要，即使没有实际终端用户，因为管理员需要可以操作这条房间数据的权限） DataGroup为： superuuid;disuuid;insuuid;projectuuid;mainuseruuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID, ';', Ins.UUID, ';', Project.UUID, ';', PA.UUID) as DataGroup, PA.UUID from PersonalAccount PA
    left join Account Project on Project.UUID = PA.ParentUUID
    left join Account Ins on Ins.ID = Project.ManageGroup 
    left join Account Dis on Dis.UUID = Project.ParentUUID 
    left join Account Super on Super.UUID = Dis.ParentUUID where PA.Role in (10, 20, 30, 31, 40) and PA.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function getSubUserDataGroup($uuid)
    {
        //终端用户从账号 DataGroup为： superuuid;disuuid;insuuid;projectuuid;mainuseruuid;subuuid
        $sql = "select concat(Super.UUID, ';', Dis.UUID, ';', Ins.UUID, ';', Project.UUID, ';', PA.UUID, ';', SUB.UUID) as DataGroup, SUB.UUID from PersonalAccount SUB
    left join PersonalAccount PA on PA.UUID = SUB.ParentUUID
    left join Account Project on Project.UUID = PA.ParentUUID
    left join Account Ins on Ins.ID = Project.ManageGroup 
    left join Account Dis on Dis.UUID = Project.ParentUUID 
    left join Account Super on Super.UUID = Dis.ParentUUID where SUB.Role in ('11', '21') and SUB.UUID = :UUID";

        return $this->execute($sql, [':UUID' => $uuid])[0]['DataGroup'];
    }

    public function updateDataGroupForInsTransfer($insUUID, $oldDisUUID, $newDisUUID)
    {
        $sql = "UPDATE RBACDataGroup SET DataGroup = REPLACE(DataGroup, :OldDataGroup, :NewDataGroup) where DataGroup like :DataGroup;";
        $oldDataGroup = $oldDisUUID . ';' . $insUUID;
        $newDataGroup = $newDisUUID . ';' . $insUUID;

        $this->db->exec2ListWArray($sql, [":OldDataGroup"=>$oldDataGroup, ":NewDataGroup"=>$newDataGroup, ":DataGroup"=>"%$insUUID%"]);
    }
}