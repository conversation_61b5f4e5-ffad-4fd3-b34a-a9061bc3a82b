<?php

namespace share\service;

class BillsysUtil
{
    // 收费计划检查，检查过期时间是否需要改变。有两种情况会使用该方法，一是收费参数改变，二是付款时候,三是刷新计费模型时候
    // $user null or array(userId) 指刷新单住户计费模型时指定用户
    public function checkPlan($communityId, $type = 0, $step = 1, $user = null)
    {
        $cLog = \share\util\getLog();
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        $cLog->payLog("#billsysUtil#checkPlan#communityId=$communityId;type=$type");
        // 先查询激活用户，没有激活用户就不需要去检测收费计划
        $projectData = $dao->account->selectByID($communityId, 'Grade,UUID')[0];

        $array = [
            ['ProjectUUID', $projectData['UUID']],
        ];
        $projectGrade = $projectData['Grade'];
        $subscriptionEndUserList = $dao->subscriptionEndUserList->selectByArray($array, 'PersonalAccountUUID,SubscriptionUUID,Amount,SiteUUID,LockUUID');
        $flushSubscription =  false;
        if (count($subscriptionEndUserList) !== 0) {
            $subscriptionListData = $dao->subscriptionList->selectByArray([['UUID', array_column($subscriptionEndUserList, 'SubscriptionUUID')], ['Status', [0,1]], ['IsDelete', 0]], 'UUID,IntervalType');
            if (count($subscriptionListData) !== 0) {
                $flushSubscription = true;
                $userArray = [
                    ['ProjectUUID', $projectData['UUID']],
                    ['SubscriptionUUID', array_column($subscriptionListData, 'UUID')],
                    ['Type', SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew'], '!='],
                    ['Type', SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew'], '!='],
                    ['Type', SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'], '!='],
                    ['Type', SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew'], '!='],
                ];

                $subscriptionList = array_column($subscriptionListData, 'IntervalType', 'UUID');

                $subscriptionEndUserList = $dao->subscriptionEndUserList->selectByArray($userArray, 'PersonalAccountUUID,SubscriptionUUID,Amount');
                $subscriptionEndUserList = array_column($subscriptionEndUserList, null, 'PersonalAccountUUID');

                $videoArray = [
                    ['ProjectUUID', $projectData['UUID']],
                    ['SubscriptionUUID', array_column($subscriptionListData, 'UUID')],
                    ['Type', [SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew'], SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew']]],
                ];

                $subscriptionVideoList = $dao->subscriptionEndUserList->selectByArray($videoArray, 'SiteUUID,SubscriptionUUID,Amount');
                $subscriptionVideoList = array_column($subscriptionVideoList, null, 'SiteUUID');

                $thirdLockArray = [
                    ['ProjectUUID', $projectData['UUID']],
                    ['SubscriptionUUID', array_column($subscriptionListData, 'UUID')],
                    ['Type', [SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'], SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew']]],
                ];
                $subscriptionThirdLockList = $dao->subscriptionEndUserList->selectByArray($thirdLockArray, 'LockUUID,SubscriptionUUID,Amount');
                $subscriptionThirdLockList = array_column($subscriptionThirdLockList, null, 'LockUUID');
            }
        }
        // $projectGrade = $db->querySList('select Grade from Account where ID = :ID', ['ID' => $communityId])[0]['Grade'];
        
        // 6.3 区别社区和办公
        $thirdLockRelateInfoUUIDs = []; //三方锁的UUID
        if ($projectGrade == COMMUNITYGRADE) {
            $user = $dao->personalAccount->selectByArray([['Role', [COMENDMROLE, PMENDMROLE]], ['ParentID', $communityId], ['Active', 1]], 'ID,ActiveTime,ExpireTime,UUID');
            // 查询视频存储的社区
            $videoCom = $dao->videoStorage->selectByArray([['AccountUUID', $projectData['UUID']], ['ProjectType', 2], ['IsEnable', 1], ['PlanType', 1]]);
            if (!empty($videoCom)){
                $videoId = [$communityId];
            }
            // 查询社区的三方锁
            $thirdLockCom = $dao->thirdLockRelateInfo->selectByArray([['AccountUUID', $projectData['UUID']], ['ProjectType', 2], ['Active', 1]]);
            if (!empty($thirdLockCom)){
                $thirdLockRelateInfoUUIDs = array_column($thirdLockCom, 'UUID');
            }
            $expireTimeType = 'ExpireTime';
        } elseif ($projectGrade == OFFICEGRADE) {
            $searchArr = [['ParentID', $communityId], ['Role', [OFFSTAFFROLE, OFFPERSONNELROLE, OFF_NEW_PERSONNEL_ROLE]], ['Active', 1]];
            if (!empty($user)) {
                $userIds =  array_column($user, 'ID');
                array_push($searchArr, ['ID', $userIds]);
            }
            $user = $dao->personalAccount->selectByArray($searchArr, 'ID,ActiveTime,ExpireTime,UUID');
            $expireTimeType = 'ExpireTime';
        } elseif ($projectGrade == PERSONGRADE) {
            // 先查询激活用户，没有激活用户就不需要去检测收费计划
            $searchArr = [['ParentID', $communityId], ['Role', PERENDMROLE], ['Active', 1]];
            if ($user == null) {
                $user = $dao->personalAccount->selectByArray($searchArr, 'ID,ActiveTime,PhoneExpireTime,UUID');

                //查询所有单住户
                $searchArr = [['ParentID', $communityId], ['Role', PERENDMROLE]];
                $videoUser = $dao->personalAccount->selectByArray($searchArr, 'ID');
                $videoId = array_column($videoUser, 'ID');

                // 查询单住户的三方锁
                $thirdLockSingle = $dao->thirdLockRelateInfo->selectByArray([['AccountUUID', $projectData['UUID']], ['ProjectType', 1], ['Active', 1]]);
                if (!empty($thirdLockSingle)){
                    $thirdLockRelateInfoUUIDs = array_column($thirdLockSingle, 'UUID');
                }
            } elseif (!empty($user)) {
                // V6.2.1 kxl 新增参数$user，指定用户刷新，并需要滤未激活的用户
                $searchArr = [['ParentID', $communityId], ['Role', PERENDMROLE], ['Active', 1], ['ID', $user]];
                $user = $dao->personalAccount->selectByArray($searchArr, 'ID,ActiveTime,PhoneExpireTime,UUID');

                //查询所有单住户
                $searchArr = [['ParentID', $communityId], ['Role', PERENDMROLE], ['ID', $user]];
                $mainUser = $dao->personalAccount->selectByArray($searchArr, 'UUID,ID');
                // 查询视频存储的单住户
                $videoSingles = $dao->videoStorage->selectByArray([['PersonalAccountUUID', array_column($mainUser, 'UUID')], ['ProjectType', 1], ['IsEnable', 1], ['PlanType', 1]], 'PersonalAccountUUID');
                if (!empty($videoSingles)) {
                    $mainUser = \share\util\arrayColumnAsKey($mainUser, 'UUID');
                    $videoId = [$communityId];
                    foreach ($videoSingles as $single) {
                        $videoId[] = $mainUser[$single]['ID'];
                    }
                }
                //查询单住户的三方锁
                $thirdLockSingle = $dao->thirdLockRelateInfo->selectByArray([['PersonalAccountUUID', array_column($mainUser, 'UUID')], ['ProjectType', 1], ['Active', 1]]);
                if (!empty($thirdLockSingle)){
                    $thirdLockRelateInfoUUIDs = array_column($thirdLockSingle, 'UUID');
                }
            }
            $expireTimeType = 'PhoneExpireTime';
        }

        // 超级管理员刷新计费列表展示即将刷新的数据,其中包含用户数据和视频存储、三方锁数据
        $updatinginfo = [];
        $total = 0;
        if (count($user) + count($videoId) + count($thirdLockRelateInfoUUIDs) == 0) {
            return [$updatinginfo, $total];
        }
        
        $now = \share\util\getNow();
        $user2Id = [];
        $users = [];

        foreach ($user as $val) {
            $expireTime = strtotime($val[$expireTimeType]) > strtotime($now) ? $val[$expireTimeType] : $now;
            $user2Id[$val['ID']] = [$expireTimeType => $expireTime, 'PersonalAccountUUID' => $val['UUID']];
            array_push($users, $val['ID']);
        }

        if ($projectGrade == COMMUNITYGRADE) {
            $instanceType = 'multiple';
        } elseif ($projectGrade == OFFICEGRADE) {
            $instanceType = 'office';
        } elseif ($projectGrade == PERSONGRADE) {
            $instanceType = 'single';
        }

        //获取视频存储收费信息
        $cLog->payLog("#billsysUtil#checkPlan#videoID:".json_encode($videoId)." type=$type");
        $utilContainer = \framework\container\Util::getInstance();
        $utilContainer->package = 'common';
        $utilClass = $utilContainer->make('order', true, ['env' => 'normal']);
        $utilDataContainer = new \framework\dataContainer\Common();
        $utilDataContainer->set('VideoSites', $videoId);
        $utilDataContainer->set('ProjectType', $instanceType);
        $utilContainer->setDataContainer($utilClass, $utilDataContainer);
        $videoStorageInfo = $utilClass->getSubscribeVideoStorageInfo();

        //获取三方锁收费信息
        $utilContainer = \framework\container\Util::getInstance();
        $utilContainer->package = 'common';
        $utilClass = $utilContainer->make('order', true, ['env' => 'normal']);
        $utilDataContainer = new \framework\dataContainer\Common();
        $utilDataContainer->set('ThirdLockRelateInfoUUIDs', $thirdLockRelateInfoUUIDs);
        $utilDataContainer->set('ProjectType', $instanceType);
        $utilContainer->setDataContainer($utilClass, $utilDataContainer);
        $thirdLockInfo = $utilClass->getSubscribeThirdLockInfoForRelateInfoUUID();

        // 向计费系统查询计划
        $userResult = $this->getCharge($instanceType, $communityId, $users, PAY_SUBSCRIPTION);
        $videoResult = $this->getVideoStorageCharge($videoStorageInfo);
        $thirdResult = $this->getThirdLockCharge($thirdLockInfo['chargeData']);

        $canceledSubscriptionList = [];
        foreach ($userResult as $val) {
            $id = $val['ID'];
            $activity = $val['Activity'];
            $expireTime = $user2Id[$id][$expireTimeType];
            // 根据结果刷新相应用户过期时间，每个用户需要对比‘是否需要修改’，只有本身过期时间和当前收费计划相悖才需要更改
            $needChange = false;
            if (count($activity) == 0) {
                // 使用计费模型的价格
                $monthFee = $val['MonthlyFee'];
                if ($monthFee == 0 && $expireTime != DEFAULTEXPIRETIME) {
                    // 收费到免费
                    $needChange = true;
                    $newExpireTime = DEFAULTEXPIRETIME;
                } elseif ($monthFee != 0 && $expireTime == DEFAULTEXPIRETIME) {
                    // 免费到收费
                    $needChange = true;
                    $newExpireTime = date('Y-m-d H:i:s', strtotime('+3 day'));

                    if ($type == 2) {
                        $needChange = false;
                    }
                }
            } else {
                // 使用营销活动的价格
                $monthFee = $activity['MonthlyFee'];
                if ($monthFee == 0 && $expireTime != DEFAULTEXPIRETIME) {
                    // 收费到免费
                    // 刷新时间到1年后，并记录使用过该优惠
                    $needChange = true;
                    // $db->insert2List("UserActivityUsed",[":PersonalAccountID"=>$id, ":Activity"=>$activity["UUID"]]);
                    $newExpireTime = $this->getActiveExpire($id, $activity['UUID'], $expireTime);
                } elseif ($monthFee != 0 && $expireTime == DEFAULTEXPIRETIME) {
                    // 免费到收费
                    $needChange = true;
                    $newExpireTime = date('Y-m-d H:i:s', strtotime('+3 day'));

                    if ($type == 2) {
                        $needChange = false;
                    }
                }
            }
            // 判断是否有进行自动续费的订阅
            if ($flushSubscription) {
                $personalAccountUUID = $user2Id[$id]['PersonalAccountUUID'];
                if (array_key_exists($personalAccountUUID, $subscriptionEndUserList)) {
                    // 比较现有金额和续费订单里金额是否一致
                    $subscriptionUUID = $subscriptionEndUserList[$personalAccountUUID]['SubscriptionUUID'];
                    $price = \share\util\inputComputedCount($monthFee);
                    // 7.1.3 不同人支付不同价格
                    $subscriptionData = $dao->subscriptionList->selectByArray([['UUID', $subscriptionUUID]])[0];
                    $payerType = $subscriptionData['PayerType'];
                    // 订阅中支付人类型；0=>终端用户,1=>物业,2=>install,3=>dis,4=>sub dis
                    if (!empty($val['MonthlyFeeRoleConfig'])) {
                        $monthFeeRoleConfig = array_column($val['MonthlyFeeRoleConfig'], null, 'Type');
                        if ($payerType == '1') {
                            // 查询项目是否有开启PM Fee开关
                            $isPmFee = $dao->communityInfo->selectByKey('AccountUUID', $subscriptionData['ProjectUUID'])[0]['IsPMFee'];
                            if ($isPmFee == '1' && isset($monthFeeRoleConfig[PAYER_ROLE_PM])) {
                                $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_PM]['Fee']);
                            }
                        } else if ($payerType == '2' && isset($monthFeeRoleConfig[PAYER_ROLE_INS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_INS]['Fee']);
                        } else if ($payerType == '3' && isset($monthFeeRoleConfig[PAYER_ROLE_DIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_DIS]['Fee']);
                        } else if ($payerType == '4' && isset($monthFeeRoleConfig[PAYER_ROLE_SUBDIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_SUBDIS]['Fee']);
                        }
                    }
                    // 获取金额
                    switch ($subscriptionList[$subscriptionUUID]) {
                        case 1: //按季
                            $price = $price * 3;
                            break;
                        case 2: //按年
                            $price = $price * 12;
                            break;
                        case 3: //按天
                            $price = \share\util\inputComputedCount($monthFee / 30);
                            break;
                    }
                    // 价格不等和避免重复取消
                    if ($price !== intval($subscriptionEndUserList[$personalAccountUUID]['Amount'])) {
                        if (!in_array($subscriptionEndUserList[$personalAccountUUID]['SubscriptionUUID'], $canceledSubscriptionList)) {
                            $canceledSubscriptionList[] = $subscriptionEndUserList[$personalAccountUUID]['SubscriptionUUID'];
                        }
                    }
                }
            }

            if ($needChange && $step == 0) {
                $cLog->payLog("#user#checkPlan#needChange#id=$id");
                $mainUser = $dao->personalAccount->selectByID($id, 'Name,SipAccount,Phone,UserInfoUUID,Role,UUID as MainUserUUID,1 as Type')[0];
                if ($mainUser['Role'] == PMENDMROLE) {
                    $accountUUID = $dao->pmAccountMap->selectByKey('PersonalAccount', $mainUser['SipAccount'], 'AccountUUID')[0]['AccountUUID'];
                    $userInfoUUID = $dao->accountMap->selectByKey('AccountUUID', $accountUUID, 'UserInfoUUID')[0]['UserInfoUUID'];
                    $mainUser['Email'] = $dao->accountUserInfo->selectByUUID($userInfoUUID, 'Email')[0]['Email'];
                } else {
                    $mainUser['Email'] = $dao->personalAccountUserInfo->selectByUUID($mainUser['UserInfoUUID'], 'Email')[0]['Email'];
                }
                array_push($updatinginfo, $mainUser);
                ++$total;

                // 获取所有已激活的从账户信息
                $subUsers = $dao->personalAccount->selectByArray([['ParentID', $id], ['Active', 1], ['Role', [PERENDSROLE, COMENDSROLE]]], 'Name,SipAccount,Phone,UserInfoUUID,Role,ParentUUID as MainUserUUID,1 as Type');
                foreach ($subUsers as $subUser) {
                    $subUser['Email'] = $dao->personalAccountUserInfo->selectByUUID($subUser['UserInfoUUID'], 'Email')[0]['Email'];
                    array_push($updatinginfo, $subUser);
                    ++$total;
                }
            }
            if ($needChange && $step == 1) {
                $cLog->payLog('#billsysUtil#checkPlan#needChange#data=' . var_export($val, true));
                $cLog->payLog(
                    "#billsysUtil#checkPlan#needChange#id=$id;$expireTimeType=$expireTime;newExpireTime=$newExpireTime;"
                );
                // 单住户需要判断是否走新的付费方式
                $isSingleNewBilling = 0;
                if ($projectGrade == PERSONGRADE) {
                    $personalAccountUUID = $user2Id[$id]['PersonalAccountUUID'];
                    $isSingleNewBilling = $dao->personalAccountSingleInfo->selectByPersonalAccountUUID($personalAccountUUID, 'IsNewBilling')[0]['IsNewBilling'];
                }
                if (intval($isSingleNewBilling) === 1) {
                    // 单住户新付费方式，同步更新ExpireTime和PhoneExpireTime
                    $cLog->payLog(
                        "#billsysUtil#checkPlan#needChange#single new billing#id=$id;$expireTimeType=$expireTime;newExpireTime=$newExpireTime;"
                    );
                    $dao->personalAccount->update(['ID' => $id, 'ExpireTime' => $newExpireTime, 'PhoneExpireTime' => $newExpireTime]);
                    // 修改所有已激活的从账户过期时间
                    $subUsers = $dao->personalAccount->selectByArray([['ParentID', $id], ['Active', 1], ['Role', [PERENDSROLE]]], 'ID');
                    foreach ($subUsers as $subUser) {
                        $dao->personalAccount->update(['ID' => $subUser['ID'], 'ExpireTime' => $newExpireTime, 'PhoneExpireTime' => $newExpireTime]);
                    }
                } else {
                    $dao->personalAccount->update(['ID' => $id, $expireTimeType => $newExpireTime]);
                    // 修改所有已激活的从账户过期时间
                    $subUsers = $dao->personalAccount->selectByArray([['ParentID', $id], ['Active', 1], ['Role', [PERENDSROLE, COMENDSROLE]]], 'ID');
                    foreach ($subUsers as $subUser) {
                        $dao->personalAccount->update(['ID' => $subUser['ID'], $expireTimeType => $newExpireTime]);
                    }
                }
            }
        }

        $videoInfo =\share\util\arrayColumnAsKey( $videoStorageInfo['config'],'SiteUUID');
        // 视频存储
        foreach ($videoResult as $val) {
            $uuid = $val['UUID'];
            $expireTime = $videoInfo[$uuid]['ExpireTime'];
            // 根据结果刷新相应用户过期时间，每个用户需要对比‘是否需要修改’，只有本身过期时间和当前收费计划相悖才需要更改
            $needChange = false;

            // 使用计费模型的价格
            $monthFee = $val['MonthlyFee'];
            if ($monthFee == 0 && $expireTime != DEFAULTEXPIRETIME) {
                // 收费到免费
                $needChange = true;
                $newExpireTime = DEFAULTEXPIRETIME;
            } elseif ($monthFee != 0 && $expireTime == DEFAULTEXPIRETIME) {
                // 免费到收费
                $needChange = true;
                $newExpireTime = date('Y-m-d H:i:s', strtotime('+3 day'));

                if ($type == 2) {
                    $needChange = false;
                }
            }

            if ($flushSubscription) {
                $siteUUID = $val['UUID'];
                if (array_key_exists($uuid, $subscriptionVideoList)) {
                    // 比较现有金额和续费订单里金额是否一致
                    $subscriptionUUID = $subscriptionVideoList[$siteUUID]['SubscriptionUUID'];
                    $price = \share\util\inputComputedCount($monthFee);
                    // 7.1.3 不同人支付不同价格
                    $subscriptionData = $dao->subscriptionList->selectByArray([['UUID', $subscriptionUUID]])[0];
                    $payerType = $subscriptionData['PayerType'];
                    // 订阅中支付人类型；0=>终端用户,1=>物业,2=>install,3=>dis,4=>sub dis
                    if (!empty($val['Model']['MonthlyFeeRoleConfig'])) {
                        $monthFeeRoleConfig = array_column($val['Model']['MonthlyFeeRoleConfig'], null, 'Type');
                        if ($payerType == '1') {
                            // 查询项目是否有开启PM Fee开关
                            $isPmFee = $dao->communityInfo->selectByKey('AccountUUID', $subscriptionData['ProjectUUID'])[0]['IsPMFee'];
                            if ($isPmFee == '1' && isset($monthFeeRoleConfig[PAYER_ROLE_PM])) {
                                $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_PM]['Fee']);
                            }
                        } else if ($payerType == '2' && isset($monthFeeRoleConfig[PAYER_ROLE_INS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_INS]['Fee']);
                        } else if ($payerType == '3' && isset($monthFeeRoleConfig[PAYER_ROLE_DIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_DIS]['Fee']);
                        } else if ($payerType == '4' && isset($monthFeeRoleConfig[PAYER_ROLE_SUBDIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_SUBDIS]['Fee']);
                        }
                    }
                    // 获取金额
                    switch ($subscriptionList[$subscriptionUUID]) {
                        case 1: //按季
                            $price = $price * 3;
                            break;
                        case 2: //按年
                            $price = $price * 12;
                            break;
                        case 3: //按天
                            $price = \share\util\inputComputedCount($monthFee / 30);
                            break;
                    }
                    // 价格不等和避免重复取消
                    if ($price !== intval($subscriptionVideoList[$siteUUID]['Amount'])) {
                        if (!in_array($subscriptionVideoList[$siteUUID]['SubscriptionUUID'], $canceledSubscriptionList)) {
                            $canceledSubscriptionList[] = $subscriptionVideoList[$siteUUID]['SubscriptionUUID'];
                        }
                    }
                }
            }

            if ($needChange && $step == 0) {
                if ($projectGrade == COMMUNITYGRADE) {
                    $mainUser = $dao->account->selectByUUID($uuid, 'Location as Name,"" as SipAccount,"" as Phone,"" as UserInfoUUID,"" as Role,UUID as MainUserUUID,2 as Type')[0];

                } elseif ($projectGrade == PERSONGRADE) {
                    $mainUser = $dao->personalAccount->selectByUUID($uuid, 'Name,SipAccount,Phone,UserInfoUUID,Role,UUID as MainUserUUID,2 as Type')[0];
                    $email = $dao->personalAccountUserInfo->selectByUUID($mainUser['UserInfoUUID'], 'Email')[0]['Email'];
                    $mainUser['Email'] = $email;
                }
                array_push($updatinginfo, $mainUser);
                ++$total;
            }
            if ($needChange && $step == 1) {
                // 更新视频存储过期
                $cLog->payLog('#billsysUtil#checkPlan#needChange#data=' . var_export($val, true));
                $cLog->payLog(
                    "#billsysUtil#checkPlan#needChange#id=$id;$expireTimeType=$expireTime;newExpireTime=$newExpireTime;"
                );

                if ($projectGrade == COMMUNITYGRADE) {
                    $dao->videoStorage->update(['AccountUUID' => $uuid, 'ExpireTime' => $newExpireTime],'AccountUUID');
                } elseif ($projectGrade == PERSONGRADE) {
                    $dao->videoStorage->update(['PersonalAccountUUID' => $uuid, 'ExpireTime' => $newExpireTime],'PersonalAccountUUID');
                }
            }
        }

        // 三方锁
        foreach ($thirdLockInfo['config'] as $key => $val) {
            $brand = $val['Brand'];
            $lockUUID = $val['LockUUID'];
            $relateInfoUUID = $val['UUID'];
            $expireTime = $val['ExpireTime'];
            // 根据结果刷新相应用户过期时间，每个用户需要对比‘是否需要修改’，只有本身过期时间和当前收费计划相悖才需要更改
            $needChange = false;
            // 使用计费模型的价格
            $charge = $thirdResult[$key];
            $monthFee = $charge['ThirdLock'][$brand]['MonthlyFee'];
            if ($monthFee == 0 && $expireTime != DEFAULTEXPIRETIME) {
                // 收费到免费
                $needChange = true;
                $newExpireTime = DEFAULTEXPIRETIME;
            } elseif ($monthFee != 0 && $expireTime == DEFAULTEXPIRETIME) {
                // 免费到收费
                $needChange = true;
                $newExpireTime = date('Y-m-d H:i:s', strtotime('+3 day'));

                if ($type == 2) {
                    $needChange = false;
                }
            }

            if ($flushSubscription) {
                if (!empty($lockUUID) && array_key_exists($lockUUID, $subscriptionThirdLockList)) {
                    // 比较现有金额和续费订单里金额是否一致
                    $subscriptionUUID = $subscriptionThirdLockList[$lockUUID]['SubscriptionUUID'];
                    $price = \share\util\inputComputedCount($monthFee);
                    // 7.1.3 不同人支付不同价格
                    $subscriptionData = $dao->subscriptionList->selectByArray([['UUID', $subscriptionUUID]])[0];
                    $payerType = $subscriptionData['PayerType'];
                    $monthFeeRoleConfig = $charge['ThirdLock'][$brand]['FeeRoleConfig'];
                    // 订阅中支付人类型；0=>终端用户,1=>物业,2=>install,3=>dis,4=>sub dis
                    if (!empty($monthFeeRoleConfig)) {
                        $monthFeeRoleConfig = array_column($monthFeeRoleConfig, null, 'Type');
                        if ($payerType == '1') {
                            // 查询项目是否有开启PM Fee开关
                            $isPmFee = $dao->communityInfo->selectByKey('AccountUUID', $subscriptionData['ProjectUUID'])[0]['IsPMFee'];
                            if ($isPmFee == '1' && isset($monthFeeRoleConfig[PAYER_ROLE_PM])) {
                                $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_PM]['MonthlyFee']);
                            }
                        } else if ($payerType == '2' && isset($monthFeeRoleConfig[PAYER_ROLE_INS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_INS]['MonthlyFee']);
                        } else if ($payerType == '3' && isset($monthFeeRoleConfig[PAYER_ROLE_DIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_DIS]['MonthlyFee']);
                        } else if ($payerType == '4' && isset($monthFeeRoleConfig[PAYER_ROLE_SUBDIS])) {
                            $price = \share\util\inputComputedCount($monthFeeRoleConfig[PAYER_ROLE_SUBDIS]['MonthlyFee']);
                        }
                    }
                    // 获取金额
                    switch ($subscriptionList[$subscriptionUUID]) {
                        case 1: //按季
                            $price = $price * 3;
                            break;
                        case 2: //按年
                            $price = $price * 12;
                            break;
                        case 3: //按天
                            $price = \share\util\inputComputedCount($monthFee / 30);
                            break;
                    }
                    // 价格不等和避免重复取消
                    if ($price !== intval($subscriptionThirdLockList[$lockUUID]['Amount'])) {
                        if (!in_array($subscriptionThirdLockList[$lockUUID]['SubscriptionUUID'], $canceledSubscriptionList)) {
                            $canceledSubscriptionList[] = $subscriptionThirdLockList[$lockUUID]['SubscriptionUUID'];
                        }
                    }
                }
            }

            if ($needChange && $step == 0) {
                if ($projectGrade == COMMUNITYGRADE) {
                    $mainUser = [
                        'Name' => $val['LockName'],
                        'SipAccount' => '',
                        'Phone' => '',
                        'Email' => '',
                        'UserInfoUUID' => '',
                        'Role' => '',
                        'MainUserUUID' => '',
                        'Type' => '3',
                    ];
                    if (!empty($val['PersonalAccountUUID'])) {
                        $userInfo = $dao->personalAccount->selectByUUID($val['PersonalAccountUUID'], 'SipAccount,Phone,UserInfoUUID,Role,UUID as MainUserUUID,3 as Type')[0];
                        $email = $dao->personalAccountUserInfo->selectByUUID($userInfo['UserInfoUUID'], 'Email')[0]['Email'];
                        $mainUser['Email'] = $email;
                        $mainUser['UserInfoUUID'] = $userInfo['UserInfoUUID'];
                        $mainUser['Phone'] = $userInfo['Phone'];
                        $mainUser['MainUserUUID'] = $userInfo['MainUserUUID'];
                        $mainUser['SipAccount'] = $userInfo['SipAccount'];
                        $mainUser['Role'] = $userInfo['Role'];
                    }
                } elseif ($projectGrade == PERSONGRADE) {
                    $mainUser = $dao->personalAccount->selectByUUID($val['PersonalAccountUUID'], 'SipAccount,Phone,UserInfoUUID,Role,UUID as MainUserUUID,3 as Type')[0];
                    $email = $dao->personalAccountUserInfo->selectByUUID($mainUser['UserInfoUUID'], 'Email')[0]['Email'];
                    $mainUser['Name'] = $val['LockName'];
                    $mainUser['Email'] = $email;
                }
                array_push($updatinginfo, $mainUser);
                ++$total;
            }

            if ($needChange && $step == 1) {
                // 更新三方锁过期时间
                $cLog->payLog('#billsysUtil#checkPlan#needChange#data=' . var_export($val, true));
                $cLog->payLog(
                    "#billsysUtil#checkPlan#needChange#UUID=$relateInfoUUID;$expireTimeType=$expireTime;newExpireTime=$newExpireTime;"
                );

                $dao->thirdLockRelateInfo->update(['UUID' => $relateInfoUUID, 'ExpireTime' => $newExpireTime], 'UUID');
            }
        }

        if ($step == 0) {
            return [$updatinginfo, $total];
        } elseif ($step == 1) {
            foreach ($canceledSubscriptionList as $val) {
                // 7表示计费模型变更取消
                $this->cancelSubscription($val, SUBSCRIPTION_END_REASON_ARRAY[7]);
            }
        }
    }

    public function getActiveExpire($id, $activeUUID, $expireTime)
    {
        $db = \share\util\getDatabase();
        $db->insert2List('UserActivityUsed', [':PersonalAccountID' => $id, ':Activity' => $activeUUID]);

        return \share\util\computedLastDate($expireTime, 12);
    }

    
    /*
     *@description
     *<AUTHOR> 2022-04-06 14:53:59 V6.4
     *@lastEditor kxl 2022-04-06 14:53:59 V6.4
     *@param {*} type enum('multiple','single','office')
     *@param {*} pcMngId pcMngId installer or communityId or officeId or null
     *@param {*} users pcMngId不为null时：主账户id数组;或者pcMngId为null时，common/order/util->getSubscribeUserInfo的处理结果
     *@param {*} payType 付款类型，0激活，1续费，2额外App... 使用常量PAY_ACTIVE | PAY_SUBSCRIPTION | PAY_ADD_APP | PAY_LANDLINE
     *@return
     */
    public function getCharge($type, $pcMngId, $users, $payType = PAY_ACTIVE)
    {
        $cLog = \share\util\getLog();
        $cLog->payLog(
            "#service#billsysUtil#getCharge#type=$type;pcMngId=$pcMngId;payType=$payType;user" . json_encode($users)
        );
        if (count($users) == 0) {
            return [];
        }
        // 保护，防止后续报错
        if (array_key_exists('all', $users) && empty($users['all'])) {
            return [];
        }
        $payType = intval($payType);
        $usersResult = $this->computedUserChargeDataAndPrice($type, $users, $payType, $pcMngId)['users'];

        return \share\util\arrayColumnAsKey($usersResult,'ID');
    }

    // 获取三方锁收费方案，并将锁品牌类型映射到云端品牌类型上
    public function getThirdLockCharge($lockChargeData)
    {
        $chargeModels = $this->queryThirdLockChargePlan(['ChargeData' => json_encode($lockChargeData)]);
        //格式 计费系统 => 云。 云：锁品牌类型：0=Qrio,1=Yale,2=BSI,3=Dormakaba,4=SL20,5=Salto,6=Itec,7=TT锁
        // 计费系统：1-Itec 2-Dormakaba
        $brandMap = [1 => 6, 2 => 3];
        $payerRole = \share\util\getPayerRole();
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        foreach ($chargeModels as $key => $val) {
            $tmpThirdLock = [];
            foreach ($val['ThirdLock'] as $k => $v) {
                $billBrand = $v['Brand'];
                $cloudBrand = $brandMap[$billBrand];
                if ($payerRole != PAYER_ROLE_ALL) {
                    $feeRoleConfig = $v['FeeRoleConfig'];
                    $feeRoleConfig = array_column($feeRoleConfig, null, 'Type');
                    $replaceFee = true;
                    if ($payerRole == PAYER_ROLE_PM) { //如果是PM支付，需要校验项目的PM FEE开关
                        $projectUUID = $lockChargeData[$key]['Instance']['ProjectUUID'];
                        $isPmFee = $dao->communityInfo->selectByKey('AccountUUID', $projectUUID)[0]['IsPMFee'];
                        if ($isPmFee != '1') {
                            $replaceFee = false;
                        }
                    }
                    if ($replaceFee) {
                        if (isset($feeRoleConfig[$payerRole]['ActiveFee'])) {
                            $v['ActiveFee'] = $feeRoleConfig[$payerRole]['ActiveFee'];
                        }
                        if (isset($feeRoleConfig[$payerRole]['MonthlyFee'])) {
                            $v['MonthlyFee'] = $feeRoleConfig[$payerRole]['MonthlyFee'];
                        }
                    }
                }

                $tmpThirdLock[$cloudBrand] = $v;
            }
            $chargeModels[$key]['ThirdLock'] = $tmpThirdLock;
        }

        return $chargeModels;
    }

    public function getVideoStorageCharge($sites)
    {
        if (count($sites) == 0) {
            return [];
        }
        $result = $this->computedVideoStorageChargeDataAndMonthPrice($sites, [])['sites'];
        return \share\util\arrayColumnAsKey($result, 'UUID');
    }

    public function getRentManagerChargePlan($disAccount, $insAccount)
    {
        $instance['Distributor'] = $disAccount;
        $instance['Installer'] = $insAccount;
        $chargeData ['Instance'] = $instance;
        $chargeDataArray [] = $chargeData;
        $chargeDataStr = json_encode($chargeDataArray);
        $cLog = \share\util\getLog();
        $cLog->payLog(
            "#service#billsysUtil#getRentManagerChargePlan#Distributor=$disAccount;Installer=$insAccount;ChargeData=" . $chargeDataStr
        );

        return $this->bmPostRequest('checkRentManagerChargePlan', ['ChargeData'=> $chargeDataStr])['data'][0];
    }

    /**
     * @description: 获取新办公计费模型
     * @params: $ChargeDataList 每个Item ['ChargeData'] = ['Distributor' => 'dis的account', 'Installer' => 'ins的account', 'Project' => '项目的Location']
     * @return: 计费模型
     * @author: shoubin.chen 2025/3/9 V7.1.0
     * @lastEditor: shoubin.chen 2025/3/9 V7.1.0
     */
    public function getNewOfficeChargePlan($ChargeDataList)
    {
        //格式校验
        foreach ($ChargeDataList as $instance) {
            if (!isset($instance['Distributor']) || !isset($instance['Installer']) || !isset($instance['Project'])) {
                throw new \Exception('getNewOfficeChargePlan ChargeDataList format error');
            }
        }

        $chargeDataStr = json_encode($ChargeDataList);
        $cLog = \share\util\getLog();
        $cLog->payLog(
            "#service#billsysUtil#checkNewOfficeInstance#ChargeDataList=" . $chargeDataStr
        );

        return $this->bmPostRequest('checkNewOfficeInstance', ['ChargeData' => $chargeDataStr])['data'];
    }

    public function createRentManager($totalPrice, $insAccount)
    {
        $db = \share\util\getDatabase();
        $cLog = \share\util\getLog();
        $cLog->debug(
            'params:totalPrice={totalPrice}; insAccount={insAccount};',
            ['totalPrice' => $totalPrice, 'insAccount' => $insAccount
            ]
        );

        //rentManager只有INS才能支付， 所以userType 直接设置成1，管理员； Payer设置成Ins的account
        $data = [
            'PayParams' => json_encode(
                [
                    'Type' => PAY_RENT_MANAGER,
                    'UserType' => 1,
                    'Payer' => $insAccount,
                    'PayerEmail' => "",
                    'Price' => $totalPrice
                ]
            )
        ];

        // 返回数据的ChargeData的Activity如果为空数组，则没有营销活动
        $result = $this->bmPostRequest('createorder', $data)['OrderData'];
        $token = \share\util\randString(54);
        $result['Token'] = $token;
        $result['Price'] = $totalPrice;

        return $result;
    }
    
    /*
     *@description 向计费系统请求创建订单，不做用户付费权限验证
     *<AUTHOR> 2022-04-01 11:55:19 V6.4
     *@lastEditor kxl 2022-04-01 11:55:19 V6.4
     *@param {*} accountID 付费者ID
     *@param {*} userType 付款者：0:终端用户，1：管理员。
     *@param {*} type enum('multiple','single','office')
     *@param {*} pcMngId installer or communityId or officeId or null
     *@param {*} payType 付款类型，0激活，1续费，2额外App... 使用常量PAY_ACTIVE | PAY_SUBSCRIPTION | PAY_ADD_APP | PAY_LANDLINE
     *@param {*} count 数量,一般为续费月数，激活等操作时，传1
     *@param {*} $mixData   多类型订单混合付费，包含以下两种数据
     *  users: pcMngId不为null时：主账户id数组;或者pcMngId为null时，common/order/util->getSubscribeUserInfo的处理结果
     *  videoStorages: common/order/util->getSubscribeVideoStorageInfo的处理结果
     *@param {*} otherData
     *@return
     */
    public function createOrder($accountID, $userType, $type, $pcMngId, $payType, $count, $mixData, $otherData = [])
    {
        $users = $mixData['Users'];
        $videoSiteInfo = $mixData['VideoStorages'];
        $thirdLockInfo = $mixData['ThirdLocks'];

        $db = \share\util\getDatabase();
        $cLog = \share\util\getLog();
        $cLog->debug(
            'params:accountID={accountID}; userType={userType}; type={type}; 
            pcMngId={pcMngId}; payType={payType}; count={count}; users={users}; mixData={mixData}; otherData={otherData}',
            ['accountID' => $accountID, 'userType' => $userType, 'type' => $type, 'pcMngId' => $pcMngId,
             'payType' => $payType, 'count' => $count, 'users' => $users, 'otherData' => $otherData, 'mixData' => $mixData
            ]
        );

        if ($userType == 0) {
            // 支付者信息
            $payerData = $db->querySList('select P.Account,U.Email from PersonalAccount P join PersonalAccountUserInfo U on P.UserInfoUUID=U.UUID where P.ID = :ID',
                [':ID' => $accountID])[0];
            $payAccount = $payerData['Account'];
            $payEmail = $payerData['Email'];
            $payEmail = ($payEmail == '' or is_null($payEmail)) ? $payEmail : \share\util\getAesForData()->decrypt($payEmail);
            // 普通用户
            $userType = 2;
        } else {
            // 支付者信息
            $payerData = $db->querySList(
                'select Account,Grade,ManageGroup from Account where ID = :ID',
                [':ID' => $accountID]
            )[0];
            if ($payerData['Grade'] == COMMUNITYGRADE || $payerData['Grade'] == PERSONGRADE
                || $payerData['Grade'] == OFFICEGRADE) {
                $payerData = $db->querySList(
                    'select Account from Account where ID = :ID',
                    [':ID' => $payerData['ManageGroup']]
                )[0];
            }
            $payAccount = $payerData['Account'];
            $payEmail = '';
            // 管理员
            $userType = 1;
        }

        if ($payType === PAY_LANDLINE) {
            //单住户高级功能
            $computedResult = $this->computedUserChargeDataAndPrice($type, $users, $payType, $pcMngId, $count, $otherData);
        } elseif ($payType === PAY_COMMUNITY_VIDEO_STORAGE || $payType === PAY_SINGLE_VIDEO_STORAGE) {
            //视频存储
            $computedResult = $this->computedVideoStorageChargeDataAndPrice($type, $videoSiteInfo, $payType, $otherData);
        } elseif (in_array($payType, [PAY_SINGLE_THIRDLOCK_ACTIVE, PAY_SINGLE_THIRDLOCK_RENEW, PAY_COMMUNITY_THIRDLOCK_ACTIVE, PAY_COMMUNITY_THIRDLOCK_RENEW])) {
            //三方锁
            $isRenew = in_array($payType, [PAY_SINGLE_THIRDLOCK_RENEW, PAY_COMMUNITY_THIRDLOCK_RENEW]) ? true : false;
            $computedResult = $this->computedThirdLockChargeDataAndPrice($type, $thirdLockInfo, $payType, $otherData, $isRenew);
        } elseif ($payType === PAY_MIX) {
            //多订单混合支付 -- 续费
            if ($type == PAY_TYPE_SINGLE) {
                $userComputedResult = $this->computedUserChargeDataAndPrice($type, $users, PAY_LANDLINE, $pcMngId, $count, $otherData);
            }elseif ($type == PAY_TYPE_MULTIPLE){
                $userComputedResult = $this->computedUserChargeDataAndPrice($type, $users, PAY_SUBSCRIPTION_BY_DAY, $pcMngId, $count, $otherData);
            }
            $otherData['Count'] = $count;
            $videoComputedResult = $this->computedVideoStorageChargeDataAndPrice($type, $videoSiteInfo, $payType, $otherData);
            $thirdLockComputedResult = $this->computedThirdLockChargeDataAndPrice($type, $thirdLockInfo, $payType, $otherData, true);
            $computedResult['price'] = $userComputedResult['price'] + $videoComputedResult['price'] + $thirdLockComputedResult['price'];
            $computedResult['users'] = $userComputedResult['users'];
            $computedResult['sites'] = $videoComputedResult['sites'];
            $computedResult['thirdLockChargeData'] = $thirdLockComputedResult['thirdLockChargeData'];
        } elseif ($payType === PAY_MIX_ACTIVE) {
            //多订单混合支付 -- 激活
            $userComputedResult = $this->computedUserChargeDataAndPrice($type, $users, PAY_ACTIVE, $pcMngId, 1, $otherData);
            $otherData['Count'] = $count;
            $thirdLockComputedResult = $this->computedThirdLockChargeDataAndPrice($type, $thirdLockInfo, $payType, $otherData, false);
            $computedResult['price'] = $userComputedResult['price'] + $thirdLockComputedResult['price'];
            $computedResult['users'] = $userComputedResult['users'];
            $computedResult['thirdLockChargeData'] = $thirdLockComputedResult['thirdLockChargeData'];
        } else {
            //其他情况走原先的
            $computedResult = $this->computedUserChargeDataAndPrice($type, $users, $payType, $pcMngId, $count, $otherData);
        }

        $price = $computedResult['price'];
        $userChargeResult = $computedResult['users'];
        $siteChargeResult = $computedResult['sites'];
        $thirdLockChargeResult = isset($computedResult['thirdLockChargeData']) ? $computedResult['thirdLockChargeData'] : [];

        /*
        计费系统需要的Type和业务的Type有点区别，计费系统没有落地续费的type，落地和一般续费是一致的。因此后续type没有对齐
        计费的支付类型是支付类型,0:激活，1:月租,2：买App付费 3：高级功能一次性付费 4：高级功能按月付费 5：补差价 ,6：按天续费
        9:rent manager续费 10：视频存储续费 11：多订单（父子订单）合并续费  13新办公付费' 14-三方锁激活 15-三方锁续费
        */
        $bmPayType = [0 => 0, 1 => 1, 2 => 2, 3 => 1, 6 => 6, 10 => 10, 11 => 10, 13 => 11, PAY_MIX_ACTIVE => 11, PAY_SINGLE_THIRDLOCK_ACTIVE => 14, PAY_COMMUNITY_THIRDLOCK_ACTIVE => 14,
            PAY_SINGLE_THIRDLOCK_RENEW => 15, PAY_COMMUNITY_THIRDLOCK_RENEW => 15];
        $data = [
            'PayParams' => json_encode(
                [
                    'Type' => $bmPayType[$payType],
                    'UserType' => $userType,
                    'Payer' => $payAccount,
                    'PayerEmail' => $payEmail,
                    'Price' => $price
                ]
            )
        ];

        // 返回数据的ChargeData的Activity如果为空数组，则没有营销活动
        $result = $this->bmPostRequest('createorder', $data)['OrderData'];
        $token = \share\util\randString(54);
        $result['Token'] = $token;
        $result['User'] = $userChargeResult;
        $result['Price'] = $price;
        $result['Site'] = $siteChargeResult;
        $result['ThirdLock'] = $thirdLockChargeResult;

        return $result;
    }


    /*
     *@description 计算获取用户收费情况和总价格
     *<AUTHOR> 2022-04-06 14:40:08 V6.4
     *@lastEditor kxl 2022-04-06 14:40:08 V6.4
     *@param {*} type enum('multiple','single','office')
     *@param {*} users pcMngId不为null时：主账户id数组;或者pcMngId为null时，common/order/util->getSubscribeUserInfo的处理结果
     *@param {*} payType 付款类型，0激活，1续费，2额外App... 使用常量PAY_ACTIVE | PAY_SUBSCRIPTION | PAY_ADD_APP | PAY_LANDLINE
     *@param {*} pcMngId installer or communityId or officeId or null
     *@param {*} count 数量,一般为续费月数，激活等操作时，传1
     *@param {*} otherData
     *@return
     */
    public function computedUserChargeDataAndPrice($type, $users, $payType, $pcMngId, $count = 1, $otherData = [])
    {
        $db = \share\util\getDatabase();

        $isMixed = false;
        $projects = [];
        // 存储从账户和主账户ID对应关系，混合计算价格时，需要知道这次是否有从账户
        $mainIdToSubIds = [];
        // 存储这次付费主账户的Id
        $mainIds = [];
        // 按项目分类，取出所有在主账户ID，用于后续计算计费条件
        if ($pcMngId !== null) {
            if (!\share\util\isOrderArray($users)) {
                throw new \Exception(var_export($users, true). ' is not a order array');
            }

            if ($payType === PAY_SUBSCRIPTION_BY_DAY) {
                throw new \Exception('Pay by day must use mixed pay.');
            }

            // 只需要主账户级别的ID
            $projects[$pcMngId] = $users;
            $mainIds = $users;
        } else {
            $isMixed = true;
            // $main = $users['main'];
            $sub = $users['sub'];
            // $pm = $users['pm'];
            $allUserInfo = $users['all'];

            // 非激活混合支付，不能有从账户数据
            if ($payType !== PAY_ACTIVE && $payType !== PAY_ADD_APP && count($sub) !== 0) {
                throw new \Exception('Inactive cannot have slave account data.');
            }

            foreach ($allUserInfo as $value) {
                $projectId = $value['ProjectId'];
                if (!array_key_exists($projectId, $projects)) {
                    $projects[$projectId] = [];
                }
                // 只需要主账户级别的ID，从账户取主账户ID存入
                $role = intval($value['Role']);
                if (in_array($role, SUBROLE)) {
                    $mainId = $value['ParentID'];
                    if (!array_key_exists($mainId, $mainIdToSubIds)) {
                        $mainIdToSubIds[$mainId] = [];
                    }
                    array_push($mainIdToSubIds[$mainId], $value['ID']);
                } else {
                    $mainId = $value['ID'];
                    array_push($mainIds, $mainId);
                }

                // 拿去计费计算只需要主账户Id，从账户到算钱时一起计算
                if (!in_array($mainId, $projects[$projectId])) {
                    array_push($projects[$projectId], $mainId);
                }
            }
        }

        $projectIds = array_keys($projects);

        // 兼容保护 组合付费没有用户类型
        if (empty($projectIds)) {
            return [
                'users' => [],
                'price' => 0
            ];
        }

        $projectTimes = $db->querySList('select TimeZone,ID from Account
        where ID in ('.implode(',', $projectIds).')');
        $projectTimesIdKey = [];
        foreach ($projectTimes as $projectTime) {
            $projectTimesIdKey[$projectTime['ID']] = $projectTime['TimeZone'];
        }


        // 每个project传输计费系统收费信息
        $chargeData = [];
        // key和id映射关系
        $usersKeyToIds = [];
        // 保存用户数据
        $usersActivities = [];
        // $projects中只保存主账户ID和从账户对应主账户ID保存
        foreach ($projects as $projectId => $userIds) {
            // 获取所有主账户的收费信息
            list($instance, $newUsers, $activities) = $this->getInstanceUserData(
                $type,
                $projectId,
                $userIds,
                $payType,
                $otherData
            );

            $chargeItem = ['Instance' => $instance];
            // 生成key并分组，因为相同社区或者单住户下，ins或者小区的条件都是一致的，因此不需要考虑
            list($chargeItem['User'], $userKeyToIds) = $this->getChargeDataAndUserAssocArray($newUsers);
            // 数组顺序和chargeData保持一致
            array_push($chargeData, $chargeItem);
            array_push($usersKeyToIds, $userKeyToIds);
            array_push($usersActivities, $activities);
        }

        // 查询收费计划
        $chargeResult = $this->queryChargePlan(['ChargeData' => json_encode($chargeData)]);
        // 根据映射关系,计算价格,生成用户数据
        $userChargeResult = [];
        $price = 0;
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        foreach ($chargeResult as $index => $chargeItem) {
            $userKeyToIds = $usersKeyToIds[$index];
            $activities = $usersActivities[$index];

            foreach ($chargeItem as $userCharge) {
                $key = $userCharge['Key'];
                // 取出的ID都是主账户ID，之前从账户也被转换为主账户ID，真正计算时必须用从账户计算
                $userTempIds = $userKeyToIds[$key];
                $userIds = [];
                foreach ($userTempIds as $userTempId) {
                    if (in_array($userTempId, $mainIds)) {
                        array_push($userIds, $userTempId);
                    }
                    if (array_key_exists($userTempId, $mainIdToSubIds)) {
                        $userIds = array_merge($userIds, $mainIdToSubIds[$userTempId]);
                    }
                }
                // 同一个收费模型的projectId是一致的
                if ($pcMngId !== null) {
                    $tempProjectId = $pcMngId;
                } else {
                    $tempMainUserId = $userTempIds[0];
                    foreach ($allUserInfo as $tempItem) {
                        if ($tempItem['ID'] === $tempMainUserId || $tempItem['ParentID'] === $tempMainUserId) {
                            $tempProjectId = $tempItem['ProjectId'];
                            break;
                        }
                    }
                }
                // 7.1.3 不同支付者 不同收费
                $payerRole = \share\util\getPayerRole();
                if ($payerRole != PAYER_ROLE_ALL) {
                    $replaceFee = true;
                    if ($payerRole == PAYER_ROLE_PM) { //如果是PM支付，需要校验项目的PM FEE开关
                        $isPmFee = $dao->communityInfo->selectByKey('AccountID', $tempProjectId)[0]['IsPMFee'];
                        if ($isPmFee != '1') {
                            $replaceFee = false;
                        }
                    }
                    if ($replaceFee) {
                        if (!empty($userCharge['ActiveFee']) && isset($userCharge['ActiveFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['ActiveFeeRoleConfig'], null, 'Type');
                            $userCharge['ActiveFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['ActiveFee'];
                        }
                        if (!empty($userCharge['MonthlyFee']) && isset($userCharge['MonthlyFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['MonthlyFeeRoleConfig'], null, 'Type');
                            $userCharge['MonthlyFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['MonthlyFee'];
                        }
                        if (!empty($userCharge['AddAppFee']) && isset($userCharge['AddAppFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['AddAppFeeRoleConfig'], null, 'Type');
                            $userCharge['AddAppFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['AddAppFee'];
                        }
                    }
                }

                $priceData = [
                    'ActiveFee' => $userCharge['ActiveFee'],
                    'MonthlyFee' => $userCharge['MonthlyFee'],
                    'AddAppFee' => $userCharge['AddAppFee'],
                    'AppNumber' => $userCharge['AppNumber'],
                    'Introduction' => null,
                    'ActivityId' => null,
                    'ActiveSaveFee' => 0,
                    'MonthlySaveFee' => 0,
                    'AddAppSaveFee' => 0,
                    'ActiveFeePercent' => 100,
                    'MonthlyFeePercent' => 100,
                    'AddAppFeePercent' => 100
                ];

                // 过滤营销活动，由于之前将相同家庭的主账户和从账户合并，算钱时需要同步算上
                foreach ($userIds as $userId) {
                    $activity = array_key_exists($userId, $activities) ? $activities[$userId] : [];
                    if (count($userCharge['Activity']) !== 0) {
                        $activityUUID = $userCharge['Activity']['UUID'];
                        if (!in_array($activityUUID, $activity)) {
                            $priceData = [
                                'ActiveFee' => $userCharge['Activity']['ActiveFee'],
                                'MonthlyFee' => $userCharge['Activity']['MonthlyFee'],
                                'AddAppFee' => $userCharge['Activity']['AddAppFee'],
                                'AppNumber' => $userCharge['AppNumber'],
                                'Introduction' => $userCharge['Activity']['Introduction'],
                                'ActivityId' => $userCharge['Activity']['UUID'],
                                'ActiveSaveFee' => $userCharge['ActiveFee']-$userCharge['Activity']['ActiveFee'],
                                'MonthlySaveFee' => $userCharge['MonthlyFee']-$userCharge['Activity']['MonthlyFee'],
                                'AddAppSaveFee' => $userCharge['AddAppFee']-$userCharge['Activity']['AddAppFee'],
                                'ActiveFeePercent' => round(\share\util\inputComputedCount(
                                    $userCharge['Activity']['ActiveFee'] / $userCharge['ActiveFee']
                                )),
                                'MonthlyFeePercent' => round(\share\util\inputComputedCount(
                                    $userCharge['Activity']['MonthlyFee'] / $userCharge['MonthlyFee']
                                )),
                                'AddAppFeePercent' => round(\share\util\inputComputedCount(
                                    $userCharge['Activity']['AddAppFee'] / $userCharge['AddAppFee']
                                ))
                            ];
                        }
                    }

                    $userResultData = [
                        'ID'=> $userId
                    ];
                    if ($payType === PAY_ACTIVE) {
                        // 激活时需要考虑混合激活存在从账户的情况
                        $unitPrice = 0;
                        if (!$isMixed) {
                            $unitPrice = $priceData['ActiveFee'];
                        } else {
                            $role = intval($allUserInfo[$userId]['Role']);
                            if (in_array($role, MAINROLE)) {
                                $unitPrice = $priceData['ActiveFee'];
                            } elseif (in_array($role, SUBROLE)) {
                                $unitPrice = $priceData['AddAppFee'];
                            }
                        }
                    } elseif ($payType === PAY_SUBSCRIPTION || $payType === PAY_LANDLINE) {
                        $unitPrice = $priceData['MonthlyFee'];
                    } elseif ($payType === PAY_ADD_APP) {
                        // 单独App付费兼容
                        $unitPrice = $priceData['AddAppFee'];
                    } elseif ($payType === PAY_SUBSCRIPTION_BY_DAY) {
                        // 按天续费，单价要按天计算
                        $nextTime = $otherData['NextTime'];
                        $expireTime = $allUserInfo[$userId]['ExpireTime'];
                        
                        if (intval($allUserInfo[$userId]['Role']) === PERENDMROLE) {
                            // 单住户时区看自己
                            $timeZone = $allUserInfo[$userId]['TimeZone'];
                        } else {
                            // 其他时区看项目
                            $timeZone = $projectTimesIdKey[$allUserInfo[$userId]['ProjectId']];
                        }

                        list($unitPrice, $days) = $this->computedPriceForDay(
                            $priceData['MonthlyFee'],
                            $expireTime,
                            $nextTime,
                            $timeZone
                        );
                        $userResultData['DayFee'] = $unitPrice;
                        $userResultData['Days'] = $days;
                    } else {
                        throw new \Exception("error pay type $payType");
                    }

                    // 存储用户收费情况数据
                    array_push($userChargeResult, array_merge($userResultData, $userCharge, $priceData));
                    
                    // 总价
                    $price += \share\util\inputComputedCount($unitPrice);
                }
            }
        }

        $price = \share\util\outputComputedCount($price * $count);

        return [
            'users' => $userChargeResult,
            'price' => $price
        ];
    }

    /**
     * @description: 按日续费计算价格
     * @param {number} unit:单价
     * @return price, originalPrice都是扩大100倍的金额，使用outputComputedCount还原
    */
    public function computedPriceForDay($unit, $expireTime, $nextTime, $timeZone)
    {
        $cLog = \share\util\getLog();
        $cLog->debug(
            'unit={unit};expireTime={expireTime};nextTime={nextTime};timeZone={timeZone}',
            ['unit'=>$unit,'expireTime'=>$expireTime,'nextTime'=>$nextTime,'timeZone'=>$timeZone]
        );
        $expireTime = strtotime($expireTime) > time() ? $expireTime : \share\util\getNow();
        // 把expiretime转换为客户时区进行计算,因为按天只算年月日,这样才准确.nextTime本来就是按客户时区,不用转换
        $expireTime = \share\util\setTimeZone($expireTime, $timeZone, 3);

        $days = \share\util\computeDiffDays($expireTime, $nextTime);
        return [\share\util\outputComputedCount(\share\util\inputComputedCount($unit / 30 * $days)), $days];
    }


    /*
     *@description 生成user charge data和对应key映射关系
     *<AUTHOR> 2022-04-02 10:43:22 V6.4
     *@lastEditor kxl 2022-04-02 10:43:22 V6.4
     *@param {*} users getInstanceUserData的user返回值
     *@return [user的charge data, key和user映射关系]
     */
    public function getChargeDataAndUserAssocArray($users)
    {
        $userKeyToIds = [];
        $userCharge = [];
        foreach ($users as $value) {
            $key = $value['Indoor'].'-'
                .$value['Landline'].'-'
                .$value['MonitorPlan'].'-'
                .$value['PMApp']
                // 办公smart plus开关
                .(array_key_exists('IsOpenSmartPlusIntercom', $value)
                    ? '-'.$value['IsOpenSmartPlusIntercom']
                    : '');

            if (!array_key_exists($key, $userKeyToIds)) {
                $userKeyToIds[$key] = [];
                $item = [
                    'Key' => $key,
                    'Indoor' => $value['Indoor'],
                    'Landline' => $value['Landline'],
                    'MonitorPlan' => $value['MonitorPlan'],
                    'PMApp' => $value['PMApp'],
                ];
                if (array_key_exists('IsOpenSmartPlusIntercom', $value)) {
                    $item['IsOpenSmartPlusIntercom'] = $value['IsOpenSmartPlusIntercom'];
                }
                
                array_push($userCharge, $item);
            }
            array_push($userKeyToIds[$key], $value['ID']);
        }

        return [$userCharge, $userKeyToIds];
    }

    /*
     *@description 获取主账户级别的收费条件，包括pmApp，如果需要从账户的收费条件，应该把对应主账户id传进来
     *<AUTHOR> 2022-03-31 17:38:31 V6.4
     *@lastEditor kxl 2022-03-31 17:38:31 V6.4
     *@param {*} type enum('multiple','single','office')
     *@param {string} pcMngId
     *@param {array} users 主账户id数组
     *@param {*} payType 常量：PAY_ACTIVE | PAY_SUBSCRIPTION | PAY_ADD_APP | PAY_LANDLINE
     *@return [{CommunityName: string, CreateTime: string, Installer:string, Distributor: string,
        FamilyNumber:string, Type:number},
     *[{ID:string, Indoor:number, Landline:number, MonitorPlan:number, Days:number, PMApp:number}],
     {userId string: [activity uuid1 ....]}
     */
    public function getInstanceUserData($type, $pcMngId, $users, $payType)
    {
        $db = \share\util\getDatabase();
        $newUser = [];
        // 营销活动
        $activitiesIdKey = [];
        $userIds = $users;
        $users = [];
        $bindArray = [];
        $idConditions = [];
        foreach ($userIds as $key => $userId) {
            $bindArray[":ID$key"] = $userId;
            array_push($idConditions, ":ID$key");
        }

        if ($type == 'multiple' || $type == 'office') {
            $now = \share\util\getNow();
            if ($type == 'multiple') {
                $data = $db->querySList(
                    'select A.Location as CommunityName,A.CreateTime,B.Account as Installer,C.Account as Distributor,
                CI.NumberOfApt as FamilyNumber,(CI.Switch & 1) as Landline from Account A 
                join Account B on B.ID = A.ManageGroup join 
                Account C on C.ID = B.ParentID join CommunityInfo CI on A.ID = CI.AccountID where A.ID = :ID',
                    [':ID' => $pcMngId]
                )[0];
            } else {
                $data = $db->querySList(
                    'select A.Location as CommunityName,A.CreateTime,B.Account as Installer,C.Account as Distributor,
                (OI.Switch & 1) as Landline from Account A join Account B on B.ID = A.ManageGroup 
                join Account C on C.ID = B.ParentID 
                join OfficeInfo OI on A.UUID = OI.AccountUUID where A.ID = :ID',
                    [':ID' => $pcMngId]
                )[0];
                $data['FamilyNumber'] = '1';
            }

            // v6.3新增办公
            $data['Type'] = $type == 'multiple' ? 1 : 3;

            // V6.1高级功能方案"之前"的室内机条件,小区和办公看整个小区
            $indoor = $db->querySList(
                'select count(*) from Devices where MngAccountID = :MngAccountID and Type = 2',
                [':MngAccountID' => $pcMngId]
            )[0]['count(*)'] == 0 ? 0 : 1;
        } else {
            $data = $db->querySList(
                'select B.Account as Installer,C.Account as Distributor from Account B join Account C on C.ID = B.ParentID where B.ID = :ID',
                [':ID' => $pcMngId]
            )[0];
            $data['Type'] = 2;
            $data['FamilyNumber'] = '1';
        }

        if (count($userIds) !== 0) {
            $basics = $db->querySList(
                'select ID,Account,Active,ActiveTime,ExpireTime,PhoneExpireTime,PhoneStatus,Role,UUID,Switch 
                from PersonalAccount where ID in ('.implode(',', $idConditions).')',
                $bindArray
            );

            if (count($basics) !== count($userIds)) {
                throw new \Exception('error userIds'.var_export($userIds, true));
            }

            $basicsIdKey = [];
            // $basicUuidKey = [];
            // $basicAccountKey = [];
            $uuidCondition = [];
            $uuidBindArray = [];
            $accountCondition = [];
            $accountBindArray = [];
            foreach ($basics as $index => $basic) {
                $basicsIdKey[$basic['ID']] = $basic;
                // $basicUuidKey[$basic['UUID']] = $basic;
                // $basicAccountKey[$basic['Account']] = $basic;

                array_push($uuidCondition, ":UUID$index");
                $uuidBindArray[":UUID$index"] = $basic['UUID'];

                array_push($accountCondition, ":Account$index");
                $accountBindArray[":Account$index"] = $basic['Account'];
            }

            // 6.1后室内机绑定情况
            $monitors = $db->querySList(
                'select D.ID, P.ID as userId from DevicesSpecial D join PersonalAccount P on D.Account = P.Account 
                where P.ID in ('.implode(',', $idConditions).')',
                $bindArray
            );
            $monitorsIdKey = [];
            foreach ($monitors as $monitor) {
                $monitorsIdKey[$monitor['userId']] = $monitor;
            }

            // 办公用户的详情
            $officeInfos = $db->querySList(
                'SELECT Flags,PersonalAccountUUID FROM PersonalAccountOfficeInfo WHERE PersonalAccountUUID in ('.
                implode(',', $uuidCondition).')',
                $uuidBindArray
            );
            $officeInfosUuidKey = [];
            foreach ($officeInfos as $officeInfo) {
                $officeInfosUuidKey[$officeInfo['PersonalAccountUUID']] = $officeInfo;
            }

            // 6.1前的单住户室内机绑定情况条件
            $perDevices = $db->querySList('select Node, count(*) as total from PersonalDevices where Node in ('.
            implode(',', $accountCondition).') group by Node', $accountBindArray);
            $perDevicesAccountKey = [];
            foreach ($perDevices as $perDevice) {
                $perDevicesAccountKey[$perDevice['Node']] = $perDevice;
            }


            // 续费时，不确定原来调用的传值，因此暂时不用===
            if ($payType == 1) {
                $activities = $db->querySList(
                    'select Activity, PersonalAccountID from UserActivityUsed where PersonalAccountID in ('.implode(',', $idConditions).')',
                    $bindArray
                );
            } else {
                $activities = [];
            }

            foreach ($activities as $activity) {
                $activitiesIdKey[$activity['PersonalAccountID']][] = $activity['Activity'];
            }

            // 整合用户计费条件
            foreach ($userIds as $key => $userId) {
                $basic = $basicsIdKey[$userId];
                $role = intval($basic['Role']);
                if (!in_array($role, [PERENDMROLE, COMENDMROLE, OFFSTAFFROLE, OFFPERSONNELROLE, PMENDMROLE, OFF_NEW_PERSONNEL_ROLE])) {
                    throw new \Exception("user id=$userId is not a main account");
                }

                $isOpenSmartPlusIntercom = null;
                // v6.3 新增办公查询计费key
                if (in_array($role, OFFROLE)) {
                    $officeInfoFlag = $officeInfosUuidKey[$basic['UUID']]['Flags'];
                    $isOpenSmartPlusIntercom = \share\util\getSpecifyBitLE($officeInfoFlag, 1);
                }

                // 6.2新增参数MonitorPlan:室内机是否必须绑定
                if (array_key_exists($userId, $monitorsIdKey)) {
                    $monitorPlan = 1;
                } else {
                    $monitorPlan = 0;
                }

                // $expireTime = strtotime($basic['ExpireTime']) > strtotime($now) ? $basic['ExpireTime'] : $now;
                // if (isset($otherData['NextTime'])) {
                //     $days = \share\util\computeDiffDays($expireTime, $otherData['NextTime']);
                // }

                if ($type == 'multiple' || $type == 'office') {
                    $landLine = $data['Landline'];
                } else {
                    // V6.5.2 单住户落地开关改为高级功能开关，单住户计费看高级功能
                    // 根据用户的落地开关判断是否使用落地
                    $landLine = \share\util\getSpecifyBitLE($basic['Switch'], 2);
                    // 用户的室内机使用情况,单住户单独看，和社区办公不一样,TODO
                    $indoor = $perDevicesAccountKey[$basic['Account']]['total'] == 0 ? 0 : 1;
                }

                $userItem = [
                    'ID' => $userId,
                    'Indoor' => $indoor,
                    'Landline' => intval($landLine),
                    'MonitorPlan' => $monitorPlan,
                    // 'Days' => !isset($days) ? 0 : $days,
                    'PMApp' => $role === PMENDMROLE ? 1 : 0
                ];
                if (in_array($role, OFFROLE)) {
                    $userItem['IsOpenSmartPlusIntercom'] = $isOpenSmartPlusIntercom;
                }

                array_push($newUser, $userItem);
            }
        }

        if (array_key_exists('Landline', $data)) {
            unset($data['Landline']);
        }
        
        return [$data, $newUser, $activitiesIdKey];
    }

    /*
     *@description 查询收费条件
     *<AUTHOR> 2022-04-02 11:12:58 V6.4
     *@lastEditor kxl 2022-04-02 11:12:58 V6.4
     *@param {*} data http://**************:40001/project/83/interface/api/195
     *@return http://**************:40001/project/83/interface/api/195
     */
    public function queryChargePlan($data)
    {
        // TODO 除了创建订单，其他调用也要更改
        return $this->bmPostRequest('checkchargeplan', $data)['data'];
    }

    public function queryChargePlanWithDiffPayer($data)
    {
        // TODO 除了创建订单，其他调用也要更改
        $chargeResult = $this->bmPostRequest('checkchargeplan', $data)['data'];
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        foreach ($chargeResult as $index => &$chargeItem) {
            foreach ($chargeItem as &$userCharge) {
                // 7.1.3 不同支付者 不同收费
                $payerRole = \share\util\getPayerRole();
                if ($payerRole != PAYER_ROLE_ALL) {
                    $replaceFee = true;
                    if ($payerRole == PAYER_ROLE_PM) { //如果是PM支付，需要校验项目的PM FEE开关,暂时用不到pm，先注释
//                        $isPmFee = $dao->communityInfo->selectByKey('AccountID', $tempProjectId)[0]['IsPMFee'];
//                        if ($isPmFee != '1') {
//                            $replaceFee = false;
//                        }
                    }
                    if ($replaceFee) {
                        if (!empty($userCharge['ActiveFee']) && isset($userCharge['ActiveFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['ActiveFeeRoleConfig'], null, 'Type');
                            $userCharge['ActiveFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['ActiveFee'];
                        }
                        if (!empty($userCharge['MonthlyFee']) && isset($userCharge['MonthlyFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['MonthlyFeeRoleConfig'], null, 'Type');
                            $userCharge['MonthlyFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['MonthlyFee'];
                        }
                        if (!empty($userCharge['AddAppFee']) && isset($userCharge['AddAppFeeRoleConfig'])) {
                            $roleConfigFee = array_column($userCharge['AddAppFeeRoleConfig'], null, 'Type');
                            $userCharge['AddAppFee'] = isset($roleConfigFee[$payerRole]) ? $roleConfigFee[$payerRole]['Fee'] : $userCharge['AddAppFee'];
                        }
                    }
                }
            }
            unset($userCharge);
        }
        unset($chargeItem);

        return $chargeResult;
    }

    public function bmPostRequest($url, $data)
    {
        $cLog = \share\util\getLog();
        $cLog->payLog('#service#billsysUtil#bmPostRequest#' . BMURL . "$url#params=" . json_encode($data));
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, BMURL . $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);

        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        //超时，只需要设置一个秒的数量就可以
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);

        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        $data = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        if ($httpCode != 200) {
            throw new \Exception("http code is $httpCode not 200. error is " . curl_error($curl));
        }
        $cLog->payLog("#service#billsysUtil#bmPostRequest#$url#httpCode=$httpCode;data=$data");
        $result = json_decode($data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("json parse error, $data is not a json string.");
        }

        if ($result['code'] != 0) {
            throw new \Exception("error data : $data");
        }

        return $result;
    }


    /**
     * @param $charges [id=>[ActiveFee=>xx,MonthlyFee=>xx,ActivityId?=>xxx]]
     */
    public function computedExTimeActive($charges, $id)
    {
        // 激活费为0，自动激活并使用1个月
        $charges = $charges[$id];
        $active = 0;
        $expireTime = \share\util\getNow();
        if ($charges['ActiveFee'] == 0) {
            $active = 1;
        }
        if ($charges['MonthlyFee'] == 0) {
            if (array_key_exists('ActivityId', $charges) && null !== $charges['ActivityId']) {
                $expireTime = $this->getActiveExpire($id, $charges['ActivityId'], $expireTime);
            } else {
                $expireTime = DEFAULTEXPIRETIME;
            }
        } else {
            $expireTime = \share\util\computedLastDate($expireTime, 1);
        }

        return [$active, $expireTime];
    }

    /**
     * 取消订单.
     *
     * @param ['Number'=>$BmOrderNumber, 'Code'=>$PayCode]
     */
    public function cancelOrder($data)
    {
        return $this->bmPostRequest('cancelorder', $data);
    }

    /**
     * @description: 获取计费系统订单信息
     * @param $data ['Number'=>$BmOrderNumber]
     * @return mixed
     * @throws \Exception
     * @author: csc 2025/4/11 15:52 V7.1.0
     * @lastEditors: csc 2025/4/11 15:52 V7.1.0
     */
    public function getBmOrder($data)
    {
        return $this->bmPostRequest('queryOrder', $data);
    }

    /**
     * 获取高级功能收费方案.
     *
     * @param data: 获取方案携带参数['FeeUUID', 'FeaturePlanType', 'FeatureFeeType']
     */
    public function queryFeaturePlan($data)
    {
        $url = \share\util\bmGetUrl('getfeaturefeetocloud', $data);
        $http = new HttpClient();
        $res = $http->get($url)->exec()->result();

        return $res;
    }

    /**
     * @description: 查询计费系统视频存储收费计划
     * @return mixed http://**************:40001/project/83/interface/api/15717
     * @author: shoubin.chen 2024/10/29 17:24:57 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/29 17:24:57  V7.1.0
     */
    public function queryVideoStorageChargePlan($data)
    {
        return $this->bmPostRequest('checkFeatureBillingChargePlan', $data)['data'];
    }

    /**
     * @description: 查询计费系统三方锁收费计划
     * http://**************:40001/project/83/interface/api/17983
     * @param $data
     * @return mixed
     * @throws \Exception
     * @author: csc 2025/2/6 16:54 V7.1.0
     * @lastEditors: csc 2025/2/6 16:54 V7.1.0
     */
    public function queryThirdLockChargePlan($data)
    {
        return $this->bmPostRequest('checkFeatureBillingChargePlanForThirdLock', $data)['data'];
    }

    // Dis刷新社区高级功能
    public function checkFeaturePlan($accountID, $step = 1)
    {
        $db = \share\util\getDatabase();
        $cLog = \share\util\getLog();
        $cLog->debug('dis query Account;AccountID={AccountID}', ['AccountID' => $accountID]);
        // 获取社区ID
        $community = $db->querySList(
            'select A.ID,C.FeatureExpireTime from Account A join CommunityInfo C on A.ID = C.AccountID where A.ParentID = :ParentID AND A.Grade = :Grade',
            [':ParentID' => $accountID, ':Grade' => COMMUNITYGRADE]
        );

        $comList = [];
        $total = 0;
        if (count($community) == 0) {
            return [$comList, $total];
        }
        foreach ($community as $val) {
            $cLog->debug('dis query ManageFeature;AccountID={AccountID}', ['AccountID' => $val['ID']]);
            $data = $db->querySList(
                'select D.FeeUUID as FeeUUID from ManageFeature D join ManageFeature C on D.FeatureID = C.FeatureID where C.AccountID = :AccountID and D.AccountID = :DisID',
                [':AccountID' => $val['ID'], ':DisID' => $accountID]
            );
            if (count($data) > 0) {
                $fpdata = ['FeeUUID' => $data[0]['FeeUUID'], 'FeaturePlanType' => 0];
                $cLog->debug(
                    'queryFeaturePlan;FeaturePlanType=0;FeeUUID={FeeUUID}',
                    ['FeeUUID' => $data[0]['FeeUUID']]
                );
                // 向计费系统获取Dis的高级方案,FeatureFeeType：0 一次性收费，1 按月收费 FeaturePlanType：0：社区community 1：办公office
                $bmFeature = $this->queryFeaturePlan($fpdata)['data'][0];
                $needChange = false;
                // 付费社区
                if ($val['FeatureExpireTime'] != '') {
                    // 月费改成一次性收费
                    if ($bmFeature['FeatureFeeType'] == 0 && $val['FeatureExpireTime'] != DEFAULTEXPIRETIME) {
                        $needChange = true;
                        $newExpireTime = DEFAULTEXPIRETIME;
                    } elseif ($bmFeature['FeatureFeeType'] == 1 && $val['FeatureExpireTime'] == DEFAULTEXPIRETIME) {
                        // 一次性收费改成月费
                        $needChange = true;
                        $newExpireTime = date('Y-m-d H:i:s', strtotime('+3 day'));
                    }
                }
                if ($needChange && $step == 0) {
                    $cLog->debug(
                        'query Community FeaturePlan;FeaturePlanType=0;FeeUUID={FeeUUID}',
                        ['FeeUUID' => $data[0]['FeeUUID']]
                    );
                    $comInfos = $db->querySList(
                        'select B.Account as Installer,A.Location from Account A join Account B on A.ManageGroup = B.ID where A.ID = :ID',
                        [':ID' => $val['ID']]
                    );
                    foreach ($comInfos as $comInfo) {
                        array_push($comList, $comInfo);
                        ++$total;
                    }
                }
                if ($needChange && $step == 1) {
                    $cLog->debug(
                        'update CommunityInfo;AccountID={AccountID};FeatureExpireTime={FeatureExpireTime}',
                        [':AccountID' => $val['ID'], ':FeatureExpireTime' => $newExpireTime]
                    );
                    $db->update2ListWKey(
                        'CommunityInfo',
                        [':AccountID' => $val['ID'], ':FeatureExpireTime' => $newExpireTime],
                        'AccountID'
                    );
                }
            }
        }
        if ($step == 0) {
            return [$comList, $total];
        }
    }

    /**
     * 获取云代金券详情.
     *
     * @param data: ['RemoteUserType'用户类型=>1:管理员,2:普通用户,'RemoteUser'=>云Account表的Account 或者 PersonalAccount的Email]
     */
    public function getCouponInfo($data)
    {
        $url = \share\util\bmGetUrl('getcoupontocloud', $data);
        $http = new HttpClient();
        $res = $http->get($url)->exec()->result();

        return $res;
    }

    /**
     * 刷新单住户收费计划.
     *
     * @param $id installer单住户ID
     *        $step 0:获取刷新列表 1:刷新计费模型
     *        $user null or array(userId)
     *
     * @return $updatinginfo: 用户列表
     *                        $total: 用户数目
     */
    public function checkPerPlan($id, $step = 1, $user = null)
    {
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        $perData = $dao->account->selectByID($id, 'Grade')[0];
        // 社区当ins的情况
        if (intval($perData['Grade']) === COMMUNITYGRADE) {
            $id = $dao->account->selectByArray([['Grade', PERSONGRADE], ['ManageGroup', $id]], 'ID')[0]['ID'];
        }
        return $this->checkPlan($id, 0, $step, $user);
    }

    /**
     * @description: 获取按天续费价格
     * @param $type
     * @param $projectId
     * @param $users
     * @param $payType
     * @param $nextTime
     * @return void
     * @throws \Exception
     * @lastEditors: zyc 2022/3/21 16:09 V6.4
     * @author:zyc 2022/3/21 16:09 V6.4
     */
    public function getComputedPriceToDay(
        $type,
        $projectId,
        $users,
        $payType,
        $nextTime
    ) {
        $data = ['NextTime' => $nextTime];
        list($data, $newUser) = $this->getInstanceUserData(
            $type,
            $projectId,
            $users,
            $payType,
            $data
        );
        $data = ['ChargeData' => json_encode(['Instance' => $data, 'User' => $newUser])];
        $res = $this->bmPostRequest('computedPrice', $data);
        return $res['Price'];
    }

    /**
     * @msg: 向计费系统请求创建Feature Plan订单
     * @param: $accountID: 购买人的ID
     *         $userType:支付人类型，物业:1,终端用户:0,2:install,3:区域管理员
     *         $payType:付款类型，4一次性收费，5月费，6补差价
     *         $count:数量
     */
    public function createFeatureOrder($accountID, $userType, $payType, $count, $feeUUID, $newFeeUUID = '')
    {
        $db = \share\util\getDatabase();
        if ($userType == 0) {
            $payerData = $db->querySList('select P.Account,U.Email from PersonalAccount P join PersonalAccountUserInfo U on P.UserInfoUUID=U.UUID where P.ID = :ID', [':ID' => $accountID])[0];
            $payAccount = $payerData['Account'];
            $payEmail = $payerData['Email'];
            $payEmail = ($payEmail == '' or is_null($payEmail)) ? $payEmail : \share\util\getAesForData()->decrypt($payEmail);
            // 普通用户
            $userType = 2;
        } else {
            $payerData = $db->querySList('select Account,Grade,ManageGroup from Account where ID = :ID', [':ID' => $accountID])[0];
            if ($payerData['Grade'] == COMMUNITYGRADE || $payerData['Grade'] == PERSONGRADE || $payerData['Grade'] == OFFICEGRADE) {
                $payerData = $db->querySList('select Account from Account where ID = :ID', [':ID' => $payerData['ManageGroup']])[0];
            }
            $payAccount = $payerData['Account'];
            $payEmail = '';
            // 管理员
            $userType = 1;
        }
        $data = ['PayParams' => json_encode([
            'Count' => $count, 'Type' => $payType, 'UserType' => $userType,
            'Payer' => $payAccount, 'PayerEmail' => $payEmail, 'FeeUUID' => $feeUUID, "NewFeeUUID" => $newFeeUUID
        ])];

        $result = $this->bmPostRequest('createfeaturePlanorder', $data);
        $token = \share\util\randString(54);
        $result['Token'] = $token;

        return $result;
    }

    /**
     * @description: 获取订阅用户的计费数据和金额
     * @param {array} $usersInfo common/order/util->getSubscribeUserInfo的处理结果
     * @param {int} IntervalType 周期类型 0:月 1:季 2:年 3:天
     * @param {int} Type 订阅类型 1: 单住户续费 2: 社区续费 3: 办公续费
     * @return array
     * @throws \Exception
     * @author: csc 2023/12/18 16:47 V6.7.1
     * @lastEditors: csc 2023/12/18 16:47 V6.7.1
     */
    public function getSubscriptionChargeDataAndPrice($usersInfo, $intervalType, $type)
    {
        switch ($type) {
            case SUBSCRIBE_TYPE['communityRenew']:
                $result = $this->computedUserChargeDataAndPrice('multiple', $usersInfo, PAY_SUBSCRIPTION, null);
                break;
            case SUBSCRIBE_TYPE['officeRenew']:
                $userIds = array_column($usersInfo['all'], 'ID');
                $officeIds = array_column($usersInfo['all'], 'ProjectId');
                $result = $this->computedUserChargeDataAndPrice('office', $userIds, PAY_SUBSCRIPTION, $officeIds[0]);
                break;
            case SUBSCRIBE_TYPE['singleRenew']:
                $result = $this->computedUserChargeDataAndPrice('single', $usersInfo, PAY_SUBSCRIPTION, null);
                break;
        }

        $userChargeData = $result['users'];
        $price = $result['price'];

        switch ($intervalType) {
            case 1: //按季
                $price = $price * 3;
                break;
            case 2: //按年
                $price = $price * 12;
                break;
            case 3: //按天
                $price = \share\util\outputComputedCount(\share\util\inputComputedCount($price / 30));
                break;
        }

        foreach ($userChargeData as $key => $value) {
            $userChargeData[$key]['SubscriptionPrice'] = $value['MonthlyFee'];
            if (intval($intervalType) === 1) {
                $userChargeData[$key]['SubscriptionPrice'] = $value['MonthlyFee'] * 3;
            } else if (intval($intervalType) === 2) {
                $userChargeData[$key]['SubscriptionPrice'] = $value['MonthlyFee'] * 12;
            } else if (intval($intervalType) === 3) {
                $userChargeData[$key]['SubscriptionPrice'] = \share\util\outputComputedCount(\share\util\inputComputedCount($value['MonthlyFee'] / 30));
            }
        }

        return [$userChargeData, $price];
    }

    /**
     * @description: 向计费系统创建订阅
     * @param {array} $payParams 参数描述
     * Array
    (
    [TotalPrice] => 40 //每期总金额
    [Type] => 2 //订阅类型 1: 单住户续费 2: 社区续费 3: 办公续费
    [Payer] => sisen-ins //付款人
    [PayerEmail] => 123@12.12csc //付款人邮箱
    [PayerType] => 2 //支付人类型；0=>终端用户,1=>物业,2=>install,3=>dis,4=>sub dis
    [IntervalType] => 0 //周期类型 0:月 1:季 2:年 3:天
    [Cycles] => 180 //周期数 0：不限
    [PayPlatUserID] => //stripe平台的userID(没有用户时为空)
    [StartTime] => 2023-12-16 09:00:00 //开始时间（东八区时间）
    )
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/12/21 11:39 V6.7.1
     * @lastEditors: csc 2023/12/21 11:39 V6.7.1
     */
    public function createSubscription($payParams)
    {
        $cLog = \share\util\getLog();
        $cLog->debug('params:payParams={payParams}', ['payParams' => $payParams]);

        $data = [
            'PayParams' => json_encode($payParams)
        ];
        $result = $this->bmPostRequest('createSubscription', $data)['SubscriptionData'];
        $token = \share\util\randString(54);
        $result['Token'] = $token;

        return $result;
    }

    /**
     * @description:取消订阅
     * @param $uuid string 订阅表的UUID
     * @param $reason int 停止订阅的原因 0-17
     * @param $reason string 项目名或用户名
     * @throws \Exception
     * @author: shoubin.chen 2023-12-28 14:03:31 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-28 14:03:31 v6.7.1
     */
    public function cancelSubscription($uuid, $reason, $name = null)
    {
        $cLog = \share\util\getLog();
        $cMessage = \share\util\getMessage();
        $cLog->debug('SubscriptionUUID={UUID};EndReason={Reason}', ['UUID' => $uuid, 'Reason' => $reason]);

        //在订阅中止原因数组内
        if (!in_array($reason, SUBSCRIPTION_END_REASON_ARRAY)) {
            throw new \Exception("The reason type={$reason} is not support.");
        }

        if ($name !== null && !is_string($name)) {
            throw new \Exception("The name={$name} is not string.");
        }

        $dao = new \framework\DaoCaller();
        $dao->autoload = true;

        $subscription = $dao->subscriptionList->selectByKey('UUID', $uuid, "UUID,Status,BmSubscriptionNumber")[0];
        if ($subscription === null) {
            throw new \Exception("The SubscriptionUUID={$uuid} is not exist.");
        }

        //调用计费系统
        $data = [
            'SubscriptionNumber' => $subscription['BmSubscriptionNumber']
        ];
        $originStatus = intval($subscription['Status']);
        //先判断是否已经取消
        if ($originStatus === SUBSCRIBE_STATUS['cancel']) {
            return true;
        }
        $result = $this->bmPostRequest('cancelSubscription', $data)['data'];

        if ($result['Status'] === 1) {
            //计费系统取消成功
            $dao->subscriptionList->update(['UUID' => $uuid, 'Status' => SUBSCRIBE_STATUS['cancel'], 'EndReason' => $reason], 'UUID');
            if ($originStatus === SUBSCRIBE_STATUS['active']) {
                $array = [$uuid, SUBSCRIBE_SEND_EMAIL_TYPE['cancel']];
                $name === null ? $array : array_push($array, $name);
                //调用应用后台发送取消成功的邮件
                $notifyEvent = \share\util\getNotifyEvent();
                $notifyEvent->collect(
                    $uuid . '_AutoPayNotify',
                    'AutoPayNotify',
                    $array
                );
            }
            return true;
        } else {
            //计费系统取消失败,记录日志
            $cLog->debug('Billing system cancel failed.SubscriptionUUID={UUID};BmSubscriptionNumber={BmSubscriptionNumber}', ['UUID' => $uuid, 'BmSubscriptionNumber' => $subscription['BmSubscriptionNumber']]);
            return false;
        }
    }


    /**
     * @description: 通过设备数量和存储时间匹配计费模型，得到视频存储的月费
     * @param {string} $deviceNum 设备数量,0=无限制，1=1，2=2，3=3
     * @param {string} $storageTime 存储时间
     * @param  {string} $pricingModels 计费模型
     * @author: shoubin.chen 2024/10/29 20:21:27 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/29 20:21:27  V7.1.0
     */
    public function getVideoStorageMonthlyFeeAndModel($deviceNum, $storageTime, $pricingModels)
    {
        $deviceNum = intval($deviceNum);
        $storageTime = intval($storageTime);
        //云 - 计费系统 映射关系
        $deviceNumMap = [
            0 => 4, //表示无限
            1 => 1,
            2 => 2,
            3 => 3
        ];
        $storageTimeMap = [
            30 => 1,//30天
            60 => 2,//60天
            90 => 3//90天
        ];
        $matchDeviceNum = $deviceNumMap[$deviceNum];
        $matchStorageTime = $storageTimeMap[$storageTime];

        //兜底方案
        $cLog = \share\util\getLog();
        if (empty($matchDeviceNum)) {
            throw new \Exception("DeviceNum err.Num:$deviceNum,Time:$storageTime");
        }
        if (empty($matchStorageTime)) {
            throw new \Exception("StorageDays err.Num:$deviceNum,Time:$storageTime");
        }

        // 进行匹配
        foreach ($pricingModels as $model) {
            if ($model['DeviceNum'] === $matchDeviceNum && $model['StorageTime'] === $matchStorageTime) {
                return [$model['MonthlyFee'], $model, $model['MonthlyFeeRoleConfig']];
            }
        }

        throw new \Exception("No matching billing model found.Num:$deviceNum,Time:$storageTime");
    }


    public function getVideoStorageSubscriptionChargeDataAndPrice($videoCommunityInfo, $intervalType, $type)
    {
        //请求计费系统得到价格
        if ($type === SUBSCRIBE_TYPE['officeRenew']) {
            throw new \Exception('Inactive type.');
        }
        $result = $this->computedVideoStorageChargeDataAndMonthPrice($videoCommunityInfo);

        $userChargeData = $result['sites'];
        $price = $result['price'];
        $price = $this->calculateDiffPrice($price, $intervalType);

        foreach ($userChargeData as $key => $value) {
            $subscriptionPrice = $value['MonthlyFee'];

            if (intval($intervalType) === 1) {
                $subscriptionPrice = $value['MonthlyFee'] * 3;
            } else if (intval($intervalType) === 2) {
                $subscriptionPrice = $value['MonthlyFee'] * 12;
            } else if (intval($intervalType) === 3) {
                $subscriptionPrice = \share\util\outputComputedCount(\share\util\inputComputedCount($value['MonthlyFee'] / 30));
            }

            $userChargeData[$key]['Model']['SubscriptionPrice'] = $subscriptionPrice;
            $userChargeData[$key]['SubscriptionPrice'] = $subscriptionPrice;
        }

        return [$userChargeData, $price];
    }
    public function getThirdLockSubscriptionChargeDataAndPrice($thirdLockInfo, $intervalType, $type)
    {
        //请求计费系统得到价格
        if ($type === SUBSCRIBE_TYPE['officeRenew']) {
            throw new \Exception('Inactive type.');
        }
        if ($type === SUBSCRIBE_TYPE['singleRenew']) {
            $type = PAY_TYPE_SINGLE;
            $payType = PAY_SINGLE_THIRDLOCK_RENEW;
        } else if ($type === SUBSCRIBE_TYPE['communityRenew']) {
            $type = PAY_TYPE_MULTIPLE;
            $payType = PAY_COMMUNITY_THIRDLOCK_RENEW;
        }


        $result = $this->computedThirdLockChargeDataAndPrice($type, $thirdLockInfo, $payType, [], true);

        $userChargeData = $result['thirdLockChargeData'];
        $price = $result['price'];
        $price = $this->calculateDiffPrice($price, $intervalType);

        foreach ($userChargeData as $key => $value) {
            $subscriptionPrice = $value['MonthlyFee'];

            if (intval($intervalType) === 1) {
                $subscriptionPrice = $value['MonthlyFee'] * 3;
            } else if (intval($intervalType) === 2) {
                $subscriptionPrice = $value['MonthlyFee'] * 12;
            } else if (intval($intervalType) === 3) {
                $subscriptionPrice = \share\util\outputComputedCount(\share\util\inputComputedCount($value['MonthlyFee'] / 30));
            }

            $userChargeData[$key]['Model']['SubscriptionPrice'] = $subscriptionPrice;
            $userChargeData[$key]['SubscriptionPrice'] = $subscriptionPrice;
        }

        return [$userChargeData, $price];
    }

    /**
     * @description: 统一的计算视频存储的方法，根据是否有下次时间进行区分
     * @param $type
     * @param $videoSiteInfo
     * @param $payType
     * @param $option
     * @return array
     * @author: shoubin.chen 2024/11/7 11:45:48 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/7 11:45:48  V7.1.0
     */
    public function computedVideoStorageChargeDataAndPrice($type, $videoSiteInfo, $payType, $option)
    {
        if (isset($option['NextTime'])) {
            return $this->computedVideoStorageChargeDataAndPriceToDay($type, $videoSiteInfo, $payType, $option);
        }

        return $this->computedVideoStorageChargeDataAndMonthPrice($videoSiteInfo, $option);
    }

    /**
     * @description: 计算视频存储的价格（月费）
     * @param {enum}    $type               项目类型，enum('multiple','single','office')
     * @param {array}   $videoSiteInfo      信息
     * @param {int}     $payType            类型
     * @author: shoubin.chen 2024/10/31 10:34:45 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/31 10:34:45  V7.1.0
     */
    public function computedVideoStorageChargeDataAndMonthPrice($videoSiteInfo, $option = [])
    {
        $cLog = \share\util\getLog();
        $cLog->payLog("#billsysUtil#computedVideoStorageChargeDataAndMonthPrice#videoSiteInfo=" . json_encode($videoSiteInfo) . "option=" . json_encode($option));
        $chargeDataItem = $videoSiteInfo['chargeData'];
        $configs = $videoSiteInfo['config'];

        $count = isset($option['Count']) ? intval($option['Count']) : 1;
        //请求计费系统得到计费模型
        $chargeModels = $this->queryVideoStorageChargePlan(['ChargeData' => json_encode($chargeDataItem)]);

        $videoChargeData = [];
        $fee = 0;
        $payerRole = \share\util\getPayerRole();
        $cLog->payLog("#billsysUtil#computedVideoStorageChargeDataAndMonthPrice#payerRole=" . $payerRole);
        $dao = new \framework\DaoCaller();
        $dao->autoload = true;
        foreach ($configs as $index => $val) {
            $deviceNum = $val['DevicesLimitNum'];
            $storageTime = $val['StorageDays'];

            list($itemFee, $model, $roleConfigItemFee) = $this->getVideoStorageMonthlyFeeAndModel($deviceNum, $storageTime, $chargeModels[$index]['VideoStorageData']);
            // 费用不为0，且有其他角色的配置的单独费用，以配置的单独费用替代
            if ($payerRole !== PAYER_ROLE_ALL && $itemFee != 0) {
                $roleConfigItemFee = array_column($roleConfigItemFee, null, 'Type');
                $replaceFee = true;
                if ($payerRole == PAYER_ROLE_PM) { //如果是PM支付，需要校验项目的PM FEE开关
                    $projectUUID = $chargeDataItem[$index]['Instance']['ProjectUUID'];
                    $isPmFee = $dao->communityInfo->selectByKey('AccountUUID', $projectUUID)[0]['IsPMFee'];
                    if ($isPmFee != '1') {
                        $replaceFee = false;
                    }
                }
                if ($replaceFee) {
                    $itemFee = isset($roleConfigItemFee[$payerRole]) ? $roleConfigItemFee[$payerRole]['Fee'] : $itemFee;
                    $model['MonthlyFee'] = $itemFee;
                }
            }
            // 总价
            $fee += \share\util\inputComputedCount($itemFee * $count);

            $videoChargeData[] = ['Model' => $model, 'MonthlyFee' => $itemFee, 'UUID' => $val['SiteUUID'], 'Type' => $val['Type'], 'ItemFee' => $itemFee];
        }
        $fee = \share\util\outputComputedCount($fee);
        return ['sites' => $videoChargeData, 'price' => $fee];
    }

    /**
     * @description: 计算续费到某天的视频存储的价格
     * @param {enum}    $type               项目类型，enum('multiple','single','office')
     * @param {array}   $videoSiteInfo      信息
     * @param {int}     $payType            支付类型
     * @param {array}     $option           支付类型,有NextTime才合法
     * @author: shoubin.chen 2024/10/31 10:34:45 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/31 10:34:45  V7.1.0
     */
    public function computedVideoStorageChargeDataAndPriceToDay($type, $videoSiteInfo, $payType, $option)
    {
        if (!isset($option['NextTime'])) {
            throw new \Exception("Don't support this type.");
        }

        $siteInfos = \share\util\arrayColumnAsKey($videoSiteInfo['site'], 'UUID');
        $chargeDataItem = $videoSiteInfo['chargeData'];
        $configs = $videoSiteInfo['config'];
        $nextTime = $option['NextTime'];

        $fee = 0;
        $videoChargeData = $this->computedVideoStorageChargeDataAndMonthPrice($videoSiteInfo)['sites'];
        foreach ($configs as $index => $val) {
            $siteUUID = $val['SiteUUID'];
            $monthFee = $videoChargeData[$index]['MonthlyFee'];
            list($itemFee, $days) = $this->computedPriceForDay(
                $monthFee,
                $siteInfos[$siteUUID]['VideoStorageExpireTime'],
                $nextTime,
                $siteInfos[$siteUUID]['TimeZone']
            );
            // 总价
            $fee += \share\util\inputComputedCount($itemFee);
            $videoChargeData[$index]['Days'] = $days;
            $videoChargeData[$index]['ItemFee'] = $itemFee;
        }
        $fee = \share\util\outputComputedCount($fee);
        return ['sites' => $videoChargeData, 'price' => $fee];
    }

    /**
     * @description: 统一的计算三方锁金额的方法，根据是否有下次时间进行区分
     * @param $type
     * @param $videoSiteInfo
     * @param $payType
     * @param $option
     * @param $isRenew
     * @return array
     * @throws \Exception
     * @author: csc 2025/2/6 16:32 V7.1.0
     * @lastEditors: csc 2025/2/6 16:32 V7.1.0
     */
    public function computedThirdLockChargeDataAndPrice($type, $thirdLockInfo, $payType, $option, $isRenew)
    {
        $chargeDataItem = $thirdLockInfo['chargeData'];
        $configs = $thirdLockInfo['config'];
        $siteInfos = \share\util\arrayColumnAsKey($thirdLockInfo['site'], 'UUID');
        $count = isset($option['Count']) ? intval($option['Count']) : 1;
        //请求计费系统得到计费模型
        $chargeModels = $this->getThirdLockCharge($chargeDataItem);

        $thirdLockChargeData = [];
        $fee = 0;

        foreach ($configs as $index => $val) {
            $brand = $val['Brand'];
            $activeFee = $monthlyFee = 0;
            $model = [];
            if (!empty($chargeModels[$index]['ThirdLock'][$brand])) {
                $monthlyFee = $chargeModels[$index]['ThirdLock'][$brand]['MonthlyFee'];
                $activeFee = $chargeModels[$index]['ThirdLock'][$brand]['ActiveFee'];
                $model = $chargeModels[$index]['ThirdLock'][$brand];
            }

            $tmpChargeData = ['Model' => $model, 'MonthlyFee' => $monthlyFee, 'UUID' => $val['LockUUID']];

            // 如果是激活, 计算的是激活的费用
            if (!$isRenew) {
                $tmpChargeData['ItemFee'] = $activeFee;
                $fee += \share\util\inputComputedCount($activeFee);
            } else if (isset($option['NextTime'])) {
                // 如果是按日续费，计算金额
                $nextTime = $option['NextTime'];
                $siteUUID = $val['AccountUUID'];
                list($itemFee, $days) = $this->computedPriceForDay(
                    $monthlyFee,
                    $val['ExpireTime'],
                    $nextTime,
                    $siteInfos[$siteUUID]['TimeZone']
                );
                // 总价
                $fee += \share\util\inputComputedCount($itemFee);
                $tmpChargeData['ItemFee'] = $itemFee;
                $tmpChargeData['Days'] = $days;
            } else {
                // 按月
                $tmpChargeData['ItemFee'] = $monthlyFee;
                $fee += \share\util\inputComputedCount($monthlyFee * $count);
            }

            $thirdLockChargeData[] = $tmpChargeData;
        }
        $fee = \share\util\outputComputedCount($fee);

        return ['thirdLockChargeData' => $thirdLockChargeData, 'price' => $fee];
    }

    /**
     * @description: 根据不同的周期计算总价格
     * @param {double} $price 月费
     * @param {int} $intervalType   间隔
     * @return {double} $price 不同间隔的实际费用
     * @author: shoubin.chen 2024/10/31 10:14:14 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/31 10:14:14  V7.1.0
     */
    private function calculateDiffPrice($price, $intervalType)
    {
        switch ($intervalType) {
            case 1: //按季
                $price = $price * 3;
                break;
            case 2: //按年
                $price = $price * 12;
                break;
            case 3: //按天
                $price = \share\util\outputComputedCount(\share\util\inputComputedCount($price / 30));
                break;
        }
        return $price;
    }
}
