<?php
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/socket_office.php');
require_once(dirname(__FILE__) . '/utility.php');
include_once(dirname(__FILE__) . '/AES128CBC.php');
require_once(dirname(__FILE__) . '/funcs_update_office_data_version.php');
require_once(dirname(__FILE__) . '/funcs_face_interface.php');

function OfficeWebCommunityModifyNotify($changeType, $node = "", $mac = "", $officeid, $department = 0)
{
    global $cLog;
    $cLog->TRACE("[OfficeWebCommunityModifyNotify]changeType=[" . $changeType . "] node=[" . $node . "] mac=[" . $mac . "] officeid=[" . $officeid . "] department=" . $department);

    switch ($changeType) {
        case model\notify\WEB_COMM_ADD_USER:
        case model\notify\WEB_COMM_MODIFY_USER:
            OfficeWebCommunityAccountModifyNotify($officeid, $node, 0);
            break;
        case model\notify\WEB_COMM_IMPORT_COMMUNITY:
        case model\notify\WEB_COMM_ALLOW_CREATE_PIN:
             updateAllOfficeAccountDataVersion($officeid);
            break;
        case model\notify\WEB_COMM_UNIT_MODIFY_DEV:
             updateOfficeDataVersionByUnitMac($department, $mac);
             break;
        case model\notify\WEB_COMM_PUB_MODIFY_DEV:
             updateOfficeDataVersionByPubMac($officeid, $mac);
             break;
        case model\notify\WEB_COMM_MODIFY_DEV:
             updateOfficeDataVersionByPerMac($node, $mac);
             break;
        case model\notify\WEB_COMM_UPDATE_PIN:
            OfficeWebCommunityAccountModifyNotify($officeid, $node, 0);
             break;
        default:
        break;
    }


    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $officeid;
    $data[4] = $department;

    $Socket = new CWebOfficeModifyNotify();
    $Socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE);
    $Socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $Socket->copy($data);
}


function OfficeWebAccessGroupModifyNotify($officeid, $AccessGroupID, $macs, $del_userlist)
{
    $macs = is_array($macs) ? $macs : [$macs];

    $macs_str = implode(",", $macs);
    global $cLog;
    $cLog->TRACE("[webAccessGroupModifyNotify]:officeid=$officeid, AccessGroupID=$AccessGroupID, MACs=$macs_str");

    $data[0] = $AccessGroupID;
    $data[1] = $macs;
    $data[2] = $officeid;
    $data[3] = $del_userlist;
    
    //更新关联用户的数据版本
    updateOfficeDataVersionByAccessGroupID($AccessGroupID);
    $socket = new CWebAccessGroupModifyNotifySocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_ACCESS_GROUP_MODIFY);
    $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $socket->copy($data);
}

//当删除时候需要传node，这时候直接更新node下的所有家庭设备user
function OfficeWebCommunityAccountModifyNotify($officeid, $Accounts, $AccessGroupIDs, $nodes="")
{
    $Accounts = is_array($Accounts) ? $Accounts : [$Accounts];
    $AccessGroupIDs = is_array($AccessGroupIDs) ? $AccessGroupIDs : [$AccessGroupIDs];
    $nodes = is_array($nodes) ? $nodes : [$nodes];
    $AccessGroupIDs = array_unique($AccessGroupIDs);

    $accounts_str = implode(",", $Accounts);
    $groupids_str = implode(",", $AccessGroupIDs);
    $nodes_str = implode(",", $nodes);
    global $cLog;
    $cLog->TRACE("[WebCommunityAccountModifyNotify]: officeid=$officeid,Account=$accounts_str groupid=$groupids_str node=$nodes_str");

    $data[0] = $AccessGroupIDs;
    $data[1] = $officeid;
    $data[2] = $Accounts;
    $data[3] = $nodes;

    updateOfficeAccountDataVersion($Accounts);

    $socket = new CWebCommunityAccountModifyNotifySocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY);
    $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $socket->copy($data);
}

//Accounts支持数组：主从账号
function OfficeWebCommunityImportAccountDatasNotify($officeid, $account_list)
{
    $account_list = is_array($account_list) ? $account_list : [$account_list];

    global $db;
    global $cLog;

    // 检查数据库连接是否可用
    if (!$db) {
        $cLog->ERROR("[OfficeWebCommunityImportAccountDatasNotify] Database connection is null");
        return;
    }

    $mac_list = array();
    $chunk_result = array_chunk($account_list, 200);
    foreach ($chunk_result as $accounts) {
        $accounts_str = implode(",", $accounts);
        $cLog->TRACE("[OfficeWebCommunityImportAccountDatasNotify]: officeid=$officeid,Account=$accounts_str");
        if (strlen($accounts_str) == 0) {
            continue;
        }

        $groupids = '';
        $units = '';

        $grouplist = $db->querySList("select G.ID,G.CommunityID,G.UnitID From AccountAccess A left join AccessGroup G on A.AccessGroupID = G.ID where A.Account in($accounts_str)", []);
        foreach ($grouplist as $group) {
            if (strlen($groupids) == 0) {
                $groupids = $group['ID'];
            } else {
                $groupids = $groupids . "," . $group['ID'];
            }
            
            if ($group['UnitID'] != 0) {
                if (strlen($units) == 0) {
                    $units = $group['UnitID'];
                } else {
                    $units = $units . "," . $group['UnitID'];
                }
            }
        }

        updateOfficeAccountDataVersion($accounts);

        if (strlen($groupids) == 0) {
            continue;
        }

        // 构建SQL查询，处理空的units情况
        $sql_parts = [];
        $sql_parts[] = "select MAC from AccessGroupDevice where AccessGroupID in($groupids)";

        // 只有当units不为空时才添加UnitID条件
        if (strlen($units) > 0) {
            $sql_parts[] = "select MAC From Devices where MngAccountID=:officeid and UnitID in($units) and Grade != 3";
        }

        $sql_parts[] = "select MAC From Devices where MngAccountID=:officeid and Grade = 1";
        $sql_parts[] = "select MAC From UserAccessGroup U left join UserAccessGroupDevice D on D.UserAccessGroupID = U.ID where U.Account in($accounts_str) and MAC is not Null";

        $final_sql = implode(" UNION ", $sql_parts);
        $devs = $db->querySList($final_sql, [":officeid" => $officeid]);
        foreach ($devs as $value) {
            array_push($mac_list, $value['MAC']);
        }
    }
    $mac_list = array_unique($mac_list);
    $chunk_result = array_chunk($mac_list, 100);
    foreach ($chunk_result as $macs) {
        $data[0] = $officeid;
        $data[1] = $macs;

        $socket = new CWebCommunityImportAccountDataNotifySocket();
        $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_COMMUNITY_IMPORT_ACCOUNT_DATAS);
        $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
        $socket->copy($data);
    }
}

function OfficewebPersonalModifyNotify($communitid, $StaffIDs, $DeliveryIDs, $AccessGroupIDs)
{
    $StaffIDs = is_array($StaffIDs) ? $StaffIDs : [$StaffIDs];
    $DeliveryIDs = is_array($DeliveryIDs) ? $DeliveryIDs : [$DeliveryIDs];
    $AccessGroupIDs = is_array($AccessGroupIDs) ? $AccessGroupIDs : [$AccessGroupIDs];
    $AccessGroupIDs = array_unique($AccessGroupIDs);

    $staff_str = implode(",", $StaffIDs);
    $deliver_str = implode(",", $DeliveryIDs);
    $groupids_str = implode(",", $AccessGroupIDs);
    global $cLog;
    $cLog->TRACE("[OfficewebPersonalModifyNotify]: office_id=$communitid,StaffIDs=$staff_str,DeliveryIDs=$deliver_str groupid=$groupids_str");

    //每1000个处理一次
    $staff_chunk_result = array_chunk($StaffIDs, 1000);
    if (count($staff_chunk_result) > 1) {
        foreach ($staff_chunk_result as $value) {
            updateStaffDeliverDataVersion($value, []);
        }
        //直接判定为批量导入和删除。这时候不需要$StaffIDs
        $data[0] = [];
        $data[1] = $DeliveryIDs;
        $data[2] = $communitid;
        $data[3] = $AccessGroupIDs;
    
        $socket = new CWebCommunityPerModifyNotifySocket();
        $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY);
        $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
        $socket->copy($data);
    } else {
        updateStaffDeliverDataVersion($StaffIDs, $DeliveryIDs);
        $data[0] = $StaffIDs;
        $data[1] = $DeliveryIDs;
        $data[2] = $communitid;
        $data[3] = $AccessGroupIDs;
    
        $socket = new CWebCommunityPerModifyNotifySocket();
        $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY);
        $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
        $socket->copy($data);
    }
}