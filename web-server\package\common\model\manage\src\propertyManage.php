<?php
/*
 * @description: 对物业管理员的相关操作
 * @version: V6.5
 * @author: cj
 * @Date: 2022-10-19 15:34:27
 * @LastEditors: cj
 * @lastEditTime 2023-09-12 09:39:54
 * @LastVersion: V6.5
 */

namespace package\common\model\manage\src;

use package\common\model\manage\config\Code;
use think\model\concern\RelationShip;

trait PropertyManage
{
    /**
     * @description:
     * @param EnableNoticePasswd 是否通知更改密码， -1:跳过，不修改时间(兼容installer app) 0：关 1开
     * @return array
     * @lastEditor: shoubin.chen 2023-09-25 15:26:30 v6.7
     */
    public function addPropertyManage()
    {
        $params = ['FirstName', 'LastName', 'Email', 'Language', PROXY_ROLE['projectId'], 'EnableNoticePasswd:enum("-1","0", "1")', 'TwoFactorAuth?:enum("0","1")', 'ProjectList:string'];
        list($firstName, $lastName, $email, $language, $projectId, $enableNoticePasswd, $enableTwoFactorAuth, $projectList) = $this->getParams($params);

        // 检测邮件重复
        $data = $this->dao->accountUserInfo->selectByKey('LoginAccount', $email);
        if (count($data) !== 0) {
            $this->output->echoErrorMsg(STATE_EMAIL_EXITS);
        }

        // 获取ins信息
        $insInfo = $this->utils->self->getInsInfo($projectId);

       list($id, $passwd) = $this->callSelfFunc('addPropertyManageInfo', [$firstName, $lastName, $email, $language,$enableNoticePasswd, $enableTwoFactorAuth, $insInfo['ID'], $insInfo['UUID'], $insInfo['TimeZone']]);

        // 分配PM管理的项目
        $projectArr = array_filter(array_map('trim', explode(';', $projectList)));
        if (!empty($projectArr)) {
            // 校验传进来的projectID都属于该Ins下
            $insProjectList = $this->dao->account->selectByArray([
                ['ManageGroup', $insInfo['ManageGroup']],
                ['Grade', [COMMUNITYGRADE, OFFICEGRADE]]
            ], 'ID');
            $insProjectIDs = array_column($insProjectList, 'ID');
            $invalidProjects = array_diff($projectArr, $insProjectIDs);
            if (!empty($invalidProjects)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
            }

            // 插入关联表
            foreach ($projectArr as $projectId) {
                $this->dao->propertyMngList->insert(['PropertyID' => $id, 'CommunityID' => $projectId]);
            }
        }

        return ['Password' => $passwd, 'ID' => $id];
    }

    public function addPropertyManageInfo()
    {
        $params = ['FirstName', 'LastName', 'Email', 'Language', 'EnableNoticePasswd:enum("-1","0", "1")', 'TwoFactorAuth?:enum("0","1")', 'InsID', 'InsUUID', 'TimeZone'];
        list($firstName, $lastName, $email, $language, $enableNoticePasswd, $enableTwoFactorAuth, $insId, $insUUID, $timeZone) = $this->getParams($params);

        $this->loadModel('projectData', true);
        $passwd = $this->share->util->generatePw(8);
        $now = $this->share->util->getNow();
        $account = $this->models->_common->projectData->getAutoAccount();

        $enableTwoFactorAuth = $enableTwoFactorAuth == null ? '0' : $enableTwoFactorAuth;
        $accountInfoUuid = explode('-', $this->share->util->uuid());
        $accountInfoUuid = $accountInfoUuid[0].'-1'.$accountInfoUuid[1];
        $accountUuid = $this->share->util->uuid();

        // 插入Account表
        $id = $this->dao->account->insert([
            'Account'=>$account,
            'Grade'=>PROPERTYMANAGE,
            'Language'=>$language,
            'ParentID'=>$insId,
            'ParentUUID'=>$insUUID,
            'ManageGroup'=>$insId,
            'TimeZone'=>$timeZone,
            'UUID'=>$accountUuid,
            'CreateTime'=>$now
        ]);

        // 插入AccountUserInfo表，isLink默认是1
        $noticePasswdTime = ($enableNoticePasswd === '0' || $enableNoticePasswd === '-1') ? null : $now;
        $this->dao->accountUserInfo->insert([
            'Email'=>$email,
            'Passwd'=>$this->share->util->getSaltPwd($passwd),
            'LoginAccount'=>$email,
            'IsLink'=>IS_PM_LINK,
            'UUID'=>$accountInfoUuid,
            'CreateTime'=>$now,
            'NoticePasswdTime' => $noticePasswdTime,
            'TwoFactorAuth' => $enableTwoFactorAuth
        ]);

        // 插入AccountMap表
        $this->dao->accountMap->insert([
            'AccountUUID'=>$accountUuid,
            'UserInfoUUID'=>$accountInfoUuid,
            'CreateTime'=>$now,
            'UUID'=>$this->share->util->uuid()
        ]);

        // 插入PropertyInfo
        $this->dao->propertyInfo->insert([
            'AccountID'=>$id,
            'FirstName'=>$firstName,
            'LastName'=>$lastName
        ]);

        // PropertyBillingInfo
        $this->loadModel('billingInfo', true);
        $this->models->_common->billingInfo->addPM($account);

        //插入RBACDataGroup表
        $dataGroup = $this->dao->rBACDataGroup->getPmDataGroup($accountUuid);
        $rbacDataGroupUUID = $this->share->util->uuid();
        $dataGroupData = [
            'UUID' => $rbacDataGroupUUID,
            'DataGroup' => $dataGroup
        ];
        $this->dao->rBACDataGroup->insert($dataGroupData);

        //添加AccountDataGroup表数据
        $accountDataGroupData = [
            'RBACDataGroupUUID' => $rbacDataGroupUUID,
            'UUID' => $this->share->util->uuid(),
            'AccountUUID' => $accountUuid,
        ];
        $this->dao->accountDataGroup->insert($accountDataGroupData);

        //添加RBACRoleMap表
        $rbacRoleUUID = $this->dao->rBACRole->getRBACRoleUUIDByGrade(PROPERTYMANAGE);
        $roleMapUUID = $this->share->util->uuid();
        $roleMapData = [
            'UUID' => $roleMapUUID,
            'RBACRoleUUID' => $rbacRoleUUID,
            'UserInfoUUID' => $accountUuid,
        ];
        $this->dao->rBACRoleMap->insert($roleMapData);

        // 审计日志
        $this->auditLog->setLog(AUDIT_CODE_ADD_P_M, $this->env, [$email], $account);

        // pm邮件
        $this->notifyEvent->collect(
            $account.'_pmCreateUser',
            'pmCreateUser',
            [$accountUuid, $passwd, $email]
        );

        return [$id, $passwd];
    }

    /**
     * @description: 编辑pm信息
     * @param ID 指pm的ID
     * @param EnableNoticePasswd 是否通知更改密码， -1:跳过，不修改时间(兼容installer app) 0：关 1开
     * @author: cj 2022/10/19 15:37 V6.5.2
     * @return {*}
     * @LastEditor: cj 2022/10/19 15:37 V6.5.2
     */
    public function editPropertyManage()
    {
        $params = [
            'ID',
            'FirstName',
            'LastName',
            PROXY_ROLE['installerId'],
            'EnableNoticePasswd:enum("-1", "0", "1")', 'TwoFactorAuth?:enum("0","1")', 'Email', 'ProjectList:string', 'Language'
        ];
        list(
            $id, $firstName, $lastName, $installerId, $enableNoticePasswd, $enableTwoFactorAuth, $email, $projectList, $language
            ) = $this->getParams($params);

        // V6.5.2增加编辑权限校验
        $this->loadUtil('common');
        $data = $this->utils->common->getAccountInfoWithID($id, ['LoginAccount', 'ManageGroup', 'UUID', 'Account', 'UserInfoUUID', 'NoticePasswdTime']);
        $oldNoticePasswdTime = $data['NoticePasswdTime'];
        $userInfo = $this->dao->accountUserInfo->selectByKey('UUID', $data['UserInfoUUID'])[0];
        $oldEmail = $userInfo['Email'];

        $languageArray = ['en', 'zh-tw', 'zh-cn', 'korean', 'ja', 'turkish', 'pl', 'ru', 'es', 'bs', 'da', 'vi', 'fr', 'pt', 'de', 'it', 'uk', 'he', 'fa', 'az'];
        if (!in_array($language, $languageArray)) {
            $this->output->echoErrorMsg(STATE_UPDATE_FAIL, ['externalErrorObj' => Code::EXT_STATE_ERROR_LANGUAGE]);
        }

        // 开关从关到开,关到开需要更新pm信息，其余不用
        if ($oldNoticePasswdTime === null && $enableNoticePasswd === '1') {
            // 关->开
            $this->dao->accountUserInfo->update(['UUID' => $data['UserInfoUUID'], 'NoticePasswdTime' => $this->share->util->getNow()], 'UUID');
        } elseif ($oldNoticePasswdTime !== null && $enableNoticePasswd === '0') {
            // 开->关
            $this->dao->accountUserInfo->update(['UUID' => $data['UserInfoUUID'], 'NoticePasswdTime' => null], 'UUID');
        }
        // 双重认证
        if ($enableTwoFactorAuth !== null) {
            $this->dao->accountUserInfo->update(['UUID' => $data['UserInfoUUID'], 'TwoFactorAuth' => $enableTwoFactorAuth], 'UUID');
        }
        if (!empty($email) && $email !== $oldEmail) {
            // 检测邮件重复
            $pmInfoData = $this->dao->accountUserInfo->selectByKey('LoginAccount', $email);
            if (count($pmInfoData) !== 0) {
                $this->output->echoErrorMsg(STATE_EMAIL_EXITS);
            }

            $this->dao->accountUserInfo->update(['UUID' => $data['UserInfoUUID'], 'LoginAccount' => $email, 'Email' => $email], 'UUID');
            // 删除Token
            $this->utils->self->delManageToken($data['LoginAccount']);
        }

        $manageGroup1 = $data['ManageGroup'];
        $manageGroup2 = $this->utils->common->getAccountInfoWithID($installerId, ['ManageGroup'])['ManageGroup'];
        if ($manageGroup1 !== $manageGroup2) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PM_NOT_EXIST]);
        }

        $this->callSelfFunc('updatePropertyBasicInfo', [$id, $data['UUID'], $firstName, $lastName, $language]);

        $oldAssignInfos = $this->dao->propertyMngList->selectByKey('PropertyID', $id);
        $oldAssignCommunityIdS = array_column($oldAssignInfos, 'CommunityID', null);

        $projectArr = array_filter(array_map('trim', explode(';', $projectList)));
        if (!empty($projectArr)) {
            // 校验传进来的projectID都属于该Ins下
            $insProjectList = $this->dao->account->selectByArray([
                ['ManageGroup', $installerId],
                ['Grade', [COMMUNITYGRADE, OFFICEGRADE]]
            ], 'ID');
            $insProjectIDs = array_column($insProjectList, 'ID');
            $invalidProjects = array_diff($projectArr, $insProjectIDs);
            if (!empty($invalidProjects)) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
            }
        }

        $delProjectIds = array_diff($oldAssignCommunityIdS, $projectArr); //移除的项目 id
        $addProjectIds = array_diff($projectArr, $oldAssignCommunityIdS); //新加入的项目 id

        if (!empty($delProjectIds)) {
            // 判断当前pm是否开启了pm app，如果开启，需要注销 pm app帐号相关
            $this->loadUtil('account', true);
            $this->loadUtil('order', true);
            $this->loadModel('account', true);

            $delPmIds = [$id];
            $data = $this->utils->_common->account->getManagerListByArray([['ID', $delPmIds]], [], 'A.ID,A.UUID,AUF.UUID as AUF_UUID');
            $accountUuids = array_column($data, 'UUID', null);
            $payerUUIDs = array_column($this->dao->accountMap->selectByKeyWArray('UserInfoUUID', array_column($data, 'AUF_UUID'), 'AccountUUID'), 'AccountUUID');

            // PM自动续费订单里的社区被移除，需取消订阅,多套房PM可能有影响
            foreach ($delProjectIds as $projectId) {
                $communityData = $this->dao->account->selectByID($projectId)[0];
                $subscriptionUUIDs = $this->utils->_common->order->getSubscriptionList([['PayerUUID', $payerUUIDs], ['PayerType', 1], ['IsDelete', 0],
                    ['Status', [0, 1]], ['ProjectUUID', $communityData['UUID']]], 'UUID');
                $this->loadProvider('billsysUtil');
                foreach ($subscriptionUUIDs as $subscriptionUUID) {
                    // 创建订阅的pm被移除项目的管理(类型9)，取消订阅
                    $this->services->billsysUtil->cancelSubscription($subscriptionUUID['UUID'], SUBSCRIPTION_END_REASON_ARRAY[9]);
                }

                $pmMaps = $this->utils->common->getTableInfoPlus(
                    PROXY_TABLES['pmAccountMap'],
                    ['ID', 'PersonalAccountUUID'],
                    ['AccountUUID' => $accountUuids, 'ProjectUUID' => $communityData['UUID']]
                );

                if (!empty($pmMaps)) {
                    $perAccountUUIDs = implode(';', array_column($pmMaps, 'PersonalAccountUUID'));
                    $this->models->common->account->delPMAppAccount($perAccountUUIDs);
                }

                //删除关联关系
                foreach ($oldAssignInfos as $oldAssignInfo) {
                    if ($oldAssignInfo['CommunityID'] == $projectId) {
                        $this->dao->propertyMngList->delete($oldAssignInfo['ID'], 'ID');
                    }
                }
            }
        }

        if (!empty($addProjectIds)) {
            // 插入新数据
            foreach ($addProjectIds as $projectId) {
                $this->dao->propertyMngList->insert(['PropertyID' => $id, 'CommunityID' => $projectId]);
            }
        }
        
        $this->auditLog->setLog(AUDIT_CODE_EDIT_P_M, $this->env, [$data['Account']], $data['Account']);
    }

    public function updatePropertyBasicInfo()
    {
        $params = ['PmID', 'PmUUID', 'FirstName', 'LastName', 'Language'];
        list($pmId, $pmUUID, $firstName, $lastName, $language) = $this->getParams($params);

        // 修改Language
        if (!empty($language)) {
            $this->dao->account->update(['Language' => $language, 'ID' => $pmId]);
        }
        // 修改 firstName 或 lastName
        $nameArray = [];
        if (!empty($firstName)) {
            $nameArray['FirstName'] = $firstName;
        }
        if (!empty($lastName)) {
            $nameArray['LastName'] = $lastName;
        }

        if (!empty($nameArray)) {
            $nameArray['AccountID'] = $pmId;
            $this->dao->propertyInfo->update($nameArray, 'AccountID');
        }

        $pmAccountMapTable = PROXY_TABLES['pmAccountMap'];
        //修改pm名字同步修改pm app中的name
        $pmMap = $this->db->querySList("select PersonalAccount from $pmAccountMapTable where AccountUUID = :AccountUUID", [':AccountUUID' => $pmUUID]);
        if (!empty($pmMap)) {
            $name = $firstName .' '. $lastName;
            $personalAccounts = array_column($pmMap, 'PersonalAccount');
            foreach ($personalAccounts as $personalAccount) {
                $this->dao->personalAccount->update(['Name' => $name, 'FirstName' => $firstName, 'LastName' => $lastName, 'Account' => $personalAccount], 'Account');
            }
        }
    }
}
