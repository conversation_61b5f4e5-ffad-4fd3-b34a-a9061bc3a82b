<?php
namespace model;
include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

class CCall {
    function queryForPM () {
        $params = [
            "StartTime"=>"",
            "EndTime"=>"",
            "Key"=>"",
            "userAliasId"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $key = $params["Key"];
        $startTime = $params["StartTime"];
        $endTime = $params["EndTime"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $bindArray = [":MngAccountID"=>$userId,":Key"=>"%$key%"];
			
        $where = "";
        if($startTime) {
            $bindArray[":StartTime"] = \util\time\setTimeZone($startTime,$timeZone,"","-");
            $where  .= " and StartTime >= :StartTime";
        }
        if($endTime)  {
            $bindArray[":EndTime"] =  \util\time\setTimeZone($endTime,$timeZone,"","-");
            $where  .= " and StartTime <= :EndTime";
        }

        $data = [];

        $sql = "select ID from %s where MngAccountID = :MngAccountID and (CallerName like :Key or CalleeName like :Key) $where";
        $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql,$bindArray);
        $sql = "select * from %s where MngAccountID = :MngAccountID and (CallerName like :Key or CalleeName like :Key) $where  order by StartTime desc";
        list($count,$simtArray) = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables,$sql,$bindArray,$offset,$rows);
        $data["total"] = $count;
        $data["row"] = [];
        $simtArray = \util\time\setQueryTimeZone($simtArray,$timeZone,$customizeForm);
        foreach($simtArray as $key=>$value)  {
            $curl = [];
            $curl['ID'] =  $value['ID'];
            $curl['CallerName'] = $value["CallerName"];
            $curl['CalleeName'] = $value["CalleeName"];
            $curl['Status'] = $value['Status'];
            $curl['Time'] = $value['StartTime'];
            $curl['AnswerStatus'] = $value['IsAnswer'];
            $curl['IsAnswer'] = $value['IsAnswer'] ? $value["Duration"] :  MSGTEXT["noAnswer"];
            array_push($data["row"],$curl);
        }
        $data["detail"] = $data["row"];
        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @description 将网页版call history记录整理成app的call history格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatPmAppCall()
    {
        $params = [
            "data" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $params['data'];

        $formatData = [];
        if (!empty($data)) {
            $formatData = $data['row'];
            foreach ($formatData as &$val) {
                $val['Time'] = \util\time\setYesterday(\util\time\setTime(\util\time\setTimeZone($val['Time'], $timeZone, $customizeForm), $customizeForm), $timeZone);
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $formatData]);
    }
}