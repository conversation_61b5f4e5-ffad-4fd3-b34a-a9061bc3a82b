<?php

namespace package\openApi\model\user\src;

trait Check
{
    /**
     * @description:oauth<PERSON>ogin
     * @param {string} user Akuvox云平台的用户账号
     * @param {string} pwd Akuvox云平台的用户密码
     * @param {string} response_type 写死单词passwd,表示通过密码式直接授权
     * @param {string} client_id 第三方应用申请接入时获得的client_id
     * @author: csc 2023/5/16 11:09 V6.6
     * @lastEditors: csc 2023/5/16 11:09 V6.6
     */
    public function oauthLogin()
    {
        $params = ['client_id' ,'user', 'pwd', 'response_type'];
        list($clientId, $user, $pwd, $responseType) = $this->getParams($params);
        if ($responseType === null or $responseType === '') {
            $responseType = '';
        }
        $this->log->debug("oauth login begin, user:{user}", ["user"=>$user]);
        //oauthLogin登录客户端可信度校验
        $trust = $this->utils->self->oauthCheckClient($clientId, '', CHECK_CLIENT_TRUST_LEVEL);

        //判断可信度
        if ($trust === -2) {
            //报错：result 1002, message:client_secret error
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }

        if ($trust === -1) {
            //报错：result 1001, message:client_id does not exist
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }

        if($trust >= COMMON_TRUST_LEVEL) {
            $userInfos = $this->dao->personalAccount->getUserInfoByAccount($user);

            if (empty($userInfos)) {
                //报错：result 1009, message:Account does not exist
                $this->output->echoErrorMsg(STATE_OPENAPI_NOT_ACCOUNT_ERROR);
            }
            $userInfo = $userInfos[0];

            if (0 !== strcmp($userInfo['Passwd'], $this->share->util->getSaltPwd($pwd))) {
                //安全考虑，统一返回1009，密码检测放在第一步
                $this->output->echoErrorMsg(STATE_OPENAPI_NOT_ACCOUNT_ERROR);
            }

            //如果是一人多套房用户，尝试取已激活、未过期的用户
            if (count($userInfos) > 1) {
                foreach ($userInfos as $info) {
                    if (intval($info['Active']) === 1 && strtotime($info['ExpireTime']) > time()) {
                        $userInfo = $info;
                        break;
                    }
                }
            }

            if (intval($userInfo['Active']) === 0) {
                //报错：result 1012, message:Your account is not activated
                $this->output->echoErrorMsg(STATE_OPENAPI_ACCOUNT_NOT_ACTIVE_ERROR);
            }

            //增加拦截帐号过期
            if (strtotime($userInfo['ExpireTime']) < time()) {
                $this->output->echoErrorMsg(STATE_OPENAPI_ACCOUNT_EXPIRED_ERROR);
            }

            // if (0 !== strcmp($userInfo['Passwd'], $this->share->util->getSaltPwd($pwd))) {
            //     //报错：result 1010, message:Password error
            //     $this->output->echoErrorMsg(STATE_OPENAPI_PASSWORD_ERROR);
            // }

            //创建OAuthToken
            list($token, $refresh_token, $code, $accessValid, $refreshValid) = $this->utils->self->createOAuthToken($userInfo['Account'], $userInfo['AppMainUserAccount']);
            //判断是否hager用户
            $isHager = $this->utils->self->checkEndUserIsHager($userInfo['UUID']);

            if($trust >= PASSWD_TRUST_LEVEL && $responseType == 'passwd') {
                //返回数据token、refresh_token
                return ['datas' => [
                    'access_token' => $token,
                    'refresh_token' => $refresh_token,
                    "access_valid" => $accessValid,
                    "refresh_valid" => $refreshValid,
                ]];
            } else {
                $data = [
                    'code' => $code
                ];
                if ($isHager) {
                    $user = $this->dao->personalAccount->selectByKey('UUID', $userInfo['UUID'])[0];
                    $firstLogin = $user['appLoginStatus'] == 0 ? 1 : 0;
                    $data['hager_first_login'] = $firstLogin;
                    $data['access_token'] = $token;
                    $data['role'] = $user['Role'];
                }


                //返回数据code
                return ['datas' => $data];
            }
        }
    }

    /**
     * @description:oauthScanLogin
     * @param {string} qrcode 加密后的二维码内容
     * @param {string} client_id 第三方应用申请接入时获得的client_id
     * @return void
     * @author: csc 2023/5/16 11:09 V6.6
     * @lastEditors: csc 2023/5/16 11:09 V6.6
     */
    public function oauthScanLogin()
    {
        $params = ['qrcode', 'client_id'];
        list($qrcode, $clientId) = $this->getParams($params);

        $this->loadProvider('aesForOpenAPI');
        $text = $this->services->aesForOpenAPI->decrypt($qrcode);
        $arr = json_decode($text, true);

        $this->log->debug("oauth scanLogin begin, user:{user}", ["user"=>$arr['account']]);

        return $this->callSelfFunc('oauthLogin', [$clientId, $arr['account'], $arr['pwd'], ""]);
    }

    /**
     * @description: 检查密码是否正确
     * @return void
     * @author: csc 2023/5/16 20:15 V6.6
     * @lastEditors: csc 2023/5/16 20:15 V6.6
     */
    public function checkPwd()
    {
        $params = [PROXY_ROLE['mainUserId'], PROXY_ROLE['subUserId'], 'Passwd'];
        list($mainUserId, $subUserId, $passWd) = $this->getParams($params);
        $userId = empty($subUserId) ? $mainUserId : $subUserId;

        $search = [['ID', $userId], ['Passwd', $this->share->util->getSaltPwd($passWd)]];
        $this->loadUtil('account', true);
        $count = $this->utils->_common->account->getUserListByArray($search, 0, [], 'count(*)')[0]['count(*)'];

        if (empty($count)) {
            $this->output->echoErrorMsg(STATE_OPENAPI_PASSWORD_ERROR);
        }
    }

    /**
     * @description: 发送登录验证码
     * @author: Assistant
     */
    public function sendLoginCode()
    {
        $params = ['MobileNumber', 'ClientId'];
        list($mobileNumber, $clientId) = $this->getParams($params);
        
        // 错误IP拦截
        $limitKey = "openapi_login_send_mobile_code_limit";
        $limitMax = 5;
        $ip = $this->share->util->getIp();
        $limit = $this->share->util->getLimitIp($limitKey, $ip, 0, $limitMax);
        if ($limit !== false && $ip !== false) {
            $this->output->echoErrorMsg(STATE_LIMIT_IP2);
        }

        // 避免疯狂发送验证码
        $limitKeySuccess = "openapi_login_send_mobile_code_limit_success_".$mobileNumber;
        $ip = $this->share->util->getIp();
        $limitSuccessMax = 1;
        $limit = $this->share->util->getLimitIp($limitKeySuccess, $ip, 60, $limitSuccessMax);
        if ($limit !== false && $ip !== false) {
            $this->output->echoErrorMsg(STATE_SENT_CODE_LATER);
        }

        // 校验ClientId可信度
        $trust = $this->utils->self->oauthCheckClient($clientId, '', CHECK_CLIENT_TRUST_LEVEL);
                
        // 判断可信度
        if ($trust === -2) {
            $this->share->util->recordAccountIp($limitKey, $ip,0,$limitMax);
            // 报错：result 1001, message:client_id does not exist
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }
        
        if ($trust === -1) {
            $this->share->util->recordAccountIp($limitKey, $ip,0,$limitMax);
            // 报错：result 1001, message:client_id does not exist
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }

        // 检查手机号是否在PersonalAccountUserInfo表中存在
        $userInfo = $this->dao->personalAccountUserInfo->selectByMobileNumber($mobileNumber);
        if (empty($userInfo)) {
            $this->share->util->recordAccountIp($limitKey, $ip,0,$limitMax);
            $this->output->echoErrorMsg(STATE_OPENAPI_NOT_ACCOUNT_ERROR);
        }

        $phoneCode = $this->dao->personalAccount->selectByKey('UserInfoUUID',$userInfo[0]['UUID'])[0]['PhoneCode'];
        
        // 生成6位随机验证码
        $code = sprintf('%06d', mt_rand(0, 999999));
        
        // 插入或更新验证码记录
        $data = [
            'MobileNumber' => $mobileNumber,
            'Code' => $code,
            'Type' => 4, // openapi登录验证码
            'CreateTime' => date('Y-m-d H:i:s')
        ];
        
        $info = $this->dao->mobileCodeVerification->selectByArray([['MobileNumber', $mobileNumber], ['Type', 4]]);
        if (count($info) != 0) {
            $this->dao->mobileCodeVerification->delete($info[0]['ID']);
        }

        // 使用REPLACE INTO语句，如果存在相同的MobileNumber和Type则更新，否则插入
        $this->dao->mobileCodeVerification->insert($data);
        $this->share->util->recordAccountIp($limitKeySuccess, $ip, 60,$limitSuccessMax);
        $this->notifyEvent->collect(
            $mobileNumber.'_sendVerificationCode',
            'sendVerificationCode',
            [0, $code, $phoneCode, $mobileNumber]
        );
    }

    public function loginWithMobileCode()
    {
        $params = ['MobileNumber', 'Code', 'ClientId'];
        list($mobileNumber, $code, $clientId) = $this->getParams($params);
        
        $limitKey = "openapi_login_mobile_code_limit";
        $ip = $this->share->util->getIp();
        $limitMax = 5;
        $limit = $this->share->util->getLimitIp($limitKey, $ip, 0, $limitMax);
        if ($limit !== false && $ip !== false) {
            $this->output->echoErrorMsg(STATE_LIMIT_IP2);
        }

        // 校验ClientId可信度
        $trust = $this->utils->self->oauthCheckClient($clientId, '', CHECK_CLIENT_TRUST_LEVEL);
                
        // 判断可信度
        if ($trust === -2) {
            $this->share->util->recordAccountIp($limitKey, $ip, 0, $limitMax);
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }
        
        if ($trust === -1) {
            $this->share->util->recordAccountIp($limitKey, $ip,0, $limitMax);
            $this->output->echoErrorMsg(STATE_OPENAPI_CLIENT_ID_ERROR);
        }

        // 检查手机号是否在PersonalAccountUserInfo表中存在
        $userInfo = $this->dao->personalAccountUserInfo->selectByMobileNumber($mobileNumber);
        if (empty($userInfo)) {
            $this->share->util->recordAccountIp($limitKey, $ip,0, $limitMax);
            $this->output->echoErrorMsg(STATE_INVAILD_VER_CODE);
        }
        
        // 验证验证码
        $codeInfo = $this->dao->mobileCodeVerification->selectByArray([['MobileNumber', $mobileNumber], ['Type', 4]]);
        if (empty($codeInfo)) {
            $this->share->util->recordAccountIp($limitKey, $ip,0, $limitMax);
            $this->output->echoErrorMsg(STATE_INVAILD_VER_CODE);
        }
        
        $codeRecord = $codeInfo[0];
        
        // 检查验证码是否正确
        if ($codeRecord['Code'] !== $code) {
            $this->share->util->recordAccountIp($limitKey, $ip,0, $limitMax);
            $this->output->echoErrorMsg(STATE_INVAILD_VER_CODE);
        }
        
        // 检查验证码是否过期（5分钟有效期）
        $createTime = strtotime($codeRecord['CreateTime']);
        $currentTime = time();
        if (($currentTime - $createTime) > 300) { // 5分钟 = 300秒
            $this->share->util->recordAccountIp($limitKey, $ip,0, $limitMax);
            $this->output->echoErrorMsg(STATE_INVAILD_VER_CODE);
        }
        
        // 验证成功，删除验证码记录
        $this->dao->mobileCodeVerification->delete($codeRecord['ID']);

        $userInfos = $this->dao->personalAccount->selectByKey('UserInfoUUID',$userInfo[0]['UUID']);
        $userInfo = $userInfos[0];
        //如果是一人多套房用户，尝试取已激活、未过期的用户
        if (count($userInfos) > 1) {
            foreach ($userInfos as $info) {
                if (intval($info['Active']) === 1 && strtotime($info['ExpireTime']) > time()) {
                    $userInfo = $info;
                    break;
                }
            }
        }

        if (intval($userInfo['Active']) === 0) {
            //报错：result 1012, message:Your account is not activated
            $this->output->echoErrorMsg(STATE_OPENAPI_ACCOUNT_NOT_ACTIVE_ERROR);
        }

        //增加拦截帐号过期
        if (strtotime($userInfo['ExpireTime']) < time()) {
            $this->output->echoErrorMsg(STATE_OPENAPI_ACCOUNT_EXPIRED_ERROR);
        }
        
        // 生成token
        list($token, $refresh_token, $code, $accessValid, $refreshValid) = $this->utils->self->createOAuthToken($userInfo['Account'], $userInfo['AppMainUserAccount']);
        
        // 返回token信息
        return ['datas' => [
            'access_token' => $token,
            'refresh_token' => $refresh_token,
            "access_valid" => $accessValid,
            "refresh_valid" => $refreshValid,
        ]];
    }
}