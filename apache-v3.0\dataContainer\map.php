<?php
/*
 * @Description: 将用户的userAliasId更改为id的数据容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-09 11:54:39
 * @LastEditors  : kxl
 */
namespace dataContainer;
include_once __DIR__."/../interfaces/container/main.php";
use interfaces\container\main\dataContainer;
class CMap implements dataContainer {
    private $data;
    public $maps = [];
    public function bind ($key,$value) {
        // $this->data[$key] = $value;
    }
    public function get ($key) {
        global $gApp,$cLog;
        $cLog->actionLog("#dataContainer#map#want key = $key");
        if(array_key_exists($key,$this->maps)) $key = $this->maps[$key];
        $cLog->actionLog("#dataContainer#map#result key = $key");
        return $gApp["plan"]["data"][$key];
    }
}