<?php
/*
 * @Description: 根据主账户设置代理为管理员
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
use \interfaces\middleware\main\IMiddleware;
class CSetMainToPCMngAlias implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        $userAliasId = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setMainToPCMngAlias#");
        $data = $db->querySList("select A.ID,A.Account from Account A left join PersonalAccount B on A.ID = B.ParentID where B.ID = :ID",[":ID"=>$userAliasId])[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}