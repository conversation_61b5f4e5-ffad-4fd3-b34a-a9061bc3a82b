<?php

namespace dao;
use framework\BasicDao;

class PersonalAccount extends BasicDao
{
    //当前表名
    public $table = 'PersonalAccount';

    //需要数据混淆的字段
    public $confusionField = ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone'];

    //主键
    protected $primaryKey = 'ID';

    
    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["Grade", 11], ["ManageGroup", 0, "!="]]
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/07 11:21 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 11:21 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2023/03/09 10:20 V6.5.4
     * @lastEditors: systemCreator 2023/03/09 10:20 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据Account的值查询对应数据
     * @param {string} $account Account的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByAccount($account, $fields = '*')
    {
        return $this->selectByKey('Account', $account, $fields);
    }

    /**
     * @description: 根据Account的值查询对应数据
     * @param {array} $accounts Account的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByAccountWArray($accounts, $fields = '*')
    {
        return $this->selectByKeyWArray('Account', $accounts, $fields);
    }

    /**
     * @description: 根据Email的值查询对应数据
     * @param {string} $email Email的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByEmail($email, $fields = '*')
    {
        return $this->selectByKey('Email', $email, $fields);
    }

    /**
     * @description: 根据Email的值查询对应数据
     * @param {array} $emails Email的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByEmailWArray($emails, $fields = '*')
    {
        return $this->selectByKeyWArray('Email', $emails, $fields);
    }

    /**
     * @description: 根据MobileNumber的值查询对应数据
     * @param {string} $mobilenumber MobileNumber的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByMobileNumber($mobilenumber, $fields = '*')
    {
        return $this->selectByKey('MobileNumber', $mobilenumber, $fields);
    }

    /**
     * @description: 根据MobileNumber的值查询对应数据
     * @param {array} $mobilenumbers MobileNumber的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByMobileNumberWArray($mobilenumbers, $fields = '*')
    {
        return $this->selectByKeyWArray('MobileNumber', $mobilenumbers, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/07 10:53 V6.5.4
     * @lastEditors: systemCreator 2023/03/07 10:53 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    /**
     * @description: 获取待激活单住户用户列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {int} $offset 偏移量
     * @param {int} $row 行数
     * @return [$userData, $total] 用户列表和数目
     * @author: shoubin.chen 2024/11/21 14:59:59 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/21 14:59:59  V7.1.0
     */
    public function getActiveSingleUserList($bindArray, $offset, $row)
    {
        $serviceType = $bindArray[':ServiceType'];
        unset($bindArray[':ServiceType']);
        $where = '';
        // 已激活用户
        // V6.2需求 PhoneStatus = 1才被查询,V6.5.2修改：PhoneStatus改为高级功能，即Switch第二位
        $mainSql = "select PA.ID,PA.Role,PA.Name,PU.Email,PA.Account, '1' as ServiceType, '' as Brand,PA.UUID from PersonalAccount PA left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID
        where PA.ParentUUID = :ParentUUID and PA.Role = 10 and PA.Active = :Active $where";
        $adtAppSql = "select PA.ID,PA.Role,PA.Name,PU.Email,PA.Account, '2' as ServiceType, '' as Brand,PA.UUID from PersonalAccount PA join PersonalAccount C on PA.ParentUUID = C.UUID 
        left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID
        where C.ParentUUID = :ParentUUID and C.Role = 10 and C.Active = 1 and PA.Role = 11 and PA.Active = :Active $where";
        // V7.1.0增加三方锁激活
        $thirdLockSql = "select T.ID,PA.Role,'' as Name,PU.Email, PA.Account, '3' as ServiceType, T.Brand,T.LockUUID as UUID from ThirdLockRelateInfo T 
        left join PersonalAccount PA on PA.UUID = T.PersonalAccountUUID left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID 
        where T.LockUUID is not null AND PA.ParentUUID = :ParentUUID and PA.Role = 10 and T.ProjectType = 1 AND T.Brand in (3,6) and T.Active = :Active $where";
        $sqlLimit = "limit $offset, $row";

        $sql = "$mainSql union all $adtAppSql union all $thirdLockSql";
        if ($serviceType == '1') {
            $sql = $mainSql;
        } else if ($serviceType == '2') {
            $sql = $adtAppSql;
        } else if ($serviceType == '3') {
            $sql = $thirdLockSql;
        }

        $userData = $this->execute("$sql $sqlLimit", $bindArray);
        $total = count($this->execute($sql, $bindArray));
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email']);
        }

        return [$userData, $total];
    }

    /*
    * @description: 获得单住户续费列表
    * @author: shoubin.chen 2024/11/21 15:30:04 V7.1.0
    * @lastEditor: shoubin.chen 2024/11/21 15:30:04  V7.1.0
    */
    public function getRenewSingleList($bindArray, $offset, $row)
    {
        $active = $bindArray[':Active'];
        $serviceType = $bindArray[':ServiceType'];
        $beginTime = $bindArray[':BeginTime'];
        $endTime = $bindArray[':EndTime'];
        $timeZone = $bindArray[':TimeZone'];
        unset($bindArray[':BeginTime'], $bindArray[':EndTime'], $bindArray[':TimeZone'], $bindArray[':ServiceType']);

        $beginWhere = $endWhere = '';
        $videoBeginWhere = $videoEndWhere = '';
        $thirdLockBeginWhere = $thirdLockEndWhere = '';
        if (!empty($beginTime)) {
            $beginWhere = 'and  PA.PhoneExpireTime >= :BeginTime';
            $videoBeginWhere = 'and  PA.ExpireTime >= :BeginTime';
            $thirdLockBeginWhere = 'and T.ExpireTime >= :BeginTime';
            $bindArray[':BeginTime'] = $this->share->util->setTimeZone($beginTime, $timeZone, '', '-');
        }

        $bindArray[':EndTime'] = DEFAULTEXPIRETIME;
        if (!empty($endTime)) {
            $endTime = $this->share->util->setTimeZone($endTime, $timeZone, '', '-');
            $endWhere = 'and  PA.PhoneExpireTime <= :EndTime';
            $videoEndWhere = 'and  PA.ExpireTime <= :EndTime';
            $thirdLockEndWhere = 'and  T.ExpireTime <= :EndTime';
            $bindArray[':EndTime'] = $endTime;
        }
        $timeWhere = $beginWhere . ' ' . $endWhere;
        $videoTimeWhere = $videoBeginWhere . ' ' . $videoEndWhere;
        $thirdLockTimeWhere = $thirdLockBeginWhere . ' ' . $thirdLockEndWhere;

        $mainJoinSql = " left join PersonalAccountSingleInfo PASI on PA.UUID = PASI.PersonalAccountUUID ";
        $mainWhere = " and PASI.IsNewBilling = 0 and (PA.Switch >> 1) & 1 = 1 and PA.PhoneExpireTime < :DefaultExpireTime";
        $bindArray[':DefaultExpireTime'] = DEFAULTEXPIRETIME;

        $newBillSql = "select PA.ID,PA.UUID,PA.PhoneExpireTime,PA.Role,PA.Name,PU.Email,PA.Account,'NewBill' as ServiceType,'' as Brand
        from PersonalAccount PA left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID $mainJoinSql
        where PA.ParentUUID = :ParentUUID and PA.Role = 10 and PA.Active = :Active  and PASI.IsNewBilling = 1 and PA.PhoneExpireTime < :DefaultExpireTime $timeWhere";

        $mainSql = "select PA.ID,PA.UUID,PA.PhoneExpireTime,PA.Role,PA.Name,PU.Email,PA.Account,'PremiumPlan' as ServiceType,'' as Brand
        from PersonalAccount PA left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID $mainJoinSql
        where PA.ParentUUID = :ParentUUID and PA.Role = 10 and PA.Active = :Active $mainWhere $timeWhere";

        $singleVideoStorageSql = "SELECT A.ID,A.UUID, PA.ExpireTime As PhoneExpireTime, A.Role, A.Name, PU.Email, A.Account,'VideoStorage' as ServiceType,'' as Brand
        FROM PersonalAccount A JOIN VideoStorage PA ON A.UUID = PA.PersonalAccountUUID And PA.PlanType = 1
        left join PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID
        WHERE A.Role = 10 AND PA.IsEnable = 1 AND A.ParentUUID = :ParentUUID $videoTimeWhere and PA.ExpireTime <:DefaultExpireTime";

        $thirdLockSql = "select T.ID,T.LockUUID as UUID,T.ExpireTime as PhoneExpireTime,A.Role,'' as Name,PU.Email, A.Account, 'ThirdLock' as ServiceType, Brand from ThirdLockRelateInfo T 
        join PersonalAccount A on A.UUID = T.PersonalAccountUUID left join PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID 
        where T.LockUUID is not null AND A.Role = 10 AND T.ProjectType = 1 AND T.Brand in (3,6) AND A.ParentUUID = :ParentUUID and T.Active = :Active and T.ExpireTime < :DefaultExpireTime $thirdLockTimeWhere";

        $sqlLimit = "limit $offset, $row";

        $tmpSql = "";
        if ($serviceType == 'all') {
            $tmpSql = "$mainSql union all $newBillSql union all $singleVideoStorageSql union all $thirdLockSql";
        } elseif ($serviceType == 0) {
            $tmpSql = $newBillSql;
        } elseif ($serviceType == 1) {
            // 高级功能
            $tmpSql = $mainSql;
        } elseif ($serviceType == 2) {
            // 视频存储
            $tmpSql = $singleVideoStorageSql;
        } elseif ($serviceType == 3) {
            // 视频存储
            $tmpSql = $thirdLockSql;
        }
        $sql = $tmpSql . " order by PhoneExpireTime";

        $userData = $this->execute("$sql $sqlLimit", $bindArray);
        $total = count($this->execute($sql, $bindArray));
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email']);
        }
        return [$userData, $total];
    }

    /**
     * @description: 获取社区待激活用户列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {int} $offset 偏移量
     * @param {int} $row 行数
     * @param {string} $sortField 排序字段
     * @param {string} $sort 排序方式
     * @return [$userData, $total] 用户列表和数目
     * @return array
     * @author: shoubin.chen 2024/11/21 17:20:47 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/21 17:20:47  V7.1.0
     */
    public function getActiveCommunityUserList($bindArray, $offset, $row, $sortField, $sort)
    {
        $active = $bindArray[':Active'];
        $beginTime = $bindArray[':BeginTime'];
        $endTime = $bindArray[':EndTime'];
        $communityTimeZone = $bindArray[':TimeZone'];
        $type = $bindArray[':Type'];
        $communityIds = $bindArray[':CommunityIds'];

        unset($bindArray[':CommunityIds']);
        unset($bindArray[':Type']);
        unset($bindArray[':TimeZone']);

        $timeWhere = '';
        unset($bindArray[':BeginTime']);
        unset($bindArray[':EndTime']);

        $mainUserRole = COMENDMROLE;
        $subUserRole = COMENDSROLE;
        $pmAppUserRole = PMENDMROLE;
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $pmAccountMapTable = PROXY_TABLES['pmAccountMap'];
        $communityRoomTable = PROXY_TABLES['communityRoom'];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        //主账号
        $aptSql = "select PA.ID,PA.UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PU.Email,PA.RoomID,PA.RoomNumber,R.RoomName,R.Floor,'MainUser' as ServiceType, '' as Brand,PA.ParentUUID as ProjectUUID,PA.CommunityUnitUUID
        from $personalAccountTable PA join $communityRoomTable R on R.ID = PA.RoomID left join $personalAccountUserInfoTable PU on PA.UserInfoUUID = PU.UUID
        where PA.ParentID in ($communityIds) and PA.Special = 0 and PA.Role = $mainUserRole and PA.Active = :Active $timeWhere";
        //子账号
        $adtAppSql = "select PA.ID,PA.UUID,C.UnitID,PA.Role,C.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PU.Email,C.RoomID,C.RoomNumber,R.RoomName,R.Floor,'SubUser' as ServiceType, '' as Brand,C.ParentUUID as ProjectUUID,C.CommunityUnitUUID
        from $personalAccountTable PA join $personalAccountTable C on PA.ParentUUID = C.UUID 
        join $communityRoomTable R on R.ID = C.RoomID left join $personalAccountUserInfoTable PU on PA.UserInfoUUID = PU.UUID
        where C.ParentID in ($communityIds) and C.Role = $mainUserRole and C.Active = 1 and PA.Role = $subUserRole and PA.Active = :Active $timeWhere";
        //pmApp
        $pmAppSql = "select PA.ID,PA.UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PM.AccountUUID as Email,PA.RoomID, '' as RoomNumber, '' as RoomName, '' as Floor,'PmApp' as ServiceType, '' as Brand,PA.ParentUUID as ProjectUUID,PA.CommunityUnitUUID
        from $personalAccountTable PA join $pmAccountMapTable PM on PA.UUID = PM.PersonalAccountUUID
        where PA.ParentID in ($communityIds) and PA.Role = $pmAppUserRole and PA.Active = :Active and PM.AppStatus = 1 $timeWhere";
        //三方锁
        $thirdLockSql = "select T.ID,T.LockUUID as UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,T.ExpireTime,PA.Name,PU.Email,PA.RoomID,PA.RoomNumber,R.RoomName,R.Floor,'ThirdLock' as ServiceType, T.Brand,T.AccountUUID as ProjectUUID,T.CommunityUnitUUID from ThirdLockRelateInfo T 
        left join PersonalAccount PA on PA.UUID = T.PersonalAccountUUID left join $communityRoomTable R on R.ID = PA.RoomID left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID 
        where T.LockUUID is not null AND T.ProjectType = 2 AND T.Brand in (3,6) AND T.AccountUUID in (select UUID from Account where ID in ($communityIds)) and T.Active = :Active $timeWhere";

        $sqlLimit = "limit $offset, $row";
        $sql = "$aptSql union all $adtAppSql union all $pmAppSql union all $thirdLockSql";
        if ($type === 'all') {
            $sql = "$aptSql union all $adtAppSql union all $pmAppSql union all $thirdLockSql";
        } elseif ($type === '0') {
            $sql = $aptSql;
        } elseif ($type === '1') {
            $sql = $pmAppSql;
        } elseif ($type === '2') {
            $sql = $adtAppSql;
        } elseif ($type === '4') {
            $sql = $thirdLockSql;
        }
        $orderBySql = '';
        //RoomName-apt, RoomNumber-AptName
        if (in_array($sortField, ['RoomName', 'RoomNumber']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort,ExpireTime";
            if ($sortField === 'RoomName') {
                $orderBySql = " order by CONVERT(RoomName, UNSIGNED) $sort,ExpireTime asc";
            }
        }
        $sql .= $orderBySql;
        $userData = $this->execute("$sql $sqlLimit", $bindArray);
        $total = count($this->execute($sql, $bindArray));
        foreach ($userData as $key => $data) {
            if ($data['Role'] == PMENDMROLE) {
                //pm app的email实际上是其他字段，实际的email是外部查询的，因此pmapp的Email不进行解码
                $userData[$key] = $this->dataArrDecode($data, ['Name']);
            } else {
                $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email']);
            }
        }

        return [$userData, $total];
    }

    /**
     * @description: 获取社区待激活用户列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {int} $offset 偏移量
     * @param {int} $row 行数
     * @param {string} $sortField 排序字段
     * @param {string} $sort 排序方式
     * @return [$userData, $total] 用户列表和数目
     * @return array
     * @author: shoubin.chen 2024/11/21 17:20:47 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/21 17:20:47  V7.1.0
     */
    public function getRenewCommunityUserList($bindArray, $offset, $row, $sortField, $sort)
    {
        $active = $bindArray[':Active'];
        $beginTime = $bindArray[':BeginTime'];
        $endTime = $bindArray[':EndTime'];
        $communityTimeZone = $bindArray[':TimeZone'];
        $type = $bindArray[':Type'];
        $communityIds = $bindArray[':CommunityIds'];
        $insID = $bindArray[':InsID'];
        unset($bindArray[':CommunityIds'], $bindArray[':Type'], $bindArray[':TimeZone'], $bindArray[':InsID']);

        // 过期时间检索
        $beginWhere = $endWhere = '';
        $videoBeginWhere = $videoEndWhere = '';
        $thirdLockBeginWhere = $thirdLockEndWhere = '';

        unset($bindArray[':BeginTime']);
        if (!empty($beginTime)) {
            $beginWhere = 'and PA.ExpireTime >= :BeginTime';
            $videoBeginWhere = 'and PA.ExpireTime >= :BeginTime';
            $thirdLockBeginWhere = 'and T.ExpireTime >= :BeginTime';
            $bindArray[':BeginTime'] = $this->share->util->setTimeZone($beginTime, $communityTimeZone, '', '-');
        }
        $endWhere = 'and PA.ExpireTime < :EndTime';
        $videoEndWhere = 'and PA.ExpireTime < :EndTime';
        $thirdLockEndWhere = 'and T.ExpireTime < :EndTime';
        $bindArray[':InsID'] = $insID;
        $bindArray[':EndTime'] = DEFAULTEXPIRETIME;
        if (!empty($endTime)) {
            $endTime = $this->share->util->setTimeZone($endTime, $communityTimeZone, '', '-');
            if (strtotime($endTime) < strtotime(DEFAULTEXPIRETIME)) {
                $endWhere = 'and PA.ExpireTime <= :EndTime';
                $videoEndWhere = 'and PA.ExpireTime <= :EndTime';
                $thirdLockEndWhere = 'and T.ExpireTime <= :EndTime';
                $bindArray[':EndTime'] = $endTime;
            }
        }
        $timeWhere = $beginWhere.' '.$endWhere;
        $videoTimeWhere = $videoBeginWhere.' '.$videoEndWhere;
        $thirdLockTimeWhere = $thirdLockBeginWhere.' '.$thirdLockEndWhere;

        $mainUserRole = COMENDMROLE;
        $subUserRole = COMENDSROLE;
        $pmAppUserRole = PMENDMROLE;
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $pmAccountMapTable = PROXY_TABLES['pmAccountMap'];
        $communityRoomTable = PROXY_TABLES['communityRoom'];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        //主账号
        $aptSql = "select PA.ID,PA.UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PU.Email,PA.RoomID,PA.RoomNumber,R.RoomName,R.Floor,'Apt' as ServiceType, '' as Brand,PA.ParentUUID as ProjectUUID,PA.CommunityUnitUUID
        from $personalAccountTable PA join $communityRoomTable R on R.ID = PA.RoomID left join $personalAccountUserInfoTable PU on PA.UserInfoUUID = PU.UUID
        where PA.ParentID in ($communityIds) and PA.Special = 0 and PA.Role = $mainUserRole and PA.Active = :Active $timeWhere";
        //子账号
        $adtAppSql = "select PA.ID,PA.UUID,C.UnitID,PA.Role,C.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PU.Email,C.RoomID,C.RoomNumber,R.RoomName,R.Floor,'AdditionalApp' as ServiceType, '' as Brand,C.ParentUUID as ProjectUUID,C.CommunityUnitUUID
        from $personalAccountTable PA join $personalAccountTable C on PA.ParentUUID = C.UUID 
        join $communityRoomTable R on R.ID = C.RoomID left join $personalAccountUserInfoTable PU on PA.UserInfoUUID = PU.UUID
        where C.ParentID in ($communityIds) and C.Role = $mainUserRole and C.Active = 1 and PA.Role = $subUserRole and PA.Active = :Active $timeWhere";
        //pmApp
        $pmAppSql = "select PA.ID,PA.UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,PA.ExpireTime,PA.Name,PM.AccountUUID as Email,PA.RoomID, '' as RoomNumber, '' as RoomName, '' as Floor,'PMApp' as ServiceType, '' as Brand,PA.ParentUUID as ProjectUUID,PA.CommunityUnitUUID
        from $personalAccountTable PA join $pmAccountMapTable PM on PA.UUID = PM.PersonalAccountUUID
        where PA.ParentID in ($communityIds) and PA.Role = $pmAppUserRole and PA.Active = :Active and PM.AppStatus = 1 $timeWhere";
        //视频存储的社区
        $communitySql = "SELECT A.ID, A.UUID, '' AS UnitID, '' AS Role, A.ID AS ProjectId, '' AS ParentID, PA.ExpireTime, '' AS Name, '' AS Email, '' AS RoomID, '' AS RoomNumber, '' AS RoomName, '' AS Floor,'VideoStorage' as ServiceType, '' as Brand,A.ParentUUID as ProjectUUID,'' as CommunityUnitUUID
        FROM Account A JOIN VideoStorage PA ON A.UUID = PA.AccountUUID  And PA.PlanType = 1
        WHERE A.Grade = 21 AND PA.IsEnable = 1 AND A.ManageGroup = :InsID And A.ID in ($communityIds) $videoTimeWhere";
        //三方锁
        $thirdLockSql = "select T.ID,T.LockUUID as UUID,PA.UnitID,PA.Role,PA.ParentID as ProjectId,PA.ParentID,T.ExpireTime,PA.Name,PU.Email,PA.RoomID,PA.RoomNumber,R.RoomName,R.Floor,'ThirdLock' as ServiceType, T.Brand,T.AccountUUID as ProjectUUID,T.CommunityUnitUUID from ThirdLockRelateInfo T 
        left join PersonalAccount PA on PA.UUID = T.PersonalAccountUUID left join $communityRoomTable R on R.ID = PA.RoomID left join PersonalAccountUserInfo PU on PA.UserInfoUUID = PU.UUID 
        where T.LockUUID is not null AND T.ProjectType = 2 AND T.Brand in (3,6) AND T.AccountUUID in (select UUID from Account where ID in ($communityIds)) and T.Active = :Active $thirdLockTimeWhere";



        $sqlLimit = "limit $offset, $row";
        $sql = "$aptSql union all $pmAppSql union all $communitySql union all $thirdLockSql";
        if ($type === 'all') {
            $sql = "$aptSql union all $pmAppSql union all $communitySql union all $thirdLockSql";
        } elseif ($type === '0') {
            $sql = $aptSql;
        } elseif ($type === '1') {
            $sql = $pmAppSql;
        } elseif ($type === '2') {
            $sql = $adtAppSql;
        } elseif ($type === '3') {
            $sql = $communitySql;
        } elseif ($type === '4') {
            $sql = $thirdLockSql;
        }
        $orderBySql = ' order by ExpireTime';

        //RoomName-apt, RoomNumber-AptName
        if (in_array($sortField, ['RoomName', 'RoomNumber']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort,ExpireTime";
            if ($sortField === 'RoomName') {
                $orderBySql = " order by CONVERT(RoomName, UNSIGNED) $sort,ExpireTime asc";
            }
        }
        $sql.=$orderBySql;
        $userData = $this->execute("$sql $sqlLimit", $bindArray);

        $total = count($this->execute($sql, $bindArray));

        foreach ($userData as $key => $data) {
            if ($data['Role'] == PMENDMROLE) {
                //pm app的email实际上是其他字段，实际的email是外部查询的，因此pmapp的Email不进行解码
                $userData[$key] = $this->dataArrDecode($data, ['Name']);
            } else {
                $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email']);
            }

        }

        return [$userData, $total];
    }

    /**
     * @description: ins获取房间列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return [$userData, $total] 用户列表和数目
     * @author: cj 2023-03-10 14:36:21 V6.5.4
     * @lastEditors: cj 2023-03-10 14:36:21 V6.5.4
     */
    public function getRoomListByIns($bindArray, $searchArray)
    {
        $active = $bindArray[':Active'];
        $status = $bindArray[':Status'];
        $build = $bindArray[':Build'];
        unset($bindArray[':Active']);
        unset($bindArray[':Status']);
        unset($bindArray[':Build']);
        $where = '';
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        if ($searchValue !== '' && !is_null($searchValue)) {
            if ($searchKey == 'Unit') {
                $searchKey = 'UnitName';
            }
            
            switch ($searchKey) {
                case 'Name':
                    $where = ' and md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key)';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
                case 'Email':
                    $where = ' and md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key)';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
                case 'RoomName':
                    $where = ' and R.RoomName like :Key';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
                case 'RoomNumber':
                    $where = ' and A.RoomNumber like :Key';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
                case 'UnitName':
                    $where = ' and U.UnitName like :Key';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
            }
        }


        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountCnf = PROXY_TABLES['personalAccountCnf'];
        $tableCommunityUnit = PROXY_TABLES['communityUnit'];
        $tableCommunityRoom = PROXY_TABLES['communityRoom'];
        $tablePersonalAccountUserInfo = PROXY_TABLES['personalAccountUserInfo'];
        if ($status !== 'all') {
            $where .= ' and A.Initialization = :Initialization';
            $bindArray[':Initialization'] = $status;
        }

        if ($active === '0') {
            $where .= ' and A.Active = :Active';
            $bindArray[':Active'] = $active;
        } elseif ($active === '1') {
            $where .= ' and (A.ExpireTime > :ExpireTime and A.Active = 1)';
            $bindArray[':ExpireTime'] = $this->share->util->getNow();
        } elseif ($active === '2') {
            $where .= ' and (A.ExpireTime < :ExpireTime and A.Active = 1)';
            $bindArray[':ExpireTime'] = $this->share->util->getNow();
        }
        $orderBySql = ' order by A.ID desc';
        if (in_array($sortField, ['Name', 'CreateTime', 'RoomName', 'RoomNumber']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by A.$sortField $sort,A.ID desc";
            if ($sortField === 'RoomName') {
                $orderBySql = " order by CONVERT(R.RoomName, UNSIGNED) $sort,A.ID desc";
            }
            if ($sortField === 'Name') {
                $orderBySql = " order by AKCSMapping.NameMapping.DeColumn $sort,A.ID desc";
            }
        }

        if ($build === 'community') {
            $sql = "select count(*) as total
                from $tablePersonalAccount A left join $tableCommunityUnit U on A.UnitID = U.ID 
                join $tableCommunityRoom R on R.ID = A.RoomID join $tablePersonalAccountCnf Pf on A.Account = Pf.Account
                left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID
                where A.ParentUUID=:ParentUUID and A.Special = 0 $where";
            $total = $this->execute($sql, $bindArray)[0]["total"];

            $sql = "select A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Special,A.FirstName,A.LastName,A.RoomNumber,PU.Email,PU.MobileNumber,A.Name,
            Pf.CallType,R.RoomName, A.ExpireTime,A.Active,A.SipAccount,A.CreateTime,A.TimeZone,A.Address,A.Phone,
            A.Phone2,A.Phone3,A.PhoneCode,A.Initialization,A.PhoneStatus,U.UnitName as Building, R.Floor,U.UUID as BuildUUID
            from $tablePersonalAccount A left join $tableCommunityUnit U on A.UnitID = U.ID 
            join $tableCommunityRoom R on R.ID = A.RoomID join $tablePersonalAccountCnf Pf on A.Account = Pf.Account
            left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID 
            left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = A.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(A.Name) 
            where A.ParentUUID=:ParentUUID and A.Special = 0 $where $orderBySql limit $offset,$row";
            $userData = $this->execute($sql, $bindArray);
        } else {
            $bindArray[':UnitID'] = $build;
            $sql = "select count(*) as total
                from $tablePersonalAccount A left join $tableCommunityUnit U on A.UnitID = U.ID 
                join $tableCommunityRoom R on R.ID = A.RoomID join $tablePersonalAccountCnf Pf on A.Account = Pf.Account
                left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID
                where A.ParentUUID=:ParentUUID and A.UnitID = :UnitID $where";
            $total = $this->execute($sql, $bindArray)[0]["total"];
            $sql = "select A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Special,A.FirstName,A.LastName,A.RoomNumber,
                PU.Email,PU.MobileNumber,A.Name,Pf.CallType,R.RoomName,A.ExpireTime,A.Active,A.SipAccount,
                A.CreateTime,A.TimeZone,A.Address,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.Initialization,
                A.PhoneStatus,U.UnitName as Building,R.Floor,U.UUID as BuildUUID
                from $tablePersonalAccount A left join $tableCommunityUnit U on A.UnitID = U.ID 
                join $tableCommunityRoom R on R.ID = A.RoomID join $tablePersonalAccountCnf Pf on A.Account = Pf.Account
                left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID 
                left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = A.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(A.Name) 
                where A.ParentUUID=:ParentUUID and A.UnitID = :UnitID $where $orderBySql limit $offset,$row";
            $userData = $this->execute($sql, $bindArray);
        }
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Email', 'MobileNumber' => 'Phone', 'Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2', 'Phone3']);
        }
        return [$userData, intval($total)];
    }

    /**
     * @description: 新pm获取用户列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return [$userData, $total] 用户列表和数目
     * @author: cj 2023-03-10 14:36:21 V6.5.4
     * @lastEditors: cj 2023-03-10 14:36:21 V6.5.4
     */
    public function getListByNewPm($bindArray, $searchArray)
    {
        $build = $bindArray[':Build'];
        $room = $bindArray[':Room'];
        $status = $bindArray[':Status'];
        $active = $bindArray[':Active'];
        $role = $bindArray[':Role'];
        unset($bindArray[':Build']);
        unset($bindArray[':Room']);
        unset($bindArray[':Status']);
        unset($bindArray[':Active']);
        unset($bindArray[':Role']);
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $where = '';
        if ($searchValue !== '' && !is_null($searchValue)) {
            switch ($searchKey) {
                case 'Name':
                    $where .= ' and md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key)';
                    $bindArray[':Key'] = "%$searchValue%";
                    break;
                case 'Email':
                    $where .= " and md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key)";
                    $bindArray[":Key"] = "%$searchValue%";
                    break;
                case 'MobileNumber':
                    $where .= " and md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :Key)";
                    $bindArray[":Key"] = "%$searchValue%";
                    break;
                default:
                    break;
            }
        }
        $normal = 1;
        $inActive = 2;
        $expiring = 3;
        $expired = 4;
        switch ($active) {
            case $normal:
                $where .= ' and P.Active = 1 and P.ExpireTime > :ExpireTime';
                $bindArray[':ExpireTime'] = $this->share->util->getNow();
                break;
            case $inActive:
                $where .= ' and P.Active = 0';
                break;
            case $expiring:
                $where .= ' and P.Active = 1 and P.ExpireTime < :ExpireTIme and P.ExpireTime > :ExpireTIme2';
                $bindArray[':ExpireTIme'] = date('Y-m-d H:i:s', strtotime('+7 day'));
                $bindArray[':ExpireTIme2'] = $this->share->util->getNow();
                break;
            case $expired:
                $where .= ' and P.Active = 1 and P.ExpireTime < :ExpireTIme';
                $bindArray[':ExpireTIme'] = $this->share->util->getNow();
                break;
        }

        $whereMaster = '';
        $whereMember = '';
        if ($room != 'all') {
            if (is_array($room)) {
                list($roomIDsStr, $bindArrayRoom) = $this->share->util->getImplodeData($room, 'RoomID');
                $whereMaster .= ' and P.RoomID in ('.$roomIDsStr.')';
                $whereMember .= ' and P1.RoomID in ('.$roomIDsStr.')';
                $bindArray = array_merge($bindArray, $bindArrayRoom);
            } else {
                $whereMaster .= ' and P.RoomID = :RoomID';
                $whereMember .= ' and P1.RoomID = :RoomID';
                $bindArray[':RoomID'] = $room;
            }
        }

        if ($build != 'all') {
            $whereMaster .= ' and P.UnitID = :UnitID';
            $whereMember .= ' and P1.UnitID = :UnitID';
            $bindArray[':UnitID'] = $build;
        }

        if ($status != 'all') {
            $where .= ' and P.Initialization = :Initialization';
            $bindArray[':Initialization'] = $status;
        }

        $orderBySql = ' order by ID desc';
        if (in_array($sortField, ['Name', 'Apt']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort,ID desc";
            if ($sortField === 'Apt') {
                $orderBySql = " order by CONVERT(RoomName, UNSIGNED) $sort,ID desc";
            }

            if ($sortField === 'Name') {
                $orderBySql = " order by DeColumn $sort,ID desc";
            }
        }

        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tableCommunityUnit = PROXY_TABLES['communityUnit'];
        $tableCommunityRoom = PROXY_TABLES['communityRoom'];
        $tablePersonalAccountUserInfo = PROXY_TABLES['personalAccountUserInfo'];
        $tablePersonalAccountCommunityInfo = PROXY_TABLES['personalAccountCommunityInfo'];

        $queryResult = 'select P.ID, P.ParentID, P.Name, P.Role, PU.Email, PU.MobileNumber, CU.UnitName,
        CR.RoomName, CR.Floor, P.Initialization, P.Active, P.ExpireTime, P.Account,AKCSMapping.NameMapping.DeColumn, PC.Remark';

        $sqlMaster = " from $tablePersonalAccount P join $tableCommunityUnit CU on P.UnitID = CU.ID
        join $tableCommunityRoom CR on CR.ID = P.RoomID
        left join $tablePersonalAccountUserInfo PU on P.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = P.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(P.Name) 
        left join $tablePersonalAccountCommunityInfo PC on PC.PersonalAccountUUID = P.UUID
        where P.ParentUUID = :ParentUUID and P.Special = 0 $where $whereMaster";

        $sqlMember = " from $tablePersonalAccount P join $tablePersonalAccount P1 on P.ParentUUID = P1.UUID
        join $tableCommunityUnit CU on P1.UnitID = CU.ID join $tableCommunityRoom CR on CR.ID = P1.RoomID 
        left join $tablePersonalAccountUserInfo PU on P.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = P.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(P.Name) 
        left join $tablePersonalAccountCommunityInfo PC on PC.PersonalAccountUUID = P.UUID
        where P1.ParentUUID = :ParentUUID and P.Role = 21 $where $whereMember";

        if ($role == 'master') {
            $total = $this->execute("select count(*) $sqlMaster", $bindArray)[0]['count(*)'];
            $userData = $this->execute("$queryResult,P.RoomNumber as AptName $sqlMaster $orderBySql limit $offset, $rows", $bindArray);
        } elseif ($role == 'member') {
            $total = $this->execute("select count(*) $sqlMember", $bindArray)[0]['count(*)'];
            $userData = $this->execute("$queryResult,P1.RoomNumber as AptName $sqlMember $orderBySql limit $offset, $rows", $bindArray);
        } else {
            $totals = $this->execute(
                "select count(*) $sqlMaster union all select count(*) $sqlMember",
                $bindArray
            );
            $total = $totals[0]["count(*)"] + $totals[1]["count(*)"];
            $userData = $this->execute(
                "$queryResult,P.RoomNumber as AptName $sqlMaster union $queryResult,P1.RoomNumber as AptName $sqlMember $orderBySql limit $offset, $rows",
                $bindArray
            );
        }

        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Email', 'MobileNumber' => 'Phone', 'Name']);
        }

        return [$userData, intval($total)];
    }

    /**
     * @description: 新pm获取房间列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return [$userData, $total] 用户列表和数目
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getAptListByNewPm($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $build = $bindArray[':Build'];
        $apt = $bindArray[':Apt'];
        $key = $bindArray[':Key'];
        unset($bindArray[':Build']);
        unset($bindArray[':Apt']);
        unset($bindArray[':Key']);
        $where = 'where PA.ParentUUID=:ParentUUID and Role = 20';
        if ($build && $build !== 'all') {
            $where .= ' and CU.ID = :build ';
            $bindArray[':build'] = $build;
        }
        if ($apt && $apt !== 'all') {
            $where .= ' and CR.ID = :apt ';
            $bindArray[':apt'] = $apt;
        }
        if ($key !== null && $key !== '') {
            $where .= ' and PA.RoomNumber like :key ';
            $bindArray[':key'] = "%$key%";
        }

        $orderBySql = ' order by PA.CreateTime desc,PA.ID desc';
        if (in_array($sortField, ['Apt', 'RoomNumber']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by PA.$sortField $sort,PA.ID desc";
            if ($sortField === 'Apt') {
                $orderBySql = " order by CONVERT(CR.RoomName, UNSIGNED) $sort,PA.ID desc";
            }
        }

        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountCnf = PROXY_TABLES['personalAccountCnf'];
        $tableCommunityUnit = PROXY_TABLES['communityUnit'];
        $tableCommunityRoom = PROXY_TABLES['communityRoom'];

        $column = "select PA.ID, PA.UUID, CU.UnitName as Build, CR.RoomName as AptNumber, CR.Floor, PA.RoomNumber as AptName, PA.Special, PA.Active,
        PA.EnableIpDirect,PA.TempKeyPermission,PC.AllowCreateSlaveCnt,PC.Flags,PA.Account";
        $sql = "from $tablePersonalAccount PA left join $tableCommunityRoom CR on PA.RoomID = CR.ID 
            left join $tableCommunityUnit CU on PA.UnitID = CU.ID left join $tablePersonalAccountCnf PC
            on PA.Account = PC.Account $where";
        $total = $this->execute("select count(*) as total $sql", $bindArray)[0]['total'];
        $userData = $this->execute("$column $sql $orderBySql limit $offset,$rows", $bindArray);

        return [$userData, intval($total)];
    }

    public function  getAllAptInfoList($bindArray)
    {
        $where = 'where PA.ParentUUID=:ParentUUID and Role = 20';

        $orderBySql = ' order by PA.CreateTime desc,PA.ID desc';
        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountCnf = PROXY_TABLES['personalAccountCnf'];
        $tableCommunityUnit = PROXY_TABLES['communityUnit'];
        $tableCommunityRoom = PROXY_TABLES['communityRoom'];

        $column = "select PA.ID, PA.UUID, CU.UnitName as Build, CR.RoomName as AptNumber, CR.Floor, PA.RoomNumber as AptName, PA.Special, PA.Active,
        PA.EnableIpDirect,PA.TempKeyPermission,PC.AllowCreateSlaveCnt,PC.Flags,PA.Account";
        $sql = "from $tablePersonalAccount PA left join $tableCommunityRoom CR on PA.RoomID = CR.ID 
            left join $tableCommunityUnit CU on PA.UnitID = CU.ID left join $tablePersonalAccountCnf PC
            on PA.Account = PC.Account $where";

        return $this->execute("$column $sql $orderBySql", $bindArray);
    }

    /**
     * @description: 新pm获取房间详情
     * @param {string} $id
     * @return $data 用户详情
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getAptInfoByNewPm($id)
    {
        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountCnf = PROXY_TABLES['personalAccountCnf'];
        $tableCommunityUnit = PROXY_TABLES['communityUnit'];
        $tableCommunityRoom = PROXY_TABLES['communityRoom'];
        $sql = "select PA.ID, PA.UUID, CU.UnitName as Building, CR.RoomName as AptNumber,CR.Floor, PA.RoomNumber as AptName,
            PA.Special, PA.EnableIpDirect,PA.TempKeyPermission,PC.AllowCreateSlaveCnt,PC.CallType,
            PC.Flags 
            from $tablePersonalAccount PA join $tableCommunityRoom CR on PA.RoomID = CR.ID
            join $tableCommunityUnit CU on PA.UnitID = CU.ID join $tablePersonalAccountCnf PC
            on PA.Account = PC.Account where PA.ID=:ID";

        $userData = $this->execute($sql, [':ID' => $id])[0];
        return $userData;
    }

    /**
     * @description: super/dis获取用户列表
     * @param {string} $role familyMaster|familyMember
     * @param {string} $disId
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return [$userData, $total] 用户列表和数目
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getListForManage($role, $disId, $searchArray, $supSearch = null)
    {
        $tableAccount = PROXY_TABLES['account'];
        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountUserInfo = PROXY_TABLES['personalAccountUserInfo'];
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        // 展示非空房间
        $where = ' and A.Special = 0';
        $where2 = '';
        $where3 = '';
        $bindArray = [];
        $mainJoinSql = $subJoinSql = $joinField = [];
        $joinPU = $joinC = false;
        if ($searchValue !== '' && !is_null($searchValue)) {
            $searchKey = $searchKey === 'Sip' ? 'SipAccount':$searchKey;

            switch ($searchKey) {
                case 'SipAccount':
                    $where .= ' AND A.SipAccount LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'Email':
                    $where .= ' AND md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = $subJoinSql[] = "left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'MobileNumber':
                    $where .= ' AND md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = $subJoinSql[] = "left join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'Name':
                    $where .= ' AND md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'Install':
                    $where .= ' AND C.Account LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                case 'FamilyMaster':
                    $where2 = ' AND md5(D.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)';
                    // 1 = 2是为了不显示主账号 FamilyMaster查询的时候
                    $where3 = ' AND 1 = 2';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'Community':
                    $where .= ' AND B.Location LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                case 'Phone':
                    $where .= ' AND (md5(A.Phone) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue) 
                    or md5(A.Phone2) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue) 
                    or md5(A.Phone3) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue))';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'SubDis':
                    list($insUUIDs, $insBindArray) = $this->dao->account->getInsSearchValueForSubDis($disId, $searchValue);
                    if ($insUUIDs === '') {
                        return [[], 0];
                    }
                    $where .= " And C.UUID in ($insUUIDs)";
                    $bindArray = array_merge($bindArray, $insBindArray);
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                default:
                    break;
            }
        }
        if (!empty($disId)) {
            $disData = $this->dao->account->selectByID($disId, 'Grade,UUID')[0];
            if (intval($disData['Grade']) === AREAGRADE) {
                $where .= ' AND B.ParentID = :ParentID';
                $bindArray[':ParentID'] = $disId;
                $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
                $joinC = true;
            } else {
                $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID', $disData['UUID'], 'InstallerUUID');
                if (count($insData) === 0) {
                    return [[], 0];
                }
                $insUUIDs = array_column($insData, 'InstallerUUID');
                list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
                $where .= " AND C.UUID in ($uuidBindStr)";
                $bindArray = array_merge($bindArray, $uuidBindArray);
                $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
                $joinC = true;
            }
        }

        // v6.8.0 增加参数DisUUID, SubDisUUID,InsUUID,Status，ExpireTimeBegin，ExpireTimeEnd
        if (isset($supSearch['DisUUID'])) {
            $where .= " AND C.ParentUUID =:DisUUID";
            $bindArray[":DisUUID"] = $supSearch['DisUUID'];
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['SubDisUUID'])) {
            $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID',$supSearch['SubDisUUID'], 'InstallerUUID');
            if (count($insData) === 0) {
                return [[], 0];
            }
            $insUUIDs = array_column($insData, 'InstallerUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
            $where .= " AND C.UUID in ($uuidBindStr)";
            $bindArray = array_merge($bindArray, $uuidBindArray);
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['InsUUID'])) {
            $where .= " AND C.UUID =:InsUUID";
            $bindArray[":InsUUID"] = $supSearch['InsUUID'];
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Community,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['Active'])) {
            switch ($supSearch['Active']) {
                case 0: //未激活
                    $where .= " AND A.Active = 0";
                    break;
                case 1: //已激活未过期
                    $where .= " AND A.Active = 1 AND A.ExpireTime > :Now";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    break;
                case 2: //已过期
                    $where .= " AND A.Active = 1 AND A.ExpireTime < :Now";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    break;
            }
        }
        if (isset($supSearch['ExpireTimeBegin'])) {
            $where .= " AND A.ExpireTime >= :ExpireTimeBegin AND A.Active !=0";
            $bindArray[":ExpireTimeBegin"] = $supSearch['ExpireTimeBegin'];
        }
        if (isset($supSearch['ExpireTimeEnd'])) {
            $where .= " AND A.ExpireTime <= :ExpireTimeEnd AND A.Active !=0";
            $bindArray[":ExpireTimeEnd"] = $supSearch['ExpireTimeEnd'];
        }

        // 7.1.1 增加 createTime搜索项
        if (isset($supSearch['CreateStartTime'])) {
            $where .= " AND A.CreateTime >= :CreateStartTime";
            $bindArray[":CreateStartTime"] = $supSearch['CreateStartTime'];
        }
        if (isset($supSearch['CreateEndTime'])) {
            $where .= " AND A.CreateTime <= :CreateEndTime";
            $bindArray[":CreateEndTime"] = $supSearch['CreateEndTime'];
        }

        $mainJoinSql = join(' ', array_unique($mainJoinSql));
        $subJoinSql = join(' ', array_unique($subJoinSql));
        $joinField = join(',', array_unique($joinField));
        if (!empty($joinField)) {
            $joinField = ',' . $joinField;
        }

        $title = "select A.ID,A.Active,A.SipAccount,A.Name,A.Role,A.Address,A.Phone,A.Phone2,A.Phone3,A.CreateTime,A.ExpireTime,A.PhoneExpireTime $joinField";
        $table1 = "$tablePersonalAccount A $mainJoinSql";
        $table2 = "$tablePersonalAccount A JOIN $tablePersonalAccount D on A.ParentID = D.ID $subJoinSql";

        $sql = "$title,'--' as FamilyMaster,A.UserInfoUUID,A.ParentUUID as ProjectUUID from $table1 where A.Role = " . COMENDMROLE . " $where $where3 union all $title,D.Name as FamilyMaster,A.UserInfoUUID,D.ParentUUID as ProjectUUID 
        from $table2 where A.Role = " . COMENDSROLE . " $where $where2";
        $countSql = "select SUM(row_count) as total from (select count(*) as row_count from $table1 where A.Role = " . COMENDMROLE . " $where $where3 union all
        select count(*) as row_count from $table2 where A.Role = " . COMENDSROLE . " $where $where2) as Counts";

        switch ($role) {
            case 'familyMaster':
                if ($where3 !== '') {
                    return [[], 0];
                }
                $sql = "$title,'--' as FamilyMaster,A.UserInfoUUID,A.ParentUUID as ProjectUUID from $table1 where A.Role = " . COMENDMROLE . " $where $where3";
                $countSql = "select count(*) as total from $table1 where A.Role = " . COMENDMROLE . " $where $where3";
                break;
            case 'familyMember':
                $sql = "$title,D.Name as FamilyMaster,A.UserInfoUUID,D.ParentUUID as ProjectUUID from $table2 where  A.Role = " . COMENDSROLE . " $where $where2";
                $countSql = "select count(*) as total from $table2 where A.Role = " . COMENDSROLE . " $where $where2";
                break;
            default:
                break;
        }

        $total = intval($this->execute($countSql, $bindArray)[0]['total']);
        $userData = $this->execute("$sql order by ID DESC limit $offset,$rows", $bindArray);

        // 有未连表的需要的字段，进行获取补充字段
        if (!$joinPU && !empty($userData)) {
            $userInfoUUIDs = array_column($userData, 'UserInfoUUID');
            //Email、Mobile字段不进行自动解密，下面会统一解密
            $userInfos = $this->dao->personalAccountUserInfo->selectByArray([['UUID', $userInfoUUIDs]], 'UUID,Email,MobileNumber', false, false);
            $userInfos = array_column($userInfos, null, 'UUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Email'] = $userInfos[$data['UserInfoUUID']]['Email'];
                $userData[$key]['MobileNumber'] = $userInfos[$data['UserInfoUUID']]['MobileNumber'];
            }
        }
        if (!$joinC && !empty($userData)) {
            $projectUUIDs = array_column($userData, 'ProjectUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($projectUUIDs);
            $sql = "select Ins.Account as Install,Ins.UUID as InstallerUUID, Project.UUID as ProjectUUID, Project.Location as Community from $tableAccount Project JOIN $tableAccount Ins on Project.ManageGroup = Ins.ID where Project.UUID in ($uuidBindStr)";
            $projectInfos = $this->execute($sql, $uuidBindArray);
            $projectInfos = array_column($projectInfos, null, 'ProjectUUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Install'] = $projectInfos[$data['ProjectUUID']]['Install'];
                $userData[$key]['InstallerUUID'] = $projectInfos[$data['ProjectUUID']]['InstallerUUID'];
                $userData[$key]['Community'] = $projectInfos[$data['ProjectUUID']]['Community'];
            }
        }

        foreach ($userData as $key => $data) {
            $confusionField = ['Name', 'Email', 'MobileNumber' => 'Phone', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone'];
            if ($data['Role'] == COMENDSROLE) {
                $confusionField['FamilyMaster'] = 'Name';
            }
            $userData[$key] = $this->dataArrDecode($data, $confusionField);
        }
        return [$userData, intval($total)];
    }

    /**
     * @description: pm获取小区下用户信息
     * @param {array} $communityId 社区ID
     * @return $userData 用户列表
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getAptUserListByNewPm($communityId)
    {
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $allUsers = $this->execute(
            "select ID,UUID,Name,RoomID,RoomNumber,Role,Special,UserInfoUUID,Account,Initialization,appLoginStatus,UnitID,FirstName,LastName,PhoneCode,Phone,Phone2,Phone3,ParentID,SyncDoorMethodToThirdPartyLock,ParentUUID from $personalAccountTable where ParentID=:MngAccountID and Role = ".COMENDMROLE."
            union select P.ID,P.UUID,P.Name,P2.RoomID,'' as RoomNumber,P.Role,0,P.UserInfoUUID,P.Account,P.Initialization,P.appLoginStatus,P.UnitID,P.FirstName,P.LastName,P.PhoneCode,P.Phone,P.Phone2,P.Phone3,P.ParentID,P.SyncDoorMethodToThirdPartyLock,P.ParentUUID from $personalAccountTable P 
            join $personalAccountTable P2 on P2.ID = P.ParentID where P2.ParentID=:MngAccountID and P.Role = ".COMENDSROLE,
            [':MngAccountID'=>$communityId]
        );
        foreach ($allUsers as $key => $data) {
            $allUsers[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $allUsers;
    }

    /**
     * @description: message获取用户信息
     * @param {array} $projectId 社区ID
     * @return $userData 用户列表
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getListForMessage($projectId, $key, $build)
    {
        $searchArray = [':ParentID'=>$projectId];
        $searchArray[':Key'] = "%$key%";
        if ($build === 'all') {
            $whereMapping = "";
            if ($key !== '' && !is_null($key)) {
                $whereMapping = " AND (P.Account like :Key 
                or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key)
                or md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key))";
            }
            $userData = $this->execute(
                'select P.Account,PU.Email,P.Name,P.Name as Name2,U.UnitName,R.RoomName,R.Floor
                from PersonalAccount P  left join CommunityUnit U on P.UnitID = U.ID
                left join CommunityRoom R on R.ID = P.RoomID left join PersonalAccountUserInfo PU on P.UserInfoUUID = PU.UUID 
                where P.Role = '.COMENDMROLE.' AND P.ParentID = :ParentID ' . $whereMapping,
                $searchArray
            );
        } else {
            $searchArray[':UnitID'] = $build;
            $whereMapping = "";
            if ($key !== '' && !is_null($key)) {
                $whereMapping = " AND (P.Account like :Key 
                or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key)
                or md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key))";
            }
            $userData = $this->execute(
                'select P.Account,PU.Email,P.Name,P.Name as Name2,U.UnitName,R.RoomName,R.Floor
                from PersonalAccount P  left join CommunityUnit U on P.UnitID = U.ID
                left join CommunityRoom R on R.ID = P.RoomID left join PersonalAccountUserInfo PU on P.UserInfoUUID = PU.UUID 
                where P.Role = '.COMENDMROLE.' AND P.ParentID = :ParentID and P.UnitID = :UnitID' . $whereMapping,
                $searchArray
            );
        }
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Email', 'Name', 'Name2' => 'Name']);
        }
        return $userData;
    }

        /**
     * @description: super/dis获取办公用户信息列表
     * @param {string} $id 项目 id
     * @return $userData 用户列表
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getOfficeUserForSupArea($id, $searchArray, $supSearch = null)
    {
        $personalAccountTable = PROXY_TABLES["personalAccount"];
        $accountTable = PROXY_TABLES["account"];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        $officePersonnelTable = PROXY_TABLES['officePersonnel'];
        $personalAccountOfficeInfoTable = PROXY_TABLES['personalAccountOfficeInfo']; //旧办公用户信息子表
        $staffRole = OFFSTAFFROLE;
        $personnelRole = OFFPERSONNELROLE;
        $newPersonnelRole = OFF_NEW_PERSONNEL_ROLE;
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $bindArray = [];
        $manageData = $this->dao->account->selectByID($id, 'Grade,UUID')[0];
        $grade = intval($manageData['Grade']);
        $id = $grade === SUPERGRADE ? '':$id;

        $joinSql = $joinField = [];
        $joinPU = $joinC = false;
        if ($searchValue !== '' && !is_null($searchValue)) {
            $searchKey === 'Sip' ? 'SipAccount':$searchKey;

            switch ($searchKey) {
                case 'Sip':
                    $where = "AND A.SipAccount LIKE :searchValue";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    break;
                case 'Email':
                    $where = "AND md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    $joinSql[] = "left join $personalAccountUserInfoTable PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'MobileNumber':
                    $where = "AND md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    $joinSql[] = "left join $personalAccountUserInfoTable PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'Name':
                    $where = "AND md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    break;
                case 'Office':
                    $where = "AND B.Location LIKE :searchValue";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                case 'Phone':
                    $where = "AND md5(A.Phone) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                    break;
                case 'SubDis':
                    list($insUUIDs, $insBindArray) = $this->dao->account->getInsSearchValueForSubDis($id, $searchValue);
                    if ($insUUIDs === '') {
                        return [[], 0];
                    }
                    $where = " And C.UUID in ($insUUIDs)";
                    $bindArray = array_merge($bindArray, $insBindArray);
                    $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                case 'Install':
                    $where = ' AND C.Account LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
                    $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                default:
                    break;
            }
        }
        // v6.8.0 增加参数DisUUID, SubDisUUID,InsUUID,Status，ExpireTimeBegin，ExpireTimeEnd
        if (isset($supSearch['DisUUID'])) {
            $where .= " AND C.ParentUUID =:DisUUID";
            $bindArray[":DisUUID"] = $supSearch['DisUUID'];
            $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['SubDisUUID'])) {
            $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID',$supSearch['SubDisUUID'], 'InstallerUUID');
            if (count($insData) === 0) {
                return [[], 0];
            }
            $insUUIDs = array_column($insData, 'InstallerUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
            $where .= " AND C.UUID in ($uuidBindStr)";
            $bindArray = array_merge($bindArray, $uuidBindArray);
            $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['InsUUID'])) {
            $where .= " AND C.UUID =:InsUUID";
            $bindArray[":InsUUID"] = $supSearch['InsUUID'];
            $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['Active'])) {
            switch ($supSearch['Active']) {
                case 0: //未激活
                    $where .= " AND A.Active = 0";
                    break;
                case 1: //已激活未过期
                    $where .= " AND A.Active = 1 AND A.ExpireTime > :Now";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    break;
                case 2: //已过期
                    $where .= " AND A.Active = 1 AND A.ExpireTime < :Now";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    break;
            }
        }
        if (isset($supSearch['ExpireTimeBegin'])) {
            $where .= " AND A.ExpireTime >= :ExpireTimeBegin AND A.Active !=0";
            $bindArray[":ExpireTimeBegin"] = $supSearch['ExpireTimeBegin'];
        }
        if (isset($supSearch['ExpireTimeEnd'])) {
            $where .= " AND A.ExpireTime <= :ExpireTimeEnd AND A.Active !=0";
            $bindArray[":ExpireTimeEnd"] = $supSearch['ExpireTimeEnd'];
        }

        // 7.1.1 新增搜索项
        if (isset($supSearch['CreateBeginTime'])) {
            $where .= " AND A.CreateTime >= :CreateBeginTime";
            $bindArray[":CreateBeginTime"] = $supSearch['CreateBeginTime'];
        }
        if (isset($supSearch['CreateEndTime'])) {
            $where .= " AND A.CreateTime <= :CreateEndTime";
            $bindArray[":CreateEndTime"] = $supSearch['CreateEndTime'];
        }

        if ($grade === AREAGRADE) {
            $where .= " AND B.ParentID = :ParentID";
            $bindArray[':ParentID'] = $id;
            $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        } elseif ($grade === SUBDISTRIBUTOR) {
            $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID', $manageData['UUID'], 'InstallerUUID');
            if (count($insData) === 0) {
                return [[], 0];
            }
            $insUUIDs = array_column($insData, 'InstallerUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
            $where .= " AND C.UUID in ($uuidBindStr)";
            $bindArray = array_merge($bindArray, $uuidBindArray);
            $joinSql[] = "join $accountTable B on A.ParentID = B.ID join $accountTable C on B.ManageGroup = C.ID";
            $joinField[] = "B.Location as Office,B.UUID,C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }

        $joinSql = join(' ', array_unique($joinSql));
        $joinField = join(',', array_unique($joinField));
        if (!empty($joinField)) {
            $joinField = ',' . $joinField;
        }

        $queryResult = "select A.ID,A.SipAccount,A.UUID as Personnel,A.Name,A.Role,A.Address,A.Phone,A.CreateTime,A.ExpireTime,A.PhoneExpireTime,A.Active,A.UserInfoUUID,A.ParentUUID as ProjectUUID $joinField";
        $table = "$personalAccountTable A $joinSql";
        $where = "A.Role in ($staffRole,$personnelRole,$newPersonnelRole) $where";

        if (isset($supSearch['SmartPlusIntercomFeature'])) {
            // 新旧办公用户是否开启SmartPlusIntercomFeature
            // 因为用left join 查询速度非常慢，所以使用 UNION 方式， 将其他的条件拼在两条UNION的语句后面
            $UNIONSql = "($queryResult from $table  join $officePersonnelTable OP on OP.PersonalAccountUUID = A.UUID and OP.IsSmartPlusIntercom = :SmartPlusIntercomFeature and $where" . ' UNION '
                . "$queryResult from $table  join $personalAccountOfficeInfoTable PAO on PAO.PersonalAccountUUID = A.UUID and PAO.Flags & 1 = :SmartPlusIntercomFeature and $where ) AS combined_results";
            $countSql = "SELECT count(*)  from " . $UNIONSql;
            $resultSql = "SELECT * FROM " . $UNIONSql . " order by ID DESC limit $offset,$rows" ;
            $bindArray[":SmartPlusIntercomFeature"] = intval($supSearch['SmartPlusIntercomFeature']);
        } else {
            $countSql = "select count(*) from $table where $where";
            $resultSql = "$queryResult from $table where $where order by ID DESC limit $offset,$rows";
        }

        $total = $this->execute($countSql, $bindArray)[0]["count(*)"];
        $userData = $this->execute($resultSql, $bindArray);

        // 有未连表的需要的字段，进行获取补充字段
        if (!$joinPU && !empty($userData)) {
            $userInfoUUIDs = array_column($userData, 'UserInfoUUID');
            //Email、Mobile字段不进行自动解密，下面会统一解密
            $userInfos = $this->dao->personalAccountUserInfo->selectByArray([['UUID', $userInfoUUIDs]], 'UUID,Email,MobileNumber', false, false);
            $userInfos = array_column($userInfos, null, 'UUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Email'] = $userInfos[$data['UserInfoUUID']]['Email'];
                $userData[$key]['MobileNumber'] = $userInfos[$data['UserInfoUUID']]['MobileNumber'];
            }
        }
        if (!$joinC && !empty($userData)) {
            $projectUUIDs = array_column($userData, 'ProjectUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($projectUUIDs);
            $sql = "select Ins.Account as Install,Ins.UUID as InstallerUUID, Project.UUID as ProjectUUID, Project.Location as Office from $accountTable Project JOIN $accountTable Ins on Project.ManageGroup = Ins.ID where Project.UUID in ($uuidBindStr)";
            $projectInfos = $this->execute($sql, $uuidBindArray);
            $projectInfos = array_column($projectInfos, null, 'ProjectUUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Install'] = $projectInfos[$data['ProjectUUID']]['Install'];
                $userData[$key]['InstallerUUID'] = $projectInfos[$data['ProjectUUID']]['InstallerUUID'];
                $userData[$key]['Office'] = $projectInfos[$data['ProjectUUID']]['Office'];
                $userData[$key]['UUID'] = $projectInfos[$data['ProjectUUID']]['ProjectUUID'];
            }
        }

        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email', 'MobileNumber' => 'Phone', 'Phone']);
        }
        return [$userData, intval($total)];
    }

    /**
     * @description:获得单住户的异常信息，不包含第三方设备
     * @param: {type}
     * @param {array} $bindArray
     * @param {array} $searchArray
     * @param {enum} $type all:设备异常和用户异常 People:用户异常 Device:设备异常
     * @return array
     * @author: shoubin.chen 2023-10-08 14:03:08 v6.7
     * @lastEditor: shoubin.chen 2023-10-08 14:03:08 v6.7
     */
    public function getSingleAbnormalList($bindArray, $searchArray, $type)
    {
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $devicesTable = PROXY_TABLES['personalDevices'];
        $selectDevice = " 'Device' AS Source, ID , UUID , Location as Name,NULL as Role, MAC ,Status,NULL as RoomNumber,Type,NULL as ExpireTime, LastDisConn as HappenOn,Brand,NULL as UserInfoUUID ";
        $whereDevice = " Node = :Node and Status = 0 And Brand = 0";

        $peopleTable = PROXY_TABLES["personalAccount"];
        $selectPeople = " 'User' AS Source, ID , UUID , Name,Role, NULL as MAC ,Active as Status,RoomNumber,NULL as Type,ExpireTime, 
        if(Active = 0 , CreateTime, ExpireTime) as HappenOn,NULL as Brand,UserInfoUUID ";
        $wherePeople = ' (Active = 0 OR (Active = 1 and ExpireTime < :Now )) And ((UUID=:UUID And Role=10) OR (ParentUUID=:UUID And Role=11))';

        $orderBySql = " order by $sortField $sort";
        switch ($type) {
            case "User":
                $totalSql = "select count(*) from $peopleTable where $wherePeople";
                $selectSql = "select $selectPeople from $peopleTable where $wherePeople $orderBySql limit $offset,$rows";
                $bindArray[":Now"] = $this->share->util->getNow();
                break;
            case "Device":
                $totalSql = "select count(*) from $devicesTable where $whereDevice";
                $selectSql = "select $selectDevice from $devicesTable where $whereDevice $orderBySql limit $offset,$rows";
                break;
            default:
                $selectPeople = "select $selectPeople from $peopleTable where $wherePeople ";
                $selectDevice = "select $selectDevice from $devicesTable where $whereDevice ";
                $tmpSelect = "$selectPeople UNION $selectDevice";
                $bindArray[":Now"] = $this->share->util->getNow();
                $totalSql = "select count(*) from ($tmpSelect) as tmp_table";
                $selectSql = "select * from ($tmpSelect) as tmp_table $orderBySql limit $offset,$rows";
                break;
        }
        $total = $this->execute($totalSql, $bindArray)[0]["count(*)"];
        $data = $this->execute($selectSql, $bindArray);
        //数据解密
        foreach ($data as $key => &$item) {
            if ($item['Source'] === 'User') {
                $item = $this->dataArrDecode($item, ['Name']);
            }
        }
        unset($item);
        return [$data, intval($total)];
    }

    /**
     * @description:获得办公的异常信息，不包含第三方设备
     * @param: {type}
     * @param {array} $bindArray
     * @param {array} $searchArray
     * @param {enum} $type all:设备异常和用户异常 People:用户异常 Device:设备异常
     * @return array
     * @author: shoubin.chen 2023-10-08 14:03:08 v6.7
     * @lastEditor: shoubin.chen 2023-10-08 14:03:08 v6.7
     */
    public function getOfficeAbnormalList($bindArray, $searchArray, $type)
    {
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $devicesTable = PROXY_TABLES['devices'];
        $selectDevice = " 'Device' AS Source, ID , UUID , Location as Name, MAC ,NULL as Role,Status,Type,Grade,NULL as ExpireTime, LastDisConn as HappenOn,Brand,NULL as UserInfoUUID,UnitID ";
        $whereDevice = " MngAccountID = :MngAccountID and Status = 0 And ProjectType = 1 And Brand = 0";

        $peopleTable = PROXY_TABLES["personalAccount"];
        $selectPeople = "'User' AS Source, ID , UUID , Name,NULL as MAC,Role,Active as Status,NULL as Type,NULL as Grade,ExpireTime, 
        if(Active = 0 , CreateTime, ExpireTime) as HappenOn,NULL as Brand,UserInfoUUID,UnitID ";
        $wherePeople = ' ParentID = :ParentID And (Active = 0 OR (Active = 1 and ExpireTime < :Now )) And role in(30,31)';

        $orderBySql = " order by $sortField $sort";
        switch ($type) {
            case "User":
                $totalSql = "select count(*) from $peopleTable where $wherePeople";
                $selectSql = "select $selectPeople from $peopleTable where $wherePeople $orderBySql limit $offset,$rows";
                $bindArray[":Now"] = $this->share->util->getNow();
                break;
            case "Device":
                $totalSql = "select count(*) from $devicesTable where $whereDevice";
                $selectSql = "select $selectDevice from $devicesTable where $whereDevice $orderBySql limit $offset,$rows";
                break;
            default:
                $selectPeople = "select $selectPeople from $peopleTable where $wherePeople ";
                $selectDevice = "select $selectDevice from $devicesTable where $whereDevice ";
                $tmpSelect = "$selectPeople UNION $selectDevice";
                $bindArray[":Now"] = $this->share->util->getNow();
                $totalSql = "select count(*) from ($tmpSelect) as tmp_table";
                $selectSql = "select * from ($tmpSelect) as tmp_table $orderBySql limit $offset,$rows";
                break;
        }
        $total = $this->execute($totalSql, $bindArray)[0]["count(*)"];
        $data = $this->execute($selectSql, $bindArray);
        //数据解密
        foreach ($data as &$item) {
            if ($item['Source'] === 'User') {
                $item = $this->dataArrDecode($item, ['Name']);
            }
        }
        unset($item);
        return [$data, intval($total)];
    }
    /**
     * @description:获得社区的异常信息，不包含第三方设备
     * @param: {type}
     * @param {array} $bindArray
     * @param {array} $searchArray
     * @param {enum} $type all:设备异常和用户异常和PmApp异常 People:用户异常 Device:设备异常 PmApp
     * @return array
     * @author: shoubin.chen 2023-10-08 14:03:08 v6.7
     * @lastEditor: shoubin.chen 2023-10-08 14:03:08 v6.7
     */
    public function getCommunityAbnormalList($bindArray, $searchArray, $type)
    {
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $devicesTable = PROXY_TABLES['devices'];
        $selectDevice = " 'Device' AS Source, ID , UUID , Location as Name,Grade,MAC,Status,Type,NULL as Role,NULL as ExpireTime,
         LastDisConn as HappenOn,Brand,NULL as UserInfoUUID,UnitID,NULL as RoomID ";
        $whereDevice = " MngAccountID = :MngAccountID and Status = 0 And ProjectType = 0 And Brand = 0";

        $peopleTable = PROXY_TABLES["personalAccount"];
        $selectPeople = " 'User' AS Source, P.ID , P.UUID , P.Name,NULL as Grade ,NULL as MAC,P.Active as Status,NULL as Type,Role,P.ExpireTime, 
        if(P.Active = 0 , P.CreateTime, P.ExpireTime) as HappenOn,NULL as Brand,P.UserInfoUUID,P.UnitID,RoomID ";
        $wherePeople = ' ParentID = :ParentID And (Active = 0 OR (Active = 1 and ExpireTime < :Now )) And role in(20,21)';
        $joinPeople = "JOIN PersonalAccountUserInfo PAU ON P.UserInfoUUID = PAU.UUID";

        $selectPm = "'PmApp' AS Source,PA.ID, PA.UUID, PA.Name,NULL as Grade,NULL AS MAC,PA.Active AS Status,NULL as Type,NULL as Role,
        PA.ExpireTime,PA.ExpireTime AS HappenOn, NULL AS Brand, NULL AS UserInfoUUID,NULL AS UnitID,NULL as RoomID";
        $fromPm = "PmAccountMap PAM JOIN PersonalAccount PA ON PAM.PersonalAccountUUID = PA.UUID";
        $wherePm = "PAM.ProjectUUID = :ProjectUUID and (PA.Active = 0  OR (PA.Active = 1 and PA.ExpireTime < :Now)) And PA.Role = 40";

        $orderBySql = " order by $sortField $sort";
        switch ($type) {
            case "User":
                $totalSql = "select count(*) from $peopleTable P $joinPeople where $wherePeople";
                $selectSql = "select $selectPeople from $peopleTable P $joinPeople where $wherePeople $orderBySql limit $offset,$rows";
                $bindArray[":Now"] = $this->share->util->getNow();
                break;
            case "Device":
                $totalSql = "select count(*) from $devicesTable where $whereDevice";
                $selectSql = "select $selectDevice from $devicesTable where $whereDevice $orderBySql limit $offset,$rows";
                break;
            case "PmApp":
                $totalSql = "select count(*) from $fromPm where $wherePm";
                $selectSql = "Select $selectPm FROM $fromPm WHERE $wherePm $orderBySql limit $offset,$rows";
                $bindArray[":Now"] = $this->share->util->getNow();
                break;
            default:
                $selectPeople = "select $selectPeople from $peopleTable P $joinPeople where $wherePeople";
                $selectDevice = "select $selectDevice from $devicesTable where $whereDevice ";
                $selectPm = "select $selectPm from $fromPm where $wherePm";
                $tmpSelect = "$selectPeople UNION $selectDevice UNION $selectPm";
                $bindArray[":Now"] = $this->share->util->getNow();
                $totalSql = "select count(*) from ($tmpSelect) as tmp_table";
                $selectSql = "select * from ($tmpSelect) as tmp_table $orderBySql limit $offset,$rows";
                break;
        }
        $total = $this->execute($totalSql, $bindArray)[0]["count(*)"];
        $data = $this->execute($selectSql, $bindArray);
        //数据解密
        foreach ($data as &$item) {
            if ($item['Source'] !== 'Device') {
                $item = $this->dataArrDecode($item, ['Name']);
            }
        }
        unset($item);
        return [$data, intval($total)];
    }

    /**
     * @description: pm获取办公用户信息列表
     * @param {string} $projectId 项目id
     * @return $userData 用户列表
     * @author: cj 2023-03-10 15:14:32 V6.5.4
     * @lastEditors: cj 2023-03-10 15:14:32 V6.5.4
     */
    public function getOfficeUserForInsPM($bindArray, $searchArray)
    {
        // PM查找的账号状态
        $NORMAL = 1;
        $INACTIVATED = 2;
        $EXPIRING = 3;
        $EXPIRED = 4;
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $active = $bindArray[':Active'];
        $departmentId = $bindArray[':DepartmentId'];
        $role = $bindArray[':Role'];
        $status = $bindArray[':Status'];
        unset($bindArray[':Active']);
        unset($bindArray[':DepartmentId']);
        unset($bindArray[':Role']);
        unset($bindArray[':Status']);
        $where = '';
        switch ($searchKey) {
            case 'Name':
                if ($searchValue != "" && !is_null($searchValue)) {
                    $where = "AND md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                }
                break;
            case 'Email':
                if ($searchValue != "" && !is_null($searchValue)) {
                    $where = "AND md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                }
                break;
            case 'MobileNumber':
                if ($searchValue != "" && !is_null($searchValue)) {
                    $where = "AND md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue)";
                    $bindArray[":searchValue"] = "%$searchValue%";
                }
                break;
            case 'EmployeeID':
                $where = "AND PO.EmployeeID LIKE :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            default:
                break;
        }

        switch ($active) {
            case $NORMAL:
                $where .= " AND P.Active = 1 AND P.ExpireTime > :ExpireTime";
                $bindArray[":ExpireTime"] = $this->share->util->getNow();
                break;
            case $INACTIVATED:
                $where .= " AND P.Active = 0";
                break;
            case $EXPIRING:
                $where .= " AND P.Active = 1 AND P.ExpireTime < :ExpireTIme AND P.ExpireTime > :ExpireTIme2";
                $bindArray[":ExpireTIme"] = date('Y-m-d H:i:s', strtotime('+7 day'));
                $bindArray[":ExpireTIme2"] = $this->share->util->getNow();
                break;
            case $EXPIRED:
                $where .= " and P.Active = 1 and P.ExpireTime < :ExpireTIme";
                $bindArray[":ExpireTIme"] = $this->share->util->getNow();
                break;
        }

        if ($departmentId != 'all') {
            $where .= " AND P.UnitID = :UnitID";
            $bindArray[":UnitID"] = $departmentId;
        }

        if ($status != 'all' && $status != null) {
            $where .= " AND P.Initialization = :Initialization";
            $bindArray[":Initialization"] = $status;
        }

        if ($role != 'all') {
            $where .= " AND P.Role = :Role";
            $bindArray[":Role"] = $role;
        } else {
            $officeRole = implode(',', OFFROLE);
            $where .= " AND P.Role in ($officeRole)";
        }

        $orderBySql = ' order by P.ID desc';
        if (in_array($sortField, ['CreateTime', 'Name', 'EmployeeID']) && in_array($sort, SORT_MODE)){
            if (in_array($sortField, ['CreateTime'])) {
                $sortField = "P.$sortField";
            }
            if ($sortField == 'Name') {
                $sortField = "AKCSMapping.NameMapping.DeColumn";
            }
            if (in_array($sortField, ['EmployeeID'])) {
                $sortField = "PO.$sortField";
            }
            $orderBySql = " order by $sortField $sort,P.ID desc";
        }

        $personalAccountTable = PROXY_TABLES["personalAccount"];
        $communityUnitTable = PROXY_TABLES["communityUnit"];
        $personalAccountOfficeInfoTable = PROXY_TABLES["personalAccountOfficeInfo"];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        $queryResult = "select P.ID,P.UUID, P.ParentID, P.Name, P.Role, PU.Email, PU.MobileNumber, P.CreateTime, PO.EmployeeID, CU.UnitName as DepartmentName, 
        P.Initialization, P.Active, P.ExpireTime, P.PhoneCode, P.Phone, P.Phone2, P.Phone3";
        $table = "$personalAccountTable P join $communityUnitTable CU on P.UnitID = CU.ID join $personalAccountOfficeInfoTable PO on PO.PersonalAccountUUID = P.UUID
        left join $personalAccountUserInfoTable PU on PU.UUID = P.UserInfoUUID 
        left join AKCSMapping.NameMapping on AKCSMapping.NameMapping.EnColumn = P.Name and AKCSMapping.NameMapping.EnColumnMd5 = md5(P.Name) ";
        $where = "P.ParentID = :ParentID $where";
        $total = $this->execute("select count(*) from $table where $where", $bindArray)[0]["count(*)"];
        $userData = $this->execute("$queryResult from $table where $where $orderBySql limit $offset,$rows", $bindArray);
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email', 'MobileNumber' => 'Phone', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return [$userData, intval($total)];
    }

    /**
     * @description: 办公获取已激活列表
     * @param {string} $officeUUID 办公uuid
     * @param {*} $row
     * @param {*} $offset
     * @return $userData 用户列表
     * @author: cj 2023-03-13 14:49:13 V6.5.4
     * @lastEditors: cj 2023-03-13 14:49:13 V6.5.4
     */
    public function getOfficeActiveUsers($officeUUID, $row, $offset)
    {
        $data = $this->where([['ParentUUID', $officeUUID], ['ExpireTime', DEFAULTEXPIRETIME, '<'],
            ['Active', 1], ['Special', 0]])->whereIn('Role', OFFROLE)->orderBy('ExpireTime')->limit("$offset, $row")
            ->select('Name,UserInfoUUID,ID,UUID,ExpireTime,UnitID');
        if (count($data) === 0) {
            return [];
        }
        $unitIdArr = array_unique(array_column($data, 'UnitID', null));
        $departmentName = $this->dao->communityUnit->selectByIDWArray($unitIdArr, 'UnitName, ID');
        $departmentName = array_column($departmentName, 'UnitName', 'ID');

        $userInfoUUIDArr = array_unique(array_column($data, 'UserInfoUUID', null));
        $userInfoEmail = $this->dao->personalAccountUserInfo->selectByUUIDWArray($userInfoUUIDArr, 'Email, UUID');
        $userInfoEmail = array_column($userInfoEmail, 'Email', 'UUID');

        $companyName = [];
        foreach ($data as &$val) {
            $val['DepartmentName'] = isset($departmentName[$val['UnitID']]) ? $departmentName[$val['UnitID']] : '';
            $val['Email'] = $userInfoEmail[$val['UserInfoUUID']];
            $val['GroupName'] = '';
            $groupUUIDs = $this->dao->officePersonnelGroup->selectByKey('PersonalAccountUUID', $val['UUID'], 'OfficeGroupUUID');
            if (!empty($groupUUIDs)) {
                foreach ($groupUUIDs as $item) {
                    $groupUUID = $item['OfficeGroupUUID'];
                    $groupName = isset($companyName[$groupUUID]) ? $companyName[$groupUUID] : $this->dao->officeGroup->selectByKey('UUID', $groupUUID)[0]['Name'];
                    $companyName[$groupUUID] = $groupName;
                    $val['GroupName'] .= $groupName . ',';
                }
                $val['GroupName'] = substr($val['GroupName'], 0, -1);
            }
        }
        unset($val);
        return $data;
    }

    /**
     * @description: 办公获取未激活用户列表
     * @param {string} $officeUUID 办公uuid
     * @param {*} $row
     * @param {*} $offset
     * @return $userData 用户列表
     * @author: cj 2023-03-13 14:49:13 V6.5.4
     * @lastEditors: cj 2023-03-13 14:49:13 V6.5.4
     */
    public function getOfficeNoActiveUsers($officeUUID, $row, $offset)
    {
        $data = $this->where([['ParentUUID', $officeUUID],
            ['Active', 0], ['Special', 0]])->whereIn('Role', OFFROLE)->limit("$offset, $row")
            ->select('Name,UserInfoUUID,UUID,ID,ExpireTime,UnitID');
        if (count($data) === 0) {
            return [];
        }
        $unitIdArr = array_unique(array_column($data, 'UnitID', null));
        $departmentName = $this->dao->communityUnit->selectByIDWArray($unitIdArr, 'UnitName, ID');
        $departmentName = array_column($departmentName, 'UnitName', 'ID');

        $userInfoUUIDArr = array_unique(array_column($data, 'UserInfoUUID', null));
        $userInfoEmail = $this->dao->personalAccountUserInfo->selectByUUIDWArray($userInfoUUIDArr, 'Email, UUID');
        $userInfoEmail = array_column($userInfoEmail, 'Email', 'UUID');

        $companyName = [];
        foreach ($data as &$val) {
            $val['DepartmentName'] = isset($departmentName[$val['UnitID']]) ? $departmentName[$val['UnitID']] : '';
            $val['Email'] = $userInfoEmail[$val['UserInfoUUID']];
            $val['GroupName'] = '';
            $groupUUIDs = $this->dao->officePersonnelGroup->selectByKey('PersonalAccountUUID', $val['UUID'], 'OfficeGroupUUID');
            if (!empty($groupUUIDs)) {
                foreach ($groupUUIDs as $item) {
                    $groupUUID = $item['OfficeGroupUUID'];
                    $groupName = isset($companyName[$groupUUID]) ? $companyName[$groupUUID] : $this->dao->officeGroup->selectByKey('UUID', $groupUUID)[0]['Name'];
                    $companyName[$groupUUID] = $groupName;
                    $val['GroupName'] .= $groupName . ',';
                }
                $val['GroupName'] = substr($val['GroupName'], 0, -1);
            }
        }
        unset($val);
        return $data;
    }



    /*
     *@description 获取办公接收消息用户列表
     *<AUTHOR> 2023-05-10 17:51:23 V6.6.0
     *@lastEditor cj 2023-05-10 17:51:23 V6.6.0
     *@param {*} department 部门ID|all
     *@param {*} key查找的Value
     *@param {*} projectId 办公ID
     *@return $userData 用户列表
     */
    public function getOfficeReceiverListForMessage($department, $key, $projectId)
    {
        $searchArray = [':ParentID' => $projectId];
        if ($department != 'all') {
            $where = 'and P.UnitID = :Department';
            $searchArray[':Department'] = $department;
        }
        $searchArray[':Key'] = "%$key%";

        $personalAccountTable = PROXY_TABLES["personalAccount"];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        $communityUnitTable = PROXY_TABLES['communityUnit'];
        $officeRole = implode(',', OFFROLE);

        $whereMapping = "";
        if ($key !== '' && !is_null($key)) {
            $whereMapping = " and (P.Account like :Key or 
                            md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key) or 
                            md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key))";
        }

        $userData = $this->execute("select P.Account,PU.Email,P.Name,P.UnitID,CU.UnitName as DepartmentName from $personalAccountTable P
        left join $personalAccountUserInfoTable PU on P.UserInfoUUID = PU.UUID left join $communityUnitTable CU on P.UnitID = CU.ID where
        P.Role in ($officeRole) and P.ParentID = :ParentID $where $whereMapping", $searchArray);
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Email', 'Name']);
        }
        return $userData;
    }



    /**
     * @description: 单住户获取主账号列表
     * @param {array} $bindArray sql查询绑定的参数数组
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return $userData 用户列表
     * @author: cj 2023-03-13 14:49:13 V6.5.4
     * @lastEditors: cj 2023-03-13 14:49:13 V6.5.4
     */
    public function getSingleMainList($bindArray, $searchArray)
    {
        list($offset, $row, $searchKey, $searchValue) = $searchArray;
        if ($searchValue != '' && !is_null($searchValue) && in_array($searchKey, ['Sip', 'Email', 'MobileNumber', 'Name'])) {
            // Sip检索需要检索Sip群主号和主账号
            if ($searchKey === 'Sip') {
                $sip = $searchValue;
                $accountSql = " in (select Account from SipGroup2 where SipGroup like :SipGroup 
                union select Account from PersonalAccount where Account like :SipGroup and Role = 10)";
                $userData = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE], ['Account', [$accountSql, [':SipGroup' => "%$sip%"]], 'origin']], [], 0, [
                    'orderBy' => 'A.ID DESC', 'limit' => "$offset, $row"], 'A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Name,A.FirstName,A.LastName,
                    A.SipAccount,A.Address,A.Active,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.CreateTime,A.PhoneStatus,A.EnableSmartHome,
                    A.RoomNumber,A.CustomizeForm,A.ExpireTime,A.TimeZone,A.PhoneExpireTime,A.Switch,A.UserInfoUUID,AUF.Email,AUF.MobileNumber');
                $total = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE], ['Account', [$accountSql, [':SipGroup' => "%$sip%"]], 'origin']], [], 0, [], 'count(*)')[0]['count(*)'];
            } elseif ($searchKey === 'Email' || $searchKey === 'MobileNumber') {
                $userData = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE]], [[$searchKey, $searchValue, '%']], 0, [
                    'orderBy' => 'A.ID DESC', 'limit' => "$offset, $row"], 'A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Name,A.FirstName,A.LastName,
                    A.SipAccount,A.Address,A.Active,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.CreateTime,A.PhoneStatus,A.EnableSmartHome,
                    A.RoomNumber,A.CustomizeForm,A.ExpireTime,A.TimeZone,A.PhoneExpireTime,A.Switch,A.UserInfoUUID,AUF.Email,AUF.MobileNumber');
                $total = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE]], [[$searchKey, $searchValue, '%']], 0, [], 'count(*)')[0]['count(*)'];
            } else {
                $userData = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE], [$searchKey, $searchValue, '%']], [], 0, [
                    'orderBy' => 'A.ID DESC', 'limit' => "$offset, $row"], 'A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Name,A.FirstName,A.LastName,
                    A.SipAccount,A.Address,A.Active,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.CreateTime,A.PhoneStatus,A.EnableSmartHome,
                    A.RoomNumber,A.CustomizeForm,A.ExpireTime,A.TimeZone,A.PhoneExpireTime,A.Switch,A.UserInfoUUID,AUF.Email,AUF.MobileNumber');
                $total = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE], [$searchKey, $searchValue, '%']], [], 0, [], 'count(*)')[0]['count(*)'];
            }
        } else {
            $userData = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE]], [], 0, [
                'orderBy' => 'A.ID DESC', 'limit' => "$offset, $row"], 'A.ID,A.UUID,A.EnableIpDirect,A.Account,A.Name,A.FirstName,A.LastName,
                A.SipAccount,A.Address,A.Active,A.Phone,A.Phone2,A.Phone3,A.PhoneCode,A.CreateTime,A.PhoneStatus,A.EnableSmartHome,
                A.RoomNumber,A.CustomizeForm,A.ExpireTime,A.TimeZone,A.PhoneExpireTime,A.Switch,A.UserInfoUUID,AUF.Email,AUF.MobileNumber');
            $total = $this->getUserListByArray([['ParentUUID', $bindArray['ParentUUID']], ['Role', PERENDMROLE]], [], 0, [], 'count(*)')[0]['count(*)'];
        }
        if ($total === '0') {
            return [[], 0];
        }
        $accounts = array_column($userData, 'Account', null);
        $sipGroupArr = array_column($this->dao->sipGroup2->selectByAccountWArray($accounts, 'SipGroup,Account'), 'SipGroup', 'Account');
        foreach ($userData as &$val) {
            $val['SipGroup'] = $sipGroupArr[$val['Account']];
            $val['SubCount'] = intval($this->selectByKey('ParentUUID', $val['UUID'], 'count(*)')[0]['count(*)']);
        }
        unset($val);
        return [$userData, intval($total)];
    }

    /**
     * @description: 单住户获取从账号列表
     * @param {array} $id 单住户主账号ID
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return $userData 用户列表
     * @author: cj 2023-03-14 10:14:36 V6.5.4
     * @lastEditors: cj 2023-03-14 14:49:36 V6.5.4
     */
    public function getSingleSubListForIns($id, $searchArray)
    {
        list($offset, $row) = $searchArray;
        $userData = $this->where([['ParentID', $id], ['Role', PERENDSROLE]])->orderBy('ID desc')->limit("$offset,$row")
        ->select('ID,UUID,Name,Role,FirstName,LastName,CreateTime,ExpireTime,Active,Phone,PhoneCode,UserInfoUUID');
        $total = $this->where([['ParentID', $id], ['Role', PERENDSROLE]])->orderBy('ID desc')
        ->select('count(*)')[0]['count(*)'];
        if ($total > 0) {
            $userInfoUUIDs = array_column($userData, 'UserInfoUUID', null);
            $userInfo = $this->dao->personalAccountUserInfo->selectByUUIDWArray($userInfoUUIDs, 'UUID,Email,MobileNumber');
            $emailArr = array_column($userInfo, 'Email', 'UUID');
            $mobileArr = array_column($userInfo, 'MobileNumber', 'UUID');
            foreach ($userData as &$val) {
                $val = \share\util\setOriginTime($val);
                $val['OriginActive'] = $val['Active'];
                $val['Email'] = $emailArr[$val['UserInfoUUID']];
                $val['MobileNumber'] = $mobileArr[$val['UserInfoUUID']];
                $val['IsMulti'] = $this->selectByKey('UserInfoUUID', $val['UserInfoUUID'], 'count(*)')[0]['count(*)'] > 1 ? 1:0;
            }
            unset($val);
        }
        return [$userData, intval($total)];
    }

    /**
     * @description: 获取super/dis的终端用户列表
     * @param {string} $role 角色筛选 familyMaster|familyMember
     * @param {string} $disId dis的ID，为空时指super
     * @param {array} $searchArray 包括$offset 偏移量 $row 行数 $sortField 排序字段$sort 排序方式等
     * @return $userData 用户列表
     * @author: cj 2023-03-14 10:14:36 V6.5.4
     * @lastEditors: cj 2023-03-14 14:49:36 V6.5.4
     */
    public function getSingleListForManage($role, $disId, $searchArray, $supSearch = null)
    {
        $tableAccount = PROXY_TABLES['account'];
        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        $tablePersonalAccountUserInfo = PROXY_TABLES['personalAccountUserInfo'];
        $where = '';
        $where2 = '';
        $where3 = '';
        $bindArray = [];
        list($offset, $row, $searchKey, $searchValue) = $searchArray;
        $mainJoinSql = $subJoinSql = $joinField = [];
        $joinPU = $joinC = false;
        if ($searchValue !== '' && !is_null($searchValue)) {
            switch ($searchKey) {
                case 'Sip':
                    $where .= ' AND A.SipAccount LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'Email':
                    $where .= ' AND md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = $subJoinSql[] = "join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'MobileNumber':
                    $where .= ' AND md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = $subJoinSql[] = "join $tablePersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID";
                    $joinField[] = 'PU.Email,PU.MobileNumber';
                    $joinPU = true;
                    break;
                case 'Name':
                    $where .= ' AND md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'Install':
                    $where .= ' AND C.Account LIKE :searchValue';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                case 'FamilyMaster':
                    if ($searchValue !== '') {
                        $where2 = ' AND md5(D.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :searchValue)';
                        // 1 = 2是为了不显示主账号 FamilyMaster查询的时候
                        $where3 = ' AND 1 = 2';
                        $bindArray[':searchValue'] = "%$searchValue%";
                    }
                    break;
                case 'Phone':
                    $where .= ' AND (md5(A.Phone) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue) 
                    or md5(A.Phone2) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue) 
                    or md5(A.Phone3) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :searchValue))';
                    $bindArray[':searchValue'] = "%$searchValue%";
                    break;
                case 'SubDis':
                    list($insUUIDs, $insBindArray) = $this->dao->account->getInsSearchValueForSubDis($disId, $searchValue);
                    if ($insUUIDs === '') {
                        return [[], 0];
                    }
                    $where .= " And C.UUID in ($insUUIDs)";
                    $bindArray = array_merge($bindArray, $insBindArray);
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                    break;
                default:
                    break;
            }
        }
        if (!empty($disId)) {
            $disData = $this->dao->account->selectByID($disId, 'Grade,UUID')[0];
            if (intval($disData['Grade']) === AREAGRADE) {
                $where .= ' AND B.ParentID = :ParentID';
                $bindArray[':ParentID'] = $disId;
                $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
                $joinC = true;
            } else {
                // sub dis
                $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID', $disData['UUID'], 'InstallerUUID');
                if (count($insData) !== 0) {
                    $insUUIDs = array_column($insData, 'InstallerUUID');
                    list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
                    $where .= " AND C.UUID in ($uuidBindStr)";
                    $bindArray = array_merge($bindArray, $uuidBindArray);
                    $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
                    $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
                    $joinC = true;
                } else {
                    return [[], 0];
                }
            }
        }
        // v6.8.0 增加参数DisUUID, SubDisUUID,InsUUID,Status，ExpireTimeBegin，ExpireTimeEnd,PremiumPlan
        // where3是主账号的条件,where2是从账号的条件
        if (isset($supSearch['DisUUID'])) {
            $where .= " AND C.ParentUUID =:DisUUID";
            $bindArray[":DisUUID"] = $supSearch['DisUUID'];
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['SubDisUUID'])) {
            $insData = $this->dao->subDisMngList->selectByKey('DistributorUUID',$supSearch['SubDisUUID'], 'InstallerUUID');
            if (count($insData) === 0) {
                return [[], 0];
            }
            $insUUIDs = array_column($insData, 'InstallerUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($insUUIDs);
            $where .= " AND C.UUID in ($uuidBindStr)";
            $bindArray = array_merge($bindArray, $uuidBindArray);
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }
        if (isset($supSearch['InsUUID'])) {
            $where .= " AND C.UUID =:InsUUID";
            $bindArray[":InsUUID"] = $supSearch['InsUUID'];
            $mainJoinSql[] = "JOIN $tableAccount B on A.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $subJoinSql[] = "JOIN $tableAccount B ON D.ParentUUID = B.UUID JOIN $tableAccount C on B.ManageGroup = C.ID";
            $joinField[] = "C.Account as Install,C.UUID as InstallerUUID";
            $joinC = true;
        }

        if (isset($supSearch['Active'])) {
            switch ($supSearch['Active']) {
                case 0: //未激活
                    $where .= " AND A.Active = 0 ";
                    break;
                case 1: //正常
                    $where .= " AND A.Active = 1 AND A.ExpireTime > :NowTime ";
                    $bindArray[":NowTime"] = $this->share->util->getNow();
                    break;
                case 2: //激活已过期
                    $where .= " AND A.Active = 1 AND A.ExpireTime <= :NowTime ";
                    $bindArray[":NowTime"] = $this->share->util->getNow();
                    break;
            }
        }
        //高级功能过期时间
        if (isset($supSearch['ExpireTimeBegin'])) {
            $where .= " AND A.PhoneExpireTime >= :ExpireTimeBegin AND (A.Switch & 2)=2";
            $bindArray[":ExpireTimeBegin"] = $supSearch['ExpireTimeBegin'];
        }
        if (isset($supSearch['ExpireTimeEnd'])) {
            $where .= " AND A.PhoneExpireTime <= :ExpireTimeEnd AND A.Active !=0 AND (A.Switch & 2)=2";
            $bindArray[":ExpireTimeEnd"] = $supSearch['ExpireTimeEnd'];
        }
        if (isset($supSearch['PremiumPlan'])) {
            switch ($supSearch['PremiumPlan']) {
                case 0: //off
                    $where3 .= " AND (A.Switch & 2)=0 ";
                    $where2 .= " AND (D.Switch & 2)=0 ";
                    break;
                case 1: //Normal
                    $where3 .= " AND (A.Switch & 2)=2 AND A.PhoneExpireTime > :Now";
                    $where2 .= " AND (D.Switch & 2)=2 AND D.PhoneExpireTime > :Now";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    break;
                case 2: //Expired
                    $where3 .= " AND (A.Switch & 2)=2 AND A.PhoneExpireTime <=:Now And A.PhoneExpireTime != :DefaultPhoneExpireTime";
                    $where2 .= " AND (D.Switch & 2)=2 AND D.PhoneExpireTime <=:Now And D.PhoneExpireTime != :DefaultPhoneExpireTime";
                    $bindArray[":Now"] = $this->share->util->getNow();
                    $bindArray[":DefaultPhoneExpireTime"] = DEFAULTPHONEEXPIRETIME;
                    break;
                case 3: //Inactivated
                    $where3 .= " AND (A.Switch & 2)=2 AND A.PhoneExpireTime = :DefaultPhoneExpireTime";
                    $where2 .= " AND (D.Switch & 2)=2 AND D.PhoneExpireTime = :DefaultPhoneExpireTime";
                    $bindArray[":DefaultPhoneExpireTime"] = DEFAULTPHONEEXPIRETIME;
                    break;
            }
        }

        // 7.1.1 增加 createTime搜索项
        if (isset($supSearch['CreateBeginTime'])) {
            $where .= " AND A.CreateTime >= :CreateBeginTime";
            $bindArray[":CreateBeginTime"] = $supSearch['CreateBeginTime'];
        }
        if (isset($supSearch['CreateEndTime'])) {
            $where .= " AND A.CreateTime <= :CreateEndTime";
            $bindArray[":CreateEndTime"] = $supSearch['CreateEndTime'];
        }

        $mainJoinSql = join(' ', array_unique($mainJoinSql));
        $subJoinSql = join(' ', array_unique($subJoinSql));
        $joinField = join(',', array_unique($joinField));
        if (!empty($joinField)) {
            $joinField = ',' . $joinField;
        }


        $title = "select A.ID,A.Active,A.SipAccount,A.Name,A.Role,A.Address,A.Phone,A.Phone2,A.Phone3,A.CreateTime,A.ExpireTime $joinField";
        $mainUserTable = "$tablePersonalAccount A $mainJoinSql";
        $subUserTable = "$tablePersonalAccount A JOIN $tablePersonalAccount D on A.ParentID = D.ID $subJoinSql";

        $sql = "$title,A.Switch,A.PhoneExpireTime,'--' as FamilyMaster,A.UserInfoUUID,A.ParentUUID as ProjectUUID from $mainUserTable where A.Role = " . PERENDMROLE . " $where $where3 union all $title,D.Switch,D.PhoneExpireTime,D.Name as FamilyMaster,A.UserInfoUUID,D.ParentUUID as ProjectUUID 
        from $subUserTable where A.Role = " . PERENDSROLE . " $where $where2";
        $countSql = "select SUM(row_count) as total from (select count(*) as row_count from $mainUserTable where A.Role = " . PERENDMROLE . " $where $where3 union all
        select count(*) as row_count from $subUserTable where A.Role = " . PERENDSROLE . " $where $where2) as Counts";
        switch ($role) {
            case 'familyMaster':
                if ($where3 !== '') {
                    return [[], 0];
                }
                $sql = "$title,A.Switch,'--' as FamilyMaster,A.UserInfoUUID,A.ParentUUID as ProjectUUID from $mainUserTable where A.Role = " . PERENDMROLE . " $where $where3";
                $countSql = "select count(*) as total from $mainUserTable where A.Role = " . PERENDMROLE . " $where $where3";
                break;
            case 'familyMember':
                $sql = "$title,D.Switch,D.Name as FamilyMaster,A.UserInfoUUID,D.ParentUUID as ProjectUUID from $subUserTable where A.Role = " . PERENDSROLE . " $where $where2";
                $countSql = "select count(*) as total from $subUserTable where A.Role = " . PERENDSROLE . " $where $where2";
                break;
            default:
                break;
        }

        $total = intval($this->execute($countSql, $bindArray)[0]['total']);
        $userData = $this->execute("$sql order by ID DESC limit $offset,$row", $bindArray);
        // 有未连表的需要的字段，进行获取补充字段
        if (!$joinPU && !empty($userData)) {
            $userInfoUUIDs = array_column($userData, 'UserInfoUUID');
            //Email、Mobile字段不进行自动解密，下面会统一解密
            $userInfos = $this->dao->personalAccountUserInfo->selectByArray([['UUID', $userInfoUUIDs]], 'UUID,Email,MobileNumber', false, false);
            $userInfos = array_column($userInfos, null, 'UUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Email'] = $userInfos[$data['UserInfoUUID']]['Email'];
                $userData[$key]['MobileNumber'] = $userInfos[$data['UserInfoUUID']]['MobileNumber'];
            }
        }
        if (!$joinC && !empty($userData)) {
            $projectUUIDs = array_column($userData, 'ProjectUUID');
            list($uuidBindStr, $uuidBindArray) = $this->share->util->getImplodeData($projectUUIDs);
            $sql = "select Ins.Account as Install,Ins.UUID as InstallerUUID, Project.UUID as ProjectUUID from $tableAccount Project JOIN $tableAccount Ins on Project.ManageGroup = Ins.ID where Project.UUID in ($uuidBindStr)";
            $projectInfos = $this->execute($sql, $uuidBindArray);
            $projectInfos = array_column($projectInfos, null, 'ProjectUUID');
            foreach ($userData as $key => $data) {
                $userData[$key]['Install'] = $projectInfos[$data['ProjectUUID']]['Install'];
                $userData[$key]['InstallerUUID'] = $projectInfos[$data['ProjectUUID']]['InstallerUUID'];
            }
        }
        foreach ($userData as $key => $data) {
            $confusionField = ['Name', 'Email', 'MobileNumber' => 'Phone', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone'];
            if ($data['Role'] == PERENDSROLE) {
                $confusionField['FamilyMaster'] = 'Name';
            }
            $userData[$key] = $this->dataArrDecode($data, $confusionField);
        }
        return [$userData, $total];
    }

    public function getAllRoomByProjectUuid($projectUUID)
    {
        $data = $this->selectByKey('ParentUUID', $projectUUID);
        $roomIds = array_unique(array_column($data, 'RoomID'));
        $rooms = $this->dao->communityRoom->selectByIDWArray($roomIds);
        $rooms = array_column($rooms, 'RoomName', 'ID');
        foreach ($data as &$val) {
            $val['Apt'] = $rooms[$val['RoomID']];
            unset($val['Passwd']);
        }
        unset($val);

        return $data;
    }

    public function getAllResidentByProjectUuid($projectUUID)
    {
        $userData = $this->execute("select * from {$this->table} where ParentUUID=:ParentUUID and Special=0
        union select A.* from {$this->table} A join {$this->table} B on A.ParentUUID=B.UUID where B.ParentUUID=:ParentUUID", [':ParentUUID'=>$projectUUID]);
    
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, $this->confusionField);
        }

        return $userData;
    }

    /**
     * @description:主用户的信息与配置
     * @author:lwj 2023-04-11 14:13:58 V6.6
     * @lastEditor:lwj 2023-04-11 14:13:58 V6.6
     * @param {string} $id 主用户ID
     * @return mixed
     * @throws \Exception
     */
    // public function getMainUserInfoForApp($id)
    // {
    //     $tablePersonalAccount = PROXY_TABLES['personalAccount'];
    //     $tablePersonalAccountCnf = PROXY_TABLES['personalAccountCnf'];
    //     $tableCommunityRoom = PROXY_TABLES['communityRoom'];
    //     $sql = "select P.FirstName,P.LastName,P.Name,P.SipAccount,P.Phone,P.Phone2,P.Phone3,P.PhoneCode,P.Email,P.TimeZone,
    //         R.RoomName,PF.CallType  
    //         from $tablePersonalAccount P
    //         join $tablePersonalAccountCnf PF on P.Account = PF.Account 
    //         left join $tableCommunityRoom R on P.RoomID = R.ID
    //         where P.ID=:ID";

    //     $userData = $this->execute($sql, [':ID' => $id])[0];
    //     foreach ($userData as $key => $data) {
    //         $userData[$key] = $this->dataArrDecode($data);
    //     }
    //     return $userData;
    // }

    /**
     * @description: 根据条件查询personalAccount及personalAccountUserInfo(或accountUserInfo)的信息
     * @param {array} $searchArray 和selectByArray的数组格式一样，personalAccount表的搜索条件
     * @param {array} $searchArrayUI 和selectByArray的数组格式一样，accountUserInfo或personalAccountUserInfo表的搜索条件
     * @param {int} $includePmApp 是否包含已关联的PmApp数据的查询 0-否 1-是
     * @param {array} $options 可选项，orderby和limit参数，仅针对$includePmApp为0时有效，格式为：['orderBy' => 'A.ID DESC,AUF.ID DESC', 'limit' => '10,20']或['orderBy' => 'A.ID DESC']
     * @param {string or array} $fields 可选项，查询的字段名，string: 如果是重复字段需要加上表别名，Account 为A，AccountUserInfo为 AUF，例："A.ID,AUF.Email"
     * 注：如果为string时，需要保证结果能够正常查出来（例如查询MobileNumber和PhoneCode，只有PersonalAccountUserInfo表有），此时如果有关联查询AccountInfo则会报错，需要改为数组写法
     * ["'' as MobileNumber,'' as PhoneCode", "AUF.MobileNumber as MobileNumber, AUF.PhoneCode as PhoneCode"],保证union的结果格式一致。第一个值为AccountUserInfo查询的字段，第二个值为PersonalAccountUserInfo的查询字段
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/5/9 16:48 V6.6
     * @lastEditors: csc 2023/5/9 16:48 V6.6
     */
    public function getUserListByArray($searchArray, $searchArrayUI, $includePmApp, $options = [], $fields = '')
    {
        //关联accountUserInfo表查询的条件
        $condition1 = $conditionBindArray1 = [];
        //关联personalAccountUserInfo表查询的条件
        $condition2 = $conditionBindArray2 = [];

        //判断$searchArray是否为空
        if (!empty($searchArray)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArray, true, 'A');
            if ($includePmApp) {
                $condition1 = array_merge($condition1, $conditionMerge);
                $conditionBindArray1 = array_merge($conditionBindArray1, $conditionBindArrayMerge);
            }
            $condition2 = array_merge($condition2, $conditionMerge);
            $conditionBindArray2 = array_merge($conditionBindArray2, $conditionBindArrayMerge);
        }

        //判断$searchArrayUI是否为空
        if (!empty($searchArrayUI)) {
            $bindCount = 0;
            if ($includePmApp) {
                list($conditionMerge, $conditionBindArrayMerge, $bindCount) = $this->dao->accountUserInfo->makeSearchCondition($searchArrayUI, true, 'AUF');
                $condition1 = array_merge($condition1, $conditionMerge);
                $conditionBindArray1 = array_merge($conditionBindArray1, $conditionBindArrayMerge);
            }

            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->personalAccountUserInfo->makeSearchCondition($searchArrayUI, true, 'AUF', $bindCount);
            $condition2 = array_merge($condition2, $conditionMerge);
            $conditionBindArray2 = array_merge($conditionBindArray2, $conditionBindArrayMerge);
        }

        if (empty($fields)) {
            //关联查询 --personalAccountUserInfo 查询的字段顺序不能反，因为要用AUF的Email覆盖PersonalAccount表的Email等
            $fields1 = "A.*, AUF.Email as Email, '' as MobileNumber, AUF.Passwd as Passwd, 
       AUF.AppMainUserAccount, AUF.AppLastLoginUserAccount, AUF.ID as AUF_ID, AUF.UUID as AUF_UUID ";
            $fields2 = "A.*, AUF.Email as Email, AUF.MobileNumber as MobileNumber, AUF.Passwd as Passwd, 
       AUF.AppMainUserAccount, AUF.AppLastLoginUserAccount, AUF.ID as AUF_ID, AUF.UUID as AUF_UUID ";
        } else {
            if (is_array($fields)) {
                $fields1 = $fields[0];
                $fields2 = $fields[1];
            } else {
                $fields1 = $fields2 = $fields;
            }
        }

        if ($includePmApp) {
            //pm app信息存在AccountUserInfo中
            $sql1 = "select $fields1, 'AccountUserInfo' as 'FromTable' from PersonalAccount A 
                left join AccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.Role = 40 and AUF.UUID is not null";
            if (!empty($condition1)) {
                $condition1 = join(' and ', $condition1);
                $sql1 .= " and $condition1";
            }

            //pm app信息存在AccountUserInfo中
            $sql2 = "select $fields2, 'PersonalAccountUserInfo' as 'FromTable' from PersonalAccount A 
                left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.Role = 40 and AUF.UUID is not null";
            if (!empty($condition2)) {
                $condition = join(' and ', $condition2);
                $sql2 .= " and $condition";
            }

            //终端用户和空房间等其他非pm app数据
            $sql3 = "select $fields2, 'PersonalAccountUserInfo' as 'FromTable' from PersonalAccount A 
                left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.Role != 40";
            if (!empty($condition2)) {
                $condition = join(' and ', $condition2);
                $sql3 .= " and $condition";
            }

            $sql = "$sql1 UNION $sql2 UNION $sql3";
            $conditionBindArray = array_merge($conditionBindArray1, $conditionBindArray2);
        } else {
            //如果没有需要查AccountUserInfo的pm app，则只要查询PersonalAccountUserInfo即可
            $sql = "select $fields2, 'PersonalAccountUserInfo' as 'FromTable' from PersonalAccount A 
                left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID where A.Role != 40";
            if (!empty($condition2)) {
                $condition2 = join(' and ', $condition2);
                $sql .= " and $condition2";
            }

            $conditionBindArray = $conditionBindArray2;

            if (!empty($options)) {
                if (!empty($options['orderBy'])) {
                    $sql .= " order by " . $options['orderBy'];
                }
                if (!empty($options['limit'])) {
                    $sql .= " limit " . $options['limit'];
                }
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        //如果是统计条数，可以提前返回
        if (isset($res[0]['count(*)'])) {
            $count = array_sum(array_column($res, 'count(*)'));
            return [['count(*)' => $count]];
        }

        //解密
        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone', 'Email', 'MobileNumber' => 'Phone']);
            unset($res[$key]['FromTable']);
        }

        return $res;
    }

    /*
     *@description 获取某个项目下的所有主账户站点
     *<AUTHOR> 2023-05-11 10:36:40 V6.6.0
     *@lastEditor kxl 2023-05-11 10:36:40 V6.6.0
     *@return 
     */
    public function getAllMainUserForMessage($projectUuid, $key)
    {
        $whereMapping = "";
        if ($key !== '' && !is_null($key)) {
            $whereMapping = " and (md5(AU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :Key) 
        or md5(A.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Key))";
        }
        $sql = "select A.Account, A.Name, AU.Email, AU.MobileNumber from ".$this->table." A 
        join ".PROXY_TABLES['personalAccountUserInfo']." AU on AU.UUID = A.UserInfoUUID 
        where A.ParentUUID=:ParentUUID and Role=10 $whereMapping";
        $userData = $this->db->querySList($sql, [':ParentUUID'=>$projectUuid, ':Key'=>"%$key%"]);

        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email', 'MobileNumber' => 'Phone']);
        }

        return $userData;
    }

    /**
     * @description: 根据Email or Account or MobileNumber获取终端用户信息
     * @param {string} $user 终端用户的Email or Account or MobileNumber
     * @param {array} $role 角色数组
     * @return array
     * @throws \Exception
     * @author: csc 2023/5/16 11:51 V6.6
     * @lastEditors: csc 2023/5/16 11:51 V6.6
     */
    public function getUserInfoByAccount($user, $role = [])
    {
        $emails = $this->dao->personalAccountUserInfo->getEncolumnArrByDeColumn('Email', $user);
        $emails = $this->share->util->implodeWithQuotation($emails);

        $sql = "SELECT A.Account,AUF.Passwd,A.Active,A.ExpireTime,AUF.AppMainUserAccount,A.UUID FROM PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID
            WHERE AUF.Email in ($emails)";
        $bindArray = [];
        if (!empty($role)) {
            list($ids, $bindArray2) = $this->getEncodeCondition('Role', $role);
            $sql .= " and Role in ($ids)";
            $bindArray = array_merge($bindArray, $bindArray2);
        }
        $userData = $this->execute($sql, $bindArray);
        if (!empty($userData)) {
            return $userData;
        }

        $sql = "SELECT A.Account,AUF.Passwd,A.Active,A.ExpireTime,AUF.AppMainUserAccount,A.UUID FROM PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID
            WHERE A.Account = :USER";
        $bindArray = [
            ':USER' => $user,
        ];
        if (!empty($role)) {
            list($ids, $bindArray2) = $this->getEncodeCondition('Role', $role);
            $sql .= " and Role in ($ids)";
            $bindArray = array_merge($bindArray, $bindArray2);
        }
        $userData = $this->execute($sql, $bindArray);
        if (!empty($userData)) {
            return $userData;
        }

        $sql = "SELECT A.Account,AUF.Passwd,A.Active,A.ExpireTime,AUF.AppMainUserAccount,A.UUID FROM PersonalAccount A left join PersonalAccountUserInfo AUF on AUF.UUID = A.UserInfoUUID
            WHERE AUF.MobileNumber = :MOBILE_ENCODE";
        $bindArray = [
            ':MOBILE_ENCODE' => $this->dataEncode('MobileNumber', $user, 'PersonalAccountUserInfo'),
        ];
        if (!empty($role)) {
            list($ids, $bindArray2) = $this->getEncodeCondition('Role', $role);
            $sql .= " and Role in ($ids)";
            $bindArray = array_merge($bindArray, $bindArray2);
        }
        $userData = $this->execute($sql, $bindArray);
        return $userData;
    }

    /**
     * @description: 根据account获取community、dis名称、ins名称
     * @param {string} $account
     * @return array
     * @throws \Exception
     * @author: csc 2023/5/16 17:52 V6.6
     * @lastEditors: csc 2023/5/16 17:52 V6.6
     */
    public function getManagerByAccount($account)
    {
        $accountInfo = $this->selectByAccount($account, 'Role,ParentID')[0];

        $data = [];
        if ($accountInfo['Role'] == COMENDMROLE)
        {
            $data['CommunityID'] = $accountInfo['ParentID'];
        }
        else //从账号 再查主账号
        {
            $data['CommunityID'] = $this->selectByID($accountInfo['ParentID'], 'ParentID')[0]['ParentID'];
        }

        $sql = "SELECT A.Account as installer,B.Account as distributor FROM Account A join Account C on A.ID=C.ManageGroup 
    join Account B on B.ID = C.ParentID WHERE C.ID = :ID";
        $bindArray = [':ID' => $data['CommunityID']];
        $result = $this->execute($sql, $bindArray)[0];
        $data['Distributor'] = $result['distributor'];
        $data['Installer'] = $result['installer'];

        return $data;
    }

    /**
     * @description: 智能家居获取用户信息
     * @param {array} $uuids personalaccount表的uuid数组
     * @param {string} $type single:查询单住户 community：查询社区用户
     * @return mixed
     * @author: kxl 2023/5/22 14:55 V6.6
     * @lastEditors: kxl 2023/5/22 14:55 V6.6
     */
    public function getUsersForSmartHome($uuids, $type)
    {
        $type = $type === 'single' ? 'single' : 'community';

        $uuidWhere = [];
        $uuidBindArray = [];

        for ($i=0; $i <count($uuids); $i++) {
            $uuid = $uuids[$i];
            array_push($uuidWhere, ":UUID$i");
            $uuidBindArray[":UUID$i"] = $uuid;
        }

        $tablePersonalAccount = PROXY_TABLES['personalAccount'];
        if ($type === 'single') {
            $uuidBindArray[':Role'] = PERENDSROLE;
            $data = $this->db->querySList(
                'select UUID,ParentUUID as AptUUID,Name,SipAccount,PhoneCode,UserInfoUUID,Active,ExpireTime
                from '.$tablePersonalAccount.'
                where ParentUUID in ('.implode(',', $uuidWhere).') and Role=:Role
                union select UUID,UUID as AptUUID,Name,SipAccount,PhoneCode,UserInfoUUID,Active,ExpireTime
                from '.$tablePersonalAccount.'
                where UUID in ('.implode(',', $uuidWhere).')',
                $uuidBindArray
            );
        } else {
            $uuidBindArray[':Role'] = COMENDSROLE;
            $data = $this->db->querySList(
                'select UUID,ParentUUID as AptUUID,FirstName,LastName,SipAccount,PhoneCode,UserInfoUUID,Active,ExpireTime
                from '.$tablePersonalAccount.'
                where ParentUUID in ('.implode(',', $uuidWhere).') and Role=:Role
                union select UUID,UUID as AptUUID,FirstName,LastName,SipAccount,PhoneCode,UserInfoUUID,Active,ExpireTime
                from '.$tablePersonalAccount.'
                where UUID in ('.implode(',', $uuidWhere).')',
                $uuidBindArray
            );
        }

        // Email和MobileNumber另外查询
        foreach ($data as &$value) {
            // Active=0未激活，1已激活未过期，2已激活但过期
            $value['Active'] = intval($value['Active']);
            if ($value['Active'] === 1 && strtotime($value['ExpireTime']) < time()) {
                $value['Active'] = 2;
            }
            # code...
            $info = $this->dao->personalAccountUserInfo->selectByUUID($value['UserInfoUUID'], 'Email,MobileNumber');
            if (count($info) === 0) {
                $value['Email'] = null;
                $value['MobileNumber'] = null;
            } else {
                $value['Email'] = $info[0]['Email'];
                $value['MobileNumber'] = $info[0]['MobileNumber'];
            }
            $value = $this->dataArrDecode($value, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name']);
        }
        unset($value);

        return $data;
    }

    /**
     * @description:获取项目下主从账户列表，并且可根据用户名、用户邮箱、房间名、房间号进行搜索
     * @param: {type}
     * @param $active
     * @param $bindArray
     * @param $params
     * @return array
     * @author: shoubin.chen 2023-09-19 19:48:51 v6.7
     * @lastEditor: shoubin.chen 2023-09-19 19:48:51 v6.7
     */
    public function getProjectUserList($active, $params, $mainUserUUIDS)
    {
        $bindArray = [];
        if (empty($mainUserUUIDS)) {
            return [0, array()];
        }
        $uuids = "'" . implode("','", $mainUserUUIDS) . "'";

        $selectMain = "SELECT A.ID,A.UUID,A.Name,A.Active,A.Role,PU.Email,PU.MobileNumber,A.ExpireTime,A.ParentUUID,U.UnitName, R.Floor,R.RoomName,A.UnitID,A.RoomNumber,A.RoomID
		               FROM PersonalAccount A
		               LEFT JOIN PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID 
		               LEFT JOIN CommunityUnit U ON A.UnitID = U.ID 
                       JOIN CommunityRoom R ON R.ID = A.RoomID 
		               JOIN PersonalAccountCnf Pf ON A.Account = Pf.Account
		               WHERE  A.Role = 20 AND A.Special = 0 AND A.UUID IN($uuids)";

        $selectSub = "SELECT tmp_a.*,tmp_b.UnitName, tmp_b.Floor,tmp_b.RoomName,tmp_b.UnitID,tmp_b.RoomNumber,tmp_b.RoomID 
                      FROM (
		                    SELECT A.ID,A.UUID,A.Name,A.Active,A.Role,PU.Email,PU.MobileNumber,A.ExpireTime,A.ParentUUID
		                    from PersonalAccount A
		                    LEFT JOIN PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID 
		                    WHERE  A.Role = 21 AND A.Special = 0 AND A.ParentUUID IN($uuids)
		                    ) AS tmp_a
		              JOIN ($selectMain) AS tmp_b
		              ON tmp_a.ParentUUID = tmp_b.UUID";
        $where = ' 1=1 ';
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $params;
        //前端：0-未激活 1-正常  2-过期 3-将过期
        if ($active === '0') {  //未激活
            $where .= ' and Active = 0 ';
        } elseif ($active === '1') { //正常（不包含即将过期）
            $where .= ' and (Active = 1 and ExpireTime > :NormalExpireTime) ';
            $bindArray[':NormalExpireTime'] = date('Y-m-d H:i:s', strtotime('+7 day'));
        } elseif ($active === '2') { //过期
            $where .= ' and (ExpireTime < :Now and Active = 1) ';
            $bindArray[':Now'] = \share\util\getNow();
        } elseif ($active === '3') { //将过期
            $where .= ' and Active = 1 and ExpireTime BETWEEN :Now AND :ExpiringTime ';
            $bindArray[':Now'] = \share\util\getNow();
            $bindArray[':ExpiringTime'] = date('Y-m-d H:i:s', strtotime('+7 day'));
        }
        if ($searchValue != '' && !is_null($searchValue)) {
            //数据加密
            //Name用户名 Email用户邮箱 RoomNumber房间名 RoomName房间号
            $where .= " and (md5(Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue1) 
            OR md5(Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping where DeColumn like :SearchValue2) 
            OR RoomNumber LIKE :SearchValue3 OR RoomName LIKE :SearchValue4) ";
            $bindArray[':SearchValue1'] = "%$searchValue%";
            $bindArray[':SearchValue2'] = "%$searchValue%";
            $bindArray[':SearchValue3'] = "%$searchValue%";
            $bindArray[':SearchValue4'] = "%$searchValue%";
        }
        $tmpSelect = " ($selectMain UNION $selectSub) as tmp_c WHERE $where";
        $orderBySql = " order by ID desc ";
        $totalSql = "select count(*) from $tmpSelect";
        $total = $this->execute($totalSql, $bindArray)[0]["count(*)"];
        $selectSql = "select * from $tmpSelect  $orderBySql limit $offset,$row";
        $list = $this->execute($selectSql, $bindArray);
        //数据解密
        foreach ($list as &$item) {
            $item = $this->dataArrDecode($item, ['Name', 'Email', 'MobileNumber' => 'Phone']);
        }
        unset($item);
        return [$total, $list];
    }

    /*
     *@description 获取项目下未激活/即将过期/过期的用户数目
     *<AUTHOR> 2023-09-20 17:09:05 V6.7.0
     *@lastEditor cj 2023-09-20 17:09:05 V6.7.0
     *@param {*} projectIds array
     *@param {*} projectType='community'|'office'|'single'
     *@return [$inactivatedResidents, $expiringResidents, $expiredResidents]
     */
    public function getUserDiffCount($projectIds, $projectType = 'community')
    {
        if (empty($projectIds)) {
            return [0, 0, 0];
        }
        list($idBindStr, $idBindArray) = $this->share->util->getImplodeData($projectIds);
        $where = " AND P.ParentID in ($idBindStr)";
        $expiringTime = date('Y-m-d H:i:s', strtotime('+7 day'));
        $nowTime = $this->share->util->getNow();
        $expiringBindArray = array_merge($idBindArray, [':ExpiringTime' => $expiringTime, ':NowTime' => $nowTime]);
        $expiredBindArray = array_merge($idBindArray, [':NowTime' => $nowTime]);
        $inactivatedResidents = 0;
        $expiringResidents = 0;
        $expiredResidents = 0;

        if ($projectType === 'community') {
            $pmAppRole = PMENDMROLE;
            $mainUserRole = COMENDMROLE;
            $subUserRole = COMENDSROLE;
            $sqlPmApp = "from {$this->table} P where P.Role = $pmAppRole $where";
            $sqlMaster = "from {$this->table} P where P.Role = $mainUserRole and P.Special = 0 $where";
            $sqlMember = "from {$this->table} P join {$this->table} P1 on P.UUID = P1.ParentUUID where P.Special = 0 and P1.Role = $subUserRole $where";
            // inactivated residents(包括pm app)
            $total = $this->execute(
                "select count(*) $sqlPmApp and P.Active = 0
                union all select count(*) $sqlMaster and P.Active = 0 
                union all select count(*) $sqlMember and P1.Active = 0",
                $idBindArray
            );
            $inactivatedResidents = $total[0]['count(*)'] + $total[1]['count(*)'] + $total[2]['count(*)'];

            // expiring residents
            $total = $this->execute(
                "select count(*) $sqlPmApp and P.Active = 1 and P.ExpireTime < :ExpiringTime and P.ExpireTime > :NowTime
                union all select count(*) $sqlMaster and P.Active = 1 and P.ExpireTime < :ExpiringTime and P.ExpireTime > :NowTime 
                union all select count(*) $sqlMember and P1.Active = 1 and P1.ExpireTime < :ExpiringTime and P1.ExpireTime > :NowTime",
                $expiringBindArray
            );
            $expiringResidents = $total[0]['count(*)'] + $total[1]['count(*)'] + $total[2]['count(*)'];

            // expired residents
            $total = $this->execute(
                "select count(*) $sqlPmApp and P.Active = 1 and P.ExpireTime < :NowTime 
                union all select count(*) $sqlMaster and P.Active = 1 and P.ExpireTime < :NowTime 
                union all select count(*) $sqlMember and P1.Active = 1 and P1.ExpireTime < :NowTime",
                $expiredBindArray
            );
            $expiredResidents = $total[0]['count(*)'] + $total[1]['count(*)'] + $total[2]['count(*)'];
        } elseif ($projectType === 'office') {
            $officeRole = implode(',', OFFROLE);
            // inactivated residents
            $total = $this->execute(
                "select count(*) from {$this->table} P where P.Role in ($officeRole) and P.Active = 0 $where",
                $idBindArray
            );
            $inactivatedResidents = $total[0]['count(*)'];

            // expiring residents
            $expiringTime = date('Y-m-d H:i:s', strtotime('+7 day'));
            $nowTime = $this->share->util->getNow();
            $total = $this->execute(
                "select count(*) from {$this->table} P where P.Role in ($officeRole) and P.Active = 1 and P.ExpireTime < :ExpiringTime 
                and P.ExpireTime > :NowTime $where",
                $expiringBindArray
            );
            $expiringResidents = $total[0]['count(*)'];

            // expired residents
            $total = $this->execute(
                "select count(*) from {$this->table} P where P.Role in ($officeRole) and P.Active = 1 and P.ExpireTime < :NowTime $where",
                $expiredBindArray
            );
            $expiredResidents = $total[0]['count(*)'];
        } elseif ($projectType === 'single') {
            $mainUserRole = PERENDMROLE;
            $subUserRole = PERENDSROLE;
            $sqlMaster = "from {$this->table} P where P.Role = $mainUserRole and P.Special = 0 $where";
            $sqlMember = "from {$this->table} P join {$this->table} P1 on P.UUID = P1.ParentUUID and P1.Role = $subUserRole $where";
            // inactivated residents
            $total = $this->execute(
                "select count(*) $sqlMaster and P.Active = 0 
                union all select count(*) $sqlMember and P1.Active = 0",
                $idBindArray
            );
            $inactivatedResidents = $total[0]['count(*)'] + $total[1]['count(*)'];

            // expiring residents
            $total = $this->execute(
                "select count(*) $sqlMaster and P.Active = 1 and P.ExpireTime < :ExpiringTime and P.ExpireTime > :NowTime 
                union all select count(*) $sqlMember and P1.Active = 1 and P1.ExpireTime < :ExpiringTime and P1.ExpireTime > :NowTime",
                $expiringBindArray
            );
            $expiringResidents = $total[0]['count(*)'] + $total[1]['count(*)'];

            // expired residents
            $total = $this->execute(
                "select count(*) $sqlMaster and P.Active = 1 and P.ExpireTime < :NowTime 
                union all select count(*) $sqlMember and P1.Active = 1 and P1.ExpireTime < :NowTime",
                $expiredBindArray
            );
            $expiredResidents = $total[0]['count(*)'] + $total[1]['count(*)'];
        }
        return [$inactivatedResidents, $expiringResidents, $expiredResidents];
    }

    /*
     *@description 获取PM app的sql检索语句
     *<AUTHOR> 2023-09-21 17:55:54 V6.7.0
     *@lastEditor cj 2023-09-21 17:55:54 V6.7.0
     *@param {*} searchValue Email检索的值/Name检索值
     *@param {array} communityIds 社区IDs
     *@param {string} insId ins的ID
     *@return [$fromPmAppWhere, $pmAppBindArray]
     */
    private function getPmAppFromWhereSql($searchValue, $communityIds, $insId)
    {
        $fromPmAppWhere = '';
        $role = PMENDMROLE;
        if (count($communityIds) === 0) {
            return [$fromPmAppWhere, []];
        }
        $idBindStr = implode(',', $communityIds);
        $pmAppBindArray = [];
        if ($searchValue === '' or is_null($searchValue)) {
            $fromPmAppWhere = "from {$this->table} left join AKCSMapping.NameMapping NM on NM.EnColumn = {$this->table}.Name and NM.EnColumnMd5 = md5({$this->table}.Name) where Role = $role and ParentID in ($idBindStr)";
            return [$fromPmAppWhere, $pmAppBindArray];
        }
        // 获取符合条件的PM
        $fromPmAppWhere = "from {$this->table} left join AKCSMapping.NameMapping NM on NM.EnColumn = {$this->table}.Name and NM.EnColumnMd5 = md5({$this->table}.Name) where Role = $role and ParentID in ($idBindStr) and md5(Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Name)";
        $pmAppBindArray[':Name'] = "%$searchValue%";
        $pmData = $this->dao->account->getManagerListByArray([['ParentID', $insId], ['Grade', PROPERTYMANAGE]], [['Email', $searchValue, '%']], [], 'A.UUID');
        if (count($pmData) === 0) {
            return [$fromPmAppWhere, $pmAppBindArray];
        }
        $pmAppAccount = $this->dao->pmAccountMap->selectByArray([['AccountUUID', array_column($pmData, 'UUID')]], 'PersonalAccount');
        if (count($pmAppAccount) === 0) {
            return [$fromPmAppWhere, $pmAppBindArray];
        }
        $pmAppAccount = array_column($pmAppAccount, 'PersonalAccount');
        list($pmAppAccountBindStr, $pmAppBindArray) = $this->share->util->getImplodeData($pmAppAccount, 'Account');
        $fromPmAppWhere = "from {$this->table} left join AKCSMapping.NameMapping NM on NM.EnColumn = {$this->table}.Name and NM.EnColumnMd5 = md5({$this->table}.Name) where Role = $role and (Account in ($pmAppAccountBindStr) or (ParentID in ($idBindStr) and md5(Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :Name)))";
        $pmAppBindArray[':Name'] = "%$searchValue%";
        return [$fromPmAppWhere, $pmAppBindArray];
    }


    /*
     *@description 获取未激活用户列表
     *<AUTHOR> 2023-09-21 20:55:36 V6.7.0
     *@lastEditor cj 2023-09-21 20:55:36 V6.7.0
     *@param {array} communityIds 社区IDs
     *@param {array} officeIds 办公IDs
     *@param {string} perId 单住户管理员ID
     *@param {string} insId ins ID
     *@param {*} limitAndSearch
     *@return [$inactivatedUserList, intval($total)]
     */
    public function getInactivatedUserList($allProjectId, $insId, $limitAndSearch)
    {
        list($communityIds, $officeIds, $perId) = $allProjectId;
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $limitAndSearch;
        $orderBySql = " order by CreateTime desc";
        if (in_array($sortField, ['Name', 'CreateTime']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort";
        }
        $limitOrderSql = $orderBySql." limit $offset,$row";
        $projectIds = array_merge($communityIds, $officeIds, $perId);
        list($idBindStr, $bindArray) = $this->share->util->getImplodeData($projectIds);

        // 获取pm App检索条件
        list($fromPmAppWhere, $pmAppBindArray) = $this->getPmAppFromWhereSql($searchValue, $communityIds, $insId);
        $whereMainLike = $whereSubLike = " AND P.ParentID in ($idBindStr) ";
        if ($searchValue !== '' && !is_null($searchValue)) {
            $whereMainLike .= "and (md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
            $whereSubLike .= "and (md5(P1.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
            $bindArray[':SearchValue'] = "%$searchValue%";
        }
        $mainRole = implode(',', [PERENDMROLE,COMENDMROLE,OFFSTAFFROLE,OFFPERSONNELROLE]);
        $subRole = implode(',', SUBROLE);
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        $fromMainWhere = "from {$this->table} P join $personalAccountUserInfoTable PU on P.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        where P.Role in ($mainRole) and P.Active = 0 and P.Special = 0 $whereMainLike";
        $fromSubWhere = "from {$this->table} P join {$this->table} P1 on P.UUID = P1.ParentUUID join $personalAccountUserInfoTable PU on P1.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        left join AKCSMapping.NameMapping NM1 on NM1.EnColumn = P1.Name and NM1.EnColumnMd5 = md5(P1.Name) 
        where P.Special = 0 and P1.Role in ($subRole) and P1.Active = 0 $whereSubLike";
        $bindArray = array_merge($bindArray, $pmAppBindArray);
        if ($fromPmAppWhere !== '') {
            $sql = "select * from ( 
            select {$this->table}.ID,UUID,Role,NM.DeColumn as Name,CreateTime,'' as Email,null as MobileNumber,RoomNumber,NM.DeColumn as MainName,ParentID $fromPmAppWhere and Active = 0 
            union all select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromPmAppWhere and Active = 0 union all select count(*) $fromMainWhere union all select count(*) $fromSubWhere";
            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'] + $count[2]['count(*)'];
        } else {
            $sql = "select * from (select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromMainWhere union all select count(*) $fromSubWhere";
            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'];
        }
        $inactivatedUserList = $this->execute($sql, $bindArray);
        foreach ($inactivatedUserList as $key => $data) {
            //Name和MainName取的就是原值，不用再次解密
            $inactivatedUserList[$key] = $this->dataArrDecode($data, ['Email', 'MobileNumber' => 'Phone']);
        }
        return [$inactivatedUserList, intval($total)];
    }


    /*
     *@description 获取即将过期用户列表
     *<AUTHOR> 2023-09-21 21:26:48 V6.7.0
     *@lastEditor cj 2023-09-21 21:26:48 V6.7.0
     *@param {*} communityIds
     *@param {*} officeIds
     *@param {*} perId
     *@param {*} insId
     *@param {*} limitAndSearch
     *@return [$expiringUserList, intval($total)]
     */
    public function getExpiringUserList($allProjectId, $insId, $limitAndSearch)
    {
        list($communityIds, $officeIds, $perId) = $allProjectId;
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $limitAndSearch;
        $orderBySql = " order by ExpireTime desc";
        if (in_array($sortField, ['Name', 'ExpireTime']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort";
        }
        $limitOrderSql = $orderBySql." limit $offset,$row";
        $projectIds = array_merge($communityIds, $officeIds, $perId);
        list($idBindStr, $bindArray) = $this->share->util->getImplodeData($projectIds);
        $expiringTime = date('Y-m-d H:i:s', strtotime('+7 day'));
        $nowTime = $this->share->util->getNow();
        $bindArray[':ExpiringTime'] = $expiringTime;
        $bindArray[':NowTime'] = $nowTime;

        // 获取pm App检索条件
        list($fromPmAppWhere, $pmAppBindArray) = $this->getPmAppFromWhereSql($searchValue, $communityIds, $insId);
        $bindArray[':SearchValue'] = "%$searchValue%";
        $whereMainLike = $whereSubLike = " AND P.ParentID in ($idBindStr) ";
        if ($searchValue !== '' && !is_null($searchValue)) {
            $whereMainLike .= "and (md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
            $whereSubLike .= "and (md5(P1.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
        }
        $mainRole = implode(',', [PERENDMROLE,COMENDMROLE,OFFSTAFFROLE,OFFPERSONNELROLE]);
        $subRole = implode(',', SUBROLE);
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];

        $fromMainWhere = "from {$this->table} P join $personalAccountUserInfoTable PU on P.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        where P.Role in ($mainRole) and P.Active = 1 and P.ExpireTime < :ExpiringTime 
        and P.ExpireTime > :NowTime and P.Special = 0 $whereMainLike";
        $fromSubWhere = "from {$this->table} P join {$this->table} P1 on P.UUID = P1.ParentUUID join $personalAccountUserInfoTable PU on P1.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        left join AKCSMapping.NameMapping NM1 on NM1.EnColumn = P1.Name and NM1.EnColumnMd5 = md5(P1.Name) 
        where P.Special = 0 and P1.Role in ($subRole) and P1.Active = 1 and P1.ExpireTime < :ExpiringTime 
        and P1.ExpireTime > :NowTime $whereSubLike";
        $bindArray = array_merge($bindArray, $pmAppBindArray);
        if ($fromPmAppWhere !== '') {
            $sql = "select * from (select {$this->table}.ID,UUID,Role,NM.DeColumn as Name,CreateTime,ExpireTime,'' as Email,null as MobileNumber,RoomNumber,NM.DeColumn as MainName,ParentID $fromPmAppWhere and Active = 1 
            and ExpireTime < :ExpiringTime and ExpireTime > :NowTime
            union all select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,P.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,P1.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromPmAppWhere and Active = 1 and ExpireTime < :ExpiringTime and ExpireTime > :NowTime union all select count(*) $fromMainWhere union all select count(*) $fromSubWhere";
            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'] + $count[2]['count(*)'];
        } else {
            $sql = "select * from (select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,P.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,P1.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromMainWhere union all select count(*) $fromSubWhere";
            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'];
        }
        $expiringUserList = $this->execute($sql, $bindArray);
        foreach ($expiringUserList as $key => $data) {
            //Name和MainName取的就是原值，不用再次解密
            $expiringUserList[$key] = $this->dataArrDecode($data, ['Email', 'MobileNumber' => 'Phone']);
        }
        return [$expiringUserList, intval($total)];
    }

    /*
     *@description 获取已过期用户列表
     *<AUTHOR> 2023-09-21 21:26:48 V6.7.0
     *@lastEditor cj 2023-09-21 21:26:48 V6.7.0
     *@param {*} communityIds
     *@param {*} officeIds
     *@param {*} perId
     *@param {*} insId
     *@param {*} limitAndSearch
     *@return [$expiredUserList, intval($total)]
     */
    public function getExpiredUserList($allProjectId, $insId, $limitAndSearch)
    {
        list($communityIds, $officeIds, $perId) = $allProjectId;
        list($offset, $row, $searchKey, $searchValue, $sortField, $sort) = $limitAndSearch;
        $orderBySql = " order by ExpireTime desc";
        if (in_array($sortField, ['Name', 'ExpireTime']) && in_array($sort, SORT_MODE)) {
            $orderBySql = " order by $sortField $sort";
        }
        $limitOrderSql = $orderBySql." limit $offset,$row";
        $projectIds = array_merge($communityIds, $officeIds, $perId);
        list($idBindStr, $bindArray) = $this->share->util->getImplodeData($projectIds);
        $nowTime = $this->share->util->getNow();
        $bindArray[':NowTime'] = $nowTime;

        // 获取pm App检索条件
        list($fromPmAppWhere, $pmAppBindArray) = $this->getPmAppFromWhereSql($searchValue, $communityIds, $insId);
        $bindArray[':SearchValue'] = "%$searchValue%";
        $whereMainLike = $whereSubLike = " AND P.ParentID in ($idBindStr) ";
        if ($searchValue !== '' && !is_null($searchValue)) {
            $whereMainLike .= "and (md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
            $whereSubLike .= "and (md5(P1.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue) 
            or md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue) 
            or md5(PU.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :SearchValue))";
        }
        $mainRole = implode(',', [PERENDMROLE,COMENDMROLE,OFFSTAFFROLE,OFFPERSONNELROLE]);
        $subRole = implode(',', SUBROLE);
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];

        $fromMainWhere = "from {$this->table} P join $personalAccountUserInfoTable PU on P.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        where P.Role in ($mainRole) and P.Active = 1 and P.ExpireTime < :NowTime and P.Special = 0 $whereMainLike";
        $fromSubWhere = "from {$this->table} P join {$this->table} P1 on P.UUID = P1.ParentUUID join $personalAccountUserInfoTable PU on P1.UserInfoUUID = PU.UUID 
        left join AKCSMapping.NameMapping NM on NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name) 
        left join AKCSMapping.NameMapping NM1 on NM1.EnColumn = P1.Name and NM1.EnColumnMd5 = md5(P1.Name) 
        where P.Special = 0 and P1.Role in ($subRole) and P1.Active = 1 and P1.ExpireTime < :NowTime $whereSubLike";
        $bindArray = array_merge($bindArray, $pmAppBindArray);
        if ($fromPmAppWhere !== '') {
            $sql = "select * from (select {$this->table}.ID,UUID,Role,NM.DeColumn as Name,CreateTime,ExpireTime,'' as Email,null as MobileNumber,RoomNumber,NM.DeColumn as MainName,ParentID $fromPmAppWhere and Active = 1 and ExpireTime < :NowTime
            union all select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,P.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,P1.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromPmAppWhere and Active = 1 and ExpireTime < :NowTime union all select count(*) $fromMainWhere union all select count(*) $fromSubWhere";

            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'] + $count[2]['count(*)'];
        } else {
            $sql = "select * from (select P.ID,P.UUID,P.Role,NM.DeColumn as Name,P.CreateTime,P.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromMainWhere 
            union all select P1.ID,P1.UUID,P1.Role,NM1.DeColumn as Name,P1.CreateTime,P1.ExpireTime,PU.Email,PU.MobileNumber,P.RoomNumber,NM.DeColumn as MainName,P.ParentID $fromSubWhere) as user_table $limitOrderSql";
            $countSql = "select count(*) $fromMainWhere union all select count(*) $fromSubWhere";
            $count = $this->execute($countSql, $bindArray);
            $total = $count[0]['count(*)'] + $count[1]['count(*)'];
        }
        $expiredUserList = $this->execute($sql, $bindArray);
        foreach ($expiredUserList as $key => $data) {
            //Name和MainName取的就是原值，不用再次解密
            $expiredUserList[$key] = $this->dataArrDecode($data, ['Email', 'MobileNumber' => 'Phone']);
        }
        return [$expiredUserList, intval($total)];
    }

    /**
     * @description: 根据项目UUID查询社区用户数据
     * @param {string} $projectUUID
     * @param {string} $column 查询的字段，指定的字段需要增加P.前缀
     * @return array
     * @throws \Exception
     * @author: csc 2023/9/18 18:24 V6.7.0
     * @lastEditors: csc 2023/9/18 18:24 V6.7.0
     */
    public function getCommUserInfoByProjUUID($projectUUID, $column = 'P.*')
    {
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $sql = "select $column from $personalAccountTable P where P.ParentUUID = :UUID and P.Role = 20 and P.Special = 0
                union select $column from $personalAccountTable P join $personalAccountTable P2 on P.ParentID = P2.ID where P2.Special = 0 and P.Role = 21 and P2.ParentUUID = :UUID";
        $data = $this->execute($sql, [':UUID' => $projectUUID]);
        foreach ($data as &$item) {
            $item = $this->dataArrDecode($item, $this->confusionField);
        }
        unset($item);
        return $data;
    }

    /**
     * @description: 搜索指定的用户数据
     * @param {array} $searchArr
     * $searchArr = [
        'user' => $user, //搜索的字符串
        'communityIds' => $communityIds,
        'officeIds' => $officeIds,
        'singIds' => $singIds,
        'offset' => $offset,
        'row' => $row,
    ];
     * @return array
     * @throws \Exception
     * @author: csc 2023/9/18 16:02 V6.7.0
     * @lastEditors: csc 2023/9/18 16:02 V6.7.0
     */
    public function getUserInfoList($searchArr)
    {
        $sql = [];

        $user = $searchArr['user'];
        $offset = $searchArr['offset'];
        $row = $searchArr['row'];

        $bindArray = [
            ':User' => "%$user%"
        ];

        $projectIds = array_merge($searchArr['communityIds'], $searchArr['officeIds'], $searchArr['singIds']);
        $subUserProjectIds = array_merge($searchArr['communityIds'], $searchArr['singIds']);

        //查询已关联统一的pm app
        if (!empty($searchArr['communityIds'])) {
            list($ids, $bind) = $this->share->util->getImplodeData($searchArr['communityIds'], 'communityId');
            $whereMapping = '';
            if ($user !== '' && !is_null($user)) {
                $whereMapping = " and (md5(PA.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :User) 
or md5(AUF.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :User))";
            }
            $sql[] = "(select PA.Account,PA.ID,PA.UUID,PA.ParentID,PA.Role, NM.DeColumn as UserName, AUF.Email, '' as MobileNumber, PA.Active, PA.ExpireTime, PA.ParentID as ProjectID 
from PersonalAccount PA inner join AccountUserInfo AUF on PA.UserInfoUUID = AUF.UUID 
left join AKCSMapping.NameMapping NM on NM.EnColumn = PA.Name and NM.EnColumnMd5 = md5(PA.Name) where PA.ParentID in ($ids) $whereMapping and PA.Role = 40)";
            $bindArray = array_merge($bindArray, $bind);
        }

        //查询办公、单住户/社区 主账号、未关联统一的pm app
        if (!empty($projectIds)) {
            list($ids, $bind) = $this->share->util->getImplodeData($projectIds, 'projId');
            $whereMapping = '';
            if ($user !== '' && !is_null($user)) {
                $whereMapping = " and (md5(PA.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :User) 
or md5(AUF.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :User) 
or md5(AUF.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :User))";
            }
            $sql[] = "(select PA.Account,PA.ID,PA.UUID,PA.ParentID,PA.Role, NM.DeColumn as UserName, AUF.Email, AUF.MobileNumber, PA.Active, PA.ExpireTime, PA.ParentID as ProjectID
from PersonalAccount PA inner join PersonalAccountUserInfo AUF on PA.UserInfoUUID = AUF.UUID 
left join AKCSMapping.NameMapping NM on NM.EnColumn = PA.Name and NM.EnColumnMd5 = md5(PA.Name) where PA.ParentID in ($ids) $whereMapping and PA.Role in (10,20,30,31,40) and PA.Special = 0)";
            $bindArray = array_merge($bindArray, $bind);
        }

        //查询单住户/社区从账号
        if (!empty($subUserProjectIds)) {
            list($ids, $bind) = $this->share->util->getImplodeData($projectIds, 'subProjId');
            $whereMapping = '';
            if ($user !== '' && !is_null($user)) {
                $whereMapping = " and (md5(PA.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :User) 
or md5(AUF.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :User) 
or md5(AUF.MobileNumber) in (select AKCSMapping.PhoneMapping.EnColumnMd5 from AKCSMapping.PhoneMapping where DeColumn like :User))";
            }
            $sql[] = "(select PA.Account,PA.ID,PA.UUID,PA.ParentID,PA.Role, NM.DeColumn as UserName, AUF.Email, AUF.MobileNumber, PA.Active, PA.ExpireTime, PA2.ParentID as ProjectID
from PersonalAccount PA inner join PersonalAccountUserInfo AUF on PA.UserInfoUUID = AUF.UUID 
left join AKCSMapping.NameMapping NM on NM.EnColumn = PA.Name and NM.EnColumnMd5 = md5(PA.Name) inner join PersonalAccount PA2 on PA2.ID = PA.ParentID where PA2.ParentID in ($ids) $whereMapping and PA.Role in (11,21) and PA.Special = 0)";
            $bindArray = array_merge($bindArray, $bind);
        }

        $sql = join(' union ', $sql);
        if (empty($sql)) {
            return [0, []];
        }

        $totalSql = "select count(1) from ($sql) as combined_tables";
        $total = intval($this->execute($totalSql, $bindArray)[0]['count(1)']);

        if (empty($total)) {
            return [0, []];
        }

        $dataSql = "select * from ($sql) as combined_tables order by BINARY UserName asc limit $offset, $row;";
        $data = $this->execute($dataSql, $bindArray);
        foreach ($data as &$item) {
            //UserName取的是原值，不用解密
            $item = $this->dataArrDecode($item, ['Email', 'MobileNumber' => 'Phone']);
        }
        unset($item);
        return [$total, $data];
    }

    /**
     * @description: 获取办公全部用户列表
     * @return array
     * @author: shoubin.chen 2023-10-07 16:54:14 v6.7
     * @lastEditor: shoubin.chen 2023-10-07 16:54:14 v6.7
     */
    public function getOfficeUserForIns($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue, $sortField, $sort) = $searchArray;
        $role = $bindArray[':Role'];
        $active = $bindArray[':Status'];
        unset($bindArray[':DepartmentId']);
        unset($bindArray[':Role']);
        unset($bindArray[':Status']);
        unset($bindArray[':SearchValue']);

        $where = '';
        if ($searchValue !== '' && !is_null($searchValue)) {
            //数据加密
            $where .= 'AND (md5(P.Name) in (select AKCSMapping.NameMapping.EnColumnMd5 from AKCSMapping.NameMapping  where DeColumn like :SearchValue1) 
            OR md5(PU.Email) in (select AKCSMapping.EmailMapping.EnColumnMd5 from AKCSMapping.EmailMapping  where DeColumn like :SearchValue2) 
            OR PO.EmployeeID LIKE :SearchValue3)';
            $bindArray[":SearchValue1"] = "%$searchValue%";
            $bindArray[":SearchValue2"] = "%$searchValue%";
            $bindArray[":SearchValue3"] = "%$searchValue%";
        }
        switch ($role) {
            case "all":
                break;
            default :
                $where .= ' And P.Role =:Role';
                $bindArray[":Role"] = $role;
        }

        //前端：0-未激活 1-正常  2-过期 3-将过期
        switch ($active) {
            case '0'://未激活
                $where .= ' and P.Active = 0';
                break;
            case '1'://正常（不包含即将过期）
                $where .= ' and P.Active = 1 and P.ExpireTime > :NormalExpireTime';
                $bindArray[':NormalExpireTime'] = date('Y-m-d H:i:s', strtotime('+7 day'));
                break;
            case '2'://过期
                $where .= ' and P.Active = 1 and P.ExpireTime < :Now';
                $bindArray[':Now'] = $this->share->util->getNow();
                break;
            case '3'://将过期，距离过期时间15天内
                $where .= ' and P.Active = 1 and P.ExpireTime BETWEEN :Now AND :ExpiringTime';
                $bindArray[':Now'] = $this->share->util->getNow();
                $bindArray[':ExpiringTime'] = date('Y-m-d H:i:s', strtotime('+7 day'));
                break;
        }
        $personalAccountTable = PROXY_TABLES["personalAccount"];
        $communityUnitTable = PROXY_TABLES["communityUnit"];
        $personalAccountOfficeInfoTable = PROXY_TABLES["personalAccountOfficeInfo"];
        $personalAccountUserInfoTable = PROXY_TABLES['personalAccountUserInfo'];
        $queryResult = "select P.ID,P.UUID, P.ParentID, P.Name, P.Role, PU.Email, PU.MobileNumber, P.CreateTime, PO.EmployeeID, CU.UnitName as DepartmentName, 
        P.Initialization, P.Active, P.ExpireTime, P.PhoneCode, P.Phone, P.Phone2, P.Phone3";
        $table = "$personalAccountTable P join $communityUnitTable CU on P.UnitID = CU.ID join $personalAccountOfficeInfoTable PO on PO.PersonalAccountUUID = P.UUID
        left join $personalAccountUserInfoTable PU on PU.UUID = P.UserInfoUUID";
        $where = "P.ParentID = :ParentID $where";
        $total = $this->execute("select count(*) from $table where $where", $bindArray)[0]["count(*)"];
        $orderBySql = ' order by P.ID desc';
        $userData = $this->execute("$queryResult from $table where $where $orderBySql limit $offset,$rows", $bindArray);

        //数据解密
        foreach ($userData as $key => $data) {
            $userData[$key] = $this->dataArrDecode($data, ['Name', 'Email', 'MobileNumber' => 'Phone', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }

        return [$userData, intval($total)];
    }

    /**
     * @description:获得指定社区的所有用户
     * @param: {string} $comUuid 社区uuid
     * @author: shoubin.chen 2023-10-12 16:08:09 v6.7
     * @lastEditor: shoubin.chen 2023-10-12 16:08:09 v6.7
     */
    public function getAllUserForCommunity($comUuid)
    {
        $personalAccountTable = PROXY_TABLES['personalAccount'];
        $select1 = " select ID from $personalAccountTable where ParentUUID = :UUID and Role = 20 and Special = 0 ";
        $select2 = "select P.ID from $personalAccountTable P join $personalAccountTable P2 on P.ParentID = P2.ID where P2.Special = 0 and P.Role = 21 and P2.ParentUUID = :UUID";
        $bindArray = [':UUID' => $comUuid];
        $selectSql = "$select1 UNION $select2";
        return $this->execute($selectSql, $bindArray);
    }

    /**
     * @description:查找数组中的主账户有几个从账户
     * @param {array} $userUuids [1,2,3]
     * @return array
     * @author: shoubin.chen 2023-10-11 16:39:19 v6.7
     * @lastEditor: shoubin.chen 2023-10-11 16:39:19 v6.7
     */
    public function getCountSingleSubUser($userUuids)
    {
        $selectSql = 'select count(*), ParentUUID from PersonalAccount where ParentUUID in (' . implode(',', $userUuids) . ') group by ParentUUID';
        return $this->execute($selectSql);
    }

    /**
     * @description: 获取pm app列表（关联Account、PmAccountMap、PersonalAccount）
     * @param {array} $searchArrA Account搜索条件
     * @param {array} $searchArrPAM PmAccountMap搜索条件
     * @param {array} $searchArrPA PersonalAccount搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getPmAppList($searchArrA, $searchArrPAM, $searchArrPA, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrA)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->account->makeSearchCondition($searchArrA, true, 'A');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrPAM)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->pmAccountMap->makeSearchCondition($searchArrPAM, true, 'PAM');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrPA)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrPA, true, 'PA');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        //关联查询
        $sql = "select $fields from Account A 
            join PmAccountMap PAM on A.UUID = PAM.AccountUUID 
            join PersonalAccount PA on PA.UUID = PAM.PersonalAccountUUID";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获取办公用户列表（关联communityUnit、PersonalAccount）
     * @param {array} $searchArrCU communityUnit搜索条件
     * @param {array} $searchArrP PersonalAccount搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getOfficeUserList($searchArrCU, $searchArrP, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrCU)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityUnit->makeSearchCondition($searchArrCU, true, 'CU');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrP)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrP, true, 'P');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        //关联查询
        $sql = "select $fields from PersonalAccount P 
            left join CommunityUnit CU on P.UnitID = CU.ID";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获取主账号带房间信息列表（关联communityRoom、communityUnit、PersonalAccount）
     * @param {array} $searchArrP PersonalAccount搜索条件
     * @param {array} $searchArrCR communityUnit搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getMainUserWithUnitRoomList($searchArrP, $searchArrCR, $searchArrCU, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrP)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrP, true, 'P');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrCR)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityRoom->makeSearchCondition($searchArrCR, true, 'CR');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrCU)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityUnit->makeSearchCondition($searchArrCU, true, 'CU');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }


        //关联查询
        $sql = "select $fields from PersonalAccount P 
            join CommunityRoom CR on P.RoomID = CR.ID join CommunityUnit CU on P.UnitID = CU.ID";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获取从账号带房间信息列表（关联communityRoom、communityUnit、PersonalAccount、PersonalAccount）
     * @param {array} $searchArrP PersonalAccount搜索条件
     * @param {array} $searchArrCR communityUnit搜索条件
     * @param {array} $searchArrP2 PersonalAccount搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getSubUserWithUnitRoomList($searchArrP, $searchArrCR, $searchArrP2, $searchArrCU, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrP)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrP, true, 'P');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrCR)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityRoom->makeSearchCondition($searchArrCR, true, 'CR');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrP2)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrP2, true, 'P2');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }
        if (!empty($searchArrCU)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->communityUnit->makeSearchCondition($searchArrCU, true, 'CU');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        //关联查询
        $sql = "select $fields from PersonalAccount P join PersonalAccount P2 on P.ParentID = P2.ID
            join CommunityRoom CR on P2.RoomID = CR.ID join CommunityUnit CU on P2.UnitID = CU.ID";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获取办公用户带详细信息列表（关联personalAccountOfficeInfo、PersonalAccount）
     * @param {array} $searchArrA PersonalAccount搜索条件
     * @param {array} $searchArrI personalAccountOfficeInfo搜索条件
     * @param {array} $options 排序、条数限制条数
     * @param {string} $fields 查询字段
     * @return array
     * @throws \Exception
     * @author: csc 2024/3/12 13:50 V6.8.0
     * @lastEditors: csc 2024/3/12 13:50 V6.8.0
     */
    public function getOfficeUserWInfoList($searchArrA, $searchArrI, $options = [], $fields = '*')
    {
        $condition = $conditionBindArray = [];

        if (!empty($searchArrA)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->makeSearchCondition($searchArrA, true, 'A');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        if (!empty($searchArrI)) {
            list($conditionMerge, $conditionBindArrayMerge) = $this->dao->personalAccountOfficeInfo->makeSearchCondition($searchArrI, true, 'I');
            $condition = array_merge($condition, $conditionMerge);
            $conditionBindArray = array_merge($conditionBindArray, $conditionBindArrayMerge);
        }

        //关联查询
        $sql = "select $fields from PersonalAccount A join PersonalAccountOfficeInfo I on A.UUID = I.PersonalAccountUUID";
        if (!empty($condition)) {
            $condition = join(' and ', $condition);
            $sql .= " where $condition";
        }

        if (!empty($options)) {
            if (!empty($options['orderBy'])) {
                $sql .= " order by " . $options['orderBy'];
            }
            if (!empty($options['limit'])) {
                $sql .= " limit " . $options['limit'];
            }
        }

        $res =  $this->execute($sql, $conditionBindArray);

        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获得社区的所有用户
     * @param $parentUUID
     * @param $searchKey
     * @param $searchValue
     * @return array
     * @author: shoubin.chen 2024/6/6 10:28:43 V6.8.0
     * @lastEditor: shoubin.chen 2024/6/6 10:28:43  V6.8.0
     */
    public function getCommunityAccessUser($parentUUID, $searchKey, $searchValue, $buildingUUID)
    {
        $whereMainUser = $whereSubUser = "";
        $bindArray = [];
        $bindArray[':ParentUUID'] = $parentUUID;
        if (!empty($searchValue)) {
            switch ($searchKey) {
                case "Name":
                    $whereMainUser .= "AND P.Name in (select AKCSMapping.NameMapping.EnColumn from AKCSMapping.NameMapping  where DeColumn like :SearchValue)  ";
                    $whereSubUser .= "AND P.Name in (select AKCSMapping.NameMapping.EnColumn from AKCSMapping.NameMapping  where DeColumn like :SearchValue)  ";
                    break;
                case "RoomName":
                    $whereMainUser .= "AND CR.RoomName LIKE :SearchValue ";
                    $whereSubUser .= "AND CR.RoomName LIKE :SearchValue ";
                    break;
            }
            $bindArray[":SearchValue"] = "%$searchValue%";
        }

        if (!empty($buildingUUID)) {
            $whereMainUser .= "AND CU.UUID = :UnitUUID";
            $whereSubUser .= "AND CU.UUID = :UnitUUID";
            $bindArray[":UnitUUID"] = $buildingUUID;
        }

        $field = "P.ID, P.UUID, P.Account, P.Name, 'User' as Type, CR.RoomName, CU.UnitName, P.Role";
        $mainUserSql = "
        select $field  from PersonalAccount P
		JOIN CommunityRoom CR ON CR.ID = P.RoomID
		JOIN CommunityUnit CU ON P.UnitID = CU.ID
		where ParentUUID = :ParentUUID and Role = 20 and Special = 0  $whereMainUser
        ";

        $subUserSql = "
         select $field from PersonalAccount P 
         join PersonalAccount P2 on P.ParentID = P2.ID
         JOIN CommunityRoom CR ON CR.ID = P2.RoomID
         JOIN CommunityUnit CU ON P.UnitID = CU.ID
         where P2.Special = 0 and P.Role = 21 and P2.ParentUUID = :ParentUUID $whereSubUser
        ";
        $sql = "$mainUserSql UNION $subUserSql";
        $res = $this->execute($sql, $bindArray);
        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }


    /**
     * @description: 获得房间下可排序的用户列表
     * @param: $aptUUID 房间UUID
     * @param: $sort    排序方式
     * @return array
     * @author: shoubin.chen 2024/6/12 15:48:14 V6.8.0
     * @lastEditor: shoubin.chen 2024/6/12 15:48:14  V6.8.0
     */
    public function getAptUserAndSort($aptUUID, $sort = 'asc')
    {
        $bindArray = [];
        $sql = "
            SELECT P.ID,P.UUID,NM.DeColumn as Name FROM PersonalAccount P 
                LEFT JOIN AKCSMapping.NameMapping NM ON NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name)
                WHERE UUID = :AptUUID AND Role = 20 AND Special = 0
            UNION
            SELECT P.ID,P.UUID,NM.DeColumn as Name FROM PersonalAccount P 
                LEFT JOIN AKCSMapping.NameMapping NM ON NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name)
                WHERE ParentUUID = :AptUUID  AND Role = 21 AND Special = 0
            ORDER BY Name $sort
        ";
        $bindArray[":AptUUID"] = $aptUUID;

        $res = $this->execute($sql, $bindArray);

        return $res;
    }

    /**
     * @description: 获取社区下所有用户
     * @author: shoubin.chen 2024/7/11 11:50:50 V6.8.1
     * @lastEditor: shoubin.chen 2024/7/11 11:50:50  V6.8.1
     */
    public function getCommunityAllUser($communityUUID)
    {
        $bindArray = [];
        $sql = "
            SELECT P.ID,P.UUID as AptUUID,P.UUID,P.Role,NM.DeColumn as Name,'User' FROM PersonalAccount P 
                LEFT JOIN AKCSMapping.NameMapping NM ON NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name)
                WHERE ParentUUID = :ParentUUID AND Role = 20 AND Special = 0
            UNION
            SELECT P.ID,P2.UUID as AptUUID,P.UUID,P.Role,NM.DeColumn as Name,'User' FROM PersonalAccount P 
                 JOIN PersonalAccount P2 on P.ParentID = P2.ID
                LEFT JOIN AKCSMapping.NameMapping NM ON NM.EnColumn = P.Name and NM.EnColumnMd5 = md5(P.Name)
                WHERE P2.Special = 0 and P.Role = 21 and P2.ParentUUID = :ParentUUID 
        ";
        $bindArray[":ParentUUID"] = $communityUUID;

        $res = $this->execute($sql, $bindArray);

        return $res;
    }


    /**
     * @description: 获取社区下所有房间
     * @param:
     * @return array
     * @author: shoubin.chen 2024/8/27 20:35:46 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/27 20:35:46  V6.8.1
     */
    public function getCommunityAllRoom($communityUUID)
    {
        $bindArray = [];
        $sql = "
            SELECT
                PA.ParentUUID AS Community,
                CU.UnitName AS Unit,
                CR.RoomName AS RoomNumber 
            FROM
                PersonalAccount PA
                JOIN CommunityRoom CR ON PA.CommunityRoomUUID = CR.UUID
                JOIN CommunityUnit CU ON PA.CommunityUnitUUID = CU.UUID 
            WHERE
                PA.ParentUUID = :ParentUUID 
        ";
        $bindArray[":ParentUUID"] = $communityUUID;

        $res = $this->execute($sql, $bindArray);

        return $res;
    }

    /**
     * @description: 获取单住户房间下所有用户
     * @param: $mainUserUUID 主账号UUID
     * @author: shoubin.chen 2024/10/11 11:30:30 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/11 11:30:30  V7.1.0
     */
    public function getSingleFamilyUsers($mainUserUUID)
    {
        $sql = "
            SELECT ID,UUID,Name,Role FROM PersonalAccount WHERE UUID = :UUID And Role = 10
            UNION 
            SELECT Son.ID,Son.UUID,Son.Name,Son.Role FROM PersonalAccount Son
            JOIN PersonalAccount Fa ON Son.ParentUUID = Fa.UUID
            WHERE Fa.UUID = :UUID And Son.Role = 11
        ";
        $bindArray[":UUID"] = $mainUserUUID;
        $res = $this->execute($sql, $bindArray);
        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    /**
     * @description: 获取社区房间下所有用户
     * @param: $mainUserUUID 主账号UUID
     * @author: shoubin.chen 2024/10/11 11:30:30 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/11 11:30:30  V7.1.0
     */
    public function getCommunityFamilyUsers($mainUserUUID)
    {
        $sql = "
            SELECT ID,UUID,Account,Name,Role FROM PersonalAccount WHERE UUID = :UUID And Role = 20
            UNION 
            SELECT Son.ID,Son.UUID,Son.Account,Son.Name,Son.Role FROM PersonalAccount Son
            JOIN PersonalAccount Fa ON Son.ParentUUID = Fa.UUID
            WHERE Fa.UUID = :UUID And Son.Role = 21
        ";
        $bindArray[":UUID"] = $mainUserUUID;
        $res = $this->execute($sql, $bindArray);
        foreach ($res as $key => $data) {
            $res[$key] = $this->dataArrDecode($data, ['Name', 'FirstName' => 'Name', 'LastName' => 'Name', 'Phone', 'Phone2' => 'Phone', 'Phone3' => 'Phone']);
        }
        return $res;
    }

    public function getSingleUsersByIns($insUUID)
    {
        $bindArray[':InsUUID'] = $insUUID;
        $sql = "select A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         JOIN Account B on A.ParentUUID = B.UUID
                         JOIN Account C on B.ManageGroup = C.ID
                where A.Role = 10
                  AND C.UUID = :InsUUID
                union all
                select A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         JOIN PersonalAccount D on A.ParentID = D.ID
                         JOIN Account B ON D.ParentUUID = B.UUID
                         JOIN Account C on B.ManageGroup = C.ID
                where A.Role = 11
                  AND C.UUID = :InsUUID";
        return $this->execute("$sql order by ID DESC", $bindArray);
    }
    public function getCommunityUsersByIns($insUUID)
    {
        $bindArray[':InsUUID'] = $insUUID;
        $sql = "select A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         JOIN Account B on A.ParentID = B.ID
                         JOIN Account C on B.ManageGroup = C.ID
                where A.Role = 20
                  and A.Special = 0
                  AND C.UUID = :InsUUID
                union all
                select  A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         JOIN PersonalAccount D on A.ParentID = D.ID
                         JOIN Account B ON D.ParentID = B.ID
                         JOIN Account C on B.ManageGroup = C.ID
                where A.Role = 21
                  and A.Special = 0
                  AND C.UUID = :InsUUID";
        return $this->execute("$sql order by ID DESC", $bindArray);
    }
    function getOfficeAdminAppsByIns($insUUID)
    {
        $bindArray[':InsUUID'] = $insUUID;
        $sql = "select A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         join Account B on A.ParentID = B.ID
                         join Account C on B.ManageGroup = C.ID
                         left join PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID where A.Role in (41)  AND C.UUID =:InsUUID";
        return $this->execute("$sql order by ID DESC", $bindArray);
    }
    function getOfficeEndUsersByIns($insUUID)
    {
        $bindArray[':InsUUID'] = $insUUID;
        $sql = "select A.ID,
                       A.UUID,
                       A.ParentUUID,
                       A.Account
                from PersonalAccount A
                         join Account B on A.ParentID = B.ID
                         join Account C on B.ManageGroup = C.ID
                         left join PersonalAccountUserInfo PU on A.UserInfoUUID = PU.UUID where A.Role in (31,30,32)  AND C.UUID =:InsUUID";
        return $this->execute("$sql order by ID DESC", $bindArray);
    }
    public function getAllResidentByProjectUuidWithoutDecode($projectUUID)
    {
        $sql="select * from PersonalAccount where ParentUUID=:ParentUUID and Special=0
        union select A.* from PersonalAccount A join PersonalAccount B on A.ParentUUID=B.UUID where B.ParentUUID=:ParentUUID";
        $searchArr=[':ParentUUID'=>$projectUUID];
        $allResident = $this->execute($sql, $searchArr);
        return $allResident;
    }
    //根据UUID获取社区用户的楼栋名称和房间名称
    public function getUnitAndAptInfoByUUID($personalAccountUUID)
    {
        if (empty($personalAccountUUID)) {
            return [];
        }

        if (is_array($personalAccountUUID)) {
            $uuids = "'" . implode("','", $personalAccountUUID) . "'";
        } else {
            $uuids = "'$personalAccountUUID'";
        }

        $sql = "SELECT A.ID,A.UUID,A.Name,A.Active,A.Role,U.UnitName, R.Floor,R.RoomName,A.UnitID,A.RoomNumber,A.RoomID,U.UnitName,U.UUID as UnitUUID
		               FROM PersonalAccount A
		               LEFT JOIN CommunityUnit U ON A.UnitID = U.ID 
                       JOIN CommunityRoom R ON R.ID = A.RoomID 
		               WHERE A.Role = 20 AND A.UUID IN($uuids)";
        $list = $this->execute($sql);
        //数据解密
        foreach ($list as &$item) {
            $item = $this->dataArrDecode($item, ['Name']);
        }
        unset($item);
        return $list;
    }

    public function getUserContact($uuidList)
    {
        $list = [];
        if (!empty($uuidList)) {
            list($uuidBindStr, $uuidBindArray) = \share\util\getImplodeData($uuidList);
            $sql = "
            SELECT 
                MN.DeColumn AS Name,
                ME.DeColumn AS Email,
                MP.DeColumn AS MobileNumber
            FROM PersonalAccount PA
            JOIN PersonalAccountUserInfo PUI ON PA.UserInfoUUID = PUI.UUID
            LEFT JOIN AKCSMapping.EmailMapping ME ON MD5(PUI.Email) = ME.EnColumnMd5
            LEFT JOIN AKCSMapping.PhoneMapping MP ON MD5(PUI.MobileNumber) = MP.EnColumnMd5
            LEFT JOIN AKCSMapping.NameMapping MN ON MD5(PA.NAME) = MN.EnColumnMd5
            WHERE PA.UUID IN ($uuidBindStr)";

            $list = $this->execute($sql, $uuidBindArray);
        }


        return $list;
    }
    
}