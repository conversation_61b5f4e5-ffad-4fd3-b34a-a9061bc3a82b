<?php
/*
 * @Description: 常量数据容器
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-09 11:54:39
 * @LastEditors  : kxl
 */
namespace dataContainer;
include_once __DIR__."/../interfaces/container/main.php";
use interfaces\container\main\dataContainer;
class CConst implements dataContainer {
    private $data;
    public function bind ($key,$value) {
        $this->data[$key] = $value;
    }

    public function get ($key) {
        global $cLog,$gApp;
        $cLog->actionLog("#dataContainer#const#want key = $key");
        if(array_key_exists($key,$this->data)) return $this->data[$key];
        else return $gApp["plan"]["data"][$key];
    }
}