<?php
#error_reporting(0);

require_once(dirname(__FILE__) . '/../socket.php');
require_once(dirname(__FILE__) . '/../funcs_kafka.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbhost = DATABASEIP;
    $dbport = DATABASEPORT;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const NGINX_DOWNLOAD_HOST = IPV4IMG;
const NGINX_LINK_KEY = "ak_fdfs";

function computedPcapLink($path)
{
    if (preg_match("/^\/group/", $path)) {
        $time = time();
        $link = $path;
        $path = substr($path, 8);
        return NGINX_DOWNLOAD_HOST . $link . "?token=" . md5($path . NGINX_LINK_KEY . $time) . "&ts=$time";
    }
}

// 下载pacp文件
function downloadPcapFile($mac)
{
    echo "downloadPcapFile begin\n";
    $db = getDB();
    try {
        $sth = $db->prepare("select UUID from PcapCaptureControl where MAC =:mac order by ID desc limit 1");
        $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
        $sth->execute();
        $uuid = $sth->fetch(PDO::FETCH_ASSOC)['UUID'];
        

        $sth = $db->prepare("select A.MAC, B.FileName, B.FileUrl from PcapCaptureControlList B left join PcapCaptureControl A on A.UUID = B.PcapCaptureUUID  where B.PcapCaptureUUID = :PcapCaptureUUID", [":PcapCaptureUUID"=>$uuid]);
        $sth->bindParam(':PcapCaptureUUID', $uuid, PDO::PARAM_STR);
        $sth->execute();
        $fileInfoList = $sth->fetchALL(PDO::FETCH_ASSOC);
        
        if ($fileInfoList) {
            // 创建存放路径
            $mac = $fileInfoList[0]['MAC'];
            $savePath = $mac;
           // $savePath = PCAP_FILE_PREFIX . $mac;
            if (is_dir($savePath)) {
                rmdir($savePath);
                mkdir($savePath);
            } else {
                mkdir($savePath);
            }
            echo "storeDir: " . $savePath . "\n";
            
            foreach ($fileInfoList as $fileInfo) {
                $link = computedPcapLink($fileInfo['FileUrl']);
                echo "downloadPcapFile begin nginx link" . $link . "\n";
                $saveFilePath = $savePath . "/" . $fileInfo['FileName'];

                $command = "wget --no-check-certificate -O " . escapeshellarg($saveFilePath) . " " . escapeshellarg($link);
                exec($command, $output, $returnCode);
            }
            
			if (file_exists($savePath . ".tar.gz")) {
				unlink($savePath . ".tar.gz");
			}				

            $command = "tar -czvf " . $mac . ".tar.gz " . $mac;
            echo "command: " . $command . "\n";
            exec($command);
            echo "download success, export cmd: sz ". $mac . ".tar.gz" . "\n";
        } else {
            echo "do not have pcap file" . "\n";
        }
    } catch (PDOException $e) {
        echo "db exception=" . $e->getMessage();
        return false;
    }
}

//使用说明：① php download_pcap.php $mac
if ($argc != 2) {
    echo("download: php download_pcap.php <mac> \n");
    exit(1);
}

$mac = $argv[1];
downloadPcapFile($mac);

