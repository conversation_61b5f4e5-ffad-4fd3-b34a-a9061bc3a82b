<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

include_once __DIR__ . "/../../util/model.php";
include_once __DIR__ . "/../../util/string.php";
include_once __DIR__ . "/../../util/computed.php";
include_once __DIR__ . "/../../util/time.php";

include_once __DIR__ . "/../basic/user.php";

class CManage
{
    private $role = ['1' => 'Manage', '2' => 'Property worker', '3' => 'Installer', '4' => 'Personal Manage'];
    //等级添加限制
    private $grade = ['1' => ['1' => '11'], '11' => ['2' => '21', '3' => '21', '4' => '22']];

    /**
     * @msg: 添加管理员
     * @services: sip(addCom)
     * @model: chargePlan.addCom,chargePlan.addPer
     */
    public function add()
    {
        $params = [
            "userAlias" => "",
            "Account" => "",
            "Role" => "",
            "Email" => "",
            "Phone" => "",
            "Info" => "",
            "TimeZone" => "",
            "Community" => "",
            "HouseCount" => "",
            "EnableValidTimeSetting" => "",
            "EnableCountSetting" => "",
            "ChargeMode" => "",
            "SipType" => "",
            "Language" => "",
            "Confusion" => "",
            "IsEncryptPin" => "",
            "IsVillaMonitor" => "",
            "ProjectFeature" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $account = $params["Account"];
        $role = $params["Role"];
        $email = $params["Email"];
        $phone = $params["Phone"];
        $info = $params["Info"];
        $timeZone = $params["TimeZone"];
        $communityID = $params["Community"];
        $houseCount = $params["HouseCount"];
        $enableValidTimeSetting = $params["EnableValidTimeSetting"];
        $enableCountSetting = $params["EnableCountSetting"];
        $chargeMode = $params["ChargeMode"];
        $sipType = $params["SipType"];
        $lang = $params["Language"];
        $confusion = $params["Confusion"];
        $isEncryptPin = $params["IsEncryptPin"];
        // 6.2dis新增室内机、高级功能配置
        $isVillaMonitor = $params["IsVillaMonitor"];
        $projectFeature = $params["ProjectFeature"];
        $special = 0;
        $group = 0;
        $this->log->actionLog("#model#manage#add#user=$user;account=$account;role=$role;email=$email;info=$info;timeZone=$timeZone;communityID=$communityID;
        houseCount=$houseCount;enableValidTimeSetting=$enableValidTimeSetting;enableCountSetting=$enableCountSetting;chargeMode=$chargeMode;sipType=$sipType;sipType=$sipType;lang=$lang");
        $this->addCom($account, $role, $email, $phone, $info, $timeZone, $communityID, $houseCount, $enableValidTimeSetting, $enableCountSetting, $special, $group, $user, $lang, $chargeMode, $sipType, $confusion, $isEncryptPin, $isVillaMonitor);
        if ($role == SUPERGRADE) {
            $accountId = $this->db->querySList("select ID from Account where Account = :Account", [":Account" => $account])[0]['ID'];
            $this->bindFeaturePlan($accountId, $projectFeature["community"]);
            $this->bindFeaturePlan($accountId, $projectFeature["office"]);
        }
    }

    /**
     * @msg: 删除dis原有高级功能方案
     * @param $accountId: dis的ID号
     */
    public function unBindFeaturePlan($accountId)
    {
        $this->db->delete2ListWKey("ManageFeature", "AccountID", $accountId);
    }

    /**
     * @msg: dis绑定高级功能
     * @param $accountId: dis的ID号，$featurePlan: 高级功能方案
     */
    public function bindFeaturePlan($accountId, $featurePlan)
    {
        foreach ($featurePlan['Plan'] as $key => $val) {
            $this->db->insert2List('ManageFeature', [
                ':AccountID' => $accountId,
                ":FeatureID" => $val['ID'],
                ":FeeUUID" => $featurePlan['Fee'][$key]['UUID']
            ]);
        }
    }

    public function addCom(
        $account,
        $role,
        $email,
        $phone,
        $info,
        $timeZone,
        $community,
        $houseCount,
        $enableValidTimeSetting,
        $enableCountSetting,
        $special,
        $group,
        $user,
        $lang,
        $chargeMode = 0,
        $sipType = 3,
        $confusion = 0,
        $isEncryptPin = 0,
        $isVillaMonitor = 1
    ) {
        global $cMessage;
        $houseCount = 10000;
        $enableValidTimeSetting = $enableValidTimeSetting == 1 ? 1 : 0;
        $enableCountSetting = $enableCountSetting == 1 ? 1 : 0;

        $smartHomeTaskOptions = [];

        $simtArray = $this->db->querySList("select ID from Account where Account = :Account or Account = :Account2", [":Account" => $account, ":Account2" => $account . "-PersonalManage"]);
        if (count($simtArray) > 0) {
            $cMessage->echoErrorMsg(StateAccountExits);
        }
        // $regex = '/^\d*$/';
        // if(preg_match($regex, $account)) $cMessage->echoErrorMsg(StateAccountNIncorrect);
        $myData = $this->db->queryAllList("Account", ["equation" => [":Account" => $user]])[0];
        $parentId = $myData['ID'];
        $grade = $myData['Grade'];
        $parentUUID = $myData['UUID'];

        // 生成uuid
        $accountUUID = \util\string\uuid();

        if ($grade == SUPERGRADE) {
            $sips = $this->db->queryAllList("Account", ["equation" => [":Grade" => AREAGRADE]]);
            $sipPrefix = $this->services["sip"]->assignSip($sips, '4');
            $uuid = \util\string\uuid();
            $now = \util\computed\getNow();
            $this->db->insert2List('SipPrefix', [
                ":UUID" => $uuid,
                ":CreateTime" => $now,
                ":UpdateTime" => $now,
                ":AccountUUID" => $accountUUID,
                ":SipPrefix" => $sipPrefix,
                ":Flag" => 0
            ]);
        }

        if ($grade == AREAGRADE) {
            $role = 4;
        }
        $password = \util\string\generatePw(8);

        $bindArray = [":Account" => $account, ":Passwd" => md5($password), ":Grade" => $this->grade[$grade][$role],":UUID"=>$accountUUID,
            ":Role" => $role, ':ParentID' => $parentId, ':Location' => $community, ':Email' => $email, ':Phone' => $phone, ":Language" => $lang,
            ':Info' => $info, ':TimeZone' => $timeZone, ":Special" => $special, ":ManageGroup" => $group, ":ParentUUID" => $parentUUID];

        if ($grade == SUPERGRADE) {
            $bindArray[':ChargeMode'] = $chargeMode;
        }

        if ($grade == AREAGRADE) {
            $bindArray[':HouseCount'] = $houseCount;
            $bindArray[':EnableValidTimeSetting'] = $enableValidTimeSetting;
            $bindArray[':EnableCountSetting'] = $enableCountSetting;
            $bindArray[':ChargeMode'] = $chargeMode;
            $bindArray[':SipType'] = $sipType;
            $bindArray[':Flags'] = $confusion;
            $bindArray[':SendRenew'] = 0;
        }

        $this->db->insert2List("Account", $bindArray);
        $id = $this->db->lastInsertId();
        // 直接添加的管理员
        array_push($smartHomeTaskOptions, ['Key' => $account, 'Type' => $grade == SUPERGRADE ? 0 : 1]);

        if ($grade == AREAGRADE) {
            $this->db->update2ListWID("Account", [":ID" => $id, ":ManageGroup" => $id]);
            // 6.2新建社区绑定featurePlan
            //默认社区绑定dis第一个featurePlan
//            $sql="SELECT * FROM ManageFeature WHERE AccountID = :AccountID";
//            $featurePlan = $this->db->querySList($sql, [":AccountID"=>$parentId])[0]["FeatureID"];
//            $communityAccount = $this->addAutoCommunity("Community1", $parentId, $sip, [
//                "timeZone" => $timeZone,
//                "group" => $id,
//                "chargeMode" => "0",
//                "landline" => 1,
//                "customizeForm" => 3,
//                "sendExpireEmailType" => 2,
//                "confusion" => $confusion,
//                "sendRenew" => 0,
//                "featurePlan" => $featurePlan
//            ]);
//            array_push($smartHomeTaskOptions, ['Key' => $communityAccount, 'Type' => 2]);
            // V4.6新增installer时，增加bill信息
            $this->db->insert2List("InstallerBillingInfo", [":Account" => $account]);
            // installer 收费模式审计日志
            $this->auditLog->setLog($chargeMode == 1 ? AuditCodeChargeModelByInstaller : AuditCodeChargeModelByUser, $this->env, [], $account);
            $this->auditLog->setLog([AuditCodeConnectUDP, AuditCodeConnectTCP, AuditCodeConnectTLS, AuditCodeConnectDefault][$sipType], $this->env, [], $account);
        }

        if ($grade == SUPERGRADE) {
            //添加是否隐藏密码为****的开关值
            $this->db->insert2List("DistributorInfo", [":Account" => $account, ":IsEncryptPin" => $isEncryptPin, ":IsVillaMonitor" => $isVillaMonitor]);

            //添加区域管理员自动生成一个个人终端管理员
            $password2 = \util\string\generatePw(8);
            // V4.4 区别一般install Role改为5
            $this->db->insert2List("Account", [":Account" => $account . "-PersonalManage", ":Passwd" => md5($password2), ":Grade" => PERSONGRADE, ":Role" => 5,
            ":ParentID" => $id, ":Location" => "", ":Special" => 1, ":TimeZone" => $timeZone, ":Language" => $lang, ":UUID" => \util\string\uuid(), ":ParentUUID" => $accountUUID]);
            $id = $this->db->lastInsertId();
            array_push($smartHomeTaskOptions, ['Key' => $account . "-PersonalManage", 'Type' => 1]);
            $this->db->update2ListWID("Account", [":ID" => $id, ":ManageGroup" => $id]);

            // V4.6新增installer时，增加bill信息
            $this->db->insert2List("InstallerBillingInfo", [":Account" => $account . "-PersonalManage"]);
            $this->db->insert2List("InstallerBillingInfo", [":Account" => $account]);
        }
        \util\computed\setGAppData(["password" => $password]);
        $this->auditLog->setLog($grade == SUPERGRADE ? AuditCodeAddDis : AuditCodeAddInstaller, $this->env, [$account], $account);
        $this->auditLog->setLog(AuditCodeSubManageTime, $this->env, ["GTM$timeZone"], $account);

        \util\computed\setSmartHomeTask($smartHomeTaskOptions, false);
    }

    // $location, $parentId, $sip, $timeZone, $group, $chargeMode = "0", $landline = 1, $customizeForm = 3, $sendExpireEmailType = 2, $confusion = 0, $sendRenew = 0, $featurePlan
    public function addAutoCommunity($location, $parentId, $sip, $options)
    {
        global $cMessage;
        $timeZone = $options['timeZone'];
        $group = $options['group'];
        $chargeMode = $options['chargeMode'] !== null ? $options['chargeMode'] : '0';
        $landline = $options['landline'] !== null ? $options['landline'] : 1;
        $customizeForm = $options['customizeForm'] !== null ? $options['customizeForm'] : 3;
        $sendExpireEmailType = $options['sendExpireEmailType'] ?: 2;
        $confusion = $options['confusion'] !== null ? $options['confusion'] : 0;
        $sendRenew = $options['sendRenew'] !== null ? $options['sendRenew'] : 0;
        $featurePlan = $options['featurePlan'];
        $enableSmartHome = $options['enableSmartHome'];

        $this->log->actionLog("#model#manage#addAutoCommunity#location=$location;parentId=$parentId;sip=$sip;
        options=". json_encode($options));
        //自动生成账号
        $comAccount = $this->getAAutoAccount();
        if ($comAccount == false) {
            $cMessage->echoErrorMsg(StateAddFail);
        }

        $password = \util\string\generatePw(8);
        // 生成uuid
        $accountUUID = \util\string\uuid();
        $bindArray = [
            ":Account" => $comAccount, ":Passwd" => md5($password), ":Grade" => COMMUNITYGRADE,
            ":Role" => 2, ':ParentID' => $parentId, ':Location' => $location, ':Email' => "",
            ':Phone' => "", ':Info' => "", ':TimeZone' => $timeZone,
            ":Special" => 1, ":ManageGroup" => $group, ":ChargeMode" => $chargeMode,
            ":CustomizeForm" => $customizeForm, ":SendExpireEmailType" => $sendExpireEmailType,":UUID" => $accountUUID,
            ":Flags" => $confusion, ":SendRenew" => $sendRenew];
        $this->db->insert2List("Account", $bindArray);

        $id = $this->db->lastInsertId();
        // 2021/09/23 zh 新增featureplan关联，6.2需求
        $this->db->insert2List("ManageFeature", [":AccountID" => $id,":FeatureID"=>$featurePlan]);

        // 2021/05/26 kxl 新增IsNew字段，6.1版本开始添加的都是新社区
        $bindArray = [":AccountID" => $id, ":Switch" => \util\computed\bitOperation($landline+4, $enableSmartHome, 5), ":IsNew" => 1];

        // 2021/09/30 zh 6.2需求高级收费如果0元直接刷新到2299年
        $sql = "SELECT FeeUUID FROM ManageFeature WHERE AccountID = :AccountID AND FeatureID = :FeatureID";
        $feeUUID = $this->db->querySList($sql, [":AccountID"=>\util\role\getParent($id, 2, ["ID"], "Account")["ID"],":FeatureID"=>$featurePlan])[0]['FeeUUID'];
        $chargeService = $this->services["billsysUtil"];
        $fee = $chargeService->getFeatureFee(["FeeUUID"=>$feeUUID])['data'][0];
        if ($fee["FeatureFee"] != 0) {
            $bindArray[":FeatureExpireTime"] = null;
        }

        $this->db->insert2List("CommunityInfo", $bindArray);
        return $comAccount;
    }

    public function getAAutoAccount()
    {
        $maxTimes = 10;
        for ($i = 0; $i < $maxTimes; $i++) {
            $account = \util\string\randString(7);
            if (count($this->db->queryAllList("Account", ["equation" => [":Account" => $account]])) > 0) {
                continue;
            }
            return $account;
        }
        return false;
    }

    /**
     * @msg: 删除管理员
     * @model: chargePlan.delete
     */
    public function delete()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "userAlias" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $id = $params["ID"];
        $this->log->actionLog("#model#manage#delete#id=$id;userId=$userId;user=$user");

        $data = $this->db->querySList("select Grade,Account,Special,ManageGroup,UUID from Account where ID = :ID", [":ID" => $id]);
        if (count($data) == 0) {
            return;
        }
        $data = $data[0];

        $gradeUser = $data['Grade'];
        $accountUser = $data['Account'];
        $special = $data['Special'];
        $group = $data['ManageGroup'];
        $isSmartHome = 0;
        //特殊账号无法删除
        if ($special == 1) {
            $cMessage->echoErrorMsg(StateDeleteFail);
        }

        //是个人终端管理员,社区管理员时，有用户不能删
        //V4.3修改，查询账号组是否存在设备用户等
        if ($gradeUser == 22 || $gradeUser == 21) {
            $accounts = $this->db->queryAllList("Account", ["equation" => [":ManageGroup" => $group]]);
            foreach ($accounts as $value) {
                $this->deviceUserMACCheck($value["ID"], $value["Grade"]);
            }
            // V4.6删除bill信息
            if ($gradeUser == 22) {
                $this->db->delete2ListWKey("InstallerBillingInfo", "Account", $accountUser);
            }
        } else {
            // 删除为区域管理员时，有子账户不能删，用导入设备不能删
            $subManages = $this->db->querySList("select count(*) from Account where ParentID = :ID AND Special = 0", [":ID" => $id])[0]["count(*)"];
            if ($subManages > 0) {
                $cMessage->echoErrorMsg(StateBindUser);
            }
            $devices = count($this->db->queryAllList("DeviceForRegister", ["equation" => [":MngID" => $id]]));
            if ($devices > 0) {
                $cMessage->echoErrorMsg(StateBindMAClibrary);
            }
            $subUsers = count($this->db->querySList("select A.ID from Account A left join PersonalAccount P on P.ParentID = A.ID where P.Role = 10 AND A.ParentID = :ParentID", [":ParentID" => $id]));
            if ($subUsers > 0) {
                $cMessage->echoErrorMsg(StateUserBindUser);
            }

            $this->db->delete2ListWKey("Account", "ParentID", $id);
            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData(["ID" => $id]);
            $this->models["chargePlan"]->delete();
            $resetParams();
            // V5.3删除sipLastAccount
            $this->db->delete2ListWKey("SipLastAccount", "MngAccount", $accountUser);
            // V6.2.1 删除SipPrefix
            $this->db->delete2ListWKey("SipPrefix", "AccountUUID", $data['UUID']);
            $this->db->delete2ListWKey("InstallerBillingInfo", "Account", $accountUser);
            $this->db->delete2ListWKey("InstallerBillingInfo", "Account", $accountUser . "-PersonalManage");
            $this->auditLog->setLog(AuditCodeDeleteDis, $this->env, [$accountUser], $accountUser);

            // V6.1删除表DistributorInfo对应的数据
            $this->db->delete2ListWKey("DistributorInfo", "Account", $accountUser);
            // v6.2删除表ManageFeature对应的数据
            $this->db->delete2ListWKey("ManageFeature", "AccountID", $id);
        }
        if ($gradeUser == 21) {
            $isSmartHome = \util\computed\getSpecifyBitLE($this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $id])[0]['Switch'], 5);
        }
        $options = [['Key' => $accountUser, 'Type' => ['21' => 5, '22' => 4, '11' => 3][$gradeUser] ,'IsSmartHome' => $isSmartHome]];

        $this->db->delete2ListWID("Account", $id);
        //V4.3删除为installer 同时删除账号组
        if (($gradeUser == PERSONGRADE || $gradeUser == COMMUNITYGRADE) && $group == $id) {
            $ids = $this->db->queryAllList("Account", ["equation" => [":ManageGroup" => $group]]);
            foreach ($ids as $val) {
                $isSmartHome = \util\computed\getSpecifyBitLE($this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $val['ID']])[0]['Switch'], 5);
                array_push($options, ['Key' => $val['Account'], 'Type' => 5, 'IsSmartHome' => $isSmartHome]);
                $this->db->delete2ListWKey("CommunityInfo", "AccountID", $val['ID']);
            }
            // 删除物业
            $propertyIds = $this->db->queryAllList("Account", ["equation" => [":ParentID" => $group, ":Grade" => PROPERTYMANAGE]]);
            foreach ($propertyIds as $propertyId) {
                $this->db->delete2ListWKey("PropertyInfo", "AccountID", $propertyId['ID']);
                $this->db->delete2ListWKey("PropertyMngList", "PropertyID", $propertyId['ID']);
                $this->db->delete2ListWKey("PropertyBillingInfo", "Account", $propertyId["Account"]);
                $this->db->delete2ListWKey("Account", "ID", $propertyId['ID']);
            }
            $this->db->delete2ListWKey("Account", "ManageGroup", $group);
            $this->auditLog->setLog(AuditCodeDeleteInstaller, $this->env, [$accountUser], $accountUser);
        }
        \util\model\deleteToken($accountUser);
        \util\computed\setSmartHomeTask($options, false);
    }

    public function deviceUserMACCheck($id, $gradeUser)
    {
        $this->log->actionLog("#model#deviceUserMACCheck#delete#id=$id");
        global $cMessage;
        $mainRole = implode(",", MAINROLE);
        $subs = count($this->db->querySList("select ID from PersonalAccount where ParentID = :ID AND Role in ($mainRole)", [":ID" => $id]));
        if ($subs > 0) {
            $cMessage->echoErrorMsg(StateBindUser);
        }

        //社区/办公管理员时有设备不能删
        if ($gradeUser == COMMUNITYGRADE || $gradeUser == OFFICEGRADE) {
            $devices = $this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $id]]);
            if (count($devices) != 0) {
                $cMessage->echoErrorMsg(StateBindDevice);
            }
        }
        if ($gradeUser == PERSONGRADE) {
            $devices = $this->db->querySList("select P.ID from PersonalDevices P join Account A on A.Account = P.Community where A.ID = :ID", [":ID" => $id]);
            if (count($devices) != 0) {
                $cMessage->echoErrorMsg(StateBindDevice);
            }
        }
        $data = $this->db->queryAllList("DeviceForRegister", ["equation" => [":PerMngID" => $id]]);
        if (count($data) != 0) {
            $cMessage->echoErrorMsg(StateBindMAClibrary);
        }
    }

    /**
     * @msg:
     * @model: chargePlan.setManage，chargePlan.setPer
     */
    public function edit()
    {
        $params = [
            "userAliasId" => "",
            "Role" => "",
            "Email" => "",
            "Phone" => "",
            "Info" => "",
            "TimeZone" => "",
            "Community" => "",
            "HouseCount" => "",
            "EnableValidTimeSetting" => "",
            "EnableCountSetting" => "",
            "ChargeMode" => "",
            "SipType" => "",
            "ID" => "",
            "Confusion" => "",
            "IsEncryptPin" => "",
            "IsVillaMonitor" => "",
            "ProjectFeature" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $role = $params["Role"];
        $email = $params["Email"];
        $phone = $params["Phone"];
        $info = $params["Info"];
        $timeZone = $params["TimeZone"];
        $community = $params["Community"];
        $houseCount = $params["HouseCount"];
        $enableValidTimeSetting = $params["EnableValidTimeSetting"];
        $enableCountSetting = $params["EnableCountSetting"];
        $chargeMode = $params["ChargeMode"];
        $sipType = $params["SipType"];
        $id = $params["ID"];
        $confusion = $params["Confusion"];
        $isEncryptPin = $params["IsEncryptPin"];
        // 6.2dis新增室内机、高级功能配置
        $isVillaMonitor = $params["IsVillaMonitor"];
        $projectFeature = $params["ProjectFeature"];

        if ($projectFeature) {
            $this->unBindFeaturePlan($id);
            $this->bindFeaturePlan($id, $projectFeature["community"]);
            $this->bindFeaturePlan($id, $projectFeature["office"]);
        }

        $houseCount = $houseCount > 1000 ? 1000 : $houseCount;
        $enableValidTimeSetting = $enableValidTimeSetting == 1 ? 1 : 0;
        $enableCountSetting = $enableCountSetting == 1 ? 1 : 0;

        $this->log->actionLog("#model#manage#edit#userId=$userId;role=$role;email=$email;info=$info;timeZone=$timeZone;community=$community;id=$id;
        houseCount=$houseCount;enableValidTimeSetting=$enableValidTimeSetting;enableCountSetting=$enableCountSetting;chargeMode=$chargeMode;sipType=$sipType;sipType=$sipType;");

        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        $data = $this->db->querySList('select Account,TimeZone,Flags,ManageGroup,ChargeMode,SipType from Account where ID = :ID', [":ID" => $id])[0];
        $grade = $myData["Grade"];

        $editConfusion = $confusion == 0 ? intval($data["Flags"]) & intval($confusion) : intval($data["Flags"]) | intval($confusion);

        $bindArray = [":ID" => $id, ":Role" => $role, ':ParentID' => $userId, ':Location' => $community, ':Email' => $email, ':Phone' => $phone,
            ':Info' => $info, ':TimeZone' => $timeZone, ":HouseCount" => $houseCount, ":EnableValidTimeSetting" => $enableValidTimeSetting, ":EnableCountSetting" => $enableCountSetting,
            ":ChargeMode" => $chargeMode, ":SipType" => $sipType, ":Flags" => $editConfusion];
        // V6.1 kxl 开放超级管理员修改dis的收费模式
        // if ($grade == SUPERGRADE) {
        //     unset($bindArray[":ChargeMode"]);
        // }

        if ($grade == AREAGRADE) {
            $this->db->update2ListWKey("Account", [":Flags" => $editConfusion, ":ManageGroup" => $data["ManageGroup"]], "ManageGroup");
            // 收费类型审计日志
            if ($data['ChargeMode'] != $chargeMode) {
                $this->auditLog->setLog(
                    $chargeMode == 1 ? AuditCodeChargeModelByInstaller : AuditCodeChargeModelByUser,
                    $this->env,
                    [],
                    $data['Account']
                );
            }
            // 连接类型审计日志
            if ($data['SipType'] != $sipType) {
                $this->auditLog->setLog(
                    [AuditCodeConnectUDP, AuditCodeConnectTCP, AuditCodeConnectTLS, AuditCodeConnectDefault][$sipType],
                    $this->env,
                    [],
                    $data['Account']
                );
            }
        }

        $this->db->update2ListWID("Account", $bindArray);

        //V6.1 修改 需要修改DistributorInfo的PIN是否加密
        if ($grade == SUPERGRADE) {
            $updateAccount = $this->db->querySList("select Account from Account where ID = :ID", [":ID" => $id])[0]["Account"];
//            $disAccount = $this->db->querySList("select count(*) from DistributorInfo where Account = :Account", [":Account" => $updateAccount])[0]["count(*)"];
//            if ($disAccount == 0) {
//                $this->db->insert2List("DistributorInfo", [":Account" => $updateAccount, ":IsEncryptPin" => $isEncryptPin]);
//            } else {
            $this->db->update2ListWKey("DistributorInfo", [":IsEncryptPin" => $isEncryptPin, ":IsVillaMonitor" => $isVillaMonitor,":Account" => $updateAccount], "Account");
//            }
        }

        $this->auditLog->setLog($grade == SUPERGRADE ? AuditCodeEditDis : AuditCodeEditInstaller, $this->env, [$data['Account']], $data['Account']);
        if ($data['TimeZone'] != $timeZone) {
            $this->auditLog->setLog(AuditCodeSubManageTime, $this->env, ["GTM$timeZone"], $data['Account']);
        }
    }

    public function resetPw()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "Account" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $account = $params["Account"];
        $this->log->actionLog("#model#manage#resetPw#userId=$userId;account=$account");
        $data = $this->db->querySList("select ID from Account where Account = :Account and ParentID = :ParentID", [":Account" => $account, ":ParentID" => $userId]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $newPassWd = \util\string\generatePw(8);
        $this->db->update2ListWKey("Account", [":Passwd" => md5($newPassWd), ":Account" => $account], "Account");

        \util\model\deleteToken($account);
        \util\computed\setGAppData(["password" => $newPassWd]);
    }

    public function changeAreaToPer()
    {
        global $cMessage;
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        $grade = $myData["Grade"];
        if ($grade == PERSONGRADE) {
            $special = $myData["Special"];
            if ($special == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }

            $data = $this->db->queryAllList("Account", ["equation" => [":ID" => $myData["ParentID"]]])[0];
            $name = $data["Account"];
            $timeZone = $data["TimeZone"];
            $role = $data["Role"];
            $changeGrade = AREAGRADE;
        } else {
            $data = $this->db->queryAllList("Account", ["equation" => [":ParentID" => $userId, ":Special" => 1]])[0];
            $name = $data["Account"];
            $timeZone = $data["TimeZone"];
            $role = $data["Role"];
            $changeGrade = PERSONGRADE;
        }

        include_once __DIR__ . "/../../database/redis.php";
        $token = \util\string\randString(TOKENLENGTH);
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        $oldToken = $redis->get($name);
        if ($oldToken !== null) {
            $redis->del($oldToken);
        }

        //存新token
        $redis->setex($token, TOKENVALIDITYTIME, $name);
        $redis->setex($name, TOKENVALIDITYTIME, $token);
        \util\computed\setGAppData(['token' => $token, 'grade' => $changeGrade, 'account' => $name, 'timeZone' => $timeZone, 'Role' => $role]);
    }

    public function changePerToCom()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "userAlias" => "",
            "Account" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $account = $params["Account"];

        $data = $this->db->querySList("select A.* from Account A join Account B on A.ManageGroup = B.ManageGroup where A.Account = :Account and B.ID = :ID", [":Account" => $account, ":ID" => $userId]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        include_once __DIR__ . "/../../database/redis.php";
        $token = \util\string\randString(TOKENLENGTH);
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        $oldToken = $redis->get($account);
        $this->log->actionLog("#model#manage#changePerToCom#account=$account;oldToken=$oldToken");
        if ($oldToken !== null) {
            $redis->del($oldToken);
        }

        $oldToken = $redis->get($user);
        $this->log->actionLog("#model#manage#changePerToCom#user=$user;oldToken=$oldToken");
        if ($oldToken !== null) {
            $redis->del($oldToken);
        }

        $this->log->actionLog("#model#manage#changePerToCom#account=$account;newToken=$token");
        //存新token
        $redis->setex($token, TOKENVALIDITYTIME, $account);
        $redis->setex($account, TOKENVALIDITYTIME, $token);
        \util\computed\setGAppData(['token' => $token, 'grade' => $data[0]["Grade"], "Role" => $data[0]["Role"]]);
    }

    public function queryArea()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        $where = "";
        $bindArray = [];
        switch ($serchKey) {
            case 'Account':
                $where = "$where AND A.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Email':
                $where = "$where AND A.Email LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $total = $this->db->querySList("select count(*) as total from Account A where Grade = " . AREAGRADE . " $where", $bindArray)[0]["total"];
        //V6.1修改返回数据需要连表查询得到IsEncryptPin
        $data = $this->db->querySList("select A.Account,A.Role,A.ID,A.Email,A.Info,A.Location,A.Phone,A.TimeZone,A.ChargeMode,B.IsEncryptPin,B.IsVillaMonitor from Account A  left join DistributorInfo B on A.Account=B.Account where Grade = " . AREAGRADE . " $where order by ID DESC limit $offset,$rows", $bindArray);
        // 6.2查询绑定featureplan
        $chargeService = $this->services["billsysUtil"];
        foreach ($data as &$value) {
            $communityFeature = $this->db->querySList("select M.FeatureID as ID, F.Name, '0' as FeaturePlanType from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where AccountID = :ID and F.Type = 0", [":ID"=>$value["ID"]]);
            $officeFeature = $this->db->querySList("select M.FeatureID as ID, F.Name, '1' as FeaturePlanType from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where AccountID = :ID and F.Type = 1", [":ID"=>$value["ID"]]);
            $value['ProjectFeature'] = ["community" => ["Plan" => [], "Fee" => []], "office" => ["Plan" => [], "Fee" => []]];
            if ($communityFeature) {
                $value['ProjectFeature']['community']['Plan'] = $communityFeature;
                $value['ProjectFeature']['community']['Fee'] = $this->db->querySList("select M.FeeUUID as UUID from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID and F.Type = 0", [":ID" => $value["ID"]]);
                foreach ($value['ProjectFeature']['community']['Fee'] as &$fee) {
                    $res = $chargeService->getFeatureFee(["FeeUUID"=>$fee['UUID']])['data'][0];
                    $fee['FeatureName'] = $res['FeatureName'];
                }
                unset($fee);
            }
            if ($officeFeature) {
                $value['ProjectFeature']['office']['Plan'] = $officeFeature;
                $value['ProjectFeature']['office']['Fee'] = $this->db->querySList("select M.FeeUUID as UUID from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID and F.Type = 1", [":ID" => $value["ID"]]);
                foreach ($value['ProjectFeature']['office']['Fee'] as &$fee) {
                    $res = $chargeService->getFeatureFee(["FeeUUID"=>$fee['UUID']])['data'][0];
                    $fee['FeatureName'] = $res['FeatureName'];
                }
                unset($fee);
            }
        }

        unset($value);     // 释放$value的引用传递
        $rows = [];
        foreach ($data as $value) {
            $curKey = array();
            $curKey['Community'] = $value['Location'];
            $curKey['Account'] = $value['Account'];
            $curKey['ID'] = $value['ID'];
            $curKey['LocationID'] = $value['LocationID'];
            $curKey['Role'] = $this->role[$value['Role']];
            $curKey['Email'] = $value['Email'];
            $curKey['Remarks'] = $value['Info'];
            $curKey['Phone'] = $value['Phone'];
            array_push($rows, $curKey);
        }

        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $rows, "detail" => $data]]);
    }

    public function queryPerManageForSup()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [];
        switch ($serchKey) {
            case 'Account':
                $where = "$where AND A.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Email':
                $where = "$where AND A.Email LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $sql = "select A.ID,A.Account,A.Email,A.Location as Community,A.Location,A.Phone,A.Role,A.TimeZone,A.HouseCount,A.EnableValidTimeSetting,A.EnableCountSetting,A.ChargeMode,A.SipType";

        $total = count($this->db->querySList("$sql,B.Account as Manager from Account A join Account B on A.ParentID = B.ID where (A.grade = " . PERSONGRADE . " or A.grade = " . COMMUNITYGRADE . ") and A.Special = 0 and A.ID = A.ManageGroup $where", $bindArray));

        $data = $this->db->querySList("$sql,B.Account as Manager from Account A join Account B on A.ParentID = B.ID where (A.grade = " . PERSONGRADE . " or A.grade = " . COMMUNITYGRADE . ") and A.Special = 0 and A.ID = A.ManageGroup $where order by ID DESC limit $offset,$rows", $bindArray);

        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $data, "detail" => $data]]);
    }

    public function queryPerManageForArea()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = " and A.ParentID = :ParentID";
        $bindArray = [":ParentID" => $userId];
        switch ($serchKey) {
            case 'Account':
                $where = "$where AND A.Account LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Email':
                $where = "$where AND A.Email LIKE :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $sql = "select A.ID,A.Account,A.Email,A.Location as Community,A.Location,A.Phone,A.Role,A.TimeZone,A.HouseCount,A.EnableValidTimeSetting,A.EnableCountSetting,A.ChargeMode,A.SipType,A.Flags & 1 as Confusion";

        $total = count($this->db->querySList("$sql,B.Account as Manager from Account A join Account B on A.ParentID = B.ID where (A.grade = " . PERSONGRADE . " or A.grade = " . COMMUNITYGRADE . ") and A.Special = 0 and A.ID = A.ManageGroup $where", $bindArray));

        $data = $this->db->querySList("$sql,B.Account as Manager from Account A join Account B on A.ParentID = B.ID where (A.grade = " . PERSONGRADE . " or A.grade = " . COMMUNITYGRADE . ") and A.Special = 0 and A.ID = A.ManageGroup $where order by ID DESC limit $offset,$rows", $bindArray);

        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $data, "detail" => $data]]);
    }

    public function queryAllArea()
    {
        $params = [
            "Key" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $key = $params["Key"];
        $data = $this->db->queryAllList("Account", ["equation" => [":Grade" => AREAGRADE], "like" => [":Account" => "%$key%"], "e2Li" => "AND"]);
        foreach ($data as &$value) {
            unset($value["Passwd"]);
            unset($value["Special"]);
            $value["Account2"] = $value["Account"];
            unset($value["Account"]);
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryComGroupList()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "installerId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $installerID = $params["installerId"];
        if ($installerID){
            $user=$this->db->querySList("select ID from Account WHERE UUID= :UUID and ParentID=:ParentID",[":UUID"=>$installerID,":ParentID"=>$params["userAliasId"]]);
            if (count($user)>0){
                $userId= $user[0]['ID'];
            }else{
                $cMessage->echoErrorMsg(StateNotPermission);

            }
        }else{
            $userId =  $params["userAliasId"];
        }

        $sql = "select C.*,A.Location,A.UUID,A.Account,A.CustomizeForm,A.TimeZone,A.ID,A.ChargeMode,A.SendExpireEmailType,A.SendRenew,D.FeatureID as FeaturePlan from Account A left join Account B on A.ManageGroup = B.ManageGroup left join CommunityInfo C on A.ID = C.AccountID left join ManageFeature D on A.ID = D.AccountID where B.ID = :ID and A.Grade = " . COMMUNITYGRADE;
        $data = $this->db->querySList($sql, [":ID" => $userId]);

        foreach ($data as &$value) {
            $id = $value["ID"];
            $value["DeviceCount"] = count($this->db->querySList("select ID from Devices where MngAccountID = :ID", [":ID" => $id]));
            $value["UserCount"] = count($this->db->querySList("select ID from PersonalAccount where ParentID = :ID and Role = 20 and Special = 0
                        union select P.ID from PersonalAccount P join PersonalAccount P2 on P.ParentID = P2.ID where P2.Special = 0 and P.Role = 21 and P2.ParentID = :ID", [":ID" => $id]));
            $workData = $this->db->querySList("select I.FirstName,I.LastName from PropertyInfo I left join PropertyMngList M on I.AccountID = M.PropertyID where M.CommunityID = :ID", [":ID" => $id]);
            $works = [];
            foreach ($workData as $val) {
                array_push($works, $val["FirstName"] . " " . $val["LastName"]);
            }

            $works = implode(",", $works);
            $value["Works"] = $works;
            $value["EnableLandline"] = intval($value["Switch"]) & 1;
            $value['EnableSmartHome'] = \util\computed\getSpecifyBit(intval($value["Switch"]), 4);
            $value['TimeZoneNumber'] = explode(' ', $value['TimeZone'])[0];
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    public function addCommunity()
    {
        $params = [
            "userAliasId" => "",
            "Location" => "",
            "Street" => "",
            "City" => "",
            "PostalCode" => "",
            "Country" => "",
            "States" => "",
            "TimeZone" => "",
            "AptPinType" => "",
            "ChargeMode" => "",
            "NumberOfApt" => "",
            "EnableLandline" => "",
            "CustomizeForm" => "",
            "SendExpireEmailType" => "",
            "SendRenew" => "",
            "FeaturePlan"=>"",
            "EnableSmartHome"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $location = $params["Location"];
        $street = $params["Street"];
        $city = $params["City"];
        $postalCode = $params["PostalCode"];
        $country = $params["Country"];
        $states = $params["States"];
        $timeZone = $params["TimeZone"];
        $keyType = $params["AptPinType"];
        $chargeMode = $params["ChargeMode"];
        $numberOfApt = $params["NumberOfApt"];
        $landline = $params["EnableLandline"];
        $customizeForm = $params["CustomizeForm"];
        $sendExpireEmailType = $params["SendExpireEmailType"] ?: 2;
        $sendRenew = $params["SendRenew"];
        $featurePlan = $params["FeaturePlan"];
        $enableSmartHome = $params["EnableSmartHome"] ?: 0;

        $this->log->actionLog("#model#manage#addCommunity#params=" . json_encode($params));
        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        // $communityAccount = $this->addAutoCommunity($location, $myData["ParentID"], $myData["SipPrefix"], $timeZone, $myData["ManageGroup"], $chargeMode, $landline, $customizeForm, $sendExpireEmailType, 0, $sendRenew, $featurePlan);
        $communityAccount = $this->addAutoCommunity($location, $myData["ParentID"], 0, [
            "timeZone" => $timeZone,
            "group" => $myData["ManageGroup"],
            "chargeMode" => $chargeMode,
            "landline" => $landline,
            "customizeForm" => $customizeForm,
            "sendExpireEmailType" => $sendExpireEmailType,
            "confusion" => 0,
            "sendRenew" => $sendRenew,
            "featurePlan" => $featurePlan,
            'enableSmartHome' => $enableSmartHome
        ]);
        $id = $this->db->lastInsertId();
        $this->db->update2ListWID("CommunityInfo", [":ID" => $id, ":Street" => $street, ":City" => $city, ":PostalCode" => $postalCode,
            ":Country" => $country, ":States" => $states, ":AptPinType" => $keyType, ":NumberOfApt" => $numberOfApt]);
        $this->auditLog->setLog(AuditCodeAddCommunity, $this->env, [$myData['Account'] . '-' . $location], $myData['Account']);
        $this->auditLog->setLog(AuditCodeSetCommunityTime, $this->env, ["GTM$timeZone"], $myData['Account']);
        $this->auditLog->setLog($chargeMode == 1 ? AuditCodeCommunityChargeModelByInstaller : AuditCodeCommunityChargeModelByUser, $this->env, [], $myData['Account']);
        \util\computed\setSmartHomeTask(['Key' => $communityAccount, 'Type' => 2]);
    }

    public function editCommunity()
    {
        $params = [
            "ID" => "",
            "Location" => "",
            "Street" => "",
            "City" => "",
            "PostalCode" => "",
            "Country" => "",
            "States" => "",
            "TimeZone" => "",
            "AptPinType" => "",
            "ChargeMode" => "",
            "CustomizeForm" => "",
            "SendExpireEmailType" => "",
            "SendRenew" => "",
            "EnableSmartHome" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $location = $params["Location"];
        $street = $params["Street"];
        $city = $params["City"];
        $postalCode = $params["PostalCode"];
        $country = $params["Country"];
        $states = $params["States"];
        $timeZone = $params["TimeZone"];
        $keyType = $params["AptPinType"];
        $chargeMode = $params["ChargeMode"];
        $customizeForm = $params["CustomizeForm"];
        $sendExpireEmailType = $params["SendExpireEmailType"] ?: 2;
        $sendRenew = $params["SendRenew"];
        $enableSmartHome = $params["EnableSmartHome"];

        $this->log->actionLog("#model#manage#editCommunity#params=" . json_encode($params));

        $oldData = $this->db->querySList('select TimeZone,ChargeMode from Account where ID=:ID', [":ID" => $id])[0];

        $communityInfo = $this->db->querySList('select Switch from CommunityInfo where AccountID=:ID', [":ID" => $id])[0];

        $this->db->update2ListWKey("CommunityInfo", [":AccountID" => $id, ":Street" => $street, ":City" => $city, ":PostalCode" => $postalCode,
            ":Country" => $country, ":States" => $states, ":AptPinType" => $keyType, ":Switch" => \util\computed\bitOperation($communityInfo['Switch'], $enableSmartHome, 5)], "AccountID");

        $this->db->update2ListWID("Account", [":ID" => $id, ":TimeZone" => $timeZone, ":Location" => $location, ":ChargeMode" => $chargeMode, ":CustomizeForm" => $customizeForm, ":SendExpireEmailType" => $sendExpireEmailType, ":SendRenew" => $sendRenew]);

        $installerData = $this->db->querySList('select A.Account from Account A join Account B on A.ID = B.ManageGroup where B.ID = :ID', [":ID" => $id])[0];


        $this->auditLog->setLog(AuditCodeEditCommunity, $this->env, [], $installerData['Account']);
        if ($oldData['TimeZone'] != $timeZone) {
            $this->auditLog->setLog(AuditCodeSetCommunityTime, $this->env, ["GTM$timeZone"], $installerData['Account']);
        }
        if ($oldData['ChargeMode'] != $chargeMode) {
            $this->auditLog->setLog($chargeMode == 1 ? AuditCodeCommunityChargeModelByInstaller : AuditCodeCommunityChargeModelByUser, $this->env, [], $installerData['Account']);
        }
    }

    /**
     * @msg: 删除社区
     * @services: rps(deleteCommunityControl),callHistoryUtil(deleteCommunityControl)
     * @model: user.deleteRoom,user.afterDeleteRoom,user.afterDeleteMain,user.afterDelete,user.afterDeleteRoomNotify
     * deviceCommunity.delete,deviceCommunity.afterDelete
     */
    public function deleteCommunity()
    {
        global $cMessage, $gApp;
        $params = [
            "ID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#manage#deleteCommunity#id=$id;userId=$userId");
        $data = $this->db->querySList(
            "select A.ID,A.Account,A.ManageGroup,A.Location from Account A join Account B on A.ManageGroup = B.ManageGroup where A.ID = :ID and B.ID = :MyID",
            [":ID" => $id, ":MyID" => $userId]
        );
        if (count($data) === 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        if ($data[0]["ManageGroup"] == $id) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $mainData = $this->db->querySList(
            "select Account from Account where ID = :ID",
            [":ID" => $data[0]["ManageGroup"]]
        )[0];
        $name = $mainData["Account"];
        $this->deleteCommunityControl($id);
        // zh 2021.09.26 6.2featureplann关联删除
        $this->db->delete2ListWKey('ManageFeature', 'AccountID', $id);

        $token = \util\string\randString(TOKENLENGTH);
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);

        //先取旧token
        $oldToken = $redis->get($name);
        if ($oldToken !== null) {
            $redis->del($oldToken);
        }

        //存新token
        $redis->setex($token, TOKENVALIDITYTIME, $name);
        $redis->setex($name, TOKENVALIDITYTIME, $token);

        \util\computed\setGAppData(["token" => $token]);

        // 更改当前的user和role，否则审计日志会有问题
        $gApp['user'] = $name;
        $gApp['role'] = RCOMMUNITYGRADE;
        $this->auditLog->setLog(AuditCodeDeleteCommunity, $this->env, [$name . "-" . $data[0]["Location"]], $name);
        \util\computed\setSmartHomeTask(['Key'=>$data[0]['Account'], 'Type'=>5, 'CommunityID'=>$id]);
    }

    public function deleteCommunityControl($id)
    {
        // 删除房间
        $data = $this->db->queryAllList("Account", ["equation" => [":ID" => $id]])[0];
        $account = $data["Account"];

        $users = $this->db->queryAllList("PersonalAccount", ["equation" => [":ParentID" => $id, ":Role" => COMENDMROLE]]);
        foreach ($users as $user) {
            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData(["ID" => $user["ID"], "userAliasId" => $id]);
            $this->models["user"]->deleteRoom();
            $this->models["user"]->afterDeleteRoom();
            $this->models["user"]->afterDeleteMain();
            $this->models["user"]->afterDelete();
            // 删除房间需要通知人脸数据的删除，收集数据
            $resetParams();
        }

        $devices = $this->db->querySList("select ID from Devices where MngAccountID = :MngAccountID and Grade != 3", [":MngAccountID" => $id]);
        foreach ($devices as $device) {
            $deviceId = $device["ID"];
            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData(["ID" => $deviceId, "userAliasId" => $id]);
            $this->models["deviceCommunity"]->delete();
            $this->models["deviceCommunity"]->afterDelete();
            $resetParams();
        }
        // 删除build
        $this->db->delete2ListWKey("CommunityUnit", "MngAccountID", $id);

        // 删除maclibrary
        $mDevices = $this->db->queryAllList("DeviceForRegister", ["equation" => [":PerMngID" => $id]]);
        foreach ($mDevices as $mDevice) {
            $mac = $mDevice["MAC"];
            $this->services["rps"]->deleteRps([$mac]);
            $this->services["rps"]->modifyMap(["mac" => [$mac], "type" => 6]);
        }

        // 删除物业管理表
        $this->db->delete2ListWKey("PropertyMngList", "CommunityID", $id);

        // 删除Communityinfo和Account数据
        $this->db->delete2ListWKey("CommunityInfo", "AccountID", $id);
        $this->db->delete2ListWKey("Account", "ID", $id);

        // 6.3 删除社区时遗漏权限组表
        $this->db->delete2ListWKey("AccessGroup", "CommunityID", $id);

        // 删除通话记录
        $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("MngAccountID", $id);
    }

    public function queryComPerManage()
    {
        $params = [
            "CommunityID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["CommunityID"];
        $userId = $params["userAliasId"];

        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        $group = $myData["ManageGroup"];
        $allWorkDatas = $this->db->querySList("select A.ID,I.FirstName,I.LastName from Account A left join PropertyInfo I on A.ID = I.AccountID where A.ManageGroup = :ManageGroup and Grade = :Grade", [":ManageGroup" => $group, ":Grade" => PROPERTYMANAGE]);

        $data = [];
        $data["AllWorks"] = [];
        foreach ($allWorkDatas as $val) {
            array_push($data["AllWorks"], ["ID" => $val["ID"], "Name" => $val["FirstName"] . " " . $val["LastName"]]);
        }
        $selectedWorks = $this->db->queryAllList("PropertyMngList", ["equation" => [":CommunityID" => $id]]);
        $data["SelectedWorks"] = [];
        foreach ($selectedWorks as $val) {
            array_push($data["SelectedWorks"], $val["PropertyID"]);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @deprecated 移动到/web-server/v3/web/common/account/changeComPerManage
     * @description
     * @lastEditor csc 2022/4/2 18:07 V6.4
     */
    public function changeCommunityProperty()
    {
        global $cMessage;
        $params = [
            "CommunityID" => "",
            "userAliasId" => "",
            "IDs" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityID = $params["CommunityID"];
        $ids = $params["IDs"];
        $userId = $params["userAliasId"];

        if ($ids == "") {
            $ids = [];
        } else {
            $ids = explode(";", $ids);
        }

        //community验证
        $myData = $this->db->queryAllList("Account", ["equation" => [":ID" => $userId]])[0];
        $group = $myData["ManageGroup"];

        $communityData = $this->db->queryAllList("Account", ["equation" => [":ID" => $communityID, ":ManageGroup" => $group]]);
        if (count($communityData) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        if (count($ids) != 0) {
            $sql = "select ID from Account where ManageGroup = " . $group . " and ";
            $str = "";
            foreach ($ids as $key => $val) {
                if ($key == 0) {
                    $str .= " ID = " . $val;
                } else {
                    $str .= " or ID = " . $val;
                }
            }
            $str = "( $str )";
            $sql .= $str;

            $data = $this->db->querySList($sql);
            if (count($data) != count($ids)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
        }
        $this->db->delete2ListWKey("PropertyMngList", "CommunityID", $communityID);
        foreach ($ids as $val) {
            $this->db->insert2List("PropertyMngList", [":PropertyID" => $val, ":CommunityID" => $communityID]);
        }
    }

    public function queryInstallArea()
    {
        $data = $this->db->querySList("select Account,ID from Account where Grade = " . AREAGRADE);
        foreach ($data as &$val) {
            $val["Childern"] = $this->db->querySList("select Account,ID from Account  where ParentID = :ParentID and Special = 0 and ID = ManageGroup", [":ParentID" => $val["ID"]]);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    // V4.5
    public function queryAllCommunity()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        $params = [
            "Key" => "",
            "Install" => "",
            "AreaManage" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $key = $params["Key"];
        $areaManage = $params["AreaManage"];
        $install = $params["Install"];

        $where = "where A.Grade = 21 and A.Location like :Key";
        $bindArray = [":Key" => "%$key%"];

        if ($areaManage != "all") {
            $where .= " and A.ParentID = :ParentID";
            $bindArray[":ParentID"] = $areaManage;
        }

        if ($install != "all") {
            $where .= " and A.ManageGroup = :ManageGroup";
            $bindArray[":ManageGroup"] = $install;
        }

        $communityFeature = $this->db->querySList(
            'SELECT ID FROM FeaturePlan WHERE Type = 0 order by ID ASC limit 2'
        );

        $total = count($this->db->querySList("select A.ID from Account A $where", $bindArray));
        $details = $this->db->querySList(
            "select A.ID,A.Location,B.Account as Install,C.Account as AreaManage,I.NumberOfApt,I.Switch,F.Name as FeatureName from Account A
            join Account B on A.ManageGroup = B.ID 
            join Account C on A.ParentID = C.ID 
            join CommunityInfo I on A.ID = I.AccountID 
            left join ManageFeature M ON A.ID = M.AccountID 
            left join FeaturePlan F on M.FeatureID = F.ID
            $where limit $offset,$rows",
            $bindArray
        );

        foreach ($details as &$val) {
            $val["Indoors"] = $this->db->querySList("select count(ID) from Devices where MngAccountID = :MngAccountID and Type = 2", [":MngAccountID" => $val["ID"]])[0]["count(ID)"];
            $val["Apts"] = $this->db->querySList("select count(ID) from PersonalAccount where ParentID = :ParentID and Role = 20", [":ParentID" => $val["ID"]])[0]["count(ID)"];
            $val["EnableLandline"] = intval($val["Switch"]) & 1;
            if ($val['ID'] === $communityFeature[0]['ID'] || $val['ID'] === $communityFeature[1]['ID']) {
                $val['FeatureName'] = $val['ID'] === $communityFeature[0]['ID'] ? MSGTEXT["featureNameBasic"] : MSGTEXT["featureNamePremium"];
            }
        }

        $data = ["total" => $total, "row" => $details, "detail" => $details];
        \util\computed\setGAppData(["data" => $data]);
    }

    public function setCommunity()
    {
        $params = [
            "NumberOfApt" => "",
            "EnableLandline" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $numberOfApt = $params["NumberOfApt"];
        $enableLandline = $params["EnableLandline"];
        $id = $params["ID"];
        $this->log->actionLog("#model#manage#setCommunity#id=$id;numberOfApt=$numberOfApt;EnableLandline=$enableLandline");
        $communityData = $this->db->queryAllList("Account", ["equation" => [":ID" => $id]])[0];
        $account = $communityData["Account"];

        $installerData = $this->db->querySList('select Account from Account where ID = :ID', [":ID" => $communityData['ManageGroup']])[0];

        $communityInfo = $this->db->queryAllList("CommunityInfo", ["equation" => [":AccountID" => $id]])[0];
        $switch = $communityInfo["Switch"];
        $switch = \util\computed\bitOperation($switch, $enableLandline, 1);
        $this->db->update2ListWKey("CommunityInfo", [":AccountID" => $id, ":NumberOfApt" => $numberOfApt, ":Switch" => $switch], "AccountID");
        $resetParams = \util\model\saveParams();
        \util\computed\setGAppData(["userAliasId" => $id]);
        $this->models["chargePlan"]->setManage();
        $resetParams();
        \util\computed\setGAppData(["Account" => $account]);

        // 对比前后是否发生更改
        if ($communityInfo['NumberOfApt'] != $numberOfApt || (intval($communityInfo['Switch']) & 1) != $enableLandline) {
            $this->services['billsysUtil']->checkPlan($id);
            if ($communityInfo['NumberOfApt'] != $numberOfApt) {
                $this->auditLog->setLog(AuditCodeAPTCount, $this->env, [$numberOfApt], $account);
            }
            if ((intval($communityInfo['Switch']) & 1) != $enableLandline) {
                $this->auditLog->setLog($enableLandline == 0 ? AuditCodeDisableLandline : AuditCodeEnableLandline, $this->env, [], $account);
            }
        }
    }

    public function queryAllInstaller()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->querySList("select Grade,Account from Account where ID = :ID", [":ID" => $userId])[0];
        $user = $data["Account"];
        $grade = $data["Grade"];
        $showAllInstallerInDis = ['Genway'];

        if ($grade == SUPERGRADE) {
            $installers = $this->db->querySList("select ID,Account as Name from Account where Grade in (" . COMMUNITYGRADE . "," . PERSONGRADE . ") and ID = ManageGroup and Special = 0");
        } else {
            // 欧洲临时更改，dis显示包括默认生成的installer
            // if (in_array($user, $showAllInstallerInDis)) {
            //     $specialCondition  = "";
            // } else {
            //     $specialCondition = "and Special = 0";
            // }
            
            // V6.4默认的installer也显示
            $installers = $this->db->querySList("select ID,Account as Name,Special,UUID from Account where Grade in (" . COMMUNITYGRADE . "," . PERSONGRADE . ") and ID = ManageGroup and ParentID=:ID", [":ID" => $userId]);
        }

        foreach ($installers as &$installer) {
            $installer["Community"] = $this->db->querySList("select ID,Location as Name, Grade from Account where Grade = 21 and ManageGroup = :ManageGroup", [":ManageGroup" => $installer["ID"]]);
            // 7.0新增installer查询办公
            $installer["Office"] = $this->db->querySList("select ID,Location as Name, UUID, Grade from Account where Grade = 23 and ManageGroup = :ManageGroup", [":ManageGroup" => $installer["ID"]]);
        }
        \util\computed\setGAppData(["data" => $installers]);
    }
}
