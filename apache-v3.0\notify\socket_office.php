<?php
//require_once("akcs_conf.php");
require_once(dirname(__FILE__).'/adapt_define.php');
require_once(dirname(__FILE__).'/proto_office.php');
require_once(dirname(__FILE__).'/socket.php');
//ini_set('date.timezone','Asia/Shanghai');

/*******************************办公消息枚举*******************************/

//office更新配置
const MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE = MSG_P2A + 2000;

//office创建用户
const MSG_P2A_NOTIFY_OFFICE_CREATE_UID = MSG_P2A + 2001;

//office用户账户续费
const MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RENEW = MSG_P2A + 2002;

//office账户过期
const MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_WILL_EXPIRE = MSG_P2A + 2003;

//office账户重置密码
const MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RESETPWD = MSG_P2A + 2004;

//office账户改密码
const MSG_P2A_NOTIFY_OFFICE_ACCOUNT_CHANGEPWD = MSG_P2A + 2005;

const MSG_P2A_NOTIFY_OFFICE_ACCESS_GROUP_MODIFY = MSG_P2A + 2006;
    //权限组相关：用户修改
const MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 2007;
    //导入用户
const MSG_P2A_NOTIFY_OFFICE_COMMUNITY_IMPORT_ACCOUNT_DATAS = MSG_P2A + 2008;
//office人员修改
const MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY = MSG_P2A + 2014;
//办公添加新站点
const MSG_P2A_OFFICE_ADD_NEW_SITE = MSG_P2A + 2015;

class CWebOfficeModifyNotify extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\AdaptOffice\WebOfficeModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setOfficeId((int) $data[3]);
            $TempData->setDepartmentId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


//办公场景
class COfficeAccountRenewSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\AdaptOffice\PMOfficeAccountRenew();
            $TempData->setCommunity($data[0]);
            $TempData->setEmail($data[1]);
            $TempData->setPmName($data[2]);
            $TempData->setAccountNum($data[3]);
            $TempData->setList($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
