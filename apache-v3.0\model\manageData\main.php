<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */
namespace model;

use share\service\HttpClient;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

include_once __DIR__."/../basic/device.php";

const INDOOR = 2;

class CManageData extends \basic\CDevice
{
    public function getBillInfo()
    {
        $params = [
            "userAlias"=>"",
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        // $installAccount = $this->db->querySList("select A.Account from Account A join Account B on A.ID = B.ManageGroup where B.Account = :Account",[":Account"=>$user])[0]["Account"];
        $data = $this->db->queryAllList("InstallerBillingInfo", ["equation"=>[":Account"=>$user]])[0];
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function editBill()
    {
        $params = [
            "userAlias"=>"",
            "BillingTitle"=>"",
            "Contactor"=>"",
            "Street"=>"",
            "City"=>"",
            "Postcode"=>"",
            "Country"=>"",
            "TelePhone"=>"",
            "Fax"=>"",
            "Email"=>"",
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $title = $params["BillingTitle"];
        $contactor = $params["Contactor"];
        $street = $params["Street"];
        $city = $params["City"];
        $postcode = $params["Postcode"];
        $country = $params["Country"];
        $telePhone = $params["TelePhone"];
        $fax = $params["Fax"];
        $email = $params["Email"];
        $this->log->actionLog("#model#manageData#editBill#user=$user;title=$title;contactor=$contactor;street=$street;city=$city;postcode=$postcode;country=$country;telePhone=$telePhone;fax=$fax;email=$fax");
        // $installAccount = $this->db->querySList("select A.Account from Account A join Account B on A.ID = B.ManageGroup where B.Account = :Account",[":Account"=>$user])[0]["Account"];
        $this->db->update2ListWKey("InstallerBillingInfo", [
            ":Account"=>$user,
            ":BillingTitle"=>$title,
            ":Contactor"=>$contactor,
            ":Street"=>$street,
            ":City"=>$city,
            ":Postcode"=>$postcode,
            ":Country"=>$country,
            ":TelePhone"=>$telePhone,
            ":Fax"=>$fax,
            ":Email"=>$email
        ], "Account");
    }

    public function checkPw()
    {
        global $cMessage;
        $params = [
            "PassWd"=>"",
            "Type"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $type = $params["Type"];
        $passwd = $params["PassWd"];
        // 一键删除所需密码，前端已md5加密
        if ($type == 'staffOrResidentDeleteAll') {
            $count = $this->db->querySList("select count(*) from Account where ID=:ID and Passwd = :Passwd", [":ID"=>$userId,":Passwd"=>$passwd])[0]["count(*)"];
        } else {
            $count = $this->db->querySList("select count(*) from Account where ID=:ID and Passwd = :Passwd", [":ID"=>$userId,":Passwd"=>md5($passwd)])[0]["count(*)"];
        }
        if ($count == 0) {
            $cMessage->echoErrorMsg(StatePasswordIncorrect);
        }
    }

    public function changePw()
    {
        $params = [
            "PassWd"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $passwd = $params["PassWd"];

        $this->db->update2ListWID("Account", [":ID"=>$userId,":Passwd"=>md5($passwd)]);
        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeSelfPassword, $this->env, [], $account);
    }

    public function getmyOpera()
    {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
        \util\computed\setGAppData(["data"=>["enableTime"=>$data["EnableValidTimeSetting"],"enableCount"=>$data["EnableCountSetting"]]]);
    }

    public function changeTimeZone()
    {
        $params = [
            "TimeZone"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["TimeZone"];
        $this->log->actionLog("#model#manageData#changeTimeZone#userId=$userId;timeZone=$timeZone");

        $oldTimeZone = $this->db->querySList('select TimeZone from Account where ID=:ID', [":ID"=>$userId])[0]["TimeZone"];

        $this->db->update2ListWID("Account", [":ID"=>$userId,":TimeZone"=>$timeZone]);

        // $userData = $this->db->queryAllList("Account",["equation"=>[":ID"=>$userId]])[0];
        if ($oldTimeZone != $timeZone) {
            $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
            $this->auditLog->setLog(AuditCodeSelfTime, $this->env, ["GTM $timeZone"], $account);
        }
    }

    public function getChargeMode()
    {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $manageData = $this->db->querySList("select ChargeMode,ParentID,SendExpireEmailType,SendRenew from Account where ID = :ID", [":ID"=>$userId])[0];
        $chargeMode = $manageData["ChargeMode"];
        $sendExpireEmailType = $manageData["SendExpireEmailType"];
        $sendRenew = $manageData["SendRenew"];
        $disChargeMode = $this->db->querySList("select ChargeMode from Account where ID = :ID", [":ID"=>$manageData["ParentID"]])[0]["ChargeMode"];
        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        \util\computed\setGAppData(["ChargeMode"=>$chargeMode,"SendExpireEmailType"=>$sendExpireEmailType, "SendRenew"=>$sendRenew]);
    }

    public function chargeMode()
    {
        $params = [
            "userAliasId"=>"",
            "ChargeMode"=>"",
            "SendExpireEmailType"=>"",
            "SendRenew"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $chargeMode = $params["ChargeMode"];
        $sendExpireEmailType = $params["SendExpireEmailType"];
        $sendRenew = $params["SendRenew"];
        $this->log->actionLog("#model#manageData#chargeMode#params=".json_encode($params));

        $this->db->update2ListWID("Account", [":ChargeMode"=>$chargeMode,":SendExpireEmailType"=>$sendExpireEmailType,":ID"=>$userId, ":SendRenew"=>$sendRenew]);
    }


    public function execImportComData()
    {
        global $cMessage;
        $file = $_FILES["Community"];
        $fileName = $file["tmp_name"];
        $this->log->actionLog("#model#manageData#execImportComData#");
        require_once __DIR__.'/../../plugin/PHPExcel/PHPExcel/IOFactory.php';
        $objPHPExcel = \PHPExcel_IOFactory::load($fileName);

        //获取表格行数
        $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
        //获取表格列数
        $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();

        $buildIndex = "Building";
        $aptIndex = "Apt";
        $aptNameIndex = "AptName";
        $deviceIndex = "Device";
        $nameFirstIndex = "FirstName";
        $nameLastIndex = "LastName";
        $emailIndex = "Email";
        $mobileIndex = "MobileNumber";
        $phoneIndex = "1stPhone";
        $phone2Index = "2ndPhone";
        $phone3Index = "3rdPhone";
        $callTypeIndex = "CallType";
        $phoneCode = "PhoneCode";
        $layout = "Layout";

        $header2Key = [
            "Building"=>$buildIndex,
            "Apt"=>$aptIndex,
            "AptName"=>$aptNameIndex,
            "Device"=>$deviceIndex,
            "FirstName"=>$nameFirstIndex,
            "LastName"=>$nameLastIndex,
            "Email"=>$emailIndex,
            "MobileNumber"=>$mobileIndex,
            "1stPhone"=>$phoneIndex,
            "2ndPhone"=>$phone2Index,
            "3rdPhone"=>$phone3Index,
            "CallType"=>$callTypeIndex,
            "TelephoneCallingCode"=>$phoneCode,
            "Layout"=>$layout
        ];

        $dataArr = [];
        $keyColumn = [];
        $startRow = 1;
        $headerNull = 0;
        for ($row = $startRow; $row <= $rowCount; $row++) {
            if ($row != $startRow) {
                $data = [];
            }

            // 列循环
            for ($column = 'A'; $column <= $columnCount; $column++) {
                // 获取单元格值
                $value = $objPHPExcel->getActiveSheet()->getCell($column.$row)->getValue();
                $value = $value === null ? "" : strval($value);
                // 获取第一行值
                if ($row == $startRow) {
                    $this->log->actionLog("#model#manageData#execImportComData#cell=$value");
                    $key = $header2Key[$value];
                    // $this->log->actionLog("#model#manageData#execImportComData#key=$key");
                    // 无效列表头
                    if (!$key) {
                        $headerNull ++;
                    }
                    $keyColumn[$column] = $key;
                } else {
                    $cellText = $value;
                    $key = $keyColumn[$column];
                    if (!$key) {
                        continue;
                    }
                    $data[$key] = $value;
                }
                $data["row"] = $row;
            }
            if ($row != $startRow) {
                array_push($dataArr, $data);
            }
        }
        if ($headerNull == count($keyColumn)) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }
        $fileList = $dataArr;
        $this->log->actionLog("#model#manageData#execImportComData#file=".json_encode($fileList));
        \util\computed\setGAppData(["CommunityData"=>$fileList]);
    }

    /**
     * 检查该社区是否必须绑定每户室内机
     * @param communityId: 社区id号
     */
    public function isCommunityIndoorPlan($communityId)
    {
        $featureItem = $this->db->querySList("select F.Item from FeaturePlan F join ManageFeature M on F.ID = M.FeatureID where M.AccountID = :ID", [
            ":ID" => $communityId
        ])[0]['Item'];
        $indoor = \util\computed\getSpecifyBit($featureItem, 4);
        return $indoor == 1 ? true : false;
    }

    /**
     * 检查导入房间设备是否有室内机类型
     * @param devices: 房间导入设备
     * @param accountId: PersonalAccount account
     */
    public function checkIndoorType($devices)
    {
        global $cMessage;
        foreach ($devices as $device) {
            if ($device[1] == INDOOR) {
                return $device[2];
            }
        }

        // 未携带绑定室内机
        $cMessage->echoErrorMsg(StateIndoorMonitorRequired);
    }

    public function insertIndoor($mac, $accountId)
    {
        $account = $this->db->querySList('select Account from PersonalAccount where ID=:ID', [':ID' => $accountId])[0]['Account'];
        $this->db->insert2List("DevicesSpecial", [
            ":Account"=>$account,
            ":MAC"=>$mac
        ]);
    }

    public function importCommunity()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
            "userAlias"=>"",
            "CommunityID"=>"",
            "CommunityData"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);

        $buildIndex = "Building";
        $aptIndex = "Apt";
        $aptNameIndex = "AptName";
        $deviceIndex = "Device";
        $nameFirstIndex = "FirstName";
        $nameLastIndex = "LastName";
        $emailIndex = "Email";
        $mobileIndex = "MobileNumber";
        $phoneIndex = "1stPhone";
        $phone2Index = "2ndPhone";
        $phone3Index = "3rdPhone";
        $callTypeIndex = "CallType";
        $phoneCode = "PhoneCode";
        $layoutIndex = "Layout";

        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $communityID = $params["CommunityID"];
        $data = $this->db->querySList(
            "select A.ID,A.ParentID,A.Account,A.Location,A.ManageGroup from Account A join Account B
            on A.ManageGroup = B.ManageGroup where A.ID = :ID and B.Account = :Account",
            [":ID"=>$communityID,":Account"=>$user]
        );
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        
        // 6.2.1 检测当前小区是否正在导入
        $count = $this->db->querySList(
            'select count(*) as total from ImportTask where Account=:Account and Type=0 and Status=0',
            [':Account' => $data[0]["Account"]]
        )[0]['total'];
        if ($count > 0) {
            // TODO
            $cMessage->echoErrorMsg(StateImportProcessing);
        }


        // 获取是否为室内机方案
        $isIndoorPlan = $this->isCommunityIndoorPlan($communityID);

        // installer
        $installerData = $this->db->querySList('select Account from Account where ID = :ID', [":ID"=>$data[0]["ManageGroup"]])[0];

        $this->auditLog->setLog(AuditCodeImportCommunity, $this->env, [$installerData['Account'].'-'.$data[0]['Location']], $installerData['Account']);

        $parentID = $data[0]["ParentID"];
        $community = $data[0]["Account"];
        $communityInfo = $this->db->querySList(
            'select IsNew, Switch from CommunityInfo where AccountID=:AccountID',
            [":AccountID"=>$communityID]
        )[0];
        $isNew = $communityInfo['IsNew'];

        $isSmartHome = 0;
        $switch = $communityInfo['Switch'];
        $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($smartHomeSwitch == 1 && $ableSmartHome) {
            $isSmartHome = 1;
            $smartUUID = $this->db->querySList(
                'select SmartHomeUUID from SmartHomeManageMap where Account=:Account',
                [':Account' => $community]
            )[0]['SmartHomeUUID'];
            require_once __DIR__.'/../../service/httpClient.php';
            $httpClient = new HttpClient();
            $result = $httpClient->get(SMART_HOME_HOST."/support/api/v1/communities/layouts/infos?community_id=".$smartUUID)
                ->headers(['Access-Token: '.SMART_HOME_TOKEN, 'Trace-Id: '.$this->log->getTraceId()])
                ->exec()
                ->checkJsonRes(true, 'success', true)
                ->result();
            $layouts = $result['result']['layouts'];
            $layoutIds = [];
            foreach ($layouts as $layout) {
                $layoutIds[$layout['name']] = $layout['layout_id'];
            }
        }


        $this->log->actionLog("#model#manageData#importCommunity#community=$community;communityID=$communityID");
        $fileList = $params["CommunityData"];

        $roomRule = '/^[0-9]{1,6}$/';
        // $buildRule = '/^[0-9]{1,2}$/';
        $mobileRule = '/^[0-9]$/';

        $dataArr = [];
        //用于判断是否重复apt
        $apts = [];
        //邮箱是否重复判断
        $emails = [];
        // 判断手机号是否重复
        $mobiles = [];
        //社区公共设备
        $publicDevice = [];

        // rps 设备检测
        $deviceForRps = [];
        $dataArr = [];
        // 新增的房间数量
        $roomNum = 0;
        // 新增设备数量
        $deviceNum = 0;

        foreach ($fileList as $key => $value) {
            $this->log->actionLog("#model#manageData#importCommunity#index=$key");
            //添加build
            $building = $value[$buildIndex];

            //设备
            $devices = $value[$deviceIndex];
            if ($devices == "") {
                $devices = [];
            } else {
                $devices = explode(";", $devices);
                foreach ($devices as &$device) {
                    $device = explode(",", $device);
                }
            }

            //整合社区公共设备
            if ($building === "") {
                foreach ($devices as $val) {
                    // 2=>mac,1=>type,0=>location
                    $this->checkMACValidForImport($val[2], $communityID, $parentID, null, $val[1], $val[0]);
                    array_push($publicDevice, $val);
                    array_push($deviceForRps, $val[2]);
                    $deviceNum += 1;
                }
                continue;
            }
            // 首次发现build号，设置空数组
            if (!array_key_exists($building, $dataArr)) {
                $dataArr[$building] = [];
            }

            //apt是否重复
            $apt = $value[$aptIndex];
            if (in_array($building.$apt, $apts) && $apt != "") {
                $cMessage->echoErrorMsg(StateAptDuplicated, [], [$apt]);
            }
            // apt六位数验证
            $isMatchedRoom = preg_match($roomRule, $apt, $matches);
            if (!$isMatchedRoom && $apt != "") {
                $cMessage->echoErrorMsg(StateAptDigiAts, [], [$apt]);
            }
            array_push($apts, $building.$apt);

            // 首次发现build的public，room，设置空数组
            if (!array_key_exists("public", $dataArr[$building])) {
                $dataArr[$building]["public"] = [];
            }
            if (!array_key_exists("room", $dataArr[$building])) {
                $dataArr[$building]["room"] = [];
            }

            //房间为空，说明导入公共设备
            if ($apt == "") {
                //检查mac地址是否已导入mac library
                foreach ($devices as $val) {
                    $this->checkMACValidForImport($val[2], $communityID, $parentID, null, $val[1], $val[0]);
                    array_push($dataArr[$building]["public"], $val);
                    array_push($deviceForRps, $val[2]);
                    $deviceNum += 1;
                }
                continue;
            }

            $room = [];
            $room["name"] = $apt;
            $room["aptname"] = $value[$aptNameIndex];
            $room["devices"] = [];

            if ($isSmartHome) {
                $layout = $value[$layoutIndex];
                if (!array_key_exists($layout, $layoutIds)) {
                    $cMessage->echoErrorMsg(StateInvalidLayout);
                }
                $room['layout'] = $layout;
                $room['layoutId'] = $layoutIds[$layout];
            }
            

            $firstName = $value[$nameFirstIndex];
            $lastName = $value[$nameLastIndex];
            $userName = $firstName." ".$lastName;
            $email = $value[$emailIndex];
            $mobile = $value[$mobileIndex];

            //检查mac地址是否已导入mac library
            foreach ($devices as $val2) {
                $this->checkMACValidForImport($val2[2], $communityID, $parentID, "", $val2[1], $val2[0]);
                array_push($room["devices"], $val2);
                array_push($deviceForRps, $val2[2]);
            }

            //只导入节点
            if (($firstName == "" || $lastName == "") && count($devices) == 0) {
                // 验证房间号是否已经存在
                if (count($this->db->querySList("select R.ID from CommunityRoom R join CommunityUnit U on R.UnitID = U.ID where U.UnitName = :UnitName and U.MngAccountID = :MngAccountID and R.RoomName = :RoomName", [":MngAccountID"=>$communityID,":UnitName"=>$building,":RoomName"=>$apt]))>0) {
                    $cMessage->echoErrorMsg(StateAptExit, [], [$apt]);
                }
                $roomNum += 1;
                array_push($dataArr[$building]["room"], $room);
                continue;
            }

            //姓名为空，不导入用户信息
            if ($firstName == "" || $lastName == "") {
                $room["user"] = null;
                // kxl V6.2 导入新房间，只需要验证导入数据中有没有室内机
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->checkIndoorType($devices);
                }
                array_push($dataArr[$building]["room"], $room);
                continue;
            }

            // V5.2导入用户时检测是否存在符合的房间
            $buildData = $this->db->querySList("select ID from CommunityUnit where MngAccountID = :MngAccountID and UnitName = :UnitName", [":UnitName"=>$building,":MngAccountID"=>$communityID]);
            // 楼栋不存在时，就不会有相同房间号非空房间
            if (count($buildData) != 0) {
                $buildData = $buildData[0];
                $roomData = $this->db->querySList("select ID from CommunityRoom where RoomName = :RoomName and UnitID = :UnitID", [":RoomName"=>$apt,":UnitID"=>$buildData["ID"]]);
                if (count($roomData) != 0) {
                    $roomData = $roomData[0];
                    $accountData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":UnitID"=>$buildData["ID"],":RoomID"=>$roomData["ID"],":ParentID"=>$communityID,":Special"=>1]]);
                    if (count($accountData) == 0) {
                        $cMessage->echoErrorMsg(StateResidentInRoom, [], [$apt]);
                    }
                } else {
                    // 说明是要新增房间
                    $roomNum += 1;
                    if ($isIndoorPlan) {
                        $room['indoorMAC'] = $this->checkIndoorType($devices);
                    }
                }
            } else {
                // 说明是要新增房间
                $roomNum += 1;
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->checkIndoorType($devices);
                }
            }

            // 邮箱是否存在
            if ($email != "") {
                if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
                    $cMessage->echoErrorMsg(StateInvalidPEmail, [], [$email]);
                }

                if (in_array($email, $emails)) {
                    $cMessage->echoErrorMsg(StateEmailDuplicated, [], [$email]);
                }

                $hasUserData = $this->db->querySList("select R.ID from CommunityRoom R join PersonalAccount P on R.ID = P.RoomID where P.Email = :Email", [":Email"=>$email]);
                if (count($hasUserData) > 0) {
                    $cMessage->echoErrorMsg(StateEmailPExit, [], [$email]);
                }
            }


            // 手机号是否符合规则，手机号是否存在
            if ($mobile != "") {
                // $isMatchedMobile = preg_match($mobileRule,$mobile);
                // if(!$isMatchedMobile) {
                //     $cMessage->echoErrorMsg(StateNotMobile,[],[$mobile]);
                // }

                $hasMobile = $this->db->querySList("select count(*) from PersonalAccount where MobileNumber = :MobileNumber", [":MobileNumber"=>$mobile])[0]["count(*)"];
                if ($hasMobile > 0) {
                    $cMessage->echoErrorMsg(StateMobileExits2, [], [$mobile]);
                }

                if (in_array($mobile, $mobiles)) {
                    $cMessage->echoErrorMsg(StateMobileDuplicated, [], [$mobile]);
                }
            }

            $room["user"] = [];
            //导入主账号，名字不能为空
            if (($email != "" || $mobile != "") && ($firstName === "" || $lastName === "" || \util\string\checkByteLength($userName, 63))) {
                if ($firstName === "") {
                    $cMessage->echoErrorMsg(StateFirstNameEmpty);
                } elseif ($lastName === "") {
                    $cMessage->echoErrorMsg(StateLastNameEmpty);
                } else {
                    $cMessage->echoErrorMsg(StateInvalidPName, [], [$userName]);
                }
            }

            $room["user"]["firstName"] = $firstName;
            $room["user"]["lastName"] = $lastName;

            $room["user"]["email"] = $email == "" ? null : $email;
            array_push($emails, $email);

            $room["user"]["mobile"] = $mobile == "" ? null : $mobile;
            array_push($mobiles, $mobile);

            $callType = $value[$callTypeIndex];
            if (!in_array($callType, ["0","1","2","3","4","5"])) {
                if ($callType === "") {
                    $cMessage->echoErrorMsg(StateCalltypeEmpty);
                } else {
                    $cMessage->echoErrorMsg(StateInvalidPCalltype, [], [$callType]);
                }
            }
            $room["user"]["callType"] = $callType;

            // $pin = $value[$pinIndex];
            // if( ($pin != "" && ( strlen($pin) < 4 || strlen($pin) > 8 )) || $pin === "0000")
            //     $cMessage->echoErrorMsg(StateInvalidPPin,[],[$pin]);

            $room["user"]["pin"] = "";

            // $active = $value[$activationIndex];
            $active = "0";
            if (!in_array($active, ["0","1"])) {
                if ($active === "") {
                    $cMessage->echoErrorMsg(StateActiveEmpty);
                } else {
                    $cMessage->echoErrorMsg(StateInvalidPActive, [], [$active]);
                }
            }

            $room["user"]["active"] = $active;
            $room["user"]["phoneCode"] = $value[$phoneCode] ?: "";
            $room["user"]["phone"] = $value[$phoneIndex];
            $room["user"]["phone2"] = $value[$phone2Index];
            $room["user"]["phone3"] = $value[$phone3Index];
            $room["user"]["rfcard"] = "";
            array_push($dataArr[$building]["room"], $room);
        }
        $this->log->actionLog("#model#manageData#importCommunity#public:".json_encode($publicDevice));
        $this->log->actionLog("#model#manageData#importCommunity#data:".json_encode($dataArr));
        // rps检测
        $this->rpsCheckMAC($deviceForRps, $parentID, $communityID);
        // 房价数量是否达到上限
        $hadRoomCount = $this->db->querySList("select count(*) as count from PersonalAccount where ParentID = :ParentID and Role = ".COMENDMROLE, [":ParentID"=>$communityID])[0]["count"];
        $aptNumber = $this->db->queryAllList("CommunityInfo", ["equation"=>[":AccountID"=>$communityID]])[0]["NumberOfApt"];
        if ($hadRoomCount+$roomNum > $aptNumber) {
            $cMessage->echoErrorMsg(StateAddOutApt, [], [$aptNumber]);
        }
        $this->log->actionLog("#model#manageData#importCommunity#check pass");

        // 超过1000条有效数据，无法导入
        if ($roomNum + $deviceNum > 1000) {
            $cMessage->echoErrorMsg(StateImportLessData, [], [1000]);
        }

        $userDatas = [];
        $deviceUsers = [];

        // 6.2.1 开始导入，插入标识
        $taskId = $this->db->insert2List('ImportTask', [
            ":UUID" => \util\string\uuid(),
            ":CreateTime" => \util\computed\getNow(),
            ":UpdateTime" => \util\computed\getNow(),
            ":Account" => $community,
            ":Status" => 0,
            ":Type" => 0
        ]);

        //收集build公共设备，传给平台
        $buildDevices = [];
        foreach ($publicDevice as $val) {
            // V5.0 在此处正式修改mac library的所有者 # 社区公共设备
            $this->db->begin();

            $this->importAPDevice($val[2], $val[0], $val[1], $communityID, $community, null);
            $this->db->commit();
        }
        foreach ($dataArr as $build => $dataVal) {
            $this->db->begin();
            $data = $this->db->querySList("select ID from CommunityUnit where MngAccountID = :MngAccountID and UnitName = :UnitName", [":UnitName"=>$build,":MngAccountID"=>$communityID]);
            //插入build
            if (count($data) == 0) {
                $this->db->insert2List("CommunityUnit", [":UnitName"=>$build,":MngAccountID"=>$communityID]);
                $buildID = $this->db->lastInsertId();
                // V6.1 新小区插入build同时插入默认权限组
                if ($isNew == '1') {
                    $this->db->insert2List('AccessGroup', [
                        ":Name"=>vsprintf(MSGTEXT['BuildAccessName'], [$build]),
                        ":CreateTime"=>\util\computed\getNow(),
                        ":CommunityID"=>$communityID,
                        ":UnitID"=>$buildID,
                        ":SchedulerType"=>1,
                        ":StartTime"=>'00:00:00',
                        ":StopTime"=>'23:59:59'
                    ]);
                }
            } else {
                $buildID = $data[0]["ID"];
            }
            $this->db->commit();
            //插入公共设备
            foreach ($dataVal["public"] as $public) {
                // V5.0 在此处正式修改mac library的所有者 # 单元公共设备
                $this->db->begin();

                $this->importAPDevice($public[2], $public[0], $public[1], $communityID, $community, $buildID);
                $this->db->commit();
            }

            array_push($buildDevices, ["buildId"=>$buildID,"devices"=>$dataVal["public"]]);
            //插入节点和用户
            foreach ($dataVal["room"] as $room) {
                $this->db->begin();
                $data = $this->db->querySList("select ID from CommunityRoom where RoomName = :RoomName and UnitID = :UnitID", [":RoomName"=>$room["name"],":UnitID"=>$buildID]);
                if (count($data) == 0) {
                    $userID = $this->importNewRoom($buildID, $room["name"], $communityID, $room['layoutId'], $room['layout']);
                    if ($isIndoorPlan) {
                        $this->insertIndoor($room['indoorMAC'], $userID);
                    }
                } else {
                    $userID = $this->db->querySList("select ID from PersonalAccount where RoomID = :RoomID", [":RoomID"=>$data[0]["ID"]])[0]["ID"];
                }

                // V4.4新增，更改aptname
                if ($room["aptname"] !== "") {
                    $this->db->update2ListWID("PersonalAccount", [":ID"=>$userID,":RoomNumber"=>$room["aptname"]]);
                }
                //使用之前社区的代码导入设备
                $roomID = $this->db->querySList("select ID from CommunityRoom where RoomName = :RoomName and UnitID = :UnitID", [":RoomName"=>$room["name"],":UnitID"=>$buildID])[0]["ID"];
                $devices = $room["devices"];

                foreach ($devices as $device) {
                    $res = $this->addOwnerDevices($device[2], $device[0], $device[1], $community, $communityID, $buildID, $userID);
                    array_push($deviceUsers, $res);
                }

                //用户导入
                $user = $room["user"];
                if ($user == null) {
                    $this->db->commit();
                    continue;
                }
                $result = $this->updateUser(
                    $communityID,
                    $buildID,
                    $roomID,
                    $user["email"],
                    $user["mobile"],
                    $user["firstName"],
                    $user["lastName"],
                    $user["phoneCode"],
                    $user["phone"],
                    $user["phone2"],
                    $user["phone3"],
                    $user["callType"],
                    $user["rfcard"],
                    $user["pin"],
                    $isNew
                );
                array_push($userDatas, $result);
                $this->db->commit();
            }
        }
        // 成功后将任务状态置为已完成
        $this->db->update2ListWID('ImportTask', [
            ":ID" => $taskId,
            ":Status" => 1
        ]);
        \util\computed\setGAppData(["userData"=>$userDatas]);
    }

    private function rpsCheckMAC($macs, $areaId, $communityId)
    {
        global $cMessage;
        $this->log->actionLog("#model#manageData#rpsCheckMAC#mac=".json_encode($macs));
        if (count($macs) == 0) {
            return true;
        }

        $bindArray = [];
        $macParam = [];
        foreach ($macs as $key => $val) {
            array_push($macParam, ":MAC$key");
            $bindArray[":MAC$key"] = $val;
        }
        unset($val);
        // 先检查在不在device for register
        $data = $this->db->querySList("select MAC from DeviceForRegister where MAC in (".implode(",", $macParam).")", $bindArray);
        if (count($data) != count($macs)) {
            $cMessage->echoErrorMsg(StateMAC2Library);
        }

        $data = $this->db->querySList(
            "select MAC from DeviceForRegister where MAC in (".implode(",", $macParam).") and MngID != :MngID union 
			select MAC from DeviceForRegister where MAC in (".implode(",", $macParam).") and PerMngID != :PerMngID and PerMngID != 0 union 
            select MAC from DeviceForRegister where MAC in (".implode(",", $macParam).") and  Owner != ''",
            array_merge([":MngID"=>$areaId,":PerMngID"=>$communityId], $bindArray)
        );
        if (count($data) != 0) {
            $cMessage->echoErrorMsg(StateMAC2Library);
        }
        // 查找需要添加rps的mac
        $needRpsMacs = [];
        $needRpsData = $this->db->querySList("select MAC from DeviceForRegister where MAC in (".implode(",", $macParam).") and PerMngID = 0", $bindArray);
        foreach ($needRpsData as $value) {
            array_push($needRpsMacs, $value["MAC"]);
        }

        if (count($needRpsMacs) != 0) {
            $res = $this->services["rps"]->testMAC($needRpsMacs);
            if (count($res) != 0) {
                $cMessage->echoErrorMsg(StateMAC2Library);
            }
            $this->services["rps"]->addRPSDevice($needRpsMacs);
            $this->db->exec2ListWArray(
                "update DeviceForRegister set PerMngID = :PerMngID where MAC in (".implode(",", $macParam).")",
                array_merge([":PerMngID"=>$communityId], $bindArray)
            );
        }

        return true;
    }

    public function checkMACValidForImport($mac, $communityID, $mngID, $name = null, $type = null, $location = null)
    {
        global $cMessage;
        $types = ["0","1","2","3","50"];
        if ($type != null && !in_array($type, $types)) {
            if ($type === "") {
                $cMessage->echoErrorMsg(StateDeviceTypeEmpty);
            }
            $cMessage->echoErrorMsg(StateInvalidPDeviceType, [], [$type]);
        }
        if ($location === "") {
            $cMessage->echoErrorMsg(StateLocationEmpty);
        }
        if ($location != null && \util\string\checkByteLength($location, 63)) {
            $cMessage->echoErrorMsg(StateLocationPLoog, [], [$mac]);
        }
        // $communityData = $this->db->queryAllList("Account",["equation"=>[":ID"=>$communityID]])[0];
    }

    public function importAPDevice($mac, $location, $type, $communityID, $community, $buildID, $nodeID = null, $node = null)
    {
        $resetParams = \util\model\saveParams();
        $netWork = 0;
        $relay = "#,Relay1,1,1,1";
        $armingFunction = 0;
        \util\computed\setGAppData(["userAlias"=>$community,"userAliasId"=>$communityID,"StairShow"=>$type == 0 ? 1 : 0,
        "Build"=>$buildID ?: "public","NetGroupNumber"=>$netWork,"MAC"=>$mac,"Relay"=>$relay,"Location"=>$location,"Type"=>$type,"ArmingFunction"=>$armingFunction, "IsAllBuild"=>'1']);
        // 这里面会去添加macLibrary的owner，不需要提前去加
        $this->models["deviceCommunity"]->add();
        $resetParams();
    }

    public function addOwnerDevices($mac, $location, $type, $community, $communityID, $buildID, $ownerID)
    {
        $resetParams = \util\model\saveParams();
        $netWork = 0;
        $relay = "#,Relay1,1,1,1";
        $armingFunction = 0;
        \util\computed\setGAppData(["userAlias"=>$community,"userAliasId"=>$communityID,"NodeID"=>$ownerID,
        "Build"=>$buildID,"NetGroupNumber"=>$netWork,"MAC"=>$mac,"Relay"=>$relay,"Location"=>$location,
        "Type"=>$type,"ArmingFunction"=>$armingFunction]);
        $netWork = 0;
        $relay = "#,Relay1,1,1,1";
        $armingFunction = 0;
        $this->models["deviceCommunity"]->add();
        $resetParams();
        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$ownerID]])[0];
        $node = $data["Account"];
        return ["node"=>$node,"buildID"=>$buildID,"mac"=>$mac];
    }

    public function importNewRoom($build, $roomName, $communityID, $layout, $layoutName)
    {
        $resetParams = \util\model\saveParams();
        $accountId = $this->models["user"]->addNewRoom($build, $roomName, $communityID, $layout, $layoutName);
        $resetParams();
        return $accountId;
    }

    public function updateUser($communityID, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phoneCode, $phone, $phone2, $phone3, $callType, $rfcard, $pin, $isNew)
    {
        global $lang,$gApp;
        $resetParams = \util\model\saveParams();
        $this->models["user"]->addComMainUsreCom($communityID, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $callType, $pin, $rfcard, $lang);
        // 获取node和password
        $node = \util\computed\getGAppDataValue("Account");
        $password = \util\computed\getGAppDataValue("Passwd");
        $active = \util\computed\getGAppDataValue("Active");
        $id = \util\computed\getGAppDataValue("ID");
        if ($isNew == '1') {
            \util\computed\setGAppData(["Step"=>0]);
            $this->models["access"]->addUser();
        }
        $resetParams();
        return ["node"=>$node,"buildId"=>$buildId,"active"=>$active,"email"=>$email,"password"=>$password,"name"=>$firstName." ".$lastName,"id"=>$id];
    }

    public function getReceipt()
    {
        $params = [
            "user"=>"",
            "data"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["user"];
        $data = $params["data"];
        $userData = $this->db->querySList('select Grade,ID,Email from Account where Account = :Account', [":Account"=>$user])[0];
        if ($userData['Grade'] == PROPERTYMANAGE) {
            $billData = $this->db->queryAllList("PropertyBillingInfo", ["equation"=>[":Account"=>$user]])[0];
            $info = $this->db->querySList('select * from PropertyInfo where AccountID = :AccountID', [':AccountID' => $userData['ID']])[0];
            $name = $info["FirstName"]." ".$info["LastName"];
        } else {
            $billData = $this->db->queryAllList("InstallerBillingInfo", ["equation"=>[":Account"=>$user]])[0];
            $name = $user;
        }
        $data = array_merge($data, $billData);
        $buyerInfo = $name." ".($userData["Email"] ? $userData["Email"] : "");
        $data["BuyerInfo"] = $buyerInfo;
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function setLang()
    {
        global $lang;
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $oldLanguage = $this->db->querySList("select Language from Account where ID = :ID", [":ID"=>$userId])[0]["Language"];
        if ($lang != $oldLanguage) {
            $this->db->update2ListWID("Account", [":ID"=>$userId,":Language"=>$lang]);
        }
    }

    public function getDisChargeMode()
    {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $userData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
        $grade = $userData["Grade"];
        if ($grade == 11) {
            $chargeMode = $userData["ChargeMode"];
        } elseif ($grade == 21 || $grade == 22 || $grade == 23) {
            $chargeMode = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userData["ParentID"]]])[0]["ChargeMode"];
        }

        \util\computed\setGAppData(["ChargeMode"=>$chargeMode]);
    }
}
