<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-17 11:13:46
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
class CHaveSubUserDeleteCheck implements IMiddleware {
    public $id;
    public function handle (\Closure $next) {
        global $cMessage,$cLog;
        $params = ["ID"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $db = \database\CDatabase::getInstance();
        $count = $db->querySList("select count(*) from PersonalAccount where Role in (".PERENDSROLE.",".COMENDSROLE.") and ParentID = :ParentID",[":ParentID"=>$id])[0]["count(*)"];
        $cLog->actionLog("#middle#haveSubUserDeleteCheck#id=$id;subUser=$count");
        if($count > 0) $cMessage->echoErrorMsg(StateBindUser);
        $next();
    }
}