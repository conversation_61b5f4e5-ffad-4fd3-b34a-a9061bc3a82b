<?php
namespace plan\process;

const ACCESS_PROCESS = [
    "getAccessList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.getList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAllAccess"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.getAllAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    'getAccessInfo'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.getInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    'getAccessPerson'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.getAccessPerson"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    'getNotAccessPerson'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.getNotAccessPerson"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    'addAccess'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.add"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ],[
            "type"=>"model",
            "model"=>"notify.changeAccess"
        ]
    ],
    'editAccess'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.edit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.changeAccess"
        ]
    ],
    'deleteAccess'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"access.delete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],[
            "type"=>"model",
            "model"=>"notify.changeAccess"
        ]
    ],
    'addAccessPerson'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"access.addPerson"
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ],[
            "type"=>"model",
            "model"=>"notify.changeAccess"
        ]
    ],
    'deleteAccessPerson'=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"access.deletePerson"
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],[
            "type"=>"model",
            "model"=>"notify.changeAccess"
        ]
    ],
    "queryNewUserAccessPM"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.getUserForPM",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],

    "addUserAccessPIN"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.addUserPIN",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "addUserAccessCard"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.addUserCard",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "editUserAccessPIN"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.editUserPIN",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "editUserAccessCard"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.editUserCard",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "deleteUserAccessPIN"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.deleteUserPIN",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ]
    ],
    "deleteUserAccessCard"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.deleteUserCard",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ]
    ],
    "editUserAccessGroup"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.setUserAccessGroup",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "editUserSelfAccess"=>[
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "access.setSelfAccess",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
];
