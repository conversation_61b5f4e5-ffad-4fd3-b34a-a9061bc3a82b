<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 10:05:28
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
class CSubUserInMainCheck implements IMiddleware {
    public $id;
    function handle (\Closure $next) {
        global $gApp,$cLog,$cMessage;
        $userId = $gApp["userAliasId"];
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        
        $db = \database\CDatabase::getInstance();
        $count = $db->querySList("select count(*) from PersonalAccount where Role in (".PERENDSROLE.",".COMENDSROLE.") and ParentID = :ParentID and ID = :ID",[":ParentID"=>$userId,":ID"=>$id])[0]["count(*)"];
        $cLog->actionLog("#middle#subUserInMainCheck#id=$id;mainId=$userId;haveUser=$count");
        if($count == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}