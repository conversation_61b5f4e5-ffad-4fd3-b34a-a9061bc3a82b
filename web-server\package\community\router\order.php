<?php

namespace package\community\router;

use \framework\BasicRouter;

class Order extends BasicRouter
{
    public function exec()
    {
        $this->setRouterName('v3', 'web', 'community', 'order', 'create')
        ->setMethod('POST')
        ->addParams(
            'Users',
            'Type',
            'Count',
            'NextTime',
            'VideoSites',
            'ThirdLockUUIDs'
        )
        ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE, RAREAGRADE, RSUBDISTRIBUTOR)
        ->setAuth('web')
        ->setControl('order', 'create');

        /**
         * INS 创建rentManager订单
         */
        $this->setRouterName('v3', 'web', 'community', 'order', 'rentManagerCreate')
            ->setMethod('POST')
            ->addParams('MonthCount:betweenWithScope("1","12")', 'RentManagerCustomerList:string-required', 'TotalPrice:string-number')
            ->addRoles(R_PROJECT_ROLE)
            ->setAuth('web')
            ->setControl('order', 'rentManagerCreate');


        $this->setRouterName('v3', 'web', 'community', 'order', 'computedPriceForDay')
        ->setMethod('POST')
        ->addParams(
            'Users',
            'NextTime',
            'VideoSites',
            'ThirdLockUUIDs'
        )
        ->addRoles(R_PROJECT_ROLE, RPROPERTYMANAGE, RAREAGRADE, RSUBDISTRIBUTOR)
        ->setAuth('web')
        ->setControl('order', 'getPriceToDay');

        /**
         * 获取订单列表
         * 原接口：installgetorderlist
         */
        $this->setRouterName('v3', 'web', 'community', 'order', 'getList')
            ->setMethod('GET')
            ->addParams('Community', 'Type:payment.communitytype', 'Status', 'Key', 'row', 'page')
            ->addRoles(R_PROJECT_ROLE)
            ->setAuth('web')
            ->setControl('order\\getList');

        /**
         * 获取订单列表
         * 原接口：getpmorderlist
         */
        $this->setRouterName('v3', 'web', 'community', 'order', 'getListForPm')
            ->setMethod('GET')
            ->addParams('Type:payment.communitytype', 'Status', 'Key', 'row', 'page')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('order\\getList');

        /**
         * 获取订单列表
         * 原接口：installgetorderlist
         */
        $this->setRouterName('v3', 'app', 'community', 'order', 'getListForApp')
            ->setMethod('GET')
            ->addParams('row', 'page')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('order\\getListForApp');

        /**
         * 订单详情
         * 原接口：installgetorderinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'order', 'getInfo')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE, R_PROJECT_ROLE)
            ->setAuth('web')
            ->setControl('order\\getInfo');

        /**
         * 订单详情
         * 原接口：communitycreateorder
         */
        $this->setRouterName('v3', 'web', 'community', 'order', 'createFeature')
            ->setMethod('POST')
            ->addParams('Type', 'Count', 'ProjectId')
            ->addRoles(R_PROJECT_ROLE, RSUBDISTRIBUTOR, RAREAGRADE)
            ->setAuth('web')
            ->setControl('order\\createFeature'); 

        return $this->getRouters();
    }
}
