#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-21
# Description   :   webtask 构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3              #需要部署的中间件

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

PKG_ROOT=$RSYNC_PATH
PM2=/usr/local/node/bin/pm2
APP_HOME=/usr/local/web
RUN_SCRIPT=async_task_run.sh
RUN_SCRIPT_PATH=$APP_HOME/async-task/script/$RUN_SCRIPT

###################### 开始安装、读取配置 ######################
echo "Begin to install webtask"
echo '读取配置'
source "$PKG_ROOT"/shell/package_start_shell/source.sh $PKG_ROOT

###################### 替换配置文件 ######################
if [ ! -d "$PKG_ROOT"/async-task/ ];then mkdir -p "$PKG_ROOT"/async-task/; fi

# 检查 BM_IP 是否包含端口号
if [[ $BM_IP == *":"* ]]; then
    # 包含端口号，直接拆分
    WEBTASK_BM_HOST=$(echo "$BM_IP" | cut -d':' -f1)
    WEBTASK_BM_PORT=$(echo "$BM_IP" | cut -d':' -f2)
else
    # 不包含端口号，使用默认端口 443
    WEBTASK_BM_HOST=$BM_IP
    WEBTASK_BM_PORT=443
fi

# async-task 替换配置文件的配置
sed -i "
    s/^.*var databaseHost.*/var databaseHost=\"$DATABASEIP\";/g
    s/^.*var databasePort.*/var databasePort=\"$DATABASEPORT\";/g
    s/^.*var databaseUser.*/var databaseUser=\"dbuser01\";/g
    s/^.*var smartHomeAddr.*/var smartHomeAddr=\"$SMARTHOME_DOMAIN\";/g
    s/^.*var smartHomeToken.*/var smartHomeToken=\"$SMARTHOME_TOKEN\";/g
    s/^.*var getWayInnerSocketName.*/var getWayInnerSocketName=\"$GATEWAY_INNER_SOCKET_NAME\";/g
    s/^.*var selfIp.*/var selfIp=\"$WEB_TASK_INNER_IP\";/g
    s/^.*var selfPort.*/var selfPort=\"$WEB_TASK_PORT\";/g
    s/^.*var enableRentManager.*/var enableRentManager=$ENABLE_RENT_MANAGER;/g
    s/^.*var bmServer.*/var bmServer=\"$WEBTASK_BM_HOST\";/g
    s/^.*var bmPort.*/var bmPort=$WEBTASK_BM_PORT;/g" "$PKG_ROOT"/async-task/define.js

###################### 停止webtask ######################
# 先判断 Nodejs 是否安装
if ! node -v 2>/dev/null; then
    echo 'nodejs 不存在，请先安装 nodejs。'
    exit 1
fi

echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi
echo "停止webtask服务"
echo "Stopping async-task/index.js ..."
if [ -f '/usr/local/web/async-task/index.js' ]; then
    $PM2 stop webtask
fi

###################### 复制安装包的文件 ######################
echo '复制webtask安装包的文件'
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi
# async-task
mkdir -p $APP_HOME
cp -rf "$PKG_ROOT"/async-task $APP_HOME

chmod 755 -R $APP_HOME
###################### 创建存放日志的文件夹 ######################
echo "创建存放日志的文件夹"
# nodejs 日志
mkdir -p /var/log/node
touch /var/log/node/record.log
touch /var/log/node/error.log
chmod 666 /var/log/node/*.log

###################### 添加开机启动项 ######################
echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi

###################### 启动服务 ######################
echo '启动 webtask 进程'
if $PM2 list | grep -q -w 'webtask'; then
    $PM2 restart webtask
else
    # 首次使用 pm2 时，要用下面的命令启动 webtask
    $PM2 start /usr/local/web/async-task/index.js --name webtask
fi
sleep 2

echo '检查 webtask 进程的运行状态'
if $PM2 list | grep -w webtask | grep -q -w online; then
    echo 'webtask 启动成功'
else
    echo 'webtask 启动失败'
    exit 1
fi

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi