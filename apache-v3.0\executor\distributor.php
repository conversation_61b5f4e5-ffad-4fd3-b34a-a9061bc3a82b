<?php
/*
 * @Description: gApp参数设置
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-19 14:08:55
 * @LastEditors  : kxl
 */
namespace executor;

include_once __DIR__."/basic.php";
include_once __DIR__."/../interfaces/executor/main.php";

class CDistributor extends \executor\Basic implements \interfaces\executor\main\IExecutor
{
    private $planName;
    private $method;
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    public function parse()
    {
        global $gApp;
        $this->planName = str_replace(COMMONURL, "", $gApp["plan"]["name"]);
        $this->method = $gApp["plan"]["method"];
    }
    public function exec()
    {
        global $gPlans,$gApp,$cLog;
        // 跨域检查
        if ($this->method === "OPTIONS") {
            exit;
        }
        include_once __DIR__."/main.php";
        $errorExecutor = FgetExecutor("errorHandler");
        // 计划存在检查
        if (!array_key_exists($this->planName, $gPlans)) {
            $worker = new CWorkplace($errorExecutor);
            $worker->setAttr("errorCode", State404);
            $worker->todo();
        }

        $plan = $gPlans[$this->planName];
        $method = $plan["method"];
        // 方法类型检查
        if ($method != $this->method) {
            $worker = new CWorkplace($errorExecutor);
            $worker->setAttr("errorCode", State405);
            $worker->todo();
        }

        // 参数获取
        $params =  array_key_exists("params", $plan) ? $plan["params"] : [];
        $method = strtolower($method);
        $data = [];
        $params = is_array($params) ? $params : [$params];
        $cLog->actionLog("#executor#distributor#accept params=".json_encode($params));
        foreach ($params as $param) {
            $data[$param] = $this->services["param"]->parse($param, $method);
        }

        $logData = $data;
        foreach (['passwd', 'password', 'oldpasswd', 'oldpassword'] as $keyword) {
            if (isset($logData[$keyword])) {
                $logData[$keyword] = "**{$keyword}**";
            }
        }
        $cLog->actionLog("#executor#distributor#http param=".json_encode($logData));
        //过滤记录建军的探测帐号，目前探测的接口POST请求的只有登录，其他的都是get请求接口
        if ($gApp['plan']['name'] == '/apache-v3.0/login' && isset($data['Account']) && $data['Account'] === 'ljj_install') {
            $cLog->IsWriteBusinessLog = false;
        }
        $cLog->businessLog("request uri={url}, method={method}, ip={ip}, param={data}",
            ['url' => $gApp["plan"]["name"], 'method' => $gApp["plan"]["method"], 'ip' => \util\computed\getIp(), 'data' => json_encode($logData)]);
        $roles = array_key_exists("roles", $plan) ? $plan["roles"] : (array_key_exists("role", $plan) ? $plan["role"] : null);

        $gApp["plan"]["params"] = $data;
        $gApp["plan"]["data"] = $data;
        $active = array_key_exists("active", $plan) ? $plan["active"] : true;
        $auth = array_key_exists("auth", $plan) ? $plan["auth"] : true;

        $gApp["plan"]["roles"] = $roles;
        $gApp["plan"]["auth"] = $auth;
        $gApp["plan"]["active"] = $active;
        $gApp["plan"]["type"] = $plan["type"];
        
        $process = $plan["process"];
        $gApp["plan"]["task"] = $process;
    }
}
