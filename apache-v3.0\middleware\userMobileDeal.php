<?php
/*
 * @Description: 对于Mobile为空字符串的处理
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 17:06:35
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";

include_once __DIR__."/../util/computed.php";
class CUserMobileDeal implements IMiddleware {
    public $Mobile;
    function handle (\Closure $next) {
        global $cLog;
        $params = ["MobileNumber"=>""];
        $mobile = \util\model\getParamsFromContainer($params,$this->dataContainer)["MobileNumber"];
        
        $cLog->actionLog("#middle#userMobileDeal#Mobile=".$mobile);
        if($mobile === "") {
            \util\computed\setGAppData(["MobileNumber"=>null]);
        }
        $next();
    }
}