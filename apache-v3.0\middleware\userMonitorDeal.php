<?php
/*
 * @Description: 对于有室内机方案MAC和Location的处理
 * @version:
 * @Author: cj
 * @Date: 2021-03-23 11:27:35
 * @LastEditors  : cj
 */
namespace middleware;

include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/model.php";

include_once __DIR__."/../util/computed.php";
class CUserMonitorDeal implements IMiddleware
{
    public $Mobile;
    function handle(\Closure $next)
    {
        global $cLog;
        $params = ["IsComMonitor"=>""];
        $isComMonitor = \util\model\getParamsFromContainer($params,$this->dataContainer)["IsComMonitor"];

        $cLog->actionLog("#middle#userMonitorDeal#IsComMonitor=".$isComMonitor);
        if ($isComMonitor === "0") {
            \util\computed\setGAppData(["MAC"=>null]);
            \util\computed\setGAppData(["Location"=>null]);
        }
        $next();
    }
}
