<?php
/**
 * @Author:zyc
 * @Date 2021/5/26
 * @LastEditors:zyc
 */

namespace plan\process;

const APARTMENT_PROCESS = [
    "getPMAptList" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "apartment.getPMAptList"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "modifyPMApt" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "apartment.modifyPMApt"
        ], [
            "type" => "model",
//            "dataContainer"=>"setUserAliasToAccount",
            "model" => "notify.userMainComUpdate"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "getPMAptInfo" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "apartment.getPMAptInfo"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getComIndoorMonitor" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "apartment.getComIndoorMonitor"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["isComMonitor" => "isComMonitor"]
        ]
    ]
];
