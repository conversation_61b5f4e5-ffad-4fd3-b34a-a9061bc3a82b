<?php

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/computed.php";

/**
 * @description 根据PM APP的帐号获取PM信息
 * <AUTHOR> 2022/4/15 13:50 V6.4
 * @lastEditor csc 2022/4/15 13:50 V6.4
 */
class CGetPMAppUserId implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        $cLog->actionLog("#middle#getPMAppUserId#");
        $params = ["PMAppPMAccount" => ""];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $pMAppPMAccount = $params['PMAppPMAccount'];
        \util\computed\setGAppData(["userId"=>$pMAppPMAccount["ID"],"user"=>$pMAppPMAccount["Account"]]);
        $next();
    }
}