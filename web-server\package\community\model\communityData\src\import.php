<?php
namespace package\community\model\communityData\src;

use Exception;
use package\community\model\communityData\config\Code;

trait Import
{
    /**
     * @description: 社区导入，这部分的util比较特殊，都在method/import.php
     * @author: kxl
     */
    public function importResolve()
    {
        $params = [PROXY_ROLE['installerId'], 'CommunityID'];
        list($installerId, $communityId) = $this->getParams($params);

        $this->loadUtil('account', true);
        $this->loadUtil('common', true);
        // 检查是否有权限导入
        $this->utils->_common->account->checkProjectInIns($communityId, $installerId);
        $communityData = $this->utils->self->getInfo($communityId);
        $basic = $communityData['basic'];

        // 检查是否正在导入
        $this->loadUtil('projectData', true);
        $this->utils->_common->projectData->checkProjectImporting($basic['UUID']);
        // 检查是否超出同时导入数目
        $this->utils->_common->projectData->checkImportingNumber($installerId, $communityId);

        // 是否是必带室内机方案
        $isIndoorPlan = $this->utils->self->getFeaturePlan($communityId)['indoor'];
        $isAbleSmartHome = $this->utils->self->isSmartHomeAble($communityId);
        // 智能家居不检测室内机
        if ($isAbleSmartHome) {
            $isIndoorPlan = 0;
        }
        // $isAbleSmartHome = $this->utils->self->isSmartHomeAble($communityId);
        // if ($isAbleSmartHome) {
        //     $layouts = $this->utils->self->getAllLayout($communityId);
        // }

        // 记录审计日志
        $insData = $this->utils->_common->account->getManagerInfo($installerId);
        $this->auditLog->setLog(
            AUDIT_CODE_IMPORT_COMMUNITY,
            $this->env,
            [$insData['Account'].'-'.$basic['Location']],
            $insData['Account']
        );

        $keys = $this->utils->self->getImportCommunityColumnKey();
        $buildIndex = $keys->building;
        $aptIndex = $keys->apt;
        $aptNameIndex = $keys->aptName;
        $deviceIndex = $keys->device;
        $nameFirstIndex = $keys->firstName;
        $nameLastIndex = $keys->lastName;
        $emailIndex = $keys->email;
        $mobileIndex = $keys->mobileNumber;
        $phoneIndex = $keys->phone1;
        $phone2Index = $keys->phone2;
        $phone3Index = $keys->phone3;
        $callTypeIndex = $keys->callType;
        // $layoutIndex = $keys->layout;
        $phoneCode = $keys->phoneCode;
        $webRelayIDIndex = $keys->webRelayID;
        $floorIndex = $keys->floor;
        $analogHandsetIndex = $keys->analogHandset;
        $fileList = $this->utils->self->resolveImportCommunityFile();

        if (!empty($fileList)) {
            // Analog Device校验
            $this->loadUtil('device');
            $this->utils->device->checkAnalogDevice($fileList, $communityId);
        }

        // rps 设备检测
        $deviceForRps = [];
        // 新增的房间数量
        $roomNum = 0;
        // 新增设备数量
        $deviceNum = 0;
        /*新增条目
         * {
         *   "type": "pubDev" | "buildDev" | "room",
         *   "devices": ["xxxxx"],
         *   "user": ["xxxxx"],
         * }
         */
        $items = [];

        foreach ($fileList as $key => $value) {
            $row = $value['row'];
            $this->log->debug("import community index={index}", ["index" => $key]);

            $building = $value[$buildIndex];
            $devices = $this->utils->_common->projectData->resolvesImportDevices($value[$deviceIndex]);

            // 社区公共设备
            if ($building === "") {
                // 楼栋为空且无设备时需要提示
                if (count($devices) == 0) {
                    $this->output->echoErrorMsg(STATE_BUILD_INVALID_LINE, ['externalErrorObj' => Code::EXT_STATE_IMPORT_UNIT_AND_DEVICE_EMPTY], [$row]);
                }
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($items, [
                        "row" => $value['row'],
                        "type" => "pubDev",
                        "device" => $device
                    ]);
                    $deviceNum += 1;
                }
                continue;
            }

            $apt = $value[$aptIndex];
            //房间为空，说明导入公共设备
            if ($apt == "") {
                // 房间为空且无设备时需要提示
                if (count($devices) == 0) {
                    $this->output->echoErrorMsg(STATE_APT_INVALID_LINE, ['externalErrorObj' => Code::EXT_STATE_IMPORT_APT_AND_DEVICE_EMPTY], [$row]);
                }
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($items, [
                        "row" => $value['row'],
                        "type" => "buildDev",
                        'build' => $building,
                        "device" => $device
                    ]);
                    $deviceNum += 1;
                }
                continue;
            }

            // 房间有效性检测
            $this->utils->self->checkAptExist($apt, $building, $communityId, $row);
            $webRelayID = $value[$webRelayIDIndex];
            $this->loadUtil('account', true);
            $room = [];
            // apt check通过后转大写存储
            $room["name"] = strtoupper($apt);

            $aptName = $value[$aptNameIndex];
            //aptName 合法性校验
            $isLegitimate = $this->utils->_common->common->checkName(63, $aptName);
            if (!$isLegitimate) {
                $this->output->echoErrorMsg(STATE_IMPORT_INVALID_APT_NAME, ['externalErrorObj' => Code::EXT_STATE_APT_NAME_INVALID], [$row]);
            }
            $room["aptName"] = $aptName;
            $room["devices"] = [];
            $firstName = $value[$nameFirstIndex];
            $lastName = $value[$nameLastIndex];
            $userName = $this->utils->_common->account->firstLastNameDeal($firstName, $lastName);
            $email = $value[$emailIndex];
            $mobile = $value[$mobileIndex];
            $analogHandset = $value[$analogHandsetIndex];

            // layout检测
            // if ($isAbleSmartHome) {
            //     $layout = $value[$layoutIndex];
            //     if (!array_key_exists($layout, $layouts)) {
            //         $this->output->echoErrorMsg(STATE_INVALID_LAYOUT);
            //     }
            //     $room['layout'] = $layout;
            //     $room['layoutId'] = $layouts[$layout];
            // }

            $callType = $value[$callTypeIndex];
            // 检测web RelayID有效性
            $webRelayID = $this->utils->self->checkWebRelayID($webRelayID, $row);
            $room["webRelayID"] = $webRelayID;
            // CallType必填
            $this->utils->self->checkCallTypeValid($callType);
            $room["callType"] = $callType;

            // Floor添加房间时必填,导入非必填
            $floor = $value[$floorIndex];
            $this->utils->self->floorCheck($floor, $row);
            $room['Floor'] = $floor;

            // 添加模拟设备
            if (!empty($analogHandset)) {
                $analogDeviceArrays = explode(';', $analogHandset);
                $analogDevices = [];
                foreach ($analogDeviceArrays as $analogDeviceInfo) {
                    $analogDevicesTemp = [];
                    $analogDeviceInfos = explode(',', $analogDeviceInfo);
                    $analogDevicesTemp['analogDeviceName'] = $analogDeviceInfos[0];
                    $analogDevicesTemp['analogDeviceNumber'] = $analogDeviceInfos[1];
                    $analogDevicesTemp['analogDeviceDtmf'] = $analogDeviceInfos[2];
                    $analogDevices[] = $analogDevicesTemp;
                }
                $room['analogDevices'] = $analogDevices;
            }

            // 只导入房间
            if (($firstName == "" || $lastName == "") && count($devices) == 0) {
                if ($isIndoorPlan) {
                    // 未携带绑定室内机
                    $this->output->echoErrorMsg(STATE_INDOOR_MONITOR_REQUIRED, ['externalErrorObj' => Code::EXT_STATE_INDOOR_MONITOR_REQUIRED]);
                }

                // 检查房间号是否存在
                if ($this->utils->self->checkRoomExist($communityId, $building, $apt)) {
                    $this->output->echoErrorMsg(STATE_APT_EXIT, ['externalErrorObj' => Code::EXT_STATE_APT_EXIST], [$apt], [$apt]);
                }
                $roomNum += 1;
                array_push($items, [
                    "row" => $value['row'],
                    "type" => "room",
                    'build' => $building,
                    "room" => $room
                ]);
                continue;
            }

            // 房间设备有效应检测
            // 指定云增加三方设备需求
            if ($this->utils->self->getThirdPartDeviceSwitch()) {
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceBrandValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($room["devices"], $device);
                }
            } else {
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($room["devices"], $device);
                }
            }

            // 添加房间+设备
            if ($firstName == "" || $lastName == "") {
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                }
                array_push($items, [
                    "row" => $value['row'],
                    "type" => "room",
                    'build' => $building,
                    "room" => $room
                ]);
                continue;
            }

            // 检测是否符合存在的房间
            $newRoom = 1;
            $this->loadUtil('common', true);
            $buildData = $this->utils->_common->common->getTableInfo(
                ["UnitName"=>$building, "MngAccountID"=>$communityId],
                ['ID'],
                PROXY_TABLES['communityUnit']
            );
            if (count($buildData) != 0) {
                $buildData = $buildData[0];
                $roomData = $this->utils->_common->common->getTableInfo(
                    ["RoomName"=>$apt,"UnitID"=>$buildData["ID"]],
                    ['ID'],
                    PROXY_TABLES['communityRoom']
                );
                if (count($roomData) != 0) {
                    $roomData = $roomData[0];
                    $accountData = $this->dao->personalAccount->selectByArray([['UnitID', $buildData["ID"]], ['RoomID', $roomData["ID"]],
                        ['ParentID', $communityId], ['Special', 1]], 'ID');
                    if (count($accountData) == 0) {
                        $this->output->echoErrorMsg(STATE_RESIDENT_IN_ROOM, ['externalErrorObj' => Code::EXT_STATE_APT_IS_OLD_ROOM], [$apt]);
                    }
                    $newRoom = 0;
                } else {
                    // 说明是要新增房间
                    $roomNum += 1;
                    if ($isIndoorPlan) {
                        $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                    }
                }
            } else {
                // 说明是要新增房间
                $roomNum += 1;
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                }
            }

            // 检测邮箱的有效性
            $this->utils->self->checkEmailValid($email);
            // 检测手机的有效性
            $this->utils->self->checkMobileValid($mobile);
            // 用户名检测
            $this->utils->self->checkUserNameValid($userName);

            // 是否新建房间
            $room['isNew'] = $newRoom;
            $room['user'] = [];
            $room['user']['firstName'] = $firstName;
            $room["user"]["lastName"] = $lastName;
            $room['user']['name'] = $userName;
            $room["user"]["email"] = $email == "" ? null : $email;
            $room["user"]["mobile"] = $mobile == "" ? null : $mobile;
            $room["user"]["callType"] = $callType;
            $room["user"]["phoneCode"] = $value[$phoneCode] ?: "";
            $room["user"]["phone"] = $value[$phoneIndex];
            $room["user"]["phone2"] = $value[$phone2Index];
            $room["user"]["phone3"] = $value[$phone3Index];
            array_push($items, [
                "row" => $value['row'],
                "type" => "room",
                'build' => $building,
                "room" => $room
            ]);
        }

        // 房间数量上限
        $this->utils->self->checkMaxRoom($communityId, $roomNum);

        // 超过1000条上限
        if ($roomNum + $deviceNum > 1000) {
            $this->output->echoErrorMsg(STATE_IMPORT_LESS_DATA, ['externalErrorObj' => Code::EXT_STATE_IMPORT_NUM_LIMIT], [1000]);
        }
        // rps检测
        $this->utils->_common->projectData->rpsCheckMAC($deviceForRps, $basic['ParentID'], $communityId);

        // 插入导入任务
        $this->utils->_common->projectData->createTask($basic['UUID'], '0', $items, null, null, PACKAGE_COMMUNITY);
    }

    /**
     * @description: 导入社区函数
     * @param taskId
     * @return void
     * @author: kxl
     */
    public function import()
    {
        $gSendSmartHomeTaskIds = $this->share->util->getGlobalParam('gSendSmartHomeTaskIds');
        $params = ['taskId'];
        list($taskId) = $this->getParams($params);
        $this->log->debug('begin import community taskId={taskId}', ['taskId' => $taskId]);
        // 读取任务，并设置为进行中
        $this->loadUtil('projectData', true);
        $communityImportType = 0;
        $res = $this->utils->_common->projectData->setImporting($taskId, $communityImportType);
        $this->log->debug('set import result = {res}', ['res' => $res]);
        if (!$res) {
            throw new \Exception('Import error, there is not task uuid='.$taskId);
        }
        $this->log->debug('set importing success');

        $this->loadUtil('common', true);
        $taskData = $this->utils->_common->common->getTableInfo(
            ['UUID' => $taskId],
            '*',
            PROXY_TABLES['importProjectTask']
        )[0];
        $this->loadUtil('account', true);
        $communityData = $this->utils->_common->account->getManagerInfoUUID($taskData['AccountUUID']);
        $communityId = $communityData['ID'];
        $failTaskDetail = [];
        // 是否是必带室内机方案
        $isIndoorPlan = $this->utils->self->getFeaturePlan($communityId)['indoor'];
        $isAbleSmartHome = $this->utils->self->isSmartHomeAble($communityId);
        $communityData['isIndoorPlan'] = $isIndoorPlan;
        $communityData['isAbleSmartHome'] = $isAbleSmartHome;

        // 开始导入
        $res = $this->utils->_common->projectData->getDetail($taskId);

        $this->log->debug('begin import community, import data={data}', ['data' => $res]);
        foreach ($res as $value) {
            $this->db->begin();
            try {
                $data = json_decode($value['Item'], true);
                $this->dealImportDetail($data, $communityData);
                $this->utils->_common->projectData->setImportDetailSuccess($value['UUID']);
                $this->db->commit();
                $this->notifyEvent->notify();
                if (count($gSendSmartHomeTaskIds) != 0) {
                    $this->share->util->sendSmartHomeTask();
                }
            } catch (Exception $e) {
                $this->notifyEvent->clear();
                $this->db->rollback();
                $msg = $e->getMessage();
                $this->log->debug("import community result error={error}", ["error" => $e]);
                echo $msg;
                // 记录错误任务，增加重试机制
                array_push($failTaskDetail, $value);
                $this->utils->_common->projectData->setImportDetailFail($value['UUID'], $msg);
            }
        }
        // 低于一定数目失败进行重试，否则直接失败
        if (count($failTaskDetail) > 0 && count($failTaskDetail) <= IMPORT_NEED_REDO_NUMBER) {
            $this->log->debug("begin redo fail import failTaskDetail={failTaskDetail}", ["failTaskDetail" => $failTaskDetail]);
            foreach ($failTaskDetail as $value) {
                $this->db->begin();
                try {
                    $this->log->debug("Redo fail import detail ID={ID}", ["ID" => $value['ID']]);
                    $data = json_decode($value['Item'], true);
                    $this->dealImportDetail($data, $communityData);
                    $this->utils->_common->projectData->setImportDetailSuccess($value['UUID']);
                    $this->db->commit();
                    $this->notifyEvent->notify();
                    if (count($gSendSmartHomeTaskIds) != 0) {
                        $this->share->util->sendSmartHomeTask();
                    }
                } catch (Exception $e) {
                    $this->notifyEvent->clear();
                    $this->db->rollback();
                    $msg = $e->getMessage();
                    $this->log->debug("import community result error={error}", ["error" => $e]);
                    echo $msg;
                }
            }
        }

        $this->utils->_common->projectData->setImportSuccess($taskId);
    }

    /**
     * @Description: 处理导入的信息
     * @param {*} $value 指importProjectDetail记录的导入信息
     * @param {*} $communityData 社区信息
     * @param {*} $$isOpenApi 是否是openApi处理
     * @return {*}
     */
    private function dealImportDetail($data, $communityData, $isOpenApi = false)
    {
        $comNetWork = 0;
        $comArmingFunction = 0;
        $comStairShow = 1;
        $communityId = $communityData['ID'];

        $resDevices = [];
        $resBuildings = [];
        $this->loadUtil('device', true);
        $type = $data['type'];
        if ($type === 'pubDev') {
            $device = $data['device'];
            $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($device['type']);
            $this->loadModel('device', false, [
                'dataContainer' => [
                    'MAC' => $device['mac'],
                    'Location' => $device['location'],
                    'Type' => $device['type'],
                    'NetGroupNumber' => $comNetWork,
                    'Relay' => $comRelay,
                    'StairShow' => $comStairShow,
                    'ArmingFunction' => $comArmingFunction,
                    'IsAllBuild' => 1,
                    'Builds' => [],
                    PROXY_ROLE['projectId'] => $communityId
                ]
            ]);
            $id = $this->models->device->addPublic();
            $resDevices[] = ['ID'=>$id, 'MAC'=>$device['mac']];
        } elseif ($type === 'buildDev') {
            $building = $data['build'];
            $buildId = $this->utils->self->getBuildId($building, $communityId);
            $resBuildings[] = ['ID'=>$buildId, 'Name'=>$building];
            $device = $data['device'];
            $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($device['type']);
            $this->loadModel('device', false, [
                'dataContainer' => [
                    'MAC' => $device['mac'],
                    'Location' => $device['location'],
                    'Type' => $device['type'],
                    'NetGroupNumber' => $comNetWork,
                    'Relay' => $comRelay,
                    'StairShow' => $comStairShow,
                    'ArmingFunction' => $comArmingFunction,
                    'Build' => $buildId,
                    'IsAllBuild' => 0,
                    'Builds' => [],
                    PROXY_ROLE['projectId'] => $communityId
                ]
            ]);
            $id = $this->models->device->addPublic();
            $resDevices[] = ['ID'=>$id, 'MAC'=>$device['mac']];
        } elseif ($type === 'room') {
            $room = $data['room'];
            $building = $data['build'];
            $buildId = $this->utils->self->getBuildId($building, $communityId);
            $resBuildings[] = ['ID'=>$buildId, 'Name'=>$building];
            // 新增房间
            if (!$this->utils->self->checkRoomExist($communityId, $building, $room['name'])) {
                $defaultEnableIpDirect = 1;
                $this->loadUtil('user');
                $roomData = $this->utils->user->addRoomNode(
                    $buildId,
                    $communityId,
                    $room['name'],
                    $room['aptName'],
                    $room['callType'],
                    $defaultEnableIpDirect,
                    $room['webRelayID'],
                    $room['Floor']
                );
                $id = $roomData['ID'];
                $personalAccountUUID = $roomData['UUID'];
            } else {
                $id = $this->utils->self->getRoomId($buildId, $room['name']);
                $personalAccountUUID = $this->dao->personalAccount->selectByID($id,'UUID')[0]['UUID'];
            }

            $buildUuid = $this->utils->self->getBuildingUUID($building, $communityId);
            $communityUUId = $this->utils->self->getCommunityUuid($communityId);

            //添加模拟设备
            $analogDevices = $room['analogDevices'];
            if (!empty($analogDevices)) {
                $this->loadModel('device');
                $this->models->device->importAddAnalogDevices($communityUUId, $buildUuid, $personalAccountUUID, $analogDevices);
            }

            // 是否携带绑定室内机
            $indoorMac = '';
            if (array_key_exists('indoorMAC', $room)) {
                $indoorMac = $room['indoorMAC'];
            }
            $devices = $room['devices'];

            $this->loadUtil('user');
            $isIndoorPlan = $communityData['isIndoorPlan'];
            $isAbleSmartHome = $communityData['isAbleSmartHome'];
            // V6.5.1 bug修改:需判断原来房间有没有存在特殊室内机，否则会加入两台特殊室内机
            $this->loadUtil('account', true);
            $userInfo = $this->utils->_common->account->getUserInfo($id);
            $this->loadUtil('common', true);
            $haveSpecialIndoorDevice = $this->utils->_common->common->getTableCount(PROXY_TABLES['devicesSpecial'], ['Account' => $userInfo['Account']]);
            if ($isAbleSmartHome && $isIndoorPlan && $haveSpecialIndoorDevice === 0) {
                $this->utils->user->addDeviceSpecial($userInfo['Account'], '');
            }
            // 添加设备
            foreach ($devices as $device) {
                $mac = $device['mac'];
                $type = $device['type'];
                $location = $device['location'];

                if ($mac === $indoorMac && !$isAbleSmartHome && $haveSpecialIndoorDevice === 0) {
                    $comRelay = $this->utils->_common->device->getInitRelayForDeviceType(DEVICE_TYPE['indoor']);
                    $this->utils->user->setAssocParams()->addDefIndoor([
                        'ID' => $id,
                        'MAC' => $mac,
                        'Relay' => $comRelay,
                        'Location' => $location,
                        PROXY_ROLE['projectId'] => $communityId
                    ]);
                } else {
                    $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($type);
                    if (array_key_exists($type, THIRD_PART_DEVICE_BRAND)) {
                        $brand = THIRD_PART_DEVICE_BRAND[$type];
                        $type = DEVICE_TYPE['indoor'];
                    } else {
                        $brand = 0; //默认AK设备
                    }
                    $this->loadModel('device', false, [
                        'dataContainer' => [
                            "ID" => $id,
                            'MAC' => $mac,
                            'Location' => $location,
                            'Type' => $type,
                            'NetGroupNumber' => $comNetWork,
                            'Relay' => $comRelay,
                            'StairShow' => $comStairShow,
                            'ArmingFunction' => $comArmingFunction,
                            PROXY_ROLE['projectId'] => $communityId,
                            'SecurityRelay' => '',
                            'Brand' => $brand
                        ]
                    ]);
                    $this->models->device->addUser();
                }

                $devInfo = $this->dao->devices->selectByKey('MAC', $mac);
                if (!empty($devInfo)) {
                    $resDevices[] = ['ID'=>$devInfo[0]['ID'], 'MAC'=>$mac];
                }
            }

            if (array_key_exists('user', $room)) {
                $user = $room['user'];
                $this->loadModel('user', false, [
                    'dataContainer' => [
                        'ID' => $id,
                        'FirstName' => $user['firstName'],
                        'LastName' => $user['lastName'],
                        'Name' => $user['name'],
                        'Email' => $user['email'],
                        'MobileNumber' => $user['mobile'],
                        'Phone' => $user['phone'],
                        'Phone2' => $user['phone2'],
                        'Phone3' => $user['phone3'],
                        'PhoneCode' => $user['phoneCode'],
                        'CallType' => $user['callType'],
                        'Language' => $communityData['Language'],
                        PROXY_ROLE['projectId'] => $communityId,
                        'IsOpenApi' => $isOpenApi,
                        'AccessFloor' => $user['accessFloor'],
                    ]
                ]);
                $userData = $this->models->user->addUser();
                $userData['MobileNumber'] = $user['mobile'];
                // 判断是否是新小区
                $this->loadUtil('common', true);
                $isNew = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['communityInfo'], [
                    'IsNew'], ['AccountID' => $communityId])[0]['IsNew'];
                if ($isNew === '1') {
                    // 把用户加到默认权限组
                    $this->loadModel('access', true, [
                        'dataContainer' => [
                            'ID' => $id,
                            'Step' => 0,
                            PROXY_ROLE['projectId'] => $communityId
                        ]
                    ]);
                    $this->models->common->access->addUser();
                }
            }
        }
        return ['user'=>$userData,'building'=>$resBuildings,'devices'=>$resDevices];
    }

    /**
     * @description: 获取导入详情
     * @author: lxf
     */
    public function importDetails()
    {
        $params = ["ImportTaskUUIDs"];
        list($importTaskUUIDs) = $this->getParams($params);
        $this->loadModel("projectData", true, ["dataContainer"=>["ImportTaskUUIDs"=> $importTaskUUIDs, "ProjectType" => 0]]);
        $data = $this->models->common->projectData->queryImportDetails();
        return ["data" => $data];
    }

    /**
     * @description: openapi导入社区 同步接口：importResolve和import两个方法的结合
     * openapi不需要做智能家居二期的修改（带室内机问题）
     * @author: czw
     */
    public function importComunityOpenapi()
    {
        $params = [PROXY_ROLE['pmId'], 'CommunityID', 'Community', 'IsV4', PROXY_ROLE['projectId']];
        list($pmId, $communityId, $fileList, $isV4, $projectId) = $this->getParams($params);

        if ($isV4) {
            $communityId = $projectId;
        }

        //设置通知方式
        $gApp = $this->share->util->getGApp();
        $gApp["operator"]["projectId"] = $communityId;
        $this->db->setAnalysisType('importCommunity');
        $this->db->setAnalysisValue(['communityId' => "$communityId"]);
        $this->db->setProjectId($communityId);

        $this->loadUtil('account', true);
        $this->loadUtil('common', true);
        $this->loadUtil('projectData', true);

        // 检查是否有权限导入
        $data = $this->db->querySList(
            "select B.ID,B.TimeZone,B.CustomizeForm,B.Account from Account A join PropertyMngList M on A.ID = M.PropertyID join Account B on B.ID = M.CommunityID where M.CommunityID = :CommunityID and A.ID = :ID and B.Grade != ".OFFICEGRADE,
            [":ID"=>$pmId,":CommunityID"=>$communityId]
        );
        if (count($data) == 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_PM]);
        }

        $communityData = $this->utils->self->getInfo($communityId);
        $basic = $communityData['basic'];
        $isEnableLandline = $this->share->util->getSpecifyBitLE($communityData['info']['Switch'], 1);

        // 是否是必带室内机方案
        $isIndoorPlan = $this->utils->self->getFeaturePlan($communityId)['indoor'];
        
        // $isAbleSmartHome = $this->utils->self->isSmartHomeAble($communityId);
        // if ($isAbleSmartHome) {
        //     $layouts = $this->utils->self->getAllLayout($communityId);
        // }

        $keys = $this->utils->self->getImportCommunityColumnKey();
        $buildIndex = $keys->building;
        $aptIndex = $keys->apt;
        $aptNameIndex = $keys->aptName;
        $deviceIndex = $keys->device;
        $nameFirstIndex = $keys->firstName;
        $nameLastIndex = $keys->lastName;
        $emailIndex = $keys->email;
        $mobileIndex = $keys->mobileNumber;
        $phoneIndex = $keys->phone1;
        $phone2Index = $keys->phone2;
        $phone3Index = $keys->phone3;
        $callTypeIndex = $keys->callType;
        $layoutIndex = $keys->layout;
        $phoneCodeIndex = $keys->phoneCode;
        $floorIndex = $keys->floor;
        $accessFloorIndex = $keys->accessFloor;


        // rps 设备检测
        $deviceForRps = [];
        // 新增的房间数量
        $roomNum = 0;
        // 新增设备数量
        $deviceNum = 0;
        /*新增条目
         * {
         *   "type": "pubDev" | "buildDev" | "room",
         *   "devices": ["xxxxx"],
         *   "user": ["xxxxx"],
         * }
         */
        $items = [];

        foreach ($fileList as $key => $value) {
            $row = $key;
            $this->log->debug("import community index={index}", ["index" => $key]);

            $building = $value[$buildIndex];
            $devices = $this->utils->_common->projectData->resolvesImportDevices($value[$deviceIndex]);

            // 社区公共设备
            if ($building === "") {
                // 楼栋为空且无设备时需要提示
                if (count($devices) == 0) {
                    $this->output->echoErrorMsg(STATE_BUILD_INVALID_LINE, ['externalErrorObj'=> Code::EXT_STATE_IMPORT_UNIT_AND_DEVICE_EMPTY], [$row]);
                }
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($items, [
                        "row" => $value['row'],
                        "type" => "pubDev",
                        "device" => $device
                    ]);
                    $deviceNum += 1;
                }
                continue;
            }

            $apt = $value[$aptIndex];
            //房间为空，说明导入公共设备
            if ($apt == "") {
                // 房间为空且无设备时需要提示
                if (count($devices) == 0) {
                    $this->output->echoErrorMsg(STATE_APT_INVALID_LINE, ['externalErrorObj' => Code::EXT_STATE_IMPORT_APT_AND_DEVICE_EMPTY], [$row]);
                }
                foreach ($devices as $device) {
                    $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                    // rps检测
                    array_push($deviceForRps, $device['mac']);
                    array_push($items, [
                        "row" => $value['row'],
                        "type" => "buildDev",
                        'build' => $building,
                        "device" => $device
                    ]);
                    $deviceNum += 1;
                }
                continue;
            }

            // 房间有效性检测
            $this->utils->self->checkAptExist($apt, $building, $communityId, $row);
            $room = [];
            $room["name"] = strtoupper($apt);
            $room["aptName"] = $value[$aptNameIndex];
            $room['Floor'] = $value[$floorIndex] ? $value[$floorIndex] : '';
            if (!$this->utils->self->isValidFloorOrEmpty($room['Floor'])) {
                $this->output->echoErrorMsg(STATE_ADD_OUT_FLOOR);
            }
            $room["devices"] = [];
            $firstName = $value[$nameFirstIndex];
            $lastName = $value[$nameLastIndex];
            $userName = $this->utils->_common->account->firstLastNameDeal($firstName, $lastName);
            $email = $value[$emailIndex];
            $mobile = $value[$mobileIndex];
            $accessFloor = $value[$accessFloorIndex] ? $value[$accessFloorIndex] : '';

            // layout检测
            // if ($isAbleSmartHome) {
            //     $layout = $value[$layoutIndex];
            //     if (!array_key_exists($layout, $layouts)) {
            //         $this->output->echoErrorMsg(STATE_INVALID_LAYOUT);
            //     }
            //     $room['layout'] = $layout;
            //     $room['layoutId'] = $layouts[$layout];
            // }

            // 只导入房间
            if (($firstName == "" || $lastName == "") && count($devices) == 0) {
                if ($isIndoorPlan) {
                    // 未携带绑定室内机
                    $this->output->echoErrorMsg(STATE_INDOOR_MONITOR_REQUIRED, ['externalErrorObj' => Code::EXT_STATE_INDOOR_MONITOR_REQUIRED]);
                }

                // 检查房间号是否存在
                if ($this->utils->self->checkRoomExist($communityId, $building, $apt)) {
                    $this->output->echoErrorMsg(STATE_APT_EXIT, ['externalErrorObj' => Code::EXT_STATE_APT_EXIST], [$apt]);
                }
                $roomNum += 1;
                array_push($items, [
                    "row" => $value['row'],
                    "type" => "room",
                    'build' => $building,
                    "room" => $room
                ]);
                continue;
            }

            // 房间设备有效应检测
            foreach ($devices as $device) {
                $this->utils->self->checkDeviceValid($device['type'], $device['location']);
                // rps检测
                array_push($deviceForRps, $device['mac']);
                array_push($room["devices"], $device);
            }

            // 添加房间+设备
            if ($firstName == "" || $lastName == "") {
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                }
                array_push($items, [
                    "row" => $value['row'],
                    "type" => "room",
                    'build' => $building,
                    "room" => $room
                ]);
                continue;
            }

            // 检测是否符合存在的房间
            $newRoom = 1;
            $buildData = $this->utils->_common->common->getTableInfo(
                ["UnitName"=>$building, "MngAccountID"=>$communityId],
                ['ID'],
                PROXY_TABLES['communityUnit']
            );
            if (count($buildData) != 0) {
                $buildData = $buildData[0];
                $roomData = $this->utils->_common->common->getTableInfo(
                    ["RoomName"=>$apt,"UnitID"=>$buildData["ID"]],
                    ['ID'],
                    PROXY_TABLES['communityRoom']
                );
                if (count($roomData) != 0) {
                    $roomData = $roomData[0];
                    $accountData = $this->dao->personalAccount->selectByArray([['UnitID', $buildData["ID"]], ['RoomID', $roomData["ID"]],
                        ['ParentID', $communityId], ['Special', 1]], 'ID');
                    if (count($accountData) == 0) {
                        $this->output->echoErrorMsg(STATE_RESIDENT_IN_ROOM, ['externalErrorObj' => Code::EXT_STATE_APT_IS_OLD_ROOM], [$apt]);
                    }
                    $newRoom = 0;
                } else {
                    // 说明是要新增房间
                    $roomNum += 1;
                    if ($isIndoorPlan) {
                        $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                    }
                }
            } else {
                // 说明是要新增房间
                $roomNum += 1;
                if ($isIndoorPlan) {
                    $room['indoorMAC'] = $this->utils->self->getIndoorMAC($devices);
                }
            }

            // 检测邮箱的有效性
            $this->utils->self->checkEmailValid($email);
            // 检测手机的有效性
            $this->utils->self->checkMobileValid($mobile);
            // 用户名检测
            $this->utils->self->checkUserNameValid($userName);
            // 检测用户楼层的有效性
            $this->loadUtil('user');
            $this->utils->user->checkAccessFloor($accessFloor);

            // V6.4.2 检验落地号码自动填入规则 $phone表示落地号
            if (SERVER_LOCATION === 'cn') {
                $phone = $this->utils->self->checkAutoInFillingMobile($value['IsAutoFillPhone'], $isEnableLandline, $mobile);
            } else {
                $phone = '';
            }

            $callType = $value[$callTypeIndex];
            $this->utils->self->checkCallTypeValid($callType);
            // 是否新建房间  V6.4.2 修改openapi部分不对外提供字段,设为默认值
            $room['isNew'] = $newRoom;
            $room['user'] = [];
            $room['user']['firstName'] = $firstName;
            $room["user"]["lastName"] = $lastName;
            $room['user']['name'] = $userName;
            $room["user"]["email"] = $email == "" ? null : $email;
            $room["user"]["mobile"] = $mobile == "" ? null : $mobile;
            $room["user"]["callType"] = $callType;
            $room["user"]["phoneCode"] = '';
            $room["user"]["phone"] = $phone;
            $room["user"]["phone2"] = '';
            $room["user"]["phone3"] = '';
            $room["user"]['accessFloor'] = $accessFloor;
            array_push($items, [
                "row" => $value['row'],
                "type" => "room",
                'build' => $building,
                "room" => $room
            ]);
        }

        // 房间数量上限
        $this->utils->self->checkMaxRoom($communityId, $roomNum);

        // 超过50条上限
        if ($roomNum + $deviceNum > 50) {
            $this->output->echoErrorMsg(STATE_IMPORT_LESS_DATA, ['externalErrorObj' => Code::EXT_STATE_IMPORT_NUM_LIMIT], [50]);
        }

        //openapi 添加到DeviceForRegister
        $this->loadProvider('rps');
        $this->loadUtil('device', true);
        $this->loadUtil('macLibrary', true);
        $this->loadModel('macLibrary', true);
        $this->loadUtil('common', true);
        $disInfo = $this->utils->_common->account->getManagerInfo($basic['ParentID']);
        $disUUID = $disInfo['UUID'];
        foreach($deviceForRps as $macTmp){
            $macTmp = strtoupper($macTmp);
            if(!$this->services->rps->testMap($macTmp)){
                $this->models->_common->macLibrary->addToDisMacLibrary($macTmp,$disInfo['ID']);
            }
        }
        
        // rps检测
        $this->utils->_common->projectData->rpsCheckMAC($deviceForRps, $basic['ParentID'], $communityId);




        $communityData = $this->utils->_common->account->getManagerInfoUUID($basic['UUID']);
        $this->log->debug('begin import community, import data={data}', ['data' => $items]);
        $userDataArr = [];
        $allBuildings = [];
        $allDevices = [];
        foreach ($items as $item) {
            $this->db->begin();
            try {
                $importResultData = $this->dealImportDetail($item, $communityData, true);
                $userData = $importResultData['user'];
                //openapi返回导入的用户账号密码等信息给接口
                if ($userData) {
                    $userDataTmp['ID'] = $userData['ID'];
                    $userDataTmp['account'] = $userData['Email'] ?:$userData['MobileNumber'];
                    $userDataTmp['passwd'] = $userData['Passwd'];
                    $userDataTmp['sip'] = $userData['Account'];
                    // kxl 6.7.2添加，通过sip获取sip群组
                    $userDataTmp['sipGroup'] = $this->dao->sipGroup2->selectByKey('Account', $userData['Account'])[0]['SipGroup'];
                    array_push($userDataArr, $userDataTmp);
                }
                
                // 累积building和device信息
                if (!empty($importResultData['building'])) {
                    $allBuildings = array_merge($allBuildings, $importResultData['building']);
                }
                if (!empty($importResultData['devices'])) {
                    $allDevices = array_merge($allDevices, $importResultData['devices']);
                }
                
                $this->db->commit();
                $this->notifyEvent->clear(); //openapi不发邮件
            } catch (Exception $e) {
                $this->notifyEvent->clear();
                $this->db->rollback();
                $msg = $e->getMessage();
                $this->log->debug("import community result error={error}", ["error" => $e]);
                echo $msg;
                exit(0);
            }
        }
        
        // 去重处理
        $tempBuilding = [];
        $buldingUnique = [];
        foreach ($allBuildings as $building) {
            if (!in_array($building['ID'], $buldingUnique)) {
                $buldingUnique[] = $building['ID'];
                $tempBuilding[] = $building;
            }
        }
        $allBuildings = $tempBuilding;

        if ($isV4) {
            return ['data' => ['user'=>$userDataArr, 'building'=>$allBuildings, 
            'device'=>$allDevices]];
        }

        return ["data" => $userDataArr];
    }

    /**
     * @description: installer-kit导入社区数据
     * @param:
     * @author: shoubin.chen 2024/8/22 11:55:17 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/22 11:55:17  V6.8.1
     */
    public function importCommunityData()
    {
        $params = ['taskId', 'importType'];
        list($taskId, $importType) = $this->getParams($params);
        $this->log->debug('installer kit: begin import community data.TaskId={taskId}', ['taskId' => $taskId]);
        // 读取任务，并设置为进行中
        $this->loadUtil('projectData', true);
        $res = $this->utils->_common->projectData->setImporting($taskId, $importType);
        $this->log->debug('set importing result = {res}', ['res' => $res]);
        if (!$res) {
            throw new \Exception('Import error, there is not task uuid=' . $taskId);
        }
        $this->log->debug('The task is set to processing.TaskId={taskId}', ['taskId' => $taskId]);

        // 开始导入
        $res = $this->utils->_common->projectData->getDetail($taskId);
        $this->log->debug('installer kit: begin import data.Import data={data}', ['data' => $res]);

        //实际处理的任务
        $failTaskDetail = $this->processImportCommunityData($res, true);

        // 低于一定数目失败进行重试，否则直接失败
        if (count($failTaskDetail) > 0 && count($failTaskDetail) <= IMPORT_NEED_REDO_NUMBER) {
            $this->log->debug('installer kit: begin redo fail import failTaskDetail={failTaskDetail}', ['failTaskDetail' => $failTaskDetail]);
            $this->processImportCommunityData($failTaskDetail);
        }

        //更新任务状态为完成
        $this->utils->_common->projectData->setImportSuccess($taskId);
        $this->log->debug('installer kit: import end');
    }

    private function processImportCommunityData($tasks, $retry = false, $failTaskDetail = [])
    {
        foreach ($tasks as $value) {
            $this->db->begin();
            try {
                $data = json_decode($value['Item'], true);
                $this->callSelfFunc('dealImportCommunityData', [$data]);
                $this->utils->_common->projectData->setImportDetailSuccess($value['UUID']);
                $this->db->commit();
                $this->notifyEvent->notify();
            } catch (Exception $e) {
                $this->notifyEvent->clear();
                $this->db->rollback();
                $errorStack = $this->share->util->getExceptionMsgWithAllTraces($e, false);
                $this->log->debug('installer kit: import result error={error}', ['error' => $errorStack]);
                // 记录错误任务，增加重试机制
                $this->utils->_common->projectData->setImportDetailFail($value['UUID'], $e->getCode());
                if (true === $retry) {
                    $failTaskDetail[] = $value;
                }
            }
        }
        return $failTaskDetail;
    }

    public function dealImportCommunityData()
    {
        $params = ['data'];
        list($data) = $this->getParams($params);
        $type = $data['type'];
        switch ($type) {
            case 'unit':
                //添加社区楼栋
                $this->callSelfFunc('dealAddUnits', [$data]);
                break;
            case 'pubDevice':
                //添加社区公共设备
                $this->callSelfFunc('dealAddPubDevices', [$data]);
                break;
            case 'unitDevice':
                //添加楼栋公共设备
                $this->callSelfFunc('dealAddUnitDevices', [$data]);
                break;
            case 'room':
                //添加楼栋下房间
                $this->callSelfFunc('dealAddRoom', [$data]);
                break;
        }
    }

    /**
     * @description: 处理添加楼栋
     * @author: shoubin.chen 2024/8/23 14:54:24 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/23 14:54:24  V6.8.1
     */
    public function dealAddUnits()
    {
        $params = ['data'];
        list($data) = $this->getParams($params);
        $this->loadModel('building', 'community');
        $this->loadUtil('account', true);
        $this->loadUtil('projectData', true);
        $community = $data['community'];
        $name = $data['unit']['name'];
        $this->loadUtil('building');

        //校验社区是否存在
        $project = $this->utils->_common->projectData->checkCommunityExist('UUID', $data['community']);

        // 该社区存在该楼栋则跳过
        $hasBuild = $this->utils->building->checkUnitInProject($name, $community);
        if ($hasBuild) {
            return;
        }

        $this->models->_community->building->add($name, $project['ID']);
    }

    /**
     * @description: 处理添加公共设备
     * @author: shoubin.chen 2024/8/23 14:54:24 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/23 14:54:24  V6.8.1
     */
    public function dealAddPubDevices()
    {
        $params = ['data'];
        list($data) = $this->getParams($params);
        $community = $data['community'];
        $mac = $data['pubDevice']['mac'];
        $name = $data['pubDevice']['name'];
        $type = $data['pubDevice']['type'];
        $iPAddress = $data['pubDevice']['ipAddress'];
        $deviceType = DEVICE_TYPE[$type];

        //校验社区是否存在
        $this->loadUtil('projectData', true);
        $project = $this->utils->_common->projectData->checkCommunityExist('UUID', $community);

        //校验该社区下是否存在该公共设备,若存在，则跳过
        $this->loadUtil('device');
        $hasDevice = $this->utils->device->checkPubDeviceInProject($mac, $community);
        if ($hasDevice) {
            return;
        }

        //mac校验,名称校验，设备类型校验
        $this->loadUtil('device', true);
        $this->utils->_common->device->macCheck($mac);
        $this->utils->_common->device->checkValidLocation($name);
        $this->utils->_common->device->checkValidType($deviceType);

        $comNetWork = $comArmingFunction = $isRepost = 0;
        $comStairShow = $isAllBuild = 1;
        if ($deviceType == DEVICE_TYPE['multipleDoor'] || $deviceType == DEVICE_TYPE['singleDoor']) {
            //门口机开启转流开关
            $isRepost = 1;
        }

        $this->loadUtil('device', true);

        $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($deviceType);
        $this->loadModel('device', false, [
            'dataContainer' => [
                'MAC' => $mac,
                'Location' => $name,
                'Type' => $deviceType,
                'NetGroupNumber' => $comNetWork,
                'Relay' => $comRelay,
                'StairShow' => $comStairShow,
                'ArmingFunction' => $comArmingFunction,
                'IsAllBuild' => $isAllBuild,
                'Builds' => [],
                PROXY_ROLE['projectId'] => $project['ID'],
                'IPAddress' => $iPAddress,
                'IsRepost' => $isRepost
            ]
        ]);
        $this->models->device->addPublic();
    }

    /**
     * @description: 处理添加楼栋公共设备
     * @author: shoubin.chen 2024/8/23 14:54:24 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/23 14:54:24  V6.8.1
     */
    public function dealAddUnitDevices()
    {
        $params = ['data'];
        list($data) = $this->getParams($params);
        $community = $data['community'];
        $unit = $data['unit'];
        $mac = $data['unitDevice']['mac'];
        $name = $data['unitDevice']['name'];
        $type = $data['unitDevice']['type'];
        $iPAddress = $data['unitDevice']['ipAddress'];
        $deviceType = DEVICE_TYPE[$type];

        // 校验社区是否存在,楼栋属于社区校验
        $this->loadUtil('projectData', true);
        $project = $this->utils->_common->projectData->checkCommunityExist('UUID', $community);
        $this->loadUtil('communityUnit', true);
        $buildInfo = $this->utils->_common->communityUnit->getUnitInfoByArr([['UnitName', $unit], ['AccountUUID', $community]])[0];
        if (empty($buildInfo)) {
            $this->output->echoErrorMsg(STATE_RESIDENT_IN_ROOM, ['externalErrorObj' => Code::EXT_STATE_UNIT_NOT_BELONG_COMMUNITY]);
        }

        //校验该社区下是否存在该楼栋公共设备,若存在，则跳过
        $this->loadUtil('device');
        $hasDevice = $this->utils->device->checkUnitDeviceInUnit($mac, $community, $buildInfo['UUID']);
        if ($hasDevice) {
            return;
        }

        //mac校验,名称校验，设备类型校验
        $this->loadUtil('device', true);
        $this->utils->_common->device->macCheck($mac);
        $this->utils->_common->device->checkValidLocation($name);
        $this->utils->_common->device->checkValidType($deviceType);

        $comNetWork = $comArmingFunction = $isAllBuild = $isRepost = 0;
        $comStairShow = 1;

        if ($deviceType == DEVICE_TYPE['multipleDoor'] || $deviceType == DEVICE_TYPE['singleDoor']) {
            //门口机开启转流开关
            $isRepost = 1;
        }

        $this->loadUtil('device', true);
        $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($deviceType);
        $this->loadModel('device', false, [
            'dataContainer' => [
                'MAC' => $mac,
                'Location' => $name,
                'Type' => $deviceType,
                'NetGroupNumber' => $comNetWork,
                'Relay' => $comRelay,
                'StairShow' => $comStairShow,
                'ArmingFunction' => $comArmingFunction,
                'Build' => $buildInfo['ID'],
                'IsAllBuild' => $isAllBuild,
                'Builds' => [],
                PROXY_ROLE['projectId'] => $project['ID'],
                'IPAddress' => $iPAddress,
                'IsRepost' => $isRepost
            ]
        ]);
        $this->models->device->addPublic();
    }

    /**
     * @description: 处理添加房间
     * @author: shoubin.chen 2024/8/23 14:54:24 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/23 14:54:24  V6.8.1
     */
    public function dealAddRoom()
    {
        $params = ['data'];
        list($data) = $this->getParams($params);
        $community = $data['community'];
        $unit = $data['unit'];
        $floor = empty($data['room']['floor']) ? "" : $data['room']['floor'];
        $roomNumber = $data['room']['roomNumber'];

        //校验社区是否存在, 楼栋属于社区校验, 房间号格式校验，房间号重复校验
        $this->loadUtil('projectData', true);
        $project = $this->utils->_common->projectData->checkCommunityExist('UUID', $community);
        $projectId = $project['ID'];

        $this->loadUtil('communityUnit', true);
        $buildInfo = $this->utils->_common->communityUnit->getUnitInfoByArr([['UnitName', $unit], ['AccountUUID', $community]])[0];
        $this->utils->self->checkRoomNumber($roomNumber, $projectId);

        //楼栋下存在该房间，则跳过创建
        $hasRoom = $this->utils->self->checkRoomExist($projectId, $unit, $roomNumber);
        if ($hasRoom) {
            $id = $this->utils->self->getRoomId($buildInfo['ID'], $roomNumber);
        } else {
            $roomName = "";
            $callType = $webRelayID = 0;
            $defaultEnableIpDirect = 1;
            $this->loadUtil('user');
            $id = $this->utils->user->addRoomNode($buildInfo['ID'], $projectId, $roomNumber, $roomName, $callType, $defaultEnableIpDirect, $webRelayID, $floor)['ID'];
        }

        //校验室内机方案时，导入的数据是否有室内机
        $roomDevices = $data['room']['roomDevices'];
        $this->loadUtil('account', true);
        $info = $this->utils->self->getInfo($project['ID']);
        $isIndoorPlan = $info['featurePlan']['indoor'];
        $isAbleSmartHome = $this->utils->self->isSmartHomeAble($projectId);

        $this->log->debug('info={info},isAbleSmartHome={isAbleSmartHome}', ['info' => $info, 'isAbleSmartHome' => $isAbleSmartHome]);

        $this->loadUtil('account', true);
        $userInfo = $this->utils->_common->account->getUserInfo($id);
        $this->loadUtil('common', true);
        $specialIndoorDevice = $this->dao->devicesSpecial->selectByArray([['Account', $userInfo['Account']]])[0];
        $indoorMac = "";
        if ($isIndoorPlan && count($specialIndoorDevice) === 0) {
            if (!$isAbleSmartHome) {
                //没开家居的室内机方案必带室内机
                $indoorMac = $this->utils->self->checkImportDataHasIndoor($roomDevices);
            } else {
                $this->utils->user->addDeviceSpecial($userInfo['Account'], '');
            }
        }

        $checkData = [
            'IndoorMac' => $indoorMac, 'IsAbleSmartHome' => $isAbleSmartHome, 'HasSpecialIndoorDevice' => count($specialIndoorDevice)
        ];
        $this->callSelfFunc('dealAddRoomDevice', [$roomDevices, $projectId, $id, $checkData, $userInfo['Account']]);
    }

    /**
     * @description: 处理添加房间下设备
     * @author: shoubin.chen 2024/8/23 14:54:24 V6.8.1
     * @lastEditor: shoubin.chen 2024/8/23 14:54:24  V6.8.1
     */
    public function dealAddRoomDevice()
    {
        $params = ['roomDevices:is-array', PROXY_ROLE['projectId'], 'ID', 'CheckData', 'Node'];
        list($roomDevices, $projectId, $id, $checkData, $node) = $this->getParams($params);

        $indoorMac = $checkData['IndoorMac'];
        $isAbleSmartHome = $checkData['IsAbleSmartHome'];
        $haveSpecialIndoorDevice = $checkData['HasSpecialIndoorDevice'];

        $comNetWork = $comArmingFunction = $brand = 0;
        $comStairShow = 1;

        $this->loadUtil('device', true);

        $hasFirstIndoor = false;
        foreach ($roomDevices as $device) {
            $isRepost = 0;
            $mac = $device['mac'];
            $type = $device['type'];
            $location = $device['name'];
            $iPAddress = $device['ipAddress'];
            $deviceType = DEVICE_TYPE[$type];

            $this->log->debug('IndoorMac={IndoorMac},mac={mac}', ['IndoorMac' => $indoorMac, 'mac' => $mac]);

            if ($deviceType == DEVICE_TYPE['multipleDoor'] || $deviceType == DEVICE_TYPE['singleDoor']) {
                //门口机开启转流开关
                $isRepost = 1;
            }
            if ($deviceType == DEVICE_TYPE['indoor'] && !$hasFirstIndoor){
                // 第一台室内机开启转流
                $hasFirstIndoor = true;
                $isRepost = 1;
            }

            // 特殊室内机方案也开启转流开关
            if ($mac == $indoorMac){
                $isRepost = 1;
            }


            //校验房间下是否存在该设备
            $hasDevice = $this->dao->devices->selectByArray([['MAC', $mac], ['Grade', DEVICE_GRADE_FAMILY], ['Node', $node]]);
            if (count($hasDevice) > 0) {
                continue;
            }

            //设备校验
            $this->utils->_common->device->macCheck($mac);
            $this->utils->_common->device->checkValidLocation($location);
            $this->utils->_common->device->checkValidType($deviceType);

            if ($mac === $indoorMac && !$isAbleSmartHome && $haveSpecialIndoorDevice === 0) {
                $comRelay = $this->utils->_common->device->getInitRelayForDeviceType(DEVICE_TYPE['indoor']);
                $this->utils->user->setAssocParams()->addDefIndoor([
                    'ID' => $id,
                    'MAC' => $mac,
                    'Relay' => $comRelay,
                    'Location' => $location,
                    PROXY_ROLE['projectId'] => $projectId,
                    'IPAddress' => $iPAddress,
                    'IsRepost' => $isRepost
                ]);
            } else {
                $comRelay = $this->utils->_common->device->getInitRelayForDeviceType($deviceType);

                $this->loadModel('device', false, [
                    'dataContainer' => [
                        "ID" => $id,
                        'MAC' => $mac,
                        'Location' => $location,
                        'Type' => $deviceType,
                        'NetGroupNumber' => $comNetWork,
                        'Relay' => $comRelay,
                        'StairShow' => $comStairShow,
                        'ArmingFunction' => $comArmingFunction,
                        PROXY_ROLE['projectId'] => $projectId,
                        'SecurityRelay' => '',
                        'Brand' => $brand,
                        'IPAddress' => $iPAddress,
                        'IsRepost' => $isRepost
                    ]
                ]);
                $this->models->device->addUser();
            }
        }
    }
}
