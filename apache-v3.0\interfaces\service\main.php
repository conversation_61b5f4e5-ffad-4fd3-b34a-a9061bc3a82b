<?php
/*
 * @Description: 服务接口
 * @version: 
 * @Author: kxl
 * @Date: 2019-12-27 13:48:16
 * @LastEditors  : kxl
 */
namespace interfaces\service\main;
interface IWebAuth {
    // 验证身份有效性
    function auth ();
    // 延长身份有效期
    function extendValid ();
}

interface IActiveAuth {
    // 验证身份有效性
    function authActive ($account);
    function authExpire ($account);
}

interface IAppAuth {
    // 验证身份有效性
    function auth ();
}

interface IOtherAuth {
    // 验证身份有效性
    function auth ();
}

// 参数解析
interface IParam {
    function parse ($name,$methods);
}

// limit
