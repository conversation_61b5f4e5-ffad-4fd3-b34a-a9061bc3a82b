<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2019-12-19 20:48:00
 * @LastEditor: csc 2022/3/10 14:03
 * @EditReason: V6.4 注销用户常量及验证码redis_db配置
 */
require_once(dirname(__FILE__).'/dynamic_config.php');

const SERVERYUNWEI = 'http://localhost';
const TOKENVALIDITYTIME = 3600;
const TOKENLENGTH = 118; //token长度 +10
const VERSION = '6.7.1';
const VERSIONHEADER = 'HTTP_X_CLOUD_VERSION';
const TOKEN = 'HTTP_X_AUTH_TOKEN';
const REDISSOCKET = 8504; //redis 端口
const REDISPW = 'Akcs#xm2610*'; //redis 密码
const REDISDB2FILE = 0; //存储file的redis数据库
const REDISDB2TOKEN = 1; //存储token的redis数据库
const REDISDB2LIMITIP = 2; //存储限制登录ip的redis数据库
const REDISDB2CODE = 15;//存储验证码的redis数据库

const EMAIILTMPTOKENVIHOUR = 3; //发送重置密码邮件token有效时间 hour
const RESENDEMAILTIME = 1; //最短验证码发送间隔 min
const REGISTEREMAILKCODETIME = 0.5; //注册时邮箱验证码有效时间
const DEVICEDEFAULTLOCATION = ['Stair Phone', 'Door Unit', 'Indoor Monitor', 'Management Center', 'Wall Phone']; //设备默认location
const DEFAULTEXPIRETIME = '2299-12-31 23:59:59';
const PERENDMROLE = 10;
const PERENDSROLE = 11;
const COMENDMROLE = 20;
const COMENDSROLE = 21;
const OFFPERSONNELROLE = 30;//办公普通员工
const OFFSTAFFROLE = 31;//办公管理员
const PMENDMROLE = 40;//PM appuser

const MAINROLE = [PERENDMROLE, COMENDMROLE, OFFSTAFFROLE, OFFPERSONNELROLE,PMENDMROLE];
const SUBROLE = [PERENDSROLE, COMENDSROLE];
const PERROLE = [PERENDMROLE, PERENDSROLE];
const COMROLE = [COMENDMROLE, COMENDSROLE];
const OFFROLE = [OFFSTAFFROLE, OFFPERSONNELROLE];

const SUPERGRADE = 1;
const AREAGRADE = 11;
const SUBDISTRIBUTOR = 12;
const COMMUNITYGRADE = 21;
const PERSONGRADE = 22;
const OFFICEGRADE = 23; //办公
const PROPERTYMANAGE = 31;
const PROJECT_ROLE = [COMMUNITYGRADE, PERSONGRADE, OFFICEGRADE];

const RPERENDMROLE = 'E'.PERENDMROLE;
const RPERENDSROLE = 'E'.PERENDSROLE;
const RCOMENDMROLE = 'E'.COMENDMROLE;
const RCOMENDSROLE = 'E'.COMENDSROLE;
const ROFFSTAFFROLE = "E".OFFSTAFFROLE;
const ROFFPERSONNELROLE = "E".OFFPERSONNELROLE;
const RPMENDMROLE = "E".PMENDMROLE;
const RSUPERGRADE = 'M'.SUPERGRADE;
const RAREAGRADE = 'M'.AREAGRADE;
const RSUBDISTRIBUTOR = 'M'.SUBDISTRIBUTOR;
const RCOMMUNITYGRADE = 'M'.COMMUNITYGRADE;
const RPERSONGRADE = 'M'.PERSONGRADE;
const ROFFICEGRADE = "M".OFFICEGRADE;
const RPROPERTYMANAGE = 'M'.PROPERTYMANAGE;

// 所有终端用户角色
const R_END_USER_ROLE = [
    RPERENDMROLE, RPERENDSROLE, RCOMENDMROLE, RCOMENDSROLE,
    ROFFSTAFFROLE, ROFFPERSONNELROLE, RPMENDMROLE
];

const LANGUAGE = [
    'en',
    'zh-tw',
    'zh-cn',
    'korean',
    'turkish',
    'ja',
    'ru',
    'pl',
    'bs',
    'da',
    'vi',
    'fr',
    'es',
    "pt",
    "de",
    "it"
];

const DATEFLAG = [1, 2, 4, 8, 16, 32, 64];
const RELATFLAG = [1, 2, 4, 8, 16, 32, 64];

const COMMONURL = '/apache-v3.0/';
const DATABASEUSER = 'dbuser01';
const DATABASEPW = 'Ak@56@<EMAIL>';
const DATABASENAME = 'AKCS';

const MESSAGE_CONTENT_LENGTH = 1024;
const MESSAGE_TITLE_LENGTH = 64;
const IMAGELINKKEY = 'ak_fdfs';
// 是否全查分表
const GETALLSLICETABLE = false;
const UPLOADFACEPATH = '/var/www/upload/face/';
// 获取tool box
const CANGETTOOLBOX = false;

const CODEVAILDTIME = 300;

const EXPORTCOUNT = 19999;
const EXPORTDAY = '30 day';

//v6.1 PIN加密 1为加密 0为不加密
const ENCRYPTION = 1;
const NOTENCRYPTED = 0;

//高级功能数目
const FEATURE_PLAN_NUMBERS = 5;

// 家庭从账户限制，最多63
const SUB_FAMILY_MEMBER_AMOUNT = 63;
const AKUVOX_REQUEST_CLIENT_ID = 'akcs-csmain-client';
const AKUVOX_REQUEST_SECRET = '8DkVZSK0N81fsZkle1lhc1eHibsCWJpUGRGAec9F8rSYIsW0yc8tSO080odJiHst';
const COMMUNITY_SMART_HOME_SWITCH_POSITION = 5;

// 亚洲云迁移日本服务器新增
const J_CLOUD = 'https://jcloud.akuvox.com';
const J_CLOUD_CAPTURE_TIME = '2022-07-13 23:15:00';
const MOVE_DIS = ["wavedge","Link","Jnets","Glamo","IIJ","VALTEC","ODI","Moncable","DigitalPower","Daminn","JTS","DOORCOM","cool_jp","testfee_jp"];
const IMG_IP_V4_S_CLOUD = 'https://scloud.akuvox.com:8091';//scloud的图片地址
const IMG_IP_V6_S_CLOUD = 'https://scloud.akuvox.com:8091';     //scloud的图片地址

// 亚洲云迁移到澳洲云
const AU_CLOUD = 'https://aucloud.akuvox.com';
const AU_CLOUD_CAPTURE_TIME = '2023-12-14 23:15:00';
const AU_MOVE_DIS = ['Alex', 'Inn', 'Tplus', 'Freeway', 'Transtech', 'HomeInc', 'Middys', 'Stentofon', 'innihome', 'Lilin', 'IOTWholesale', 'Fixtel', 'LTS-AU', 'SIETEC', 'Becas', 'Advance-d','s2au_dis', 'bela_dis'];