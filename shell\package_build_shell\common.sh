#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2024-02-20
# Description   :   通用包 构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
PROJECT_PATH=$1             #git clone时项目路径
SRC_PATH=$2                 #编译后代码存储路径
PACKAGE_NAME=$3             #包名
WEBROOT_PACKAGE_NAME=$4     #webroot下实际生成包的目录名
CODE_PACKAGE_NAME=$5        #代码下实际包的目录名

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

# 检查是否传递了 $4 参数，如果没有，则将 $WEBROOT_PACKAGE_NAME 设为 $PACKAGE_NAME 的值
if [ -z "$WEBROOT_PACKAGE_NAME" ]; then
    WEBROOT_PACKAGE_NAME=$PACKAGE_NAME
fi
# 检查是否传递了 $5 参数，如果没有，则将 $CODE_PACKAGE_NAME 设为 $PACKAGE_NAME 的值
if [ -z "$CODE_PACKAGE_NAME" ]; then
    CODE_PACKAGE_NAME=$PACKAGE_NAME
fi

AKCS_SRC_ROOT=$PROJECT_PATH
source "$AKCS_SRC_ROOT"/shell/package_build_shell/source.sh

###################### 整合代码，准备代码同步 ######################
# 复制Dockerfile，docker-compose(后续会改成compose启动)
cp -rf $AKCS_SRC_WEB/docker/web_$PACKAGE_NAME $AKCS_PACKAGE_ROOT_WEBROOT/

bash "$AKCS_SRC_WEB/web-server/script/build.sh" $CODE_PACKAGE_NAME
mv "$AKCS_SRC_WEB/web-server/dist" "$AKCS_PACKAGE_ROOT_WEBROOT/$WEBROOT_PACKAGE_NAME"
rm -R $AKCS_PACKAGE_ROOT_WEBROOT/$WEBROOT_PACKAGE_NAME/test
#拷贝func, 后台一份代码，但是前端有多个地方引用
cp -rf $AKCS_SRC_WEB/apache-v3.0/notify $AKCS_PACKAGE_ROOT_WEBROOT/$WEBROOT_PACKAGE_NAME/share/
