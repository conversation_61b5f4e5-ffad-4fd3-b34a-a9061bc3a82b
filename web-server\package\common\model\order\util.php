<?php
/**
 * @description 订单
 * <AUTHOR>
 * @date 2022-03-30 14:53:54
 * @version V6.4
 * @lastEditor cj
 * @lastEditTime 2023-05-27 11:51:12
 * @lastVersion V6.6.0
 */

namespace package\common\model\order;

use \framework\BasicUtil;

class Util extends BasicUtil
{
    public $allowedDao = ['orderList', 'orderEndUserList', 'account', 'subscriptionEndUserList', 'subscriptionList',
        'subscriptionUsers', 'rentManagerOrderList', 'lockOrder', 'thirdLockRelateInfo', 'orderThirdLockList', 'orderListOffice'];
    use method\Create;
    use method\Check;
    use method\Get;
}
