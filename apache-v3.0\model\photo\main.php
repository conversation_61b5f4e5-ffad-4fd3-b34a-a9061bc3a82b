<?php
/*
 * @Description: 人脸管理
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

class CPhoto
{
    public function import()
    {
        $params = [
            "userAlias"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#photo#import#params=".json_encode($params));
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $file = $_FILES["PhotoFile"];
        $fileTemName = $file["tmp_name"];
        $unique = $user."_".\util\string\randString(10);
        $fileName = $unique.".zip";
        $re = move_uploaded_file($fileTemName, UPLOADFACEPATH.$fileName);
        $projectType = $this->db->querySList("select Grade from Account where ID = $userId")[0]["Grade"] == '23' ? 'office' : 'community';
        if ($re) {
            \util\computed\setGAppData(["path"=>UPLOADFACEPATH.$fileName,"uniqueId"=>$unique,"communityId"=>$userId, "projectType"=>$projectType]);
        } else {
            $this->log->actionLog("#model#photo#import#upload file");
        }
    }

    public function addFace()
    {
        global $cMessage;
        $params = [
            "userAlias"=>"",
            "userAliasId"=>"",
            'Step'=>"",
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#photo#addFace#params=".json_encode($params));
        $community = $params["userAlias"];
        $communityId = $params["userAliasId"];
        $id = $params["ID"];
        $step = $params["Step"];

        if ($step == 1) {
            $data = $this->db->querySList('select Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
            if ($data['Role'] == 21) {
                $data = $this->db->querySList('select ParentID from PersonalAccount where ID = :ID', [":ID"=>$data["ParentID"]])[0];
            }

            if ($communityId != $data['ParentID']) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }

            // 6.3区分办公、社区
            $user = $this->db->querySList('select Account, Role from PersonalAccount where ID=:ID', [":ID"=>$id])[0];
            $account = $user["Account"];
            $projectType = ($user["Role"] == OFFSTAFFROLE || $user["Role"] == OFFPERSONNELROLE) ? 'office' : 'community';
            
            $file = $_FILES["Face"];
            //获取文件后缀
            $fileExt = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileTemName = $file["tmp_name"];
            $unique = $community."_".\util\string\randString(10);
            $fileName = $unique.'.'.$fileExt;
            $re = move_uploaded_file($fileTemName, UPLOADFACEPATH.$fileName);
            if ($re) {
                \util\computed\setGAppData(["path"=>UPLOADFACEPATH,"fileName"=>$fileName,"account"=>$account, "projectType"=>$projectType]);
            } else {
                $this->log->actionLog("#model#photo#import#upload fail");
            }
        }
    }


    public function query()
    {
        $params = [
            // UnitID
            "Build"=>"",
            // RoomID
            "Room"=>"",
            "Key"=>"",
            // CommunityID
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $build = $params["Build"];
        $room = $params["Room"];
        $userId = $params["userAliasId"];
        $key = $params["Key"];

        $where = " where F.MngAccountID = :MngAccountID and P.Name like :Key";
        $bindArray = [":MngAccountID"=>$userId,":Key"=>"%$key%"];
        if ($room != "all") {
            $where .= " and R.ID = :Room";
            $bindArray[":Room"] = $room;
        } elseif ($build != "all") {
            $where .= " and F.UnitID = :UnitID";
            $bindArray[":UnitID"] = $build;
        }
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        // TODO 住从账户
        $total = $this->db->querySList("select count(*) as total from FaceMng F join PersonalAccount P on F.PersonalAccountID = P.ID join PersonalAccount P2 on P2.Account = F.Node
        join CommunityRoom R on P2.RoomID = R.ID join CommunityUnit U on U.ID = F.UnitID $where", $bindArray)[0]["total"];

        $data = $this->db->querySList("select P.Name,R.RoomName,U.UnitName,F.ID from FaceMng F join PersonalAccount P on F.PersonalAccountID = P.ID join PersonalAccount P2 on P2.Account = F.Node
        join CommunityRoom R on P2.RoomID = R.ID join CommunityUnit U on U.ID = F.UnitID $where order by F.ID desc limit $offset,$rows", $bindArray);
        \util\computed\setGAppData(["data"=>["row"=>$data,"detail"=>$data,"total"=>intval($total)]]);
    }

    public function remove($deleteAll = false)
    {
        global $cMessage;
        $params = $deleteAll?[
            "userAliasId"=>""
        ]:[
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        if (!$deleteAll) {
            $id = $params["ID"];
            $ids = explode(";", $id);
        } else {
            // 一键删除查询所有ID号
            $ids = [];
            $resData = $this->db->querySList("select P.Name,R.RoomName,U.UnitName,F.ID from FaceMng F join PersonalAccount P on F.PersonalAccountID = P.ID join PersonalAccount P2 on P2.Account = F.Node
        join CommunityRoom R on P2.RoomID = R.ID join CommunityUnit U on U.ID = F.UnitID where F.MngAccountID = :MngAccountID order by F.ID desc", [":MngAccountID"=>$userId]);
            foreach ($resData as $val) {
                array_push($ids, $val['ID']);
            }
        }
        
        $this->log->actionLog("#model#photo#remove#params=".json_encode($params));
        
        $faceData = [];
        foreach ($ids as $id) {
            $data = $this->db->querySList("select count(*) as total,PersonalAccountID,FaceUrl from FaceMng where ID = :ID and MngAccountID=:MngAccountID", [":ID"=>$id,":MngAccountID"=>$userId])[0];
            $count = $data["total"];
            array_push($faceData, $data);
            if ($count == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->db->delete2ListWID("FaceMng", $id);

            $account = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [":ID"=>$data['PersonalAccountID']])[0]['Account'];
            $this->auditLog->setLog(AuditCodeDeleteFace, $this->env, [$account], $account);
        }
        \util\computed\setGAppData(["FaceData"=>$faceData, "DeleteAllFaceData"=>$deleteAll, "CommunityId"=>$userId]);
    }

    public function removeAll()
    {
        $this->remove(true);
    }

    public function importPresent()
    {
        global $cMessage;
        $params = [
            "UniqueId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $uniqueId = $params["UniqueId"];
        include_once __DIR__."/../../database/redis.php";
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2FILE);
        $data = $redis->get($uniqueId);
        if ($data == null) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }

        $data = json_decode($data, true);
        $result = $data["result"];
        if ($result != 0) {
            $this->log->actionLog("#model#importPresent#import fail msg = ".$data["message"]);
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }
        $present = $data["realPercent"];
        if ($present == 1) {
            $redis->delete($uniqueId);
        }
        $fileList = $data["fileList"];
        \util\computed\setGAppData(["data"=>["fileList"=>$fileList,"percent"=>$present]]);
    }

    /**
     * @description: 添加Staff Face
     * @author: cj 2022-12-5 16:20:01 V6.5.2
     * @LastEditor: cj 2022-12-5 16:20:01 V6.5.2
     * @param userAlias 社区Account
     * @param userAliasId 社区ID
     * @param ID Staff ID
     * @return void
     */
    public function addStaffFace()
    {
        global $cMessage;
        $params = [
            'userAlias' => '',
            'userAliasId' => '',
            'ID' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog('#model#photo#addStaffFace#params='.json_encode($params));
        $community = $params['userAlias'];
        $communityId = $params['userAliasId'];
        // staff.staffAdd设置 ID为单元素数组，做兼容
        $id = is_array($params['ID']) ? $params['ID'][0] : $params['ID'];
        // 校验Staff是否在对应社区下
        $staffData = $this->db->querySList('select CommunityID,UUID from Staff where ID = :ID', [':ID' => $id])[0];
        if ($staffData['CommunityID'] !== $communityId) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        
        $file = $_FILES['Face'];
        //获取文件后缀
        $fileExt = pathinfo($file['name'], PATHINFO_EXTENSION);
        $fileTemName = $file['tmp_name'];
        $unique = $community.'_'.\util\string\randString(10);
        $fileName = $unique.'.'.$fileExt;
        $re = move_uploaded_file($fileTemName, UPLOADFACEPATH.$fileName);
        if ($re) {
            \util\computed\setGAppData(['path'=>UPLOADFACEPATH, 'fileName'=>$fileName, 'staffUUID'=>$staffData['UUID']]);
        } else {
            $this->log->actionLog("#model#photo#addStaffFace#upload fail");
        }
    }

    /**
     * @description: 删除Staff Face,在删除Staff前把相关人脸数据保存
     * @author: cj 2022-12-6 11:11:01 V6.5.2
     * @LastEditor: cj 2022-12-6 11:11:01 V6.5.2
     * @param userAlias 社区Account
     * @param userAliasId 社区ID
     * @param ID Staff ID,多个用分号拼接
     * @return void
     */
    public function deleteStaffFace()
    {
        global $cMessage;
        $params = [
            'userAliasId' => '',
            'ID' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog('#model#photo#deleteStaffFace#params='.json_encode($params));
        $communityId = $params['userAliasId'];
        // 兼容删除和编辑时不同的ID格式
        $ids = is_array($params['ID']) ? $params['ID'] : explode(';', $params['ID']);
        $faceUrlArr = [];
        foreach ($ids as $id) {
            // 校验Staff是否在对应社区下
            $staffData = $this->db->querySList('select CommunityID,FaceUrl from Staff where ID = :ID', [':ID' => $id])[0];
            if ($staffData['CommunityID'] !== $communityId) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->db->update2ListWID('Staff', [':ID' => $id, ':FileName' => '', ':FaceUrl' => '', ':FaceMD5' => '']);
            if (!empty($staffData['FaceUrl'])) {
                array_push($faceUrlArr, $staffData['FaceUrl']);
            }
        }
        \util\computed\setGAppData(['FaceUrlArr' => $faceUrlArr]);
    }

    /**
     * @description: 编辑Staff Face,人脸删除需要重新添加，未删除不做处理
     * @author: cj 2022-12-7 11:18:21 V6.5.2
     * @LastEditor: cj 2022-12-7 11:18:21 V6.5.2
     * @param {bool} isDeleteFace 是否删除原有人脸
     * @return void
     */
    public function editStaffFace()
    {
        $params = [
            'IsDeleteFace' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog('#model#photo#editStaffFace#params='.json_encode($params));
        $isDeleteFace = $params['IsDeleteFace'] ? true:false;
        if ($isDeleteFace) {
            $this->deleteStaffFace();
        }
        $this->addStaffFace();
    }
}
