<?php
namespace plan\process;
const CAPTURE_PROCESS = [
    "getCaptrueForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryCaptureForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getCaptrueForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryCaptureForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMotionForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryMotionForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMotionForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryMotionForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getDoorLogForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryDoorLogForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getDoorLogForApp2"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryDoorLogForApp2"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data", 'LockIndex'=>'LockIndex', 'DeviceIndex' => 'DeviceIndex']
        ]
    ],

    "delCaptureForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteCaptrueForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForUser"
        ]
    ],

    "delCaptureForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteCaptrueForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForUser"
        ]
    ],

    "delMotionForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteMotionForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForUser"
        ]
    ],

    "delMotionForPmApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteForCommunity"
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForCom"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],

    "delMotionForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.deleteMotionForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ],[
            "type"=>"model",
            "model"=>"notify.captureDeleteForUser"
        ]
    ],
    "getPMTemperature"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryTemperatureForPM",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "queryComPhotos"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"photo.query"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "ImportFacePhoto"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"photo.import"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd,
            "options"=>["UniqueId"=>"uniqueId"]
        ],[
            "type"=>"model",
            "model"=>"notify.comImportFace"
        ]
    ],
    "deleteFacePhoto"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"photo.remove"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],[
            "type"=>"model",
            "model"=>"notify.comDeleteFace"
        ]
    ],
    "deleteAllFacePhoto"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.checkPw"
        ],[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"photo.removeAll"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],[
            "type"=>"model",
            "model"=>"notify.comDeleteFace"
        ]
    ],
    "queryImportFacePresent"=>[
        [
            "type"=>"model",
            "model"=>"photo.importPresent"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "exportPmLog"=>[
        [
            "type" => "model",
            "model" => "capture.insertPMExportLog"
        ],
        [
            "type" => "model",
            "model" => "notify.exportPmLog"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getExportTime"=>[
        [
            "type" => "model",
            "model" => "capture.getExportTime"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "exportPMLogExcel"=>[
        [
            "type" => "model",
            "model" => "capture.exportPMLogExcel"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["DownloadUrl" => "DownloadUrl"]
        ]
    ],
    //pm app获取door log
    "getDoorLogForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.queryDoorLogForPM"
        ],[
            "type"=>"model",
            "model"=>"capture.formatPmAppDoorLog"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取Capture Logs列表
    "getCaptureForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.setQueryCaptureParams"
        ],[
            "type"=>"model",
            "model"=>"capture.queryCaptureForPM"
        ],[
            "type"=>"model",
            "model"=>"capture.formatPmAppCapture"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取motion列表
    "getMotionForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],[
            "type"=>"model",
            "model"=>"capture.setQueryMotionParams"
        ],[
            "type"=>"model",
            "model"=>"capture.queryCaptureForPM"
        ],[
            "type"=>"model",
            "model"=>"capture.formatPmAppMotion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];