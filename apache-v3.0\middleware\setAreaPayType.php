<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 10:05:28
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/computed.php";
class CSetAreaPayType implements IMiddleware {
    function handle (\Closure $next) {
        global $cLog;
        $cLog->actionLog("#middle#setAreaPayType#");
        \util\computed\setGAppData(["payType"=>3]);
        $next();
    }
}