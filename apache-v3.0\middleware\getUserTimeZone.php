<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 17:29:36
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";

class CGetUserTimeZone implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        // 时区要用自己的Id,从账户使用主账户时区，社区主账户使用小区时区
        $id = $gApp["userId"];
        $role = $gApp["role"];
        $db = \database\CDatabase::getInstance();
        if($role == RPERENDSROLE || $role == RCOMENDSROLE) {
            $id = $db->querySList("select ParentID from PersonalAccount where ID = :ID",[":ID"=>$id])[0]["ParentID"];
        }
        if($role == RPERENDSROLE || $role == RPERENDMROLE) {
            $data = $db->querySList("select TimeZone,CustomizeForm from PersonalAccount where ID = :ID",[":ID"=>$id])[0];
            $timeZone = $data["TimeZone"];
            $customizeForm = $data["CustomizeForm"];
        }else{
            $id =  $id = $db->querySList("select ParentID from PersonalAccount where ID = :ID",[":ID"=>$id])[0]["ParentID"];
            $data = $db->querySList("select TimeZone,CustomizeForm from Account where ID = :ID",[":ID"=>$id])[0];
            $timeZone = $data["TimeZone"];
            $customizeForm = $data["CustomizeForm"];
        }
        
        $cLog->actionLog("#middle#getUserTimeZone#id=$id;timeZone=$timeZone;customizeForm=$customizeForm");
        \util\computed\setGAppData(["SelfTimeZone"=>$timeZone,"SelfCustomizeForm"=>$customizeForm]);
        $next();
    }
}