<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 14:24:47
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CAddRoomUser {
    function on () {
        
    }
    function emit ($isAddUser) {
        global $cLog;
        $cLog->actionLog("#event#addRoomUser.emit#isAddUser=$isAddUser");
        if($isAddUser == 1) {
            \util\computed\setGAppBranch("addRoomUser");
        }
    }
}