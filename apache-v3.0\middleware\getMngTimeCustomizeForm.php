<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-21 14:20:16
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";

class CGetMngTimeCustomizeForm implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        // 时区要用自己的Id
        // $id = $gApp["userId"];
        // $db = \database\CDatabase::getInstance();
        // $customizeForm = $db->querySList("select CustomizeForm from Account where ID = :ID",[":ID"=>$id])[0]["CustomizeForm"];
        // $cLog->actionLog("#middle#getMngTimeCustomizeForm#id=$id;customizeForm=$customizeForm");
        // \util\computed\setGAppData(["SelfCustomizeForm"=>$customizeForm]);
        $next();
    }
}