<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 18:18:27
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CEditUserName {
    private $name;
    function on ($id) {
        global $cLog;
        $cLog->actionLog("#event#editUserName.on#id=$id");
        $db = \database\CDatabase::getInstance();
        $name = $db->querySList("select Name from PersonalAccount where ID=:ID",[":ID"=>$id])[0]["Name"];
        $this->name = $name;
    }

    function emit ($name) {
        global $cLog;
        $cLog->actionLog("#event#editUserName.emit#name=$name");
        if($name != $this->name) {
            include_once __DIR__."/../util/computed.php";
            \util\computed\setGAppBranch("changeName");
        }
    }
}