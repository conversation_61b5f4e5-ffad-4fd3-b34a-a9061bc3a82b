<?php

namespace package\community\model\subscription\method;


trait Deal
{
    /**
     * @description: 处理订阅的类型
     * @param:{string} Type 订阅的类型
     * @return array 1=app,2=视频存储,3=三方锁 4=rentmanager
     * @author: shoubin.chen 2024/11/6 15:12:45 V7.1.0
     * @lastEditors: csc 2025/2/17 16:49 V7.1.0
     */
    public function dealSubscriptionFunction()
    {
        $params = ['Type', 'MixType'];
        list($type, $mixType) = $this->getParams($params);
        $result = [];
        if ($type == SUBSCRIBE_TYPE['mixRenew']) {
            $mixTypeArr = $this->share->util->getBitPositions($mixType);
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['singleRenew'], SUBSCRIBE_TYPE['communityRenew'], SUBSCRIBE_TYPE['officeRenew']])) {
                $result[] = 1;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['communityVideoStorageRenew'], SUBSCRIBE_TYPE['singleVideoStorageRenew']])) {
                $result[] = 2;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['singleThirdLockRenew'], SUBSCRIBE_TYPE['communityThirdLockRenew']])) {
                $result[] = 3;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['rentManagerRenew']])) {
                $result[] = 4;
            }
        } else if ($type == SUBSCRIBE_TYPE['communityVideoStorageRenew'] || $type == SUBSCRIBE_TYPE['singleVideoStorageRenew']) {
            $result[] = 2;
        } else if ($type == SUBSCRIBE_TYPE['rentManagerRenew']) {
            $result[] = 4;
        } else if ($type == SUBSCRIBE_TYPE['singleThirdLockRenew'] || $type == SUBSCRIBE_TYPE['communityThirdLockRenew']) {
            $result[] = 3;
        } else {
            $result[] = 1;
        }
        return $result;
    }

    /**
     * @description: 获取订阅终端用户类型
     * @author: shoubin.chen 2024/12/1 16:29:22 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/1 16:29:22  V7.1.0
     */
    public function dealSubscriptionEndUserType()
    {
        $params = ['Type', 'Role'];
        list($type, $role) = $this->getParams($params);

        $type = intval($type);
        if (in_array($type, [SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew'], SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew']])) {
            return "3"; //视频存储
        }
        if ($role == PMENDMROLE) {
            return "1"; //pm app
        }
        if (in_array($type, [SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'], SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew']])) {
            return "4"; //三方锁
        }
        return "0"; // app
    }

}