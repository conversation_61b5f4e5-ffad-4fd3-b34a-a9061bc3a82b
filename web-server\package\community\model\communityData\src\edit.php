<?php
namespace package\community\model\communityData\src;

use Exception;
use package\community\model\communityData\config\Code;

trait Edit
{
    /**
     * @description super编辑社区
     * <AUTHOR> 2022/4/21 17:22 V6.4
     * @params NumberOfApt 房间数量
     * @params EnableLandline 呼叫手机号服务
     * @params ID
     * @return void
     * @lastEditor csc 2022/4/21 17:22 V6.4
     */
    public function editCommunityChargePlan()
    {
        $params = ['NumberOfApt', 'EnableLandline', 'ID', 'FeatureID', 'IsHaveAccessArea', 'IsLocalAKManage', 'VideoStorageExpireTime', 'IsPMFee'];
        list($numberOfApt, $enableLandline, $id, $featureID, $isHaveAccessArea, $isLocalAkManage, $videoStorageExpireTime, $isPMFee) = $this->getParams($params);

        $this->loadUtil('common', true);

        $this->log->debug("id=$id;numberOfApt=$numberOfApt;EnableLandline=$enableLandline");
        $communityData = $this->utils->_common->common->getAccountInfoWithID($id);
        $account = $communityData["Account"];
        $this->callSelfFunc('editLocalAkManage', [$communityData['UUID'], $isLocalAkManage]);

        $communityInfo = $this->dao->communityInfo->lock()->selectByAccountID($id)[0];
        //pub+private => pub需要刷新个人权限为全天候
        if($isHaveAccessArea === '0' && $this->share->util->getSpecifyBitLE($communityInfo['Switch'], 7) === 1){
            $this->loadUtil('access');
            $this->utils->access->selfAccessRefreshAllWeather($id);
        }
        // 更新视频存储过期时间
        $this->loadModel('videoStorage');
        $this->models->videoStorage->updateCommunityVideoStorageExpireTime($communityData['UUID'], $videoStorageExpireTime);

        //编辑社区的方案（旧社区无方案),新社区的featureID也有可能是空的
        if($communityInfo['IsNew'] === '1' && !empty($featureID)){
            $featureFlag = $this->callSelfFunc('editCommunityFeature', [$id, $featureID]);
        }
        $switch = $communityInfo["Switch"];
        $switch = $this->share->util->bitOperation($switch, $enableLandline, 1);
        $switch = $this->share->util->bitOperation($switch, $isHaveAccessArea, 7);

        $isPMFee = intval($isPMFee);

        $infoUpdate = [":AccountID" => $id, ":NumberOfApt" => $numberOfApt, ":Switch" => $switch, ":IsPMFee" => $isPMFee];

        $this->dao->communityInfo->update($infoUpdate, 'AccountID');
        // 对比前后是否发生更改
        if ($communityInfo['NumberOfApt'] != $numberOfApt || (intval($communityInfo['Switch']) & 1) != $enableLandline || $featureFlag || $communityInfo['IsPMFee'] != $isPMFee) {
            $this->loadProvider('billsysUtil');
            $this->services->billsysUtil->checkPlan($id);
            if ($communityInfo['NumberOfApt'] != $numberOfApt) {
                $this->auditLog->setLog(AUDIT_CODE_A_P_T_COUNT, $this->env, [$numberOfApt], $account);
            }
            if ((intval($communityInfo['Switch']) & 1) != $enableLandline) {
                $this->auditLog->setLog($enableLandline == 0 ? AUDIT_CODE_DISABLE_LANDLINE : AUDIT_CODE_ENABLE_LANDLINE, $this->env, [], $account);
            }
            if ($communityInfo['IsPMFee'] != $isPMFee) { //需要取消PM创建的订阅
                $this->loadUtil('subscription');
                $subscriptionList = $this->utils->subscription->getProjectSubscriptionForPM($communityInfo['AccountUUID']);
                foreach ($subscriptionList as $val) {
                    $this->services->billsysUtil->cancelSubscription($val['UUID'], SUBSCRIPTION_END_REASON_ARRAY[7]);
                }
            }
        }
    }

    /**
     * @description: 编辑韩国本地云项目
     * @param: IsLocalAKManage 是否是韩国本地云管理的项目
     * @author: shoubin.chen 2024/9/2 11:28:47 V6.8.1
     * @lastEditor: shoubin.chen 2024/9/2 11:28:47  V6.8.1
     */
    public function editLocalAkManage()
    {
        $params = [PROXY_ROLE['projectUUID'], 'IsLocalAKManage?:enum("0","1")'];
        list($projectUUID, $isLocalAkManage) = $this->getParams($params);
        $manage = $this->dao->localAKManage->selectByKey('AccountUUID', $projectUUID);
        if ($isLocalAkManage == null){
            return;
        }
        if ($isLocalAkManage == '1') {
            if (empty($manage)) {
                $this->dao->localAKManage->insert(['AccountUUID' => $projectUUID, 'UUID' => $this->share->util->uuid()]);
            }
        } else {
            $this->dao->localAKManage->delete($projectUUID, 'AccountUUID');
        }
    }

    /**
     * @description: 编辑社区方案
     * @author:lwj 2022/9/29 10:12 V6.5
     * @lastEditor: lwj 2022/9/29 10:12 V6.5
     * @param: id 社区ID
     * @return:
     * @return bool
     */
    public function editCommunityFeature()
    {
        $params = ['ID', 'FeatureID'];
        list($id, $featureId) = $this->getParams($params);
        //方案是否变标识
        $featureFlag = false;
        $manageFeatureTable = PROXY_TABLES['manageFeature'];
        $manageFeatureInfo = $this->db->querySList(
            "select FeatureID from $manageFeatureTable where AccountID =:AccountID",
            [':AccountID' => $id]
        )[0];
        //方案未更改
        if($manageFeatureInfo['FeatureID'] === $featureId){
            return $featureFlag;
        }
        $oldFeaturePlan = $this->dao->featurePlan->selectByKey('ID', $manageFeatureInfo['FeatureID'])[0];
        //方案已经修改
        //校验方案id是否合法
        $featureTable = PROXY_TABLES['featurePlan'];
        $featureInfo = $this->db->querySList(
            "select ID,Item,Name from $featureTable where ID =:featureID and Type = :Type",
            [':featureID' => $featureId, ':Type' => 0]
        )[0];
        if (empty($featureInfo)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_FEATURE_NOT_EXIST]);
        }
        //从Premium改为Basic
        if ($oldFeaturePlan['Name'] === 'Premium' && $featureInfo['Name'] === 'Basic') {
            $this->loadUtil('account', true);
            $this->loadUtil('amenity');
            $projectUUID = $this->utils->_common->account->accountSelectByKey('ID', $id, 'UUID')[0]['UUID'];
            $this->utils->amenity->setInvalidForFeatureChange($projectUUID);
        }

        //方案切换-室内机逻辑处理
        $this->loadUtil('device');
        $this->utils->device->operateDeviceSpecial($id, $featureInfo['Item']);
        $featureFlag = true;
        $this->loadUtil('common', true);
        $communityData = $this->utils->_common->common->getAccountInfoWithID($id);
        //更新社区方案
        $this->db->update2ListWKey($manageFeatureTable, [':AccountID' => $id, ':FeatureID' => $featureId], 'AccountID');
        //要更改的方案不含有第三方设备则需要清除数据
        if($this->share->util->getSpecifyBitLE($featureInfo['Item'], 7) !== 1){
            $this->db->delete2ListWKey(PROXY_TABLES['thirdPartCamera'], 'ProjectUUID', $communityData['UUID']);
        }
        $this->loadModel('billingInfo', true);
        $this->models->_common->billingInfo->updateActiveUser($id, [COMENDMROLE, PMENDMROLE], 1);
        $this->models->_common->billingInfo->updateActiveThirdLock($id, 1, 1);
        return $featureFlag;
    }

    /**
     * @description: ins编辑社区
     * @author: cj 2022/6/7 20:59 V6.5
     * @param ID指社区ID
     * @return {*}
     * @LastEditor: cj 2022/6/7 20:59 V6.5
     */
    public function editCommunity()
    {
        $params = [PROXY_ROLE['projectId'], 'ID', 'Location', 'Street', 'City', 'PostalCode',
        'States', 'Country', 'TimeZone', 'AptPinType', 'ChargeMode', 'CustomizeForm',
        'SendExpireEmailType', 'SendRenew', 'EnableSmartHome', 'IsCommunityCalls', 'NameDisplay?:enum("0", "1")',
        'EnableVideoStorage?:enum("0","1","")','IsHaveAccessArea?:enum("0","1")','EnableScanToRegister?:enum("0","1","")',
        'EnableDormakaba?:enum("0","1","")'];
        list($projectId, $id, $location, $street, $city, $postalCode, $states, $country, $timeZone,
            $keyType, $chargeMode, $customizeForm, $sendExpireEmailType, $sendRenew, $enableSmartHome,
            $isCommunityCalls, $nameDisplay, $enableVideoStorage, $idHaveAccessArea,$enableScanToRegister,$enableDormakaba) = $this->getParams($params);
        
        //需要过滤前后的空格
        $postalCode = trim($postalCode);
        $this->log->debug('projectId={projectId}', ['projectId'=>$projectId]);

        $sql = 'select UUID,TimeZone,ManageGroup,ChargeMode,Flags from '. PROXY_TABLES['account'] .' where ID = :ID for update';
        $oldData = $this->db->querySList($sql, [':ID' => $id])[0];


        $this->loadUtil('common', true);
        $installerData = $this->utils->_common->common->getAccountInfoWithID($projectId, ['ManageGroup', 'Account', 'ParentID', 'UUID']);
        $manageGroup = $installerData['ManageGroup'];
        $chargeMode = intval($chargeMode);
        if ($oldData['ManageGroup'] !== $manageGroup) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_INS]);
        }
        // 社区下的收费模式由普通支付改为非普通支付,需要取消管理该小区的所有PM订阅
        if ($chargeMode !== CHARGEMODE_NORMAL && intval($oldData['ChargeMode']) === CHARGEMODE_NORMAL) {
            // 获取管理该小区的全部PM付费
            $this->loadUtil('order', true);
            $subscriptionUUIDs = $this->utils->_common->order->getSubscriptionList([['ProjectUUID', $oldData['UUID']], ['PayerType', 1], ['IsDelete', 0],
            ['Status', [0, 1]]], 'UUID');
            $this->loadProvider('billsysUtil');
            foreach ($subscriptionUUIDs as $subscriptionUUID) {
                // 创建订阅的pm被移除项目的管理(类型9)，取消订阅
                $this->services->billsysUtil->cancelSubscription($subscriptionUUID['UUID'], SUBSCRIPTION_END_REASON_ARRAY[9]);
            }
        }

        $sql = 'select IsNew,Switch,EnableDormakaba from '. PROXY_TABLES['communityInfo'] .' where AccountID = :AccountID for update';
        $communityInfo = $this->db->querySList($sql, [':AccountID' => $id])[0];
        // 家居开关从关到开
        if ($this->share->util->getSpecifyBitLE($communityInfo['Switch'], 5) === 0 && $enableSmartHome === '1') {
            // 解绑整个社区下的一人多套房账号
            $this->loadUtil('user');
            $this->utils->user->deleteUserWMultiLinkForCom($id);
            $this->notifySmartHome->collect(['Project', 'notifyCreateProject'], [$oldData['UUID'], true], 6710);
        } else {
            $this->notifySmartHome->collect(['Project', 'notifyUpdateProject'], [$oldData['UUID']], 6710);
        }
        if ($idHaveAccessArea !== null) {
            $communityInfo['Switch'] = $this->share->util->bitOperation($communityInfo['Switch'], $idHaveAccessArea, 7);
        }
        $bindArray = [
            ':AccountID' => $id,
            ':Street' => $street,
            ':City' => $city,
            ':PostalCode' => $postalCode,
            ':Country' => $country,
            ':States' => $states,
            ':AptPinType' => $keyType,
            ':Switch' => $this->share->util->bitOperation($communityInfo['Switch'], $enableSmartHome, 5),
            ':EnableDormakaba' => $enableDormakaba,
        ];
        if ($enableScanToRegister !== null) {
            $bindArray[':EnableScanToRegister'] = $enableScanToRegister;
        }
        //新小区才能开启户户通
        if(intval($communityInfo['IsNew']) === 1){
            $oldData['Flags'] = $this->share->util->bitOperation($oldData['Flags'], $isCommunityCalls, 5);
        }

        if ($nameDisplay !== null) {
            $bindArray[':NameDisplay'] = $nameDisplay;
        }
        //dormakaba开关从关到开
        if(intval($communityInfo['EnableDormakaba']) === 0 && $enableDormakaba === "1"){            //确定ins账号是否已经创建dormakaba账号了

            $sql = 'select ThirdAccountUUID from '. PROXY_TABLES['DormakabaAccount'] .' where AccountUUID = :AccountUUID for update';
            $dormakabaInfo = $this->db->querySList($sql, [':AccountUUID' => $installerData["UUID"]]);
            if(count($dormakabaInfo) === 0){
                $this->output->echoErrorMsg(STATE_DORMAKABA_ACCOUNT_NOT_EXISTS);
            } else {
                $siteParam = [
                    'InsUuid' => $installerData["UUID"],
                    'DormakabaAccountUuid' => $dormakabaInfo[0]['ThirdAccountUUID'],
                    'CommunityUuid' => $oldData['UUID'],
                    'CommunityName' => $location,
                    'Country' => $country,
                    'State' => $states,
                    'PostCode' => $postalCode,
                    'Type' => 2, //1=单住户,2=社区项目',
                ];
                $this->loadProvider('phpAdaptNotify');
                $this->services->phpAdaptNotify->addDormakabaSite($siteParam);
            }
        }
        // 更新社区信息
        $this->dao->communityInfo->update($bindArray, 'AccountID');
        $this->db->update2ListWID(PROXY_TABLES['account'], [':ID' => $id, ':TimeZone' => $timeZone,
        ':Location' => $location,':ChargeMode' => $chargeMode, ':CustomizeForm' => $customizeForm,
        ':SendExpireEmailType' => $sendExpireEmailType, ':SendRenew' => $sendRenew, ':Flags' => $oldData['Flags']]);
        // 更新视频存储配置
        if ($enableVideoStorage != null) {
            $this->loadModel('videoStorage');
            $this->models->videoStorage->updateCommunityVideoStorage($installerData['UUID'], $oldData['UUID'], $enableVideoStorage);
        }

        // 记录审计日志
        $this->auditLog->setLog(AUDIT_CODE_EDIT_COMMUNITY, $this->env, [], $installerData['Account']);
        if ($oldData['TimeZone'] !== $timeZone) {
            $this->auditLog->setLog(AUDIT_CODE_SET_COMMUNITY_TIME, $this->env, ["GTM$timeZone"], $installerData['Account']);
        }
        if (intval($oldData['ChargeMode']) !== $chargeMode) {
            $this->auditLog->setLog($chargeMode === CHARGEMODE_NOPERMISSION ? AUDIT_CODE_COMMUNITY_CHARGE_MODEL_BY_INSTALLER : AUDIT_CODE_COMMUNITY_CHARGE_MODEL_BY_USER, $this->env, [], $installerData['Account']);
        }
    }

    /**
     * @description:pm修改社区信息
     * @author:lwj 2023-02-09 14:07:11 V6.6
     * @lastEditor:lwj 2023-02-09 14:07:11 V6.6
     * @param:
     * @return array
     */
    public function editProjectInfoForPM()
    {
        $params = [
            PROXY_ROLE['projectId'], PROXY_ROLE['projectUUID'],'Location', 'Street', 'City', 'PostalCode',
            'States', 'Country', 'AptPinType', 'DevOfflineNotify', 'EnableSIMWarning',
            'EnableUserPin', 'PhoneCode', 'MobileNumber', 'IsAccessFollow','CreateTenantsDate','DeleteTenantsDate',
            'TempkeyValidityPeriod?:enum("0","30")'
        ];
        list($projectId, $projectUUID, $location, $street, $city, $postalCode, $states, $country, $aptPinType,
            $devOfflineNotify, $enableSIMWarning, $enableUserPin, $phoneCode, $mobileNumber, $isAccessFollow, $createTenantsDate, $deleteTenantsDate,
            $tempkeyValidityPeriod) = $this->getParams($params);
        //需要过滤前后的空格
        $postalCode = trim($postalCode);

        $projectInfo = [
            ':Street' => $street,
            ':City' => $city,
            ':PostalCode' => $postalCode,
            ':States' => $states,
            ':Country' => $country,
            ':PhoneCode' => $phoneCode,
            ':MobileNumber' => $mobileNumber,
            ':AptPinType' => $aptPinType,
            ':AccountID' => $projectId
        ];
        
        // 添加TempkeyValidityPeriod字段，限制临时密钥的有效时间
        if ($tempkeyValidityPeriod !== null) {
            $projectInfo[':TempkeyValidityPeriod'] = $tempkeyValidityPeriod;
        }

        $sql = 'select IsNew,Switch from '. PROXY_TABLES['communityInfo'] .' where AccountID = :AccountID for update';
        $data = $this->db->querySList($sql, [':AccountID' => $projectId])[0];
        // V6.5 PM增加打开警报触发自动开门的开关(新小区),Switch第6位
        $tempSwitchForOffline = $this->share->util->bitOperation($data['Switch'], $devOfflineNotify, 2);
        $tempSwitchForPin = $this->share->util->bitOperation($tempSwitchForOffline, $enableUserPin, 3);
        $projectInfo[':Switch'] = $this->share->util->bitOperation($tempSwitchForPin, $enableSIMWarning, 4);
        $this->db->update2ListWID(
            PROXY_TABLES['account'],
            [':Location' => $location, ':ID' => $projectId]
        );

        $this->dao->communityInfo->update($projectInfo, 'AccountID');
        //如果 EnableUserPin 的值 是关的，则需要同步清空对应社区用户已创建的PIN 的数据
        if ($enableUserPin == '0') {
            $projectUsers = $this->dao->personalAccount->getAllResidentByProjectUuid($projectUUID);
           if (!empty($projectUsers)) {
               $accounts = [];
               foreach ($projectUsers as $projectUser) {
                   if ($projectUser['Role'] == COMENDMROLE || $projectUser['Role'] == COMENDSROLE) {
                       $accounts[] = $projectUser['Account'];
                   }
               }

               $this->loadUtil('key', true);
               $this->utils->_common->key->delPinCodeByAccounts($accounts);
           }
        }

        $this->loadUtil('account', true);
        $comInfo = $this->utils->_common->account->getManagerInfo($projectId);
        $reload = 0;
        if ($comInfo['Location'] !== $location) {
            $reload = 1;
        }

       $this->notifySmartHomeUpdateProject($data['Switch'], $projectId);

       $this->log->debug('projectUUID:{projectUUID}, isAccessFollow: {isAccessFollow}, createTenantsDate: {createTenantsDate}, deleteTenantsDate: {deleteTenantsDate} ',['projectUUID' => $projectUUID, 'isAccessFollow' => $isAccessFollow, 'createTenantsDate' => $createTenantsDate, 'deleteTenantsDate' => $deleteTenantsDate]);
        //7.1.0 如果是rentManager社区，修改相关设置
        if (($projectUUID!==null) &&($isAccessFollow!==null) &&($createTenantsDate!==null)&& ($deleteTenantsDate!==null)){
            $this->loadModel('rentManager');
            $this->models->rentManager->editCommunityInfo($projectUUID, $isAccessFollow, $createTenantsDate, $deleteTenantsDate);
        }
      
        return ['data' => ['reload' => $reload]];
    }

    /**
     * @Author: chenpl
     * @Description: 保存新社区紧急告警配置信息
     * @Params:EmergencyDoorGroup: Device;relay;SecurityRelay    IsAllEmergencyDoor:是否社区下的所有设备的全部relay和securityRelay
     * @Return:
     * @Date: 2025/3/3
     */
    public function editProjectEmergencyInfo()
    {
        $params = [
            PROXY_ROLE['projectId'],  PROXY_ROLE['projectUUID'], 'TriggerAction','IsSendEmergencyNotifications', 'EmergencyDoorGroup', 'IsAllEmergencyDoor'
        ];
        list($projectId, $projectUUID, $triggerAction, $isSendEmergencyNotifications, $emergencyDoorGroup, $isAllEmergencyDoor) = $this->getParams($params);

        $sql = 'select IsNew,Switch from '. PROXY_TABLES['communityInfo'] .' where AccountID = :AccountID for update';
        $data = $this->db->querySList($sql, [':AccountID' => $projectId])[0];
        if ($data['IsNew'] === '1') {
            // 新社区
            $tempEnableTriggerAction = $this->share->util->bitOperation($data['Switch'], $triggerAction, 6);
            $tempIsSendEmergencyNotifications = $this->share->util->bitOperation($tempEnableTriggerAction, $isSendEmergencyNotifications, 8);
            $projectInfo = [
                ':Switch' => $tempIsSendEmergencyNotifications,
                ':IsAllEmergencyDoor' => intval($isAllEmergencyDoor),
                ':AccountID' => $projectId
            ];

            $this->dao->communityInfo->update($projectInfo, 'AccountID');

            if ($isAllEmergencyDoor !== COMMUNITY_IS_SELECTED_ALL_RELAY_FOR_EMERGENCY ) {
                // 如果是勾选全部Door,则把设备保存到表里面去, 后续操作都会查询所有符合条件的门去下发给设备
                // 不勾选全部设备，则将选择的设备保存到表中
                $this->loadUtil('device', true);
                $this->loadUtil('device', 'community');
                // 非全选Door 按照传进来的设备信息将对应设备的对应Relay和SecurityRelay设置成EmergencyRelay
                if (!empty($emergencyDoorGroup)) {
                    // 先删除，再插入
                    $currentDevices = $this->dao->communityEmergencyDoorGroup->selectByKey('AccountUUID', $projectUUID);
                    foreach ($currentDevices as $value) {
                        $this->dao->communityEmergencyDoorGroup->delete($value['ID']);
                    }
                    // 插入
                    $deviceList = json_decode($emergencyDoorGroup, true);
                    foreach ($deviceList as $deviceInfo) {
                        $deviceUUID = $deviceInfo['Device'];
                        $relays = $deviceInfo['Relay'];
                        $securityRelays = $deviceInfo['SecurityRelay'];
                        foreach ($relays as $relayIndex) {
                            $uuid = $this->share->util->uuid();
                            $insertData = [
                                ':UUID' => $uuid,
                                ':AccountUUID' => $projectUUID,
                                ':DevicesUUID' => $deviceUUID,
                                ':RelayIndex' => $relayIndex,
                                ':RelayType' => 0,
                            ];
                            $this->dao->communityEmergencyDoorGroup->insert($insertData);
                        }
                        foreach ($securityRelays as $securityRelayIndex) {
                            $uuid = $this->share->util->uuid();
                            $insertData = [
                                ':UUID' => $uuid,
                                ':AccountUUID' => $projectUUID,
                                ':DevicesUUID' => $deviceUUID,
                                ':RelayIndex' => $securityRelayIndex,
                                ':RelayType' => 1,
                            ];
                            $this->dao->communityEmergencyDoorGroup->insert($insertData);
                        }
                    }
                }
            } else {
                // 如果是选择全部设备，需要把之前保存的一些设备都清掉
                $currentDevices = $this->dao->communityEmergencyDoorGroup->selectByKey('AccountUUID', $projectUUID);
                foreach ($currentDevices as $value) {
                    $this->dao->communityEmergencyDoorGroup->delete($value['ID']);
                }
            }
        }
    }

    /**
     * @description 通知家居
     * @param
     * @param $switch
     * @param $projectId
     * @param $uuid
     * <AUTHOR> 2024/5/15 18:20 v6.8.0
     * @lastEditor clay 2024/5/15 18:20 v6.8.0
     */
    public function notifySmartHomeUpdateProject($switch, $projectId, $uuid = "") {
        $smartHomeSwitch = $this->share->util->getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
        if ($smartHomeSwitch == 1) {
            if (!$uuid) {
                $proInfo = $this->utils->_common->account->getManagerInfo($projectId);
                $uuid = $proInfo["UUID"];
             }
            $this->notifySmartHome->collect(['Project', 'notifyUpdateProject'], [$uuid], 6710);
        }
    }


    /**
     * @description:移动侦测的设置
     * @author:lwj 2023-02-03 16:25:15 V6.6
     * @lastEditor:lwj 2023-02-03 16:25:15 V6.6
     * @param:{string} EnableMotion 0-关闭，1-红外线检测，2-视屏检测
     * @param:{string} MotionTime 警报延迟时间
     */
    public function setMotion()
    {
        $params = [PROXY_ROLE['projectId'], 'EnableMotion', 'MotionTime', 'PackageDetection'];
        list($projectId, $enableMotion, $motionTime, $packageDetection) = $this->getParams($params);
        $this->db->update2ListWKey(
            PROXY_TABLES['communityInfo'],
            [':EnableMotion' => $enableMotion, ':MotionTime' => $motionTime, ':AccountID' => $projectId, ':EnablePackageDetection'=>$packageDetection],
            'AccountID'
        );
    }

    /**
     * @description:时间设置
     * @author:lwj 2023-02-06 16:25:15 V6.6
     * @lastEditor:lwj 2023-02-06 16:25:15 V6.6
     * @param:{string} TimeZone 时区
     * @param:{string} CustomizeForm 时间格式，2-12小时，3-24小时
     */
    public function setTime()
    {
        $params = [PROXY_ROLE['projectId'], 'TimeZone', 'CustomizeForm'];
        list($projectId, $timeZone, $customizeForm) = $this->getParams($params);
        $this->db->update2ListWKey(
            PROXY_TABLES['account'],
            [':TimeZone' => $timeZone, ':CustomizeForm' => $customizeForm, ':ID' => $projectId],
            'ID'
        );
        $this->loadUtil('account', true);
        $proInfo = $this->utils->_common->account->getManagerInfo($projectId);
        $this->auditLog->setLog(AUDIT_CODE_SET_COMMUNITY_TIME, $this->env, ["GTM$timeZone"], $proInfo['Account']);

        $sql = 'select Switch from '. PROXY_TABLES['communityInfo'] .' where AccountID = :AccountID';
        $data = $this->db->querySList($sql, [':AccountID' => $projectId])[0];
        $this->notifySmartHomeUpdateProject($data['Switch'], $projectId, $proInfo['UUID']);
    }

    /**
     * @description:访客系统设置
     * @author:lwj 2023-02-06 16:25:15 V6.6
     * @lastEditor:lwj 2023-02-06 16:25:15 V6.6
     * @param:{string} IDCardVerification 身份证识别
     * @param:{string} FaceEnrollment 人脸识别
     */
    public function setVisitor()
    {
        $params = [PROXY_ROLE['projectId'], 'IDCardVerification', 'FaceEnrollment'];
        list($projectId, $idCardVerification, $faceEnrollment) = $this->getParams($params);
        $this->db->update2ListWKey(
            PROXY_TABLES['communityInfo'],
            [':IDCardVerification' => $idCardVerification, ':FaceEnrollment' => $faceEnrollment, ':AccountID' => $projectId],
            'AccountID'
        );
    }

    /*
     *@description 编辑PM管理社区的权限
     *<AUTHOR> 2023-09-11 13:40:48 V6.7.0
     *@lastEditor cj 2023-09-11 13:40:48 V6.7.0
     *@param {*} ID:string-required PM的ID
     *@param {*} CommunityID:string-required 社区ID
     *@param {*} EnableDeleteAccount:enum("0"","1") PM是否可以删除社区下账号
     *@param {*} EnableShowLog:enum("0"","1") PM是否可以展示Log
     *@return void
     */
    public function editPmMngPermission()
    {
        $params = ['ID:string-required', 'CommunityID:string-required', 'EnableDeleteAccount:enum("0", "1")', 'EnableShowLog:enum("0", "1")'];
        list($pmId, $communityID, $enableDeleteAccount, $enableShowLog) = $this->getParams($params);

        $this->loadUtil('manage', true);
        $pmMngInfo = $this->utils->_common->manage->getPmMngInfo($pmId, $communityID);
        if (empty($pmMngInfo)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_COMMUNITY_NOT_BELONG_PM]);
        }
        $this->utils->_common->manage->updatePmMngPermission(['ID' => $pmMngInfo['ID'], 'EnableDeleteAccount' => $enableDeleteAccount,
        'EnableShowLog' => $enableShowLog], 'ID');
    }

    /**
     * @description:编辑项目限制的Apt num,Landline,FeaturePlan
     * @param: NumberOfApt      房间数
     * @param: EnableLandline   是否开启落地服务
     * @param: FeaturePlan      功能方案
     * @param: ID               社区ID
     * @author: shoubin.chen 2024/6/24 14:56:42 V6.8.1
     * @lastEditor: shoubin.chen 2024/6/24 14:56:42  V6.8.1
     */
    public function editProjectLimit()
    {
        $params = ['EnableLandline?:enum("0","1")', 'FeaturePlan?:string', 'ID:id'];
        list($landLine, $featurePlan, $id) = $this->getParams($params);
        //只有二者都存在才进行更改
        if ($landLine === null || $featurePlan === null) {
            return;
        }
        $this->log->debug('update community limit,ProjectID={ProjectID}', ['ProjectID' => $id]);

        //校验配置是否发生变化
        $checkResult = $this->utils->self->checkCommunityConfigChange($landLine, $featurePlan, $id);
        if (!$checkResult['result']) {
            return;
        }
        $numberOfApt = $checkResult['oldNumOfApt'];
        $communityInfo = $this->dao->communityInfo->selectByKey('AccountID', $id)[0];
        $IsHaveAccessArea = $this->share->util->getSpecifyBitLE($communityInfo['Switch'], 7);
        //更新配置项
        $this->callSelfFunc('editCommunityChargePlan', [$numberOfApt, $landLine, $id, $featurePlan, $IsHaveAccessArea]);
        //更新计费模型
        $this->loadModel('billingInfo', true);
        $this->models->_common->billingInfo->updateBillList(1, $id, 1);
    }


    /**
     * @description: 设置三方锁同步到的品牌
     * @param: {string} ITec iTec锁
     * @author: shoubin.chen 2025/1/22 15:38:04 V7.1.0
     * @lastEditor: shoubin.chen 2025/1/22 15:38:04  V7.1.0
     */
    public function setThirdLockSyncDoorMethod()
    {
        $params = ['ITec:enum("0","1")','Dormakaba:enum("0","1")', PROXY_ROLE_CHECK['projectId'], PROXY_ROLE_CHECK['pmUUID']];
        list($iTec, $dormakaba,$projectId, $pmUuid) = $this->getParams($params);

        $this->loadUtil('account', true);
        $communityInfo = $this->utils->_common->account->getCommunityInfo($projectId);
        $oldSyncLock = $this->share->util->getSpecifyBitLE($communityInfo['SyncDoorMethodToThirdPartyLock'], 1);
        $oldSyncDormakaba = $this->share->util->getSpecifyBitLE($communityInfo['SyncDoorMethodToThirdPartyLock'], 2);

        $newSyncLock = $this->share->util->bitOperation($communityInfo['SyncDoorMethodToThirdPartyLock'], $iTec, 1);
        $this->dao->communityInfo->update([
            'AccountID' => $projectId,
            'SyncDoorMethodToThirdPartyLock' => $newSyncLock
        ], 'AccountID');

        //从关到开
        if ($oldSyncLock == 0 && $iTec == 1) {
            $this->loadModel('thirdLock');
            $taskUuid = $this->models->thirdLock->syncPinAndCardToThirdLock($projectId, $pmUuid);

            $this->loadProvider('phpAdaptNotify');
            $this->services->phpAdaptNotify->syncPinAndCard($taskUuid);
        }

        //从开到关
        if ($oldSyncLock == 1 && $iTec == 0) {
            $this->loadModel('thirdLock');
            $taskUuid = $this->models->thirdLock->syncClosePinAndCardToThirdLock();
            $this->loadProvider('phpAdaptNotify');
            $this->services->phpAdaptNotify->syncPinAndCard($taskUuid);
            $taskUuid="";
        }


        //更新开关值
        $communityInfo = $this->utils->_common->account->getCommunityInfo($projectId);
        $this->log->debug("oldSyncDormakaba={oldSyncDormakaba}",['oldSyncDormakaba'=>json_encode($oldSyncDormakaba)]);
        $newSyncDormakaba = $this->share->util->bitOperation($newSyncLock,$dormakaba,2);
        $this->log->debug("newSyncDormakaba={newSyncDormakaba}",['newSyncDormakaba'=>json_encode($newSyncDormakaba)]);

        //更新开关值
        $newSyncSwitch = $newSyncDormakaba;
        $this->log->debug("newSyncSwitch={newSyncSwitch}",['newSyncSwitch'=>json_encode($newSyncSwitch)]);
        $this->loadUtil('smartLock', true);
        $userList = $this->dao->personalAccount->getAptUserListByNewPm($projectId);
        $staffList = $this->dao->staff->selectByArray([['CommunityID', $projectId]]);
        $deliveryList = $this->dao->delivery->selectByArray([['CommunityID', $projectId]]);
        $user = array_column($userList, 'ID');
        $staff = array_column($staffList, 'ID');
        $delivery = array_column($deliveryList, 'ID');

        $this->loadUtil('account', true);
        $projectUuid = $this->utils->_common->account->accountSelectByKey('ID', $projectId)[0]['UUID'];
        //如果是从关到开
        if ($oldSyncDormakaba == 0 && $dormakaba == 1) {
            //查出当前项目所有的user,delivery,staff
            $this->dao->communityInfo->update([
                'AccountID' => $projectId,
                'SyncDoorMethodToThirdPartyLock' => $newSyncSwitch
            ], 'AccountID');

            $userParam = [
                'type'=>'communityUser',
                'addList'=>['user'=>$user,'staff'=>$staff,'delivery'=>$delivery,'accessGroup'=>['type'=>'all']],
                'delList'=>[],
            ];
            $taskUuid = $this->utils->_common->smartLock->pushSmartLockForTask(['type'=>'createByPm'],['type'=>'createByPm'],$userParam,true,'dormakaba',null,$projectUuid, $pmUuid);
        }

        $this->log->debug("oldSyncDormakaba={oldSyncDormakaba},dormakaba={dormakaba}",['oldSyncDormakaba'=>json_encode($oldSyncDormakaba),'dormakaba'=>json_encode($dormakaba)]);
        if ($oldSyncDormakaba == 1 && $dormakaba == 0) {
            //开关从开到关，删除所有pin/card
            $this->log->debug("*************************************");
            $userParam = [
                'type'=>'communityUser',
                'addList'=>[],
                'delList'=>['user'=>$user,'staff'=>$staff,'delivery'=>$delivery,'accessGroup'=>['type'=>'all']],
            ];
            $this->log->debug("userParam={userParam}",['userParam'=>json_encode($userParam)]);
            $this->utils->_common->smartLock->pushSmartLock(['type'=>'createByPm'],['type'=>'createByPm'],$userParam,false,'dormakaba',null,$projectUuid, $pmUuid);

            $this->dao->communityInfo->update([
                'AccountID' => $projectId,
                'SyncDoorMethodToThirdPartyLock' => $newSyncSwitch
            ], 'AccountID');
        }


        return ['data' => ['TaskUUID' => $taskUuid]];
    }
}
