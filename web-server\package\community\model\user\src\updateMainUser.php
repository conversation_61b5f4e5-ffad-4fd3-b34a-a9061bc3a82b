<?php
/**
 * @Author:zyc
 * @Date 2021/11/23
 * @LastEditors:zyc
 */

namespace package\community\model\user\src;

use package\community\model\user\config\Code;

trait UpdateMainUser
{
    // 更新房间信息
    public function updateRoom()
    {
        $params = ['ID', 'RoomNumber', 'RoomName', PROXY_ROLE['projectId'], 'CallType', 'EnableIpDirect', 'WebRelayID', 'Floor'];
        list($id, $roomName, $roomNumber, $communityId,
        $callType, $enableIpDirect, $webRelayId, $floor) = $this->getParams($params);
        $this->loadUtil('common', true);
        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, ['Special', 'Account', 'PhoneStatus', 'UUID']);
        $special = $userData['Special'];
        // 修改数据库roomName字段
        if ($roomNumber !== null) {
            $this->utils->self->updateRoomNumber($id, $roomNumber, $communityId, $floor);
        }
        $this->utils->self->updateRoomCnf($id, $roomName, $callType, $enableIpDirect, $communityId);
        $this->dao->personalAccountCnf->update([
            ':Account'=>$userData['Account'],':WebRelay'=>$webRelayId], 'Account');

        $this->notifySmartHome->collect(['User', 'notifyUpdateRoom'], [$userData['UUID']], 6710);
        return ['Special' => $special];
    }

    /**
     * @description: 修改房间后修改主账号信息
     * @author: cj 2022-03-12 16:16:45 V6.4
     * @LastEditors: cj 2022-03-12 16:16:45 V6.4
     * @param {*}
     * @return {*}
     */
    public function updateComMainUser()
    {
        $params = [
            'ID', 'FirstName', 'LastName', 'Name', 'Email', 'MobileNumber', 'Phone',
            'Phone2', 'Phone3', 'PhoneCode', 'CallType', 'Key',
            'EnableIpDirect', 'TempKeyPermission', PROXY_ROLE['projectId'], 'AccessFloor', 'Remark'
        ];
        list(
            $id, $firstName, $lastName, $name, $email, $mobile, $phone,
            $phone2, $phone3, $phoneCode, $callType, $key,
            $enableIpDirect, $tempKeyPermission, $communityId, $accessFloor, $remark
        ) = $this->getParams($params);

        // 1.获取用户信息
        $this->loadUtil('common', true);
        // V6.5.2.5 access floor兼容其他入口传值，例如openapi
        $accessFloor = $accessFloor === null ? '' : $accessFloor;
        $accessFloor = $this->utils->self->checkAccessFloor($accessFloor);

        $remark = $remark == null ? '' : $remark;

        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, [
                    'PhoneStatus', 'Account', 'UnitID', 'RoomID', 'UUID', 'UserInfoUUID']);
        $userInfo = $this->dao->personalAccountUserInfo->selectByUUID($userData['UserInfoUUID'], 'Email,MobileNumber')[0];
        $userData['Email'] = $userInfo['Email'];
        $userData['MobileNumber'] = $userInfo['MobileNumber'];
        $newPhoneStatus = $callType == 1 ? 1 : 0;
        $tempKeyPermission = $tempKeyPermission == null ? 1 : $tempKeyPermission;
        $enableIpDirect = $enableIpDirect === null ? 1 : $enableIpDirect;
        $oldCallType = $this->dao->personalAccountCnf->selectByAccount($userData['Account'], 'CallType')[0]['CallType'];
        $oldPhoneStatus = $oldCallType == 1 ? 1 : 0;

        // 2.更新用户信息
        // 无关联多套房才可修改邮箱手机号
        $linkNum = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userData['UserInfoUUID'], 'count(*)')[0]['count(*)'];
        if ($linkNum === '1') {
            $userInfoArr = ['Email' => $email, 'MobileNumber' => $mobile, 'UUID' => $userData['UserInfoUUID']];
            if ($email !== $userData['Email']) {
                $this->loadUtil('account', true);
                $this->utils->_common->account->deleteUserToken($userData['Account']);
                // 事件收集：邮箱改变发送邮件
                $password = $this->share->util->generatePw(8);
                $this->notifyEvent->collect(
                    $email . '_communityCreateUser',
                    'communityCreateUser',
                    [$userData['Account'], $password, $email]
                );
                $userInfoArr['Passwd'] = $this->share->util->getSaltPwd($password);
            }
            $this->dao->personalAccountUserInfo->update($userInfoArr, 'UUID');
        }
        $bindArray = [':ID'=>$id,':Name'=>$name,':FirstName'=>$firstName,
        ':LastName'=>$lastName,':Phone'=>$phone,':Phone2'=>$phone2,':Phone3'=>$phone3,
        ':PhoneCode'=>$phoneCode,':PhoneStatus'=>$newPhoneStatus,
        ':TempKeyPermission'=>$tempKeyPermission, ':EnableIpDirect'=>$enableIpDirect];
        $this->dao->personalAccount->update($bindArray);
        // V6.5.3.5 PersonalCommunityInfo 修改，兼容旧数据。PersonalCommunityInfo是新增表，旧数据没有刷
        $uuid = $userData['UUID'];
        $userExtraInfo = $this->dao->personalAccountCommunityInfo->selectByPersonalAccountUUID($uuid);
        if (count($userExtraInfo) === 0) {
            $this->dao->personalAccountCommunityInfo->insert([
                ':UUID'=>$this->share->util->uuid(),
                ':PersonalAccountUUID' => $uuid,
                ':AccessFloor' => $accessFloor,
                ':Remark' => $remark
            ]);
        } else {
            $this->dao->personalAccountCommunityInfo->update([
                ':PersonalAccountUUID' => $uuid,
                ':AccessFloor' => $accessFloor,
                ':Remark' => $remark
            ], 'PersonalAccountUUID');
        }

        $this->loadModel('tempKey', 'community');
        $this->models->_community->tempKey->updateUserAccessFloor($userData['Account'], $accessFloor, '');

        // 原来主账号邮箱和手机号都没填，重新填入需要重置未填手机和邮箱的从账号
        $this->loadUtil('account', true);
        $this->utils->_common->account->sendEmailForSub($userData, $id, $email, $mobile, COMENDSROLE);
        $this->dao->personalAccountCnf->update([
                ':Account'=>$userData['Account'],':CallType'=>$callType], 'Account');
        
        // 3.设置群呼和审计日志
        $this->loadProvider('sip');
        $this->services->sip->phoneStatusCheck($oldPhoneStatus, $newPhoneStatus, $userData['Account']);
        // V5.4 sip call or ip call
        if ($userData['EnableIpDirect'] != $enableIpDirect) {
            $this->services->sip->setIpDirect('Devices', $userData['Account'], $enableIpDirect);
        }
        
        // $keyData = $this->utils->_common->common->getTableInfoPlus(PROXY_TABLES['personalPrivateKey'], [
        //             'ID'], ['Node'=>$userData['Account'], 'Special'=>1]);
        // if (count($keyData) > 0) {
        //     $this->db->update2ListWID(PROXY_TABLES['personalPrivateKey'], [
        //         ':UnitID'=>$userData['UnitID'],':Code'=>$key,':ID'=>$keyData[0]['ID']]);
        // }
        $this->auditLog->setLog(AUDIT_CODE_EDIT_FAMILY_MASTER, $this->env, ['Account' => $userData['Account'], 'ID' => $id, 'Email' => $email, 'MobileNumber' => $mobile], $userData['Account']);

        if ($oldCallType != $callType) {
            $installerId = $this->utils->_common->common->getAccountInfoWithID($communityId, [
                            'ManageGroup'])['ManageGroup'];
            $installerAccount = $this->utils->_common->common->getAccountInfoWithID($installerId, [
                            'Account'])['Account'];
            $roomName = $this->dao->communityRoom->selectByID($userData['RoomID'], 'RoomName')[0]['RoomName'];
            $buildName = $this->dao->communityUnit->selectByID($userData['UnitID'], 'UnitName')[0]['UnitName'];
            $this->auditLog->setLog(AUDIT_CODE_CALL_TYPE_ARRAY[$callType], $this->env, [$buildName, $roomName], $installerAccount);
        }
        if ($userData['Email'] !== $email) {
            $this->auditLog->setLog(AUDIT_CODE_USER_EMAIL, $this->env, [$email, $name], $userData['Account']);
        }
        if ($userData['MobileNumber'] !== $mobile) {
            $this->auditLog->setLog(AUDIT_CODE_USER_MOBILE, $this->env, [$mobile, $name], $userData['Account']);
        }
        $this->notifySmartHome->collect(['User', 'notifyUpdateUser'], [$uuid, $uuid], 6710);
        return ['Account' => $userData['Account'], 'Passwd' => $password];
    }

    /**
     * @description: endUser修改社区主/从账号信息
     * @author: cj 2022-03-14 14:31:45 V6.4
     * @LastEditors: Do not edit
     * @param ID指从账号ID
     * @return {*}
     */
    // public function updateUserForEndUser()
    // {
    //     $params = [
    //         'ID', 'FirstName', 'LastName', 'Name', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', PROXY_ROLE['mainUserId']
    //     ];
    //     list(
    //         $id, $firstName, $lastName, $name, $phone, $phone2, $phone3, $phoneCode, $mainUserId
    //     ) = $this->getParams($params);
    //     $this->loadUtil('account', true);
    //     $bindArray = [
    //         ':ID'=>$mainUserId,
    //         ':Name'=>$name,
    //         ':Phone'=>$phone,
    //         ':PhoneCode'=>$phoneCode,
    //         ':FirstName' => $firstName,
    //         ':lastName' => $lastName
    //     ];
    //     if ($id !== null) {
    //         $bindArray[':ID'] = $id;
    //         $this->utils->_common->account->subUserInMainCheck($id, $mainUserId);
    //         $data = $this->utils->_common->account->getUserInfo($id);
    //         $this->dao->personalAccount->update($bindArray);
    //         $this->log->endUserLog(2, null, "edit user:$name");
    //         $this->auditLog->setLog(AUDIT_CODE_EDIT_FAMILY_MEMBER, $this->env, ['Account' => $data['Account'], 'ID' => $id], $data['Account']);
    //     } else {
    //         $bindArray[':Phone2'] = $phone2;
    //         $bindArray[':Phone3'] = $phone3;
    //         $this->dao->personalAccount->update($bindArray);
    //     }
    // }

    /**
     * @description: App修改从账号信息
     * @author: cj 2022-03-14 15:19:12 V6.4
     * @LastEditors: Do not edit
     * @param ID指从账号ID
     * @return {*}
     */
    // public function updateSubUserForApp()
    // {
    //     $params = [
    //         'FirstName', 'LastName', 'Name', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', PROXY_ROLE['subUserId']
    //     ];
    //     list(
    //         $firstName, $lastName, $name, $phone, $phone2, $phone3, $phoneCode, $subUserId
    //     ) = $this->getParams($params);

    //     $this->loadUtil('account', true);
    //     $this->utils->_common->account->updateUserBasicInfo($subUserId, $firstName, $lastName, $name, $phone, $phone2, $phone3, $phoneCode);
    // }

    // public function changePerData()
    // {
    //     $params = [
    //         PROXY_ROLE['mainUserId'],
    //         'Name',
    //         'Phone',
    //         'Phone2',
    //         'Phone3',
    //         'PhoneCode'
    //     ];
    //     list($userId,$name,$phone,$phone2,$phone3,$phoneCode) = $this->getParams($params);
    //     $this->dao->personalAccount->update([":Name" => $name, ":Phone" => $phone, ":Phone2" => $phone2, ":Phone3" => $phone3,
    //             ":PhoneCode" => $phoneCode, ":ID" => $userId]
    //     );
    // }

    /**
     * @description: 新PM修改房间信息
     * @author: cj 2022/04/02 15:17
     * @LastEditors: Do not edit
     * @param {*} ID指房间主账号ID
     * @return {*}
     */
    public function updateRoomForPM()
    {
        $params = [
            'ID', 'AptName', 'CallType', 'EnableIpDirect', 'TempKeyPermission', 'FamilyMemberControl', 'AllowCreateSlaveCnt', PROXY_ROLE['projectId'],
            'RegisterFaceControl', 'AllowCreateIDAccess?:enum("0","1")', 'AllowCreateRfCard?:enum("0","1")'
        ];
        list($id,$aptName,$callType,$enableIpDirect,$tempKeyPermission,
        $familyMemberControl,$allowCreateSlaveCnt,$communityId, $RegisterFaceControl, $allowCreateIDAccess, $allowCreateRfCard) = $this->getParams($params);
        
        $this->utils->self->updateRoomCnf($id, $aptName, $callType, $enableIpDirect, $communityId);
        $this->loadUtil('common', true);
        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, [
            'Account', 'UUID']);
        $account = $userData['Account'];

        $oldCnf = $this->db->querySList(
            'select Flags from '.PROXY_TABLES['personalAccountCnf'].' where Account = :Account for update',
            [':Account' => $account]
        )[0];
        $updatePersonalAccountCnfData = [':Account' => $account];
        // 6.2需求，家庭成员数限制
        $flags = $this->share->util->bitOperation($oldCnf['Flags'], $familyMemberControl, 1);
        if ($familyMemberControl === '1') {
            $updatePersonalAccountCnfData[':AllowCreateSlaveCnt'] = $allowCreateSlaveCnt;
        }
        //face控制
        $flags = $this->share->util->bitOperation($flags, $RegisterFaceControl, 2);
        $updatePersonalAccountCnfData[':Flags'] = $flags;
        //允许用户创建ID-Access开关
        if ($allowCreateIDAccess !== null) {
            $flags = $this->share->util->bitOperation($flags, $allowCreateIDAccess, 3);
            $updatePersonalAccountCnfData[':Flags'] = $flags;
        }
        //是否允许用户创建RF Card
        if ($allowCreateRfCard  !== null) {
            $flags = $this->share->util->bitOperation($flags, $allowCreateRfCard, 4);
            $updatePersonalAccountCnfData[':Flags'] = $flags;

            //判断当前用户的这个开关是否是关，是的话，要同步清空该PM下APP用户所创建的RF Card数据
            if ($allowCreateRfCard == '0') {
                $accounts [] = $account;
                $this->loadUtil('account', true);
                $childAccounts = $this->utils->_common->account->personalAccountSelectByKey('ParentID', $id);
                if (!empty($childAccounts)) {
                    foreach ($childAccounts as $childAccount) {
                        $accounts [] = $childAccount['Account'];
                    }
                }
                $this->loadUtil('key', true);
                $this->utils->_common->key->delRfCardByAccounts($accounts);
            }
        }
        $this->dao->personalAccountCnf->update($updatePersonalAccountCnfData, 'Account');
        $this->dao->personalAccount->update([':ID' => $id, ':TempKeyPermission' => $tempKeyPermission]);
        $this->notifySmartHome->collect(['User', 'notifyUpdateRoom'], [$userData['UUID']], 6710);
    }

    /**
     * @description 修改pm Name,实际无法编辑
     * @return void
     * @lastEditor kxl
     * <AUTHOR> 2022/4/24 15:35 V6.4
     */
    // public function changeName()
    // {
    //     $params = ["Name", PROXY_ROLE["mainUserId"]];
    //     list($name,$userId) = $this->getParams($params);
    //     $this->dao->personalAccount->update([":ID" => $userId, ":Name" => $name]);
    // }

    /**
     * @description: 设置用户初始化Pin
     * @param PIN指设置用户的初始化Pin
     * @author: cj 2022/7/22 17:15 V6.5
     * @return {*}
     * @LastEditor: cj 2022/7/22 17:15 V6.5
     */
    public function setProjectUserPinInit()
    {
        $params = ['PIN', PROXY_ROLE['mainUserId'], PROXY_ROLE['subUserId']];
        list($pin, $mainUserId, $subUserId) = $this->getParams($params);
        if (empty($subUserId)) {
            $userId = $mainUserId;
        } else {
            $userId = $subUserId;
        }
        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getUserInfo($userId);
        // 设置app已登陆,Switch设置从账号重新初始化
        $this->dao->personalAccount->update([':ID' => $userId, ':Initialization' => 1, ':Switch' => $this->share->util->bitOperation($userData['Switch'], 1, 1)]);
        // 初始化Pin
        if ($pin) {
            $this->loadUtil('key');
            $this->utils->key->addPinInit($pin, $userId);
        }
    }

    /**
     * @description: App修改社区账号信息
     * @author: cj 2022-03-14 15:19:12 V6.4
     * @LastEditors: Do not edit
     * @param {*}
     * @return {*}
     */
    public function updateMainUserForApp()
    {
        $params = [
            'PIN', 'FirstName', 'LastName', 'Name', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', PROXY_ROLE['mainUserId'], PROXY_ROLE['subUserId'], PROXY_ROLE['projectId']
        ];
        list(
            $pin, $firstName, $lastName, $name, $phone, $phone2, $phone3, $phoneCode, $mainUserId, $subUserId, $projectId
        ) = $this->getParams($params);

        $this->loadUtil('account', true);
        $mainUserData = $this->utils->_common->account->getUserInfo($mainUserId);
        if (empty($subUserId)) {
            $userId = $mainUserId;
            $userData = $mainUserData;
        } else {
            $userId = $subUserId;
            $userData = $this->utils->_common->account->getUserInfo($subUserId);
        }
        if ($pin !== null) {
            $this->loadUtil('common', true);
            // 区别新旧社区
            $isNew = $this->dao->communityInfo->selectByAccountID($projectId, 'IsNew')[0]['IsNew'];
            // 除6.1前社区，后续的都走ComPerPrivateKey表
            if ($isNew == 1) {
                $keyData = $this->dao->commPerPrivateKey->selectByArray([['Account',$userData['Account']], ['Special',1]], 'ID,Code')[0];
                if ($keyData) {
                    $this->dao->commPerPrivateKey->update([':Code' => $pin, ':ID' => $keyData['ID']]);

                    $oldCode = $keyData['Code'];
                    $this->loadModel('iTec');
                    $this->models->iTec->updateITecPinForApp($userId, $oldCode, $pin, $projectId);
                } else {
                    $this->loadUtil('key');
                    $this->utils->key->addPinInit($pin, $userId);
                }
            } else {
                $keyData = $this->dao->personalPrivateKey->selectByArray([['AccountID',$userId], ['Special',1]], 'ID')[0];
                if ($keyData) {
                    $this->dao->personalPrivateKey->update([':Code' => $pin, ':ID' => $keyData['ID']]);
                } else {
                    $this->loadUtil('key');
                    $this->utils->key->addPinInit($pin, $userId);
                }
            }
        }
        $this->loadUtil('account', true);
        $this->utils->_common->account->updateUserBasicInfo($userId, $firstName, $lastName, $name, $phone, $phone2, $phone3, $phoneCode);
        // 通知家居用户信息更新
        $this->notifySmartHome->collect(['User', 'notifyUpdateUser'], [$mainUserData['UUID'], $userData['UUID']], 6710);
    }

    /**
     * @description: 新社区搬出房间
     * @author: cj 2022-12-19 16:15:10 V6.5.3
     * @LastEditor: cj 2022-12-19 16:15:10 V6.5.3
     * @param {*} $id 主账号ID
     * @param {*} $projectId 社区ID
     * @return {*}
     */
    public function resetRoom()
    {
        $params = [
            'ID', PROXY_ROLE['projectId']
        ];
        list($id, $projectId) = $this->getParams($params);
        // 重置房间相关信息
        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getUserInfo($id);
        // 房间住人做处理：1.重置用户信息2.多套房重置站点
        if ($userData['Special'] === '0') {
            $newAppMainUserAccount = $this->utils->_common->account->resetRoomInfo($id);
            $password = $this->share->util->generatePw(8);
            $userInfoUUID = $userData['UserInfoUUID'];
            // 检测主账号是否关联多套房
            $linkNum = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userInfoUUID, 'count(*)')[0]['count(*)'];
            // 如果未被Link需删除主账号信息
            if ($linkNum === '1') {
                $this->dao->personalAccountUserInfo->update(['Email' => null, 'MobileNumber' => null, 'AppLastLoginUserAccount' => '', 'UUID' => $userInfoUUID, 'Passwd' => $this->share->util->getSaltPwd($password)], 'UUID');
            } else {
                // 多套房时若作为主站点需重置主站点，上次登录为主站点也需重置上次登录账号
                $this->utils->self->deleteUserWMultiLink($userInfoUUID, $userData['Account'], $newAppMainUserAccount);
                $newUserInfoUUID = $this->share->util->uuid(\dao\PersonalAccountUserInfo::UUID_PREFIX);
                $this->dao->personalAccountUserInfo->insert([ 'Email' => null, 'MobileNumber' => null, 'AppMainUserAccount' => $userData['Account'], 'AppLastLoginUserAccount' => '', 'UUID' => $newUserInfoUUID, 'Passwd' => $this->share->util->getSaltPwd($password)]);
                $this->dao->personalAccount->update(['ID'=>$id, 'UserInfoUUID'=>$newUserInfoUUID]);
            }
        }
        // 清除主账号的Log
        $this->utils->_common->account->deleteRoomLog($id);
        //删除三方锁数据
        $this->loadModel('thirdLock');
        $this->models->thirdLock->deleteThirdLockData($projectId, $id);
        // 清除Apt内全部Pin、Card、人脸以及QRCode
        $this->utils->self->resetRoomData($id, $projectId);
        // 删除全部从账号
        $this->loadUtil('common', true);
        $subs = $this->dao->personalAccount->selectByArray([['ParentID', $id], ['Role', COMENDSROLE]], 'ID');
        foreach ($subs as $sub) {
            $this->loadModel('user', false, [
                'dataContainer' => [
                    'ID' => $sub['ID'],
                    PROXY_ROLE['projectId'] => $projectId,
                    'IsDeleteSelf' => true
                ]
            ]);
            $this->models->user->deleteCommunitySubUser();
        }

        $this->loadProvider('phpAdaptNotify');
        $this->services->phpAdaptNotify->sendResetCommunityRoom($userData['UUID']);

        //7.1.0 重置房间通知 家居重置房间
        $this->notifySmartHome->collect(['room', 'notifyResetRoom'], [$userData['UUID']], 6710);
    }

    /**
     * @author:lwj 2023-01-12 11:11:12 V6.6
     * @lastEditor:lwj 2023-01-12 11:11:12 V6.6
     * @description:设置用户过期时间
     * @param:
     */
    public function setMainUserExpireTime()
    {
        $params = ['ID', 'TimeZone', 'ExpireTime'];
        list($id, $timeZone, $expireTime) = $this->getParams($params);
        $this->loadUtil('common', true);
        $userData = $this->utils->_common->common->getPersonalAccountInfoWithID($id, ['Account', 'Role', 'UUID']);
        if($userData['Role'] !== strval(COMENDMROLE) && $userData['Role'] !== strval(PMENDMROLE)){
            $this->output->echoErrorMsg(
                STATE_NOT_PERMISSION,
                ['externalErrorObj' => Code::EXT_STATE_IS_NOT_MAIN_USER]
            );
        }

        $expireTime = $this->share->util->setTimeZone($expireTime, $timeZone, '', '-');
        $this->dao->personalAccount->update([':ExpireTime' =>$expireTime, ':ID' =>$id]);

        $userUUID = $userData['UUID'];
        $this->dao->personalAccount->update(['ExpireTime' => $expireTime, 'ParentUUID' => $userUUID], 'ParentUUID');
        $this->loadProvider('sip');
        $this->services->sip->insertSipEnable($userData['Account']);
    }

    /**
     * @description:设置用户呼叫
     * @author:lwj 2023-04-10 13:33:51 V6.6
     * @lastEditor:lwj 2023-04-10 13:33:51 V6.6
     * @param:
     */
    public function setCall()
    {
        $params = [
            PROXY_ROLE['mainUserId'], 'EnableRobinCall', 'RobinCallTime', 'RobinCallVal'
        ];
        list($mainUserId, $enableRobinCall, $robinCallTime, $robinCallVal) = $this->getParams($params);
        $this->loadUtil('account', true);
        $userData = $this->utils->_common->account->getUserInfo($mainUserId);
        intval($enableRobinCall) === 1 ? 1 : 0;
        foreach ($robinCallVal as &$value) {
            $tmpValue = explode('-', $value);
            $value = ['ID' => intval($tmpValue[1]), 'Type' => intval($tmpValue[0]), 'Pre' => 0];
        }
        $robinCallVal = json_encode((object)$robinCallVal);
        $userUpdateCnf = [
            ':EnableRobinCall' => $enableRobinCall,
            ':RobinCallTime' => $robinCallTime,
            ':RobinCallVal' => $robinCallVal,
            ':Account' => $userData['Account']
        ];
        $this->dao->personalAccountCnf->update($userUpdateCnf, 'Account');
    }

    /*
     *@description 编辑用户的可达楼层
     *<AUTHOR> 2023-07-04 15:49:16 V6.6.0
     *@lastEditor cj 2023-07-04 15:49:16 V6.6.0
     *@param {*} ID 用户ID
     *@param {*} AccessFloor 可达楼层,用分号拼接
     *@return void
     */
    public function editUserAccessFloor()
    {
        $params = [
            'ID', 'AccessFloor'
        ];
        list($id, $accessFloor) = $this->getParams($params);
        $this->loadUtil('account', true);
        $userInfo = $this->utils->_common->account->getUserInfo($id);
        $accessFloor = $accessFloor === null ? '' : $accessFloor;
        $accessFloor = $this->utils->self->checkAccessFloor($accessFloor);
        $this->dao->personalAccountCommunityInfo->update([
        'PersonalAccountUUID' => $userInfo['UUID'],
        'AccessFloor' => $accessFloor
        ], 'PersonalAccountUUID');

        $parentAccount = "";
        if (intval($userInfo['Role']) == COMMUNITYGRADE) {
            $parentAccount = $this->dao->personalAccount->selectByUUID($userInfo['ParentUUID'], 'Account')[0]['Account'];
        }

        // 更新AccessFloor后，对于已经创建的TempKey，如果有楼层移除，需要将已创建的TempKey的楼层也一起移除掉
        $this->loadModel('tempKey', 'community');
        $this->models->_community->tempKey->updateUserAccessFloor($userInfo['Account'], $accessFloor, $parentAccount);
    }
}
