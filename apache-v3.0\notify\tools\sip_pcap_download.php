<?php
error_reporting(0);

require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');
require_once(dirname(__FILE__) . '/../../config/base.php');
require_once(dirname(__FILE__) . '/../../config/global.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../socket.php');
require_once(dirname(__FILE__) . '/../funcs_face_interface.php');
require_once(dirname(__FILE__) . '/../funcs_common.php');
require_once(dirname(__FILE__) . '/../utility.php');
require_once(dirname(__FILE__) . '/../funcs_user.php');

// nginx下载url
#const NGINX_DOWNLOAD_HOST = "https://inner.dev.akuvox.com:8091";
const NGINX_DOWNLOAD_HOST = IPV4IMG;
const NGINX_LINK_KEY = "ak_fdfs";

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = DATABASEIP;
    $dbport = DATABASEPORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=siphub";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}


function computedPcapLink($path)
{
    if (preg_match("/^\/group/", $path)) {
        $time = time();
        $link = $path;
        $path = substr($path, 8);
        return NGINX_DOWNLOAD_HOST . $link . "?token=" . md5($path . NGINX_LINK_KEY . $time) . "&ts=$time";
    }
}

// 下载pacp文件
function downloadPcapFile($callid)
{
    echo "downloadPcapFile begin, callid:$callid\n";;
    $db = getDB();
    try {
        $sth = $db->prepare("select B.fileurl,B.filename from sip_media_records B left join sip_media A on A.caller_uuid = B.sip_media_uuid or A.callee_uuid = B.sip_media_uuid where A.caller_callid = :callid");
        $sth->bindParam(':callid', $callid, PDO::PARAM_STR);
        $sth->execute();
        $fileInfoList = $sth->fetchAll(PDO::FETCH_ASSOC);

        if ($fileInfoList) {
            // 创建存放路径
            $savePath = $callid;
            if (is_dir($savePath)) {
                rmdir($savePath);
                mkdir($savePath);
            } else {
                mkdir($savePath);
            }

            foreach ($fileInfoList as $fileInfo) {
                $link = computedPcapLink($fileInfo['fileurl']);
                echo "downloadPcapFile begin nginx link:$link\n";
                $saveFilePath = $savePath . "/" . $fileInfo['filename'];

                $command = "wget --no-check-certificate -O " . escapeshellarg($saveFilePath) . " " . escapeshellarg($link);
                exec($command, $output, $returnCode);
            }
            
            if (file_exists($savePath . ".tar.gz")) {
                unlink($savePath . ".tar.gz");
            }

            $command = "tar -czvf " . $savePath . ".tar.gz " . $savePath;
            echo "command:$command\n";;
            exec($command);
            echo "download success, export cmd: sz $savePath.tar.gz\n";
        }
    } catch (PDOException $e) {
        echo "db exception=:" . $e->getMessage() . "\n";
        return false;
    }

    return true;
}

//使用说明：① php sip_download_pcap.php $callid
if ($argc != 2) {
    echo("download: php sip_download_pcap.php <callid> \n");
    exit(1);
}

$callid = $argv[1];
downloadPcapFile($callid);