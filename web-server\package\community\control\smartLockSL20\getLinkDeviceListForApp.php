<?php

namespace package\community\control\smartLockSL20;

use package\community\control\Executor;

class GetLinkDeviceListForApp extends \package\community\control\CommunityCommonController
{
    public function exec()
    {
        $executor = new Executor();
        $executor->execMiddle('getOperaId', true);
        $executor->execModel('smartLockSL20.getLinkDeviceListForApp');
        $executor->execEcho(STATE_SUCCESS_QUERY, ['data']);
    }
}
