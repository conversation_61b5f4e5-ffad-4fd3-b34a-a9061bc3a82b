<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

include_once __DIR__ . "/../../util/model.php";
include_once __DIR__ . "/../../util/string.php";
include_once __DIR__ . "/../../util/computed.php";
include_once __DIR__ . "/../../util/time.php";
include_once __DIR__ . "/../staff/util.func.php";

class CBuild
{
    /**
     * @msg: 获取所有楼栋
     */
    public function queryall()
    {
        $params = [
            // 社区ID
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]);
        foreach ($data as &$val) {
            if (intval($val['Floor']) === \model\staff\LADDER_CONTROL_ALL_FLOOR) {
                $val['Floor'] = 'all';
            }
        }
        unset($val);
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 添加楼栋
     */
    public function add()
    {
        global $cMessage;
        $params = [
            // 社区/办公ID
            "userAliasId" => "",
            "Name" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $name = $params["Name"];
        $this->log->actionLog("#model#build#add#userId=$userId;name=$name");
        
        $projectGrade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID"=>$userId])[0]["Grade"];
        $this->log->actionLog("#model#build#add#projectGrade=$projectGrade");

        // 2021-12-28 V6.3区别社区和办公下重复提示语
        if ($this->db->isExistFiled("CommunityUnit", [":UnitName" => $name, ":MngAccountID" => $userId], null)) {
            if ($projectGrade == COMMUNITYGRADE) {
                $cMessage->echoErrorMsg(StateBuildingExit);
            } elseif ($projectGrade == OFFICEGRADE) {
                $cMessage->echoErrorMsg(StateDepartmentExit);
            }
        }
        $this->db->insert2List("CommunityUnit", [":UnitName" => $name, ":MngAccountID" => $userId]);
        $id = $this->db->lastInsertId();

        // 2021-12-27 cj 7.0版本新增办公，默认都自动添加权限组
        if ($projectGrade == COMMUNITYGRADE) {
            // 2021-05-26 kxl 6.1版本新社区在添加楼栋时需要自动添加权限组
            $autoAddAccess = $this->db->querySList('select IsNew from CommunityInfo where AccountID = :AccountID', [':AccountID'=>$userId])[0]['IsNew'] == 1;
            $accessName = vsprintf(MSGTEXT['BuildAccessName'], [$name]);
        } elseif ($projectGrade == OFFICEGRADE) {
            $autoAddAccess = true;
            $accessName = vsprintf(MSGTEXT['departmentAccessName'], [$name]);
        }
        if ($autoAddAccess == true) {
            $this->db->insert2List('AccessGroup', [
                ":Name"=>$accessName,
                ":CreateTime"=>\util\computed\getNow(),
                ":CommunityID"=>$userId,
                ":UnitID"=>$id,
                ":SchedulerType"=>1,
                ":StartTime"=>'00:00:00',
                ":StopTime"=>'23:59:59'
            ]);
        }

        \util\computed\setGAppData(["ID" => $id]);
    }

    public function delete()
    {
        global $cMessage;
        $params = [
            // 社区ID
            "userAliasId" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $this->log->actionLog("#model#build#delete#userId=$userId;id=$id");
        // 6.3 兼容社区和办公
        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', [":ID" => $userId])[0]['Grade'];
        if (!$this->db->isExistFiled("CommunityUnit", [":MngAccountID" => $userId, ":ID" => $id], null)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        //有设备或用户不让删
        $devices = $this->db->queryAllList("Devices", ["equation" => [":UnitID" => $id]]);
        if (count($devices) !== 0) {
            if ($projectGrade == COMMUNITYGRADE) {
                $cMessage->echoErrorMsg(StateBuildingBindDevice);
            } elseif ($projectGrade == OFFICEGRADE) {
                $cMessage->echoErrorMsg(StateDepartmentBindDevice);
            }
        }

        $users = $this->db->queryAllList("PersonalAccount", ["equation" => [":UnitID" => $id]]);
        if (count($users) !== 0) {
            if ($projectGrade == COMMUNITYGRADE) {
                $cMessage->echoErrorMsg(StateBuildingBindUser);
            } elseif ($projectGrade == OFFICEGRADE) {
                $cMessage->echoErrorMsg(StateDepartmentBindUser);
            }
        }
        $this->db->delete2ListWID("CommunityUnit", $id);
        // V5.4 guard phone 管理build，删除对应PubDevMngList build
        $this->db->delete2ListWKey("PubDevMngList", "UnitID", $id);
        // 6.1 kxl直接删除build对应权限组
        $this->db->delete2ListWKey('AccessGroup', 'UnitID', $id);
        \util\computed\setGAppData(["ID" => $id]);
    }

    public function edit()
    {
        global $cMessage;
        $params = [
            // 社区ID
            "userAliasId" => "",
            "ID" => "",
            "Name" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $name = $params["Name"];
        $this->log->actionLog("#model#build#edit#userId=$userId;id=$id;name=$name");
        // 6.3 兼容社区和办公
        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', [":ID" => $userId])[0]['Grade'];
        if (!$this->db->isExistFiled("CommunityUnit", [":MngAccountID" => $userId, ":ID" => $id], null)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        //验证Name是否存在
        if ($this->db->isExistFiled("CommunityUnit", [":UnitName" => $name, ":MngAccountID" => $userId], $id)) {
            if ($projectGrade == COMMUNITYGRADE) {
                $cMessage->echoErrorMsg(StateBuildingExit);
            } elseif ($projectGrade == OFFICEGRADE) {
                $cMessage->echoErrorMsg(StateDepartmentExit);
            }
        }
        $this->db->update2ListWID("CommunityUnit", [":UnitName" => $name, ":ID" => $id]);
        \util\computed\setGAppData(["ID" => $id]);
    }
}
