<?php

namespace package\community\model\smartLockSL20\src;

trait Get
{
    public function getLinkDeviceListForApp()
    {
        $params = [PROXY_ROLE_CHECK['mainUserUUID'], PROXY_ROLE_CHECK['mainUserId']];
        list($mainUserUUID, $mainUserID) = $this->getParams($params);
        // 获取pm给该用户设置的个人设备relay权限组
        $data = $this->db->querySList(
            'select Account,UUID from ' . PROXY_TABLES['personalAccount'] . ' where ID = :ID', [':ID' => $mainUserID]
        )[0];
        $account = $data['Account'];
        $accessId = $this->db->querySList(
            'select ID from ' . PROXY_TABLES['userAccessGroup'] . ' where Account=:Account', [':Account' => $account]
        )[0]['ID'];
        $userAccessGroupDeviceList = $this->db->querySList(
            'select MAC, Relay from ' . PROXY_TABLES['userAccessGroupDevice'] . ' where UserAccessGroupID=:UserAccessGroupID', [':UserAccessGroupID' => $accessId]
        );
        $userDeviceRelayAvailableMap = [];
        foreach ($userAccessGroupDeviceList as $index => $item) {
            $userDeviceRelayAvailableMap[$item['MAC']] = $item['Relay'];
        }

        $deviceType = [DEVICE_TYPE['multipleDoor'], DEVICE_TYPE['singleDoor'], DEVICE_TYPE['accessControl']];
        $searchArray = ['Type' => $deviceType, 'PersonalAccountUUID' => $mainUserUUID, 'Grade' => DEVICE_GRADE_FAMILY];

        $this->loadUtil('device', true);
        $bindDeviceList = $this->utils->_common->device->getDevicesInfo($searchArray, 'ID,UUID,Location,MAC,Relay,Firmware');

        //获取设备的Relay使用情况
        $this->loadUtil('smartLockSL20', true);
        $this->loadUtil('device', true);
        foreach ($bindDeviceList as $index => &$device) {
            // 对设备开启的relay与pm给该用户设置的个人设备relay权限取交集
            $relayJson = $device['Relay'];
            $data = json_decode($relayJson, true);
            $relayAvailable = intval($userDeviceRelayAvailableMap[$device['MAC']]);
            for ($i = 0; $i < 4; $i++) {
                if (isset($data[$i])) {
                    if (($relayAvailable & 1) == 0) {
                        // pm把个人relay权限关闭, 即使设备本身的relay开着也把enable置0
                        $data[$i]['enable'] = 0;
                    }
                }
                $relayAvailable = $relayAvailable >> 1;
            }
            $device['Relay'] = json_encode($data);
            $device['LinkedRelayList'] = $this->utils->_common->smartLockSL20->getRelayInUse($device['UUID'], $device['MAC']);
        }
        unset($device);
        $bindDeviceList = array_values($bindDeviceList);

        return ['data' => $bindDeviceList];
    }
}