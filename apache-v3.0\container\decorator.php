<?php
namespace container;
// 装饰器特征拓展
trait decorator {
    private $methods = [];
    // 设置方法组
    function __call ($name,$arguments) {
        if(!array_key_exists($name,$this->methods)) {
            throw new \Exception("Please use _setMethod() to register method '$name' first");
        }else{
           return $this->methods[$name]($this->id,...$arguments);
        }
    }
    function _setMethod ($name,\Closure $call) {
        if(!array_key_exists($name,$this->methods)) {
            $this->methods[$name] = $call;
        }else {
            throw new \Exception("You have used this decorator:$name");
        }
    }

    public function _issetMethod($name)
    {
        return array_key_exists($name, $this->methods);
    }

    function _delete ($name) {
        unset($this->methods[$name]);
    }
}