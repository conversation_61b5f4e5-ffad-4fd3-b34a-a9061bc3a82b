<?php
/*
 * @Description: 新增时检测key是否和RoomName
 * @version:
 * @Author: zyc
 * @Date: 2021-02-24 09:54:00
 * @LastEditors  : zyc
 */
namespace middleware;

include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";

class CAddModDeviceParam implements IMiddleware
{
    public function handle(\Closure $next)
    {
        global $cLog,$cMessage,$db;
        $params = [
            "MAC"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac=$params["MAC"];
        $data = $db->querySList('select ID,Type,Relay from PersonalDevices where MAC=:MAC', [':MAC' => $mac]);
        $id = $data[0]['ID'];
        $type = $data[0]['Type'];
        $relay = $data[0]['Relay'];

        \util\computed\setGAppData(["ID"=>$id, "Type"=>$type, "Relay"=>$relay]);
        $next();
    }
}
