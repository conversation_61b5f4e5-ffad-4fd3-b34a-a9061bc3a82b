<?php

namespace dao;
use framework\BasicDao;

class VisitorIDAccess extends BasicDao
{
    //当前表名
    public $table = 'VisitorIDAccess';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';

    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {string} $uuid UUID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByUUID($uuid, $fields = '*')
    {
        return $this->selectByKey('UUID', $uuid, $fields);
    }

    /**
     * @description: 根据UUID的值查询对应数据
     * @param {array} $uuids UUID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2024/06/18 09:28 V6.5.4
     * @lastEditors: systemCreator 2024/06/18 09:28 V6.5.4
     */
    public function selectByUUIDWArray($uuids, $fields = '*')
    {
        return $this->selectByKeyWArray('UUID', $uuids, $fields);
    }

    /**
     * @description: pm获取访客ID-Access列表
     * @param $bindArray
     * @param $offset
     * @param $row
     * @return array
     * @author: shoubin.chen 2024/6/18 11:38:16 V6.8.1
     * @lastEditor: shoubin.chen 2024/6/18 11:38:16  V6.8.1
     */
    public function getListByPm($bindArray, $offset, $row)
    {
        $where = '';
        if (isset($bindArray['ProjectUUID'])) {
            $projectUUID = $bindArray['ProjectUUID'];
            $where .= "ProjectUUID = :ProjectUUID ";
            $bindArray[':ProjectUUID'] = $projectUUID;
        }
        if (isset($bindArray['Name'])) {
            $name = $bindArray['Name'];
            $where .= "And Name LIKE :Name ";
            $bindArray[':Name'] = "%$name%";
        }
        if (isset($bindArray['Run'])) {
            $run = $bindArray['Run'];
            $where .= "And Run LIKE :Run ";
            $bindArray[':Run'] = "%$run%";
        }
        if (isset($bindArray['Serial'])) {
            $serial = $bindArray['Serial'];
            $where .= "And Serial LIKE :Serial ";
            $bindArray[':Serial'] = "%$serial%";
        }

        $orderBy = "UpdateTime DESC";

        $bindArray[":Now"] = $this->share->util->getNow();
        $sql = "select * from VisitorIDAccess Where $where  order by $orderBy limit $offset, $row;";

        $total = $this->execute("select count(*) from VisitorIDAccess Where $where", $bindArray)[0]["count(*)"];

        $list = $this->execute($sql, $bindArray);
        return [intval($total), $list];
    }

}