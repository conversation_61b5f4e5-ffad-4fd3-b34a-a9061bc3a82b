<?php
/*
 * @Description: location检查
 * @version:
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors  : cj
 */
namespace middleware;

include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/string.php";
class CDevLocationCheck implements IMiddleware
{
    public function handle(\Closure $next)
    {
        global $cMessage;
        global $cLog;
        $params = ["Location"=>""];
        $location = \util\model\getParamsFromContainer($params, $this->dataContainer)["Location"];
        $cLog->actionLog("#middle#devLocationCheck#location=".$location);
        if ($location === null) {
            $next();
            return;
        }
        if (\util\string\checkByteLength($location, 63) || !$location) {
            $cMessage->echoErrorMsg(StateLocationLong);
        }
        $next();
    }
}
