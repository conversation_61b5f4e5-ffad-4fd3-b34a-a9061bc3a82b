<?php
//require_once("akcs_conf.php");
require_once(dirname(__FILE__).'/adapt_define.php');
require_once(dirname(__FILE__).'/proto.php');
//ini_set('date.timezone','Asia/Shanghai');

/* php <-->csmain的消息枚举 */
const MSG_P2A=0x00400000;

const MSG_P2A_REBOOT_TO_DEVICE = MSG_P2A + 1;
    
/** 当界面上请求远程配置设备时通知csadapt,csadapt会将消息ID修改并透传给csmain, PHP <-->csadapt
 */
const MSG_P2A_CONFIGURE_TO_DEVICE = MSG_P2A + 2;	//4.6注释，后续版本不再使用
    
/** 当界面上请求远程设备的配置信息时通知csadapt,csadapt会将消息ID修改并透传给csmain, PHP <-->csadapt
 */
const MSG_P2A_CONFIGURE_FROM_DEVICE = MSG_P2A + 3;	//4.6注释，后续版本不再使用

/**界面或者app请求重置密码时,发送邮件通知用户
 */
const MSG_P2A_RESET_PASSWD = MSG_P2A + 4;
const MSG_P2A_OFFICE_RESETPWD = MSG_P2A + 2004;
    
//added by chenyc,2017-09-01
/**个人终端用户,界面添加/删除个人终端用户主账号,或者个人终端管理员往主账号添加设备,或者修改key所能打开的设备时触发
 */
const MSG_P2A_PERSONNAL_UPDATE_USER = MSG_P2A + 5;	//4.6注释，后续版本不再使用
    
//同一联动系统中设备的location或者app用户的昵称发生改变时,csadapt透传给csmain,由csmain去通知设备进行更新,app不需要通知,app是实时查询的
const MSG_P2A_PERSONNAL_UPDATE_NODE_DEV = MSG_P2A + 6;

//同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
const MSG_P2A_PERSONNAL_ALARM_DEAL = MSG_P2A + 7;
    
//个人终端管理员添加设备
const MSG_P2A_PERSONNAL_ADD_DEV = MSG_P2A + 8;
    
//个人终端管理员修改设备
const MSG_P2A_PERSONNAL_MODIFY_DEV = MSG_P2A + 9;

//个人终端管理员删除设备
const MSG_P2A_PERSONNAL_DEL_DEV = MSG_P2A + 10;

//个人终端管理员删除个人终端账号
const MSG_P2A_PERSONNAL_DEL_UID = MSG_P2A + 11;

//个人终端管理员删除个人终端账号
const MSG_P2A_PERSONNAL_DEL_PIC = MSG_P2A + 12;

//个人终端管理员创建uid
const MSG_P2A_PERSONNAL_CREATE_UID = MSG_P2A + 13;
    
//个人终端管理员创建uid
const MSG_P2A_PERSONNAL_CHANGE_PWD = MSG_P2A + 14;

//个人终端管理员需要发送文本消息给联动系统
const MSG_P2A_PERSONNAL_NEW_TEXT_MESSAGE = MSG_P2A + 15;

    
/////////////V4.0社区开始
//社区更新设备操作
const MSG_P2A_COMMUNITY_UPDATE_NODE_DEV = MSG_P2A + 16;
    
//社区更新用户
const MSG_P2A_COMMUNITY_UPDATE_USER = MSG_P2A + 17;
    
//社区警告被处理通知
const MSG_P2A_COMMUNITY_ALARM_DEAL = MSG_P2A + 18;
    
//社区设备添加
const MSG_P2A_COMMUNITY_ADD_DEV = MSG_P2A + 19;
    
//社区设备修改
const MSG_P2A_COMMUNITY_MODIFY_DEV = MSG_P2A + 20;
    
//社区设备删除
const MSG_P2A_COMMUNITY_DEL_DEV = MSG_P2A + 21;
    
//社区删除账号
const MSG_P2A_COMMUNITY_DEL_UID = MSG_P2A + 22;
    
//社区删除图片
const MSG_P2A_COMMUNITY_DEL_PIC = MSG_P2A + 23;
    
//社区创建账号
const MSG_P2A_COMMUNITY_CREATE_UID = MSG_P2A + 24;
    
//社区改变密码
const MSG_P2A_COMMUNITY_CHANGE_PWD = MSG_P2A + 25;
    
//社区管理员发送消息
const MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE = MSG_P2A + 26;

//社区修改主账号通知 只有修改主账号名称和房间号码
const MSG_P2A_COMMUNITY_MODIFY_MASTER_USER = MSG_P2A + 27;
// 注册账号时候要求发送验证码给某个邮箱
const MSG_P2A_PERSONNAL_SEND_CKECK_CODE_TO_EMAIL = MSG_P2A + 28;
// 设备码被绑定后，通知设备清空设备码
const MSG_P2A_NOTIFY_DEV_CLEAN_DEV_CODE = MSG_P2A + 29;
// 付费后 由过期到未过期的通知
const MSG_P2A_NOTIFY_DEV_NOT_EXPIRE = MSG_P2A + 30;
// 个人社区删除公共设备,需要修改所有联动联系人
const MSG_P2A_NOTIFY_DEL_PER_PUBLIC_DEV_VIRTUAL_ACCOUNT = MSG_P2A + 31;
// app上增加视频存储计划
const MSG_P2A_NOTIFY_ADD_VIDEO_SCHED = MSG_P2A + 32;
// app上增加视频存储计划
const MSG_P2A_NOTIFY_DEL_VIDEO_SCHED = MSG_P2A + 33;
// 用户实时删除视频存储
const MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE = MSG_P2A + 34;
// 社区修改公共设备的key
const MSG_P2A_NOTIFY_COMMUNITY_PUBLIC_KEY_CHANGE = MSG_P2A + 35;

// 账号是否激活
const MSG_P2A_NOTIFY_ACOUNT_ACTIVE = MSG_P2A + 36;
// 分享tempkey
const MSG_P2A_NOTIFY_SHARE_TEMPKEY = MSG_P2A + 37;
// 远程开门
const MSG_P2A_NOTIFY_REMOTE_OPENDOOR = MSG_P2A + 38;
// 创建物业
const MSG_P2A_NOTIFY_CREATE_PROPERTY_WORK = MSG_P2A + 39;
//更新所有社区公共设备
const MSG_P2A_NOTIFY_UPDATE_ALL_PUB_DEV = MSG_P2A + 40;

// 设备单独更新配置
const MSG_P2A_NOTIFY_DEV_CONFIG_UPDATE = MSG_P2A + 41;
//社区更新APT+PIN的方式
const MSG_P2A_NOTIFY_COMMUNITY_APT_PIN_CHANGE = MSG_P2A + 42;
//远程开门 开Security Relay
const MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY = MSG_P2A + 43;
//PM一键开关门
const MSG_P2A_NOTIFY_PM_EMERGENCY_DOOR_CONTROL = MSG_P2A + 44;
//家居设备通过web远程开门
const MSG_P2A_DEVICE_NOTIFY_REMOTE_OPENDOOR = MSG_P2A + 45;

const MSG_P2A_DEVICE_NOTIFY_REQUEST_CAPTURE = MSG_P2A + 46;

//ENDUSER续费成功
const MSG_P2A_NOTIFY_ENDUSER_RENEWSERVER = MSG_P2A + 103;

//alexa登陆
const MSG_P2A_NOTIFY_ALEXA_LOGIN = MSG_P2A + 105;
//alexa设置arming
const MSG_P2A_NOTIFY_ALEXA_SET_ARMING = MSG_P2A + 106;

//社区月租从原先的收费改成永久免费,后台去刷新社区下账号的过期时间
const MSG_P2A_NOTIFY_UPDATE_COMM_MONTHLY_FEE = MSG_P2A + 111;

//创建远程设备连接
const MSG_P2A_NOTIFY_CREATE_REMOTE_DEV_CONTORL = MSG_P2A + 112;

//发送短信验证码
const MSG_P2A_SEND_SMS_CODE_MESSAGE = MSG_P2A + 115;

//导出日志
const MSG_P2A_PM_EXPORT_LOG = MSG_P2A + 116;

//AUTOP单次下发
const MSG_P2A_UPDATE_CONFIG = MSG_P2A + 118;

//AUTOP批量下发 REGULAR
const MSG_P2A_REGULAR_AUTOP = MSG_P2A + 119;

//AUTOP批量下发 ONCE
const MSG_P2A_ONCE_AUTOP = MSG_P2A + 120;

//删除app账户
const MSG_P2A_DELETE_APP_ACCOUNT = MSG_P2A + 121;

//权限组更新
const MSG_P2A_NOTIFY_ACCESS_GROUP_MODIFY = MSG_P2A + 207;
//权限组用户更新
const MSG_P2A_NOTIFY_ACCESS_GROUP_PER_MODIFY = MSG_P2A + 208;
//新社区人员更新
const MSG_P2A_NOTIFY_COMMUNITY_PERSONAL_MODIFY = MSG_P2A + 209;
//新社区用户
const MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 210;

//导入用户数据通知
const MSG_P2A_NOTIFY_COMMUNITY_IMPORT_ACCOUNT_DATAS= MSG_P2A + 211;

//PM激活邮件
const MSG_P2A_NOTIFY_PM_ACOUNT_ACTIVE = MSG_P2A + 212;

//新版本更新installer下的用户或设备配置
const MSG_P2A_NOTIFY_PERSONAL_MESSAGE = MSG_P2A + 1000;
//新版本更新社区下的用户或设备配置或社区信息
const MSG_P2A_NOTIFY_COMMUNITY_MESSAGE = MSG_P2A + 1001;

//数据分析
const MSG_P2A_NOTIFY_DATA_ANALYSIS_NOTIFY = MSG_P2A + 1004;

//PM创建uid
const MSG_P2A_PM_CREATE_UID = MSG_P2A + 1005;

//远程重置设备
const MSG_P2A_RESET_TO_DEVICE = MSG_P2A + 1009;

//社区用户添加新站点
const MSG_P2A_COMMUNITY_ADD_NEW_SITE = MSG_P2A + 1010;

//单住户添加新站点
const MSG_P2A_PERSONAL_ADD_NEW_SITE = MSG_P2A + 1011;

//pm web link新站点
const MSG_P2A_PM_LINK_NEW_SITES = MSG_P2A + 1012;

//pm app 添加新站点
const MSG_P2A_PM_APP_ADD_NEW_SITE = MSG_P2A + 1013;

//pm web创建pm
const MSG_P2A_PM_WEB_CREATE_UID = MSG_P2A + 1014;

//pm web重置密码
const MSG_P2A_PM_WEB_CHANGE_PWD = MSG_P2A + 1015;

//发送短信验证码
const MSG_P2A_SEND_COMMON_SMS_CODE = MSG_P2A + 1016;

//发送邮件验证码
const MSG_P2A_SEND_COMMON_EMAIL_CODE = MSG_P2A + 1017;

//开启/关闭抓包
const MSG_P2A_PCAP_CAPTURE_CONTROL = MSG_P2A + 1018;

//发送邮件
const MSG_P2A_SEND_EMAIL_NOTIFY = MSG_P2A + 1019;

//发送过期邮件
const MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY = MSG_P2A + 1020;

//发送Message
const MSG_P2A_SEND_MESSAGE_NOTIFY = MSG_P2A + 1021;

// 发送过期Message
const MSG_P2A_SEND_MESSAGE_CRONTAB_NOTIFY = MSG_P2A + 1022;

//kit方案下的账号注销
const MSG_P2A_KIT_ACCOUNT_LOG_OFF = MSG_P2A + 2016;

//SIP开启/关闭抓包
const MSG_C2S_SIP_PCAP_CAPTURE_CONTROL = MSG_P2A + 2017;

//openapi socket健康探测
const MSG_C2S_OPENAPI_SOCKET_HEALTH_CHECK = MSG_P2A + 2018;

const MSG_P2A_NEWOFFICE_ALARM_DEAL = MSG_P2A + 2020;

define("UNIX_DOMAIN", "/var/adapt_sock/adapt.sock");

class Byte
{
    //长度
    private $length=0;
    
    private $byte='';
    //操作码
    private $code;
    public function setBytePrev($content)
    {
        $this->byte=$content.$this->byte;
    }
    public function getByte()
    {
        return $this->byte;
    }
    public function getLength()
    {
        return $this->length;
    }
    public function writeChar($string, $size)
    {
        $this->byte.=pack('a'.$size, $string);
        $this->length+=$size;
        return;//下面为V3.2使用的

        $strsize=strlen($string);
        if ($size<=$strsize) {
            exit('字符串超长');
        }
        $this->length+=$strsize;
        $str=array_map('ord', str_split($string));
        foreach ($str as $vo) {
            $this->byte.=pack('c', $vo);
        }

        for ($i=1; $i<=($size-$strsize); $i++) {
            $this->byte.=pack('c', '0');
            $this->length++;
        }
    }
    public function writeInt($str)
    {
        $this->length+=4;
        $this->byte.=pack('N', $str);
    }
    public function writeShortInt($interge)
    {
        $this->length+=2;
        $this->byte.=pack('n', $interge);
    }
    public function writeProtobuf($string)
    {
        $this->byte = $string;
        $strsize=strlen($string);
        $this->length+=$strsize;
    }
}

/*4.6注释，以下Class到CResetPasswdSocket前均已不使用*/
/*---------------------------------------------------------------------------*/

/**界面增加设备时php将设备信息传给csadpt,php<-->csmain
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 *@param[IN]  szMacOld -- 修改之前设备的MAC地址,只在修改设备时用
 *@param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 *typedef struct CSP2A_DEVICE_INFO_T {
 *	CHAR szMac[MAC_SIZE];
 *  CHAR szMacOld[MAC_SIZE];
 *   INT  nRet;
 *}CSP2A_DEVICE_INFO;
*/

class CAddDevSocket
{
    private $socket;
    private $port=8503;
    private $host='*************';
    private $byte;
    //以下为消息头字段定义
    private $id=0x00400001;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct($host='*************', $port=8503)
    {
        $this->host='localhost';
        $this->port=8503;
        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$this->socket) {
                TRACE('phpsocket: CAddDevSocket : Create socket failed');
                return;
            }
            //$result = socket_connect($this->socket,$this->host,$this->port);
            //if(!$result){
            //	exit('Connect host failed:'.$this->host);
            //}
        }

        $this->byte=new Byte();
    }
    public function copyDev($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            
            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeChar($data[1], 20);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
    public function recvMsg()
    {
        $result= socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            TRACE('phpsocket: CAddDevSocket : recvMsg failed');
            return;
        }
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", 0x00400001);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result = \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        }
        if (!$result) {
            TRACE('phpsocket: CAddDevSocket : sendMsg failed');
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}


/**界面修改设备时php将设备信息传给csadpt,php<-->csmain
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 *@param[IN]  szMacOld -- 修改之前设备的MAC地址,只在修改设备时用
 *@param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 *typedef struct CSP2A_DEVICE_INFO_T {
 *	CHAR szMac[MAC_SIZE];
 *  CHAR szMacOld[MAC_SIZE];
 *   INT  nRet;
 *}CSP2A_DEVICE_INFO;
*/

class CModifyDevSocket
{
    private $socket;
    private $port=8503;
    private $host='*************';
    private $byte;
    //以下为消息头字段定义
    private $id=0x00400002;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;
        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$this->socket) {
                TRACE('phpsocket: CModifyDevSocket : Create socket failed');
                return;
            }
            //$result = socket_connect($this->socket,$this->host,$this->port);
            //if(!$result){
            //	exit('Connect host failed:'.$this->host);
            //}
        }

        $this->byte=new Byte();
    }
    public function copyDev($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            
            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeChar($data[1], 20);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
    public function recvMsg()
    {
        $result= socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            TRACE('phpsocket: CModifyDevSocket : recvMsg failed');
            return;
        }
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", 0x00400002);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        }
        if (!$result) {
            TRACE('phpsocket: CModifyDevSocket : sendMsg failed');
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}


/**界面需要远程重启设备时php将设备信息传给csadpt,php<-->csmain
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 *@param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 * typedef struct CSP2A_REBOOT_DEVICE_T{
 *   CHAR szMac[MAC_SIZE];
 *   INT  nRet;
 * }CSP2A_REBOOT_DEVICE;
 */
class CRebootDevSocket
{
    private $socket;
    private $port=8503;
    private $host='*************';
    private $byte;
    //以下为消息头字段定义
    private $id=0x00400004;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;
        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$this->socket) {
                TRACE('phpsocket: CRebootDevSocket : Create socket failed');
                return;
            }
            //$result = socket_connect($this->socket,$this->host,$this->port);
            //if(!$result){
            //	exit('Connect host failed:'.$this->host);
            //}
        }

        $this->byte=new Byte();
    }
    public function copyDev($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            
            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeInt($data[1]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
    public function recvMsg()
    {
        $result= socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            TRACE('phpsocket: CRebootDevSocket : recvMsg failed');
            return;
        }
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", 0x00400004);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        }
        if (!$result) {
            TRACE('phpsocket: CRebootDevSocket :sendMsg failed');
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}

/** 远程请求设备的配置信息,php<-->csmain
 *
 *@param[IN]  szIPAddr -- 需要远程的设备IP地址
 *@param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

typedef struct CSP2A_DEVICE_CONFIGURE_T {
    CHAR szMac[MAC_SIZE];
    //modify by chenyc,2017-06-20,之前value是从界面上保存在value中的,在多用户同时操作时value会被覆盖，修改成从界面上直接将
    //key1=value1;key2=value2... 的形式传过来，nModuleID不再使用
    CHAR szConfigPair[CONFIGPAIR_SIZE];
    INT  nRet;
}CSP2A_DEVICE_CONFIGURE;
 */
 
class CSetDevConfSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    private $byte;
    //以下为消息头字段定义
    private $id=0x00400005;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;
        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$this->socket) {
                TRACE('phpsocket: CSetDevConfSocket :Create socket failed');
                return;
            }
            //$result = socket_connect($this->socket,$this->host,$this->port);
            //if(!$result){
            //	exit('Connect host failed:'.$this->host);
            //}
        }

        $this->byte=new Byte();
    }
    public function copyConfigure($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            
            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeChar($data[1], 640);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        
        //同步等待后台响应
        //$this->recvMsg();
        return;
    }
    public function recvMsg()
    {
        $result= socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            TRACE('phpsocket: CSetDevConfSocket :recvMsg failed');
            return;
        }
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", MSG_P2A_CONFIGURE_TO_DEVICE);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        }
        if (!$result) {
            TRACE('phpsocket: CSetDevConfSocket :sendMsg failed');
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}

/** 远程请求设备的配置信息,php<-->csmain
 *
 *@param[IN]  szIPAddr -- 需要远程的设备IP地址
 *@param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 *typedef struct CSP2A_DEVICE_CONFIGURE_T {
 *	CHAR szMac[MAC_SIZE];
 *  INT  nModuleID;
 *  INT  nRet;
 *}CSP2A_DEVICE_CONFIGURE;
 */
 
class CGetDevConfSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    private $byte;
    //以下为消息头字段定义
    private $id=0x00400005;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;

        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$this->socket) {
                TRACE('phpsocket: CGetDevConfSocket :Create socket failed');
                return;
            }
            //设置发送、接受超时时间
            socket_set_option($this->socket, SOL_SOCKET, SO_RCVTIMEO, array("sec"=>3, "usec"=>0 ));
            socket_set_option($this->socket, SOL_SOCKET, SO_SNDTIMEO, array("sec"=>1, "usec"=>0 ));
        }

        $this->byte=new Byte();
    }
    public function copyConfigure($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            
            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeInt($data[1]);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        
        return 0;
    }
    public function recvMsg()
    {
        $result= socket_recvfrom($this->socket, $buf, 2048, 0, $this->host, $this->port);
        if ($result === false) {
            //echo "recvfrom error";,在这里echo，也会返回给js,所以界面上也会打印
            // echo 'socket_recvfrom failed: '.socket_strerror(socket_last_error())."\n";
            return "-1";
        } elseif ($result < 24) {
            //echo "recvMsg length error, length is".$result;
            return "-1";
        }
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", MSG_P2A_CONFIGURE_FROM_DEVICE);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        }
        if (!$result) {
            TRACE('phpsocket: CGetDevConfSocket :sendMsg failed');
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}
/*---------------------------------------------------------------------------*/

/**界面或者app请求重置密码时,发送邮件通知用户
 *
 *@param[IN]  szUser -- 个人终端账号
 *@param[IN]  szEmail -- 个人终端账号邮箱

 typedef struct CSP2A_USER_EAMIL_INFO_T {
    CHAR szUser[USER_SIZE];
    CHAR szEmail[EMAIL_SIZE];
}CSP2A_USER_EAMIL_INFO;
}
*/
class CResetPasswdSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    private $byte;
    //以下为消息头字段定义
    private $id=MSG_P2A_RESET_PASSWD;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;

        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
            if (!$this->socket) {
                TRACE('phpsocket: CResetPasswdSocket: Create socket failed');
                return;
            }
            socket_connect($this->socket, UNIX_DOMAIN);
        }

        $this->byte=new Byte();
    }
    public function setMsgID($id)
    {
        $this->id = $id;
        return ;
    }
    public function copyUserInfo($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\ResetPasswd();
            if (strlen(WEB_DOMAIN) > 0) {
                $TempData->setWebIP(WEB_DOMAIN);//在此插入服务器的ip
            } else {
                $TempData->setWebIP(WEB_IP);//在此插入服务器的ip
            }
            $TempData->setUser($data[0]);
            if (strlen($data[1]) != 0) {
                $TempData->setEmail($data[1]);
            }
            $TempData->setToken($data[2]);
            $TempData->setRoleType($data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return 0;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        //return pack("N", $this->$id);
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }
    public function setMsgFrom($from)
    {
        $this->from = $from;
        return ;
    }
    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result = \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result=socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        }
        if (!$result) {
            return;
        }
    }
    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}

/**界面需要刷新设备配置信息文件时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  szUser -- 主账号
 *@param[IN]  nType -- 增删动作  csadapt::UPDATE_ADD_USER

typedef struct CSP2A_UPDATE_USER_T {
    CHAR szUser[USER_SIZE];
    int nType;
}CSP2A_UPDATE_USER;
*/
class CPersonnalUpdateUserSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    private $byte;
    //以下为消息头字段定义
    private $id=MSG_P2A_PERSONNAL_UPDATE_USER;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;

        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
            if (!$this->socket) {
                TRACE('phpsocket: CPersonnalUpdateUserSocket: Create socket failed');
                return;
            }
            socket_connect($this->socket, UNIX_DOMAIN);
        }

        $this->byte=new Byte();
    }
    public function copyUpdateUserInfo($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PersonnalUpdateUser();
            $TempData->setUser($data[0]);
            $TempData->setType($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return 0;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        //return pack("N", $this->$id);
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }
    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        }
        if (!$result) {
            TRACE('phpsocket: CPersonnalUpdateUserSocket: sendMsg failed');
            return;
        }
    }
    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}

/**界面更新联动单元下的设备配置信息时,通知后台
 *
 *@param[IN]  szNode -- 个人终端联动单元
typedef struct CSP2A_PERSONNAL_UPDATE_NODE_T {
    CHAR szNode[USER_SIZE];
}CSP2A_PERSONNAL_UPDATE_NODE;
*/
class CPersonnalUpdateNodeSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    private $byte;
    //以下为消息头字段定义
    private $id=MSG_P2A_PERSONNAL_UPDATE_NODE_DEV;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;

        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
            if (!$this->socket) {
                TRACE('phpsocket: CPersonnalUpdateNodeSocket: Create socket failed');
                return;
            }
            socket_connect($this->socket, UNIX_DOMAIN);
        }

        $this->byte=new Byte();
    }
    public function copyUpdateNodeInfo($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PersonnalUpdateNode();
            $TempData->setNode($data[0]);
            $TempData->setUpdateType($data[1]);	//4.6新增，更新类型，如配置文件5
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return 0;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        //return pack("N", $this->$id);
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }
    private function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        }
        if (!$result) {
            TRACE('phpsocket: CPersonnalUpdateNodeSocket: sendMsg failed');
            return;
        }
    }
    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}

//基类
class CSocket
{
    private $socket;
    private $port=8503;
    private $host='localhost';
    public $byte;
    //以下为消息头字段定义
    private $id;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }
    public function __construct()
    {
        $this->host='localhost';
        $this->port=8503;
        //不是kafka通道，走socket通道的才需要初始化socket连接相关
        if(!\notifyKafka::check()) {
            $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
            if (!$this->socket) {
                TRACE('phpsocket: CSocket: Create socket failed');
                return;
            }
            socket_connect($this->socket, UNIX_DOMAIN);
        }

        $this->byte=new Byte();
    }
    public function setMsgID($id)
    {
        $this->id = $id;
        return ;
    }
    public function setMsgFrom($from)
    {
        $this->from = $from;
        return ;
    }
    public function setMsgOEM($oem_type)
    {
        $this->param1 = $oem_type;
        return;
    }
    public function copy($data)
    { //须由子类重载
    }
    public function recvMsg()
    {
        $buf = socket_read($this->socket, strlen($this->byte->getByte()));
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    public function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    public function sendMsg()
    {
        if(\notifyKafka::check()) {
            $result= \notifyKafka::sendMsg($this->byte->getByte());
        } else {
            $result= socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        }
        if (!$result) {
            TRACE('phpsocket: CSocket: sendMsg failed--msgid'. $this->id);
            return;
        }
    }

    public function __desctruct()
    {
        //不是kafka通道，走socket通道的才需要销毁socket连接相关
        if(!\notifyKafka::check()) {
            socket_close($this->socket);
        }
    }
}
//个人终端用户界面上、app上处理告警的时候通知后台

//个人终端告警处理结构体
/*typedef struct CSP2A_PERSONNAL_DEAL_ALARM_T
{
#define ALARM_ID_SIZE		     16
#define ALARM_RESULT_SIZE		 64
    CHAR szAreaNode[AREA_NODE_SIZE]; //32
    CHAR szUser[USER_SIZE]; //32
    CHAR szAlarmID[ALARM_ID_SIZE];
    CHAR szResult[ALARM_RESULT_SIZE];
}CSP2A_PERSONNAL_DEAL_ALARM;
*/
class CAlarmDealSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PersonalAlarmDeal();
            $TempData->setAreaNode($data[0]);
            $TempData->setUser($data[1]);
            $TempData->setAlarmId($data[2]);
            $TempData->setResult($data[3]);
            $TempData->setMac($data[4]);
            $TempData->setAlarmTime($data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead(); //setMsgID在调用方已经赋值了
        $this->sendMsg();
        return;
    }
}

/**个人终端管理员,界面增加/修改设备时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 *@param[IN]  szMacOld -- 修改之前设备的MAC地址,只在修改设备时用

typedef struct CSP2A_PERSONAL_DEVICE_INFO_T {
    CHAR szMac[MAC_SIZE];
    CHAR szMacOld[MAC_SIZE];
}CSP2A_PERSONAL_DEVICE_INFO;
*/
class CPerAddDevSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerAddDev();
            $TempData->setMac($data[0]);
            $TempData->setMacOld($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
class CPerModDevSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerModDev();
            $TempData->setMacid($data[0]);
            $TempData->setIsPer($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,界面上删除设备
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 */
class CPerDelDevSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerDelDev();
            $TempData->setMac($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
/**个人终端用户,界面上删除联动系统账号
 *
 *@param[IN]  szUid -- 相关账号的uid
 */
class CPerDelUidSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerDelUid();
            $TempData->setUid($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,界面上删除图图片
 *
 *@param[IN]  pic_url -- 相关图片的url
 */
class CPerDelPicSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerDelPic();
            $TempData->setPicUrl($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,界面上删除图图片
 *
 *@param[IN]  pic_url -- 相关图片的url
 */
class CPerCreateUserSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerCreateUser();
            if (strlen($data[0]) != 0) {
                $TempData->setUser($data[0]);
            }
            $TempData->setPwd($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setEmail($data[2]);
            }
            $TempData->setQrcodeBody($data[3]);
            $TempData->setQrcodeUrl($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,界面上修改密码
 */
class CPerChangePwdSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerChangePwd();
            $TempData->setUser($data[0]);
            $TempData->setPwd($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setEmail($data[2]);
            }
            $TempData->setQrcodeBody($data[3]);
            $TempData->setQrcodeUrl($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
/**终端管理员新建要发送给联动系统的message
 */
class CPerNewMessageSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerNewMessage();
            if (strlen($data[0]) != 0) {
                $TempData->setData($data[0]);
            }
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**社区发送设备更新信息给联动系统
 */
class CCommunityUpdateDevNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityUpdateDevNotify();
            $TempData->setNode($data[0]);
            $TempData->setMac($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setMngAccountId($data[2]);
            }
            if (strlen($data[3]) != 0) {
                $TempData->setUnitId($data[3]);
            }
            if (strlen($data[4]) != 0) {
                $TempData->setUpdateDevType($data[4]);
            }
            $TempData->setUpdateType($data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**社区发送用户或单元更新、删除
 */
class CCommunityUpdateUserOrUnitSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityUpdateUserOrUnit();
            $TempData->setNode($data[0]);
            $TempData->setMngAccountId($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setUnitId($data[2]);
            }
            $TempData->setType($data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
/**社区修改主张信息 只有改名称和房间号时候通知
 *
 *@param[IN]  node -- 联动
 */
class CCommunitModifyMasterUserSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunitModifyMasterUser();
            $TempData->setData($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,发送校验码到邮件
 */
class CPerSendCheckCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerSendCheckCode();
            $TempData->setCheckCode($data[0]);
            if (strlen($data[1]) != 0) {
                $TempData->setEmail($data[1]);
            }
            $TempData->setLanguage($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**个人终端用户,发送校验码到邮件
 */
class CPerCleanDevCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerCleanDevCode();
            $TempData->setMacs($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/*
 *设备由过期到未过期的通知
 */
class CNotityDevExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDevExpire();
            $TempData->setUids($data[0]);
            $TempData->setMacs($data[1]);
            $TempData->setNode($data[2]);
            $TempData->setType($data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


/*
 *删除公共设备的账号时候调用
 */
class CNotityDelPerPublicDevVirtuaAccountSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDelPerPublicDevVirtuaAccount();
            $TempData->setAccount($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

//added by chenyc, 2018-08-27,for 视频存储
/*
 *用户实时增加视频存储计划
 */
class CNotityAddVideoSched extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityAddVideoSched();
            $TempData->setId($data[0]);
            $TempData->setSchedType($data[1]);
            $TempData->setDateFlag($data[2]);
            $TempData->setMac($data[3]);
            $TempData->setBeginTime($data[4]);
            $TempData->setEndTime($data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
/*
 *用户实时删除视频存储计划
 */
class CNotityDelVideoSched extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDelVideoSched();
            $TempData->setId($data[0]);
            $TempData->setSchedType($data[1]);
            $TempData->setMac($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/*
 *用户实时删除视频存储片段
 */
class CNotityDelVideo extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDelVideo();
            $TempData->setVideoId($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**社区公共设备卡变化
 */
class CCommunityUpdatePubKeyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityUpdatePubKeyNotify();
            $TempData->setMac($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**激活禁用账号
 */
class CAccountActiveNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\AccountActiveNotify();
            $TempData->setActive($data[0]);
            $TempData->setUserName($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setEmail($data[2]);
            }
            $TempData->setSubscription($data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**分享key
 */
class CShareTempKeyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\ShareTempKeyNotify();
            $TempData->setTmpKey((string)$data[0]);
            if (strlen($data[1]) != 0) {
                $TempData->setEmail((string)$data[1]);
            }
            $TempData->setMsg((string)$data[2]);	//计划类型
            $TempData->setCountEvery((string)$data[3]);	//string 周计划时不限次数，此处是周几
            $TempData->setStartTime((string)$data[4]);
            $TempData->setStopTime((string)$data[5]);
            $TempData->setQrcodeBody((string)$data[6]);
            $TempData->setLanguage((string)$data[7]);
            $TempData->setMngId((int)$data[8]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**创建物业
 */
class CCreatePropertyWorkNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CreatePropertyWorkNotify();
            if (strlen($data[0]) != 0) {
                $TempData->setEmail($data[0]);
            }
            $TempData->setUserName($data[1]);
            $TempData->setPassword($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}



/**更新所有公共设备
 */
class CUpdateCommnuityAllPubDevNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\UpdateCommnuityAllPubDevNotify();
            $TempData->setMngId($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}



class CDevRebootNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\DevRebootNotify();
            $TempData->setMac($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**更新设备配置
 */
class CDevConfigUpdateNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\DevConfigUpdateNotify();
            $TempData->setMac($data[0]);
            $TempData->setConfig($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


/**社区更新apt+pin的开门方式
 */
class CCommunityAptPinChangeNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityAptPinChangeNotify();
            $TempData->setMngId($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/**续费成功通知
 */
class CRenewServerNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\RenewServerNotify();
            $TempData->setUid($data[0]);	//uid_str拼接的
            $TempData->setType($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}



class CAlexaLoginNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\AlexaLoginNotify();
            $TempData->setNode($data[0]);	//uid_str拼接的
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CAlexaSetArmingNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\AlexaSetArmingNotify();
            $TempData->setMac($data[0]);
            $TempData->setMode($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CUpdateCommMonthlyFeeNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\UpdateCommMonthlyFee();
            $TempData->setAccount($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CCreateRemoteDevContorlNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CreateRemoteDevContorl();
            $TempData->setUser($data[0]);
            $TempData->setPassword($data[1]);
            $TempData->setPort($data[2]);
            $TempData->setMac($data[3]);
            $TempData->setSshProxyDomain($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CWebPersonalModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebPersonalModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setInstallerId((int) $data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebCommunityModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setCommunityId((int) $data[3]);
            $TempData->setUnitId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendSmsCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendSmsCode();
            $TempData->setType((int) $data[0]);
            $TempData->setCode((string) $data[1]);
            $TempData->setAreaCode((string) $data[2]);
            $TempData->setPhone((string) $data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CWebExportPmLogNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PmExportLog();

            $TempData->setTraceId((string) $data[0]);
            $TempData->setCommunitId((int) $data[1]);
            $TempData->setDatas((string) $data[2]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CWebAccessGroupModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\AccessGroupModifyNotify();

            $TempData->SetAccessGroupId($data[0]);
            $TempData->SetMacList($data[1]);
            $TempData->SetCommunityId($data[2]);
            $TempData->SetDelUserList($data[3]);

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CWebAccessGroupPerModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\AccessGroupPersonalModifyNotify();

            $TempData->SetAccessGroupId((int)$data[0]);
            $TempData->SetCommunityId((int)$data[1]);

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityPerModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityPersonalModifyNotify();

            $TempData->SetStaffIdList($data[0]);
            $TempData->SetDeliveryIdList($data[1]);
            $TempData->SetCommunityId((int)$data[2]);
            $TempData->SetAccessGroupIdList($data[3]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityAccountModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityAccountModifyNotify();

            $TempData->SetAccessGroupIdList($data[0]);
            $TempData->SetCommunityId((int)$data[1]);
            $TempData->SetAccountList($data[2]);
            $TempData->SetNodeList($data[3]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityImportAccountDataNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityImportAccountDataNotify();

            $TempData->SetCommunityId((int)$data[0]);
            $TempData->SetMacList($data[1]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CWebDataAnalysisNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\DataAnalysisNotify();

            $TempData->SetDatas($data[0]);
            $TempData->setProjectId($data[1]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CRegularAutopNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\RegularAutopNotify();

            $TempData->setMacList($data[0]);
            $TempData->setProjectId($data[1]);

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class COnceAutopNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OnceAutopNotify();

            $TempData->setMacList($data[0]);
            $TempData->setConfig($data[1]);
            $TempData->setProjectId($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CDelAppAccountNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\DelAppAccountNotify();

            $TempData->setAccount($data[0]);

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPmEmergencyDoorControlSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PmEmergencyDoorControlNotify();

            $TempData->setUuid($data[0]);
            $TempData->setType($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPmAccountActiveNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PmAccountActiveEmailNotify();
            $TempData->setActive($data[0]);
            $TempData->setAccount($data[1]);
            $TempData->setSubscription($data[2]);
            
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPerAddNewSiteSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PerAddNewSite();
            if (strlen($data[0]) != 0) {
                $TempData->setUserInfoUuid($data[0]);
            }
            $TempData->setProjectName($data[1]);
            if (strlen($data[2]) != 0) {
                $TempData->setEmail($data[2]);
            }
            if (strlen($data[3]) != 0) {
                $TempData->setAptNum($data[3]);
            }
            if (strlen($data[4]) != 0) {
                $TempData->setSendType($data[4]);
            }
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPmLinkNewSitesSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PmLinkNewSites();
            if (strlen($data[0]) != 0) {
                $TempData->setAccountUuid($data[0]);
            }
            $TempData->setCommNameList($data[1]);
            $TempData->setOfficeNameList($data[2]);
            if (strlen($data[3]) != 0) {
                $TempData->setEmail($data[3]);
            }
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendSMSVerficationCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendSMSVerficationCode();
            if (strlen($data[0]) != 0) {
                $TempData->setMobileNumber($data[0]);
            }
            $TempData->setPhoneCode($data[1]);
            $TempData->setLanguage($data[2]);
            $TempData->setCode($data[3]);
            $TempData->setType($data[4]);
            $TempData->setAccountUuid($data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendEmailVerficationCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendEmailVerficationCode();
            if (strlen($data[0]) != 0) {
                $TempData->setEmail($data[0]);
            }
            $TempData->setName($data[1]);
            $TempData->setLanguage($data[2]);
            $TempData->setCode($data[3]);
            $TempData->setType($data[4]);
            $TempData->setAccountUuid($data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPcapCaptureControlSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebPcapCaptureNotify();
            if (strlen($data[0]) != 0) {
                $TempData->setType($data[0]);
            }
            if (strlen($data[1]) != 0) {
                $TempData->setUuid($data[1]);
            }
            if (strlen($data[2]) != 0) {
                $TempData->setMac($data[2]);
            }

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendEmailNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendEmailNotifyMsg();
            $TempData->setKey($data[0]);
            $TempData->setPayload($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendMessageNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendMessageNotifyMsg();
            $TempData->setKey($data[0]);
            $TempData->setPayload($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * 开relay通知
 */
 class COpenDoorNotifySocket extends CSocket
 {
     public function copy($data)
     {
         if (is_string($data) || is_int($data) || is_float($data)) {
             $data[] = $data;
         }
         if (is_array($data)) {
             $TempData = new AK\Adapt\OpenDoorNotify();
             $TempData->setMac($data[0]);
             $TempData->setUid($data[1]);
             $TempData->setRelay($data[2]);
             $TempData->setRepost($data[3]);
             $PbData = $TempData->serializeToString();
             $this->byte->writeProtobuf($PbData);
         }
         $this->setMsgHead();
         $this->sendMsg();
         return;
     }
 }

 /* * 开security relay通知
 */
 class COpenSecurityRelayNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenSecurityRelayNotify();
            $TempData->setMac($data[0]);
            $TempData->setUid($data[1]);
            $TempData->setSecurityRelay((int)$data[2]);
            $TempData->setMsgTraceid($data[3]);
            $TempData->setRepost((int)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CKitAccountLogOffSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\KitAccountLogOff();
            $TempData->setNode($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSipPcapCaptureControlSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebSipPcapCaptureNotify();
            if (strlen($data[0]) != 0) {
                $TempData->setType($data[0]);
            }
            if (strlen($data[1]) != 0) {
                $TempData->setUuid($data[1]);
            }
            if (strlen($data[2]) != 0) {
                $TempData->setCallerSip($data[2]);
            }

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class COpenApiHealthChecklSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenApiSocketHealthCheck();
            if (strlen($data[0]) != 0) {
                $TempData->setMessage($data[0]);
            }

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class COpenDoorNotifyMsgSocket extends CSocket
{
    public function copy($data)
    {
        $cLog = \share\util\getLog();
        if (is_string($data) || is_int($data) || is_float($data)) {
            $cLog->TRACE("COpenDoorNotifyMsgSocket send data: " . $data);
            $data[] = $data;
        }
        if (is_array($data)) {
            $cLog->TRACE("COpenDoorNotifyMsgSocket send data: " . json_encode($data));
            $TempData = new AK\Adapt\OpenDoorNotifyMsg();
            $TempData->setUid(strval($data['uid']));
            $TempData->setMac(strval($data['mac']));
            $TempData->setRepostMac(strval($data['resport_mac']));
            $TempData->setProjectType($data['project_type']);
            $TempData->setRelayType($data['relay_type']);
            $TempData->setRelay($data['relay']);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CrequestDeviceCaptureNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\RequestDeviceCaptureNotifyMsg();
            $TempData->setMac($data[0]);
            $TempData->setSite($data[1]);
            $TempData->setUuid($data[2]);
            $TempData->setCamera($data[3]);
            $TempData->setProjectType($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}