<?php

namespace rule\src;

class Payment
{
    // 固定4位
    const PRE_CODE = '0011';
    // 错误码位数4+3
    const CODE = [
        'type' => self::PRE_CODE . '000',
    ];

    static public function type($param)
    {
        $paramArr = explode(',', $param);
        $commonType = array_values(PAYMENT_SEARCH_TYPE);
        $difference = array_diff($paramArr, $commonType);
        if (!is_array($paramArr) or empty($paramArr) or !empty($difference)) {
            return self::CODE['type'];
        }

        return true;
    }

    static public function communitytype($param)
    {
        $paramArr = explode(',', $param);
        $communityType = ["all","1","2","3","9","10","11","12","13","14","15","16"];
        $difference = array_diff($paramArr, $communityType);
        if (!is_array($paramArr) or empty($paramArr) or !empty($difference)) {
            return self::CODE['type'];
        }

        return true;
    }

    static public function singletype($param)
    {
        $paramArr = explode(',', $param);
        $singleType = ["all","1","2","3","4","9","10","11","14","15","16"];
        $difference = array_diff($paramArr, $singleType);
        if (!is_array($paramArr) or empty($paramArr) or !empty($difference)) {
            return self::CODE['type'];
        }

        return true;
    }

    static public function officeType($param)
    {
        $paramArr = explode(',', $param);
        $communityType = ["all","1","2","9","19","22","23","26","30"];
        $difference = array_diff($paramArr, $communityType);
        if (!is_array($paramArr) or empty($paramArr) or !empty($difference)) {
            return self::CODE['type'];
        }

        return true;
    }


}