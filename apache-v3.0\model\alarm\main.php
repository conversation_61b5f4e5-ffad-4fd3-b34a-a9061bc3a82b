<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";
class CAlarm
{
    private $alarmCode = [MSGTEXT["alarmDoorUnlock"],MSGTEXT["alarmInfrared"],MSGTE<PERSON>["drmagnet"],MSG<PERSON><PERSON>["alarmSmoke"],MSGTEXT["alarmGas"],MSG<PERSON>XT["alarmUrgency"],MSGTEXT["alarmSOS"],MSGTEXT["alarmTamper"]];
    private $alarmLocation = [MSGTEXT["alarmGate"],MSGTEXT["alarmDoor"],MSGTEXT["alarmBedroom"],<PERSON>G<PERSON><PERSON>["alarmGuestRoom"],<PERSON><PERSON><PERSON><PERSON>["alarmHall"],<PERSON><PERSON><PERSON><PERSON>["alarmWindow"],<PERSON>G<PERSON><PERSON>["alarmBalcony"],MSGTEXT["alarmKitchen"],MSGTEXT["alarmStudy"],MSGTEXT["alarmBathroom"]];
    private $alarmArea = [MSGTEXT["alarmArea"]."1",MSGTEXT["alarmArea"]."2",MSGTEXT["alarmArea"]."3",MSGTEXT["alarmArea"]."4",MSGTEXT["alarmArea"]."5",MSGTEXT["alarmArea"]."6",MSGTEXT["alarmArea"]."7",MSGTEXT["alarmArea"]."8"];
    /**
     * @msg: app列表
     */
    public function queryForApp()
    {
        $params = [
            "userAlias"=>"",
            "userAliasId"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        $this->log->actionLog("#model#alarm#param#timeZone=$timeZone;customizeForm=$customizeForm");
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID"=>$userId])[0]["Role"];
        $userType = in_array($role, PERROLE) ? 0 : 1;
        if ($userType == 0) {
            $data = $this->db->querySList('select A.ID,A.AlarmCode,A.AlarmCustomize,A.AlarmLocation,A.AlarmZone,A.AlarmType,A.AlarmTime,A.Status,A.DealResult,D.Location,A.AlarmCode,D.MAC from PersonalAlarms A left join PersonalDevices D on A.DevicesMAC = D.MAC where A.Node = :Node AND A.Status = 0 order by A.ID DESC', [":Node"=>$user]);
        } else {
            $data = $this->db->querySList('select A.ID,A.AlarmCode,A.AlarmCustomize,A.AlarmLocation,A.AlarmZone,A.AlarmType,A.AlarmTime,A.Status,A.DealResult,D.Location,A.AlarmCode,D.MAC from Alarms A left join Devices D on A.DevicesMAC = D.MAC where A.Node = :Node AND A.Status = 0 order by A.ID DESC', [":Node"=>$user]);
        }
    
        $len = (100-count($data));
        if ($len > 0) {
            if ($userType == 0) {
                $data2 = $this->db->querySList("select A.ID,A.AlarmCode,A.AlarmCustomize,A.AlarmLocation,A.AlarmZone,A.AlarmType,A.AlarmTime,A.Status,A.DealResult,D.Location,D.MAC from PersonalAlarms A left join PersonalDevices D on A.DevicesMAC = D.MAC where A.Node = :Node AND A.Status = 1 order by A.ID DESC limit 0,$len", [":Node"=>$user]);
            } else {
                $data2 = $this->db->querySList("select A.ID,A.AlarmCode,A.AlarmCustomize,A.AlarmLocation,A.AlarmZone,A.AlarmType,A.AlarmTime,A.Status,A.DealResult,D.Location,D.MAC from Alarms A left join Devices D on A.DevicesMAC = D.MAC where A.Node = :Node AND A.Status = 1 order by A.ID DESC limit 0,$len", [":Node"=>$user]);
            }
            $data = array_merge($data, $data2);
        }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$val) {
            $val["AlarmTime"] = \util\time\setYesterday(\util\time\setTime($val["AlarmTime"], $customizeForm), $timeZone);
            $alarmCode = intval($val["AlarmCode"]) - 1;
            $alarmLocation = $val["AlarmLocation"] == 0 ? intval($val["AlarmLocation"]) : intval($val["AlarmLocation"]) - 1;
            $alarmArea = $val["AlarmZone"]  == 0 ? intval($val["AlarmZone"]) : intval($val["AlarmZone"]) - 1;
            if (array_key_exists($alarmCode, $this->alarmCode)
            && array_key_exists($alarmLocation, $this->alarmLocation)
            && array_key_exists($alarmArea, $this->alarmArea)
            && $val["AlarmCustomize"] == 0) {
                if (in_array($alarmCode, [0,6,7])) {
                    $val["AlarmType"] = $this->alarmCode[$alarmCode];
                } else {
                    $val["AlarmType"] = $this->alarmArea[$alarmArea]." ".$this->alarmLocation[$alarmLocation]." ".$this->alarmCode[$alarmCode];
                }
            }
        }
        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @msg: web列表
     */
    public function queryForWeb()
    {
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "Status"=>"",
            "AlarmType"=>"",
            "BeginTime"=>"",
            "EndTime"=>"",
            "Location"=>"",
            "Device"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "userAlias"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $status = $params["Status"];
        $alarmType = $params["AlarmType"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $location = $params["Location"];
        $device = $params["Device"];
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID"=>$userId])[0]["Role"];
        $userType = in_array($role, PERROLE) ? 0 : 1;
        $deviceTabel = $userType == 0 ? "PersonalDevices" : "Devices";
        $alarmTabel = $userType == 0 ? "PersonalAlarms" : "Alarms";

        $where = "";
        $bindArray = [":Node"=>$user];
        if ($device) {
            $where = "$where AND D.ID = :ID";
            $bindArray[':ID'] = $device;
        }
        if ($status!=null) {
            $where = "$where AND P.Status = :Status";
            $bindArray[':Status'] = $status;
        }
        if ($beginTime) {
            $where = "$where AND P.AlarmTime > :BeginTime";
            //$beginTime = strtotime($beginTime);
            $bindArray[':BeginTime'] = \util\time\setTimeZone($beginTime, $timeZone, "", '-');
        }
        if ($endTime) {
            $where = "$where AND P.AlarmTime < :EndTime";
            //$endTime = strtotime($endTime);
            $bindArray[':EndTime'] = \util\time\setTimeZone($endTime, $timeZone, "", '-');
        }
        if ($alarmType || $location) {
            $where = "$where AND P.AlarmType like :AlarmType";
            $bindArray[':AlarmType'] = "%$location $alarmType%";
        }

        $total = count($this->db->querySList("select P.ID from $alarmTabel P left join $deviceTabel D on P.DevicesMAC = D.MAC where P.Node = :Node $where", $bindArray));
        $data = $this->db->querySList("select P.ID,P.AlarmCode,P.AlarmCustomize,P.AlarmLocation,P.AlarmZone,P.AlarmType,P.AlarmTime,P.Status,P.DealResult,P.DealTime,D.Location from $alarmTabel P left join $deviceTabel D on P.DevicesMAC = D.MAC where P.Node = :Node $where order by P.ID DESC limit $offset,$rows", $bindArray);
        $simtArray = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

        $rows = [];
        for ($i=0; $i<count($simtArray); $i++) {
            $curArray = array();
            $curArray['ID'] = $simtArray[$i]['ID'];
            $alarmCode = intval($simtArray[$i]["AlarmCode"]) - 1;
            $alarmLocation = $simtArray[$i]["AlarmLocation"] == 0 ? intval($simtArray[$i]["AlarmLocation"]) : intval($simtArray[$i]["AlarmLocation"]) - 1;
            $alarmArea = $simtArray[$i]["AlarmZone"]  == 0 ? intval($simtArray[$i]["AlarmLocation"]) : intval($simtArray[$i]["AlarmZone"]) - 1;
            if (array_key_exists($alarmCode, $this->alarmCode)
            && array_key_exists($alarmLocation, $this->alarmLocation)
            && array_key_exists($alarmArea, $this->alarmArea)
            && $simtArray[$i]["AlarmCustomize"] == 0) {
                if ($alarmCode == 0 || $alarmCode == 6 || $alarmCode == 7) {
                    $curArray["AlarmType"] = $this->alarmCode[$alarmCode];
                } else {
                    $curArray["AlarmType"] = $this->alarmArea[$alarmArea]." ".$this->alarmLocation[$alarmLocation]." ".$this->alarmCode[$alarmCode];
                }
            } else {
                $curArray['AlarmType'] = $simtArray[$i]['AlarmType'];
            }
            $curArray['Alarm Time'] = $simtArray[$i]['AlarmTime']?:'--';
            $curArray['Status'] = $simtArray[$i]['Status'];
            $curArray['Deal Result'] = $simtArray[$i]['DealResult']?:'--';
            $curArray['Deal Time'] = $simtArray[$i]['DealTime']?:'--';
            $curArray['Device'] = $simtArray[$i]['Location']?:'--';
            array_push($rows, $curArray);
        }

        \util\computed\setGAppData(["data"=>["detail"=>$data,"total"=>$total,"row"=>$rows]]);
    }

    /**
     * @msg: 处理alarm
     */
    public function deal()
    {
        $params = [
            "ID"=>"",
            "Result"=>"",
            // 主账户
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $result = $params["Result"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#alarm#deal#id=$id;result=$result;userId=$userId");
        $now = \util\computed\getNow();
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID"=>$userId])[0]["Role"];
        $userType = in_array($role, PERROLE) ? 0 : 1;
        $alarmTabel = $userType == 0 ? "PersonalAlarms" : "Alarms";
        $this->db->update2ListWID($alarmTabel, [":DealResult"=>$result,":DealTime"=>$now,":ID"=>$id,":Status"=>1]);
        // TODO 通知
    }

    /**
     * @msg: 未处理的alarm数量
     */
    public function queryUnDealNum()
    {
        $params = [
            "userAliasId"=>"",
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID"=>$userId])[0]["Role"];
        $userType = in_array($role, PERROLE) ? 0 : 1;
        $alarmTabel = $userType == 0 ? "PersonalAlarms" : "Alarms";
        $total = $this->db->querySList("select count(*) from $alarmTabel where Node = :Node AND Status = 0", [":Node"=>$user])[0]["count(*)"];
        \util\computed\setGAppData(["data"=>["total"=>$total]]);
    }
}
