<?php

namespace package\common\model\smartLockSL20\method;
trait Get
{
    public function getSL20Lock()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->sL20Lock->selectByArray($array, $fields);
    }

    public function getRelayInUse()
    {
        $params = ['DeviceUUID', 'MAC'];
        list($deviceUUID, $mac) = $this->getParams($params);

        $this->loadUtil('smartLock');
        $bindLockRelayList = $this->utils->smartLock->getThirdPartyLockDevice([['MAC', $mac]], "Relay");
        $relayInUse = [];

        $this->loadUtil('device');
        foreach (array_column($bindLockRelayList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }

        $this->loadUtil('iTec', true);
        $this->loadUtil('salto', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('ttLock', true);
        $this->loadUtil('smartLockSL20', true);
        $bindDormakabaList = $this->utils->_common->dormakaba->getDormakabaLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindSaltoList = $this->utils->_common->salto->getSaltoLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindITecList = $this->utils->_common->iTec->getITecLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindTtLockList = $this->utils->_common->ttLock->getTtLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindSL20List = $this->callSelfFunc('getSL20Lock', [[['DeviceUUID', $deviceUUID]], "Relay"]);
        foreach (array_column($bindDormakabaList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSaltoList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindITecList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindTtLockList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSL20List, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        return array_map('intval', $relayInUse);
    }
}

