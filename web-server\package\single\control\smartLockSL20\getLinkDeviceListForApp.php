<?php

namespace package\single\control\smartLockSL20;

use package\single\control\Executor;

class GetLinkDeviceListForApp extends \package\single\control\SingleCommonController
{
    public function exec()
    {
        $executor = new Executor();
        $executor->execMiddle('getOperaId', true);
        $executor->execModel('smartLockSL20.getLinkDeviceListForApp');
        $executor->execEcho(STATE_SUCCESS_QUERY, ['data']);
    }
}
