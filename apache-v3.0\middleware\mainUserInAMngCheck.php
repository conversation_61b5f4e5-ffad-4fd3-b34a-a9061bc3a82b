<?php
/*
 * @Description: 检测主账户是否在区域管理员下
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-17 15:01:21
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";

class CMainUserInAMngCheck implements IMiddleware {
    public $id;
    function handle (\Closure $next) {
        global $gApp,$cLog,$cMessage;
        $userId = $gApp["userAliasId"];
        $params = ["ID"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $db = \database\CDatabase::getInstance();
        $count = $db->querySList("select count(*) from PersonalAccount P 
        join Account A on A.ID = P.ParentID where P.Role in (".PERENDMROLE.",".COMENDMROLE.") and A.ParentID = :ParentID and ID = :ID",[":ParentID"=>$userId,":ID"=>$id])[0]["count(*)"];
        $cLog->actionLog("#middle#mainUserInAMngCheck#id=$id;mngId=$userId;haveUser=$count");
        if($count == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}