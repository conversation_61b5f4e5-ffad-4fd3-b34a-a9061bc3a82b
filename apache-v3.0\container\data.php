<?php
/*
 * @Description: 数据容器处理
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-09 13:53:22
 * @LastEditors  : kxl
 */
namespace container;
include_once __DIR__."/../util/string.php";
/**
 * @name: setData
 * @msg: 向数据容器添加数据
 * @param {type} 
 * @return: 数据容器
 */
function setData ($data,$containerName) {
    global $cLog;
    $initConValue = $containerName;
    if(is_array($containerName)) {
        $containerName = $containerName["name"];
    }

    $mapName = $containerName;
    $containerName = ($containerName == 'common') ? 
        'common' : (
            ($containerName == 'const') ? 
            'const' : 'map'
        );
    include_once __DIR__."/../dataContainer/$containerName.php";
    $className  = \util\string\getClassName($containerName);
    $container = "\\dataContainer\\$className";
    $container = new $container();
    // $cLog->actionLog("#container#setData#containerName=$containerName");
    if($containerName == 'const') {
        $data = $initConValue["data"];
        foreach($data as $key=>$value) $container->bind($key,$value);
    }else if($containerName == 'map') {
        include_once __DIR__."/../dataMap/$mapName.map.php";
        // $cLog->actionLog("#container#setData#MAP=".json_encode(\dataMap\MAP));
        $container->maps = \dataMap\MAP;
    }
    // foreach($data as $key=>$value) {
    //     $container->bind($key,$value);
    // }

    return $container;
}