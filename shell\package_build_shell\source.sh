#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-25
# Description   :   存放共享变量（配置项读取等）及函数定义
# Modifier      :
# ****************************************************************************
###################### 定义变量 ######################
PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

DOCKER_TAG=harbor.akcs.com:8443/web_backend/web_backend:latest          #docker镜像tag号
CONTAINER_NAME=web_backend      #启动容器名称

AKCS_SRC_ROOT=$PROJECT_PATH
AKCS_SRC_WEB=${AKCS_SRC_ROOT}

#task
AKCS_SRC_WEB_TASK_DIR=${AKCS_SRC_WEB}/async-task
AKCS_SRC_WEB_TASK_DIST_DIR=${AKCS_SRC_WEB}/async-task/async-task
AKCS_SRC_WEB_TASK_SCRIPT_DIR=${AKCS_SRC_WEB}/async-task/script

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=package
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}
AKCS_PACKAGE_ROOT_WEBROOT=${AKCS_PACKAGE_ROOT}/webroot
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/web_scripts
AKCS_PACKAGE_ROOT_TASK=${AKCS_PACKAGE_ROOT}/async-task
AKCS_PACKAGE_ROOT_OPENAPI=$AKCS_PACKAGE_ROOT/openapi
AKCS_PACKAGE_ROOT_OPENAPI_SCRIPT=$AKCS_PACKAGE_ROOT/openapi_scripts

#download
AKCS_SRC_DOWNLOAD=${AKCS_SRC_WEB}/download


###################### 定义函数 ######################
clean() {
    echo "【清理上次安装包】"
    if [ -d $AKCS_PACKAGE_ROOT ]; then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    mkdir -p $AKCS_PACKAGE_ROOT_TASK
    mkdir -p $AKCS_PACKAGE_ROOT_OPENAPI_SCRIPT
    chmod -R 777 $AKCS_PACKAGE_ROOT/*
}

check_articles(){
    IMAGE_NAME=registry.cn-hangzhou.aliyuncs.com/ak_system/php:5-fpm
    echo "【检测词条】"
    docker pull $IMAGE_NAME
    timestamp=$(date +%s)
    cname="check_articles_${timestamp}"
    if [ `docker ps -a | grep $cname | wc -l` -gt 0 ];then docker rm $cname;fi
    docker run --name $cname -v $PROJECT_PATH:/var/www/html $IMAGE_NAME bash -c "/bin/sh /var/www/html/shell/check_articles.sh"
    docker stop $cname
    docker rm $cname
}

integrate_package() {
    echo "【整合多项目，合并到package目录，准备代码同步】"

    cd $AKCS_SRC_WEB
    cp -rf apache-v3.0 $AKCS_PACKAGE_ROOT_WEBROOT/
    cp -rf web-server $AKCS_PACKAGE_ROOT_WEBROOT/

	# web-server 打包WEB
	bash $AKCS_SRC_WEB/web-server/script/build.sh web
	mv $AKCS_SRC_WEB/web-server/dist $AKCS_PACKAGE_ROOT_WEBROOT/dist
	if [ -d $AKCS_PACKAGE_ROOT_WEBROOT/web-server ];then rm -rf $AKCS_PACKAGE_ROOT_WEBROOT/web-server; fi
	mv $AKCS_PACKAGE_ROOT_WEBROOT/dist $AKCS_PACKAGE_ROOT_WEBROOT/web-server
    if [ -d $AKCS_PACKAGE_ROOT_WEBROOT/web-server/test ];then rm -R $AKCS_PACKAGE_ROOT_WEBROOT/web-server/test; fi
	if [ -d $AKCS_PACKAGE_ROOT_WEBROOT/apache-v3.0/test ];then rm -R $AKCS_PACKAGE_ROOT_WEBROOT/apache-v3.0/test; fi

    #拷贝func, 后台一份代码，但是前端有多个地方引用
	cp -rf $AKCS_PACKAGE_ROOT_WEBROOT/apache-v3.0/notify $AKCS_PACKAGE_ROOT_WEBROOT/web-server/share/
    cp -rf $AKCS_SRC_WEB/shell/change_web_conf_by_etcd.php $AKCS_PACKAGE_ROOT_SCRIPTS/
}

