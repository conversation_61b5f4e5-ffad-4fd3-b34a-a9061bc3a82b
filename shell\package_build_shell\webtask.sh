#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-25
# Description   :   webtask 构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

AKCS_SRC_ROOT=$PROJECT_PATH
source "$AKCS_SRC_ROOT"/shell/package_build_shell/source.sh

###################### 开始编译 ######################
echo "【开始编译项目】"
mkdir -p $SRC_PATH
if [[ -n $(docker ps -q -f "name=${CONTAINER_NAME}") ]];then
    echo "已启动容器：$CONTAINER_NAME"
elif [[ -n $(docker ps -aq -f "name=${CONTAINER_NAME}") ]];then
    echo "容器已停止，正在重新启动容器中"
    docker restart $CONTAINER_NAME
else
    echo "未启动容器，正在启动中"
    docker run --name $CONTAINER_NAME -v $PROJECT_PATH:/opt/jenkins/workspace $DOCKER_TAG
fi

docker exec $CONTAINER_NAME sh -c "cd $PROJECT_PATH/async-task && npm install --registry=https://registry.npmmirror.com/ && npm run build"

###################### 整合代码，准备代码同步 ######################
if [ ! -d $AKCS_SRC_WEB_TASK_DIST_DIR ]; then mkdir -p $AKCS_SRC_WEB_TASK_DIST_DIR; fi
if [ ! -d $AKCS_SRC_WEB_TASK_SCRIPT_DIR ]; then mkdir -p $AKCS_SRC_WEB_TASK_SCRIPT_DIR; fi
cp -rf $AKCS_SRC_WEB_TASK_DIST_DIR $AKCS_PACKAGE_ROOT
cp -rf $AKCS_SRC_WEB_TASK_SCRIPT_DIR $AKCS_PACKAGE_ROOT_TASK