<?php
namespace plan\process;

const STAFF_PROCESS = [
    "getStaffList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"staff.queryStaff"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getDeliveryList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"staff.queryDelivery"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getStaffInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"staff.staffInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getDeliveryInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"staff.deliveryInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addDelivery"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"staff.deliveryAdd"
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "addStaff"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"staff.staffAdd"
        ],[
            "type"=>"model",
            "model"=>"photo.addStaffFace"
        ],[
            "type" => "model",
            "model" => "notify.addStaffFace",
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "editDelivery"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"staff.deliveryEdit"
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],

    "editStaff"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"staff.staffEdit"
        ],[
            "type"=>"model",
            "model"=>"photo.editStaffFace"
        ],[
            "type" => "model",
            "model" => "notify.editStaffFace",
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],

    "deleteDelivery"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"staff.deliveryDelete"
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],

    "deleteStaff"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type" => "database",
            "method" => "begin"
        ],[
            "type"=>"model",
            "model"=>"photo.deleteStaffFace"
        ],[
            "type"=>"model",
            "model"=>"staff.staffDelete"
        ],[
            "type" => "model",
            "model" => "notify.deleteStaffFace",
        ],[
            "type" => "database",
            "method" => "commit"
        ],[
            "type"=>"model",
            "model"=>"notify.changeStaffWithAccess"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
]; 