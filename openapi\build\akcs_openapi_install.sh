#!/bin/sh

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${WORK_DIR}/..
chmod 777 -R ${PAKCAGES_ROOT}/*
INSTALL_CONF=/etc/openapi_install.conf
HOST_IP=/etc/ip

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}
CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null; 
    #IP地址必须为全数字 
    if [ $? -ne 0 ] 
    then 
        return 1 
    fi 
    ipaddr=$1 
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值 
    b=`echo $ipaddr|awk -F . '{print $2}'` 
    c=`echo $ipaddr|awk -F . '{print $3}'` 
    d=`echo $ipaddr|awk -F . '{print $4}'` 
    for num in $a $b $c $d 
    do 
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间 
        then 
            return 1 
        fi 
    done 
    return 0 
} 
EchoHostIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
    
    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6; 
        
    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}

EnterBasicSrvIPAddr()
{
    yellow "Enter your fdfs server inner IPV4: \c"
    read FDFS_INNER_IP;      
 
    #输入csgate网关编码
    yellow "Enter your csgate no: \c"
    read GATEWAY_NUM;

    #输入csgate网关扩展编号
    yellow "Enter your expand server number.exclude csgate no.<eg:11,22>: \c"
    read EXPAND_GATEWAY_NUM;

    #输入csvs外网ip
    yellow "Enter your csvs(video storage) server outer IPV4: \c"
    read CSVSIP;

    #输入本机域名
    yellow "Enter the host's domain name: \c"
    read SERVER_DOMAIN;  

    #输入pbx ip
    yellow "Enter the pbx outer IPV4: \c"
    read PBXIP;  
    
    yellow "Enter the pbx outer IPV6: \c"
    read PBXIPV6;  
    
    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;    

    #输入网关的域名
    yellow "Enter your gate server domain name : \c"
    read GATE_DOMAIN_NAME;
	
	#输入网关的IP
    yellow "Enter your gate server ip: \c"
    read GATE_IP;
	
    #输入远程设备访问的域名
    yellow "Enter your remote devices config server domain name : \c"
    read REMOTE_CONIFG_DOMAIN_NAME;

    #计费系统
    yellow "Enter your BM Server domain name : \c"
    read BM_DOMAIN;
	
	#计费系统
    yellow "Enter your BM Server IP : \c"
    read BM_IP;
	
	yellow "Enter your Server List IP <eg:************,*************>: \c"
    read SERVER_LIST;
	
	#选择不同地区网页做不同处理
    blue "Area list:"
    blue "   1)ccloud"
    blue "   2)scloud"
	blue "   3)ecloud"
	blue "   4)ucloud"
	blue "   5)other"
    yellow "Enter your choice no: \c"
    read SYSTEM_AREA;
    
    for ip in $FDFS_INNER_IP $PBXIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "GATEWAY_NUM=$GATEWAY_NUM" >>$INSTALL_CONF
	echo "EXPAND_GATEWAY_NUM=$EXPAND_GATEWAY_NUM" >>$INSTALL_CONF
    echo "CSVSIP=$CSVSIP" >>$INSTALL_CONF
    echo "SERVER_DOMAIN=$SERVER_DOMAIN" >>$INSTALL_CONF
    echo "PBXIP=$PBXIP" >>$INSTALL_CONF
    echo "PBXIPV6=$PBXIPV6" >>$INSTALL_CONF  
    echo "FDFS_INNER_IP=$FDFS_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
    echo "GATE_DOMAIN_NAME=$GATE_DOMAIN_NAME" >>$INSTALL_CONF
	echo "GATE_IP=$GATE_IP" >>$INSTALL_CONF
	echo "REMOTE_CONIFG_DOMAIN_NAME=$REMOTE_CONIFG_DOMAIN_NAME" >>$INSTALL_CONF
	echo "SYSTEM_AREA=$SYSTEM_AREA" >>$INSTALL_CONF
	echo "BM_DOMAIN=$BM_DOMAIN" >>$INSTALL_CONF
	echo "BM_IP=$BM_IP" >>$INSTALL_CONF
	echo "SERVER_LIST=$SERVER_LIST" >>$INSTALL_CONF
}
function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0
	
	fi
}

if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    
    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi
EchoBasicSrvIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    gate_no_str="GATEWAY_NUM="
    gate_no_cat=`cat $INSTALL_CONF | grep -w GATEWAY_NUM | awk -F'=' '{ print $2 }'`
    gate_no=$gate_no_str$gate_no_cat
    echo $gate_no
	
    gate_no_str="EXPAND_GATEWAY_NUM="
    gate_no_cat=`cat $INSTALL_CONF | grep -w EXPAND_GATEWAY_NUM | awk -F'=' '{ print $2 }'`
    gate_no=$gate_no_str$gate_no_cat
    echo $gate_no	

    csvs_outer_ip_str="CSVSIP="
    csvs_outer_ip_cat=`cat $INSTALL_CONF | grep -w CSVSIP | awk -F'=' '{ print $2 }'`
    csvs_outer_ip=$csvs_outer_ip_str$csvs_outer_ip_cat
    echo $csvs_outer_ip

    server_domain_str="SERVER_DOMAIN="
    server_domain_cat=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    server_domain=$server_domain_str$server_domain_cat
    echo $server_domain
    
    pbx_outer_ipv4_str="PBXIP="
    pbx_outer_ipv4_cat=`cat $INSTALL_CONF | grep -w PBXIP | awk -F'=' '{ print $2 }'`
    pbx_outer_ipv4=$pbx_outer_ipv4_str$pbx_outer_ipv4_cat
    echo $pbx_outer_ipv4
    
    pbx_outer_ipv6_str="PBXIPV6="
    pbx_outer_ipv6_cat=`cat $INSTALL_CONF | grep -w PBXIPV6 | awk -F'=' '{ print $2 }'`
    pbx_outer_ipv6=$pbx_outer_ipv6_str$pbx_outer_ipv6_cat
    echo $pbx_outer_ipv6
    
    fdfs_inner_ip_str="FDFS_INNER_IP="
    fdfs_inner_ip_cat=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    fdfs_inner_ip=$fdfs_inner_ip_str$fdfs_inner_ip_cat
    echo $fdfs_inner_ip

    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    redis_inner_ip_str="REDIS_INNER_IP="
    redis_inner_ip_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    redis_inner_ip=$redis_inner_ip_str$redis_inner_ip_cat
    echo $redis_inner_ip      

    gate_domain_str="GATE_DOMAIN_NAME="
    gate_domain_cat=`cat $INSTALL_CONF | grep -w GATE_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
    gate_domain=$gate_domain_str$gate_domain_cat
    echo $gate_domain   
	
	gate_ip_str="GATE_IP="
    gate_ip_cat=`cat $INSTALL_CONF | grep -w GATE_IP | awk -F'=' '{ print $2 }'`
    gate_ip=$gate_ip_str$gate_ip_cat
    echo $gate_ip  

    remote_domain_str="REMOTE_CONIFG_DOMAIN_NAME="
    remote_domain_cat=`cat $INSTALL_CONF | grep -w REMOTE_CONIFG_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
    remote_domain=$remote_domain_str$remote_domain_cat
    echo $remote_domain  	
	
	system_area_str="SYSTEM_AREA="
    system_area_cat=`cat $INSTALL_CONF | grep -w SYSTEM_AREA | awk -F'=' '{ print $2 }'`
    system_area=$system_area_str$system_area_cat
    echo $system_area 
	
	tmp_str="BM_DOMAIN="
    tmp_cat=`cat $INSTALL_CONF | grep -w BM_DOMAIN | awk -F'=' '{ print $2 }'`
    tmp_echo=$tmp_str$tmp_cat
    echo $tmp_echo 	
	
	tmp_str="BM_IP="
    tmp_cat=`cat $INSTALL_CONF | grep -w BM_IP | awk -F'=' '{ print $2 }'`
    tmp_echo=$tmp_str$tmp_cat
    echo $tmp_echo 	
	
	tmp_str="SERVER_LIST="
    tmp_cat=`cat $INSTALL_CONF | grep -w SERVER_LIST | awk -F'=' '{ print $2 }'`
    tmp_echo=$tmp_str$tmp_cat
    echo $tmp_echo 
}
if [ -f $INSTALL_CONF ];then
    echo -e "\033[34m$1\033[0m"
    EchoBasicSrvIPAddr
    GATEWAY_NUM=`cat $INSTALL_CONF | grep -w GATEWAY_NUM | awk -F'=' '{ print $2 }'`
	EXPAND_GATEWAY_NUM=`cat $INSTALL_CONF | grep -w EXPAND_GATEWAY_NUM | awk -F'=' '{ print $2 }'`
    CSVSIP=`cat $INSTALL_CONF | grep -w CSVSIP | awk -F'=' '{ print $2 }'`
    SERVER_DOMAIN=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    PBXIP=`cat $INSTALL_CONF | grep -w PBXIP | awk -F'=' '{ print $2 }'`
    PBXIPV6=`cat $INSTALL_CONF | grep -w PBXIPV6 | awk -F'=' '{ print $2 }'`
    FDFS_INNER_IP=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    GATE_DOMAIN_NAME=`cat $INSTALL_CONF | grep -w GATE_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
	GATE_IP=`cat $INSTALL_CONF | grep -w GATE_IP | awk -F'=' '{ print $2 }'`
	REMOTE_CONIFG_DOMAIN_NAME=`cat $INSTALL_CONF | grep -w REMOTE_CONIFG_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
	SYSTEM_AREA=`cat $INSTALL_CONF | grep -w SYSTEM_AREA | awk -F'=' '{ print $2 }'`
	BM_DOMAIN=`cat $INSTALL_CONF | grep -w BM_DOMAIN | awk -F'=' '{ print $2 }'`
	SERVER_LIST=`cat $INSTALL_CONF | grep -w SERVER_LIST | awk -F'=' '{ print $2 }'`
	BM_IP=`cat $INSTALL_CONF | grep -w BM_IP | awk -F'=' '{ print $2 }'`
	if [ -z $BM_IP ];then
		echo "BM_IP is null!"
		exit 1
	fi
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/openapi_install.conf>, please enter all information below:" 
    EnterBasicSrvIPAddr
fi

PBXIPV6_REPLACE_LINE="const G_PBX_IPV6 = \"[${PBXIPV6}]:5070\";"
sed -i "s/^.*G_PBX_IPV6.*/${PBXIPV6_REPLACE_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/define.php
PBX_REPLACE_LINE="const G_PBX_IPV4 = \"${PBXIP}:5070\";"
sed -i "s/^.*G_PBX_IPV4.*/${PBX_REPLACE_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/define.php

#redis的内网ip就是本机内网ip
REDIS_INNER_IP_REPLACE_LINE="const REDISIP = \"${SERVER_INNER_IP}\";"
sed -i "s/^.*const REDISIP.*/${REDIS_INNER_IP_REPLACE_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*const REDISIP.*/${REDIS_INNER_IP_REPLACE_LINE}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php

sed -i "s/^.*const HTTPSERVER.*/const HTTPSERVER = 'http:\/\/${SERVER_DOMAIN}';/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*const G_RTSP_IPV4.*/const G_RTSP_IPV4 = '${SERVERIP}:554';/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*const G_RTSP_IPV6.*/const G_RTSP_IPV6 = '${SERVERIPV6}:554';/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*IPV4IMG.*/const IPV4IMG = 'https:\/\/${SERVER_DOMAIN}:8091';/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
sed -i "s/^.*IPV4TMPQR.*/const IPV4TMPQR = 'http:\/\/${SERVERIP}';/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
sed -i "s/^.*SERVERHOST.*/const SERVERHOST = 'https:\/\/${SERVER_DOMAIN}';/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
sed -i "s/^.*SERVERURL.*/const SERVERURL = '${GATE_IP}:9999';/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php

if [ $SYSTEM_AREA -eq 1 ];then
	sed -i "s/^.*const CHINA_CLOUD.*/const CHINA_CLOUD = 1;/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/define.php
	sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"cn\";/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
elif [ $SYSTEM_AREA -eq 2 ];then
	sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"as\";/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
elif [ $SYSTEM_AREA -eq 3 ];then
	sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"eu\";/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
elif [ $SYSTEM_AREA -eq 4 ];then
	sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"na\";/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
else
	sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"na\";/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php	
fi

sed -i "s/^.*const SERVER_LIST.*/const SERVER_LIST='${SERVER_LIST}';/g" ${PAKCAGES_ROOT}/openapi/common/define.php

#bm
BMURL="const BMURL = 'https:\/\/${BM_IP}\/bmserver\/';"
sed -i "s/^.*const BMURL.*/${BMURL}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php

#后于之前替换的代码,保证db redis配置不被覆盖
bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF 
ENABLE_DBPROXY=`cat $INSTALL_CONF | grep -w ENABLE_DBPROXY | awk -F'=' '{ print $2 }'`
if [ $ENABLE_DBPROXY -eq 1 ];then
	#slim api 落在主库，这部分数据库实现方式和网页不一样，没有单独的类处理，一次请求会有多个数据连接
    DBPROXY_LINE="const DATABASEIP = \"$MYSQL_INNER_IP\";"
	sed -i "s/^.*const DATABASEIP.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
    DBPROXY_LINE="\$mysql_conn_string = \"mysql:host=$MYSQL_INNER_IP;port=3306;dbname=AKCS\";"
	sed -i "s/^.*dbname=AKCS.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/comm.php 

else
    DBPROXY_LINE="const DATABASEIP = \"$MYSQL_INNER_IP\";"
	sed -i "s/^.*const DATABASEIP.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
    DBPROXY_LINE="\$mysql_conn_string = \"mysql:host=$MYSQL_INNER_IP;port=3306;dbname=AKCS\";"
	sed -i "s/^.*dbname=AKCS.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/comm.php
fi

bash $WORK_DIR/redis-install-for-system.sh $INSTALL_CONF
ENABLE_SENTINEL=`cat $INSTALL_CONF | grep -w ENABLE_REDIS_SENTINEL | awk -F'=' '{ print $2 }'`
ENABLE_REDIS_SENTINEL_LINE="const ENABLE_REDIS_SENTINEL = \"$ENABLE_SENTINEL\";"
SENTINEL_HOSTS=`cat $INSTALL_CONF | grep -w SENTINEL_HOSTS | awk -F'=' '{ print $2 }'`
REDIS_SENTINEL_LINE="const REDIS_SENTINEL_HOSTS = \"$SENTINEL_HOSTS\";"
sed -i "s/^.*ENABLE_REDIS_SENTINEL.*/${ENABLE_REDIS_SENTINEL_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*REDIS_SENTINEL_HOSTS.*/${REDIS_SENTINEL_LINE}/g" ${PAKCAGES_ROOT}/openapi/common/define.php
sed -i "s/^.*ENABLE_REDIS_SENTINEL.*/${ENABLE_REDIS_SENTINEL_LINE}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
sed -i "s/^.*REDIS_SENTINEL_HOSTS.*/${REDIS_SENTINEL_LINE}/g" ${PAKCAGES_ROOT}/openapi/PHPFramework/config/base.php
 
mkdir -p /var/www/html-api/
rm -rf /var/www/html-api/openapi
cp -rf ${PAKCAGES_ROOT}/openapi /var/www/html-api/



#以下为web端的日志
if [ ! -d /var/log/php/ ]
then
    mkdir -p /var/log/php
fi

touch /var/log/php/openapi.log  >/dev/null 2>&1
chown nobody:nogroup /var/log/php/openapi.log

echo "akcs web install completed ..."
