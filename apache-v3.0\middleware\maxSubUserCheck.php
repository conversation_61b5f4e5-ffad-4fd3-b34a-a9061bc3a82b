<?php
/*
 * @Description: 最大从账户个数验证
 * @version:
 * @Author: kxl
 * @Date: 2020-01-12 17:52:34
 * @LastEditors  : kxl
 */
namespace middleware;

include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../database/main.php";

class CMaxSubUserCheck implements IMiddleware
{
    public function handle(\Closure $next)
    {
        global $gApp,$cMessage;
        global $cLog;
        $userId = $gApp["userId"];
        $db = \database\CDatabase::getInstance();
        $data = $db->querySList("select ID from PersonalAccount where Role in (".PERENDSROLE.",".COMENDSROLE.") and ParentID = :ParentID", [":ParentID"=>$userId]);
        $subUsers = count($data);
        $sysMaxSubCount = $db->queryAllList('SystemExtremum')[0]["MaxApps"] - 1;
        $cLog->actionLog("#middle#maxSubUserCheck#subUser=$subUsers;MaxUsers=$sysMaxSubCount");
        // 系统最大值检测
        if ($subUsers >= $sysMaxSubCount) {
            $cMessage->echoErrorMsg(StateFamilyMemberCannotCreate);
        }
        $next();
    }
}
