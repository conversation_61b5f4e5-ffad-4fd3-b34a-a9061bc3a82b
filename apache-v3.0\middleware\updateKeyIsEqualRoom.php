<?php
/*
 * @Description: 修改时检测key是否和RoomName
 * @version:
 * @Author: zyc
 * @Date: 2021-02-24 09:54:00
 * @LastEditors  : zyc
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";

class CUpdateKeyIsEqualRoom implements IMiddleware{
    public function handle(\Closure $next) {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Key"=>"",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $ID=$params["ID"];
        $userAliasId=$params["userAliasId"];
        $key=$params["Key"];
        $db = \database\CDatabase::getInstance();
        $Code = $db->querySList("SELECT Code FROM PubPrivateKey where ID = :ID",[":ID"=>$ID])[0]["Code"];
        if ($Code == $key) {
            $next();
            return;
        }
        $count = $db->querySList("SELECT COUNT(*) FROM CommunityUnit A JOIN CommunityRoom B ON A.ID = B.UnitID WHERE A.MngAccountID = :userAliasId AND B.RoomName = :RoomName",[":userAliasId"=>$userAliasId,":RoomName"=>$key])[0]["COUNT(*)"];
        if ($count!=0) $cMessage->echoErrorMsg(StatePmKeyIsEqualRoom);
        $next();
    }
}