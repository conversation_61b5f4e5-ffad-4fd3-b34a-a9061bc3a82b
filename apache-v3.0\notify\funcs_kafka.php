<?php
/**
 * @description: notifyKafka
 * @author: csc 2022/12/12 18:19 V6.5.2
 * @lastEditor cj 2024/05/24 14:48 V6.8.0
 */
class notifyKafka
{
    //topic
    public static $topicName = 'notify_app_backend';

    //是否开启kafka
    public static $openKafka = true;

    /**
     * @description: 发送消息
     * @param $data
     * @param $topic
     * @return void
     * @lastEditors: csc 2022/12/13 10:26 V6.5.2
     * @author: csc 2022/12/13 10:26 V6.5.2
     */
    public static function sendMsg($data, $topicName = '', $retry = true) {
        try {
            $checkRes = self::check();
            if (false === $checkRes) {
                return false;
            }
            $producer = self::createProducer();
            $topicName = empty($topicName) ? self::$topicName : $topicName;
            $topic = $producer->newTopic($topicName);
            global $cLog;
            if (strlen($data) >= 1024000) {
                $cLog->TRACE('cancel send to kafka, data size more than 1024000');
                $cLog->errorLog('cancel send to kafka, data size more than 1024000');
                return false;
            }
            $cLog->TRACE(date('Y-m-d H:i:s').',ready to send to kafka, topic' . $topicName . ',data:' . print_r($data,true));
            //RD_KAFKA_PARTITION_UA 不指定分区 自动分配
            $topic->produce(RD_KAFKA_PARTITION_UA, 0, $data);
            //非阻塞调用
            $producer->poll(0);
            //设置超时10s，如果未成功，消息可能投递失败，也可以设置为-1，无限等待。
            $result = $producer->flush(10000);
            if (RD_KAFKA_RESP_ERR_NO_ERROR !== $result) {
                global $cLog;
                $cLog->TRACE("notifyKafka Was unable to flush, messages might be lost! data:".print_r($data, true) . ', topic:' . $topicName);
                $cLog->errorLog("notifyKafka Was unable to flush, messages might be lost! data:".print_r($data, true) . ', topic:' . $topicName);
                return false;
            }
            $cLog->TRACE(date('Y-m-d H:i:s').',send to kafka success, topic' . $topicName . ',data:' . print_r($data,true));
            return true;
        } catch (\Exception $e) {
            if ($retry) {
                global $cLog;
                $cLog->TRACE("notifyKafka sendMsg error, ready to retry, data:".print_r($data, true) . ', topic:' . $topicName . ' error:' . $e->getMessage());
                return self::sendMsg($data, $topicName, false);
            } else {
                global $cLog;
                $cLog->TRACE("notifyKafka sendMsg error, data:".print_r($data, true) . ', topic:' . $topicName . ' error:' . $e->getMessage());
                $cLog->errorLog("notifyKafka sendMsg error, data:".print_r($data, true) . ', topic:' . $topicName . ' error:' . $e->getMessage());
                return false;
            }
        }
    }

    /**
     * @description:检测是否kafka可发送
     * @return bool
     * @lastEditors: csc 2022/12/13 11:05 V6.5.2
     * @author: csc 2022/12/13 11:05 V6.5.2
     */
    public static function check()
    {
        if (defined("FORCE_OPEN_KAFKA") && FORCE_OPEN_KAFKA){
            return true;
        }
        if ('workman' !== WORK_FRAME or !self::$openKafka) {
            return false;
        }
        return true;
    }

    /**+
     * @description: 创建producer
     * @return \RdKafka\Producer
     * @lastEditors: csc 2022/12/13 10:25 V6.5.2
     * @author: csc 2022/12/13 10:25 V6.5.2
     */
    private static function createProducer() {
        $conf = new \RdKafka\Conf();
        $conf->set('metadata.broker.list', KAFKA_BROKER_LIST);
        $producer = new RdKafka\Producer($conf);
        return $producer;
    }
}

//基类
class CKafka
{
    public $byte;
    //以下为消息头字段定义
    private $id;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }

    public function __construct()
    {
        $this->byte=new Byte();
    }

    public function setMsgID($id)
    {
        $this->id = $id;
        return ;
    }
    public function setMsgFrom($from)
    {
        $this->from = $from;
        return ;
    }
    public function copy($data)
    { //须由子类重载
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    public function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    public function sendMsg()
    {
        $result= \notifyKafka::sendMsg($this->byte->getByte());
        if (!$result) {
            TRACE('phpsocket: CSocket: sendMsg failed--msgid'. $this->id);
            return;
        }
    }
}

/**
 * @description: 数据分析
 * @author: csc 2023/1/31 13:54 V6.6
 * @lastEditors: csc 2023/1/31 13:54 V6.6
 */
class CWebDataAnalysisNotifyKafka extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\DataAnalysisNotify();

            $TempData->SetDatas($data[0]);
            $TempData->setProjectId($data[1]);

            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}