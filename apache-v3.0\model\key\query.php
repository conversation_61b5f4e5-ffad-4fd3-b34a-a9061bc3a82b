<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-20 15:40:20
 * @LastEditors: cj
 */
namespace model\key;

include_once __DIR__."/../../util/arr.php";

trait query
{
    public function queryCom($account, $manage=false, $build=false)
    {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = '';
        $bindArray = [":Node"=>$account];
        switch ($serchKey) {
            case 'Code':
                $where=" AND A.Code like :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'Key':
                $where=" AND A.TmpKey like :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
                break;
            case 'User':
                $where=" AND B.Name like :serchValue";
                $bindArray[":serchValue"] = "%$serchValue%";
        }
        $tabel = $this->tabelName[$this->type];
        if ($manage === false) {
            if ($this->type == 0) {
                $sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account where A.Node = :Node $where";
            } else {
                $sql = "select  A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node where A.Node = :Node $where";
            }
        } else {
            if ($build == false) {
                if ($this->type == 0) {
                    $sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account left join Account C on C.ID = B.ParentID where C.ID = :Node $where";
                } else {
                    $sql = "select  A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node left join Account D on D.ID = C.ParentID where D.ID = :Node $where";
                }
            } else {
                $bindArray[":UnitID"] = $build;
                if ($this->type == 0) {
                    $sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account left join Account C on C.ID = B.ParentID where C.ID = :Node and B.UnitID = :UnitID $where";
                } else {
                    $sql = "select  A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node left join Account D on D.ID = C.ParentID where D.ID = :Node and C.UnitID = :UnitID $where";
                }
            }
        }
        
        $total = count($this->db->querySList($sql, $bindArray));
        $sql .= " order by A.ID DESC limit $offset,$rows";
        $data = $this->db->querySList($sql, $bindArray);
        
        $rows = [];
        foreach ($data as $row) {
            $cur = [];
            $cur['ID'] = $row['ID'];
            $cur['Room'] = $row['RoomNumber'];
            $cur['RoomID'] = $row['RoomID'];
            if ($this->type == 0) {
                $cur['Key'] = $row['TmpKey'];
                $cur['Count'] = $row['AllowedTimes']-$row['AccessTimes'];
                $cur['Img'] = $row['QrCodeUrl'];
                $cur['QRCode'] = $row['QrCodeUrl'];
                $cur["DateFlag"] = \util\computed\getDateScript($row["DateFlag"]);
                $cur['Name'] = $row['Description'];
                $cur["EndTime"] = \util\time\setCustomizeFormat(strtotime($row["EndTime"]), $customizeForm);
                if ($row["SchedulerType"] != 0) {
                    $cur["Count"] = "--";
                    $cur["EndTime"] = "--";
                }
                $cur["Repeats"] = [MSGTEXT["never"],MSGTEXT["daily"],MSGTEXT["weekly"]][$row["SchedulerType"]];
                $cur['Creator'] = $this->db->querySList("select Name from PersonalAccount where Account = :Account", [":Account"=>$row['Creator']])[0]["Name"];
            }
            if ($this->type == 1) {
                $cur['Code'] = $row['Code'];
                $cur['User'] = $row['User']?:'--';
                $cur['UserID'] = $row['UserID'];
                $cur['Expiration Time'] = $row['ExpireTime'];
            }
            if ($this->type == 2) {
                $cur['User'] = $row['User']?:'--';
                $cur['UserID'] = $row['UserID'];
                $cur['Expiration Time'] = $row['ExpireTime'];
                $cur['Code'] = $row['Code'];
            }
            array_push($rows, $cur);
        }
        return ["total"=>$total,"detail"=>$data,"row"=>$rows];
    }
        
    public function queryComKey()
    {
        $params = [
            //主账户
            "userAlias"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->queryCom($user);
        // 更改数据流
        \util\computed\setGAppData(["data"=>$data]);
    }
    
    public function queryTmp()
    {
        $this->type = 0;
        $this->queryComKey();
    }
    
    public function queryRF()
    {
        $this->type = 1;
        $this->queryComKey();
    }
    
    public function queryPri()
    {
        $this->type = 2;
        $this->queryComKey();
    }
    
    public function queryCommunity()
    {
        global $cMessage;
        $params = [
            //主账户
            "userAliasId"=>"",
            "RoomID"=>"",
            "Build"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $room = $params["RoomID"];
        $build = $params["Build"];
        if ($room == "all") {
            $build = $build == "community" ? false : $build;
            $data = $this->queryCom($userId, true, $build);
        } else {
            $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$room]]);
            if (count($data) == 0) {
                $cMessage->echoErrorMsg(StateAccountNotExit);
            }
            $user = $data[0]["Account"];
            $data = $this->queryCom($user);
        }
        // 更改数据流
        \util\computed\setGAppData(["data"=>$data]);
    }
    
    public function queryTmpFC()
    {
        $this->type = 0;
        $this->queryCommunity();
    }
    
    public function queryRFFC()
    {
        $this->type = 1;
        $this->queryCommunity();
    }
    
    public function queryPriFC()
    {
        $this->type = 2;
        $this->queryCommunity();
    }

    public function queryTmpForApp()
    {
        $params = [
            //主账户
            "userAlias"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            'IsGetNowTempKey' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        // 用于返回新增的TempKey
        $isGetNowTempKey = $params['IsGetNowTempKey'] ? true:false;
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        $nowTime = \util\time\setTimeZone(\util\computed\getNow(), $timeZone, 3);
        if ($isGetNowTempKey === true) {
            $limit = 'CreateTime desc limit 1';
        } else {
            $limit = "FIELD((AllowedTimes > AccessTimes && EndTime > '$nowTime') || (SchedulerType != 0 && SchedulerType != 3), 1, 0) asc,ID desc limit $offset,$rows";
        }
        $data = $this->db->querySList("select * from PersonalAppTmpKey where Node = :Node order by $limit", [":Node"=>$user]);

        $keyIds = array_column($data, 'ID', null);
        $extraData = [];
        if (!empty($keyIds)) {
            $keyIds = implode(',', $keyIds);
            $extraData = $this->db->querySList("select * from PersonalAppTmpKeyList where KeyID in ({$keyIds})");
            $extraData = \util\arr\formatArrByKey($extraData, 'KeyID');
        }

        foreach ($data as &$value) {
            $value["DateFlag"] = \util\computed\getDateScript($value["DateFlag"]);
            $value["Expired"] = 0;
            if ($value["SchedulerType"] == 0 or $value["SchedulerType"] == 3) {
                if ($value["AccessTimes"] >= $value["AllowedTimes"]) {
                    $value["Expired"] = 1;
                } else {
                    // 此时用户的时区,使用服务器时刻转换为用户时区的时间进行比较
                    if (strtotime($nowTime) > strtotime($value["EndTime"])) {
                        $value["Expired"] = 1;
                    }
                }
            }
            $value["BeginTime"] = \util\time\setCustomizeFormat(strtotime($value["BeginTime"]), $customizeForm);
            $value["EndTime"] = \util\time\setCustomizeFormat(strtotime($value["EndTime"]), $customizeForm);
            // kangxiaolong 2021-03-25 修改 12小时制会被删除掉后面AM PM的bug
            $startTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$value["StartTime"]), $customizeForm));
            $endTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$value["StopTime"]), $customizeForm));
            array_shift($startTimes);
            array_shift($endTimes);
            $value["StartTime"] = implode(" ", $startTimes);
            $value["StopTime"] = implode(" ", $endTimes);
            $value['IsOld'] = 0;
            $value['Doors'] = isset($extraData[$value['ID']]) ? $extraData[$value['ID']] : [];
            if ($value["SchedulerType"] == 0) { //后台刷数据有relay（list表里有数据），但是EachAllowedTimes没有刷，兼容防止app旧的显示为0
                $value['EachAllowedTimes'] = $value['AllowedTimes'];
            }
            $value['TempkeyType'] = $value['Type'];
            if ($value['TempkeyType'] == 2) {
                $value['Description'] = MSGTEXT['delivery_description'];
            }
        }
        unset($value);
        \util\computed\setGAppData(["data"=>$data]);
    }


    // function queryEndUser () {
    // 	$params = [
    // 		//主账户
    // 		"userAlias"=>"",
    // 	];
    // 	$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
    // 	$user = $params["userAlias"];
    // 	$this->queryEndUserCom($user);
    // }

    // function queryForCommunity () {
    // 	global $cMessage;
    // 	$params = [
    // 		//可能是主账户
    // 		"userAliasId"=>"",
    // 		"RoomID"=>"",
    // 		"Build"=>""
    // 	];
    // 	$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
    // 	$user = $params["userAliasId"];
    // 	$room = $params["RoomID"];
    // 	$build = $params["Build"];

    // 	if($room == "all") {
    // 		$build = $build == "community" ? false : $build;
    // 		$this->queryEndUserCom($user,true,$build);
    // 	}else {
    // 		$data = $this->db->queryAllList("PersonalAccount",["equation"=>[":ID"=>$room]]);
    // 		if(count($data) == 0) $cMessage->echoErrorMsg(StateAccountNotExit);
    // 		$user = $data[0]["Account"];
    // 		$this->queryEndUserCom($user);
    // 	}
    // }

    // private function queryEndUserCom ($account,$manage=false,$build=false) {
    // 	$params = [
    // 		//主账户
    // 		"SelfTimeZone"=>"",
    // 		"SelfCustomizeForm"=>""
    // 	];
    // 	$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
    // 	$timeZone = $params["SelfTimeZone"];
    // 	$customizeForm = $params["SelfCustomizeForm"];
    // 	list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
    // 	$where = '';
    // 	$bindArray = [];
    // 	switch ($serchKey)  {
    // 		case 'Code':
    // 			$where = "AND A.Code like :serchValue";
    // 			$bindArray[":serchValue"] = "%$serchValue%";
    // 			break;
    // 		case 'Key':
    // 			$where = "AND A.TmpKey like :serchValue";
    // 			$bindArray[":serchValue"] = "%$serchValue%";
    // 			break;
    // 		case 'User':
    // 			$where = "AND B.Name like :serchValue";
    // 			$bindArray[":serchValue"] = "%$serchValue%";
    // 			break;
    // 	}

    // 	$tabel = $this->tabelName[$this->type];
    // 	if($manage === false) {
    // 		$sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account where A.Node = :Node $where";
    // 		if ($this->type!=0) $sql = "select A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node where A.Node = :Node $where";
    // 		$bindArray[":Node"] = $account;
    // 	}else {
    // 		if($build == false) {
    // 			$sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account left join Account C on C.ID = B.ParentID where C.ID = :Node $where";
    // 			if($this->type!=0) $sql = "select A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node left join Account D on D.ID = C.ParentID where D.ID = :Node $where";
    // 			$bindArray[":Node"] = $account;
    // 		}else{
    // 			$sql = "select A.*,B.RoomNumber,B.ID as RoomID from $tabel A left join PersonalAccount B on A.Node = B.Account left join Account C on C.ID = B.ParentID where C.ID = :Node and B.UnitID = :UnitID $where";
    // 			if($this->type!=0) $sql = "select A.ID,A.Access,A.Code,A.ExpireTime,B.Name as User,B.ID as UserID,C.RoomNumber,C.ID as RoomID from $tabel A left join PersonalAccount B on A.AccountID = B.ID left join PersonalAccount C on C.Account = A.Node left join Account D on D.ID = C.ParentID where D.ID = :Node and C.UnitID = :UnitID $where";
    // 			$bindArray[":Node"] = $account;
    // 			$bindArray[":UnitID"] = $build;
    // 		}
    // 	}

    // 	$total = count($this->db->querySList($sql,$bindArray));
    // 	$sql .= " order by A.ID DESC limit $offset,$rows";
    // 	$data = $this->db->querySList($sql,$bindArray);
    // 	$row = [];
    // 	foreach ($data as $val) {
    // 		$cur = array();
    // 		$cur['ID'] = $val['ID'];
    // 		$cur['Room'] = $val['RoomNumber'];
    // 		$cur['RoomID'] = $val['RoomID'];
    // 		if($this->type == 0) {
    // 			$cur['Key'] = $val['TmpKey'];
    // 			$cur['Count'] = $val['AllowedTimes']-$val['AccessTimes'];
    // 			$cur['Img'] = $val['QrCodeUrl'];
    // 			$cur['QRCode'] = $val['QrCodeUrl'];
    // 			$cur["DateFlag"] = \util\computed\getDateScript($val["DateFlag"]);
    // 			$cur['Name'] = $val['Description'];
    // 			// 用户显示的tempkey不要时区计算
    // 			$cur["EndTime"] = \util\time\setCustomizeFormat(strtotime($val["EndTime"]),$customizeForm);
    // 			if($val["SchedulerType"] != 0) {
    // 				$cur["Count"] = "--";
    // 				$cur["EndTime"] = "--";
    // 			}
    // 			$cur["Repeats"] = [MSGTEXT["never"],MSGTEXT["daily"],MSGTEXT["weekly"]][$val["SchedulerType"]];
    // 			$cur['Creator'] = $this->db->querySList("select Name from PersonalAccount where Account = :Account",[":Account"=>$val['Creator']])[0]["Name"];
    // 		}

    // 		if ($this->type == 1) {
    // 			$cur['Code'] = $val['Code'];
    // 			$cur['User'] = $val['User']?$val['User']:'--';
    // 			$cur['UserID'] = $val['UserID'];
    // 			$cur['Expiration Time'] = $val['ExpireTime'];
    // 		}
    // 		if ($this->type == 2) {
    // 			$cur['User'] = $val['User']?$val['User']:'--';
    // 			$cur['UserID'] = $val['UserID'];
    // 			$cur['Expiration Time'] = $val['ExpireTime'];
    // 			$cur['Code'] = $val['Code'];
    // 		}
    // 		array_push($row,$cur);
    // 	}

    // 	\util\computed\setGAppData(["data"=>["total"=>$total,"row"=>$row,"detail"=>$data]]);
    // }
}
