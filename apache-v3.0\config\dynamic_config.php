<?php

const SERVERURL = '**************:9999';
const SERVERNUMBER = '172';
const SERVER_NUMBER_EXPAND = [172, 173];
const IPV4IMG = 'https://*************:8091';
const IPV6IMG = 'https://[]:8091';
const IPV4VIDEO = 'http://*************:8188';
const IPV6VIDEO = 'http://[]:8188';
const SERVERHOST = 'https://*************';
const SIPSERVER = 'http://*************:118';
const G_PBX_IPV6 = "[*************]:5070";
const G_PBX_IPV4 = "*************:5070";

const WEB_IP = '**************';
const CSGAET_NET = '**************:9999';
const CSGAET_NET_IPV6 = '[]:19999';
const CSGAET_DOMAIN = '*************:9999';
const WEB_DOMAIN = '*************';
const REMOTE_CONFIG_PRIMARY_API_URL = 'https://rexx.dev.com:1301';
const REMOTE_CONFIG_SECONDARY_API_URL = 'https://rexx.dev.com:1301';

const REMOTE_CONIFG_PRIMARY_DOMAIN = 'rexx.dev.com';
const REMOTE_CONIFG_SECONDARY_DOMAIN = 'rexx2.dev.com';
const REMOTE_CONFIG_DOMAIN_NAME = 'rexx.dev.com';


#bm
const BMURL = 'https://*******/bmserver/';
const BMAPYURL = 'https://bmsys.akuvox.com1/dist/pay.html';

const PBX_LANDLINE_NUMBER =['+15036837202'];
const CANGETTOOLBOX = 1;
const SERVER_LOCATION = "na";

#db
const DATABASEIP="12,3,3.4";
const DATABASEPORT = 3306;

#redis
const REDISIP = "*************";
const ENABLE_REDIS_SENTINEL = "1";
const REDIS_SENTINEL_HOSTS = "92.168.14:8506, *************:8599";

#国内金茂对特定小区特殊处理
#const SUPPORT_OFFLINE_TEMP_KEY_COMMUNITYS = array(424);
const SUPPORT_OFFLINE_TEMP_KEY_COMMUNITYS = array(0);
const ABLE_SMART_HOME = true;
const SMART_HOME_HOST = "https://test.smarthome.akuvox.com";
const SMART_HOME_TOKEN = '1a6qprpncvvztkcddsu9jqe5bm2z096i4grwtzli173atokk8cmvn5qoucbf4fp62euruioquzl0o5e9hx74a2exhuva2atd3ean06kl5xanrhgflvrvjlzbrczz3v36';

#OSS
const OSS_PROVIDER = "aliyun";
const OSS_TAG = "dev";
const OSS_BUCKET_FORLOG = "server-log-back";
const OSS_BUCKET_FORPIC = "server-log-back-pic";
const OSS_BUCKET_FORFACE = "server-log-back-face";
const OSS_REGION_ID = "ap-southeast-1";
const OSS_ENDPOINT = "oss-ap-southeast-1.aliyuncs.com";
const OSS_OUTER_ENDPOINT = "https://oss-ap-southeast-1.aliyuncs.com";
const OSS_USER = "LTAILDY6u3uotCJm";
const OSS_PW = "46EL7LJInh6xvJ6fh5KTvEyXEvqYCb";
const OSS_STS_ENDPOINT = "sts.ap-southeast-1.aliyuncs.com";
const OSS_ROLE_ARN = "acs:ram::1185918277276864:role/osscontrol";

#网关服务器编号
const GATEWAY_NUM = 65;

#配置OEM,用于推送服务的区分,空值就是akuvox
const OEM_NAME = "Akuvox";
