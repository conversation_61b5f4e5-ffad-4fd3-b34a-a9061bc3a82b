<?php
/*
 * @Description: 全局变量定义
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-19 11:00:59
 * @LastEditors  : kxl
 */
include_once __DIR__."/../plan/main.php";
include_once __DIR__."/model.php";
include_once __DIR__."/auditCode.php";

// message
include_once __DIR__."/../print/message.php";
$cMessage = \message\CMessage::getInstance();
include_once __DIR__."/../print/log.php";
$cLog = \log\CLog::getInstance();
include_once __DIR__."/../print/auditLog.php";
$cAuditLog = \log\CAuditLog::getInstance();
include_once __DIR__."/../database/main.php";
$db = \database\CDatabase::getInstance();

$gSendSmartHomeTaskIds = [];

$gApp = [
    // 用户
    "user"=>"",
    // 用户角色，带前缀
    "role"=>"",
    // 登录用户的用户Id,实际用户身份
    "userId"=>"",
    // 代理用户Id，如管理员代理主账户添加从账户，该值初始值等于userId,系统中需要使用userId是应使用该值,所有操作都要代理上级有权限的用户
    "userAliasId"=>"",
    // 代理用户账户，同上
    "userAlias"=>"",
    "plan"=>[
        // 任务名，url请求名
        "name"=>"",
        // 请求方法
        "method"=>"",
        // 类型
        "type"=>"",
        // 别名
        "alias"=>"",
        // 验证token
        "auth"=>true,
        // 过期或者激活验证
        "active"=>true,
        // 任务内容
        "task"=>[],
        // 数据内容
        "data"=>[],
        // 原始内容
        "params"=>[],
        // 分支线执行
        "branches"=>[]
    ],
    
];
