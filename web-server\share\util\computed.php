<?php
/*
 * @Description: 计算类型函数
 * @version:
 * @lastEditor cj
 * @Date: 2020-01-10 15:24:18
 * @LastEditors: jun.cai <EMAIL>
 */

namespace share\util;

use framework\database\CRedis;
/**
 * @name:
 * @msg: 全局数据设置
 *
 * @param {type}
 * @return:
 */
function setGAppData($data)
{
    if ('workman' !== WORK_FRAME) {
        global $gApp;
    } else {
        $gApp = getGApp();
    }
    foreach ($data as $key => $value) {
        $gApp['plan']['data'][$key] = $value;
    }
}

function setGAppBranch($branch)
{
    if ('workman' !== WORK_FRAME) {
        global $gApp;
    } else {
        $gApp = getGApp();
    }

    array_push($gApp['plan']['branches'], $branch);
}

function getGAppDataValue($key)
{
    if ('workman' !== WORK_FRAME) {
        global $gApp;
    } else {
        $gApp = getGApp();
    }

    return $gApp['plan']['data'][$key];
}

/**
 * @name: recordAccountIp
 * @msg: 记录登录错误次数
 *
 * @param {type}
 * @return:
 */
function recordAccountIp($user, $ip, $second = 0, $maxTimes = 4)
{
    $second = $second == 0 ? 300 : $second;
    // TODO
    $redis = getRedis();
    $redis->select(REDISDB2LIMITIP);
    $oldRecord = $redis->get($user);
    if ($oldRecord !== null) {
        $oldRecord = json_decode($oldRecord, true);
        $oldIp = $oldRecord['ip'];
        if ($ip == $oldIp) {
            $num = $oldRecord['number'];
            if ($num < $maxTimes) {
                //次数不到，增加次数
                $num = $num + 1;
                $oldRecord['number'] = $num;
                $now = getNow();
                $oldRecord['time'] = $now;
                $oldRecord = json_encode($oldRecord);
                $redis->setex($user, $second, $oldRecord);

                return [false, $num];
            }
            //返回剩余时间
            $time = strtotime($oldRecord['time']);
            $now = strtotime(getNow());

            return [($second - ($now - $time)), 5];
        }

        return recordNew($user, $ip, getNow(), $redis, $second);
    }

    return recordNew($user, $ip, getNow(), $redis, $second);
}

function recordNew($user, $ip, $time, $redis, $second = 0)
{
    $second = $second == 0 ? 300 : $second;
    //新纪录
    $newRecord = ['time' => $time, 'ip' => $ip, 'number' => 1];
    $redis->setex($user, $second, json_encode($newRecord));

    return [false, 1];
}

/**
 * @name: getLimitIp
 * @msg: 获取IP是否被限制
 *
 * @param {type}
 * @return:
 */
function getLimitIp($user, $ip, $second = 0, $maxTimes = 4)
{
    $second = $second == 0 ? 300 : $second;
    //获取被限制ip
    // TODO
    $redis = CRedis::getInstance();
    $redis->select(REDISDB2LIMITIP);
    $oldRecord = $redis->get($user);
    if ($oldRecord !== null) {
        $oldRecord = json_decode($oldRecord, true);
        if ($oldRecord['ip'] == $ip && $oldRecord['number'] >= $maxTimes) {
            //返回剩余时间
            $time = strtotime($oldRecord['time']);
            $now = strtotime(getNow());

            return $second - ($now - $time);
        }

        return false;
    }

    return false;
}

function getNow()
{
    $now = date('Y-m-d H:i:s');

    return $now;
}

/**
 * @name: computedLastDate
 * @msg: 时间计算，月份加
 *
 * @param {type}
 * @return:
 */
function computedLastDate($expireTime, $month)
{
    // 首日
    $oldEndDay = explode('-', explode(' ', $expireTime)[0])[2];
    $beginDate = date('Y-m-01', strtotime($expireTime));

    $month = $month + 1;

    $endTime = date('Y-m-d', strtotime("$beginDate +$month month -1 day"));
    $endTimeArr = explode('-', $endTime);
    $day = $endTimeArr[2];
    $day = $day < $oldEndDay ? $day : $oldEndDay;

    // 时间组装
    return $endTimeArr[0] . '-' . $endTimeArr[1] . '-' . $day . ' ' . explode(' ', $expireTime)[1];
}

/**
 * 计算下次过期时间.
 *
 * @param expireTime: 当前过期时间
 * @param months: 续费月份
 *
 * @return string: 下次过期时间
 */
function computedNextExpireTime($expireTime, $months)
{
    $nextTime = '';
    $now = getNow();
    if ($expireTime) {
        // 若未过期，为过期时间延期n个月
        $nextTime = computedLastDate($expireTime, $months);
        $nextNow = computedLastDate($now, $months);
        // 若已过期， 过期时间为此时购买时间点延期n个月
        if (strtotime($nextTime) < strtotime($nextNow)) {
            $nextTime = $nextNow;
        }
    } else {
        // 第一次购买，过期时间为此时购买时间点延期n个月
        $nextTime = computedLastDate($now, $months);
    }

    return $nextTime;
}

/**
 * @msg: 去除首字符
 */
function delKeyFirstChar(array $array)
{
    $newArr = [];
    foreach ($array as $key => $value) {
        $key = substr($key, 1);
        $newArr[$key] = $value;
    }

    return $newArr;
}

/**
 * @msg: 输出金额计算
 */
function outputComputedCount($value)
{
    $value = (float)$value;

    return $value / 100;
}

/**
 * @msg: 输入金额计算
 */
function inputComputedCount($value)
{
    $value = (float)$value;

    return (int)(string)($value * 100);
}

/**
 * @msg: 保留两位小数
 */
function roundToTwoDecimal($value) {
    $value = (float)$value;
    return round($value, 2);
}


/**
 * @msg: 打折计算
 */
function computedDiscount($price, $discount)
{
    return round($price * $discount / 100);
}

// 澳洲云迁移，图片奥定向访问scloud oss
function computedImageLinkAuCloud($path) {
    if ($path === '') {
        return '';
    }
    require_once __DIR__."/../plugin/oss/aliyun.phar";
    $ossClient = new \OSS\OssClient('LTAI5tANJubr9zHBw31qk9SM', '******************************', 'oss-ap-southeast-1-internal.aliyuncs.com');
    $signedUrl = $ossClient->signUrl('akcs-scloud-pic-bucket', $path, 900, "GET", "");
    $signedUrl = str_replace("oss-eu-central-1-internal.aliyuncs.com", "oss-eu-central-1.aliyuncs.com", $signedUrl);
    $signedUrl = str_replace("oss-ap-southeast-1-internal.aliyuncs.com", "oss-ap-southeast-1.aliyuncs.com", $signedUrl);
    $signedUrl = str_replace("oss-us-west-1-internal.aliyuncs.com", "oss-us-west-1.aliyuncs.com", $signedUrl);
    $signedUrl = str_replace("oss-cn-shenzhen-internal.aliyuncs.com", "oss-cn-shenzhen.aliyuncs.com", $signedUrl);
    return $signedUrl;
}

function computedImageLink($path, $host, $optionPath = '')
{
    if ($path === '') {
        return '';
    }

    if (preg_match("/^\/group/", $path)) {
        // 旧模式
        $time = time();
        $link = $path;
        if (stristr($path, "_40x40") !== false) {
            $path = $optionPath;
        }
        $path = substr($path, 8);
        return $host. $link . "?token=" . md5($path . IMAGELINKKEY . $time) . "&ts=$time";
    }

    try {
        if (OSS_PROVIDER === 'aliyun') {
            // 阿里云
            require_once __DIR__."/../plugin/oss/aliyun.phar";
            $ossClient = new \OSS\OssClient(OSS_USER, OSS_PW, OSS_ENDPOINT);
            $signedUrl = $ossClient->signUrl(OSS_BUCKET_FORPIC, $path, 900, "GET", "");
            $signedUrl = str_replace("oss-eu-central-1-internal.aliyuncs.com", "oss-eu-central-1.aliyuncs.com", $signedUrl);
            $signedUrl = str_replace("oss-ap-southeast-1-internal.aliyuncs.com", "oss-ap-southeast-1.aliyuncs.com", $signedUrl);
            $signedUrl = str_replace("oss-us-west-1-internal.aliyuncs.com", "oss-us-west-1.aliyuncs.com", $signedUrl);
            $signedUrl = str_replace("oss-cn-shenzhen-internal.aliyuncs.com", "oss-cn-shenzhen.aliyuncs.com", $signedUrl);
            // 替换 http 为 https
            $signedUrl = str_replace("http://", "https://", $signedUrl);

            return $signedUrl;
        }

        if (OSS_PROVIDER === 'us3') {
            require_once __DIR__."/../plugin/oss/ukd/ufileclient.php";
            $client = new \UFileClient(OSS_USER, OSS_PW, OSS_ENDPOINT);
            return $client->GetSignUrl(OSS_BUCKET_FORPIC, $path, 900);
        }

        require_once __DIR__."/../plugin/oss/aws.phar";
        $credentials = new \Aws\Credentials\Credentials(OSS_USER, OSS_PW);
        $s3Client = new \Aws\S3\S3Client([
            'version'     => 'latest',
            'region'      => OSS_REGION_ID,
            'credentials' => $credentials,
            'use_dual_stack_endpoint' => true
        ]);
        $cmd = $s3Client->getCommand('GetObject', [
            'Bucket' => OSS_BUCKET_FORPIC,
            'Key' => $path
        ]);
        $request = $s3Client->createPresignedRequest($cmd, '+900 second');
        $presignedUrl = (string)$request->getUri();
        return $presignedUrl;
    } catch(\Exception $e) {
        return '';
    }
}

function getDateScript($dateFlag)
{
    $tmpData = [];
    $dateFlags = [1, 2, 4, 8, 16, 32, 64];
    foreach ($dateFlags as $key => $value) {
        if (($dateFlag & $value) == $value) {
            array_push($tmpData, $key);
        }
    }

    return implode(';', $tmpData);
}


function minusMonthCeil($first, $last)
{
    $first = explode(' ', $first)[0];
    $last = explode(' ', $last)[0];

    $firstYear = explode('-', $first)[0];
    $lastYear = explode('-', $last)[0];
    $firstMonth = explode('-', $first)[1];
    $lastMonth = explode('-', $last)[1];
    $firstDay = explode('-', $first)[2];
    $lastDay = explode('-', $last)[2];

    return ($lastYear - $firstYear) * 12 + ($lastMonth - $firstMonth) + (($lastDay - $firstDay) > 0 ? 1 : 0);
}

/**
 * @name:
 * @msg: PM导出log偏移值
 *
 * @param {count,$num：正序第几条}
 * @return:
 */
function exportLogOffset($count, $num)
{
//    count - 要取的那一位下标值 - 1
    return $count - $num - 1;
}

/**
 * 数据库字段位运算
 * @param initData: 原始数据
 * @param targetRes: 目标位结果
 * @param targetBit: 目标位(1表示个位...)
 */
function bitOperation($initData, $targetRes, $targetBit)
{
    $totalBit = 32;
    if ($targetRes == 0) {
        // 除目标位数剩余位数都应该为1，保证&运算不会改动其他位数
        $remainRes = pow(2, 32) - 1 - pow(2, $targetBit - 1);
    } elseif ($targetRes == 1) {
        // 除目标位数剩余位数都应该为0，保证|运算不会改动其他位数
        $remainRes = 0;
    }

    // 改动目标位数后的情况
    $resData = intval($targetRes) * pow(2, $targetBit - 1) + $remainRes;
    if ($targetRes == 0) {
        return (intval($initData) & $resData);
    }
    return (intval($initData) | $resData);
}

/**
 * 获取指定位数值
 */
function getSpecifyBit($initData, $targetBit)
{
    return (intval($initData) >> $targetBit) & 1;
}

/**
 * 获取指定位数值，从1开始
 */
function getSpecifyBitLE($initData, $targetBit)
{
    $targetBit = $targetBit - 1;
    return (intval($initData) >> $targetBit) & 1;
}

/**
 * 根据指定的位位置生成一个十进制数。
 * @param int|array $positions 一个整数或整数数组，表示需要设置为 1 的位位置（从 1 开始）。
 * @return int 返回由指定位位置组成的二进制数的十进制表示。
 */
function getDecimalFromBits($positions) {
    $result = 0;

    // 检查是否传入的是数组
    if (is_array($positions)) {
        // 遍历数组中的每个位置
        foreach ($positions as $position) {
            // 通过左移操作将该位设置为 1，并使用按位或运算符合并到结果中
            $result |= (1 << ($position - 1));
        }
    } else {
        // 如果传入的是单个整数，直接计算该位置的二进制值
        $result = 1 << ($positions - 1);
    }

    // 返回计算得到的十进制值
    return $result;
}

/**
 * 获取十进制数在二进制表示中所有为 1 的位的位置。
 *
 * @param int $decimal 要检查的十进制数。
 * @return array 包含所有为 1 的位的位置（从 1 开始）。
 */
function getBitPositions($decimal) {
    $positions = [];
    $position = 1;

    while ($decimal > 0) {
        if (($decimal & 1) === 1) {
            $positions[] = $position;
        }
        $decimal >>= 1;
        $position++;
    }

    return $positions;
}


/**
 * pin加密.
 */
function setPinIsEncryptPin($userId, $targetVal)
{
    $dis = \share\util\getDisForInstaller($userId);
    if ($dis['IsEncryptPin'] == ENCRYPTION) {
        $targetVal = $targetVal === null ? '' : $targetVal;
        if ($targetVal !== '') {
            return '****';
        } else {
            return $targetVal;
        }
    } else {
        return $targetVal;
    }
}

/**
 * @description 设置用户某个操作错误次数
 * @param $user 用户名
 * @param $key 操作key
 * @param $limitTimes 最大限制次数
 * @param $remainTime 限制持续时间的时间戳
 * @return bool 当前记录后超过限制次数返回true，没有超过返回false
 * @lastEditor csc、2022/3/10 10:24、V6.4
 * <AUTHOR> 10:24、V6.4
 */
function recordLimitTimes($user, $key, $limitTimes, $remainTime)
{
    $redis = CRedis::getInstance();
    $redisKey = $user . '_' . $key;
    $redis->select(REDISDB2LIMITIP);
    $oldRecord = $redis->get($redisKey);
    $now = strtotime(getNow());
    if (false !== $oldRecord && null !== $oldRecord) {
        $newRecord = $oldRecord = json_decode($oldRecord, true);
        //次数不到，增加次数，时间更新
        if ($oldRecord['number'] < $limitTimes) {
            $newRecord['number'] = $oldRecord['number'] + 1;
            $newRecord['time'] = $now + $remainTime;
        }
    } else {
        $newRecord = [
            'number' => 1,
            'time' => $now + $remainTime,
        ];
    }
    $recodeValue = json_encode($newRecord);
    $redis->setex($redisKey, $remainTime, $recodeValue);

    return $newRecord['number'] >= $limitTimes ? true : false;
}

/**
 * @description 获取用户某个操作是否超过限制次数
 * @param $user 帐号
 * @param $key 操作key
 * @param $limitTimes 限制次数
 * @return false|int 无限制返回false，已被限制返回剩余时间戳
 * @lastEditor csc、2022/3/10 10:23、V6.4
 * <AUTHOR> 10:23、V6.4
 */
function getLimitTimes($user, $key, $limitTimes)
{
    $redis = CRedis::getInstance();
    $redisKey = $user . '_' . $key;
    $redis->select(REDISDB2LIMITIP);
    $oldRecord = $redis->get($redisKey);
    if (false !== $oldRecord && null !== $oldRecord) {
        $oldRecord = json_decode($oldRecord, true);
        if ($oldRecord['number'] >= $limitTimes) {
            //返回限制剩余时间
            $time = $oldRecord['time'];
            $now = strtotime(getNow());

            return $time > $now ? $time - $now : false;
        }
        return false;
    }

    return false;
}

/**
 * @description 删除错误次数
 * @param $user
 * @param $key
 * @return void
 * @lastEditor csc 2022/3/16 19:53 V6.4
 * <AUTHOR> 2022/3/16 19:53 V6.4
 */
function delLimitTimes($user, $key)
{
    $redis = CRedis::getInstance();
    $redisKey = $user . '_' . $key;
    $redis->select(REDISDB2LIMITIP);
    $redis->del($redisKey);
}

/**
 * @description:计算天数
 * @param $start "2000-1-1"
 * @param $end "2001-1-1"
 * @return float|int
 * @lastEditors: zyc 2022/3/21 18:55 V6.4
 * @author:zyc 2022/3/21 18:55 V6.4
 */
function computeDiffDays($start, $end)
{
    $second1 = strtotime(date("Y-m-d", strtotime($start)));
    $second2 = strtotime(date("Y-m-d", strtotime($end)));

    if ($second1 < $second2) {
        $tmp = $second2;
        $second2 = $second1;
        $second1 = $tmp;
    }
    return intval(($second1 - $second2) / 86400);
}

/**
 * @description: 计算fdfs文件地址
 * @param $url
 * @param $exprieTime 链接有效期
 * @return string
 * @author: csc 2022/12/16 15:44 V6.5.2
 * @lastEditors: csc 2022/12/16 15:44 V6.5.2
 */
function computedFdfsToken($url, $exprieTime = 31536000)
{
    $key = "ak_download";
    $path = strstr($url, "/group");

    $ts = time() + $exprieTime;  ///代表31536000秒后过期，可以自行调整
    $md5 = base64_encode(md5($key . ':' . $path . ':' . $ts, true));
    $md5 = strtr($md5, '+/', '-_');
    $md5 = str_replace('=', '', $md5);
    return $url . "?token=" . $md5 . "&e=" . $ts;
}

/**
 * @description: 获取屏保自定义上传图片网络地址 (临时方案，后续设备端出版本可以规范直接获取fdfs地址返回设备不用转一次)
 * @param $id ScreenSaverImg表的ID
 * @return string
 * @author: csc 2022/12/19 10:36 V6.5.2
 * @lastEditors: csc 2022/12/19 10:36 V6.5.2
 */
function computeScreenSaverImgPath($id)
{
    return 'https://' . WEB_DOMAIN . '/web-server/s?K='. $id . '.jpg';
}

/**
 * @description: 通用次数限制检测
 * @param {string} $key 用来保存的标识，不同模块方法的限制不能相同，否则会重合统计
 * @return void
 * @throws \Exception
 * @author: csc 2023/3/22 14:56 V6.5.4
 * @lastEditors: csc 2023/3/22 14:56 V6.5.4
 */
function checkCommonLimit($key) {
    $cMessage = \share\util\getMessage();
    $ip = \share\util\getIp();

    $whiteIpList = ['127.0.0.1'];
    if (in_array($ip, $whiteIpList) or false === $ip) {
        return;
    }

    $limitKey = $key. '_' .$ip;
    $limit = \share\util\getLimitIp($limitKey, $ip);
    if ($limit) {
        $cMessage->echoErrorMsg(STATE_LIMIT_WITH_IP, ['externalErrorObj' => \package\single\model\device\config\Code::EXT_STATE_DEVICE_MAC_DECRYPT_FAIL_TOO_OFTEN], [$ip]);
    }
}

/**
 * @description: 增加通用次数限制
 * @param {string} $key 用来保存的标识，不同模块方法的限制不能相同，否则会重合统计
 * @return void
 * @author: csc 2023/3/22 14:56 V6.5.4
 * @lastEditors: csc 2023/3/22 14:56 V6.5.4
 */
function addCommonLimit($key) {
    $ip = \share\util\getIp();

    $whiteIpList = ['127.0.0.1'];
    if (in_array($ip, $whiteIpList) or false === $ip) {
        return;
    }

    $limitKey = $key. '_' .$ip;
    \share\util\recordAccountIp($limitKey, $ip);
}

//获取手册带域名的路径
function getGuideUrl($path) {
    $path = computedFdfsToken(IPV4IMG . $path);
    return $path;
}

/*
 *@description 获取当前时间所在月份的第一天
 *<AUTHOR> 2024-09-14 18:29:06 V6.7.3
 *@lastEditor cj 2024-09-14 18:29:06 V6.7.3
 *@return $firstDayOfMonth
 */
function getCurrentMonthFirstDay()
{
    $nowDate = \share\util\getNow();
    $currentDate = new \DateTime($nowDate);
    // 获取当前月份的第一天的 00:00:00
    $firstDayOfMonth = $currentDate->modify('first day of this month')->setTime(0, 0, 0)->format('Y-m-d H:i:s');

    return $firstDayOfMonth;
}

/*
 *@description 通过 按位与运算 快速判断两个整数的二进制是否存在同为 1 的位
 *@return true/false
 */
function hasCommonBit($a, $b) {
    return ($a & $b) !== 0;
}