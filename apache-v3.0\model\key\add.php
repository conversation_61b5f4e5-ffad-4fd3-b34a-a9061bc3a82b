<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-20 15:40:20
 * @LastEditors  : kxl
 */

namespace model\key;

trait add
{
    public function addCom($account)
    {
        global $cMessage;
        $params = [
            // "Allow"=>"",
            "ExpireTime" => "",
            "UserID" => "",
            "Code" => "",
            "SelfTimeZone" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        // $allow = $params["Allow"];
        $expireTime = $params["ExpireTime"];
        $userId = $params["UserID"];
        $code = $params["Code"];
        $timeZone = $params["SelfTimeZone"];


        $tabel = $this->tabelName[$this->type];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $role = $userData["Role"];
        $account = $account == null ? $userData["Account"] : $account;
        $this->log->actionLog("#model#key#addCom#expireTime=$expireTime;userId=$userId;code=$code;account=$account");
        $expireTime = \util\time\setTimeZone($expireTime, $timeZone, "", "-");

        if ($this->type == 1 || $this->type == 2) {
            $rfCard = $this->db->queryAllList($tabel, ["equation" => [":Code" => $code, ":Node" => $account]]);
            if (count($rfCard) > 0) {
                $cMessage->echoErrorMsg($this->type == 1 ? StateRFCardExit : StatePrivateKeyExists);
            }
        }

        $now = \util\computed\getNow();
        $tmpType = 1;
        $unit = 0;
        if (in_array($role, COMROLE)) {
            //社区查询相应building
            $unit = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $account]])[0]["UnitID"];
            $tmpType = 0;
        }

        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $account]]);
        $msgID = $data[0]["ParentID"];
        $expireTime = DEFAULTEXPIRETIME;
        $this->db->insert2List(
            $tabel,
            [
                ":Code" => $code,
                ":Node" => $account,
                ":CreateTime" => $now,
                ":ExpireTime" => $expireTime,
                ":AccountID" => $userId,
                ":UnitID" => $unit,
                ":Type" => 1,
                ":Grade" => 3,
                ":MngAccountID" => $msgID
            ]
        );
        $id = $this->db->lastInsertId();
        \util\computed\setGAppData(["ID" => $id, "Type" => $this->type, "IsPer" => $tmpType]);
        \util\computed\setGAppData(["Account" => $account]);
    }

    public function addRF()
    {
        global $gApp;
        $role = $gApp["role"];
        $params = [
            "userAlias" => "",
            "Code" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $code = $params["Code"];
        $this->type = 1;
        $this->addCom($user);
        if ($role === RPERENDMROLE) {
            $this->log->endUserLog(3, null, "add RF card:$code");
        }
        $this->auditLog->setLog(AuditCodeAddRf, $this->env, [$code], $user);
    }

    public function addPri()
    {
        global $gApp;
        $role = $gApp["role"];
        $params = [
            "userAlias" => "",
            "Code" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $code = $params["Code"];
        $this->type = 2;
        $this->addCom($user);
        if ($role === RPERENDMROLE) {
            $this->log->endUserLog(3, null, "add private key:$code");
        }

        $this->auditLog->setLog(AuditCodeAddPin, $this->env, [$code], $user);
    }

    public function addPriFCom()
    {
        $this->type = 2;
        $this->addFCom();
    }

    public function addRFFCom()
    {
        $this->type = 1;
        $this->addFCom();
    }

    public function addFCom()
    {
        global $cMessage;
        $params = [
            "RoomID" => "",
            "Code" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $room = $params["RoomID"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $room]]);
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateAccountNotExit);
        }
        $account = $data[0]["Account"];
        $code = $params["Code"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#key#addFCom#room=$room;account=$account;code=$code;userId=$userId");

        $data1 = $this->db->queryAllList(
            $this->tabelName[$this->type],
            ["equation" => [":Code" => $code, ":MngAccountID" => $userId]]
        );
        $tables = ["2" => "PubPrivateKey", "1" => "PubRfcardKey"];
        $data2 = $this->db->queryAllList(
            $tables[$this->type],
            ["equation" => [":Code" => $code, ":MngAccountID" => $userId]]
        );
        if (count($data1) != 0 || count($data2) != 0) {
            $cMessage->echoErrorMsg($this->type == 1 ? StateRFCardExit : StatePrivateKeyExists);
        }
        $this->addCom($account);
    }

    public function addTempCom(
        $allowedTimes,
        $description,
        $idNumber,
        $dateFlag,
        $startDay,
        $stopDay,
        $startTime,
        $stopTime,
        $schedulerType,
        $user,
        $mainAccount,
        $macs,
        $eachAllowedTimes
    ) {
        global $cMessage;
        $dateFlag = explode(";", $dateFlag);
        $tmpDate = 0;
        foreach ($dateFlag as $value) {
            $tmpDate += DATEFLAG[$value];
        }
        $dateFlag = $tmpDate ? $tmpDate : 0;
        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $mainAccount]])[0];

        $type = in_array($userData["Role"], PERROLE) ? 1 : 0;
        $mngAccountID = $userData["ParentID"];
        $unitID = $userData["UnitID"];

        if ($schedulerType == 0 || $schedulerType == 3) {
            $beginTime = "$startDay $startTime";
            $endTime = "$stopDay $stopTime";
        } else {
            $beginTime = null;
            $endTime = null;
        }

        $times = 0;

        while ($times < 10) {
            $times++;
            $code = floor((9 + \util\string\randFloat()) * ********);//tmpkey由系统生成
            if (!$this->db->isExistFiled("PubAppTmpKey", [":MngAccountID" => $mngAccountID, ":Code" => $code])
                && !$this->db->isExistFiled(
                    "PersonalAppTmpKey",
                    [":MngAccountID" => $mngAccountID, ":TmpKey" => $code]
                )) {
                break;
            }
        }
        if ($times >= 10) {
            $cMessage->echoErrorMsg(StateAddTmpKeyFail);
        }

        $this->db->insert2List(
            "PersonalAppTmpKey",
            [
                ":MngAccountID" => $mngAccountID,
                ":IDNumber" => $idNumber,
                ":TmpKey" => $code,
                ":UnitID" => $unitID,
                ":Type" => $type,
                ":Node" => $mainAccount,
                ":Creator" => $user,
                ":DateFlag" => $dateFlag,
                ":SchedulerType" => $schedulerType,
                ":BeginTime" => $beginTime,
                ":EndTime" => $endTime,
                ":AllowedTimes" => $allowedTimes,
                ":Description" => $description,
                ":StartTime" => $startTime,
                ":StopTime" => $stopTime,
                ":EachAllowedTimes" => intval($eachAllowedTimes),
            ]
        );
        $id = $this->db->lastInsertId();
        foreach ($macs as $value) {
            $relay = $value["Relay"];
            $relays = explode(";", $relay);
            $relay = 0;
            foreach ($relays as $val) {
                $relay += \util\computed\getRelayValue($val);
            }

            $securityRelays = explode(';', $value['SecurityRelay']);
            $securityRelay = 0;
            foreach ($securityRelays as $val) {
                $securityRelay += \util\computed\getRelayValue($val);
            }

            $this->db->insert2List(
                "PersonalAppTmpKeyList",
                [":MAC" => $value["MAC"], ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":KeyID" => $id]
            );
        }
        \util\computed\setGAppData(["ID" => $id, "Code" => $code, "Node" => $mainAccount]);
        $this->auditLog->setLog(AuditCodeAddTemp, $this->env, [$code], $mainAccount);
        // createTmpKeyQrCodeUrl($mainAccount,$code,$id,$type);
    }

    public function addTemp()
    {
        $params = [
            "DateFlag" => "",
            "StartDay" => "",
            "StopDay" => "",
            "StartTime" => "",
            "StopTime" => "",
            "SchedulerType" => "",
            "AllowedTimes" => "",
            "Description" => "",
            "IDNumber" => "",
            "userAlias" => "",
            "user" => "",
            "MAC" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $dateFlag = $params["DateFlag"];
        $startDay = $params["StartDay"];
        $stopDay = $params["StopDay"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $schedulerType = $params["SchedulerType"];
        $allowedTimes = $params["AllowedTimes"];
        $description = $params["Description"];
        $idNumber = $params["IDNumber"];
        $user = $params["userAlias"];
        $self = $params["user"];
        $macs = $params["MAC"];

        $eachAllowedTimes = 0;
        if ($schedulerType === "3") {
            $eachAllowedTimes = $allowedTimes;
//            $relayNum = 0;
//            foreach ($macs as $mac) {
//                $relayNum += count(explode(";", $mac['Relay']));
//            }
//            $allowedTimes = intval($allowedTimes) * $relayNum;
            $allowedTimes = intval($allowedTimes) * count($macs);
        }

        $this->log->actionLog(
            "#model#key#addTemp#dateFlag=$dateFlag;startDay=$startDay;stopDay=$stopDay;startTime=$startTime;stopTime=$stopTime;schedulerType=$schedulerType;allowedTimes=$allowedTimes;
		description=$description;idNumber=$idNumber;user=$user;self=$self"
        );
        $this->addTempCom(
            $allowedTimes,
            $description,
            $idNumber,
            $dateFlag,
            $startDay,
            $stopDay,
            $startTime,
            $stopTime,
            $schedulerType,
            $self,
            $user,
            $macs,
            $eachAllowedTimes
        );
    }
}
