<?php
namespace model\access;

function checkValid($id, $communityId)
{
    global $db, $cMessage;
    $count = $db->querySList(
        'select count(*) from AccessGroup where CommunityID = :CommunityID and ID = :ID',
        [":CommunityID" => $communityId, ":ID"=>$id]
    )[0]["count(*)"];
    if ($count == 0) {
        $cMessage->echoErrorMsg(StateNotPermission);
    }
}

function checkNameUnique($name, $communityId, $id = null)
{
    global $db, $cMessage;
    $where = "";
    $bindArray = [":CommunityID"=>$communityId, ":Name"=>$name];
    if ($id) {
        $where = " and ID != :ID";
        $bindArray[":ID"] = $id;
    }
    $count = $db->querySList(
        'select count(*) from AccessGroup where CommunityID = :CommunityID and Name = :Name'.$where,
        $bindArray
    )[0]['count(*)'];

    if ($count != 0) {
        $cMessage->echoErrorMsg(StateAccessNameExist);
    }
}

function checkStartLessStopTime($params)
{
    global $cMessage,$cLog;
    $startTime = $params["StartTime"];
    $stopTime = $params["StopTime"];
    $startDay = $params["StartDay"];
    $stopDay = $params["StopDay"];
    $schedulerType = $params["SchedulerType"];
    $cLog->actionLog('#model#access#checkStartLessStopTime#allow='.json_encode($params));
    if ($schedulerType == 0 && strtotime("$startDay 00:00:00") > strtotime("$stopDay 23:59:59")
    || $schedulerType != 0 && strtotime("2000-01-01 $startTime") > strtotime("2000-01-01 $stopTime")) {
        $cMessage->echoErrorMsg(StateEndThanStart);
    }
}

function checkAccessMacValid($device, $communityId)
{
    global $db,$cMessage;
    foreach ($device as $value) {
        $count = $db->querySList(
            'select count(*) from Devices where MAC = :MAC and MngAccountID = :MngAccountID',
            [':MAC'=> $value['MAC'], ':MngAccountID'=> $communityId]
        )[0]['count(*)'];
        if ($count == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }
}

/**
 * @name 获取用户对传入设备是否有权限
 * @param userId:主从ID皆可
 */
function checkAccessUserMacValid($device, $userId)
{
    global $db,$cMessage,$cLog;
    $data = $db->querySList('select Account,Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$userId])[0];
    $account = $data['Account'];
    if ($data['Role'] == 21) {
        $account = $db->querySList('select Account from PersonalAccount where ID = :ID', [":ID"=>$data["ParentID"]])[0]['Account'];
    }
    $cLog->actionLog('#model#access#checkAccessUserMacValid#device='.json_encode($device));
    foreach ($device as $value) {
        $count = $db->querySList(
            'select count(*) from Devices where MAC = :MAC and Node = :Node',
            [':MAC'=> $value['MAC'], ':Node'=> $account]
        )[0]['count(*)'];
        if ($count == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }
}

/**
 * @name 获取用户对传入权限组是否有权限
 * @param id:主从ID皆可
 */
function checkUserAccessValid($id, $accessGroup)
{
    global $cLog;
    $allow = getUserAllowAccess($id);
    foreach ($allow as &$val) {
        $val = $val["ID"];
    }
    unset($val);
    $cLog->actionLog("#model#access#checkUserAccessValid#id=$id;allow=".json_encode($allow)
    ."access=".json_encode($accessGroup));
    foreach ($accessGroup as $val) {
        if (!in_array($val, $allow)) {
            return $val;
        }
    }

    return true;
}

/**
 * @description:社区的pm是否有pub+private的权限
 * @author:lwj 2022/12/7 16:33 v6.5.2
 * @lastEditor: lwj 2022/12/7 16:33 v6.5.2
 * @throws \Exception
 */
function checkCommunityHaveAccessArea($communityId){
    global $db, $cMessage;
    $info = $db->querySList(
        'select Grade from Account where ID = :ID',
        [':ID'=> $communityId]
    )[0];
    if($info['Grade'] != OFFICEGRADE){
        $info = $db->querySList(
            'select Switch from CommunityInfo where AccountID = :AccountID',
            [':AccountID'=> $communityId]
        )[0];
        //是否开启了pub+private权限
        if(\util\computed\getSpecifyBitLE($info['Switch'], 7) !== 1){
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }
}

function checkUserValid($id, $communityId)
{
    global $db, $cMessage;
    $data = $db->querySList('select Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
    $mainId = $id;
    if ($data['Role'] == 21) {
        $mainId = $data['ParentID'];
        $data = $db->querySList('select ParentID from PersonalAccount where ID = :ID', [":ID"=>$data["ParentID"]])[0];
    }

    if ($communityId != $data['ParentID']) {
        $cMessage->echoErrorMsg(StateNotPermission);
    }
    return $mainId;
}

function dealDataFlag($dateFlag, $schedulerType)
{
    $dateFlag = explode(";", $dateFlag);
    $tmpDate = 0;
    foreach ($dateFlag as $value) {
        $tmpDate += DATEFLAG[$value];
    }
    $dateFlag = $tmpDate;
    if ($dateFlag == 127 && $schedulerType == 2) {
        $schedulerType = 1;
    }

    return [$dateFlag, $schedulerType];
}

function getCommunityId($id)
{
    global $db;
    $data = $db->querySList('select Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
    $communityId = $data['ParentID'];
    if ($data['Role'] == 21) {
        $communityId = $db->querySList('select ParentID from PersonalAccount where ID = :ID', [":ID"=>$communityId])[0]['ParentID'];
    }

    return $communityId;
}

function getRelay($relay)
{
    $relays = explode(";", $relay);
    $relay = 0;
    foreach ($relays as $value) {
        $relay += \util\computed\getRelayValue($value);
    }
    return $relay;
}

function getRelayKeyPosition($relay)
{
    $relay = intval($relay);
    $relayStatus = [1, 2, 4, 8];
    $result = [];
    foreach ($relayStatus as $key => $value) {
        if (($relay & $value) == $value) {
            array_push($result, $key);
        }
    }
    return implode(';', $result);
}

function getDateFlagPosition($dateFlag)
{
    return \util\computed\getDateScript($dateFlag);
}

function getAccessMACArray($id)
{
    global $db;
    $macs = $db->querySList('select MAC from AccessGroupDevice where AccessGroupID = :AccessGroupID', [":AccessGroupID"=>$id]);
    foreach ($macs as &$mac) {
        $mac = $mac['MAC'];
    }
    unset($mac);
    return $macs;
}

function getAccessDevicesLocation($id, $unitId, $communityId)
{
    global $db;
    if ($unitId == 0) {
        $devices = $db->querySList(
            'select D.Location from Devices D join AccessGroupDevice A on D.MAC = A.MAC where AccessGroupID = :AccessGroupID',
            [":AccessGroupID" => $id]
        );
    } else {
        $devices = $db->querySList(
            'select Location from Devices where UnitID = :UnitID and Grade = 2 and Type in (0, 1, 50)
            union select D.Location from Devices D join PubDevMngList P on D.ID = P.DevicesID where P.UnitID=:UnitID and Type in (0, 1, 50)
            union select Location from Devices where MngAccountID=:MngAccountID and (Flags >> 3) & 1 = 1 and Grade = 1 and Type in (0, 1, 50)',
            [':UnitID'=>$unitId, ':MngAccountID'=> $communityId]
        );
    }
    foreach ($devices as &$device) {
        $device = $device['Location'];
    }
    unset($device);
    $devices = implode(';', $devices);
    return $devices;
}

function getAllPersonInAccess($id, $searchKey, $searchValue)
{
    global $db;
    // 搜索房间，物业和快递直接为空
    if ($searchKey == 'RoomName' && $searchValue != '') {
        $delivery = [];
        $staff = [];
    } else {
        $where = '';
        $bindArray = [':AccessGroupID'=>$id];
        if ($searchKey == 'Name') {
            $where = ' and Name like :Name';
            $bindArray[':Name'] = "%$searchValue%";
        }
        $delivery = $db->querySList(
            'select ID, "Delivery" as Type, Name, "" as UnitName, "" as RoomName from Delivery where
        ID in (select DeliveryID from DeliveryAccess where AccessGroupID = :AccessGroupID)'.$where,
            $bindArray
        );
        $staff = $db->querySList(
            'select ID, "Staff" as Type, Name, "" as UnitName, "" as RoomName from Staff where 
        ID in (select StaffID from StaffAccess where AccessGroupID = :AccessGroupID)'.$where,
            $bindArray
        );
    }

    $where = '';
    $bindArray = [':AccessGroupID'=>$id];
    switch ($searchKey) {
        case 'RoomName':
            $where = ' and CR.RoomName like :RoomName';
            $bindArray[':RoomName'] = "%$searchValue%";
            break;
        case 'Name':
            $where = ' and P.Name like :Name';
            $bindArray[':Name'] = "%$searchValue%";
            break;
    }

    $account = $db->querySList('select P.ID, "User" as Type, P.Name, CR.RoomName, CU.UnitName from PersonalAccount P join CommunityRoom CR on P.RoomID = CR.ID
    join CommunityUnit CU on P.UnitID = CU.ID where P.Account in (select Account from AccountAccess where AccessGroupID = :AccessGroupID) and P.Role = 20'.$where.'
    union select P.ID, "User" as Type, P.Name, CR.RoomName, CU.UnitName from PersonalAccount P join PersonalAccount P2 on P.ParentID = P2.ID join CommunityRoom CR on P2.RoomID = CR.ID
    join CommunityUnit CU on P2.UnitID = CU.ID where P.Account in (select Account from AccountAccess where AccessGroupID = :AccessGroupID) and P.Role = 21'.$where, $bindArray);

    return [$delivery, $staff, $account];
}

function getPersonNotInAccess($id, $searchKey = '', $searchValue = '')
{
    global $db;
    // 先查找属于哪个社区的权限组
    $accessData = $db->querySList('select CommunityID from AccessGroup where ID = :ID', [':ID'=>$id])[0];
    $communityId = $accessData['CommunityID'];

    if ($searchKey == 'RoomName' && $searchValue != '') {
        $delivery = [];
        $staff = [];
    } else {
        $where = '';
        $bindArray = [':AccessGroupID'=>$id, ':CommunityID'=>$communityId];
        if ($searchKey == 'Name') {
            $where = ' and Name like :Name';
            $bindArray[':Name'] = "%$searchValue%";
        }
        $delivery = $db->querySList(
            'select ID, Name, "Delivery" as Type, Name, "" as UnitName, "" as RoomName from Delivery where 
        ID not in (select DeliveryID from DeliveryAccess where AccessGroupID = :AccessGroupID) and CommunityID=:CommunityID'.$where,
            $bindArray
        );
        $staff = $db->querySList(
            'select ID, Name, "Staff" as Type, "" as UnitName, "" as RoomName from Staff where 
        ID not in (select StaffID from StaffAccess where AccessGroupID = :AccessGroupID) and CommunityID=:CommunityID'.$where,
            $bindArray
        );
    }
    
    $account = getAllowAccessAccount($id, $searchKey, $searchValue);
    // 删除已经在access里面的account

    $inAccount = $db->querySList('select Account from AccountAccess where AccessGroupID = :AccessGroupID', [':AccessGroupID' => $id]);
    foreach ($inAccount as &$val) {
        $val = $val['Account'];
    }
    unset($val);
    $resident = [];
    foreach ($account as $value) {
        if (!in_array($value['Account'], $inAccount)) {
            array_push($resident, $value);
        }
    }

    return [$delivery, $staff, $resident];
}

function getAllowAccessAccount($id, $searchKey = '', $searchValue = '')
{
    global $db;
    $where = ' and P.Special = 0 ';
    $bindArray = [];
    $people = [];
    switch ($searchKey) {
        case 'RoomName':
            $where .= ' and CR.RoomName like :RoomName';
            $bindArray[':RoomName'] = "%$searchValue%";
            break;
        case 'Name':
            $where .= ' and P.Name like :Name';
            $bindArray[':Name'] = "%$searchValue%";
            break;
    }
    $accessData = $db->querySList('select UnitID, CommunityID from AccessGroup where ID = :ID', [":ID"=>$id])[0];
    $unitID = $accessData['UnitID'];
    $communityID = $accessData['CommunityID'];
    // 6.3 办公权限组办公人员区别社区人员
    $projectGrade = $db->querySList('select Grade from Account where ID = :ID', [':ID'=>$communityID])[0]['Grade'];
    if ($projectGrade == OFFICEGRADE) {
        // 6.3 办公默认staff具有所有权限
        $people = $db->querySList("select ID,Account from PersonalAccount where ParentID = :ParentID and Role = 31", [':ParentID'=>$communityID]);
    } else {
        $people = [];
    }
    if ($unitID == 0) {
        // 用户是否能添加到这个权限组的条件是权限组内设备用户都有权限开
        $devices = $db->querySList(
            'select D.ID, D.UnitID, D.Grade, D.Flags from Devices D join AccessGroupDevice A on D.MAC = A.MAC where AccessGroupID = :AccessGroupID',
            [":AccessGroupID" => $id]
        );

        $unitId = 0;
        $pubDevs = [];
        foreach ($devices as $device) {
            if ($device['Grade'] == 2) {
                if ($unitId == 0) {
                    $unitId = $device['UnitID'];
                } elseif ($unitId != $device['UnitID']) {
                    return $people;
                }
            } else {
                array_push($pubDevs, $device);
            }
        }

        $unitIds = [];
        foreach ($pubDevs as $key => $pubDev) {
            if (\util\computed\getSpecifyBit($pubDev['Flags'], 3)) {
                $units = $db->querySList('select ID as UnitID from CommunityUnit where MngAccountID=:MngAccountID', [':MngAccountID'=>$communityID]);
            } else {
                $units = $db->querySList('select UnitID from PubDevMngList where DevicesID = :DevicesID', [":DevicesID"=>$pubDev['ID']]);
            }
            foreach ($units as &$val) {
                $val = $val['UnitID'];
            }
            unset($val);
            if ($key == 0) {
                $unitIds = $units;
            } else {
                // 外围公共设备的单元取交集
                $unitIds = array_intersect($unitIds, $units);
            }
        }
        $unitIds = $unitId == 0
            ? $unitIds
            : (count($pubDevs) == 0 || in_array($unitId, $unitIds) ? [$unitId] : []);
        if (count($unitIds) == 0) {
            return $people;
        }
    } else {
        $unitIds = [$unitID];
    }
    
    if ($projectGrade == COMMUNITYGRADE) {
        $account = $db->querySList('select P.ID, P.Account, P.Name, "User" as Type, CR.RoomName, CU.UnitName from PersonalAccount P join CommunityRoom CR on P.RoomID = CR.ID
        join CommunityUnit CU on P.UnitID = CU.ID where P.UnitID in ('.implode(',', $unitIds).') and P.Role = 20 '.$where.'
        union select P.ID, P.Account, P.Name, "User" as Type, CR.RoomName, CU.UnitName from PersonalAccount P join PersonalAccount PM on P.ParentID = PM.ID 
        join CommunityRoom CR on PM.RoomID = CR.ID join CommunityUnit CU on PM.UnitID = CU.ID where PM.UnitID in ('.implode(',', $unitIds).') and P.Role = 21'.$where, $bindArray);
    } elseif ($projectGrade == OFFICEGRADE) {
        // 6.3 这边主要的目的是验证移入移出权限组，只需要Account,ID参数就行，办公权限组列表在新框架下
        $account = $db->querySList('select ID,Account from PersonalAccount where Role = 31 and ParentID = :ParentID union all 
        select ID,Account from PersonalAccount where UnitID in ('.implode(',', $unitIds).') and Role = 30 and ParentID = :ParentID', [':ParentID' => $communityID]);
    }

    return $account;
}

function getUserAllowAccess($id, $data = [])
{
    global $db;
    if ($id) {
        $data = $db->querySList('select Account,Role,ParentID,UnitID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $account = $data['Account'];
        // 本身拥有的权限组
        $oldAccess = $db->querySList('select AccessGroupID from AccountAccess where Account=:Account', [":Account"=>$account]);
    } else {
        $oldAccess = [];
    }
    $unitId = $data['UnitID'];
    $communityId = $data['ParentID'];
    if ($data['Role'] == 21) {
        $data = $db->querySList('select Account,ParentID from PersonalAccount where ID = :ID', [":ID"=>$data["ParentID"]])[0];
        $communityId = $data['ParentID'];
    }

    // 楼栋权限组
    $unitAccess = $db->querySList('select ID from AccessGroup where UnitID = :UnitID', [":UnitID"=>$unitId]);
    // 查询所有不被支持的设备
    if ($data['Role'] == OFFSTAFFROLE) {
        // 6.3 办公权限组，支持所有设备
        $devices = [];
        // Staff具有全部部门的权限组
        $unitAccess = $db->querySList('select ID from AccessGroup where CommunityID = :CommunityID', [":CommunityID"=>$communityId]);
    } else {
        $devices = $db->querySList(
            'select MAC from Devices where UnitID != :UnitID and Grade = 2 and MngAccountID = :MngAccountID
        union select MAC from Devices where MngAccountID = :MngAccountID and Grade = 1 and (Flags >> 3) & 1 = 0 and MAC not in
        (select distinct D.MAC from Devices D join PubDevMngList P on D.ID = P.DevicesID where P.UnitID = :UnitID and D.MngAccountID = :MngAccountID and Grade = 1)',
            [":UnitID"=>$unitId, ":MngAccountID"=>$communityId]
        );
    }

    foreach ($devices as &$val) {
        $val = "'".$val["MAC"]."'";
    }
    unset($val);
    // 查询社区下不包含不被支持的设备的权限组
    if (count($devices) == 0) {
        $access = $db->querySList(
            'select ID from AccessGroup where CommunityID = :CommunityID and UnitID=0',
            [':CommunityID'=>$communityId]
        );
    } else {
        $access = $db->querySList(
            'select ID from AccessGroup where ID not in (select distinct AccessGroupID from AccessGroupDevice where MAC in ('.implode(',', $devices).') ) and CommunityID = :CommunityID and UnitID=0',
            [":CommunityID"=>$communityId]
        );
    }

    $result = [];
    $accessResult = [];
    foreach ($oldAccess as $val) {
        array_push($accessResult, $val["AccessGroupID"]);
        // array_push($result, $db->querySList('select * from AccessGroup where ID = :ID', [":ID"=>$val["AccessGroupID"]])[0]);
    }
    foreach ($access as $val) {
        array_push($accessResult, $val["ID"]);
        // array_push($result, $db->querySList('select * from AccessGroup where ID = :ID', [":ID"=>$val["ID"]])[0]);
    }
    foreach ($unitAccess as $val) {
        array_push($accessResult, $val["ID"]);
    }
    $accessResult = array_unique($accessResult);
    foreach ($accessResult as $val) {
        array_push($result, $db->querySList('select * from AccessGroup where ID = :ID', [":ID"=>$val])[0]);
    }


    return $result;
}

function setAccessTimeFormat($times, $customizeForm)
{
    $begin = \util\time\setCustomizeFormat(strtotime($times["BeginTime"]), $customizeForm);
    $end = \util\time\setCustomizeFormat(strtotime($times["EndTime"]), $customizeForm);
    // kangxiaolong 2021-03-25 修改 12小时制会被删除掉后面AM PM的bug
    $startTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$times["StartTime"]), $customizeForm));
    $endTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$times["StopTime"]), $customizeForm));
    array_shift($startTimes);
    array_shift($endTimes);
    $startTimes = implode(" ", $startTimes);
    $endTimes = implode(" ", $endTimes);

    return [$begin, $end, $startTimes, $endTimes];
}

function deleteAccess($id)
{
    global $db;
    $db->delete2ListWID('AccessGroup', $id);
    $db->delete2ListWKey('AccessGroupDevice', 'AccessGroupID', $id);
    $db->delete2ListWKey('AccountAccess', 'AccessGroupID', $id);
    $db->delete2ListWKey('StaffAccess', 'AccessGroupID', $id);
    $db->delete2ListWKey('DeliveryAccess', 'AccessGroupID', $id);
}

function addUserKeyCard($id, $pin, $card, $accessGroup, $env)
{
    global $db, $cAuditLog;
    $data = $db->querySList('select Account,Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
    $account = $data['Account'];
    $communityId = $data['ParentID'];
    if ($data['Role'] == 21) {
        $communityId = $db->querySList('select ParentID from PersonalAccount where ID = :ID', [":ID"=>$communityId])[0]['ParentID'];
    }
    if ($pin) {
        $db->insert2List('CommPerPrivateKey', [":Account"=>$account, ":Code"=>$pin, ":CommunityID"=>$communityId, ":CreateTime"=>\util\computed\getNow()]);
        $cAuditLog->setLog(AuditCodeAddPin, $env, [$pin], $account);
    }
    if ($card) {
        $db->insert2List('CommPerRfKey', [":Account"=>$account, ":Code"=>$card, ":CommunityID"=>$communityId, ":CreateTime"=>\util\computed\getNow()]);
        $cAuditLog->setLog(AuditCodeAddRf, $env, [$card], $account);
    }
    foreach ($accessGroup as $value) {
        $db->insert2List('AccountAccess', [":Account"=>$account, ":AccessGroupID"=>$value]);
    }
}
