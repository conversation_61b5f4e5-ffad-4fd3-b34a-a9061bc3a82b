<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */
namespace model;

use function PHPSTORM_META\type;

include_once __DIR__."/set.func.php";
const INDOOR_AND_APP = 0;
const TYPE_DELIVERY = 1;
const TYPE_TEMPKEY = 2;
const TYPE_JP = 3;
//电量通知
const TYPE_BATTERY_WARNING = [
    "4" => MSGTEXT["2weekBatteryWarning"],
    "5" => MSGTEXT["1weekBatteryWarning"],
    "6" => MSGTEXT["replaceBatteryWarning"]
];
class CMessage
{
    private $revicerType = MESSAGE_REVICER_TYPE;
    public function add()
    {
        $params = [
            "userAliasId"=>"",
            "Message"=>"",
            "MessageTitle"=>"",
            "Recevier"=>"",
            "isAllMessage"=>"",
            "ClientType"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $message = $params["Message"];
        $title = $params["MessageTitle"];
        $device = $params["Recevier"];
        $clientType = $params["ClientType"];
        addComControl($message, $title, $device, $clientType, $userId);
    }
    
    public function afterAdd()
    {
        //perNewTextMesage();
    }
    
    public function delete()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        deleteComControl($id, $userId);
    }
    
    public function betchdelete()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $ids = explode(";", $id);
        foreach ($ids as $id) {
            deleteComControl($id, $userId);
        }
    }
    
    public function getMessageForApp()
    {
        $params = [
            "userAliasId"=>"",
            "user"=>"",
            "userAlias"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $self = $params["user"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $personalInfo = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$self]])[0];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        $selectTime = $personalInfo['CreateTime'];
        //messageSql
        $messageSql = "select M.ID, M.Title, M.Content, M.CreateTime, M.Type, L.Status, 0 as `NoticeType`, '' as Location from Message M
            left join MessageAccountList L on M.ID = L.MessageID
            where M.CreateTime >= :time and L.ClientType = 2 and L.Account = :self";
        //语音Sql
        $voiceSql = "select L.ID,'' as Title,'' as Content, M.CreateTime, '' as Type, L.Status, 1 as `NoticeType`, M.Location from PersonalVoiceMsg M 
            left join PersonalVoiceMsgList L on M.UUID = L.MsgUUID
            where M.CreateTime >= :time and L.PersonalAccountUUID = :personalAccountUUID";

        $data = $this->db->querySList(
            "$messageSql union all $voiceSql order by CreateTime desc limit $offset,$rows",
            [":time"=>$selectTime, ":self"=>$self, ':personalAccountUUID' => $personalInfo['UUID']]
        );
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$value) {
            $value["NoticeType"] = $value["NoticeType"] === '0' ? 'msg': 'voiceMsg';
            $value["CreateTime"] = \util\time\setYesterday(\util\time\setTime($value["CreateTime"], $customizeForm), $timeZone);
            if($value["NoticeType"] === 'voiceMsg'){
                $value['Title'] = MSGTEXT['voiceTitle'];
                $value['Content'] = vsprintf(MSGTEXT['voiceContent'], [$value['Location']]);
            }else{
                if ($value["Type"] == TYPE_DELIVERY) {
                    $value["Content"] = vsprintf(MSGTEXT["deliveryMsg"], $value["Content"]);
                    $value["Title"] = MSGTEXT["deliveryTitle"];
                } elseif ($value["Type"] == TYPE_TEMPKEY) {
                    $value["Title"] = MSGTEXT["tempkeyUsed"];
                    $value["Content"] = vsprintf(MSGTEXT["tempkeyContent"], $value["Content"]);
                } elseif ($value["Type"] == TYPE_JP) {
                    $content = json_decode($value["Content"], true);
                    $aptNum = $content['AptNum'];
                    $boxNum = $content['BoxNum'];
                    $boxPwd = $content['BoxPwd'];
                    $value["Content"] = vsprintf("%s号室様。宅配ボックス%sに荷物をお預けしました。暗証番号は、%sです。", [$aptNum, $boxNum, $boxPwd]);
                    $value["Title"] = "新しい荷物があります！";
                }elseif (array_key_exists($value["Type"], TYPE_BATTERY_WARNING)) {
                    $value["Title"] = MSGTEXT["yaleBatteryWarning"];
                    // Content如果type=4,5,6时代表电量剩余情况 4-剩余two week 5-剩余one weeks 6-需更换电池
                    $value["Content"] = vsprintf(TYPE_BATTERY_WARNING[$value['Type']], $value["Content"]);
                }
            }
            unset($value['Location']);
        }
        unset($value);
        \util\computed\setGAppData(["data"=>$data]);
    }
    
    public function getDetail()
    {
        $params = [
            "ID"=>"",
            "user"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "NoticeType"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $self = $params["user"];
        //voiceMsg语音留言类型
        if($params['NoticeType'] === 'voiceMsg'){
            $info = $this->getVoiceMsgInfo($id, $self);
        }else if($params['NoticeType'] === 'msg'){
            $msgList = $this->db->queryAllList("MessageAccountList", ["equation"=>[":Account"=>$self, ":MessageID"=>$id, ":ClientType"=>2]])[0];
            $msgListId = $msgList['ID'];
            $unread =  $msgList['Status'];
            $data = $this->db->queryAllList("Message", ["equation"=>[":ID"=>$id]]);
            $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
            $data[0]['Unread'] = $unread;
            foreach ($data as &$value) {
                $value["CreateTime"] = \util\time\setTime($value["CreateTime"], $customizeForm);
                if ($value["Type"] == TYPE_DELIVERY) {
                    $value["Title"] = MSGTEXT["deliveryTitle"];
                    $value["Content"] = vsprintf(MSGTEXT["deliveryMsg"], $value["Content"]);
                } elseif ($value["Type"] == TYPE_TEMPKEY) {
                    $value["Title"] = MSGTEXT["tempkeyUsed"];
                    $value["Content"] = vsprintf(MSGTEXT["tempkeyContent"], $value["Content"]);
                } elseif ($value["Type"] == TYPE_JP) {
                    $content = json_decode($value["Content"], true);
                    $aptNum = $content['AptNum'];
                    $boxNum = $content['BoxNum'];
                    $boxPwd = $content['BoxPwd'];
                    $value["Content"] = vsprintf("%s号室様。宅配ボックス%sに荷物をお預けしました。暗証番号は、%sです。", [$aptNum, $boxNum, $boxPwd]);
                    $value["Title"] = "新しい荷物があります！";
                }elseif (array_key_exists($value["Type"], TYPE_BATTERY_WARNING)) {
                    $value["Title"] = MSGTEXT["yaleBatteryWarning"];
                    // Content如果type=4,5,6时代表电量剩余情况 4-剩余two week 5-剩余one weeks 6-需更换电池
                    $value["Content"] = vsprintf(TYPE_BATTERY_WARNING[$value['Type']], $value["Content"]);
                }
            }
            $this->db->update2ListWID("MessageAccountList", [":ID"=>$msgListId, ":Status"=>1]);
            $info = $data[0];
        }
        \util\computed\setGAppData(["data"=> $info]);
    }

    /**
     * @description:语音留言详情 v6.5.2
     * @author:lwj 2022/9/26 14:46
     * @lastEditor: lwj 2022/9/26 14:46
     * @param $id
     * @param $timeZone
     * @param $customizeForm
     * @return array|void
     * @throws \Exception
     */
    public function getVoiceMsgInfo($id, $user)
    {
        global $cMessage;
        $userInfo = $this->db->querySList("select UUID from PersonalAccount where Account =:User", [':User' => $user])[0];
        $msgList = $this->db->queryAllList('PersonalVoiceMsgList', ['equation' => [':ID' => $id, ':PersonalAccountUUID' => $userInfo['UUID']]])[0];
        if (empty($msgList)) {
            return $cMessage->echoErrorMsg(StateVoiceMsgInvalid);
        }
        $data = $this->db->queryAllList('PersonalVoiceMsg', ['equation' => [':UUID' => $msgList['MsgUUID']]]);
        if (empty($data)) {
            return $cMessage->echoErrorMsg(StateVoiceMsgInvalid);
        }

        $data[0]['FileUrl'] = $this->getUrl($data[0]['FileUrl']);
        $data[0]['PicUrl'] = $this->getUrl($data[0]['PicUrl']);

        $unread = $msgList['Status'];
        //语音消息已读
        $this->db->update2ListWID('PersonalVoiceMsgList', [':ID' => $id, ':Status' => 1]);
        return ['FileUrl' => $data[0]['FileUrl'], 'PicUrl' => $data[0]['PicUrl'], 'Unread' => $unread] ;
    }

    /**
     * @description:获取文件地址
     * @author:lwj 2022/10/27 16:13 V6.5.2
     * @lastEditor: lwj 2022/10/27 16:13 V6.5.2
     * @param $url
     * @return string
     */
    public function getUrl($url)
    {
        $ip = $_SERVER["REMOTE_ADDR"];
        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }
        return  \util\computed\computedImageLink($url, $ipFix);
    }
    
    public function deleteForApp()
    {
        $params = [
            "ID"=>"",
            "VoiceMsgID"=>"",
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $self = $params["userAlias"];
        $this->log->actionLog("#model#message#deleteForApp#params=".json_encode($params));
        $ids = explode(";", $id);
        if(!empty($ids)){
            betchDeleteControl($ids, $self);
        }
        //语音留言删除
        $ids = explode(";", $params['VoiceMsgID']);
        if(!empty($ids)){
            batchDeleteVoiceMsgControl($ids, $self);
        }
    }

    public function queryForMng()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $where = "";
        $bindArray = [":AccountID"=>$userId];
        if ($serchKey == "Message") {
            $where = " AND P.Content like :Key";
            $bindArray[":Key"] = "%$serchValue%";
        } elseif ($serchKey == "Receiver") {
            $where = " AND P.NickNames like :Key";
            $bindArray[":Key"] = "%$serchValue%";
        }

        $total = $this->db->querySList("select count(*) from Message P where P.AccountID = :AccountID $where", $bindArray)[0]['count(*)'];
        $data = $this->db->querySList("select * from Message P where P.AccountID = :AccountID $where order by P.ID desc limit $offset, $rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

        $rows = [];
        foreach ($data as $value) {
            $curr = [];
            $curr['ID'] = $value['ID'];
            $curr['CreateTime'] = $value['CreateTime'];
            $curr['Message'] = $value['Content'];
            $curr['Title'] = $value['Title'];

            $curr['NickNames'] = $value['NickNames'];
            $curr['Type'] =  $this->revicerType[$value['ReceiverType']];
            array_push($rows, $curr);
        }

        \util\computed\setGAppData(["data"=>["total"=>$total,"row"=>$rows,"detail"=>$rows]]);
    }

    public function queryPerUser()
    {
        $params = [
            "userAliasId"=>"",
            "Key"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $key = $params["Key"];
        $data = $this->db->querySList("select P.Account,P.Email,P.Name as Name2 from PersonalAccount P 
		where P.Role = 10 AND P.ParentID = :ParentID AND (P.Account like :Key or P.Email like :Key or P.Name like :Key)", [":Key"=>"%$key%",":ParentID"=>$userId]);
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function queryComUser()
    {
        $params = [
            "userAliasId"=>"",
            "Key"=>"",
            "Build"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $key = $params["Key"];
        $build = $params["Build"] ?: "all";
        $data = $this->db->querySList("select P.Account,P.Email,P.Name as Name2,U.UnitName,R.RoomName,R.Floor from PersonalAccount P left join CommunityUnit U on P.UnitID = U.ID left join CommunityRoom R on R.ID = P.RoomID
		where P.Role = 20 AND P.ParentID = :ParentID AND (P.Account like :Key or P.Email like :Key or P.Name like :Key)".($build == "all" ? "" :" and P.UnitID = $build"), [":Key"=>"%$key%",":ParentID"=>$userId]);
        foreach ($data as &$val) {
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
        }
        unset($val);
        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @description 将网页版message记录整理成app的message格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatMessage()
    {
        $params = [
            "data" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $params['data'];

        if (!empty($data)) {
            foreach ($data['row'] as &$val) {
                $val['CreateTime'] = \util\time\setYesterday(\util\time\setTime($val['CreateTime'], $customizeForm), $timeZone);
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $data]);
    }
}
