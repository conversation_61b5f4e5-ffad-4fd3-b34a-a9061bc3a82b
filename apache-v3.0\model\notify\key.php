<?php
namespace model\notify;

const RESIDENT = '0';
const DELIVERY = '1';
const STAFF = '2';
trait key
{
    public function priKeyPerUpdate()
    {
        $params = [
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["Account"];
        $this->log->actionLog("#model#notify#priKeyPerUpdate#user=$user");
        \webPersonalModifyNotify(WEB_PER_UPDATE_PIN, $user);
    }

    public function rfCardPerUpdate()
    {
        $params = [
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["Account"];
        $this->log->actionLog("#model#notify#rfCardPerUpdate#user=$user");
        \webPersonalModifyNotify(WEB_PER_UPDATE_RF, $user);
    }


    public function priKeyComUpdate()
    {
        $params = [
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $accounts = $params["Account"];
        if (is_string($accounts)) {
            $accounts = [$accounts];
        }
        $this->log->actionLog("#model#notify#priKeyComUpdate#account=".json_encode($accounts));
        foreach ($accounts as $account) {
            // V6.5.1 可能存在账号已被删除，但是Key还存在的数据
            if (empty($account)) {
                break;
            }
            $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$account]])[0];
            if ($data["Role"] == COMENDSROLE) {
                $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$data["ParentID"]]])[0];
            }
            \webCommunityModifyNotify(WEB_COMM_UPDATE_PIN, $data["Account"], "", $data["ParentID"], $data["UnitID"]);
        }
    }

    public function rfCardComUpdate()
    {
        // 暂时无用，社区个人无法操作
        $params = [
            "Account"=>"",
            "DeleteAllRFCard"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $accounts = $params["Account"];
        $deleteAll = $params["DeleteAllRFCard"];
        if (is_string($accounts)) {
            $accounts = [$accounts];
        }
        $this->log->actionLog("#model#notify#rfCardComUpdate#account=".json_encode($accounts)."#deleteallrfcard=$deleteAll");
        foreach ($accounts as $account) {
            // V6.5.1 可能存在账号已被删除，但是Key还存在的数据
            if (empty($account)) {
                break;
            }
            $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$account]])[0];
            if ($deleteAll) {
                \webCommunityModifyNotify(WEB_COMM_DELETE_ALL_RF_CARD, "", "", $data["ParentID"], 0);
                break;
            } else {
                \webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $account, "", $data["ParentID"], $data["UnitID"]);
            }
        }
    }

    public function tempKeyAdd()
    {
        global $cMessage;
        $params = [
            "Code"=>"",
            "ID"=>"",
            "Node"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $node = $params["Node"];
        $code = $params["Code"];
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#tempKeyAdd#node=$node;code=$code;id=$id");
        $res = \webTempKeyNotify(1, $node, $code, $id);
        if ($res === false) {
            $cMessage->echoErrorMsg(StateAddFail);
        }
    }


    public function tempKeyDelete()
    {
        $params = [
            "ID"=>"",
            "Account"=>"",
            "keyUrl"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $node = $params["Account"];
        $keyUrl = $params['keyUrl'];
        $this->log->actionLog("#model#notify#tempKeyDelete#node=$node;id=$id");
        \webTempKeyNotify(3, $node, "", $id, $keyUrl);
    }

    public function priKeyComUpdateForPM()
    {
        $params = [
            "macs"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $macs = $params["macs"];
        $communityId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#priKeyComUpdateForPM#mac=$macs");
        $macs = explode(";", $macs);

        $communityDev = [];
        $unitDev = [];
        $userDev = [];
        foreach ($macs as $val) {
            $data = $this->db->queryAllList("Devices", ["equation"=>[":MAC"=>$val]])[0];
            $grade = $data["Grade"];
            if ($grade == 1) {
                array_push($communityDev, $val);
            } elseif ($grade == 2) {
                if (!array_key_exists($data["UnitID"], $unitDev)) {
                    $unitDev[$data["UnitID"]] = [];
                }
                array_push($unitDev[$data["UnitID"]], $val);
            } else {
                if (!array_key_exists($data["Node"], $unitDev)) {
                    $unitDev[$data["Node"]] = [];
                }
                array_push($userDev[$data["Node"]." ".$data["UnitID"]], $val);
            }
        }
        
        if (count($communityDev) != 0) {
            \webCommunityModifyNotify(WEB_COMM_PUB_UPDATE_PIN, "", implode(";", $communityDev), $communityId);
        }
        foreach ($unitDev as $unitId=>$dev) {
            \webCommunityModifyNotify(WEB_COMM_UNIT_UPDATE_PIN, "", implode(";", $dev), $communityId, $unitId);
        }
    }

    public function rfCardComUpdateForPM()
    {
        $params = [
            "macs"=>"",
            "userAliasId"=>"",
            "DeleteAllRFCard"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $macs = $params["macs"];
        $deleteAll = $params["DeleteAllRFCard"];
        $communityId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#rfCardComUpdateForPM#mac=$macs");
        $macs = explode(";", $macs);

        if ($deleteAll) {
            \webCommunityModifyNotify(WEB_COMM_DELETE_ALL_RF_CARD, "", "", $communityId, 0);
        } else {
            $communityDev = [];
            $unitDev = [];
            $userDev = [];
            foreach ($macs as $val) {
                $data = $this->db->queryAllList("Devices", ["equation"=>[":MAC"=>$val]])[0];
                $grade = $data["Grade"];
                if ($grade == 1) {
                    array_push($communityDev, $val);
                } elseif ($grade == 2) {
                    if (!array_key_exists($data["UnitID"], $unitDev)) {
                        $unitDev[$data["UnitID"]] = [];
                    }
                    array_push($unitDev[$data["UnitID"]], $val);
                } else {
                    if (!array_key_exists($data["Node"], $unitDev)) {
                        $unitDev[$data["Node"]] = [];
                    }
                    array_push($userDev[$data["Node"]." ".$data["UnitID"]], $val);
                }
            }

            \webCommunityModifyNotify(WEB_COMM_PUB_UPDATE_RF, "", implode(";", $communityDev), $communityId);
            foreach ($unitDev as $unitId=>$dev) {
                \webCommunityModifyNotify(WEB_COMM_UNIT_UPDATE_RF, "", implode(";", $dev), $communityId, $unitId);
            }
        }

        // foreach($userDev as $nodeUnit=>$dev) {
        //     list($node,$unitId) = explode(" ",$nodeUnit);
        //     \webCommunityModifyNotify(WEB_COMM_UPDATE_RF,$node,implode(";",$dev),$communityId,$unitId);
        // }
    }

    // libarary修改pin
    public function pinOrCardComUpdateForPM()
    {
        $params = [
            "Account"=>"",
            "communityId"=>"",
            "deliveryIds"=>"",
            "staffIds"=>"",
            "accessGroupsId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#pinOrCardComUpdateForPM#params=".json_encode($params));
        $accounts = $params["Account"];
        $deliveryIDs = $params["deliveryIds"];
        $staffIDs = $params["staffIds"];
        $communityId = $params["communityId"];
        $accessGroupsId = $params["accessGroupsId"];

        if (count($accounts) != 0) {
            \webCommunityAccountModifyNotify($communityId, $accounts, 0);
        }
        if (count($deliveryIDs) != 0 || count($staffIDs) != 0) {
            \webCommunityPersonalModifyNotify($communityId, $staffIDs, $deliveryIDs, count($accessGroupsId)==0 ? 0:$accessGroupsId);
        }
    }

    public function importCardForPM()
    {
        $params = [
            "Account"=>"",
            "communityId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#pinOrCardComUpdateForPM#params=".json_encode($params));
        $accounts = $params["Account"];
        $communityId = $params["communityId"];

        \webCommunityImportAccountDatasNotify($communityId, $accounts);
    }

    public function tempKeyAddForPM()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Email"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#tempKeyAddForPM#Email=$email;id=$id");
        
        $res = shareTmpkeyEmailNotify($email, $id, 0);
        if ($res === false) {
            $cMessage->echoErrorMsg(StateAddFail);
        }
    }

    public function tempKeyShare()
    {
        $params = [
            "ID"=>"",
            "Email"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params["Email"];
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#tempKeyShare#Email=$email;id=$id");

        //是否pm app的分享带下划线的id
        if (strpos($id,'_') !== false){
            list($type, $id) = explode("_", $id);
            if ($type == 1) {
                shareTmpkeyEmailNotify($email, $id, 0);
            } else {
                shareTmpkeyEmailNotify($email, $id, 1);
            }
        } else {
            shareTmpkeyEmailNotify($email, $id, 1);
        }
    }
}
