<?php
/**
 * @description:
 * @author: csc 2023/12/15 13:52 V6.7.1
 * @lastEditors: csc 2023/12/15 13:52 V6.7.1
 */

namespace package\common\model\subscription\src;

use package\common\model\subscription\config\Code;

trait Get
{
    /**
     * @description: 计算订阅金额
     * @param {array} Users 续费的用户列表,getSubscribeUserInfo获取的数据
     * @param {int} IntervalType 周期类型 0:月 1:季 2:年 3:天
     * @param {int} Type 订阅类型 1: 单住户续费 2: 社区续费 3: 办公续费
     * @return void
     * @throws \Exception
     * @author: csc 2023/12/15 14:14 V6.7.1
     * @lastEditors: csc 2023/12/15 14:14 V6.7.1
     */
    public function computedPrice()
    {
        //获取参数
        $params = ['Users', 'IntervalType:enum("0","1","2","3")', 'Type:enum(1,2,3)', 'SubscriptionUUID?:or-rule("uuid","string-empty")'];
        list($users, $intervalType, $type, $subscriptionUUID) = $this->getParams($params);

        //检测apt项目是否已经在有效订阅中
        $personalAccountUUIDs = array_column($users['all'], 'UUID');
        $existsItem = $this->utils->self->checkItemInSubscription($personalAccountUUIDs);

        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的apt是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的用户，并且不在当前订阅中
                $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
                if (empty($subscription)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
                }
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }

            if (!empty($existsItem)) {
                $this->loadUtil('account');
                $result = $this->utils->account->personalAccountSelectByArray([['UUID', $existsItem]], 'Name');
                $personalAccountName = implode(',', array_column($result, 'Name'));
                $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
            }
        }
        //检测订阅总金额
        $this->loadProvider('billsysUtil');
        $chargeService = $this->services->billsysUtil;
        list($userCharges, $price) = $chargeService->getSubscriptionChargeDataAndPrice($users, $intervalType, $type);

        return [
            'Price' => $price,
        ];
    }

    /**
     * @description: 计算视频存储的费用
     * @param: {string} Type 类型 1=单住户 2=社区 3=办公
     * @return int|mixed
     * @author: shoubin.chen 2024/10/30 14:37:57 V7.1.0
     * @lastEditor: shoubin.chen 2024/10/30 14:37:57  V7.1.0
     */
    public function computedVideoStoragePrice()
    {
        //获取参数
        $params = ['Type:enum(1,2,3)', 'IntervalType:enum("0","1","2","3")', 'VideoStorageInfo', 'SubscriptionUUID?:or-rule("uuid","string-empty")'];
        list($type, $intervalType, $videoStorageInfo, $subscriptionUUID) = $this->getParams($params);
        $price = 0;
        if (empty($videoStorageInfo)) {
            return $price;
        }

        //检测视频存储项目是否已经在有效订阅中
        $siteUUIDs = array_column($videoStorageInfo['site'], 'UUID');
        $existsItem = $this->utils->self->checkVideoStorageItemInSubscription($siteUUIDs);
        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的视频存储是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的用户，并且不在当前订阅中
                $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
                if (empty($subscription)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
                }
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkVideoStorageInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }

            if (!empty($existsItem)) {
                if ($type == 1) {
                    $this->loadUtil('account');
                    $result = $this->utils->account->personalAccountSelectByArray([['UUID', $existsItem]], 'Name');
                    $personalAccountName = implode(',', array_column($result, 'Name'));
                    $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
                } elseif ($type == 2) {
                    $this->loadUtil('account');
                    $result = $this->utils->account->accountSelectByArray([['UUID', $existsItem]], 'Location');
                    $personalAccountName = implode(',', array_column($result, 'Location'));
                    $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$personalAccountName]);
                }
            }
        }

        //检测订阅总金额
        $this->loadProvider('billsysUtil');
        $chargeService = $this->services->billsysUtil;
        list($userCharges, $price) = $chargeService->getVideoStorageSubscriptionChargeDataAndPrice($videoStorageInfo, $intervalType, $type);

        return ['Price' => $price];
    }

    /**
     * @description: 计算三方锁的费用
     * @param: {string} Type 类型 1=单住户 2=社区 3=办公
     * @return array|int
     * @throws \Exception
     * @author: csc 2025/2/11 14:07 V7.1.0
     * @lastEditors: csc 2025/2/11 14:07 V7.1.0
     */
    public function computedThirdLockPrice()
    {
        //获取参数
        $params = ['Type:enum(1,2,3)', 'IntervalType:enum("0","1","2","3")', 'ThirdLockInfo', 'SubscriptionUUID?:or-rule("uuid","string-empty")'];
        list($type, $intervalType, $thirdLockInfo, $subscriptionUUID) = $this->getParams($params);
        $price = 0;
        if (empty($thirdLockInfo)) {
            return $price;
        }

        //检测三方锁是否已经在有效订阅中
        $thirdLockUUIDs = array_column($thirdLockInfo['config'], 'LockUUID');
        $existsItem = $this->utils->self->checkThirdLockItemInSubscription($thirdLockUUIDs);
        if (!empty($existsItem)) {
            //如果有旧订阅，判断已存在的视频存储是否都在原订阅中
            if ($subscriptionUUID) {
                //已经存在订阅的三方锁，并且不在当前订阅中
                $subscription = $this->dao->subscriptionList->selectByUUID($subscriptionUUID);
                if (empty($subscription)) {
                    $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_ERROR]);
                }
                $subscriptionEndUserList = $this->dao->subscriptionEndUserList->checkThirdLockInOtherSubscription($existsItem, $subscription[0]['UUID']);
                $existsItem = $subscriptionEndUserList;
            }

            if (!empty($existsItem)) {
                $this->loadUtil('dormakaba', true);
                $this->loadUtil('itec', true);
                $dormakabaName = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $existsItem]]);
                $itecName = $this->utils->_common->itec->getItecLock([['UUID', $existsItem]]);
                $name = implode(',', array_merge(array_column($dormakabaName, 'Name'), array_column($itecName, 'Name')));
                $this->output->echoErrorMsg(STATE_ITEM_IN_SUBSCRIPTION, [], [$name]);
            }
        }

        //检测订阅总金额
        $this->loadProvider('billsysUtil');
        $chargeService = $this->services->billsysUtil;
        list($userCharges, $price) = $chargeService->getThirdLockSubscriptionChargeDataAndPrice($thirdLockInfo, $intervalType, $type);

        return ['Price' => $price];
    }

    /**
     * @description:获取订阅列表
     * @param: {array} Array 搜索数组
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     */
    public function getSubscriptionList()
    {
        $params = ['Array:is-array'];
        list($searchArray) = $this->getParams($params);
        list($offset, $row, $searchKey, $searchValue) = $this->getParamsLimitAndSearch();
        $field = "ID,UUID,SubscriptionNumber,PayPlatform,Type,Cycles,IntervalType,TimeZone,StartTime,EndTime,NextPayTime,TotalPrice,Discount,Status,CreateTime,InsUUID";
        $data = $this->dao->subscriptionList->orderBy("Status = 1 DESC,CreateTime DESC")->limit("$offset, $row")->selectByArray($searchArray, $field);
        $total = $this->dao->subscriptionList->selectByArray($searchArray, "count(*)")[0]['count(*)'];
        $changeField = "ID,Status,IntervalType,Cycles,PayPlatform";
        //缓存map，key存放ins的UUID，value存放ins的名称
        $map = [];
        $this->loadUtil('account');
        foreach ($data as &$item) {
            //时间转换和类型转换
            $item = $this->share->util->setQueryTimeZoneForObject($item, $item['TimeZone']);
            $item = $this->share->util->changeStringToNumber($item, $changeField);
            //已到期的隐藏下次支付时间
            $item['NextPayTime'] = $item['Status'] === SUBSCRIBE_STATUS['expired'] ? '' : $item['NextPayTime'];

            //查询Installer
            $insUUID = $item['InsUUID'];
            $item['Installer'] = isset($map[$insUUID]) ? $map[$insUUID] : $this->utils->account->accountSelectByKey('UUID', $insUUID, 'Account')[0]['Account'];
            $map = $this->share->util->addToMap($map, $insUUID, $item['Installer']);
            //金额计算
            $disCount = $this->share->util->computedDiscount($item['TotalPrice'], $item['Discount']);
            $item['TotalPrice'] = $this->share->util->outputComputedCount($disCount);
        }
        unset($item);

        return [intval($total), $data];
    }

    /**
     * @description:dis获取订阅列表
     * @param: {array} Array 搜索数组
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     */
    public function getSubscriptionListByDis()
    {
        $params = ['Array:is-array'];
        list($searchArray) = $this->getParams($params);
        list($offset, $row, $searchKey, $searchValue) = $this->getParamsLimitAndSearch();
        $field = "ID,UUID,SubscriptionNumber,PayPlatform,Type,Cycles,IntervalType,TimeZone,StartTime,EndTime,NextPayTime,TotalPrice,Discount,Status,CreateTime,InsUUID,MixType";
        $data = $this->dao->subscriptionList->orderBy("Status = 1 DESC,CreateTime DESC")->limit("$offset, $row")->selectByArray($searchArray, $field);
        $total = $this->dao->subscriptionList->selectByArray($searchArray, "count(*)")[0]['count(*)'];
        $changeField = "ID,Status,IntervalType,Cycles,PayPlatform";
        //缓存map，key存放ins的UUID，value存放ins的名称
        $map = [];
        $this->loadUtil('account');
        foreach ($data as &$item) {
            //时间转换和类型转换
            $item = $this->share->util->setQueryTimeZoneForObject($item, $item['TimeZone']);
            $item = $this->share->util->changeStringToNumber($item, $changeField);
            //已到期的隐藏下次支付时间
            $item['NextPayTime'] = $item['Status'] === SUBSCRIBE_STATUS['expired'] ? '' : $item['NextPayTime'];

            //查询Installer
            $insUUID = $item['InsUUID'];
            $item['Installer'] = isset($map[$insUUID]) ? $map[$insUUID] : $this->utils->account->accountSelectByKey('UUID', $insUUID, 'Account')[0]['Account'];
            $map = $this->share->util->addToMap($map, $insUUID, $item['Installer']);
            //金额计算
            $disCount = $this->share->util->computedDiscount($item['TotalPrice'], $item['Discount']);
            $item['TotalPrice'] = $this->share->util->outputComputedCount($disCount);
            $item['Function'] = $this->utils->self->dealSubscriptionFunction($item['Type'],$item['MixType']);
            $item['Type'] = $this->utils->self->getSubscriptionProjectTypeByDis($item['MixType']);
        }
        unset($item);

        return [intval($total), $data];
    }

    /**
     * @description:dis获取订阅列表
     * @param: {string} _distributorId
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     */
    public function getListForDis()
    {
        $params = [PROXY_ROLE_CHECK['distributorUUID'], 'Installer?:string', 'Type:enum("1","2","3","")'];
        list($disUUID, $insName, $type) = $this->getParams($params);
        $this->loadUtil('account');
        $searchArray = [['PayerUUID', $disUUID], ['IsDelete', 0]];
        if ($insName !== '' && $insName !== null) {
            //找出所有ins
            $disID = $this->dao->account->selectByUUID($disUUID, 'ID')[0]['ID'];
            $insList = $this->dao->account->getInsDataByInsDisSearch($insName, $disID);
            $insUUIDList = array_column($insList, 'UUID');
            $searchArray[] = ['InsUUID', $insUUIDList];
        }
        if ($type !== null && $type !== '') {
            $type = intval($type);
            switch ($type) {
                case 1:
                    $single = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['singleRenew']);
                    $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['singleVideoStorageRenew']);
                    $searchArray[] = ['MixType', "& ($single | $video) !=0", 'origin'];
                    break;
                case 2:
                    $community = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityRenew']);
                    $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityVideoStorageRenew']);
                    $rent = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
                    $searchArray[] = ['MixType', "& ($community | $video | $rent) !=0", 'origin'];
                    break;
                case 3:
                    $office = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['officeRenew']);
                    $searchArray[] = ['MixType', "& ($office) !=0", 'origin'];
                    break;
            }
        }
        list($total, $data) = $this->callSelfFunc('getSubscriptionListByDis', [$searchArray]);
        return ['data' => ['total' => $total, 'row' => $data]];
    }

    /**
     * @description:sub-dis获取订阅列表
     * @param: {string} _distributorId
     * @return array[]
     * @author: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     * @lastEditor: shoubin.chen 2023-12-14 15:22:21 v6.7.1
     */
    public function getListForSubDis()
    {
        $params = [PROXY_ROLE_CHECK['subDistributorUUID'], 'Installer?:string', 'Type:enum("1","2","3","")'];
        list($subDisUUID, $insName, $type) = $this->getParams($params);
        $this->loadUtil('account');
        $searchArray = [['PayerUUID', $subDisUUID], ['IsDelete', 0]];
        if ($insName !== '' && $insName !== null) {
            //找出所有ins
            $insList = $this->dao->subDisMngList->getProjectDataForSubDis($subDisUUID, 'Installer', $insName, PERSONGRADE);
            $insUUIDList = array_column($insList, 'UUID');
            $searchArray[] = ['InsUUID', $insUUIDList];
        }

        if ($type !== null && $type !== '') {
            $type = intval($type);
            switch ($type) {
                case 1:
                    $single = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['singleRenew']);
                    $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['singleVideoStorageRenew']);
                    $searchArray[] = ['MixType', "& ($single | $video) !=0", 'origin'];
                    break;
                case 2:
                    $community = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityRenew']);
                    $video = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['communityVideoStorageRenew']);
                    $rent = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['rentManagerRenew']);
                    $searchArray[] = ['MixType', "& ($community | $video | $rent) !=0", 'origin'];
                    break;
                case 3:
                    $office = $this->share->util->getDecimalFromBits(SUBSCRIBE_TYPE['officeRenew']);
                    $searchArray[] = ['MixType', "& ($office) !=0", 'origin'];
                    break;
            }
        }

        list($total, $data) = $this->callSelfFunc('getSubscriptionListByDis', [$searchArray]);
        return ['data' => ['total' => $total, 'row' => $data]];
    }

    /**
     * @Author: chenpl
     * @Description: 获取Ins支付的，还在续费的，月费有变化的订阅记录
     * @Params:
     * @Return:
     * @Date: 2024/12/27
     */
    public function getRentManagerRenewingSubscription()
    {
        $params = ['InsUUID:uuid', 'MonthlyFee'];
        list($insUUID, $monthlyFee) = $this->getParams($params);
        $monthlyFee = $this->share->util->inputComputedCount($monthlyFee);
        $searchArray = [
            ['PayerUUID', $insUUID],
            ['Type', SUBSCRIBE_TYPE['rentManagerRenew']],
            ['Status', 1],
            ['TotalPrice', $monthlyFee, '!=']
        ];
        return $this->dao->subscriptionList->selectByArray($searchArray);
    }
}

