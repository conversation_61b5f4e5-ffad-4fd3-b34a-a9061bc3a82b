<?php

namespace model\communityData;

/*
 * 高级方案功能和过期时间关系
 * @param $FeatureExpireTime 高级方案过期时间
 *        $Item 高级功能
 */
function checkFeaturePlan($FeatureExpireTime, $Item, $IsDefaultFeature)
{
    $now = \util\computed\getNow();
    $item = decbin($Item);
    $num = strlen($item) - 1;
    $items = str_split($item);
    // 获取高级功能数组
    if ($IsDefaultFeature == 0) {
        for ($i = 0; $i < FEATURE_PLAN_NUMBERS; ++$i) {
            $data[$i] = '0';
            // 默认开启QRCode 和 PIN
            if ($i == 1 || $i == 2) {
                $data[$i] = '1';
            }
        }
    } else {
        for ($i = 0; $i < FEATURE_PLAN_NUMBERS; ++$i) {
            if ($num >= 0) {
                // 有高级功能但是过期,状态为2;有且未过期状态为1;无,状态0
                if ($item[$num] == '1' && $FeatureExpireTime == null) {
                    $data[$i] = '0';
                } elseif ($items[$num] == '1' && strtotime($FeatureExpireTime) < strtotime($now)) {
                    $data[$i] = '2';
                } else {
                    $data[$i] = $items[$num];
                }
                --$num;
            } else {
                $data[$i] = '0';
            }
        }
    }

    return $data;
}


/**
 * @description: 邮编规则验证
 * @author:lwj 2022/8/26 10:04
 * @lastEditor: lwj 2022/8/26 10:04
 * @param PostalCode 邮编
 * @return:
 */
function checkPostalCode($postalCode)
{
    global $cMessage;
    //支持填字母（A-Z，a-z），数字（0-9）以及空格
    if(!preg_match("/^[0-9a-zA-Z\\s]*$/i",$postalCode)){
        $cMessage->echoErrorMsg(StatePostalCodeInvalid);
    }
}
