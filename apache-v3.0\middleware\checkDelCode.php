<?php

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";

/**
 * @description 校验app注销帐号code
 * <AUTHOR> 2022/3/15 13:33 V6.4
 * @LastEditor csc 2022/3/15 13:33 V6.4
 */
class CCheckDelCode implements IMiddleware {

    public function handle (\Closure $next) {
        global $gApp,$cMessage;
        global $cLog;
        $user = $gApp["userAlias"];
        $userId = $gApp["userAliasId"];
        $db = \database\CDatabase::getInstance();
        $params = ["Code"=>""];
        $code = \util\model\getParamsFromContainer($params,$this->dataContainer)["Code"];
        $cLog->actionLog("#middle#CCheckDelCode#Code=$code;user=$user");
        $userInfo = $db->querySList("select Account from PersonalAccount where ID = :ID",[":ID" => $userId])[0];

        //检测注销错误次数是否达到最大值
        $res = \util\computed\getLimitTimes($userInfo['Account'], 'deleteCode', 3);
        if (false !== $res) {
            $cMessage->echoErrorMsg(STATE_DELCODE_OVER_LIMIT_TIMES);
        }

        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2CODE);
        $validCode = $redis->get($userInfo['Account']);
        if ($code !== $validCode) {
            //本次操作错误后如果达到最大值则报错
            $res = \util\computed\recordLimitTimes($userInfo['Account'], 'deleteCode', 3, 24 * 3600);
            if (true === $res) {
                $cMessage->echoErrorMsg(STATE_DELCODE_GET_LIMIT_TIMES);
            } else {
                $cMessage->echoErrorMsg(STATE_DELCODE_ERROR);
            }
        } else {
            $redis->del($userInfo['Account']);
            \util\computed\delLimitTimes($userInfo['Account'], 'deleteCode');
        }

        $next();
    }
}