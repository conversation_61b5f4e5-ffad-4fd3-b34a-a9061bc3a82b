<?php
/*
 * @Description: 超级管理员根据社区设备id设置代理为社区管理员
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
class CSetSupToComWDevId implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setSupToComWDevId#");
        $data = $db->querySList("select A.ID,A.Account from Account A join Devices B on A.ID = B.MngAccountID where B.ID = :ID",[":ID"=>$id])[0];
        $gApp["userAliasId"] = $data["ID"];
        $gApp["userAlias"] = $data["Account"];
        $next();
    }
}