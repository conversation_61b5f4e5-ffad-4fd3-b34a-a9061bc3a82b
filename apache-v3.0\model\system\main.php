<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: kxl
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

class CSystem
{
    public function queryMenuDownList()
    {
        global $gApp;
        $role = $gApp["role"];
        $list = [
            RPERSONGRADE=>[
                "Akuvox_SmartPlus_for_Community_management.pdf",
                "Akuvox_SmartPlus_for_Single_Tenant_Management.pdf"
            ],
            RCOMMUNITYGRADE=>[
                "Akuvox_SmartPlus_for_Community_management.pdf",
                "Akuvox_SmartPlus_for_Single_Tenant_Management.pdf"
            ],
            RPROPERTYMANAGE=>[
                "Akuvox_SmartPlus_for_Property_Management.pdf"
            ],
            ROFFICEGRADE=>[
                "Akuvox_SmartPlus_for_Community_management.pdf",
                "Akuvox_SmartPlus_for_Single_Tenant_Management.pdf"
            ]
        ];
        if (array_key_exists($role, $list) && CANGETTOOLBOX) {
            $data = [
                "Show"=>1,
                "List"=>$list[$role]
            ];
        } else {
            $data = ["Show"=>0,"List"=>[]];
        }

        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @description:获取智能家居配置信息
     * @author:lwj 2023-03-23 17:16:17 V6.5.4
     * @lastEditor:lwj 2023-03-23 17:16:17 V6.5.4
     * @return mixed [是否开启智能家居, 当前生效的家居版本]
     */
    public function getSmartHomeCnf()
    {
        $sql = 'select IsSmartHomeStatus,SmartHomeVersion from SystemSetting limit 1';
        $info = $this->db->querySList($sql)[0];
        return [$info['IsSmartHomeStatus'], $info['SmartHomeVersion']];
    }
}
