<?php
/*
 * @Description: 更换参数名称
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

class CChangeParamName implements IMiddleware {
    public function handle(\Closure $next) {
        global $gApp;
        $params = $this->params;
        foreach($params as $key=>$value) {
            $gApp["plan"]["data"][$value] = $gApp["plan"]["data"][$key];
        }
        $next();
    }
}