<?php

namespace model\userData;

function initInsert($user, $userId, $pin)
{
    global $db,$cMessage;
    $myData = $db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
    $role = $myData['Role'];
    $isNew = $db->querySList('select IsNew from CommunityInfo where AccountID = :ID', [":ID" => $myData["ParentID"]])[0]['IsNew'];

    if ($isNew == 1 || in_array($role, OFFROLE)) {
        // 已存在先删除
        $data = $db->querySList('select ID from CommPerPrivateKey where Account=:Account and Special = 1', [':Account' => $user]);
        foreach ($data as $val) {
            $db->delete2ListWID('CommPerPrivateKey', $val['ID']);
        }

        $db->insert2List('CommPerPrivateKey', [
            ":Account" => $user,
            ":Code" => $pin,
            ":CommunityID" => $myData["ParentID"],
            ":CreateTime" => \util\computed\getNow(),
            ":Special" => 1
        ]);
    } else {
        // 已存在先删除
        $data = $db->querySList('select ID from PersonalPrivateKey where Node=:Account and Special = 1', [':Account' => $user]);
        foreach ($data as $val) {
            $db->delete2ListWID('PersonalPrivateKey', $val['ID']);
        }

        $db->insert2List(
            "PersonalPrivateKey",
            [
                ":MngAccountID" => $myData["ParentID"],
                ":UnitID" => $myData["UnitID"],
                ":Grade" => 3,
                ":Type" => 0,
                ":Code" => $pin,
                ":Node" => $user,
                ":AccountID" => $userId,
                ":Special" => 1
            ]
        );
        //跳过pin设置，me页面设置也需添加设备
        $keyID = $db->lastInsertId();
        // 增加自动添加设备列表
        $devAssocData = $db->querySList(
            "select ID,MAC,Relay,SecurityRelay from Devices where Grade = 1 and MngAccountID = :MngAccountID and Type in (0,1,50) and ((ID in (select DevicesID from PubDevMngList where UnitID = :UnitID)) or (Flags & 8 != 0))
            union all select ID,MAC,Relay,SecurityRelay from Devices where Grade = 2 and UnitID = :UnitID and Type in (0,1,50)
            union all select ID,MAC,Relay,SecurityRelay from Devices  where Grade = 3 and Node=:Node and Type in (0,1,50)",
            [":MngAccountID" => $myData["ParentID"], ":UnitID" => $myData["UnitID"], ":Node" => $user]
        );
        foreach ($devAssocData as $dev) {
            $relays = explode(";", $dev["Relay"]);
            $relay = 0;
            foreach ($relays as $key => $value) {
                $relayStatus = explode(",", $value)[4];
                if ($relayStatus == 1) {
                    $relay += \util\computed\getRelayValue($key);
                }
            }
            $securityRelays = explode(';', $dev['SecurityRelay']);
            $securityRelay = 0;
            foreach ($securityRelays as $key => $value) {
                $securityRelayStatus = explode(",", $value)[4];
                if ($securityRelayStatus == 1) {
                    $securityRelay += \util\computed\getRelayValue($key);
                }
            }
            if ($relay != 0 or $securityRelay != 0) {
                $db->insert2List(
                    "PersonalPrivateKeyList",
                    [":KeyID" => $keyID, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":MAC" => $dev["MAC"]]
                );
            }
        }
    }
    // 用户唯一PIN设置
    $count = $db->querySList('select count(*) from PinUnique where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $myData['UUID']])[0]['count(*)'];
    if ($count > 0) {
        $cMessage->echoErrorMsg(StateNotPermission);
    } else {
        $db->insert2List('PinUnique', [
            ":UUID" => \util\string\uuid(),
            ":PersonalAccountUUID" => $myData['UUID'],
            ":CreateTime" => \util\computed\getNow()
        ]);
    }
}
