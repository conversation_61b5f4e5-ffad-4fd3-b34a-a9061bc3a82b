<?php

namespace model\notify;
//个人

trait community
{
    function buildAdd() {
        $params = [
            // 社区ID
            "userAliasId" => "",
            // build id
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $buildId = $params["ID"];
        $this->log->actionLog("#model#notify#BuildAdd#userId=$userId;buildId=$buildId");
        \webCommunityModifyNotify(WEB_COMM_ADD_BUILDING, "", "", $userId, $buildId);
    }

    function buildDelete() {
        $params = [
            // 社区ID
            "userAliasId" => "",
            // build id
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $buildId = $params["ID"];
        $this->log->actionLog("#model#notify#BuildDelete#userId=$userId;buildId=$buildId");
        \webCommunityModifyNotify(WEB_COMM_DEL_BUILDING, "", "", $userId, $buildId);
    }

    function buildUpdate() {
        $params = [
            // 社区ID
            "userAliasId" => "",
            // build id
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $buildId = $params["ID"];
        $this->log->actionLog("#model#notify#BuildUpdate#userId=$userId;buildId=$buildId");
        \webCommunityModifyNotify(WEB_COMM_MODIFY_BUILDING, "", "", $userId, $buildId);
    }

    function communityAdd() {
        $params = [
            "CommunityID" => "",
            "userData" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityID = $params["CommunityID"];
        $userData = $params["userData"];
        $this->log->actionLog("#model#notify#communityAdd#params=" . json_encode($params));
        \webCommunityModifyNotify(WEB_COMM_IMPORT_COMMUNITY, "", "", $communityID, "");
        foreach ($userData as $val) {
            \communityCreateUser($val["node"], $val["password"], $val["email"]);
        }
    }

    function communityDataUpdate() {
        $params = [
            "userAliasId" => "",
            "simStateChange" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["userAliasId"];
        $simStateChange = $params["simStateChange"];
        $this->log->actionLog("#model#notify#CommunityDataUpdate#id=$id#simState=$simStateChange");
        \webCommunityModifyNotify(WEB_COMM_INFO, "", "", $id);
        // 流量超额状态发生改变，发送通知
        if ($simStateChange) {
            \webCommunityModifyNotify(WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT, "", "", $id, 0);
        }
    }

    function communityTimeUpdate() {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["userAliasId"];
        $this->log->actionLog("#model#notify#communityTimeUpdate#id=$id");
        \webCommunityModifyNotify(WEB_COMM_MODIFY_TIME, "", "", $id);
    }


    function communityMotionUpdate() {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["userAliasId"];
        $this->log->actionLog("#model#notify#communityTimeUpdate#id=$id");
        \webCommunityModifyNotify(WEB_COMM_MOTION, "", "", $id);
    }

    function communityAptUpdate() {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["userAliasId"];
        $this->log->actionLog("#model#notify#communityAptUpdate#id=$id");
        \webCommunityModifyNotify(WEB_COMM_APT_PIN, "", "", $id);
    }

    function communityDel() {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#communityDeleteNotify#id=$id");
        \webCommunityModifyNotify(WEB_COMM_DELETE_COMMUNITY, "", "", $id);
    }


    function exportPmLog() {
        $params = [
            "rowData" => "",
            "SelfCustomizeForm" => "",
            "data" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $rowData = $params["rowData"];
        $isShowMorePrompt = $params["data"]["isShowMorePrompt"] ? $params["data"]["isShowMorePrompt"] : false;
        $customizeForm = $params["SelfCustomizeForm"];
        $accountData = $this->db->querySList("select A.*,B.FirstName,B.LastName from Account A left join PropertyInfo B on A.ID = B.AccountID where A.ID = :ID", [":ID" => $rowData['PmID']])[0];
        $communityData = $this->db->querySList("select * from Account where ID = :ID", [":ID" => $rowData['CommunityID']])[0];
        $datas = ["trace_id" => $rowData["TraceID"], "communit_id" => $rowData["CommunityID"], "export_startime" => $rowData["BeginTime"], "export_endtime" => $rowData["LastTime"],
            "log_type" => $rowData["LogType"], "export_type" => $rowData["ExportType"], "language" => $accountData["Language"], "email" => $accountData["Email"],
            "time_form" => $customizeForm, "time_zone" => $communityData["TimeZone"], "community" => $communityData["Location"], "name" => $accountData["FirstName"] . " " . $accountData["LastName"]];
        \pmExportLog($datas);
        $echoData = ["type" => $rowData["ExportType"], "TraceID" => $rowData["TraceID"], "isShowMorePrompt" => $isShowMorePrompt];
        \util\computed\setGAppData(["data" => $echoData]);
    }

    function communityUserPinUpdate() {
        $params = [
            "userAliasId" => "",
            "pinStateChange" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["userAliasId"];
        $pinStateChange = $params["pinStateChange"];
        $this->log->actionLog("#model#notify#communityUserPinUpdate#id=$id#pinState=$pinStateChange");
        // 流量超额状态发生改变，发送通知
        if ($pinStateChange) {
            \webCommunityModifyNotify(WEB_COMM_ALLOW_CREATE_PIN, "", "", $id);
        }
    }
}