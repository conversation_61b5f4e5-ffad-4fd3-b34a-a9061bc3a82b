<?php
namespace model\pmKey\update;

const PINID_IDX = 1;
const RESIDENT = '0';
const DELIVERY = '1';
const STAFF = '2';
const OFFICEPERSONNEL = '30';
const OFFICESTAFF = '31';
const PIN = 0;
const RFCARD = 1;

namespace model\pmKey;

trait update
{
    public function edit()
    {
        $params = [
            "Name" => "",
            "Key" => "",
            "MAC" => "",
            "ID" => "",
            "DateFlag" => "",
            "StartTime" => "",
            "StopTime" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "SchedulerType" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $key = $params["Key"];
        $macs = $params["MAC"];
        $id = $params["ID"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $schedulerType = $params["SchedulerType"];
        $userId = $params["userAliasId"];
        $this->log->actionLog("#model#pmKey#edit#param=" . json_encode($params));
        $this->editControl($userId, $name, $key, $macs, $dateFlag, $startTime, $stopTime, $beginTime, $endTime, $schedulerType, $id);
    }

    public function editControl($userId, $name, $key, $macs, $dateFlag, $startTime, $stopTime, $beginTime, $endTime, $schedulerType, $id)
    {
        global $cMessage;
        //V6.1 修改如果$key=****就不修改
        if ((($this->owernType == 2 && $this->type == 0) || ($this->owernType == 1 && $this->type == 0)) && $key == '****') {
            if ($this->owernType == 2) {
                $key = $this->db->querySList("select Code from " . $this->personalTables[$this->type] . " where ID=:ID", [":ID" => $id])[0]["Code"];
            } else {
                $key = $this->db->querySList("select Code from " . $this->tables[$this->type] . " where ID=:ID", [":ID" => $id])[0]["Code"];
            }
        }

        if ($schedulerType == 0) {
            if (strtotime($endTime) <= strtotime($beginTime)) {
                $cMessage->echoErrorMsg(StateEndThanStart);
            }
        } else {
            if (strtotime("2000-01-01 $stopTime") <= strtotime("2000-01-01 $startTime")) {
                $cMessage->echoErrorMsg(StateEndThanStart);
            }
        }

        if ($this->deviceVailCheck($userId, $macs) === false) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        if ($this->db->isExistFiled($this->tables[$this->type], [":Code" => $key, ":MngAccountID" => $userId], $id)
            || $this->db->isExistFiled($this->personalTables[$this->type], [":Code" => $key, ":MngAccountID" => $userId], $id)) {
            $cMessage->echoErrorMsg($this->type == 0 ? StatePrivateKeyExists : StateRFCardExit);
        }


        $dateFlag = explode(";", $dateFlag);
        $tmpDate = 0;
        foreach ($dateFlag as $value) {
            $tmpDate += DATEFLAG[$value];
        }
        $dateFlag = $tmpDate;
        if ($dateFlag == 127 && $schedulerType == 2) {
            $schedulerType = 1;
        }

        if ($this->owernType == 2) {
            $this->db->update2ListWID($this->personalTables[$this->type], [":ID" => $id, ":Code" => $key,
                ":DateFlag" => $dateFlag, ":StartTime" => $startTime, ":StopTime" => $stopTime, ":BeginTime" => $beginTime, ":EndTime" => $endTime, ":SchedulerType" => $schedulerType]);
            $oldMac = [];
            $this->db->delete2ListWKey($this->personalListTables[$this->type], "KeyID", $id);
        } else {
            $this->db->update2ListWID($this->tables[$this->type], [":ID" => $id, ":Name" => $name, ":Code" => $key,
                ":DateFlag" => $dateFlag, ":StartTime" => $startTime, ":StopTime" => $stopTime, ":BeginTime" => $beginTime, ":EndTime" => $endTime, ":SchedulerType" => $schedulerType]);
            $oldMac = $this->db->queryAllList($this->listTables[$this->type], ["equation" => [":KeyID" => $id]]);
            $this->db->delete2ListWKey($this->listTables[$this->type], "KeyID", $id);
        }
        // $this->db->update2ListWID($this->tables[$this->type],[":ID"=>$id,":Name"=>$name,":Code"=>$key]);
        // $oldMac = $this->db->queryAllList($this->listTables[$this->type],["equation"=>[":KeyID"=>$id]]);
        // $this->db->delete2ListWKey($this->listTables[$this->type],"KeyID",$id);
        // foreach($macs as $val) $this->db->insert2List($this->listTables[$this->type],[":KeyID"=>$id,":MAC"=>$val]);

        $nMacs = [];
        foreach ($macs as $val) {
            $relay = $val["Relay"];
            $relays = explode(";", $relay);
            $relay = 0;
            foreach ($relays as $value) {
                $relay += \util\computed\getRelayValue($value);
            }

            $securityRelay = $val["SecurityRelay"];
            $securityRelays = explode(";", $securityRelay);
            $securityRelay = 0;
            foreach ($securityRelays as $value) {
                $securityRelay += \util\computed\getRelayValue($value);
            }

            if ($this->owernType == 2) {
                $this->db->insert2List($this->personalListTables[$this->type], [":KeyID" => $id, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":MAC" => $val["MAC"]]);
            } else {
                $this->db->insert2List($this->listTables[$this->type], [":KeyID" => $id, ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":MAC" => $val["MAC"]]);
            }
            array_push($nMacs, $val["MAC"]);
        }

        //通知数据变化 czx
        if ($this->owernType == 2) {
            $keyData = $this->db->queryAllList($this->personalTables[$this->type], ["equation" => [":ID" => $id]])[0];
            $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $keyData["AccountID"]]])[0];
            if (in_array($userData["Role"], SUBROLE)) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userData["ParentID"]]])[0];
            }
            // communityUpdateNodeNotify($userData["Account"],$userData["ParentID"],$userData["UnitID"],1);
            \util\computed\setGAppData(["Account" => $userData["Account"]]);
        } else {
            foreach ($oldMac as $val) {
                array_push($nMacs, $val["MAC"]);
            }
            $nMacs = array_unique($nMacs);
            $nMacs = implode(";", $nMacs);
        }
        \util\computed\setGAppData(["macs" => $nMacs, "ownerType" => $this->owernType]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog($this->type == 0 ? AuditCodeEditPin : AuditCodeEditRf, $this->env, [$key], $account);
    }

    public function afterEdit()
    {
        $params = [
            "macs" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $macs = $params["macs"];
        // communityUpdatePubKeyNotify($macs);
    }

    public function modAPKey()
    {
        $this->owernType = 1;
        $this->type = 0;
        $this->edit();
    }

    public function modARKey()
    {
        $this->owernType = 1;
        $this->type = 1;
        $this->edit();
    }

    public function modSPKey()
    {
        $this->owernType = 0;
        $this->type = 0;
        $this->edit();
    }

    public function modSRKey()
    {
        $this->owernType = 0;
        $this->type = 1;
        $this->edit();
    }

    public function modRPKey()
    {
        $this->owernType = 2;
        $this->type = 0;
        $this->edit();
    }

    public function modRRKey()
    {
        $this->owernType = 2;
        $this->type = 1;
        $this->edit();
    }

    public function editAllPinOrCard()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "ID" => "",
            "Type" => "",
            "Code" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = explode('_', $params["ID"])[update\PINID_IDX];
        $type = $params["Type"];
        $code = $params["Code"];
        $initCode = $code == '****';
        $perIds = [];   //resident对应id号
        $deliveryIds = [];   //delivery对应id号
        $staffIds = []; //staff对应id号
        $codeType = $this->type == 0 ? 'PIN' : 'RFCARD';

        if (($type == update\RESIDENT || $type == update\OFFICEPERSONNEL || $type == update\OFFICESTAFF) && !$initCode) {
            \model\staff\checkPinCardUnique($code, $userId, $type == update\RESIDENT ? 2 : 3, $codeType, $id);
            $this->db->update2ListWID($this->commPerTables[$this->type], [":ID" => $id, ":Code" => $code]);

            // 通知数据变化
            $userData = $this->db->queryAllList($this->commPerTables[$this->type], ["equation" => [":ID" => $id]])[0];
            array_push($perIds, $userData["Account"]);
        } elseif ((!$initCode && $type == update\DELIVERY || $type == update\STAFF)) {
            $typeCode = $this->type == update\PIN ? ":PinCode" : ":CardCode";
            $tables = $type == update\DELIVERY ? "Delivery" : "Staff";

            \model\staff\checkPinCardUnique($code, $userId, $type - 1, $codeType, $id);
            if ($type == update\DELIVERY) {
                array_push($deliveryIds, $id);
            } else {
                array_push($staffIds, $id);
            }
            $this->db->update2ListWID($tables, [":ID" => $id, $typeCode => $code]);
        }
        \util\computed\setGAppData(["communityId"=>$userId,"deliveryIds"=>$deliveryIds, "staffIds"=>$staffIds,"Account"=>$perIds, "accessGroupsId"=>[]]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        if ($code !== '' && $this->type === 0) {
            $this->auditLog->setLog(AuditCodeEditPin, $this->env, [$code], $account);
        }
        if ($code !== '' && $this->type === 1) {
            $this->auditLog->setLog(AuditCodeEditRf, $this->env, [$code], $account);
        }
    }

    public function editAllPin()
    {
        $this->type = 0;
        $this->editAllPinOrCard();
    }

    public function editAllCard()
    {
        $this->type = 1;
        $this->editAllPinOrCard();
    }
}
