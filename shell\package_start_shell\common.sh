#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2024-02-20
# Description   :   通用包构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
PACKAGE_NAME=$4            #包名
WEBROOT_PACKAGE_NAME=$5    #webroot下实际包的目录名
IMAGE_ACR="${6}"           #镜像仓库地址

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

PKG_ROOT=$RSYNC_PATH
COMMON_HTML_ROOT=/var/www/docker/web_$PACKAGE_NAME/html

# 检查是否传递了 $5 参数，如果没有，则将 $WEBROOT_PACKAGE_NAME 设为 $PACKAGE_NAME 的值
if [ -z "$WEBROOT_PACKAGE_NAME" ]; then
    WEBROOT_PACKAGE_NAME=$PACKAGE_NAME
fi

###################### 开始安装、读取配置 ######################
echo "Begin to install $PACKAGE_NAME"
echo '读取配置'
source "$PKG_ROOT"/shell/package_start_shell/source.sh $PKG_ROOT

###################### 替换配置文件 ######################
echo '替换配置文件的配置'
if [ ! -d "$PKG_ROOT"/web_scripts ];then mkdir -p "$PKG_ROOT"/web_scripts; fi
# 生成配置文件 dynamic_config.php
create_php_config "$PKG_ROOT"/web_scripts/dynamic_config.php
mkdir -p $CONFWATCH_EXEC
if [ -f "$PKG_ROOT/web_scripts/change_web_conf_by_etcd.php" ];then
  cp -f "$PKG_ROOT"/web_scripts/change_web_conf_by_etcd.php "$CONFWATCH_EXEC"/
fi

###################### 复制安装包的文件 ######################
echo '复制$PACKAGE_NAME安装包的文件'
mkdir -p $COMMON_HTML_ROOT
cp -f "$PKG_ROOT"/web_scripts/dynamic_config.php "$PKG_ROOT"/webroot/$WEBROOT_PACKAGE_NAME/config/
#share/lang下的文件夹常量改为数组（防止重新打了词条库忘记修改的情况）
sedGrepFile 'const MSGTEXT =' 'return' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/lang"
# notify下的global变量修改 config配置路径修改
sedGrepFile 'global $db;' '$db = \\share\\util\\getDatabase();' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile 'global $cLog;' '$cLog = \\share\\util\\getLog();' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile 'global $cAuditLog;' '$cAuditLog = \\share\\util\\getAuditLog();' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile ''\''\/..\/config\/dynamic_config.php' ''\''\/..\/..\/config\/dynamic_config.php' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile '\\util\\string' '\\share\\util' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile '\\util\\time' '\\share\\util' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
sedGrepFile '\\util\\computed' '\\share\\util' "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/share/notify"
# 日志目录变更
sedGrepFile 'packageName' "web_$PACKAGE_NAME" "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/webman/config/app.php"
if [ -d $COMMON_HTML_ROOT/$PACKAGE_NAME ]; then
    rm -rf $COMMON_HTML_ROOT/$PACKAGE_NAME
fi
#workerman启动进程数量修改
count=$(calculate_process_count "$PACKAGE_NAME")
sedGrepFile 'workerNum' "$count" "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/webman/config/server.php"
sedGrepFile 'packageName' "web_$PACKAGE_NAME" "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/webman/config/server.php"
sedGrepFile 'packageName' "web_$PACKAGE_NAME" "$PKG_ROOT/webroot/$WEBROOT_PACKAGE_NAME/webman/config/process.php"

cp -rf "$PKG_ROOT"/webroot/$WEBROOT_PACKAGE_NAME $COMMON_HTML_ROOT/$PACKAGE_NAME
#复制Dockerfile，docker-compose(后续会改成compose启动)
cp -rf "$PKG_ROOT"/webroot/web_$PACKAGE_NAME/* $COMMON_HTML_ROOT/../
chmod 755 -R $COMMON_HTML_ROOT

###################### 创建存放日志的文件夹 ######################
echo "创建存放日志的文件夹"
# php 日志
mkdir -p /var/log/php
touch /var/log/php/php-error.log
touch /var/log/php/php-error-detail.log
touch /var/log/php/php-person-action.log
touch /var/log/php/php-pay-action.log
touch /var/log/php/php-business.log
touch /var/log/php/web-request.log

# php 和后台通信日志文件
touch /var/log/php/csadapt-interface.log
chown nobody:nogroup /var/log/php/csadapt-interface.log
chmod 666 /var/log/php/*.log

# 相关日志
mkdir -p /var/log/php/web_$PACKAGE_NAME/logs
touch /var/log/php/web_$PACKAGE_NAME/logs/stdout.log
touch /var/log/php/web_$PACKAGE_NAME/logs/workerman.log
touch /var/log/php/web_$PACKAGE_NAME/logs/php-business.log
touch /var/log/php/web_$PACKAGE_NAME/logs/php-pay-action.log
touch /var/log/php/web_$PACKAGE_NAME/logs/php-person-action.log
touch /var/log/php/web_$PACKAGE_NAME/logs/php-error.log
touch /var/log/php/web_$PACKAGE_NAME/logs/php-slow.log
chmod 666 /var/log/php/web_$PACKAGE_NAME/logs/*.log
chmod 755 /var/log/php/web_$PACKAGE_NAME/*

#docker端口
declare -A map
map["office"]=8788
map["smartHome2"]=8789
map["insapp"]=8790
map["alexa"]=8791
map["single"]=8793
map["community"]=8794
map["webserver"]=8795
map["log"]=8797



###################### 启动服务 ######################
echo "启动 $PACKAGE_NAME docker 进程"
IMAGE_NAME="$IMAGE_ACR"/ak_system/web:latest
CONTAINER_NAME_COMMON=web_$PACKAGE_NAME
#docker仓库拉取镜像
docker pull $IMAGE_NAME
if [ `docker ps -a | grep $CONTAINER_NAME_COMMON | wc -l` -gt 0 ];then docker stop $CONTAINER_NAME_COMMON;docker rm $CONTAINER_NAME_COMMON;fi
docker run -d -e TZ=Asia/Shanghai --restart=always -p ${map["$PACKAGE_NAME"]}:8787 -v /var/www/docker/web_$PACKAGE_NAME/html:/var/www/html -v /var/log/php:/var/log/php -v /var/async-task.sock:/var/async-task.sock -v /var/www/download:/var/www/download -v /usr/local/akcs:/usr/local/akcs -v /var/www/upload:/var/www/upload -v /download/tmp_key_qrcode:/download/tmp_key_qrcode -v /etc/ip:/etc/ip -v /home/<USER>/home/<USER>/usr/local/php/etc/client.conf:/usr/local/out/php/etc/client.conf -v /etc/oss_install.conf:/etc/oss_install.conf --name $CONTAINER_NAME_COMMON $IMAGE_NAME php /var/www/html/$PACKAGE_NAME/webman/start.php start
