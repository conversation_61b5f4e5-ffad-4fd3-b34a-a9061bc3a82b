<?php

namespace package\common\model\account\method;

use stdClass;

trait Info
{
    /**
     * @description: 获取管理员Account表中信息
     * @param ID
     * @return Account_Data
     * @author: kxl
     */
    public function getManagerInfo()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        return $this->callSelfFunc('getManagerInfoByKey', ['ID', $id]);
    }

    public function getManagerInfoUUID()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);
        return $this->callSelfFunc('getManagerInfoByKey', ['UUID', $uuid]);
    }

    public function getManagerInfoByKey()
    {
        $params = ['key', 'value'];
        list($key, $value) = $this->getParams($params);
        $data = $this->dao->account->selectByKey($key, $value)[0];
        unset($data['Passwd'], $data['LoginAccount'], $data['Email']);
        if (!empty($data)) {
            $userInfoData = $this->dao->account->getAccountUserInfoByAccountID($data['ID']);
            $data['LoginAccount'] = !empty($userInfoData['LoginAccount']) ? $userInfoData['LoginAccount'] : '';
            $data['Email'] = !empty($userInfoData['Email']) ? $userInfoData['Email'] : '';
            $data['UserInfoUUID'] = !empty($userInfoData['UUID']) ? $userInfoData['UUID'] : '';
            $data['EmailNotify'] = $userInfoData['EmailNotify'];
        }

        return $data;
    }


    /*
     *@description 获取社区信息
     *<AUTHOR> 2023-04-19 17:17:23 V6.6
     *@lastEditor cj 2023-04-19 17:17:23 V6.6
     *@param {*} ID 指Account表的ID
     *@return communityInfo信息
     */
    public function getCommunityInfo()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        return $this->dao->communityInfo->selectByAccountID($id)[0];
    }

    /**
     * @description:dis获取单住户管理员信息
     * @author:lwj 2022-12-29 11:12:23 V6.5.3
     * @lastEditor:lwj 2022-12-29 11:12:23 V6.5.3
     * @param:
     * @return mixed
     */
    public function getSingleManageInfoByDisID()
    {
        $params = [PROXY_ROLE['distributorId']];
        list($distributorId) = $this->getParams($params);
        $data = $this->callSelfFunc('accountSelectByArray', [[['ParentID', $distributorId], ['Grade', PERSONGRADE]], 'Account']);
        return $data;
    }

    /**
     * @description: 获取用户信息
     * @param ID,type?:basic|conf|all
     * @return {*}
     * @author: kxl
     */
    public function getUserInfo()
    {
        $params = ['ID', 'type'];
        list($id, $type) = $this->getParams($params);
        $this->loadUtil('common');
        $type = $type ? $type : 'basic';
        $basic = $this->callSelfFunc('getUserListByArray', [[['ID', $id]], 1])[0];
        unset($basic['SipPwd']);
        unset($basic['Passwd']);
        if ($type === 'basic') {
            return $basic;
        }
        // 从账号无conf，返回值应为空数据[]，否则merge会存在问题
        if (in_array($basic['Role'], SUBROLE)) {
            $conf = [];
        } else {
            $conf = $this->dao->personalAccountCnf->selectByAccount($basic['Account'])[0];
        }
        if ($type === 'conf') {
            return $conf;
        }

        return ['basic' => $basic, 'conf' => $conf];
    }

    /*
     *@description 批量获取用户在PersonalAccount中的信息
     *<AUTHOR> 2022-03-30 17:20:54 V6.4
     *@lastEditor kxl 2022-03-30 17:20:54 V6.4
     *@param {array} IDs
     *@return
     */
    public function getUsersInfo()
    {
        $params = ['IDs'];
        list($ids) = $this->getParams($params);
        $this->loadUtil('common');

        $data = $this->callSelfFunc('getUserListByArray', [[['ID', $ids]], 1]);

        //整理返回所需要的字段
        $columns = 'ID,Account,Role,ParentID,UnitID,RoomID,Email,Name,CreateTime,Phone,PhoneStatus,RoomNumber,TimeZone,ExpireTime,Special,CustomizeForm,Active,FirstName,LastName,Phone2,Phone3,PhoneCode,PhoneExpireTime,Language,MobileNumber,UUID,ParentUUID';
        $columns = explode(',', $columns);

        $newData = [];
        foreach ($data as $key => $val) {
            $tmpVal = [];
            foreach ($columns as $column) {
                if (!array_key_exists($column, $val)) {
                    throw new \Exception("Don't exists fields:" . $column);
                }
                $tmpVal[$column] = $val[$column];
            }
            $newData[$key] = $tmpVal;
        }

        return $newData;
    }

    /*
     *@description 根据roomID批量获取房间信息
     *<AUTHOR> 2022-04-06 18:13:41 V6.4
     *@lastEditor kxl 2022-04-06 18:13:41 V6.4
     *@param {array} IDs
     *@return
     */
    public function getAptNO()
    {
        $params = ['IDs'];
        list($ids) = $this->getParams($params);
        $this->loadUtil('common');
        return $this->utils->common->getTableInfoPlus(
            PROXY_TABLES['communityRoom'],
            ['ID', 'RoomName', 'Floor'],
            ['ID' => $ids]
        );
    }

    /**
     * @description 根据UUID获取用户信息
     * <AUTHOR> 2022/4/14 16:27 V6.4
     * @params UUID
     * @return array|mixed
     * @lastEditor csc 2022/4/14 16:27 V6.4
     */
    public function getUserInfoByUUID()
    {
        $params = ['UUID', 'type'];
        list($uuid, $type) = $this->getParams($params);
        $this->loadUtil('common');
        $type = $type ? $type : 'basic';
        $basic = $this->callSelfFunc('getUserListByArray', [[['UUID', $uuid]], 1])[0];
        unset($basic['SipPwd']);
        unset($basic['Passwd']);
        if ($type === 'basic') {
            return $basic;
        }
        $conf = $this->dao->personalAccountCnf->selectByAccount($basic['Account'])[0];
        if ($type === 'conf') {
            return $conf;
        }

        return ['basic' => $basic, 'conf' => $conf];
    }

    public function getUserInfoByUUIDs()
    {
        $params = ['UUID', 'Field'];
        list($uuids, $field) = $this->getParams($params);
        $field = empty($field) ? '' : $field;
        $this->loadUtil('common');

        $basic = $this->callSelfFunc('getUserListByArray', [[['UUID', $uuids]], 1, null, $field]);

        return $basic;
    }

    /*
     *@description 通过key和value获取用户信息
     *<AUTHOR> 2022-12-23 16:19:16 V6.4
     *@lastEditor kxl 2022-12-23 16:19:16 V6.4
     *@param {*} key
     *@param {*} value
     *@param {*} type
     *@return array
     */
    public function getUserInfoByKey()
    {
        $params = ['key', 'value', 'type'];
        list($key, $value, $type) = $this->getParams($params);
        $this->loadUtil('common');
        $type = $type ? $type : 'basic';
        $basic = $this->callSelfFunc('getUserListByArray', [[[$key, $value]], 1])[0];
        unset($basic['SipPwd']);
        unset($basic['Passwd']);
        if ($type === 'basic') {
            return $basic;
        }
        $conf = $this->dao->personalAccountCnf->selectByAccount($basic['Account'])[0];
        if ($type === 'conf') {
            return $conf;
        }

        return ['basic' => $basic, 'conf' => $conf];
    }

    /**
     * @description:终端用户关联智能家居数据
     * @author:lwj 2022-12-30 10:55:09 V6.5.3
     * @lastEditor:lwj 2022-12-30 10:55:09 V6.5.3
     * @param:
     * @return mixed
     */
    public function getSmartHomeUserMapInfoByKey()
    {
        $params = ['key', 'value', 'Field'];
        list($key, $value, $field) = $this->getParams($params);
        $this->loadUtil('common');
        if (empty($field)) $field = [];
        $info = $this->utils->common->getTableInfoPlus(
            PROXY_TABLES['smartHomeUserMap'],
            $field,
            [$key => $value]
        )[0];
        return $info;
    }

    /**
     * @author: cj
     * @Description: 检测用户的PIN/Card是否重复
     * @param {pin/card值} $pin
     * @param {项目的Id} $projectId
     * @param {PIN/Card} $codeType
     * @param {用户ID} $id
     * @return {pin或card重复数目}
     */
    public function getPinCardNumberForPerson($pin, $projectId, $codeType, $id = null)
    {
        $countPerson = 0;
        if ($pin != null) {
            $search = new stdClass();
            $equalField = ["Code" => $pin, "CommunityID" => $projectId];
            $unequalField = [];
            if ($id) {
                $unequalField = ["ID" => $id];
            }
            $search->equalField = $equalField;
            $search->unequalField = $unequalField;
            $this->loadUtil('common');
            if ($codeType == 'PIN') {
                $countPerson = $this->utils->common->getTableCount(PROXY_TABLES['commPerPrivateKey'], $search);
            } else {
                $countPerson = $this->utils->common->getTableCount(PROXY_TABLES['commPerRfKey'], $search);
            }
        }
        return $countPerson;
    }

    /**
     * @description 获取计费模式
     * <AUTHOR> 2022/4/24 9:58 V6.4
     * @params userId  主账号id
     * @return array
     * @lastEditor csc 2022/4/24 9:58 V6.4
     */
    public function getChargeMode()
    {
        $params = [PROXY_ROLE['distributorId']];
        list($disId) = $this->getParams($params);

        $this->loadUtil('common');

        $chargeMode = 1;
        //modified by chenyc,2021-06-30,代码强制走‘pay by installer’
        //        $this->loadUtil('account', true);
        //        $disChargeMode = $this->utils->_common->account->accountSelectByArray([['ID', $disId]], 'ChargeMode')[0]["ChargeMode"];
        //        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        $subscriptions = 1;

        return [$chargeMode, $subscriptions];
    }

    /**
     * @description: 获取从账号那个号添加提示语情况
     * @author: cj
     * @param Active 激活状态
     * @param mainData 主账号信息
     * @return tipCode 提示语状态
     * @LastEditor: cj
     */
    public function getTipForAddSub()
    {
        $params = ['Active', 'mainData', 'MobileNumber', 'Email', 'Grade'];
        list($active, $mainData, $mobile, $email, $grade) = $this->getParams($params);
        $this->loadUtil('common');
        // V6.4 创建从账号提示语
        // 从账号邮箱和手机全为空不同提示语
        if (empty($email) && empty($mobile)) {
            // 主账号邮箱不为空
            if (!empty($mainData['Email'])) {
                $tipCode = 0;
            } else {
                if (!empty($mainData['MobileNumber'])) {
                    // 主账号邮箱为空，但手机号不为空
                    $tipCode = 1;
                } else {
                    // 主账号邮箱和手机号全为空
                    $tipCode = 2;
                }
            }
        }
        return $tipCode;
    }

    /*
     *@description 获取从账户列表
     *<AUTHOR> 2022-06-08 10:45:44 V6.5
     *@lastEditor kxl 2022-06-08 10:45:44 V6.5
     *@return array
     */
    public function getSubList()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm'];
        list($id, $timeZone, $customizeForm) = $this->getParams($params);

        $this->loadUtil('account');
        $userInfo = $this->callSelfFunc('getUserInfo', [$id]);
        $role = $userInfo['Role'];
        if (in_array($role, SUBROLE)) {
            $mainUuid = $userInfo['ParentUUID'];
        } else {
            $mainUuid = $userInfo['UUID'];
        }

        $users = $this->callSelfFunc('getUserListByArray', [[['ParentUUID', $mainUuid]], 0, ['orderBy' => 'A.ID desc']]);

        //整理返回所需要的字段
        $columns = 'ID,UUID,Account,Name,FirstName,LastName,Email,MobileNumber,PhoneCode,Phone,CreateTime,Active,SipAccount,Role,ExpireTime,PhoneExpireTime,Initialization';
        $columns = explode(',', $columns);

        $newData = [];
        foreach ($users as $key => $val) {
            $tmpVal = [];
            foreach ($columns as $column) {
                if (!array_key_exists($column, $val)) {
                    throw new \Exception("Don't exists fields:" . $column);
                }
                $tmpVal[$column] = $val[$column];
            }
            $newData[$key] = $tmpVal;
        }
        $users = $newData;
        //设置OriginTime
        $users = $this->share->util->setOriginTime($users);
        if ($timeZone !== null && $customizeForm != null) {
            $users = $this->share->util->setQueryTimeZone($users, $timeZone, $customizeForm);
        }

        foreach ($users as &$user) {
            $uuid = $user['UUID'];
            $userExtra = $this->dao->personalAccountCommunityInfo->selectByPersonalAccountUUID($uuid, 'AccessFloor,Remark');
            $user['AccessFloor'] = count($userExtra) === 0 ? '' : $userExtra[0]['AccessFloor'];
            $user['AccessFloor'] = $user['AccessFloor'] === '129' ? 'all' : $user['AccessFloor'];

            if (count($userExtra) === 0 || empty($userExtra[0]['Remark'])) {
                $user['Remark'] = '';
            } else {
                $user['Remark'] = $userExtra[0]['Remark'];
            }

            $user['IsMulti'] = $this->callSelfFunc('checkAppLinkStatus', [$user['UUID']]);
        }
        unset($user);

        return $users;
    }

    /*
     *@description 获取一个家庭下所有app
     *<AUTHOR> 2022-06-15 17:13:36 V6.5
     *@lastEditor kxl 2022-06-15 17:13:36 V6.5
     *@param {*} ID 主从账户id
     *@return
     */
    public function getFamilyUsers()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);

        $this->loadUtil('account');
        $userInfo = $this->callSelfFunc('getUserInfo', [$id]);
        $role = $userInfo['Role'];
        if (in_array($role, SUBROLE)) {
            $mainUuid = $userInfo['ParentUUID'];
        } else {
            $mainUuid = $userInfo['UUID'];
        }

        $subs = $this->callSelfFunc('getSubList', [$id]);
        $main = $this->callSelfFunc('getUserInfoByUUID', [$mainUuid]);
        $main = [
            'ID' => $main['ID'],
            'UUID' => $main['UUID'],
            'Account' => $main['Account'],
            'Name' => $main['Name'],
            'FirstName' => $main['FirstName'],
            'LastName' => $main['LastName'],
            'Email' => $main['Email'],
            'MobileNumber' => $main['MobileNumber'],
            'PhoneCode' => $main['PhoneCode'],
            'Phone' => $main['Phone'],
            'CreateTime' => $main['CreateTime'],
            'Active' => $main['Active'],
            'SipAccount' => $main['SipAccount'],
            'Role' => $main['Role'],
            'ExpireTime' => $main['ExpireTime'],
            'PhoneExpireTime' => $main['PhoneExpireTime'],
            'Initialization' => $main['Initialization'],
            'Special' => intval($main['Special'])
        ];
        array_unshift($subs, $main);
        return $subs;
    }

    public function getAppUserConf()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        $data = $this->dao->personalAccount->selectByID($id)[0];
        $account = $data['Account'];
        $appData = $this->db->querySList(
            'select count(*),Node,CreateTime from ' . PROXY_TABLES['appSpecial'] . ' where Account = :Account',
            [':Account' => $account]
        )[0];
        // 只有App注册的社区从账号才有限制
        if ($appData['count(*)'] == 0) {
            return 0;
        } else {
            $now = $this->share->util->getNow();
            $projectId = $this->dao->personalAccount->selectByAccount($appData['Node'], 'ParentID')[0]['ParentID'];
            $isExpired = $this->dao->communityInfo->selectByArray([
                ['AccountID', $projectId],
                ['FeatureExpireTime', $now, '<']
            ], 'count(*)')[0]['count(*)'];
            // Feature Plan 已过期
            if ($isExpired > 0) {
                return 0;
            } else {
                $accountInfo = $this->dao->personalAccountCnf->selectByAccount($appData['Node'], 'AllowCreateSlaveCnt,Flags')[0];
                if ($accountInfo['Flags'] % 2 == 1) {
                    $subAppNumber = $this->db->querySList(
                        'select count(*) from ' . PROXY_TABLES['appSpecial'] . ' where Node = :Node and 
                    CreateTime < :CreateTime',
                        [':Node' => $appData['Node'], ':CreateTime' => $appData['CreateTime']]
                    )[0]['count(*)'];
                    if ($subAppNumber >= $accountInfo['AllowCreateSlaveCnt']) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            }
        }
    }

    /*
     *@description 根据主账号Account获取Sip群主号
     *<AUTHOR> 2023-04-21 16:20:14 V6.6
     *@lastEditor cj 2023-04-21 16:20:14 V6.6
     *@param {*} Account 主账号Account
     *@return sipGroup
     */
    public function getUserSipGroup()
    {
        $params = ['Account'];
        list($mainAccount) = $this->getParams($params);
        $sipGroup = $this->dao->sipGroup2->selectByAccount($mainAccount, 'SipGroup')[0]['SipGroup'];
        return $sipGroup;
    }

    /**
     * @description:获取主账号的落地缴费情况
     * @author:lwj 2023-04-11 14:37:47 V6.6
     * @lastEditor:lwj 2023-04-11 14:37:47 V6.6
     * @param:{sting} ID 主用户ID
     * @return array
     */
    public function getLandLineChargeRes()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        $userData = $this->dao->personalAccount->selectByID($id)[0];
        $phoneStatus = $userData['PhoneStatus'];
        $expireTime = $userData['PhoneExpireTime'];
        $now = $this->share->util->getNow();
        //落地开启、未激活情况，等同于关闭
        if ($phoneStatus === '1' && $expireTime === DEFAULTPHONEEXPIRETIME) {
            $phoneStatus = '0';
        }
        //落地开启，不是未激活但已过期
        if ($expireTime !== DEFAULTPHONEEXPIRETIME && strtotime($expireTime) < strtotime($now)) {
            $landLineChargeRes = '0';
        } else {
            $landLineChargeRes = '1';
        }
        return ['LandLineStatus' => $phoneStatus, 'LandLineChargeRes' => $landLineChargeRes];
    }

    /**
     * @description: 根据条件查询account的信息
     * @param {array} $searchArray [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * @param {array} $options 可选项，orderby和limit参数，格式为：['orderBy' => 'A.ID DESC,AUF.ID DESC', 'limit' => '10,20']或['orderBy' => 'A.ID DESC']
     * @param {string} $fields 可选项，查询的字段名，如果是重复字段需要加上表别名，Account 为A，AccountUserInfo为 AUF，例："A.ID,AUF.Email"
     * @return array
     * @author: csc 2023/5/9 15:25 V6.6
     * @lastEditors: csc 2023/5/9 15:25 V6.6
     */
    public function getManagerListByArray()
    {
        $params = ['searchArray', 'Options', 'Fields'];
        list($searchArray, $options, $fields) = $this->getParams($params);
        if (!is_array($searchArray)) {
            return [];
        }

        if ($options === null or $options === '') {
            $options = [];
        }
        if ($fields === null or $fields === '') {
            $fields = '';
        }

        //抽离出Email、Passwd、LoginAccount的搜索条件为新的一个数组
        $searchArrayUI = [];
        foreach ($searchArray as $key => $val) {
            if (in_array($val[0], ['Email', 'Passwd', 'LoginAccount', 'Type'])) {
                $searchArrayUI[] = $val;
                unset($searchArray[$key]);
            }
        }

        //通过dao层查询数据
        return $this->dao->account->getManagerListByArray($searchArray, $searchArrayUI, $options, $fields);
    }

    /**
     * @description: 根据条件获取personalAccount的信息
     * @param {array} $searchArray [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * @param {int} $includePmApp 是否包含已关联的PmApp数据的查询 0-否 1-是，没传参数时默认为1，包含pm app的查询
     * @param {array} $options 可选项，orderby和limit参数，仅针对$includePmApp为0时有效，格式为：['orderBy' => 'A.ID DESC,AUF.ID DESC', 'limit' => '10,20']或['orderBy' => 'A.ID DESC']
     * @param {string or array} $fields 可选项，查询的字段名，string: 如果是重复字段需要加上表别名，Account 为A，AccountUserInfo为 AUF，例："A.ID,AUF.Email"
     * 注：如果为string时，需要保证结果能够正常查出来（例如查询MobileNumber和PhoneCode，只有PersonalAccountUserInfo表有），此时如果有关联查询AccountInfo则会报错，需要改为数组写法
     * ["'' as MobileNumber,'' as PhoneCode", "AUF.MobileNumber as MobileNumber, AUF.PhoneCode as PhoneCode"],保证union的结果格式一致。第一个值为AccountUserInfo查询的字段，第二个值为PersonalAccountUserInfo的查询字段
     * @return array
     * @author: csc 2023/5/9 15:25 V6.6
     * @lastEditors: csc 2023/5/9 15:25 V6.6
     */
    public function getUserListByArray()
    {
        $params = ['SearchArray', 'IncludePmApp', 'Options', 'Fields'];
        list($searchArray, $includePmApp, $options, $fields) = $this->getParams($params);

        if (!is_array($searchArray)) {
            return [];
        }

        //没传参数时默认为1，包含pm app的查询
        if ($includePmApp === null or $includePmApp === '') {
            $includePmApp = 1;
        }
        $includePmApp = intval($includePmApp);

        if ($options === null or $options === '') {
            $options = [];
        }
        if ($fields === null or $fields === '') {
            $fields = '';
        }

        //抽离出Email、Passwd、MobileNumber的搜索条件为新的一个数组
        $searchArrayUI = [];
        foreach ($searchArray as $key => $val) {
            if (in_array($val[0], ['Email', 'Passwd', 'MobileNumber', 'AppMainUserAccount'])) {
                //MobileNumber、PhoneCode的查询仅针对于PersonalAccountUserInfo表，所以有该字段的查询时，改为只查询PersonalAccountUserInfo
                if (in_array($val[0], ['MobileNumber'])) {
                    $includePmApp = 0;
                }
                $searchArrayUI[] = $val;
                unset($searchArray[$key]);
            }
        }

        //通过dao层查询数据
        return $this->dao->personalAccount->getUserListByArray($searchArray, $searchArrayUI, $includePmApp, $options, $fields);
    }

    /**
     * @description: accountDao的selectByKey方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function accountSelectByKey()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->account->selectByKey($key, $val, $fields);
    }

    /**
     * @description: accountDao的selectByKeyWArray方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function accountSelectByKeyWArray()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->account->selectByKeyWArray($key, $val, $fields);
    }

    /**
     * @description: accountDao的selectByArray方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function accountSelectByArray()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->account->selectByArray($array, $fields);
    }


    /**
     * @description: personalAccountDao的selectByKey方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function personalAccountSelectByKey()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->personalAccount->selectByKey($key, $val, $fields);
    }

    /**
     * @description: personalAccountDao的selectByKeyWArray方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function personalAccountSelectByKeyWArray()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->personalAccount->selectByKeyWArray($key, $val, $fields);
    }

    /**
     * @description: personalAccountDao的selectByArray方法
     * @return mixed
     * @author: csc 2023/5/10 17:30 V6.6
     * @lastEditors: csc 2023/5/10 17:30 V6.6
     */
    public function personalAccountSelectByArray()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->personalAccount->selectByArray($array, $fields);
    }


    /*
     *@description 获取所有link app 站点信息
     *<AUTHOR> 2023-05-15 18:03:42 V6.6.0
     *@lastEditor kxl 2023-05-15 18:03:42 V6.6.0
     *@param {*} UUID personalAccount uuid
     *@return array
     */
    public function getLinkedApp()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);

        //查询当前站点的信息
        $userData = $this->callSelfFunc('getUserInfoByUUID', [$uuid]);
        return $this->dao->personalAccount->selectByKey('UserInfoUUID', $userData['UserInfoUUID']);
    }

    public function getLinkedManager()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);

        //查询当前站点的信息
        $userData = $this->callSelfFunc('getManagerInfoUUID', [$uuid]);

        $accountMaps = $this->dao->accountMap->selectByKey('UserInfoUUID', $userData['UserInfoUUID']);
        $uuids = array_column($accountMaps, 'AccountUUID');
        return $this->dao->account->selectByArray([['UUID', $uuids]]);
    }

    public function getRoomInfo()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        return $this->db->querySList('select * from CommunityRoom where ID=:ID', [':ID' => $id])[0];
    }

    /*
      *@description 根据Account数组获取用户及其Manage相关信息
      *<AUTHOR> 2023-04-28 11:57:50 V6.5.4
      *@lastEditor cj 2023-04-28 11:57:50 V6.5.4
      *@param {array} Accounts
      *@return userData
      */
    public function getManageInfoByNode()
    {
        $params = ['Accounts'];
        list($nodes) = $this->getParams($params);
        $nodes = is_array($nodes) ? $nodes : [$nodes];
        $userData = $this->dao->personalAccount->selectByArray([['Account', $nodes]], 'ParentID,Name,Account');
        $insData = $this->dao->account->getInstallDataForUser(array_column($userData, 'ParentID', null));
        $data = [];
        foreach ($userData as $value) {
            $data[$value['Account']] = [
                'Name' => $value['Name'],
                'Installer' => $insData[$value['ParentID']]
            ];
        }
        return $data;
    }

    /*
      *@description 根据ProjectID数组获取Location及其Manage相关信息
      *<AUTHOR> 2023-04-28 11:57:50 V6.5.4
      *@lastEditor cj 2023-04-28 11:57:50 V6.5.4
      *@param {array} ProjectIds
      *@return data
      */
    public function getManageInfoByProjectId()
    {
        $params = ['ProjectIds'];
        list($projectIds) = $this->getParams($params);
        $projectIds = is_array($projectIds) ? $projectIds : [$projectIds];
        $projectData = $this->dao->account->selectByArray([['ID', $projectIds]], 'ID,Location');
        $insData = $this->dao->account->getInstallDataForUser($projectIds);
        $data = [];
        foreach ($projectData as $value) {
            $data[$value['ID']] = [
                'Location' => $value['Location'],
                'Installer' => $insData[$value['ID']]
            ];
        }
        return $data;
    }


    /*
      *@description 根据ProjectID数组获取Location及其Manage相关信息
      *<AUTHOR> 2024-08-28 09:57:50 V7.0.0
      *@lastEditor kzr 2024-08-28 09:57:50 V7.0.0
      *@param {array} ProjectIds
      *@return data
      */
    public function getOfficeInfo($projectIds){
        $projectIds = is_array($projectIds) ? $projectIds : [$projectIds];

        $projectData = $this->dao->account->selectByArray([['ID', $projectIds]]);
        $uuidToProjectIdMap = [];
        foreach ($projectData as $project) {
            $uuidToProjectIdMap[$project['UUID']] = $project['ID'];
        }
    
        $projectUUID = array_column($projectData, 'UUID');
        $officeInfo = $this->dao->officeInfo->selectByArray([['AccountUUID', $projectUUID]]);
        
        $data = [];
        foreach ($officeInfo as $value) {
            $accountUUID = $value['AccountUUID'];
            if (isset($uuidToProjectIdMap[$accountUUID])) {
                $projectId = $uuidToProjectIdMap[$accountUUID];
                $data[$projectId] = $value;
            }
        }
    
        return $data;
    }      

    /**
     * @description: 查询manage的AccountUserInfo信息
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/9/26 15:37 V6.7.0
     * @lastEditors: csc 2023/9/26 15:37 V6.7.0
     */
    public function getManageAccountInfoByKey()
    {
        $params = ['key', 'value'];
        list($key, $value) = $this->getParams($params);
        $data = $this->dao->accountUserInfo->selectByKey($key, $value)[0];
        unset($data['Passwd']);

        return $data;
    }

    /**
     * @description: 根据项目UUID查询社区用户数据
     * @param {string} $projectUUID
     * @param {string} $column 查询的字段，指定的字段需要增加P.前缀
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/9/18 18:28 V6.7.0
     * @lastEditors: csc 2023/9/18 18:28 V6.7.0
     */
    public function getCommUserInfoByProjUUID()
    {
        $params = ['ProjectUUID:string', 'Column:string'];
        list($projectUUID, $column) = $this->getParams($params);

        if (empty($column)) {
            $column = 'P.*';
        }
        return $this->dao->personalAccount->getCommUserInfoByProjUUID($projectUUID, $column);
    }

    /**
     * @description: 根据项目UUID查询办公用户数据
     * @param {string} $projectUUID
     * @param {string} $column 查询的字段
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/9/18 18:28 V6.7.0
     * @lastEditors: csc 2023/9/18 18:28 V6.7.0
     */
    public function getOfficeUserInfoByProjUUID()
    {
        $params = ['ProjectUUID:string', 'Column:string'];
        list($projectUUID, $column) = $this->getParams($params);

        if (empty($column)) {
            $column = '*';
        }
        return $this->dao->personalAccount->selectByArray([['ParentUUID', $projectUUID], ['Role', [OFFSTAFFROLE, OFFPERSONNELROLE]]], $column);
    }

    /**
     * @description: 根据ins查询其下所有项目信息
     * @param {string} $installerId
     * @return mixed
     * @throws \Exception
     * @author: kzr 2024/07/16 19:30 V7.0.0
     * @lastEditors: kzr 2024/07/16 19:30 V7.0.0
     */
    public function getProjectAccountByIns()
    {
        $params = ['installerId', 'projectType', 'column'];
        list($installerId, $projectType, $column) = $this->getParams($params);
        if (!empty($projectType)&&$projectType == 1) {
            return $this->dao->account->selectByArray([['ManageGroup', $installerId], ['Grade', [COMMUNITYGRADE]]], $column);
        } elseif(!empty($projectType)&&$projectType == 2) {
            return $this->dao->account->selectByArray([['ManageGroup', $installerId], ['Grade', [OFFICEGRADE]]], $column);
        }
        return $this->dao->account->selectByArray([['ManageGroup', $installerId], ['Grade', [COMMUNITYGRADE, PERSONGRADE, OFFICEGRADE]]], $column);

    }


    /**
     * @description: 获取项目的Name (项目->Location) (ins->LoginAccount)
     * @param {string} $installerId
     * @return mixed
     * @throws \Exception
     * @author: kzr 2024/07/16 19:30 V7.0.0
     * @lastEditors: kzr 2024/07/16 19:30 V7.0.0
     */
    public function getProjectInfoByIns()
    {
        $params = ['projectId'];
        list($projectId) = $this->getParams($params);
        
        $project = $this->dao->account->selectByKey('ID',$projectId)[0];
        $name="";
        if ($project['Grade'] == COMMUNITYGRADE) {
            $name = $project['Location'];
        } elseif ($project['Grade']  == OFFICEGRADE) {  
            $name = $project['Location'];
        } else {
            // $name =$this->dao->account->getAccountUserInfoByAccountID($projectId)['LoginAccount'];
            $name = "";
        }

        return $name;
    }

    public function personalAccountUserInfoSelectByKey()
    {
        $params = ['Key', 'Val', 'Fields'];
        list($key, $val, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->personalAccountUserInfo->selectByKey($key, $val, $fields);
    }
}
