<?php
namespace plan\process;
const MESSAGE_PROCESS = [
    "addMessage"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
    		"type"=>"database",
    		"method"=>"begin"
    	],[
            "type"=>"model",
            "model"=>"message.add",
        ],[
    		"type"=>"database",
    		"method"=>"commit"
    	],[
            "type"=>"model",
            "model"=>"notify.messageAdd",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd,
        ]
    ],
    "delMngMessage"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
    		"type"=>"database",
    		"method"=>"begin"
    	],[
            "type"=>"model",
            "model"=>"message.delete",
        ],[
    		"type"=>"database",
    		"method"=>"commit"
    	],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
    "betchDelMngMessage"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
    		"type"=>"database",
    		"method"=>"begin"
    	],[
            "type"=>"model",
            "model"=>"message.betchdelete",
        ],[
    		"type"=>"database",
    		"method"=>"commit"
    	],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
    "getMessageForApp"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"message.getMessageForApp",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMessageDetail"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"message.getDetail",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "delUserMessage"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
    		"type"=>"database",
    		"method"=>"begin"
    	],[
            "type"=>"model",
            "model"=>"message.deleteForApp",
        ],[
    		"type"=>"database",
    		"method"=>"commit"
    	],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
    "getMessagePerUser"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryPerUser",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMessageComUser"=>[
    	[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryComUser",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMessageForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryForMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMessageMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryForMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addMessageTemplate"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"messageTemplate.add"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],

    "editMessageTemplate"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"messageTemplate.edit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "deleteMessageTemplate"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"messageTemplate.delete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "queryMessageTemplate"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"messageTemplate.queryAll"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取message列表
    "getMessageForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"setWebRowToAppRow"],
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryForMng"
        ], [
            "type"=>"model",
            "model"=>"message.formatMessage"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取message user列表
    "getMessageComUserForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"message.queryComUser",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app增加message
    "addMessageForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"message.add",
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"model",
            "model"=>"notify.messageAdd",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd,
        ]
    ],
    //pm app删除message
    "betchDelMngMessageForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"message.betchdelete",
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
];