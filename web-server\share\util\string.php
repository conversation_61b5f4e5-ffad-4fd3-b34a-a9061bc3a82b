<?php
/*
 * @Description: 处理字符串的工具函数，全部为纯函数
 * @version:
 * @Author: kxl
 * @Date: 2020-01-09 14:00:06
 * @LastEditors: cj
 * @lastEditTime 2023-02-23 16:23:08
 */
namespace share\util;

function getClassName($string)
{
    return ucwords($string);
}

function getFileName($string)
{
    return lcfirst($string);
}
/**
 * @name:getModelMethod
 * @msg:
 * @param string
 * @return: array 类名，方法名
 */
function getModelMethod($string)
{
    return explode(".", $string);
}

/**
 * @name: checkByteLength
 * @msg: 字符串字节检测
 * @param {type}
 * @return: boolean
 */
function checkByteLength($str, $length)
{
    return (strlen($str)>$length);
}

function generatePw($len)
{
    $max = rand(2, $len-1);
    $min = rand(1, $max-1);

    $nLen = $max - $min;
    $uLen = $min;
    $lLen = $len - $max;

    $number = '0123456789';
    $uLetter = "ABCDEFGHJKMNOPQRSTUVWXYZ";
    $lLetter = "abcdefghjkmnopqrstuvwxyz";
    $string = '';
    $string = getString($string, $nLen, 10, $number);
    $string = getString($string, $uLen, 24, $uLetter);
    $string = getString($string, $lLen, 24, $lLetter);

    return $string;
}

function getString($string, $len, $num, $chars)
{
    $range = $len;
    for (; $len>0; $len--) {
        $position = rand()%$range;
        $str = rand()%$num;
        $string = substr_replace($string, substr($chars, $str, 1), $position, 0);
    }
    return $string;
}

function randString($len)
{
    $chars='ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
    $string=time();
    for (; $len>=1; $len--) {
        $position=rand()%strlen($chars);
        $position2=rand()%strlen($string);
        $string=substr_replace($string, substr($chars, $position, 1), $position2, 0);
    }
    return $string;
}

//校验mac格式合法情况，true为非法，false为合法
function checkMAC($mac, $isThirdPartMac = false)
{
    if ($isThirdPartMac && in_array(SERVER_LOCATION, THIRD_PART_DEVICE_SERVER_LOCATION)) {
        $rule = "/^[0-9a-zA-Z]{12}$/";
    } else {
        $rule = "/^[0-9a-fA-F]{12}$/";
    }
    if (!preg_match($rule, $mac)) {
        return true;
    } else {
        return false;
    }
}

function randFloat($min = 0, $max = 1)
{
    return $min + mt_rand()/mt_getrandmax() * ($max-$min);
}

function format($string, $keys)
{
    foreach ($keys as $key => $value) {
        $string = str_replace("{".$key."}", $value, $string);
    }

    //如果有修改为关联数组的，也转化为非关联数组进行转化显示
    $oldKeys = array_values($keys);
    foreach ($oldKeys as $key => $value) {
        $string = str_replace("{".$key."}", $value, $string);
    }

    return $string;
}

/**
 * curl get请求封装url
 * @param url: 请求地址
 * @param data: 携带参数
 * @return string
 */
function bmGetUrl($url, $data)
{
    $getUrl = BMURL.$url;
    $index = 0;
    foreach ($data as $key => $val) {
        if ($index == 0) {
            $getUrl .= '?'.$key.'='.$val;
        } else {
            $getUrl .= '&'.$key.'='.$val;
        }
        $index ++;
    }
    return $getUrl;
}

function uuid($prefix = '')
{
    $db = \share\util\getDatabase();
    $uuid = $db->querySList('select uuid() as uuid', [])[0]['uuid'];
    return SERVER_LOCATION.'-'. $prefix . str_replace('-', '', $uuid);
}

/**
 * 字段处理
 * @param $userInfo: 传入的字段; $requireField: 必选字段
 * @param $defaultField 设置默认值
 * @return array
 */
function dealFieldForPA($userInfo, $requireField, $defaultField)
{
    $cMessage = \share\util\getMessage();
    $field = array();
    $count = 0;
    foreach ($defaultField as $key => $val) {
        if (!array_key_exists($key, $userInfo)) {
            $userInfo[$key] = $val;
        }
    }
    foreach ($userInfo as $key => $value) {
        if (in_array($key, $requireField)) {
            ++$count;
        }
        $newKey = ':'.$key;
        $field[$newKey] = $value;
    }
    if ($count !== count($requireField)) {
        $cMessage->echoErrorMsg(STATE_ADD_FAIL);
    }
    return $field;
}

/**
 * @description 隐藏报错异常中的重要信息，例如密码等
 * @param $exception  \Exception | \Exception->toString()
 * @return array|string|string[]
 * @lastEditor csc 2022/9/1 14:06 V6.5
 * <AUTHOR> 2022/9/1 14:06 V6.5
 */
function hideExceptionMsg($exception)
{
    $exceptionStr = (string) $exception;
    $hideInfoArr = [
        'DB_PWD' => DATABASEPW,
        'REDIS_PW' => REDISPW,
        'RPS_DB_PWD' => 'Akuvox2!3#0^1W!',
    ];
    foreach ($hideInfoArr as $key => $val) {
        $exceptionStr = str_replace($val, "**{$key}**", $exceptionStr);
    }
    return $exceptionStr;
}

/**
 * @description 处理返回异常中所有的trace信息
 * @param $exception \Exception | \Exception->toString()
 * @param $hideTrace true | false 是否隐藏trace详细参数信息
 * @return string
 * @lastEditor csc 2022/9/5 14:02 V6.5
 * <AUTHOR> 2022/9/5 14:02 V6.5
 */
function getExceptionMsgWithAllTraces($exception, $hideTrace = true)
{
    $msg = explode("\n" ,(string) $exception)[0];
    $msg .= "\nStack trace:";
    $traces = $exception->getTrace();
    foreach ($traces as $key => $trace) {
        if ($hideTrace) {
            $msg .= "\n#{$key} {$trace['file']}({$trace['line']}) {$trace['class']}{$trace['type']}{$trace['function']}";
        } else {
            $msg .= "\n#{$key} {$trace['file']}({$trace['line']}) {$trace['class']}{$trace['type']}{$trace['function']}, args:";
            $msg .= "\n" . print_r($trace['args'], true);
        }

    }
    return hideExceptionMsg($msg);
}
/**
 * @description: 获取登录token前缀
 * @author:lwj 2022/9/26 17:14 V6.5
 * @lastEditor: lwj 2022/9/26 17:14 V6.5
 * @param $token
 * @param $grade
 * @return string
 */
function getPreTokenName($token, $grade)
{
    $preName = '';
    switch ($grade) {
        //超级管理员
        case SUPERGRADE:
            $preName = 'super';
            break;
        //dis
        case AREAGRADE:
            $preName = 'dis';
            break;
        //社区
        case COMMUNITYGRADE:
            $preName = 'comm';
            break;
        //个人
        case PERSONGRADE:
            $preName = 'personal';
            break;
        //办公
        case OFFICEGRADE:
            $preName = 'office';
            break;
        //pm
        case PROPERTYMANAGE:
            $preName = 'pm';
            break;
        //dis免密登录ins
        case -1:
            $preName = 'dis_ins';
            break;
        default:
            break;
    }
    if ($preName !== '') {
        $preName = base64_encode($preName) . '-';
    }
    return $preName.$token;
}

/**
 * @description: 解析登录token前缀decode
 * @author:lwj 2022/9/26 17:14 V6.5
 * @lastEditor: lwj 2022/9/26 17:14 V6.5
 * @return string
 */
function decodeToken()
{
    $token = \share\util\getServerInfo(TOKEN);
    $tokenArr = explode('-', $token);
    if(count($tokenArr) > 1){
        $preToken = base64_decode($tokenArr[0]);
        $newToken = $preToken.'-'.$tokenArr[1];
    }else{
        $newToken = $tokenArr[0];
    }
    return $newToken;
}

/**
 * @description: 获取中台账号(dis/ins)的UUID
 * @author: cj 2022/11/17 11:11 V6.5.2
 * @return {*} 区域ID-u(代表中台ID)32位
 * @LastEditor: cj 2022/11/17 11:11 V6.5.2
 */
function middlePlatformUUID()
{
    $db = \share\util\getDatabase();
    $uuid = $db->querySList('select uuid() as uuid', [])[0]['uuid'];
    return SERVER_LOCATION.'-u'.str_replace('-', '', $uuid);
}

/**
 * @description:随机生成数字
 * @author:lwj 2023-04-19 16:41:49 V6.6
 * @lastEditor:lwj 2023-04-19 16:41:49 V6.6
 * @param:{int} $min 随机数最小值
 * @param:{int} $max 随机数最大值
 * @return int
 */
function getMtRand($min = 100000, $max = 999999)
{
    return mt_rand($min, $max);
}

/**
 * @description: 生成随机字符串
 * @param $length
 * @param {int} $length
 * @return string
 * @author: csc 2023/5/16 13:47 V6.6
 * @lastEditors: csc 2023/5/16 13:47 V6.6
 */
function getRandomString($length)
{
    $pattern = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLOMNOPQRSTUVWXYZ';
    $key = '';
    for($i=0;$i<$length;$i++)
    {
        $key .= $pattern[mt_rand(0,62)];    //生成php随机数
    }
    return $key;
}

/**
 * 如果$array是一个关联数组，那么将存在于$changeField中的键对应的值转换为int
 * @param: {array} $array 关联数组
 * @param: {array} $changeField 需要改变的键 如 "ID,Type"
 * @return mixed
 * @author: shoubin.chen 2023-12-21 15:05:42 v6.7.1
 * @lastEditor: shoubin.chen 2023-12-21 15:05:42 v6.7.1
 */
function changeStringToNumber($array, $changeField)
{
    $changeFieldArr = explode(',', $changeField);
    foreach ($array as $key => &$item) {
        if (in_array($key, $changeFieldArr)) {
            $item = intval($item);
        }
    }
    unset($item);
    return $array;
}

//凯撒加密
function caesarEncrypt($plaintext, $shift = 5) {
    $result = "";
    $length = strlen($plaintext);
    for ($i = 0; $i < $length; $i++) {
        $char = $plaintext[$i];
        // 判断是否是字母
        if (ctype_alpha($char)) {
            // 判断大写还是小写字母
            $isUppercase = ctype_upper($char);
            // 将字母转换成小写以便处理
            $char = strtolower($char);
            // 加上偏移量并处理超出范围的情况
            $char = chr((ord($char) - 97 + $shift) % 26 + 97);
            // 如果原来是大写字母，加密后也是大写
            if ($isUppercase) {
                $char = strtoupper($char);
            }
        }
        $result .= $char;
    }
    return $result;
}

//凯撒解密
function caesarDecrypt($ciphertext, $shift = 5) {
    // 解密实际上就是加密的逆操作，所以调用加密函数即可
    // 由于加密的偏移量为正，解密时使用相反的偏移量即可
    return caesarEncrypt($ciphertext, 26 - $shift);
}

/**
 * @description: 获取加盐的密码
 * @param {string} $pwd 原始明文密码/md5一次过的密码
 * @param {bool} $md5 pwd是否已经md5一次
 * @return string
 * @author: csc 2024/4/1 9:45 V6.8.0
 * @lastEditors: csc 2024/4/1 9:45 V6.8.0
 */
function getSaltPwd($pwd, $md5 = false)
{
    if (!$md5) {
        $pwd = md5($pwd);
    }
    return md5(PASSWORD_SALT . md5($pwd));
}

/**
 * @description: 校验密码复杂度
 * * @param {string} $pwd 原始明文密码
 * * @return void
 */
function checkPasswordFormat($pwd)
{
    $cMessage = \share\util\getMessage();
    // 校验密码符合以下规则: 1. 至少8位；2. 包含大写字母、小写字母、数字和特殊字符(`~!@#$^&*()=|{}':;',[\].<>/?) 中的至少三种
    if (strlen($pwd) < 8) {
        $cMessage->echoErrorMsg(STATE_PASSWORD_TOO_WEAK);
    }
    $hasUpper = preg_match('/[A-Z]/', $pwd);
    $hasLower = preg_match('/[a-z]/', $pwd);
    $hasNumber = preg_match('/[0-9]/', $pwd);
    $hasSpecialChar = preg_match('/[`~!@#\$^\&*\(\)\=\|\{\}\':;\',\[\]\.\<\>\?]/', $pwd);
    $typesCount = ($hasUpper ? 1 : 0) + ($hasLower ? 1 : 0) + ($hasNumber ? 1 : 0) + ($hasSpecialChar ? 1 : 0);
    if ($typesCount < 3) {
        $cMessage->echoErrorMsg(STATE_PASSWORD_TOO_WEAK);
    }
}

/**
 * @description: 解析校验码，得到一次性密码和MAC
 * @param: $verificationCode
 * @return array|false
 * @author: shoubin.chen 2024/9/12 09:41:36 V6.8.1
 * @lastEditor: shoubin.chen 2024/9/12 09:41:36  V6.8.1
 */
function decodeVerificationCode($verificationCode)
{
    $file_path = realpath(__DIR__ . '/../../config/private_key.pem');
    if ($file_path === false || !file_exists($file_path)) {
        throw new \Exception('Private key file not found');
    }
    $privateKey = file_get_contents($file_path);
    openssl_private_decrypt(hex2bin($verificationCode), $decrypted, $privateKey);
    $position = strpos($decrypted, "Ak");
    if (!$position) {
        return false;
    }
    $mac = substr($decrypted, 0, $position);//获取AK前的字符串
    $rand = substr($decrypted, $position + 2, 6);//获取AK后的6位随机字符串

    $pwd = md5($rand . $mac . $rand);
    return ['Passwd' => $pwd, 'MAC' => $mac];
}