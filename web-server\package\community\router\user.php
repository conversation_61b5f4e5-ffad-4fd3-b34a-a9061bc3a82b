<?php
/*
 * @description: 社区主、从迁移的相关接口
 * @author: cj
 * @date: 2022-03-14 11:13:10
 * @version: V6.4
 * @lastEditor cj
 * @lastEditTime 2022-03-31 14:16:09
 * @lastVersion V6.4
 */

namespace package\community\router;

use framework\BasicRouter;

class User extends BasicRouter
{
    public function exec()
    {
        /**
         * 社区添加房间 及 社区主账号
         * 原接口:addcomroom
         */
        $this->setRouterName('v3', 'web', 'community', 'room', 'add')
            ->setMethod('POST')
            ->addParams('ID', 'BuildID', 'RoomNumber', 'RoomName', 'IsAddUser', 'FirstName', 'LastName', 'Email', 'MobileNumber',
            'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'CallType', 'Key', 'Language', 'EnableIpDirect', 'IsComMonitor', 'MAC:device.macEmpty',
            'ArmingFunction', 'NetGroupNumber', 'Location?:device.location', 'Relay?:device.relay-json', 'WebRelayID', 'Floor', 'AccessFloor',
            'AptCallType:and-rule("required", "enum("0","1")")', 'FirstSequenceCall', 'SecondSequenceCall', 'ThirdSequenceCall', 'Remark')
            ->addRoles(RCOMMUNITYGRADE)
            ->setAuth('web')
            ->setControl('user\\addRoom');

        /**
         * 社区编辑房间 及 社区主账号
         * 原接口: modcomroom
         */
        $this->setRouterName('v3', 'web', 'community', 'room', 'edit')
            ->setMethod('POST')
            ->addParams('ID', 'RoomNumber', 'RoomName', 'IsAddUser', 'FirstName', 'LastName', 'Email', 'MobileNumber',
            'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'CallType', 'Key', 'Language', 'EnableIpDirect', 'IsComMonitor', 'MAC:device.macEmpty',
            'ArmingFunction', 'NetGroupNumber', 'Location?:device.location', 'Relay?:device.relay-json', 'NodeID', 'WebRelayID', 'Floor', 'AccessFloor',
            'AptCallType:and-rule("required", "enum("0","1")")', 'FirstSequenceCall', 'SecondSequenceCall', 'ThirdSequenceCall', 'Remark')
            ->addRoles(RCOMMUNITYGRADE)
            ->setAuth('web')
            ->setControl('user\\updateRoom');

        /**
         * community删除房间
         * 原接口: deletecomuserroom
         */
        $this->setRouterName('v3', 'web', 'community', 'room', 'delete')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE)
            ->setAuth('web')
            ->setControl('user\\deleteRoom');

        /**
         * sup,ins,pm获得楼栋下所有房间
         */
        $this->setRouterName('v3', 'web', 'community', 'room', 'getAllByManager')
            ->setMethod('GET')
            ->addParams('BuildUUID:uuid')
            ->addRoles(RSUPERGRADE, RSUBSUPERGRADE, R_PROJECT_ROLE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\getAllByManager');

        /**
         * 6.1前社区 PM新增社区主账号
         * 原接口:addpuser
         */
        $this->setRouterName('v3', 'web', 'community', 'mainUser', 'addForOldPm')
            ->setMethod('POST')
            ->addParams('Build', 'Room', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'PhoneState', 'Key', 'TempKeyPermission', 'RfCard', 'RoomNumber', 'EnableIpDirect')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\addMainUser');

        /**
         *  v6.1后社区 PM新增主账号第一步check
         *  原接口: addnewpmainusercheck
         */
        $this->setRouterName('v3', 'web', 'community', 'mainUser', 'addCheckForPm')
            ->setMethod('POST')
            ->addParams('RoomID', 'FirstName', 'LastName', 'Email', 'MobileNumber')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\checkUserForNewPm');

        /**
         * v6.1后社区 PM新增主账号
         * 原接口: addnewpmainuser
         */
        $this->setRouterName('v3', 'web', 'community', 'mainUser', 'addForPm')
            ->setMethod('POST')
            ->addParams('RoomID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'Step', 'PIN', 'Card', 'Face', 'AccessGroup', 'Device', 'DateFlag', 'StartTime', 'StopTime', 'StartDay', 'StopDay', 'SchedulerType', 'AccessFloor', 'Mode?:enum("0","1","2")', 'Run?:string', 'Serial?:string', 'Remark',
              'DormakabaRFID?:dormakaba.card','Plate?:licensePlate.licensePlate', 'UHF?:licensePlate.uhf', 'EnableTimeControl', 'PlateBeginTime','PlateEndTime')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\addMainUser');

        /**
         * 旧PM修改主账号
         * 原接口: modpuser
         */
        $this->setRouterName('v3', 'web', 'community', 'mainUser', 'editForOldPm')
            ->setMethod('POST')
            ->addParams('ID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'PhoneState', 'RoomNumber', 'TempKeyPermission', 'EnableIpDirect')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\updateMainUser');

        /**
         * 新PM修改主/从账号信息
         * 原接口: editpuserinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'editForPm')
        ->setMethod('POST')
        ->addParams('ID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'PhoneCode', 'Phone', 'Phone2', 'Phone3', 'Remark')
        ->addRoles(RPROPERTYMANAGE)
        ->setAuth('web')
        ->setControl('user\\updateUserForNewPM');

        /**
         * enUser社区主账号修改信息
         * 原接口:setperosnaldataweb主账号修改自己信息
         *        modifyperosnaluser主账号修改从账号
         */
        // $this->setRouterName('v3', 'web', 'community', 'comUser', 'editForEndUser')
        //     ->setMethod('POST')
        //     ->addParams('PIN', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'FirstName', 'LastName', 'ID')
        //     ->addRoles(RCOMENDMROLE)
        //     ->setAuth('web')
        //     ->setControl('user\\updateUserForEndUser');

        /**
         * 社区添加从账号
         * 原接口：addcommunitysubuser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'add')
            ->setMethod('POST')
            ->addParams('ID', 'Name', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode', 'AccessFloor', 'Remark')
            ->addRoles(RCOMMUNITYGRADE)
            ->setAuth('web')
            ->setControl('user\\addCommunitySubUser');

        /**
         *  v6.1后社区 PM新增从账号第一步check
         *  原接口: addnewpsubusercheck
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'addCheckForPm')
            ->setMethod('POST')
            ->addParams('RoomID', 'FirstName', 'LastName', 'Email', 'MobileNumber')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\addPMSubUserForNewCheck');

        /**
         *  v6.1后社区 PM新增从账号第二步
         *  原接口: addnewpsubuser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'addForPm')
            ->setMethod('POST')
            ->addParams('RoomID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode', 'Step', 'PIN', 'Card', 'Face', 'AccessGroup', 'Device', 'DateFlag', 'StartTime', 'StopTime', 'StartDay', 'StopDay', 'SchedulerType',
                'AccessFloor', 'Mode?:enum("0","1","2")', 'Run?:string', 'Serial?:string', 'Remark', 'DormakabaRFID?:dormakaba.card', 'Plate?:licensePlate.licensePlate', 'UHF?:licensePlate.uhf', 'EnableTimeControl', 'PlateBeginTime','PlateEndTime')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\addPMSubUserForNew');

        /**
         *  v6.1前社区 PM新增从账号
         *  原接口: addpsubuser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'addForOldPm')
            ->setMethod('POST')
            ->addParams('ID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\addCommunitySubUser');

        /**
         *  APP主账号添加从账号
         *  原接口: addpersonaluserapp
         */
        $this->setRouterName('v3', 'app', 'community', 'subUser', 'add')
            ->setMethod('POST')
            ->addParams('Name', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode', 'IsAdmin', 'Remark')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\addSubUserForMain');

        /**
         * app主账号修改从账号信息
         */
        $this->setRouterName('v3', 'app', 'community', 'subUser', 'edit')
            ->setMethod('POST')
            ->addParams('ID', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'PhoneCode', 'Phone', 'Remark')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\updateSubUser');

        /**
         *  endUser主账号添加从账号
         *  原接口: addpersonaluser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'addForEndUser')
            ->setMethod('POST')
            ->addParams('Name', 'FirstName', 'LastName', 'Email', 'MobileNumber', 'Phone', 'PhoneCode')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('web')
            ->setControl('user\\addSubUserForMain');

        /**
         *  旧PM修改从账号信息
         *  原接口: modpsubuser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'editForOldPm')
            ->setMethod('POST')
            ->addParams('ID', 'FirstName', 'LastName', 'Phone', 'PhoneCode', 'Email', 'MobileNumber')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\updateSubUser');

        /**
         *  社区修改从账号
         *  原接口: modcommunitysubuser
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'edit')
        ->setMethod('POST')
        ->addParams('ID', 'FirstName', 'LastName', 'Phone', 'PhoneCode', 'Email', 'MobileNumber', 'AccessFloor', 'Remark')
        ->addRoles(RCOMMUNITYGRADE)
        ->setAuth('web')
        ->setControl('user\\updateSubUser');

        /**
         * 旧PM/installer删除用户
         * 原接口: deletepsubuser            旧PM删除从账号
         *        deletecommunitysubuser    installer删除社区从账号
         *        deletePersonalUser        endUser删除社区从账号
         */
        $this->setRouterName('v3', 'web', 'community', 'subUser', 'delete')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE, RPROPERTYMANAGE, RCOMENDMROLE)
            ->setAuth('web')
            ->setControl('user\\deleteCommunitySubUser');

        /**
         * app删除社区从账号
         * 原接口:deletepersonaluserapp
         */
        $this->setRouterName('v3', 'app', 'community', 'subUser', 'delete')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\deleteCommunitySubUser');

        /**
         * 旧PM/community删除主账号
         * 原接口: deletepuser    旧PM删除主账号
         *        deletecomuser  删除社区主账号
         */
        $this->setRouterName('v3', 'web', 'community', 'mainUser', 'delete')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\deleteCommunityMainUser');

        /**
         * 新PM批量删除主从账号
         * 原接口：deletependuser
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'deleteForPm')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\deleteUserForNewPM');

        /**
         * 注销社区账号
         * 原接口: deleteAccount
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'delete')
            ->setMethod('POST')
            ->addParams('Code')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE)
            ->setAuth('app')
            ->setControl('user\\delUser');

        /**
         * PM修改房间
         * 原接口: modifypapt 新PM修改房间信息
         */
        $this->setRouterName('v3', 'web', 'community', 'room', 'editForPm')
            ->setMethod('POST')
            ->addParams('ID', 'AptName', 'CallType', 'EnableIpDirect', 'TempKeyPermission', 'FamilyMemberControl', 'AllowCreateSlaveCnt', 'RegisterFaceControl',
            'Area?:or-rule("communalFee.number","string-empty")', 'CommunalFee?:communalFee.number', 'AutoSendBills?:enum("0","1")', 'IsDefaultCommunalFee?:enum("0","1")',
            'AllowCreateIDAccess?:enum("0","1")', 'AptCallType:and-rule("required", "enum("0","1")")', 'FirstSequenceCall', 'SecondSequenceCall', 'ThirdSequenceCall', 'AllowCreateRfCard?:enum("0","1")')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\updateRoom');


        // 获取待激活列表
        $this->setRouterName('v3', 'web', 'community', 'user', 'getActiveInfoList')
            ->setMethod('GET')
            ->addParams('ProjectUUID', 'Type', 'InstallerUUID', 'IsBatch', 'row', 'page', 'SortField', 'Sort')
            ->addRoles(RCOMMUNITYGRADE, ROFFICEGRADE, RPERSONGRADE, RAREAGRADE, RPROPERTYMANAGE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('user\\getActiveUserInfo');

        // 获取续费列表
        $this->setRouterName('v3', 'web', 'community', 'user', 'getReNewInfoList')
            ->setMethod('GET')
            ->addParams('ProjectUUID', 'Type', 'InstallerUUID', 'BeginTime', 'EndTime', 'IsBatch', 'row', 'page', 'SortField', 'Sort')
            ->addRoles(RCOMMUNITYGRADE, ROFFICEGRADE, RPERSONGRADE, RAREAGRADE, RPROPERTYMANAGE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('user\\getRenewUserInfo');

        /**
         * 主、从账号重置密码
         * 原接口：resetpwpuser
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'resetPwd')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\resetUserPwd');

        /**
         * 获取计费模式
         * 原接口: getchargemode
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getChargeMode')
            ->setNoAuthActive()
            ->setMethod('GET')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('web')
            ->setControl('user\\getChargeMode');

        /**
         * 获取计费模式
         * 原接口: getchargemodeapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getChargeMode')
            ->setNoAuthActive()
            ->setMethod('GET')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\getChargeMode');

        /**
         * app修改pm账号Name，需求变更app不能修改name，网页端pm名字修改同步修改名称
         */
//        $this->setRouterName('v3', 'app', 'community', 'name', 'edit')
//            ->setMethod('POST')
//            ->addParams('Name')
//            ->addRoles(RPMENDMROLE)
//            ->setAuth('app')
//            ->setControl('user\\updateUserNameForApp');

        /**
         * 获取免费从帐号app信息
         * 原接口: getfamilyfreeapp
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getFreeSubApp')
            ->setMethod('GET')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('web')
            ->setControl('user\\getFreeSubApp');

        /**
         * 获取app添加从账号提示语高级功能等信息
         * 原接口: personalchargeapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getChargeInfo')
            ->setMethod('GET')
            ->addParams('Type')
            ->addRoles(RCOMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\getChargeInfo');


        /**
         * 获取查询从账号收费情况
         * 原接口: getpersonalsubpay
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getSubPayInfo')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RCOMENDMROLE, RCOMMUNITYGRADE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\getSubPayInfo');

        /**
         * 旧pm、新pm获取收费方案
         * 原接口: getpchargeplan
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getChargeInfo')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\getChargeInfo');

        /**
         * 获取主从账户详情，包含房间和楼栋信息
         * 原接口: getpersonaluserinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getUserInfo')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE, RAREAGRADE, RSUPERGRADE, RSUBDISTRIBUTOR, RSUBSUPERGRADE)
            ->setAuth('web')
            ->setControl('user', 'getUserInfo');

        /**
         * 获取房间从账户列表，参数ID可以是主从账户的ID
         * 原接口: getpersonaluserinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getSubList')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE, RCOMMUNITYGRADE, RAREAGRADE, RSUPERGRADE, RSUBDISTRIBUTOR, RSUBSUPERGRADE)
            ->setAuth('web')
            ->setControl('user', 'getSubList');

        /**
         * ins获取小区用户/房间 列表
         * 原接口: getcomuser
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getRoomListByIns')
            ->setMethod('GET')
            ->addParams('Build', 'Status', 'Active', 'row', 'page', 'searchKey', 'searchValue', 'SortField', 'Sort')
            ->addRoles(RCOMMUNITYGRADE)
            ->setAuth('web')
            ->setControl('user', 'getRoomListByIns');

        /**
         * PM获取账户详情
         * 原接口: getpusersinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getInfoByPm')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getUserInfo');

        /**
         * 新PM获取账户列表
         * 原接口: v2/getpenduserlist
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getListByNewPm')
            ->setMethod('GET')
            ->addParams("Build", "Room", "Status", "Active", "Role", "searchKey", "searchValue", "row", "page", 'SortField', 'Sort')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'GetListByNewPm');

        /**
         * 新PM获取房间列表
         * 原接口: v2/getpaptlist
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getRoomListByNewPm')
            ->setMethod('GET')
            ->addParams('Build', 'Apt', 'Key', 'row', 'page', 'SortField', 'Sort')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getAptListByNewPm');
        /**
         * PM获取房间列表
         * 原接口: v2/getpaptinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getRoomInfoByNewPm')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getAptInfoByNewPm');

        /**
         * 获取家庭从账户列表，参数ID可以是主从账户的ID
         * 原接口: getpersonaluserapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getSubList')
            ->setMethod('GET')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE)
            ->setAuth('app')
            ->setControl('user', 'getSubListForApp');
        /*
         * app初始化PIN
         * 原接口:setainitdata
         * 参数：CallType、Phone、Phone2、Phone3、PhoneCode、Password都无用删除，和前端测试已确认
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'setPinInit')
            ->setMethod('POST')
            ->addParams('PIN')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\setProjectUserPinInit');

        /**
         * app修改社区账号信息
         * 原接口:setperosnaldata
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'edit')
            ->setMethod('POST')
            ->addParams('PIN', 'Phone', 'Phone2', 'Phone3', 'PhoneCode', 'FirstName', 'LastName')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setAuth('app')
            ->setControl('user\\updateProjectUserForApp');


        $this->setRouterName('v3', 'web', 'community', 'user', 'getFamilyAppList')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getFamilyApp');

        /**
         * 新PM获取用户详情
         * 原接口:v2/getpuserinfo
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getInfoForNewPm')
            ->setMethod('GET')
            ->addParams('ID')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getInfoForNewPm');

        /**
         * super/dis获取用户列表
         * 原接口:getallpersonaluser Role只有dis检索才有用
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getListForManage')
            ->setMethod('GET')
            ->addParams('row', 'page', 'searchKey', 'searchValue', 'role', "DisUUID", "SubDisUUID", "InsUUID", "Active", "ExpireTimeBegin", "ExpireTimeEnd", 'CreateBeginTime', 'CreateEndTime')
            ->addRoles(RSUPERGRADE, RSUBSUPERGRADE, RAREAGRADE, RSUBDISTRIBUTOR)
            ->setAuth('web')
            ->setControl('user', 'getListForManage');

        /**
         * pm获取小区下楼栋，房间，用户等信息，用于Pin/card添加检索栏
         * 原接口:getcommunitybru
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getRoomResidentForPm')
            ->setMethod('GET')
            ->addParams()
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getRoomResidentForPm');

        /**
         *endUser获取一些通用的配置信息(如dis是否开启了yale锁)
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getConfig')
            ->setMethod('GET')
            ->addParams()
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE)
            ->setAuth('app')
            ->setControl('user', 'getConfig');

        // 社区Reset房间，目前只有新社区
        $this->setRouterName('v3', 'web', 'community', 'room', 'reset')
            ->setMethod('POST')
            ->addParams('ID')
            ->addRoles(RCOMMUNITYGRADE, RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user\\resetRoom');

        // pm导入住户
        $this->setRouterName('v3', 'web', 'community', 'resident', 'import')
            ->setMethod('POST')
            ->addParams('Resident')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'importResidentForPm');

        // pm获取导入详情
        $this->setRouterName('v3', 'web', 'community', 'resident', 'getImportDetails')
            ->setMethod('GET')
            ->addParams('ImportProjectTaskUUIDs')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getImportResidentDetails');

        // pm设置导入任务已读
        $this->setRouterName('v3', 'web', 'community', 'importTask', 'setDeal')
            ->setMethod('POST')
            ->addParams('ImportProjectTaskUUIDs')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'setImportTaskDeal');

        /**
         * super设置endUser
         * 原接口：setusercnf
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'setCnf')
            ->setMethod('POST')
            ->addParams('ID', 'ExpireTime')
            ->setAuth('web')
            ->addRoles(RSUPERGRADE, RSUBSUPERGRADE)
            ->setControl('user\\setCnf');

        /**
         * 用户获取信息
         * 原接口：getpersondata
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getInfo')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\getInfoForApp');

        /**
         * 修改密码前检查密码正确
         * 原接口：personalcheckpw
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'checkPwd')
            ->setMethod('POST')
            ->addParams('PassWd')
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\checkPwd');

        /**
         * 用户自己修改密码
         * 原接口：personalchangepw
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'changePwd')
            ->setMethod('POST')
            ->addParams('PassWd')
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\changePwd');

        /**
         * 用户获取呼叫队列设置
         * 原接口：personalallaindapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getCallQueueList')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE)
            ->setControl('user\\getCallQueueList');

        /**
         * 主用户获取配置信息
         * 原接口：personalusercnfapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getCnf')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE)
            ->setControl('user\\getUserCnf');

        /**
         * 设置用户的呼叫
         * 原接口：personalsetcallapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'setCall')
            ->setMethod('POST')
            ->addParams('EnableRobinCall', 'RobinCallTime', 'RobinCallVal')
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE)
            ->setControl('user\\setCall');

        /**
         * pmapp获取所有主用户列表
         * 原接口：getpmessageusersforapp
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getListForMessage')
            ->setMethod('GET')
            ->addParams('Key', 'Build')
            ->setAuth('app')
            ->addRoles(RPMENDMROLE)
            ->setControl('user\\getListForMessage');

        /**
         * pm获取所有主用户列表
         * 原接口：getpmessageusers
         */
        $this->setRouterName('v3', 'web', 'community', 'user', 'getListForMessage')
            ->setMethod('GET')
            ->addParams('Key', 'Build')
            ->setAuth('web')
            ->addRoles(RPROPERTYMANAGE)
            ->setControl('user\\getListForMessage');

        /**
         * app用户获取创建tempKey的权限
         * 原接口：gettempkeyopera
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getTempKeyPermission')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\getTempKeyPermission');

        /**
         * 获取从账号或者pm账号的电话信息
         * 原接口：getcomsubdata
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getUserPhoneData')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\getUserPhoneData');

        /**
         * 获取用户信息
         * 原接口：getperosnaldata
         */
        $this->setRouterName('v3', 'app', 'community', 'user', 'getUserInfoData')
            ->setMethod('GET')
            ->addParams()
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
            ->setControl('user\\getUserInfoData');
        
        // pm编辑用户的可达楼层
        $this->setRouterName('v3', 'web', 'community', 'user', 'editAccessFloor')
            ->setMethod('POST')
            ->addParams('ID', 'AccessFloor')
            ->setAuth('web')
            ->addRoles(RPROPERTYMANAGE)
            ->setControl('user\\editUserAccessFloor');

        // app社区主账号获取家庭下全部用户列表(包括自己)
        $this->setRouterName('v3', 'app', 'community', 'user', 'getFamilyList')
            ->setMethod('GET')
            ->setAuth('app')
            ->addRoles(RCOMENDMROLE)
            ->setControl('user\\getFamilyListForApp');

        // pm导出住户
        $this->setRouterName('v3', 'web', 'community', 'resident', 'export')
            ->setMethod('GET')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'exportResidentForPm');

        //pm导出房间
        $this->setRouterName('v3', 'web', 'community', 'apartment', 'export')
            ->setMethod('GET')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'exportApartmentForPm');

        // pm批量导入修改住户
        $this->setRouterName('v3', 'web', 'community', 'resident', 'importBatchEdit')
            ->setMethod('POST')
            ->addParams('Resident')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'importBatchEditResident');

        // pm获取旧社区住户列表 原接口getpusers
        $this->setRouterName('v3', 'web', 'community', 'resident', 'getList')
            ->setMethod('GET')
            ->addParams('Build:string', 'Room:string', 'Status:enum("all","0","1")','searchKey:string', 'searchValue:string',
                'page:string-required', 'row:string-required', 'SortField:enum("RoomName","RoomNumber","Name","")', 'Sort:enum("asc","desc","")')
            ->addRoles(RPROPERTYMANAGE)
            ->setAuth('web')
            ->setControl('user', 'getResidentList');

         //app获取用户所在社区的时间设置
         $this->setRouterName('v3', 'app', 'community', 'user', 'getTimeSetting')
         ->setMethod('GET')
         ->addRoles(RCOMENDMROLE, RCOMENDSROLE, RPMENDMROLE)
         ->setAuth('app')
         ->setControl('user', 'getTimeSetting');

        // pm获取房间下的用户列表
        $this->setRouterName('v3', 'web', 'community', 'user', 'getAllList')
        ->setMethod('GET')
        ->addParams('Building?:uuid','Room?:uuid')
        ->addRoles(RPROPERTYMANAGE)
        ->setAuth('web')
        ->setControl('user', 'getAllList');


        return $this->getRouters(); // 最后要返回路由
    }
}
