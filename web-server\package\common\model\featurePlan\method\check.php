<?php
/**
 * @description 检测方法
 * <AUTHOR>
 * @date 2022-06-15 15:02:04
 * @version V6.5
 * @lastEditor kxl
 * @lastEditTime 2022-06-15 15:06:24
 * @lastVersion V6.5
 */

namespace package\common\model\featurePlan\method;

trait Check
{
    public function checkFeaturePlan($featureExpireTime, $item, $isDefaultFeature)
    {
        $now = $this->share->util->getNow();
        $item = decbin($item);
        $num = strlen($item) - 1;
        $items = str_split($item);
        // 获取高级功能数组
        if ($isDefaultFeature == 0) {
            for ($i = 0; $i < FEATURE_PLAN_NUMBERS; ++$i) {
                $data[$i] = '0';
                // 默认开启QRCode 和 PIN
                if ($i == 1 || $i == 2) {
                    $data[$i] = '1';
                }
            }
        } else {
            for ($i = 0; $i < FEATURE_PLAN_NUMBERS; ++$i) {
                if ($num >= 0) {
                    // 有高级功能但是过期,状态为2;有且未过期状态为1;无,状态0
//                    if ($items[$num] == '1' && $featureExpireTime == null) {
//                        $data[$i] = '0';
//                    } elseif ($items[$num] == '1' && strtotime($featureExpireTime) < strtotime($now)) {
//                        $data[$i] = '2';
//                    } else {
//                        $data[$i] = $items[$num];
//                    }
//                    --$num;
                    //7.1.1修改逻辑：高级功能现在都免费，az au的高级功能方案和计费系统对不上，导致fetureExpireTime为空，所以简单兼容，选项有就当做生效
                    if (isset($items[$num]) && $items[$num] == '1') {
                        $data[$i] = '1';
                    } else {
                        $data[$i] = $items[$num];
                    }
                    --$num;
                } else {
                    $data[$i] = '0';
                }
            }
        }

        return $data;
    }

    /**
     * @description: 检测社区功能方案
     * @param {string} MngID
     * @param {Feature} Feature featurePlan 的Item
     * @return bool
     * @throws \Exception
     * @author: csc 2023/8/16 10:59 V6.7.0
     * @lastEditors: csc 2023/8/16 10:59 V6.7.0
     */
    public function checkCommunityFeaturePlan()
    {
        $params = ['MngID:id', 'Feature:enum(1,2,3,4,5,6,7)'];
        list($mngID, $feature) = $this->getParams($params);
        $result = $this->dao->manageFeature->selectByKey('AccountID', $mngID, 'FeatureID');
        $featureID = $result[0]['FeatureID'];
        if ($featureID == 0) {
            if ($feature == FEATURE_ITEM_PIN || $feature == FEATURE_ITEM_TEMPKEY) {
                return true;
            }
            return false;
        }
        $result = $this->dao->featurePlan->selectByID($featureID, 'Item');
        if (!empty($result)) {
            return $this->share->util->getSpecifyBitLE($result[0]['Item'], $feature);
        }
        return false;
    }
}
