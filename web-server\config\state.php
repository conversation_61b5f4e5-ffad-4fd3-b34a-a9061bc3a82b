<?php
/*
 * @DESCRIPTION: 状态码定义
 * @VERSION: RE1.0
 * @AUTHOR: KXL
 * @DATE: 2019-12-19 16:13:48
 * @LASTEDITORS  : KXL
 */
// 成功状态
const STATE_SUCCESS_ADD = 1;
const STATE_SUCCESS_DELETE = 2;
const STATE_SUCCESS_EDIT = 3;
const STATE_SUCCESS_QUERY = 4;
const STATE_SUCCESS_CHANGE_PW = 5;
const STATE_SUCCESS_REGISTER = 6;
const STATE_SUCCESS_SUBMIT = 8;
const STATE_PASSWORD_CORRECT = 10;
const STATE_PASSWD_CHANGE = 11;
const SATE_SUCCESS_IMPORT = 13;
const STATE_SET_SUCCESS = 14;
const STATE_DEAL_SUCCESS = 15;
const STATE_SUCCESS_ADD_PW = 16;
const STATE_SUCCESS_RESET_PW = 17;
const STATE_SUCCESS_PAY = 18;
const STATE_SUCCESS_SEND_EMAIL = 19;
const STATE_SUCCESS_SEND_CODE = 20;//成功发送验证码
const STATE_CORRECT_CODE = 21;//验证码正确
const STATE_SUCCESS_GET_UPGRADE = 22;//成功获取升级内容
const STATE_SUCCESS_PAYMENT_CANCEL = 23;//取消订单成功
const STATE_SUCCESS_CANCEL = 23;
const STATE_SUCCESS_ADD_FEEDBACK = 24; //成功添加feedback
const STATE_SUCCESS_CLEAR = 25;//清除成功
const STATE_SUCCESS_LOGOUT = 26;//登出成功

const STATE_SUCCESS_ADD_SYNC_THIRD_LOCK = 27;
const STATE_SUCCESS_EDIT_SYNC_THIRD_LOCK = 28;

//openapi成功状态
const STATE_OPENAPI_SUCCESS = 100;

// 错误状态
const STATE_404 = 0;
const STATE_405 = 1;
const STATE_500 = 2;
const STATE_EMAIL_EXITS_101 =101;
const STATE_INVAILD_DC_102 = 102;
const STATE_INVALID_IDENTITY = 110;
const STATE_INVALID_IDENTITY_OPENAPI = 111;
const STATE_DEVICE_LOGIN_ERROR = 1000;
const STATE_ACCOUNT_INCORRECT = 1001;
const STATE_ACCOUNT_INCORRECT_2 = 1002;
const STATE_EMAIL_OR_ACCOUNT_NOT_EXIT = 1004;
const STATE_EMAIL_NOT_EXITS = 1005;
const STATE_INVALID_PAGE = 1006;
const STATE_EMAIL_EXITS = 1010;
const STATE_NOT_PERMISSION = 1007;
const STATE_EMAIL_EXITS_1008 = 1008;
const STATE_SENT_CODE_LATER = 1009;
const STATE_EMAIL_EXITS_1010 = 1010;
const STATE_OPERATE_OFTEN = 1011;
const STATE_INVAILD_VER_CODE = 1012;
const STATE_INVAILD_DC_1013 = 1013;
const STATE_INCORRECT_SIP_ACCOUNT = 1014;
const STATE_INCORRECT_SIP_ACCOUNT_GROUP = 1015;
const STATE_SIP_STATUS = 1016;
const STATE_INVAILD_DC = 1020;
const STATE_LIMIT_IP2 = 1021;
const STATE_UN_KNOW_DT = 1017;
const STATE_DEVICE_NOT_FIND_USER = 1019;
const STATE_DEVICE_NOT_BELONG_USER = 1022;
const STATE_LIMIT_WITH_IP = 1030;
const STATE_MAC_EXITS = 2001;
const STATE_NAME_LONG = 2003;
const STATE_LOCATION_LONG = 2004;
const STATE_USER_NOT_EXITS = 2005;
const STATE_ROOM_NOT_EXIT = 2006;
const STATE_ACCOUNT_ID_REQUIRED = 2007;
const STATE_RF_CARD_EXIT = 3001;
const STATE_PRIVATE_KEY_EXISTS = 3002;
const STATE_PRIVATE_KEYS_DATA_EXIST = 3003;
const STATE_PRIVATE_KEY_DATA_EXISTS = 3004;
const STATE_OPERATE_RF_CARD_INVALID = 3005;
const STATE_KEY_LONG = 3006;
const STATE_KEY_EXISTS_FOR_APP = 3007;
const STATE_PAY_FAILED = 5003;
const STATE_PAY_NEXTTIME_INVALID = 5004;
const STATE_PASSWORD_INCORRECT = 6003;
const STATE_UPDATE_FAIL = 6003;
const STATE_ACCOUNT_NOT_EXIT = 7002;
const STATE_ADD_TMPKEY_FAIL = 8001;
const STATE_LIMIT_MINUTE = 8002;
const STATE_CHARGE_MODE_EXCEED_SUB_DIS=8004;

//openapi专用9xxx start
const STATE_OPENAPI_PARAM_ERROR = 9001;
const STATE_OPENAPI_CLIENT_ID_ERROR = 9002;
const STATE_OPENAPI_CLIENT_SECRET_ERROR = 9003;
const STATE_OPENAPI_AUTHORIZATION_CODE_ERROR = 9004;
const STATE_OPENAPI_REFRESH_TOKEN_ERROR = 9005;
const STATE_OPENAPI_TOKEN_EXPIRED_ERROR = 9006;
const STATE_OPENAPI_ACCESS_TOKEN_ERROR = 9007;
const STATE_OPENAPI_PERMISSION_ERROR = 9008;
const STATE_OPENAPI_NOT_KEY_ERROR = 9009;
const STATE_OPENAPI_NOT_ACCOUNT_ERROR = 9010;
const STATE_OPENAPI_PASSWORD_ERROR = 9011;
const STATE_OPENAPI_CODE_EXPIRED_ERROR = 9012;
const STATE_OPENAPI_ACCOUNT_NOT_ACTIVE_ERROR = 9013;
const STATE_OPENAPI_PARAMS_VALID_ERROR = 9014;
const STATE_OPENAPI_TIME_VALID_ERROR = 9015;
const STATE_OPENAPI_NOT_AUTHORITY_DOOR_ERROR = 9016;
const STATE_OPENAPI_ACCOUNT_EXPIRED_ERROR = 9017;
const STATE_OPENAPI_INDOOR_OFFLINE_ERROR = 9018;
const STATE_OPENAPI_FAMILY_MEMBER_TOO_MUCH_ERROR = 9019;
const STATE_OPENAPI_MUTLI_EDIT_EMAIL_ERROR = 9020;
const STATE_OPENAPI_USE_V3_API = 9021;
const STATE_OPENAPI_EMAIL_NOT_EXISTS = 9022;
const STATE_OPENAPI_LIMIT_IP2 = 9023;
const STATE_OPENAPI_START_END_MU = 9024;
const STATE_OPENAPI_ACTIVE_FAMILY_ACCOUNT = 9025;
const STATE_OPENAPI_FAMILY_MEMBER_CANNOT_CREATE = 9026;
const STATE_OPENAPI_STATE_NAME_LONG = 9027;
const STATE_OPENAPI_STATE_EMAIL_EXITS_1010 = 9028;
const STATE_OPENAPI_STATE_QRCODE_INVALID = 9029;
//openapi专用9xxx end

const STATE_INVAILD_ACCOUNT_PW = 10002;
const STATE_NO_ACTIVE_WEB = 10010;
const STATE_ACCOUNT_EXPIRE = 10011;
const STATE_EXPIRE_LOGIN = 10011;
const STATE_MOBILE_NUMBER_NOT_EXIST = 10012;//新增手机号
const STATE_CODE_INCORRECT = 10013;//验证码错误
const STATE_MOBILE_NUMBER_EMPTY = 10014;//手机号为空
const STATE_J_CLOUD_SERVER = 10015;//亚洲云迁移日本云

const STATE_INVAILD_MAC = 20001;
const STATE_REDIRECTED_ON_RPS = 20002;
const STATE_CHECK_MAC_EXITS = 20003;
const STATE_UNBIND_MAC_USER = 20004;
const STATE_INVALID_FILE = 20005;
const STATE_IMPORT_FAIL_MAC_EXIT = 20006;
const STATE_MAC_2_LIBRARY = 20008;
const STATE_MAC_LENGTH = 20009;
const STATE_IMPORT_FAIL_DiS_NOT_EXIST = 20010;
const STATE_IMPORT_FAIL_NOT_PERMISSION = 20011;
const STATE_IMPORT_FAIL_TOO_MANY_ADD = 20012;
const STATE_IMPORT_FAIL_ADDED = 20013;
const STATE_MAC_ASSIGN_TO_LIMIT = 20014;
const STATE_MAC_NUM_TO_LIMIT = 20015;
const STATE_ERROR_ON_RPS = 20016;
const STATE_FACE_IMPORT_LENGTH = 20100;
const STATE_POSTAL_CODE_INVALID = 20110;
const STATE_POSTAL_CODE_NOT_EMPTY = 20111;

const STATE_FACE_IMPORT_ERROR_SYSTEM = 20029;
const STATE_FACE_IMPORT_ERROR_VIEW = 20030;
const STATE_FACE_IMPORT_ERROR_WEAR_MASK = 20031;
const STATE_FACE_IMPORT_ERROR_LOW_RESOLUTION = 20032;
const STATE_FACE_IMPORT_ERROR_WRONG_FORMAT = 20033;
const STATE_FACE_IMPORT_ERROR_NO_FACE = 20034;
const STATE_FACE_IMPORT_ERROR_FILE_LARGE = 20035;
const STATE_FACE_IMPORT_ERROR_FACE_LARGE = 20036;
const STATE_FACE_IMPORT_ERROR_FACE_SMALL = 20037;
const STATE_FACE_IMPORT_ERROR_MULTI_FACES = 20038;
const STATE_FACE_IMPORT_ERROR_WRONG_NAME = 20039;
const STATE_FACE_IMPORT_ERROR_EMPTY_NAME = 20040;
const STATE_FACE_IMPORT_ERROR_NO_ACCOUNT_INFO = 20041;
const STATE_FACE_IMPORT_ERROR_ACCOUNT_INACTIVE = 20042;
const STATE_FACE_IMPORT_ERROR_NOT_CLEAR = 20043;

const STATE_TIME_LESS_CURRENT = 30001;
const STATE_DELETE_FAIL = 30002;
const STATE_ADD_FAIL = 30004;
const STATE_EDIT_FAIL = 30005;

const STATE_ACCOUNT_EXITS = 40001;
const STATE_ACCOUNT_NINCORRECT = 40002;
const STATE_NO_SIP = 40004;
const STATE_BIND_USER = 40005;
const STATE_BIND_DEVICE = 40007;
const STATE_BIND_MAC_LIBRARY = 40006;
const STATE_USER_BIND_USER = 40009;
const STATE_USER_MAX_P_LIMIT = 40010;
const STATE_ID_EXITS_LINE = 40012;
const STATE_ENTER_FIRST_LINE = 40013;
const STATE_ENTER_LAST_LINE = 40014;
const STATE_NEW_OFFICE = 40015;

const STATE_APT_DUPLICATED = 50002;
const STATE_APT_DIGI_ATS = 50003;
const STATE_APT_EXIT = 50004;
const STATE_EMAIL_P_EXIT = 50005;
const STATE_FIRST_NAME_EMPTY = 50006;
const STATE_LAST_NAME_EMPTY = 50007;
const STATE_INVALID_P_NAME = 50008;
const STATE_INVALID_P_EMAIL = 50009;
const STATE_EMAIL_DUPLICATED = 50010;
const STATE_CALL_TYPE_EMPTY = 50011;
const STATE_INVALID_P_CALL_TYPE = 50012;
const STATE_INVALID_P_PIN = 50013;
const STATE_ACTIVE_EMPTY = 50014;
const STATE_INVALID_P_ACTIVE = 50015;
const STATE_INVALID_P_DEVICE_TYPE = 50016;
const STATE_DEVICE_TYPE_EMPTY = 50017;
const STATE_LOCATION_EMPTY = 50018;
const STATE_LOCATION_PLOOG = 50019;
const STATE_BUILDING_EXIT = 50021;
const STATE_BUILDING_BIND_DEVICE = 50022;
const STATE_BUILDING_BIND_USER = 50023;
const STATE_DEPARTMENT_EXIST = 50025;
const STATE_UPLOAD_OVERSIZE = 50026;
const STATE_UPLOAD_INVALID_TYPE = 50027;
const STATE_UPLOAD_FAILED = 50028;
const STATE_UPLOAD_SCREEN_SAVER_TOO_MUCH = 50029;
const STATE_SCREEN_SAVER_IMG_TOO_LITTLE = 50030;
const STATE_SCREEN_SAVER_IMG_TOO_MUCH = 50031;
const STATE_SCREEN_SAVER_DEVICES_OFFLINE = 50032;
const STATE_SAVE_FAILED = 50033;
const STATE_PROJECT_OPEN_LANDLINE = 50034;
const STATE_PIN_MUST_BE_DIGITS = 50035;
const STATE_PIN_CAN_NOT_START_WITH_NINE = 50036;
const STATE_APT_EMPTY = 50037;
const STATE_ANALOG_DEVICE_MORE_THEN_TEN= 50038;
const STATE_INVALID_ANALOG_DEVICE_NAME_IMPORT= 50040;
const STATE_EMPTY_DEVICE_NUMBER_IMPORT= 50041;
const STATE_INVALID_ANALOG_DEVICE_NUMBER_IMPORT= 50042;
const STATE_EXISTS_ANALOG_DEVICE_NUMBER_IMPORT= 50043;
const STATE_EMPTY_ANALOG_DEVICE_NAME_ADD= 50044;
const STATE_INVALID_ANALOG_DEVICE_NAME_ADD= 50045;
const STATE_EMPTY_ANALOG_DEVICE_NUMBER_ADD= 50046;
const STATE_INVALID_ANALOG_DEVICE_NUMBER_ADD= 50047;
const STATE_EXISTS_ANALOG_DEVICE_NUMBER_ADD= 50048;
const STATE_INVALID_REMARK_IMPORT= 50049;
const STATE_NUMBER_CONTROL_PANEL_LIMIT = 50050;
const STATE_NUMBER_CONTROL_PANEL_LIMIT_MAX_REACHED = 50051;
const STATE_ANALOG_DEVICE_DTMF_CODE = 50052;
const STATE_ANALOG_DEVICE_DTMF_CODE_IMPORT = 50053;

const STATE_ERROR_VERSION = 60001;
const STATE_VERSION_EXIT = 60002;
const STATE_VERSION_NAME_NUMBER_EXIT = 60003;
const STATE_VERSION_LOG_MAX_LEN = 60004;

const STATE_ACTIVE_FAMILY_ACCOUNT = 70001;
const STATE_ADD_OUT_APT = 70002;
const STATE_RF_CARD_DUPLICATED = 70003;
const STATE_RF_CARD_EXIT_2 = 70004;
const STATE_NOT_MAC_BIND = 70005;
const STATE_ACCOUNT_NUM_LET = 70006;

const STATE_CANT_DELETE_PIN = 70007;
const STATE_RESIDENT_IN_ROOM = 70008;
const STATE_END_THAN_START = 70009;
const STATE_END_THAN_START_FILE = 70010;
const STATE_SERVER_UPGRADE_TIPS = 70011;
const STATE_NOT_PERMISSION_CODE_5 = 70012;
const STATE_NOT_IMPORT_FAILED = 70013;
const STATE_MOBILE_EXITS = 70014;
const STATE_NOT_MOBILE = 70015;
const STATE_MOBILE_EXITS2 = 70016;
const STATE_MOBILE_DUPLICATED = 70017;
const STATE_INVALID_USER = 70018;
const STATE_LOCATION_EXITS = 70019;

const STATE_RF_CARD_DUPLICATED_LINES = 70020;
const STATE_RF_CARD_NAME_INVALID = 70021;
const STATE_RF_CARD_EXIST_LINES = 70022;
const STATE_IMPORT_FAIL_MAC_EXIST_LINES = 70023;

const STATE_EXPORT_EXCEL_COUNT_NULL = 70024;

const STATE_PM_KEY_IS_EQUAL_ROOM = 70025;

const STATE_COMMUNITY_NAME_EXITS = 70026;
const STATE_EXPORT_EXCEL_DATA_BEFORE = 70027;
const STATE_ACCESS_NAME_EXIST = 70028;
const STATE_ADD_FACE_FAIL = 70029;

const STATE_USER_INVALID = 70030;
const STATE_GROUP_INVALID = 70031;
const STATE_TIME_IN_LINE_INVALID = 70032;

const STATE_PAYMENT_CANCEL_FAILED = 70033;
const STATE_DELETE_FEATURE_PLAN = 70034;
const STATE_FAMILY_MEMBER_BEYOND = 70035;
const STATE_INDOOR_MAC_NOT_CORRECT = 70036;
const STATE_ENTER_VALID_ACCOUNT = 70037;
const STATE_INVALID_KIT_IMPORT_MAC = 70038;
const STATE_IMPORT_LESS_DATA = 70039;
const STATE_QRCODE_INVALID = 70040;
const STATE_FAMILY_MEMBER_CANNOT_CREATE = 70041;
const STATE_IMPORT_PROCESSING = 70042;

const STATE_INDOOR_MONITOR_REQUIRED = 70043;
const STATE_ID_EXIST = 70044;
const STATE_OFFICE_NAME_EXITS = 70045;
const STATE_TOGGLE_FEATURE_PLAN = 70046;
const STATE_LAND_LINE_OPEN_TO_CLOSED_FAIL = 70047;
const STATE_INVALID_KIT_IMPORT_RPS_MAC = 70048;

const STATE_ADD_OUT_FLOOR = 70060;
const STATE_ADD_OUT_FLOOR_LINE = 70061;
const STATE_IMPORT_INVALID_APT_NAME = 70062;
const STATE_ACCESS_FLOOR_COUNT_OUT = 70063;

const STATE_INVALID_LAYOUT = 80000;
const STATE_INVALID_IDENTITY_LINE = 80001;
const STATE_INVALID_SMARTPLUS_INTERCOM_LINE = 80002;
const STATE_ID_DUPLICATED = 80003;
const STATE_IMPORT_OUT_TASK = 80004;
const STATE_APT_INVALID_LINE = 80005;
const STATE_BUILD_INVALID_LINE = 80006;
const STATE_DEPARTMENT_INVALID_LINE = 80007;
const STATE_ID_INVALID_LINE = 80008;
const STATE_DEPARTMENT_BIND_DEVICE = 80030;
const STATE_DEPARTMENT_BIND_USER = 80031;
const STATE_PAY_OUTSTANDING = 80009;
const STATE_WEB_RELAY_ID_INVALID_LINE = 80010;
const STATE_PAY_OUTSTANDING_DIFFERENT_PAYER = 80011;

const STATE_EXPORT_OUT_TASK = 80012;
const STATE_EXPORT_IN_PROGRESS = 80013;

const STATE_BSI_ONCE_CODE_INVALID = 80040;
const STATE_BSI_PERMANENT_CODE_INVALID = 80041;
const STATE_BSI_ONCE_CODE_OUT_NUM = 80042;
const STATE_BSI_PERMANENT_CODE_OUT_NUM = 80043;
const STATE_BSI_ONCE_CODE_EXIST = 80044;
const STATE_BSI_PERMANENT_CODE_EXIST = 80045;

const STATE_ADD_CONTACT_FAVORITE_NUM = 80080;
const STATE_ADD_CONTACT_BLOCK_NUM = 80081;

const STATE_PIN_DUPLICATED_LINES = 80100;
const STATE_PIN_EXIST_LINES = 80101;
const STATE_PIN_INVALID_LINES = 80102;
const STATE_PIN_AND_RFCARD_NOT_NULL_LINES = 80103;
const STATE_LINK_MAX = 80104;
const STATE_LINK_SMARTHOME = 80105;
const STATE_LINK_EXITS = 80106;
const STATE_LINK_SAME_HOME = 80107;

const STATE_DELCODE_GET_LIMIT_TIMES = 90001; //注销接口错误次数达到最大值
const STATE_DELCODE_OVER_LIMIT_TIMES = 90002; //注销接口错误次数超过最大值
const STATE_DELCODE_ERROR = 90003; //注销接口验证码错误

const STATE_RTSP_ADDRESS_EMPTY = 90010;
const STATE_RTSP_ADDRESS_INVALID = 90011;
const STATE_RTSP_PORT_EMPTY = 90012;
const STATE_RTSP_PORT_INVALID = 90013;
const STATE_RTSP_PASSWORD_EMPTY = 90014;
const STATE_RTSP_PASSWORD_INVALID = 90015;
const STATE_CAMERA_EXIST = 90016;

const STATE_CHANGE_HOME_FEATURE_INVALID = 90020;
const STATE_CHANGE_INTERCOM_FEATURE_INVALID = 90021;
const STATE_SERVICE_UNAVAILABLE = 90022;

const STATE_PARAMS_ERROR = 100001; //参数错误
const STATE_PMAPP_STATUS_INVALID = 100002; //PM APP禁用状态
const STATE_NOT_ACTIVATE_LANDLINE_SERVER = 100010; //landline 未开通

const STATE_PARAMS_FOR_REST = 110000; //rest接口的错误码开头,用户对接第三方设备,web和h5调用的接口

const STATE_IMPORT_IN_PROCESS = 120000;
const STATE_IMPORT_BUILDING_INVALID_LINE = 120001;
const STATE_IMPORT_APT_INVALID_LINE = 120002;
const STATE_IMPORT_ACCOUNT_TYPE_INVALID_LINE = 120003;
const STATE_IMPORT_FIRST_NAME_INVALID_LINE = 120004;
const STATE_IMPORT_LAST_NAME_INVALID_LINE = 120005;
const STATE_IMPORT_KEY_INVALID_LINE = 120006;
const STATE_IMPORT_KEY_EXISTS_LINE = 120007;
const STATE_IMPORT_RF_CARD_INVALID_LINE = 120008;
const STATE_IMPORT_RF_CARD_EXISTS_LINE = 120009;
const STATE_IMPORT_ACCESS_GROUP_INVALID_LINE = 120010;
const STATE_IMPORT_ACCESS_GROUP_NO_PERMISSION_LINE = 120011;
const STATE_IMPORT_EXCEEDED_NUMBER_LINE = 120012;
const STATE_IMPORT_NO_ACTIVE_MASTER_LINE = 120013;
const STATE_IMPORT_MASTER_EXISTS_LINE = 120014;
const STATE_IMPORT_NO_CREATE_MASTER_LINE = 120015;
const STATE_IMPORT_LATEST_TEMPLATE = 120016;
const STATE_IMPORT_NAME_INVALID_LINE = 120017;
const STATE_IMPORT_UID_INVALID_LINE = 120018;
const STATE_IMPORT_EMAIL_INVALID_LINE = 120019;
const STATE_IMPORT_MOBILE_INVALID_LINE = 120020;
const STATE_IMPORT_PHONE_CODE_INVALID_LINE = 120021;
const STATE_IMPORT_FLOOR_INVALID_LINE = 120022;
const STATE_IMPORT_APT_NAME_INVALID_LINE = 120023;


const STATE_IMPORT_PLATE_INVALID_LINE = 120024;
const STATE_IMPORT_PLATE_EXISTS_LINE = 120025;
const STATE_IMPORT_UHF_CARD_INVALID_LINE = 120026;
const STATE_IMPORT_UHF_CARD_EXISTS_LINE = 120027;
const STATE_IMPORT_PLATE_MAX=120028;
const STATE_IMPORT_PLATE_EMPTY=120029;
const STATE_IMPORT_EMAIL_EXISTS=120030;
const STATE_IMPORT_INVALID_LANGUAGE=120031;
const STATE_IMPORT_INVALID_COMMUNITY_PROJECT=120032;
const STATE_IMPORT_INVALID_COMMERCIAL_PROJECT=120033;
const STATE_IMPORT_COUNT_LIMIT=120034;
const STATE_IMPORT_ALL_FAIL = 120035;
const STATE_IMPORT_PART_FAIL = 120036;
const STATE_IMPORT_EMAIL_INCONSISTENCIES = 120037;
const STATE_IMPORT_PM_OUT_TASK = 120038;

const STATE_DEVICE_DISCONECT = 130000;
const STATE_NO_MODEL = 130001;
const STATE_UPGRADE_DEV_VER = 130002;
const STATE_PM_LINK_MUL = 130003;
const STATE_PM_LINK_SAME_INS = 130004;
const STATE_PM_LINK_SELF_PM = 130005;
const STATE_LINK_APP_DIFF_CODE = 130006;
const STATE_MOBILE_NO_EXITS = 130007;
const STATE_START_END_MU = 130008;
const STATE_PM_PERMISSION_CLOSE = 130009;
const STATE_TOO_MANY_REMOTE_CONTROL = 130010;
const STATE_LINK_NEW_OFFICE_USER = 130011;

const STATE_START_GUIDE_EXISTS = 140000;
const STATE_ACCOUNT_EXPIRED = 140001;
const STATE_ACCOUNT_NO_ACTIVE = 140002;

const STATE_ITEM_IN_SUBSCRIPTION = 150001;

const STATE_CANCEL_SUBSCRIPTION_FAIL = 150002;
const STATE_COMPANY_CODE_EXITS = 150003;
const STATE_COMPANY_DELETE_ERROR = 150004;
const STATE_RELAY_VALUE_INVAILD = 150005;

const STATE_MAC_ADDRESS_NOT_ADD = 150006;

const STATE_DIS_NOT_EXIT = 150007;
const STATE_CONFIG_CONDISTENT = 150008;
const SUBSCRIPTION_DIS_PAY_INS = 150009;


const STATE_THIRD_PART_MESSAGE = 160000;
const STATE_FILE_EMPTY = 160001;//文件内容为空
const STATE_IMPORT_MAC_EXIST_LINE = 160002;//第 %s 行的MAC已存在
const STATE_IMPORT_DIS_NOT_EXIST_LINE = 160003;//第 %s 行的distributor不存在
const STATE_IMPORT_MAC_FORMAT_LINE = 160004;//第 %s 行的MAC格式不正确
const STATE_IMPORT_RPS_MAC_REDIRECTED_LINE = 160005;//第 %s 行的MAC在RPS上重定向
const STATE_IMPORT_ROW_REPEAT_LINE = 160006;//第 %s 行的数据重复
const STATE_IMPORT_MAC_NOT_ALLOW_LINE = 160007;//第 %s 行的mac不允许添加
const STATE_DEVICE_OFFLINE = 160008;//离线

const STATE_APT_NUMBER_ERROR = 170000;//房间数错误

const STATE_MAC_ADDRESS_ALREADY_ADDED = 260001; //第 %s 行的MAC已存在
const STATE_MAC_DIS_Not_Exist = 260002; //第 %s 行的distributor不存在
const STATE_MAC_INVALID_ADDRESS = 260003; //第 %s 行的MAC格式不正确

//公共设施booking相关
const STATE_BUILDING_INVALID = 180000;//楼栋不合法
const STATE_DEVICE_INVALID = 180001;//设备不合法
const STATE_BUSINESS_HOURS_INVALID = 180002;//BUSINESSHOURS为不合法
const STATE_RESERVATION_TIME_CROSSDAY = 180003;//开始时间和结束时间不能跨天
const STATE_RESERVATION_TIME_INVALID = 180004;//开始时间不能小于结束时间
const STATE_AMENITY_NOT_OPEN = 180005;//设施未开放
const STATE_RESERVATION_TIME_NOT_ALLOW = 180006;//设施当前选择的时间区间,存在不可预约时间段
const STATE_RESERVATION_USER_NOT_BOOKABLE = 180007;//用户不在可预约范围内
const STATE_RESERVATION_TIME_REPEAT = 180008;//在当前时间段内已有预约
const STATE_RESERVATION_DURATION_TIME_INVALID = 180009;//单次预约不能超过设施的最大可预约时长
const STATE_AMENITY_NOT_EXIST = 180010;//The amenity has been deleted.
const STATE_RESERVATION_NOT_EXIST = 180011;//The reservation has been deleted.
const STATE_FEATURE_PLAN_INVALID = 180012;//未开启高级功能
const STATE_AMENITY_NAME_EXIST = 180013;//设施名已存在
const STATE_RESERVATION_CANCELLED = 180014;//The reservation has been cancelled.

const STATE_VISITOR_ID_ACCESS_EXIST = 190000;//社区访客的ID-Access的Run已存在
const STATE_USER_ID_ACCESS_EXIST = 190001;//社区用户已存在一张ID-Access

const  STATE_TEMPLATE_IS_LATEST = 200001;
const  STATE_IS_SIGNED = 200002;


const STATE_HOLDDOOR_TIME_INVALID=210001;
const STATE_HOLDDOOR_RELAY_INVAID=210002;
const STATE_HOLDDOOR_SWITCH_NOT_OPEN=210003;
const  STATE_LANGUAGE_IS_EXIST = 200004;
const  STATE_PARAMETER_EXIST = 220001;
const  STATE_OPERATION_FAILED = 220002;

// RentManager 相关
const STATE_RENTMANAGER_PM_NOT_EXIT=230001;  // PM不存在
const STATE_RENTMANAGER_PM_HAS_BOUND=230002; // PM已经绑定了company
const STATE_RENTMANAGER_DUPLICATED_COMPANYNAME=230003;// installer下company名称重复
const STATE_RENTMANAGER_DUPLICATED_COMPANYCODE=230004;// installer下companyCode重复
const STATE_RENTMANAGER_INVALID_COMPANYCODE=230005;// company的code无效
const STATE_RENTMANAGER_COMPANYNAME_NOT_MATCH=230006;// 同一个company的name与之前的设置不相同
const STATE_RENTMANAGER_CUSTOMER_NOT_EXIST=230007;// customer不存在
const STATE_RENTMANAGER_INS_NOT_EXIT=230008;//ins不存在
const STATE_RENTMANAGER_PM_NOT_BELONG_INS=230009;//pm不属于ins
const STATE_RENTMANAGER_USER_ACCESS_FOLLOW=230010;//用户的权限(权限组和可达楼层)设置为跟随主账户，不允许编辑
const STATE_RENTMANAGER_COMMUNITY_NOT_EXIST=230011;//用户的权限(权限组和可达楼层)设置为跟随主账户，不允许编辑
const STATE_RENTMANAGER_CUSTOMER_EXPIRED_OR_INACTIVE=230012;//customer信息无效
const STATE_RENTMANAGER_PROPERTY_NOT_EXIT=230013;//property不存在
const STATE_RENTMANAGER_PROPERTY_IS_BINDED=230014;//property已经被绑定过
const STATE_RENTMANAGER_PROPERTY_BIND_PROJECT_FAILED=230015;//从远程拉取项目失败
const STATE_RENTMANAGER_DELETE_CUSTOMER_BIND_COMMUNITY=230016;//删除绑定了社区的cutomer
const STATE_RENTMANAGER_INS_HAS_BOUND=230017;//ins已经绑定了customer

const STATE_VERIFICATION_CODE_INVALID = 250001;//非法的校验码
const STATE_INVALID_DEVICE_MAC_NOT_FOR_DIS = 250002;//mac不属于dis

const STATE_INVALID_MODEL_NUMBER_NOT_EXIST = 250003;//model number不存在

const STATE_DELETE_TASK_PROCCESSING = 270001;
const STATE_RESET_PASSWORD_LIND_EXPIRED = 280001;
const STATE_RESET_PASSWORD_OVER_LIMIT_TIMES = 280002;
const STATE_REMOVE_ITEC_ACCESSGROUP = 280003;
const STATE_PASSWORD_TOO_WEAK = 280004;

// 系统通知
const STATE_NOTIFICATION_NOT_EXISTS = 290001;
// 给网管系统的错误码
const STATE_MAINTENANCE_AUTH_ERROR = 300000;

// pmApp门常开
const STATE_ACCESSGROUP_EXCEED_LIMIT=310000;

// 即插即用模块
const STATE_QUICK_DEPLOY_SYSTEM_ERROR = 320000;//系统错误
const STATE_QUICK_DEPLOY_PARAM_ERROR = 320001;//请求参数错误

// 三方锁权限组
const STATE_USER_CAN_ONLY_IN_ONE_ACCESSGROUP_PERLOCK = 330001; // XX1 is already in the XX2 access group and can only be in one access group per lock.
const STATE_ITEC_PIN_FORMAT_ERROR_LINE = 330003;//iTec锁的pin格式错误
const STATE_DORMAKABA_CARD_EXIST = 330004;//dormakaba卡号已存在
const STATE_DORMAKABA_ACCOUNT_NOT_EXISTS = 330005;
const STATE_DORMAKABA_CARD_FORMAT_ERROR_LINE = 330006; //dormakaba卡号格式错误
const STATE_DORMAKABA_PIN_FORMAT_ERROR_LINE = 330007; //dormakaba的pin格式错误

// 车牌
const STATE_LICENSE_NUM_MAX=350000;//个人下的车牌到达上限
const STATE_PLATE_INVALID=350001; // 车牌号不合法
const STATE_UHF_INVALID=350002;// uhf卡号不合法
const STATE_PLATE_EXSIT=350003; // 车牌号存在
const STATE_UHF_EXSIT=350004;// uhf卡号存在