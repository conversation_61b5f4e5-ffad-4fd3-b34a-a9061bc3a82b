<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

include_once __DIR__ . "/../../util/model.php";
include_once __DIR__ . "/../../util/string.php";
include_once __DIR__ . "/../../util/computed.php";
include_once __DIR__ . "/../../util/time.php";


include_once __DIR__ . "/../basic/user.php";

class CTempKey
{
    public function queryForPM()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "webRowToAppRow" => "",
            'IsGetNowTempKey' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $webRowToAppRow = $params["webRowToAppRow"]; //是否app调用的
        $isGetNowTempKey = $params['IsGetNowTempKey'] ? true:false; // app是否获取当前创建TempKey
        $where = "";
        $bindArray = [":MngAccountID" => $userId];
        if ($serchKey == "Description") {
            $where = " and P.Description like :Key";
            $bindArray[":Key"] = "%$serchValue%";
        }

        $sql = "select '' as Node,P.ID,P.SchedulerType,P.Code,P.IDNumber,P.Description,P.BeginTime,
            P.EndTime,P.StartTime,P.StopTime,P.AllowedTimes,P.EachAllowedTimes,P.QrCodeUrl,P.Account as Creator,P.CreateTime, 1 as Type, 0 as TempkeyType,
            P.DateFlag,P.AccessTimes from PubAppTmpKey P
			where P.MngAccountID = :MngAccountID $where
			union all select P.Node,P.ID,P.SchedulerType,P.TmpKey as Code,P.IDNumber,P.Description,P.BeginTime,
			P.EndTime,P.StartTime,P.StopTime,P.AllowedTimes,P.EachAllowedTimes,P.QrCodeUrl,A.Name as Creator,P.CreateTime,0 as Type, P.Type as TempkeyType,
			P.DateFlag,P.AccessTimes from PersonalAppTmpKey P
			join PersonalAccount A on P.Creator = A.Account
			where P.MngAccountID = :MngAccountID $where";
        $data = [];
        $data["total"] = count($this->db->querySList($sql, $bindArray));

        $sort = "CreateTime desc";
        if (true === $webRowToAppRow) {
            $nowTime = \util\time\setTimeZone(\util\computed\getNow(), $timeZone, 3);
            $sort = "FIELD((AllowedTimes > AccessTimes && EndTime > '$nowTime') || (SchedulerType != 0 && SchedulerType != 3), 1, 0) asc,CreateTime desc";
        }

        if ($isGetNowTempKey === true) {
            $sql .= " order by CreateTime desc limit 1";
        } else {
            $sql .= " order by $sort limit $offset,$rows";
        }
        $data["row"] = $this->db->querySList($sql, $bindArray);

        foreach ($data["row"] as &$val) {
            if ($val["Type"] == 1) {
                $devices = $this->db->querySList(
                    "select D.Location from Devices D join PubAppTmpKeyList L on D.MAC = L.MAC where L.KeyID = :ID",
                    [":ID" => $val["ID"]]
                );
            } else {
                $devices = $this->db->querySList(
                    "select D.Location from PersonalAppTmpKeyList P join Devices D on D.MAC = P.MAC where P.KeyID = :ID",
                    [":ID" => $val["ID"]]
                );
            }
            $location = [];
            foreach ($devices as $value) {
                array_push($location, $value["Location"]);
            }
            $val["Location"] = implode(";", $location);
            $val["ID"] = $val["Type"] . "_" . $val["ID"];
            $val["QrCodeUrl"] = $val["QrCodeUrl"];
            $val["BeginTime"] = \util\time\setCustomizeFormat(strtotime($val["BeginTime"]), $customizeForm);
            $val["EndTime"] = \util\time\setCustomizeFormat(strtotime($val["EndTime"]), $customizeForm);
            $val["StartTime"] = substr(\util\time\setCustomizeFormat(strtotime("2000-01-01 " . $val["StartTime"]), $customizeForm), 11);
            $val["StopTime"] = substr(\util\time\setCustomizeFormat(strtotime("2000-01-01 " . $val["StopTime"]), $customizeForm), 11);
            if ($val["SchedulerType"] == 0) {
                $val["Count"] = $val['AllowedTimes'];
                $val['BeginTime'] = \util\time\setCustomizeFormat(strtotime($val["CreateTime"]), $customizeForm);
            } elseif ($val['SchedulerType'] == 3) {
                $val["Count"] = $val['EachAllowedTimes'];
            } else {
                $val["Count"] = "--";
                $val['BeginTime'] = $val['StartTime'];
                $val["EndTime"] = $val['StopTime'];
            }
            $val["Repeats"] = [
                MSGTEXT["never"],
                MSGTEXT["daily"],
                MSGTEXT["weekly"],
                MSGTEXT["never"]
            ][$val["SchedulerType"]];
            $val['IsEncryptPin'] = NOTENCRYPTED;
            //快递pin描述用词条库 保证语言切换
            if ($val['TempkeyType'] == 2) {
                $val['Description'] = MSGTEXT['delivery_description'];
            }
        }

        //v6.1新pm的删除id取得是detail，这里是为了保证ID相同
        $data["detail"] = $data["row"];
        //V6.1修改 判断dis是否有加密，加密返回****
        $dis = \util\role\getDisForInstaller($userId);
        if ($dis['IsEncryptPin'] == ENCRYPTION) {
            foreach ($data['row'] as &$row) {
                $row['Code'] = \util\computed\setPinIsEncryptPin($userId, $row['Code']);
                $row['IsEncryptPin'] = ENCRYPTION;
                unset($row['QrCodeUrl']);
            }
            foreach ($data['detail'] as &$detail) {
                $detail['Code'] = \util\computed\setPinIsEncryptPin($userId, $detail['Code']);
                unset($detail['QrCodeUrl']);
            }
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function add()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "MAC" => "",
            "DateFlag" => "",
            "StartTime" => "",
            "StopTime" => "",
            "BeginTime" => "",
            "ExpireTime" => "",
            "SchedulerType" => "",
            "Allow" => "",
            "Description" => "",
            "IDNumber" => "",
            "Delivery" => "",
            "Room" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $macs = $params["MAC"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["ExpireTime"];
        $schedulerType = $params["SchedulerType"];
        $allowedTimes = $params["Allow"];
        $description = $params["Description"];
        $idNumber = $params["IDNumber"];
        $email = $params["Delivery"];
        $room = $params["Room"];
        $eachAllowedTimes = 0;
        // 2022/3/9-v6.4-zyc: schedulerType === 3时取前端的beginTime,否则取系统当前时间
        if ($schedulerType === "3") {
            if (strtotime($endTime) <= strtotime($beginTime)) {
                $cMessage->echoErrorMsg(StateEndThanStart);
            }
            $eachAllowedTimes = $allowedTimes;
            //如果又改回relay算次数，需要（securityNum+relayNum) * $allowedTimes
//            $relayNum = 0;
//            foreach ($macs as $mac) {
//                $relayNum += count(explode(";", $mac['Relay']));
//            }
//            $allowedTimes = intval($allowedTimes) * $relayNum;
            $allowedTimes = intval($allowedTimes) * count($macs);
        } else {
            $beginTime = \util\computed\getNow();
            $beginTime = \util\time\setTimeZone($beginTime, $timeZone, $customizeForm);
        }

        $keyId = $this->addControl(
            $userId,
            $description,
            $dateFlag,
            $beginTime,
            $endTime,
            $startTime,
            $stopTime,
            $schedulerType,
            $idNumber,
            $allowedTimes,
            $macs,
            $room,
            $eachAllowedTimes
        );
        \util\computed\setGAppData(["ID" => $keyId, "Email" => $email]);
        // shareTmpkeyEmailNotify($email,$keyId,0);
    }

    public function addControl(
        $userId,
        $description,
        $dateFlag,
        $beginTime,
        $endTime,
        $startTime,
        $stopTime,
        $schedulerType,
        $idNumber,
        $allowedTimes,
        $macs,
        $room,
        $eachAllowedTimes
    ) {
        global $cMessage;
        if (
            $schedulerType != 0 && $schedulerType != 3 &&
            (strtotime("2000-01-01 $stopTime") <= strtotime("2000-01-01 $startTime"))
        ) {
            $cMessage->echoErrorMsg(StateEndThanStart);
        }
        $params = [
            "user" => "",
            "userAliasId" => "",
            "OfficeUserID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["user"];
        $communityId = $params["userAliasId"];
        // 获取周几序列
        $dateFlag = explode(";", $dateFlag);
        $tmpDate = 0;
        foreach ($dateFlag as $value) {
            $tmpDate += DATEFLAG[$value];
        }
        $times = 0;
        $msgID = $userId;
        while ($times < 10) {
            $times++;
            $code = floor((9 + \util\string\randFloat()) * ********); //tmpkey由系统生成
            if (
                !$this->db->isExistFiled("PubAppTmpKey", [":MngAccountID" => $msgID, ":Code" => $code])
                && !$this->db->isExistFiled("PersonalAppTmpKey", [":MngAccountID" => $msgID, ":TmpKey" => $code])
            ) {
                break;
            }
        }
        if ($times >= 10) {
            $cMessage->echoErrorMsg(StateAddTmpKeyFail);
        }

        $grade = $this->db->querySList("SELECT Grade FROM Account WHERE ID = $communityId", [])[0]['Grade'];
        if ($room && $grade == COMMUNITYGRADE) {
            if ($grade == COMMUNITYGRADE) {
                $accountId = $this->db->querySList(
                    "select ID from PersonalAccount where RoomID = :RoomID and Role = 20",
                    [":RoomID" => $room]
                )[0]["ID"];
            }
        }

        if ($grade == OFFICEGRADE) {
            $accountId = $params['OfficeUserID'];
        }


        $this->db->insert2List(
            "PubAppTmpKey",
            [
                ":MngAccountID" => $msgID,
                ":Account" => $user,
                ":IDNumber" => $idNumber,
                ":Code" => $code,
                ":StartTime" => $startTime,
                ":StopTime" => $stopTime,
                ":PersonalAccountID" => $accountId,
                ":DateFlag" => $tmpDate,
                ":SchedulerType" => $schedulerType,
                ":BeginTime" => $beginTime,
                ":EndTime" => $endTime,
                ":AllowedTimes" => $allowedTimes,
                ":Description" => $description,
                ":EachAllowedTimes" => $eachAllowedTimes,
            ]
        );

        $keyId = $this->db->lastInsertId();
        foreach ($macs as $value) {
            $relay = $value["Relay"];
            $relays = explode(";", $relay);
            $relay = 0;
            foreach ($relays as $val) {
                $relay += \util\computed\getRelayValue($val);
            }

            $securityRelays = explode(';', $value['SecurityRelay']);
            $securityRelay = 0;
            foreach ($securityRelays as $val) {
                $securityRelay += \util\computed\getRelayValue($val);
            }

            $this->db->insert2List(
                "PubAppTmpKeyList",
                [":MAC" => $value["MAC"], ":Relay" => $relay, ":SecurityRelay" => $securityRelay, ":KeyID" => $keyId]
            );
        }
        $account = $this->db->querySlist(
            'select Account from Account where ID=:ID',
            [':ID' => $communityId]
        )[0]['Account'];
        $this->auditLog->setLog(AuditCodeAddTemp, $this->env, [$code], $account);

        return $keyId;
    }

    public function delete()
    {
        global $cMessage;
        $params = [
            "userAliasId" => "",
            "ID" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $id = explode(";", $id);
        foreach ($id as $val) {
            $this->deleteControl($userId, $val);
        }
    }

    public function deleteControl($userId, $id)
    {
        global $cMessage;
        list($type, $id) = explode("_", $id);
        if ($type == 1) {
            $data = $this->db->querySList(
                "select ID,IDNumber,Code from PubAppTmpKey where ID = :ID and MngAccountID = :MngAccountID",
                [":ID" => $id, ":MngAccountID" => $userId]
            );
            if (count($data) == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->db->delete2ListWKey("PubAppTmpKeyList", "KeyID", $id);
            $this->db->delete2ListWKey("PubAppTmpKey", "ID", $id);
        } else {
            $data = $this->db->querySList(
                "select ID,IDNumber,TmpKey as Code from PersonalAppTmpKey where ID = :ID and MngAccountID = :MngAccountID",
                [":ID" => $id, ":MngAccountID" => $userId]
            );
            if (count($data) == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->db->delete2ListWKey("PersonalAppTmpKeyList", "KeyID", $id);
            $this->db->delete2ListWKey("PersonalAppTmpKey", "ID", $id);
        }

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeDeleteTemp, $this->env, [$data[0]["Code"]], $account);
    }

    public function afterDelete()
    {
        $params = [
            "userAliasId" => "",
            "ID" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        // delCommunitMngTmpKeyQrCode($userId,$id);
    }

    public function getInfo()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "ID" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];

        $tempkeyInfo = $this->getInfoList();
        $data = $tempkeyInfo[$id];

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @description 获取tempkey详细数据
     * <AUTHOR> 2022/5/6 14:56 V6.4
     * @return array
     * @lastEditor csc 2022/5/6 14:56 V6.4
     */
    public function getInfoList()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "ID" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $id = $params["ID"];
        $ids = explode(';', $id);
        $pubIds = $perIds = [];
        //分类是公共的tempkey还是私人的tempkey
        foreach ($ids as $id) {
            list($type, $id) = explode("_", $id);
            if ($type == 1) {
                $pubIds[] = intval($id);
            } else {
                $perIds[] = intval($id);
            }
        }
        $grade = $this->db->querySList("SELECT Grade FROM Account WHERE ID = $userId", [])[0]['Grade'];

        $allData = [];
        if (!empty($pubIds)) {
            $pubIds = \util\arr\implodeWithQuotation($pubIds);
            if ($grade == COMMUNITYGRADE) {
                $data = $this->db->querySList(
                    "select P.*,A.RoomNumber,C.RoomName,C.Floor,U.UnitName,A.Name as People from PubAppTmpKey P 
                    left join PersonalAccount A on P.PersonalAccountID = A.ID 
                    left join CommunityRoom C on C.ID = A.RoomID 
                    left join CommunityUnit U on U.ID=C.UnitID 
                    where P.ID in ({$pubIds}) and P.MngAccountID = :MngAccountID",
                    [":MngAccountID" => $userId]
                );
            }

            if ($grade == OFFICEGRADE) {
                $data = $this->db->querySList(
                    "select P.*,A.RoomNumber,U.UnitName,A.Name as People from PubAppTmpKey P 
                    left join PersonalAccount A on P.PersonalAccountID = A.ID  
                    left join CommunityUnit U on U.ID=A.UnitID where P.ID in ({$pubIds}) 
                    and P.MngAccountID = :MngAccountID",
                    [":MngAccountID" => $userId]
                );
            }
            $device = $this->db->querySList(
                "select D.Location,D.MAC,U.UnitName,D.Relay,D.SecurityRelay,P.Relay as TRelay,P.SecurityRelay as TSecurityRelay,P.KeyID from PubAppTmpKeyList P join Devices D on D.MAC = P.MAC 
            left join CommunityUnit U on U.ID = D.UnitID where P.KeyID  in ({$pubIds})"
            );

            $relayStatus = [1, 2, 4, 8];
            $securityRelayStatus = [1, 2];
            foreach ($device as &$val) {
                $tRelay = $val["TRelay"];
                $relay = $val["Relay"];
                $relay = explode(";", $relay);
                $temRelay = [];
                foreach ($relayStatus as $key => $relayValue) {
                    if (($tRelay & $relayValue) == $relayValue) {
                        array_push($temRelay, explode(",", $relay[$key])[1]);
                    }
                }
                $val["Relay"] = implode(";", $temRelay);

                $tSecurityRelay = $val['TSecurityRelay'];
                $securityRelay = $val['SecurityRelay'];
                $securityRelay = explode(';', $securityRelay);
                $temSecurityRelay = [];
                foreach ($securityRelayStatus as $key => $securityRelayValue) {
                    if (($tSecurityRelay & $securityRelayValue) == $securityRelayValue) {
                        array_push($temSecurityRelay, explode(",", $securityRelay[$key])[1]);
                    }
                }
                $val['SecurityRelay'] = implode(';', $temSecurityRelay);
            }
            unset($val);

            $device = \util\arr\formatArrByKey($device, 'KeyID');

            $newData = [];
            foreach ($data as $val) {
                $keyId = $val['ID'];
                // V6.5.1 增加Floor字段
                if ($val['Floor'] !== '' && $val['RoomName'] !== null) {
                    $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
                }
                $val['IsOld'] = 0;
                $val["TmpKey"] = $val["Code"];
                $val["CreatorName"] = $val["Account"];
                $val["device"] = isset($device[$keyId]) ? $device[$keyId] : [];
                $newData["1_".$keyId] = $val;
            }
            $allData = array_merge($allData, $newData);
        }

        if (!empty($perIds)) {
            $oldPerIds = $perIds;
            $perIds = \util\arr\implodeWithQuotation($perIds);
            if ($grade == COMMUNITYGRADE) {
                $data = $this->db->querySList(
                    "select P.*,A.Name as CreatorName,A.RoomNumber,C.RoomName,U.UnitName,A.Name as People from PersonalAppTmpKey P 
                    left join PersonalAccount A on P.Creator = A.Account left join CommunityRoom C on C.ID = A.RoomID 
                    left join CommunityUnit U on U.ID=C.UnitID where P.MngAccountID = :MngAccountID and P.ID in ({$perIds})",
                    [":MngAccountID" => $userId]
                );
            }

            if ($grade == OFFICEGRADE) {
                $data = $this->db->querySList(
                    "select P.*,A.Name as CreatorName,A.RoomNumber,U.UnitName,A.Name as People from PersonalAppTmpKey P 
                    left join PersonalAccount A on P.Creator = A.Account left join CommunityUnit U on U.ID=A.UnitID
                     where P.MngAccountID = :MngAccountID and P.ID in ({$perIds})",
                    [":MngAccountID" => $userId]
                );
            }
            $data = array_column($data, null, 'ID');

            $devices = $this->db->querySList(
                "select D.Location,D.MAC,U.UnitName,D.Relay,D.SecurityRelay,P.Relay as TRelay,P.SecurityRelay as TSecurityRelay,P.KeyID from PersonalAppTmpKeyList P join Devices D on D.MAC = P.MAC
        left join CommunityUnit U on U.ID = D.UnitID where P.KeyID in ({$perIds})"
            );

            $relayStatus = [1, 2, 4, 8];
            $securityRelayStatus = [1, 2];
            foreach ($devices as &$val) {
                $tRelay = $val["TRelay"];
                $relay = $val["Relay"];
                $relay = explode(";", $relay);
                $temRelay = [];
                foreach ($relayStatus as $key => $relayValue) {
                    if (($tRelay & $relayValue) == $relayValue) {
                        array_push($temRelay, explode(",", $relay[$key])[1]);
                    }
                }
                $val["Relay"] = implode(";", $temRelay);

                $tSecurityRelay = $val['TSecurityRelay'];
                $securityRelay = $val['SecurityRelay'];
                $securityRelay = explode(';', $securityRelay);
                $temSecurityRelay = [];
                foreach ($securityRelayStatus as $key => $securityRelayValue) {
                    if (($tSecurityRelay & $securityRelayValue) == $securityRelayValue) {
                        array_push($temSecurityRelay, explode(",", $securityRelay[$key])[1]);
                    }
                }
                $val['SecurityRelay'] = implode(';', $temSecurityRelay);
            }
            unset($val);
            $devices = \util\arr\formatArrByKey($devices, 'KeyID');

            $newData = [];
            foreach ($data as $val) {
                $keyId = $val['ID'];
                $val['IsOld'] = 0;
                $val["device"] = isset($devices[$keyId]) ? $devices[$keyId] : [];
                $newData["0_".$keyId] = $val;
            }
            $allData = array_merge($allData, $newData);
        }

        foreach ($allData as &$data) {
            $data["Repeats"] = [
                MSGTEXT["never"],
                MSGTEXT["daily"],
                MSGTEXT["weekly"],
                MSGTEXT['never']
            ][$data["SchedulerType"]];
            $data["DateFlag"] = \util\computed\getDateScript($data["DateFlag"]);
            $data["BeginTime"] = \util\time\setCustomizeFormat(strtotime($data["BeginTime"]), $customizeForm);
            $data["EndTime"] = \util\time\setCustomizeFormat(strtotime($data["EndTime"]), $customizeForm);
            $data["StartTime"] = substr(
                \util\time\setCustomizeFormat(strtotime("2000-01-01 " . $data["StartTime"]), $customizeForm),
                11
            );
            $data["StopTime"] = substr(
                \util\time\setCustomizeFormat(strtotime("2000-01-01 " . $data["StopTime"]), $customizeForm),
                11
            );
            //V6.1修改 判断dis是否有加密，加密返回****
            if (\util\role\getDisForInstaller($userId)["IsEncryptPin"] == ENCRYPTION) {
                unset($data['QrCodeUrl']);
            }
            $data['TmpKey'] = \util\computed\setPinIsEncryptPin($userId, $data['TmpKey']);
            if ($data["SchedulerType"] === '3') {
                $data['AllowedTimes'] = $data['EachAllowedTimes'];
            }
        }
        unset($data);

        return $allData;
    }

    /**
     * @description 将网页版tempkey记录整理成app的tempkey格式
     * @return void
     * @lastEditor csc 2022/4/11 16:32 V6.4
     * <AUTHOR> 2022/4/11 16:32 V6.4
     */
    public function formatPmAppTempKey()
    {
        $params = [
            "data" => "",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params['data'];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $nowTime = \util\time\setTimeZone(\util\computed\getNow(), $timeZone, 3);

        $formatData = [];
        if (!empty($data['row'])) {
            $formatData = $data['row'];
            $ids = implode(';', array_column($formatData, 'ID'));
            $resetParams = \util\model\saveParams();//保存
            \util\computed\setGAppData(["ID" => $ids]);
            $tempkeyInfos = $this->getInfoList();
            $resetParams();//重置
            foreach ($formatData as &$val) {
                $val['TmpKey'] = $val['Code'];
                $val['DateFlag'] = \util\computed\getDateScript($val["DateFlag"]);
                $val["Expired"] = 0;
                if ($val["SchedulerType"] == 0 or $val["SchedulerType"] == 3) {
                    if ($val["AccessTimes"] >= $val["AllowedTimes"]) {
                        $val["Expired"] = 1;
                    } else {
                        // 此时用户的时区,使用服务器时刻转换为用户时区的时间进行比较
                        if (strtotime($nowTime) > strtotime($val["EndTime"])) {
                            $val["Expired"] = 1;
                        }
                    }
                }
                $val["BeginTime"] = \util\time\setCustomizeFormat(strtotime($val["BeginTime"]), $customizeForm);
                $val["EndTime"] = \util\time\setCustomizeFormat(strtotime($val["EndTime"]), $customizeForm);

                // kangxiaolong 2021-03-25 修改 12小时制会被删除掉后面AM PM的bug
                $startTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$val["StartTime"]), $customizeForm));
                $endTimes = explode(" ", \util\time\setCustomizeFormat(strtotime("2000-01-01 ".$val["StopTime"]), $customizeForm));
                array_shift($startTimes);
                array_shift($endTimes);
                $val["StartTime"] = implode(" ", $startTimes);
                $val["StopTime"] = implode(" ", $endTimes);
                $tempkeyInfo = $tempkeyInfos[$val['ID']];
                $val['IsOld'] = $tempkeyInfo['IsOld'];
                $val['Doors'] = $tempkeyInfo['device'];
                if (!empty($val['IsOld'])) {
                    $val['EachAllowedTimes'] = $val['AllowedTimes'];
                }
                $val['UnitName'] = $tempkeyInfo['UnitName'];
                $val['RoomName'] = $tempkeyInfo['RoomName'];
                $val['RoomNumber'] = $tempkeyInfo['RoomNumber'];
                $val['QrCodeUrl'] = isset($val['QrCodeUrl']) ? $val['QrCodeUrl'] : '';
            }
            unset($val);
        }
        \util\computed\setGAppData(["data" => $formatData]);
    }
}
