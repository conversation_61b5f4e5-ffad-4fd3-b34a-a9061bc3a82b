<?php
/*
 * @Description: 模板
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;
include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

class CMessageTemplate {
	function add () {
        global $cMessage;
        $params = [
            "Name"=>"",
            "Title"=>"",
            "Message"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $name = $params["Name"];
        $title = $params["Title"];
        $message = $params["Message"];
        $userId = $params["userAliasId"];
        $count = $this->db->querySList("select count(*) as total from MessageTemplate where AccountID = :AccountID",[":AccountID"=>$userId])[0]["total"];
        if($count >= 6) $cMessage->echoErrorMsg(StateNotPermission);

        $this->db->insert2List("MessageTemplate",[":Name"=>$name,":Title"=>$title,":Message"=>$message,":AccountID"=>$userId,":CreateTime"=>\util\computed\getNow()]);
    }

    function delete () {
        global $cMessage;
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $count = $this->db->querySList("select count(*) as total from MessageTemplate where AccountID = :AccountID and ID = :ID",[":AccountID"=>$userId,":ID"=>$id])[0]["total"];
        if($count == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $this->db->delete2ListWID("MessageTemplate",$id);
    }

    function edit () {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Name"=>"",
            "Title"=>"",
            "Message"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $title = $params["Title"];
        $message = $params["Message"];
        $userId = $params["userAliasId"];
        $count = $this->db->querySList("select count(*) as total from MessageTemplate where AccountID = :AccountID and ID = :ID",[":AccountID"=>$userId,":ID"=>$id])[0]["total"];
        if($count == 0) $cMessage->echoErrorMsg(StateNotPermission);

        $this->db->update2ListWID("MessageTemplate",[":ID"=>$id,":Name"=>$name,":Title"=>$title,":Message"=>$message]);
    }

    function queryAll () {
        $params = [
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->queryAllList("MessageTemplate",["equation"=>[":AccountID"=>$userId]]);
        \util\computed\setGAppData(["data"=>$data]);
    }
}