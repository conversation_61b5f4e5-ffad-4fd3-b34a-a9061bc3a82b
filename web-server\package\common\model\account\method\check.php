<?php
/**
 * @description
 * <AUTHOR>
 * @date 2023-05-13 16:21:46
 * @version V6.6.0
 * @lastEditor kxl
 * @lastEditTime 2025-05-08 09:12:22
 * @lastVersion V6.7.0
 */

namespace package\common\model\account\method;

use package\common\model\account\config\Code;

trait Check
{
    /**
     * 检查小区是否dis在操作范围下
     */
    public function checkCommunityInDis()
    {
        $params = ['CommunityId', PROXY_ROLE['distributorId']];
        list($communityId, $disId) = $this->getParams($params);

        $this->log->debug("disId={disId};communityId={communityId}", ["disId" => $disId, "communityId" => $communityId]);

        $id = $this->db->querySList(
            "select C.ID from " . PROXY_TABLES['account'] . " A join " . PROXY_TABLES['account'] . " B on A.ManageGroup = B.ID 
            join " . PROXY_TABLES['account'] . " C on B.ParentID = C.ID where A.ID = :communityId and C.ID = :userId",
            [
                ":communityId" => $communityId,
                ":userId" => $disId
            ]
        );
        if (count($id) === 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
        }
    }

    /**
     * @name:检查小区、办公等是否ins在操作范围下
     */
    public function checkProjectInIns()
    {
        $params = ['ProjectId', PROXY_ROLE['installerId']];
        list($projectId, $installerId) = $this->getParams($params);

        $this->log->debug("installerId={installerId};communityId={communityId}", ["installerId" => $installerId, "communityId" => $projectId]);

        $tableAccount = PROXY_TABLES['account'];

        $res = !!($this->db->querySList(
            "select count(*) from $tableAccount where ID=:CommunityID and ManageGroup=:InstallerID",
            [":CommunityID" => $projectId, ":InstallerID" => $installerId]
        )[0]['count(*)']);
        if (!$res) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_INS]);
        }
    }

    /**
     * 检查用户名称是否合法（长度及是否为空）
     */
    public function userNameCheck()
    {
        $params = ["Name"];
        list($name) = $this->getParams($params);
        $this->log->debug('Name={Name}', ['Name' => $name]);
        if ($this->share->util->checkByteLength($name, 63) || $name === "" || $name === null) {
            $this->output->echoErrorMsg(STATE_NAME_LONG, ['externalErrorObj' => Code::EXT_STATE_USER_NAME_INVALID]);
        }
    }

    /*
     * 校验邮箱是否存在
     */
    public function userEmailCheck()
    {
        $params = ["Email", "ID", "isReturn"];
        list($email, $id, $isReturn) = $this->getParams($params);
        $isReturn = !!$isReturn;

        $result = true;
        $this->log->debug("Email={Email};ID={ID}", ["Email" => $email, "ID" => $id]);
        if ($email != null && $email != "") {
            $emails = $this->dao->personalAccountUserInfo->getDecolumnArrByDeColumn('Email', $email);
            if ($id) {
                $userInfoUUID = $this->callSelfFunc('getUserListByArray', [[['ID', $id]]])[0]['UserInfoUUID'];
                $searchArray = [['Email', $emails], ['UserInfoUUID', $userInfoUUID, '!=']];
                $data = $this->callSelfFunc('getUserListByArray', [$searchArray, 0]);
            } else {
                $searchArray = [['Email', $emails]];
                $data = $this->callSelfFunc('getUserListByArray', [$searchArray, 0]);
            }
            $this->log->debug("userEmailCheck count={count}", ["count" => count($data)]);
            if (count($data) > 0) {
                if ($isReturn) {
                    $result = false;
                } else {
                    $this->output->echoErrorMsg(STATE_EMAIL_EXITS_1010, ['externalErrorObj' => Code::EXT_STATE_USER_EMAIL_EXIST]);
                }
            }
        }

        return $result;
    }

    /*
     * 检验手机是否存在
     */
    public function userMobileCheck()
    {
        $params = ["MobileNumber", "ID"];
        list($mobile, $id) = $this->getParams($params);
        $this->log->debug("MobileNumber={MobileNumber};ID={ID}", ["MobileNumber" => $mobile, "ID" => $id]);
        if ($mobile !== null && $mobile !== "") {
            $mobiles = $this->dao->personalAccountUserInfo->getDecolumnArrByDeColumn('MobileNumber', $mobile);
            if ($id) {
                $userInfoUUID = $this->callSelfFunc('getUserListByArray', [[['ID', $id]]])[0]['UserInfoUUID'];
                $searchArray = [['MobileNumber', $mobiles], ['UserInfoUUID', $userInfoUUID, '!=']];
                $data = $this->callSelfFunc('getUserListByArray', [$searchArray, 0]);
            } else {
                $searchArray = [['MobileNumber', $mobiles]];
                $data = $this->callSelfFunc('getUserListByArray', [$searchArray, 0]);
            }
            $this->log->debug("userMobileCheck count={count}", ["count" => count($data)]);
            if (count($data) > 0) {
                $this->output->echoErrorMsg(STATE_MOBILE_EXITS, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
            }
        }
    }

    /*
     *@description 检查项目在不在dis下
     *<AUTHOR> 2022-06-08 10:28:25 V6.5
     *@lastEditor kxl 2022-06-08 10:28:25 V6.5
     *@param {*} ID
     *@param {*} PROXY_ROLE['distributorId']
     *@return
     */
    public function checkProjectInDis()
    {
        $params = ['ID', PROXY_ROLE['distributorId']];
        list($id, $disId) = $this->getParams($params);

        $total =  $this->db->querySList(
            'select count(*) from Account where ID=:ID and ParentID=:ParentID',
            [':ID' => $id, ':ParentID' => $disId]
        )[0]['count(*)'];

        if ('0' === $total) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
        }
    }

    /**
     * @description: 检查项目在不在dis下
     * @return void
     * @throws \Exception
     * @author: csc 2025/3/6 11:42 V7.1.0
     * @lastEditors: csc 2025/3/6 11:42 V7.1.0
     */
    public function checkProjectInDisByUUID()
    {
        $params = ['UUID', PROXY_ROLE['distributorId']];
        list($uuid, $disId) = $this->getParams($params);

        $total =  $this->db->querySList(
            'select count(*) from Account where UUID=:UUID and ParentID=:ParentID',
            [':UUID' => $uuid, ':ParentID' => $disId]
        )[0]['count(*)'];

        if ('0' === $total) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
        }
    }
    
    /*
     *@description 检查项目在不在子dis下
     *<AUTHOR> 2023-03-24 20:40:22 V6.5.4
     *@lastEditor cj 2023-03-24 20:40:22 V6.5.4
     *@param {*} ID
     *@param {*} PROXY_ROLE['distributorId']
     *@return
     */
    public function checkProjectInSubDis()
    {
        $params = ['ID', PROXY_ROLE['subDistributorUUID']];
        list($id, $disUUID) = $this->getParams($params);

        $insId = $this->dao->account->selectByID($id, 'ManageGroup')[0]['ManageGroup'];
        $insUUID = $this->dao->account->selectByID($insId, 'UUID')[0]['UUID'];
        $total = $this->dao->subDisMngList->selectByArray([['DistributorUUID', $disUUID], ['InstallerUUID', $insUUID]], 'count(*)')[0]['count(*)'];

        if ('0' === $total) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
        }
    }

    /**
     * @description: 检查项目在不在子dis下
     * @return void
     * @throws \Exception
     * @author: csc 2025/3/6 11:41 V7.1.0
     * @lastEditors: csc 2025/3/6 11:41 V7.1.0
     */
    public function checkProjectInSubDisByUUID()
    {
        $params = ['UUID', PROXY_ROLE['subDistributorUUID']];
        list($uuid, $disUUID) = $this->getParams($params);

        $insId = $this->dao->account->selectByUUID($uuid, 'ManageGroup')[0]['ManageGroup'];
        $insUUID = $this->dao->account->selectByID($insId, 'UUID')[0]['UUID'];
        $total = $this->dao->subDisMngList->selectByArray([['DistributorUUID', $disUUID], ['InstallerUUID', $insUUID]], 'count(*)')[0]['count(*)'];

        if ('0' === $total) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_DIS]);
        }
    }

    /*
     *@description 检查主从账户在不在dis下
     *<AUTHOR> 2022-06-08 10:23:00 V6.5
     *@lastEditor kxl 2022-06-08 10:23:00 V6.5
     *@param {*} ID
     *@param {*} PROXY_ROLE['projectId']
     *@return void
     */
    public function checkUserInDis()
    {
        $params = ['ID', PROXY_ROLE['distributorId'], PROXY_ROLE['subDistributorUUID']];
        list($id, $disId, $subDisUUID) = $this->getParams($params);

        $userData = $this->callSelfFunc('getUserInfo', [$id]);
        if (in_array(intval($userData['Role']), SUBROLE)) {
            $id = $userData['ParentID'];
            $userData = $this->callSelfFunc('getUserInfo', [$id]);
        }

        $projectId = $userData['ParentID'];

        if (empty($subDisUUID)) {
            $this->callSelfFunc('checkProjectInDis', [$projectId, $disId]);
        } else {
            $this->callSelfFunc('checkProjectInSubDis', [$projectId, $subDisUUID]);
        }
    }

    /*
     *@description 检测主账户或者从账户在不在项目下
     *<AUTHOR> 2022-06-08 10:11:52 V6.5
     *@lastEditor kxl 2022-06-08 10:11:52 V6.5
     *@param {*} ID
     *@param {*} PROXY_ROLE['projectId']
     *@return void
     */
    public function checkUserInProject()
    {
        $params = ['ID', PROXY_ROLE['projectId']];
        list($id, $projectId) = $this->getParams($params);
        $userData = $this->callSelfFunc('getUserInfo', [$id]);
        if (in_array(intval($userData['Role']), SUBROLE)) {
            $id = $userData['ParentID'];
        }

        $this->callSelfFunc('mainUserInPCMngCheck', [$id, $projectId]);
    }

    /*
     * 检测主账户在不在个人和项目管理员下
     */
    public function mainUserInPCMngCheck()
    {
        // V7.0 kxl 修改installerId从参数传入
        $params = ["ID", PROXY_ROLE['projectId']];
        list($id, $projectId) = $this->getParams($params);
        $this->log->debug("userId={userId};ID={ID}", ["userId" => $projectId, "ID" => $id]);
        $this->loadUtil("common");
        $search = [['Role', MAINROLE], ['ID', $id], ['ParentID', $projectId]];
        $count = $this->dao->personalAccount->selectByArray($search, 'count(*)')[0]['count(*)'];
        $this->log->debug("haveUser={count}", ["count" => $count]);
        if ($count == 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_USER_NOT_BELONG_PROJECT]);
        }
    }

    /*
     * 检测社区主账户在不在ins下
     */
    public function checkComMainUserInIns()
    {
        $params = ["UUID", PROXY_ROLE['installerId']];
        list($uuid, $insID) = $this->getParams($params);
        $this->loadUtil('manage');
        $communityList = $this->utils->manage->getAllProjectByProjId($insID, COMMUNITYGRADE);

        $this->loadUtil("common");
        $search = [['Role', COMENDMROLE], ['UUID', $uuid], ['ParentID', array_column($communityList, 'ID')]];
        $count = $this->dao->personalAccount->selectByArray($search, 'count(*)')[0]['count(*)'];
        $this->log->debug("haveUser={count}", ["count" => $count]);
        if ($count == 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
    }

    /*
     *@description 检测主账户Account在不在项目下
     *<AUTHOR> 2023-04-26 17:11:52 V6.6
     *@lastEditor cj 2023-04-26 17:11:52 V6.6
     *@param {*} Node 主账号Account
     *@param {*} PROXY_ROLE['projectId']
     *@return void
     */
    public function checkMainAccountInProject()
    {
        $params = ['Node', PROXY_ROLE['projectId']];
        list($account, $projectId) = $this->getParams($params);
        $this->log->debug("projectId={projectId};Account={Account}", ["projectId" => $projectId, "Account" => $account]);
        $search = [['Role', MAINROLE], ['Account', $account], ['ParentID', $projectId]];
        $count = $this->dao->personalAccount->selectByArray($search, 'count(*)')[0]['count(*)'];
        if ($count == 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_USER_NOT_BELONG_PROJECT]);
        }
    }


    /*
     * 主账号最大从账户个数验证
     */
    public function maxSubUserCheck()
    {
        $params = [PROXY_ROLE['mainUserId']];
        list($mainUserId) = $this->getParams($params);
        $search = [['Role', [PERENDSROLE, COMENDSROLE]], ['ParentID', $mainUserId]];
        $data = $this->dao->personalAccount->selectByArray($search, 'ID');
        $subUsers = count($data);
        $sysMaxSubCount = $this->dao->systemExtremum->getSystemConfig()['MaxApps'] - 1;
        $this->log->debug("mainUserId={mainUserId},subUser={subUser},sysMaxSubCount={sysMaxSubCount}", ["mainUserId"=>$mainUserId, "subUser" => $subUsers, "sysMaxSubCount" => $sysMaxSubCount]);
        // 系统最大值检测
        if ($subUsers >= $sysMaxSubCount) {
            $this->output->echoErrorMsg(STATE_FAMILY_MEMBER_CANNOT_CREATE, ['externalErrorObj' => Code::EXT_STATE_FAMILY_MEMBER_CANNOT_CREATE]);
        }
    }

    /*
     * 检测从账号在不在主账号下
     */
    public function subUserInMainCheck()
    {
        $params = ["ID", PROXY_ROLE['mainUserId']];
        list($ids, $userId) = $this->getParams($params);
        // 对多选删除的检测做了兼容
        $ids = explode(';', $ids);
        $this->log->debug("userId={userId};ID={ID}", ["userId" => $userId, "ID" => $ids]);
        foreach ($ids as $id) {
            $count = $this->dao->personalAccount->selectByArray([['Role', [PERENDSROLE, COMENDSROLE]], ['ParentID', $userId], ['ID', $id]], 'count(*)')[0]['count(*)'];
            $this->log->debug("mainUserId={mainUserId},haveUser={count}", ["mainUserId" => $userId,"count" => $count]);
            if ($count == 0) {
                $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUB_USER_IN_MAIN_CHECK]);
            }
        }
    }

    /**
     * @author: cj
     * @Description: 判断账号邮箱是否修改
     * @param {ID,email}
     * @return {setGAppBranch("changeEmail")}
     */
    public function checkEmailChange()
    {
        $params = [
            'ID',
            'Email'
        ];
        list($id, $email) = $this->getParams($params);
        $this->loadUtil("common");
        $oldEmail = $this->utils->common->getPersonalAccountInfoWithID($id, ['Email'])['Email'];
        $this->log->debug('oldEmail={oldEmail},newEmail={newEmail}', ["oldEmail"=>$oldEmail,"newEmail"=>$email]);
        if ($oldEmail != $email) {
            $this->share->util->setGAppBranch("changeEmail");
        }
    }

    /**
     * @Description 检测注销错误次数是否达到最大值
     * <AUTHOR> 11:29、V6.4
     * @param $id 当前登录帐号的id
     * @return void
     * @LastEditors csc、2022/3/10 11:29、V6.4
     */
    public function checkDelLimit()
    {
        if ($this->share->util->isSingleMainUser() or $this->share->util->isCommunityMainUser() or $this->share->util->isOfficeUser()) {
            $params = [PROXY_ROLE['mainUserId']];
        } else {
            $params = [PROXY_ROLE['subUserId']];
        }

        list($id) = $this->getParams($params);
        $this->loadUtil("common");
        $userInfo = $this->utils->common->getPersonalAccountInfoWithID($id, ['Account']);

        $res = $this->share->util->getLimitTimes($userInfo['Account'], 'deleteCode', 3);
        if (false !== $res) {
            $this->output->echoErrorMsg(STATE_DELCODE_OVER_LIMIT_TIMES, ['over_limit_times' => true, ['externalErrorObj' => Code::EXT_STATE_DELCODE_OVER_LIMIT_TIMES]]);
        }
    }

    /**
     * @Description: 检查注销用户验证码有效性
     * @Author: csc、2022/3/10 14:52、V6.4
     * @param $code string 验证码
     * @return void
     * @LastEditors: csc、2022/3/10 14:52、V6.4
     */
    public function checkDelCode()
    {
        if ($this->share->util->isSingleMainUser() or $this->share->util->isCommunityMainUser() or $this->share->util->isOfficeUser()) {
            $params = [PROXY_ROLE['mainUserId'], 'Code'];
        } else {
            $params = [PROXY_ROLE['subUserId'], 'Code'];
        }

        list($id, $code) = $this->getParams($params);
        $this->loadUtil("common");
        $userInfo = $this->utils->common->getPersonalAccountInfoWithID($id, ['Account']);

        $redis = $this->share->util->getRedis();
        $redis->select(REDISDB2CODE);
        $validCode = $redis->get($userInfo['Account']);

        if ($code !== $validCode) {
            //本次操作错误后如果达到最大值则报错
            $res = $this->share->util->recordLimitTimes($userInfo['Account'], 'deleteCode', 3, 24 * 3600);
            if (true === $res) {
                $this->output->echoErrorMsg(STATE_DELCODE_GET_LIMIT_TIMES, ['over_limit_times' => true, 'externalErrorObj' => Code::EXT_STATE_DELCODE_OVER_LIMIT_TIMES]);
            } else {
                $this->output->echoErrorMsg(STATE_DELCODE_ERROR, ['externalErrorObj' => Code::EXT_STATE_DELCODE_ERROR]);
            }
        } else {
            $redis->del($userInfo['Account']);
            $this->share->util->delLimitTimes($userInfo['Account'], 'deleteCode');
        }
    }

    /**
     * @description: 添加从账号数目检测
     * @author: cj 2022-03-15 15:16:12 v6.4
     * @LastEditors: Do not edit
     * @param {*}
     * @return {*}
     */
    public function checkSubNumber()
    {
        $params = ['subMemberNumber'];
        list($subMemberNumber) = $this->getParams($params);
        $sysMaxSubCount = $this->dao->systemExtremum->getSystemConfig()['MaxApps'] - 1;
        if ($subMemberNumber >= $sysMaxSubCount) {
            $this->output->echoErrorMsg(STATE_FAMILY_MEMBER_CANNOT_CREATE, ['externalErrorObj' => Code::EXT_STATE_FAMILY_MEMBER_CANNOT_CREATE]);
        }
    }

    /*
     *@description 检测PM和小区的管理关系
     *<AUTHOR> 2022-06-08 10:09:57 V6.4
     *@lastEditor kxl 2022-06-08 10:09:57 V6.4
     *@param {*} PROXY_ROLE['pmId']
     *@param {*} ProjectIds
     *@return
     */
    public function checkPmManageProjects()
    {
        $params = [PROXY_ROLE['pmId'], 'ProjectIds'];
        list($pmId, $projectIds) = $this->getParams($params);

        $this->log->debug(
            'params:pmId={pmId};projectIds={projectIds}',
            ['pmId' => $pmId, 'projectIds' => $projectIds]
        );

        if (count($projectIds) === 0) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_EMPTY]);
        }
        // 获取pm管理的全部小区
        $userInfoUUID = $this->callSelfFunc('getManagerInfo', [$pmId])['UserInfoUUID'];
        $pmUUIDs = array_column($this->dao->accountMap->selectByKey('UserInfoUUID', $userInfoUUID, 'AccountUUID'), 'AccountUUID');
        $pmIds = array_column($this->dao->account->selectByUUIDWArray($pmUUIDs, 'ID'), 'ID');
        $mngProjectIds = array_column($this->dao->propertyMngList->selectByKeyWArray('PropertyID', $pmIds, 'CommunityID'), 'CommunityID');

        if (!empty(array_diff($projectIds, $mngProjectIds))) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PROJECT_NOT_BELONG_TO_PM]);
        }
    }

    /**
     * @description:校验PM是否属于installer
     * @param: {string} _pmId PM的ID
     * @param: {string} _installerId ins的ID
     * @author: shoubin.chen 2023-10-17 11:51:10 v6.7
     * @lastEditor: shoubin.chen 2023-10-17 11:51:10 v6.7
     */
    public function checkPmManageInstaller()
    {
        $params = [PROXY_ROLE['pmId'], PROXY_ROLE_CHECK['installerId']];
        list($pmId, $insId) = $this->getParams($params);
        $account = $this->callSelfFunc('getManagerInfo', [$insId]);
        $pmList = $this->dao->account->selectByArray([['Grade', 31], ['ParentUUID', $account['UUID']]]);
        $pmIDList = array_column($pmList, 'ID');
        if (!in_array($pmId, $pmIDList)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PM_NOT_BELONG_TO_INS]);
        }
    }

    /**
     * @description:校验PM App是否属于某项目
     * @param: {string} ID PersonalAccount中PM app的ID
     * @param: {string} _projectId 项目ID
     * @author: shoubin.chen 2023-10-17 11:51:10 v6.7
     * @lastEditor: shoubin.chen 2023-10-17 11:51:10 v6.7
     */
    public function checkPmAppForProject()
    {
        $params = ['ID:uuid', PROXY_ROLE_CHECK['projectId']];
        list($id, $projectId) = $this->getParams($params);
        $this->loadUtil('account', true);
        $this->utils->_common->account->mainUserInPCMngCheck($id, $projectId);
    }

    /**
     * @description: 检测单住户和房间下存在从账号无法删除
     * @author: cj 2022/5/25 16:14 V6.4
     * @param ID 指主账号ID
     * @return {*}
     * @LastEditor: cj 2022/5/25 16:14 V6.4
     */
    public function checkHaveSubUserDelete()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        $search = [['Role', [PERENDSROLE, COMENDSROLE]], ['ParentID', $id]];
        $count = $this->dao->personalAccount->selectByArray($search, 'count(*)')[0]['count(*)'];
        if ($count > 0) {
            $this->output->echoErrorMsg(STATE_BIND_USER, ['externalErrorObj' => Code::EXT_STATE_BIND_USER]);
        }
    }

    /**
     * @description: 检测rtsp用户名规则
     * @param Name 用户名
     * @author: cj 2022/11/3 11:30 V6.5.2
     * @return {*}
     * @LastEditor: cj 2022/11/3 11:30 V6.5.2
     */
    public function checkRtspUserName()
    {
        $params = ['Name'];
        list($name) = $this->getParams($params);
        $name = trim($name, ' ');
        if ($name === '' || $name === null) {
            $this->output->echoErrorMsg(STATE_NAME_LONG, ['externalErrorObj' => Code::EXT_STATE_RTSP_USER_NAME_EMPTY]);
        }
        if (preg_match('/[@:\/]+/', $name)) {
            $this->output->echoErrorMsg(STATE_ACCOUNT_INCORRECT, ['externalErrorObj' => Code::EXT_STATE_RTSP_USER_NAME_INVALID]);
        }
    }

    /**
     * @description: 检测rtsp密码规则
     * @param PassWd 密码
     * @author: cj 2022/9/30 17:33 V6.5.2
     * @return {*}
     * @LastEditor: cj 2022/11/3 11:30 V6.5.2
     */
    public function checkComplexPwd()
    {
        $params = ['PassWd'];
        list($passwd) = $this->getParams($params);
        $passwd = trim($passwd, ' ');
        if ($passwd === '' || $passwd === null) {
            $this->output->echoErrorMsg(STATE_RTSP_PASSWORD_EMPTY, ['externalErrorObj' => Code::EXT_STATE_RTSP_PWD_EMPTY]);
        }
        if (preg_match('/[@:\/]+/', $passwd)) {
            $this->output->echoErrorMsg(STATE_RTSP_PASSWORD_INVALID, ['externalErrorObj' => Code::EXT_STATE_RTSP_PWD_EMPTY]);
        }
    }

    /**
     * @description:修改密码前检查密码正确
     * @author:lwj 2023-02-07 18:11:46 V6.6
     * @lastEditor:lwj 2023-02-07 18:11:46 V6.6
     * @param:{string} ID 用户ID
     * @param:{string} PassWd 密码
     */
    public function checkUserPwd()
    {
        $params = ['ID', 'PassWd'];
        list($userId, $passWd) = $this->getParams($params);
        $search = [['ID', $userId], ['Passwd', $this->share->util->getSaltPwd($passWd)]];
        $count = $this->callSelfFunc('getUserListByArray', [$search, 1])[0];
        if (empty($count)) {
            $this->output->echoErrorMsg(STATE_PASSWORD_INCORRECT, ['externalErrorObj' => Code::EXT_STATE_CHECK_PWD_INVALID]);
        }
    }

    /*
     *@description 检测link的手机号和验证码有效性
     *<AUTHOR> 2023-05-12 15:39:43 V6.6.0
     *@lastEditor kxl 2023-05-12 15:39:43 V6.6.0
     *@param {*} Account 用户账号
     *@param {*} MobileNumber
     *@param {*} Code
     *@return void
     */
    public function checkLinkCode()
    {
        $params = ['UserAccount', 'MobileNumber', 'Code'];
        list($userAccount, $mobileNumber, $code) = $this->getParams($params);

        $user = $this->callSelfFunc('getUserInfoByKey', ['MobileNumber', $mobileNumber]);
        //如果系统中不存在该手机号
        if (empty($user)) {
            //记录一次失败记录
            $this->share->util->recordLimitTimes($userAccount, 'linkAccount', 5, 300);
            $this->output->echoErrorMsg(STATE_NOT_MOBILE, [], [$mobileNumber]);
        }

        $verInfo = $this->dao->mobileCodeVerification->selectByArray([['MobileNumber', $mobileNumber], ['Code', $code], ['Type', 1]]);
        $now = $this->share->util->getNow();
        // 判断手机号对应的验证码是否正确,是否超过5分钟
        if (count($verInfo) === 0 || strtotime($verInfo[0]['CreateTime'] . ' + 5 minutes') < strtotime($now)) {
            //记录一次失败记录
            $this->share->util->recordLimitTimes($userAccount, 'linkAccount', 5, 300);
            $this->output->echoErrorMsg(STATE_CODE_INCORRECT);
        }

        return $user['UserInfoUUID'];
    }

    /*
     *@description 检测link时账号密码是否正确
     *<AUTHOR> 2023-05-16 11:40:55 V6.6.0
     *@lastEditor kxl 2023-05-16 11:40:55 V6.6.0
     *@param {*} UserAccount 用户账号
     *@param {*} Account
     *@param {*} Passwd
     *@return 
     */
    function checkLinkPasswd() {
        //接收帐号和密码参数
        $params = ['UserAccount', 'Account', 'Passwd'];
        list($userAccount, $account, $passwd) = $this->getParams($params);

        //如果帐号为空
        if (empty($account)) {
            //记录一次失败记录
            $this->share->util->recordLimitTimes($userAccount, 'linkAccount', 5, 300);
            $this->output->echoErrorMsg(STATE_INVAILD_ACCOUNT_PW);
        }

        //如果密码为空
        if (empty($passwd)) {
            //记录一次失败记录
            $this->share->util->recordLimitTimes($userAccount, 'linkAccount', 5, 300);
            $this->output->echoErrorMsg(STATE_INVAILD_ACCOUNT_PW);
        }

        $userInfo = $this->callSelfFunc('getUserListByArray', [[['MobileNumber', $account], ['Passwd', $this->share->util->getSaltPwd($passwd, true)]], 0]);
        $userInfo = empty($userInfo) ? $this->callSelfFunc('getUserListByArray', [[['Email', $account], ['Passwd', $this->share->util->getSaltPwd($passwd, true)]], 0]) : $userInfo;
        $userInfo = empty($userInfo) ? $this->callSelfFunc('getUserListByArray', [[['AppMainUserAccount', $account], ['Passwd', $this->share->util->getSaltPwd($passwd, true)]], 0]) : $userInfo;
        //判断帐号和密码是否存在对应的用户
        if (empty($userInfo) || $userInfo[0]['Role'] === strval(PMENDMROLE)) {
            $this->share->util->recordLimitTimes($userAccount, 'linkAccount', 5, 300);
            $this->output->echoErrorMsg(STATE_INVAILD_ACCOUNT_PW);
        }

        return $userInfo[0]['UserInfoUUID'];
    }



    /*
     *@description 检测绑定资格
     *<AUTHOR> 2023-05-13 16:21:50 V6.6.0
     *@lastEditor kxl 2023-05-13 16:21:50 V6.6.0
     *@param {*} UserInfoUUID 被绑定的账号的personalAccountUserInfo表的UUID
     *@param {*} SelfUUID 自己账号的PersonalAccountUserInfo表UUID
     *@return void
     */
    public function checkLinkStatus()
    {
        //接收参数
        $params = ['UserInfoUUID', 'SelfUUID','LinkMaxNum'];
        list($userInfoUUID, $selfUUID, $linkMaxNum) = $this->getParams($params);

        if ($userInfoUUID === $selfUUID) {
            $this->output->echoErrorMsg(STATE_PM_LINK_SELF_PM);
        }

        //查询当前帐号已绑定的站点信息
        $linkedSitesInfo = $this->dao->personalAccount->selectByKey('UserInfoUUID', $selfUUID);


        if (!empty($linkMaxNum)){
            if(count($linkedSitesInfo) >= $linkMaxNum) { // 新办公传入20上限
                //报错：You can only link up to x sites.
                $this->output->echoErrorMsg(STATE_LINK_MAX, [], [$linkMaxNum]);
            }
        }else if(count($linkedSitesInfo) >= MAX_LINK_NUM) { //如果当前帐号下的站点个数达到5个,5用常量MAX_LINK_NUM代替方便后续修改
            //报错：You can only link up to x sites.
            $this->output->echoErrorMsg(STATE_LINK_MAX, [], [MAX_LINK_NUM]);
        }

        $bindUserInfo = $this->callSelfFunc('getUserInfoByKey', ['UserInfoUUID', $userInfoUUID]);
        $bindRole = intval($bindUserInfo['Role']);

        //如果待添加的站点已经被其他帐号添加(已有多套房) 报错： You cannot link an account that already has multiple sites.
        if ($this->callSelfFunc('checkAppLinkStatus', [$bindUserInfo['UUID']])) {
            $this->output->echoErrorMsg(STATE_PM_LINK_MUL);
        }

        //如果绑定的房间带家居 报错：You cannont link an account with home automation sevice at the moment.
        $enableSmartHome = '0';
        if (in_array($bindRole, PERROLE)) {
            $enableSmartHome = $bindUserInfo['EnableSmartHome'];
            if ($bindRole === PERENDSROLE) {
                $enableSmartHome = $this->callSelfFunc('getUserInfo', [$bindUserInfo['ParentID']])['EnableSmartHome'];
            }
        } elseif (in_array($bindRole, COMROLE)) {
            $communityId = $bindUserInfo['ParentID'];
            if ($bindRole === COMENDSROLE) {
                $communityId = $this->callSelfFunc('getUserInfoByUUID', [$bindUserInfo['ParentUUID']])['ParentID'];
            }

            $switch = $this->callSelfFunc('getCommunityInfo', [$communityId])['Switch'];
            $enableSmartHome = strval($this->share->util->getSpecifyBitLE($switch, 5));
        }
        if ($enableSmartHome === '1') {
            $this->output->echoErrorMsg(STATE_LINK_SMARTHOME);
        }


        //如果待添加的站点是自己账号下任一站点的主从账号（包括自己帐号） 报错：You cannot link multiple accounts of the same family.
        $bindMainId = in_array($bindRole, SUBROLE) ? $bindUserInfo['ParentID'] : $bindUserInfo['ID'];
        foreach ($linkedSitesInfo as $value) {
            $mainId = in_array(intval($value['Role']), SUBROLE) ? $value['ParentID'] : $value['ID'];
            if ($bindMainId === $mainId) {
                $this->output->echoErrorMsg(STATE_LINK_SAME_HOME);
            }
        }

        if (($bindUserInfo['PhoneCode'] !== $linkedSitesInfo[0]['PhoneCode'])) {
            $this->output->echoErrorMsg(STATE_LINK_APP_DIFF_CODE);
        }
    }

    /**
     * @description: 根据pm app的uuid判断该pm是否已关联统一
     * @param {string} $uuid pm app的personalAccount的UUID
     * @return int
     * @author: csc 2023/5/12 10:00 V6.6
     * @lastEditors: csc 2023/5/12 10:00 V6.6
     */
    public function checkPmAppLinkStatus()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);
        $pmUUID = $this->dao->pmAccountMap->selectByKey('PersonalAccountUUID', $uuid, 'AccountUUID')[0]['AccountUUID'];
        $linkStatus = intval($this->callSelfFunc('getManagerListByArray', [[['UUID', $pmUUID]], [], 'AUF.IsLink'])[0]['IsLink']);
        return $linkStatus;
    }

    /**
     * @description: 判断PM WEB是否多套房用户（link了其他pm web）
     * @param {string} UUID Account表的UUID
     * @return int
     * @author: csc 2023/5/15 9:31 V6.6
     * @lastEditors: csc 2023/5/15 9:31 V6.6
     */
    public function checkPmWebLinkStatus()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);
        $userInfoUUID = $this->callSelfFunc('getManagerListByArray', [[['UUID' => $uuid]]])[0]['UserInfoUUID'];
        $count = $this->dao->accountMap->selectByKey('UserInfoUUID', $userInfoUUID, 'count(*)')[0]['count(*)'];
        if ($count > 1) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @description: 判断enduser app是否是多套房
     * @param {string} $uuid app的personalAccount的UUID
     * @return int
     * @author: csc 2023/5/12 10:00 V6.6
     * @lastEditors: csc 2023/5/12 10:00 V6.6
     */
    public function checkAppLinkStatus()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);
        $userInfo = $this->dao->personalAccount->selectByUUID($uuid)[0];

        // 空房间
        if ($userInfo['UserInfoUUID'] === '') {
            return 0;
        }
        $count = $this->dao->personalAccount->selectByKey('UserInfoUUID', $userInfo['UserInfoUUID'], 'count(*)')[0]['count(*)'];
        if ($count > 1) {
            return 1;
        } else {
            return 0;
        }
    }

    /*
     *@description 检测邮箱验证码有效性，时间只有5分钟
     *<AUTHOR> 2023-05-18 14:00:28 V6.6.0
     *@lastEditor kxl 2023-05-18 14:00:28 V6.6.0
     *@param {*} Email 邮箱
     *@param {*} Code 验证码
     *@param {*} Type
     *@return Boolean
     */
    public function checkEmailVaildCode()
    {
        $params = ['Email', 'Code', 'Type'];
        list($email, $code, $type) = $this->getParams($params);
        
        $now = $this->share->util->getNow();
        $info = $this->dao->emailCodeVerification->selectByArray([['Email', $email], ['Code', $code], ['Type', $type]]);
        if (count($info) === 0 || strtotime($info[0]['CreateTime'] . ' + 5 minutes') < strtotime($now)) {
            return false;
        }
        return true;
    }

    /*
     *@description 检测手机验证码有效性，时间只有5分钟
     *<AUTHOR> 2023-05-18 14:00:28 V6.6.0
     *@lastEditor kxl 2023-05-18 14:00:28 V6.6.0
     *@param {*} Email 邮箱
     *@param {*} Code 验证码
     *@param {*} Type
     *@return Boolean
     */
    public function checkMobileVaildCode()
    {
        $params = ['MobileNumber', 'Code', 'Type'];
        list($mobileNumber, $code, $type) = $this->getParams($params);
        
        $now = $this->share->util->getNow();
        $info = $this->dao->mobileCodeVerification->selectByArray([['MobileNumber', $mobileNumber], ['Code', $code], ['Type', $type]]);
        if (count($info) === 0 || strtotime($info[0]['CreateTime'] . ' + 5 minutes') < strtotime($now)) {
            return false;
        }
        return true;
    }

    /*
     *@description 检测临时token是否有效
     *<AUTHOR> 2023-05-18 14:14:47 V6.6.0
     *@lastEditor kxl 2023-05-18 14:14:47 V6.6.0
     *@param {*} WebToken
     *@param {*} AppToken
     *@param {*} Type 参考TmpToken表Type字段
     *@return string account
     */
    public function checkTmpToken()
    {
        $params = ['WebToken', 'AppToken', 'Type'];
        list($webToken, $appToken, $type) = $this->getParams($params);

        $condition = [['Type', $type]];
        if (!empty($webToken)) {
            $condition[] = ['WebToken', $webToken];
        }

        if (!empty($appToken)) {
            $condition[] = ['AppToken', $appToken];
        }

        $info = $this->dao->tmpToken->selectByArray($condition);
        $now = $this->share->util->getNow();
        if (count($info) === 0 || strtotime($now) > strtotime($info[0]['EndTime'])) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj'=>Code::TMP_TOKEN_INVAILD]);
        }

        return $info[0]['Account'];
    }

    /**
     * @description: 只有社区从账号才需要判断，pm设置从账号个数  超过限制的不允许登陆（0-超出限制 1-正常）
     * @param {string} MngID
     * @param {array} UserData 从账号数据
     * @return int
     * @author: csc 2023/8/15 15:26 V6.7.0
     * @lastEditors: csc 2023/8/15 15:26 V6.7.0
     */
    public function checkFamilyMemberControl()
    {
        $params = ['MngID:id', 'UserData:is-array'];
        list($mngID, $userData) = $this->getParams($params);

        $checkExpire = $this->dao->communityInfo->selectByAccountID($mngID, 'FeatureExpireTime < now() as expire, ISNULL(FeatureExpireTime) as fnull');
        if ($checkExpire[0]['expire'] == 1 or $checkExpire[0]['fnull'] == 1) {
            $featureExpire = true;
        } else {
            $featureExpire = false;
        }

        $checkSlave = 1;
        $result = $this->dao->aPPSpecial->selectByKey('Account', $userData['Account'], 'Node, ID');
        if (empty($result)) {
            return $checkSlave;
        }

        $node = $result[0]['Node'];
        $slaveID = $result[0]['ID'];
        $nodeCount = $this->dao->aPPSpecial->selectByKey('Node', $node, 'count(*)')[0]['count(*)'];
        $resultCnf = $this->dao->personalAccountCnf->getAllowCreateSlaveCnt($node);
        if (empty($resultCnf)) {
            return $checkSlave;
        }

        $allowCnt = $resultCnf[0]['AllowCreateSlaveCnt'];
        if ($allowCnt >= $nodeCount) {
            return $checkSlave;
        }

        $this->loadUtil('featurePlan', true);
        $checkPlanStatus = $this->utils->_common->featurePlan->checkCommunityFeaturePlan($mngID, FEATURE_ITEM_FAMILY_CONTROL);
        if ($featureExpire or !$checkPlanStatus) {
            return $checkSlave;
        }

        $lessCount = $this->dao->aPPSpecial->selectByArray([['Node', $node], ['ID', $slaveID, '<']], 'count(1)')[0]['count(1)'];
        if ($lessCount >= $allowCnt) {
            $checkSlave = 0;
        }
        return $checkSlave;
    }

    /**
     * @description:校验dis是否存在
     * @param: {type}
     * @author: shoubin.chen 2024-03-07 15:50:24 v6.8.0
     * @lastEditor: shoubin.chen 2024-03-07 15:50:24 v6.8.0
     */
    public function checkDisExist()
    {
        $params = [PROXY_ROLE_CHECK['distributorId']];
        list($disID) = $this->getParams($params);
        $dis = $this->dao->account->selectByArray([['ID', $disID], ['Grade', AREAGRADE]])[0];
        if ($dis === null) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_DIS_NOT_EXIST]);
        }
        return $dis;
    }

    /**
     * @description: 校验发送邮件,5分钟内只能重置一次
     * @param {*}
     * @author: shoubin.chen  2024-04-26 16:43:17 v6.7.3
     * @lastEditor: shoubin.chen  2024-04-26 16:43:17 v6.7.3
     */
    public function checkSendEmailInLimitTime()
    {
        $params = ['EmailOrId', 'IsJumpError'];
        list($emailOrId, $isJumpError) = $this->getParams($params);
        $sendEmail = "sendEmail";
        $ip = $this->share->util->getIp();
        $user = $ip . "_" . $emailOrId;
        $resEmail = $this->share->util->getLimitTimes($user, $sendEmail, 1);

        if (false !== $resEmail) {
            if ($isJumpError) {
                return true;
            } else {
                $this->output->echoErrorMsg(STATE_LIMIT_IP2);
            }

        }

        $this->share->util->recordLimitTimes($user, $sendEmail, 1, 300);
    }

    /**
     * @description:校验验证码，5分钟内只能重试5次
     * @param:
     * @return:
     * @author: shoubin.chen 2025/06/06 18:30:18 V7.2.0
     * @lastEditor: shoubin.chen 2025/06/06 18:30:18 V7.2.0
     */
    public function checkPhoneInLimitTime()
    {
        $params = ['Phone', 'LimitNum', 'Type'];
        list($phone, $limitNum, $type) = $this->getParams($params);
        $validType = [
            PHONE_CHECK_CODE,// 验证码
        ];
        if (!in_array($type, $validType)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }

        $ip = $this->share->util->getIp();
        $user = $ip . "_" . $phone;
        $resTime = $this->share->util->getLimitTimes($user, $type, $limitNum);

        if (false !== $resTime) {
            $this->output->echoErrorMsg(STATE_LIMIT_IP2);
        }

        $this->share->util->recordLimitTimes($user, $type, $limitNum, 300);
    }

    /**
     * @description:校验终端用户账号密码是否正确
     * @author:kzr 2025-04-28 11:55:16 V7.1.3
     * @lastEditor:kzr 2025-04-28 11:55:16 V7.1.3
     * @param:
     * @return mixed
     */
    public function checkoutEndUserPwd()
    {
        $params = ['MobileNumber'];
        list($mobilenumber) = $this->getParams($params);

        //校验account是否被限制登录
        $ip = $this->share->util->getIp();
        $limit = $this->share->util->getLimitIp($mobilenumber, $ip);
        if (!empty($limit) && false !== $ip) {
            $this->output->echoErrorMsg(STATE_LIMIT_IP2, ['externalErrorObj' => Code::EXT_STATE_LIMIT_IP, 'time'=>$limit]);
        }

        $this->loadUtil('account', true);
        $data = $this->dao->personalAccountUserInfo->checkEndUserLogin($mobilenumber);

        $this->log->debug("data={data}",["data" => $data]);

        if (!empty($data)) {
            return $data[0];
        }

        if (false === $ip) {
            $this->output->echoErrorMsg(STATE_ACCOUNT_INCORRECT, ['externalErrorObj' => Code::EXT_STATE_LOGIN_LIMIT_NUMBER, 'number' => -1]);
        } else {
            //记录登录失败次数
            list($times, $number) = $this->share->util->recordAccountIp($mobilenumber, $ip);
            if ($times === false) {
                $num = $number >= 3 ? (5 - $number) : -1;
                $this->output->echoErrorMsg(STATE_ACCOUNT_INCORRECT, ['externalErrorObj' => Code::EXT_STATE_LOGIN_LIMIT_NUMBER, 'number' => $num]);
            }

            $this->output->echoErrorMsg(STATE_ACCOUNT_INCORRECT_2, ['externalErrorObj' => Code::EXT_STATE_LOGIN_LIMIT_TIMES, 'time' => $times]);
        }

    }
}
