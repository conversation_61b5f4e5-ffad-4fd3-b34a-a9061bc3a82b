<?php
/*
 * @Description:
 * @version: V7.0
 * @Author: cj
 * @Date: 2021-12-15 14:28:09
 * @LastEditors: cj
 * @LastEditTime: 2021-12-24 18:15:43
 */
namespace package\common\model\projectData;

class Main extends \framework\BasicModel
{
    public $allowedDao = ['account', 'personalAccount', 'rBACDataGroup', 'accountDataGroup', 'rBACRoleMap', 'rBACRole', 'communityInfo', 'officeInfo','officeAdmin', 'localAKManage',
        'devicesDoorList', 'subDisMngList'];
    use src\AddProjectItem;
    use src\QueryProjectList;
    use src\QueryImportDetails;
}
