<?php

namespace model\order;

trait query
{
    public function getOrderList()
    {
        $params = [
            "userId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "payType" => "",
            "PayType" => "",
            "Type" => "",
            "Status" => "",
            "Key" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "Installer" => "",
            "Community" => "",
            // 社区办公内部订单不传，单主户和获取所有订单需要传，为社区类型时IsBatch必须是1
            "ProjectType" => "",
            // V6.4是否是获取所有同类型订单
            "IsBatch" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $myID = $params["userId"];
        $payType = $params["payType"] !== null ? $params["payType"] : null;
        $type = $params["Type"];
        $status = $params["Status"];
        $key = $params["Key"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $installer = $params["Installer"] ?: 'all';
        $community = $params["Community"] ?: 'all';
        $projectType = $params["ProjectType"] ?: 'all';
        $isBatch = $params['IsBatch'] ? 1 : 0;
        
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        if ($myID && $isBatch !== 1) {
            $where = "where AccountID = :AccountID and IsDelete = 0 and OrderNumber like :Key and PayerType = :PayerType ";
            $bindArray = [":AccountID" => $myID, ":PayerType" => $payType, ":Key" => "%$key%"];
        } else {
            $where = "where IsDelete = 0 and OrderNumber like :Key and PayerType = :PayerType ";
            $bindArray = [":PayerType" => $payType, ":Key" => "%$key%"];
        }


        if ($payType === null) {
            $where = "where (OrderNumber like :Key or Payer like :Key)";
            $bindArray = [":Key" => "%$key%"];

            $payType = $params["PayType"];
            if ($payType != "all") {
                $where .= " and PayerType = :PayerType";
                $bindArray[":PayerType"] = $payType;
            }
        }

        if ($beginTime) {
            $beginTime = \util\time\setTimeZone($beginTime, $timeZone, "", "-");
            $where .= " and CreateTime >= :BeginTime";
            $bindArray[":BeginTime"] = $beginTime;
        }

        if ($endTime) {
            $endTime = \util\time\setTimeZone($endTime, $timeZone, "", "-");
            $where .= " and CreateTime <= :EndTime";
            $bindArray[":EndTime"] = $endTime;
        }

        if ($type != "all" && $type != null) {
            // 获取所有feature类型订单
            if ($type == "5") {
                $where .= " and (Type = 5 or Type = 6 or Type = 7)";
            } elseif ($type == "2") {
                // V6.5.2 修复检索问题2指续费类型(包括4单住户落地)
                $where .= " and Type IN(2,4,8)";
            } else {
                $where .= " and Type = :Type";
                $bindArray[":Type"] = $type;
            }
        }

        if ($status != "all" && $status != null) {
            $where .= " and Status = :Status";
            $bindArray[":Status"] = $status;
        }

        if ($installer != "all" && $installer != null || $isBatch === 1) {
            if ($isBatch === 1) {
                $installer = $this->db->querySList(
                    'select ManageGroup from Account where ID=:ID',
                    [':ID' => $myID]
                )[0]['ManageGroup'];
            }

            if ($community == "all") {
                $manages = $this->db->querySList(
                    "select ID from Account where ManageGroup = :ManageGroup",
                    [":ManageGroup" => $installer]
                );
                $manageId = [];
                foreach ($manages as $val) {
                    array_push($manageId, $val["ID"]);
                }

                if (count($manages) !== 0) {
                    $manageId = implode(",", $manageId);
                    $where .= " and InstallID in ($manageId)";
                } else {
                    $where .= " and 1=0";
                }
            } else {
                // V6.5.0 兼容批量支付小区检索包括ins,dis和super
                if ($isBatch === 1 || $payType === 3 || $params["payType"] === null) {
                    $projectUUID = $this->db->querySList(
                        "select UUID from Account where ID = :ID",
                        [":ID" => $community]
                    )[0]['UUID'];
                    $orderIds = $this->db->querySList(
                        "select OrderID from OrderEndUserList where ProjectUUID = :ProjectUUID",
                        [":ProjectUUID" => $projectUUID]
                    );
                    if (count($orderIds) > 0) {
                        $orderIds = implode(",", array_unique(array_column($orderIds, 'OrderID')));
                        $where .= " and (InstallID = :InstallID or ID in ($orderIds))";
                    } else {
                        $where .= " and InstallID = :InstallID";
                    }
                } else {
                    // 单小区支付,InstallID为小区ID
                    $where .= " and InstallID = :InstallID";
                }
                $bindArray[":InstallID"] = $community;
            }
        }

        // 仅能在installer时候传，6.4 payId=insID，InstallerID=insID可能是单住户支付也可能是多社区支付
        if ($projectType === 'single') {
            $where .= " and InstallID = :InstallID and IsBatch = 0";
            $bindArray[":InstallID"] = $myID;
        } elseif ($projectType === 'multiple' && $isBatch === 1) {
            // 搜索条件：insId=小区ID payId=小区 or insId
            $manages = $this->db->querySList('select A.ID from Account A join Account B 
            on A.ManageGroup=B.ManageGroup where B.ID=:ID and A.Grade = 21', [':ID' => $myID]);
            $manageIds = array_column($manages, 'ID');
            // 6.5.0 修复批量不展示单住户订单列表, 单住户的订单为非批量且InstallID为InsID
            if (count($manageIds) !== 0) {
                $manageId = implode(",", $manageIds);
                $where .= " and AccountID in ($manageId,$installer) and (ID not in (select ID from OrderList where IsBatch = 0 and InstallID = $installer))";
            } else {
                $where .= " and AccountID in ($installer) and (ID not in (select ID from OrderList where IsBatch = 0 and InstallID = $installer))";
            }
        } elseif ($projectType === 'office') {
            $manages = $this->db->querySList('select A.ID from Account A join Account B 
            on A.ManageGroup=B.ManageGroup where B.ID=:ID and A.Grade = 23', [':ID' => $myID]);
            $manageIds = array_column($manages, 'ID');
            if (count($manageIds) !== 0) {
                $manageId = implode(",", $manageIds);
                $where .= " and AccountID in ($manageId)";
            } else {
                $where .= " and 1=0";
            }
        }

        $statusArray = [
            MSGTEXT["processing"],
            MSGTEXT["successed"],
            MSGTEXT["failed"],
            MSGTEXT["timeOut"],
            MSGTEXT["abnormal"],
            MSGTEXT["cancel"],
            MSGTEXT["systemProcessing"]
        ];
        $typesArray = [
            "",
            MSGTEXT["activation"],
            MSGTEXT["subscription"],
            MSGTEXT["additionalApp"],
            MSGTEXT["subscription"],
            MSGTEXT["featureActivationFee"],
            MSGTEXT["featureMonthlyFee"],
            MSGTEXT["featurePriceDifferences"],
            MSGTEXT["subscription"]
        ];
        $payerArray = [MSGTEXT["family"], MSGTEXT["community"]];

        $total = $this->db->querySList("select count(*) from OrderList $where", $bindArray)[0]["count(*)"];
        $orderDatas = $this->db->querySList(
            "select * from OrderList $where order by ID desc limit $offset,$rows",
            $bindArray
        );
        foreach ($orderDatas as &$value) {
            $endData = $this->db->querySList(
                "select ID,ProjectUUID from OrderEndUserList where OrderID = :OrderID",
                [":OrderID" => $value["ID"]]
            );
            $projectUUID = array_unique(array_column($endData, 'ProjectUUID'));
            $value['Projects'] = '';
            $uuidWhere = [];
            $uuidBindArray = [];
            $count = count($projectUUID);
            if ($count > 0) {
                for ($i =0; $i < $count; $i++) {
                    array_push($uuidWhere, ":UUID$i");
                    $uuidBindArray[":UUID$i"] = $projectUUID[$i];
                }
                $value['Projects'] = implode(',', array_column($this->db->querySList('select Location from Account where UUID in ('.implode(',', $uuidWhere).')', $uuidBindArray), 'Location'));
            }

            $value["AptNumber"] = count($endData);
            $pcMngData = $this->db->querySList(
                'select Account,Grade,ManageGroup,Location from Account where ID = :ID',
                [":ID" => $value['InstallID']]
            );
            if (count($pcMngData) == 0) {
                $value["Installer"] = "";
                $value["Community"] = "";
            } else {
                $pcMngData = $pcMngData[0];
                if ($pcMngData["Grade"] == PERSONGRADE) {
                    $value["Installer"] = $pcMngData["Account"];
                    $value["Community"] = "";
                } else {
                    $value["Community"] = $pcMngData["Location"];
                    $value["Installer"] = $this->db->querySList(
                        'select Account from Account where ID = :ID',
                        [":ID" => $pcMngData["ManageGroup"]]
                    )[0]["Account"];
                }
            }

            if ($value['PayerType'] == 2) {
                $value['Payer'] = $this->db->querySList('select A.Account from Account A
                join Account B on A.ID=B.ManageGroup where B.ID=:ID', [':ID' => $value['AccountID']])[0]['Account'];
            }

            $value['Projects'] = $value['Projects'] === '' ? $value['Community'] : $value['Projects'];

            $value["TotalPriceNum"] = $this->outputComputedCount(
                $this->computedDiscount($value["FinalPrice"], $value["Discount"])
            );
            $value["TotalPrice"] = "$" . $value["TotalPriceNum"];
            $value["StatusEnum"] = $value["Status"];
            $value["Status"] = $statusArray[$value["Status"]];
            $value["Type"] = $value["Type"] == 4 ? 2 : $value["Type"];
            $value["TypeEnum"] = $value["Type"];
            $value["Type"] = $typesArray[$value["Type"]];
            $value["PayerTypeName"] = $payerArray[$value["PayerType"]];
            $value["bmurl"] = BMAPYURL . "?order=" . $value["BmOrderNumber"] . "&token=" . $value["WebHookToken"] . "&code=" . $value["PayCode"];
        }
        $orderDatas = \util\time\setQueryTimeZone($orderDatas, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data" => ["row" => $orderDatas, "detail" => $orderDatas, "total" => $total]]);
    }

    public function checkOrderExit()
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "userId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userId"];
        // $orderData = $this->db->queryAllList("OrderList", ["equation" => [":ID" => $id, ":AccountID" => $userId]]);
        // V6.4修改多小区支付时，AccountID为insId，insId的任意角色可以查看所有ins下的订单
        // TODO 这里忽略了终端用户，重启终端用户付费时需要修改
        $userData = $this->db->querySList('select Grade, ManageGroup from Account where ID=:ID', [':ID' => $userId])[0];
        $role = $userData['Grade'];
        if ($role == PERSONGRADE || $role == COMMUNITYGRADE || $role == OFFICEGRADE) {
            $installerIds = $this->db->querySList(
                'select ID from Account where ManageGroup=:ManageGroup',
                [':ManageGroup' => $userData['ManageGroup']]
            );
            $installerIds = array_column($installerIds, 'ID');
            $installerIds = implode(',', $installerIds);
            $orderCount = $this->db->querySList(
                "select count(*) as total from OrderList where ID = :ID and AccountID in ($installerIds)",
                [':ID' => $id]
            )[0]['total'];
        } else {
            $orderCount = $this->db->querySList(
                'select count(*) as total from OrderList where ID=:ID and AccountID=:AccountID',
                [
                    ':ID' => $id,
                    ':AccountID' => $userId
                ]
            )[0]['total'];
        }

        if ($orderCount == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }

    public function getOrderInfo()
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $orderData = $this->db->queryAllList("OrderList", ["equation" => [":ID" => $id]]);
        $orderData = $orderData[0];
        $type = $orderData["Type"];
        $statusArray = [
            MSGTEXT["processing"],
            MSGTEXT["successed"],
            MSGTEXT["failed"],
            MSGTEXT["timeOut"],
            MSGTEXT["abnormal"],
            MSGTEXT["cancel"],
            MSGTEXT["systemProcessing"]
        ];
        $typesArray = [
            "",
            MSGTEXT["activation"],
            MSGTEXT["subscription"],
            MSGTEXT["additionalApp"],
            MSGTEXT["subscription"],
            MSGTEXT["featureActivationFee"],
            MSGTEXT["featureMonthlyFee"],
            MSGTEXT["featurePriceDifferences"],
            MSGTEXT["subscription"]
        ];
        $orderData["OriginalPrice"] = $this->outputComputedCount($orderData["TotalPrice"]);
        $orderData["TotalPrice"] = $this->outputComputedCount(
            $this->computedDiscount($orderData["FinalPrice"], $orderData["Discount"])
        );
        $orderData["CouponCount"] = $this->outputComputedCount($orderData["CouponCount"]);
        $orderData["BeforeOncePrice"] = $this->outputComputedCount($orderData["BeforeOncePrice"]);
        $orderData["StatusEnum"] = $orderData["Status"];
        $orderData["Status"] = $statusArray[$orderData["Status"]];
        $orderData["Type"] = $typesArray[$orderData["Type"]];
        $orderData["TypeEnum"] = $type;
        $orderData["ProjectType"] = $this->db->queryAllList(
            "Account",
            ["equation" => [":ID" => $orderData["InstallID"]]]
        )[0]['Grade'];

        if ($orderData['PayerType'] == 2) {
            $orderData['Payer'] = $this->db->querySList('select A.Account from Account A
            join Account B on A.ID=B.ManageGroup where B.ID=:ID', [':ID' => $orderData['AccountID']])[0]['Account'];
        }

        $orderData = \util\time\setQueryTimeZone([$orderData], $timeZone, $customizeForm)[0];
        if ($type == 1 || $type == 3 || $type == 4) {
            $orderData["Childern"] = $this->db->queryAllList(
                "OrderEndUserList",
                ["equation" => [":OrderID" => $orderData["ID"]]]
            );
            foreach ($orderData["Childern"] as &$val) {
                $val["Price"] = $val["Amount"] = $this->outputComputedCount($val["Amount"]);
            }
        } elseif ($type == 2) {
            $orderData["Childern"] = $this->db->queryAllList(
                "OrderEndUserList",
                [
                    "equation" => [
                        ":OrderID" => $orderData["ID"],
                        ":Type" => 2
                    ]
                ]
            );
            foreach ($orderData["Childern"] as &$val) {
                $outApps = $this->db->queryAllList(
                    "OrderEndUserList",
                    [
                        "equation" => [
                            ":OrderID" => $orderData["ID"],
                            ":Type" => 4,
                            ":ParentID" => $val["AppID"]
                        ]
                    ]
                );
                $val["OutApps"] = count($outApps);
                $val["OutAppsAmount"] = count($outApps) === 0 ? 0 : $this->outputComputedCount(
                    $outApps[0]["Amount"] * count($outApps)
                );
                $val["Price"] = $this->outputComputedCount(
                    ((count($outApps) === 0 ? 0 : $outApps[0]["Amount"] * count(
                        $outApps
                    )) + $val["Amount"]) * $orderData["Months"]
                );
                $val["Amount"] = $this->outputComputedCount($val["Amount"]);
            }
        } elseif ($type == 5 || $type == 6 || $type == 7) {
            $orderData["Childern"] = [];
        } elseif ($type == 8) {
            $orderData["Childern"] = $this->db->queryAllList(
                "OrderEndUserList",
                [
                    "equation" => [
                        ":OrderID" => $orderData["ID"]
                    ]
                ]
            );
            foreach ($orderData["Childern"] as &$val) {
                $val["Price"] = $val["Amount"] = $this->outputComputedCount($val["Amount"]);
                $val['MonthlyFee'] = json_decode($val['ChargeData'], true)['MonthlyFee'];
            }
        }

        \util\computed\setGAppData(["data" => $orderData]);
    }

    public function getReceipt()
    {
        $params = [
            "userId" => "",
            "user" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "OrderID" => "",
            "Payer" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userId"];
        $user = $params["user"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $id = $params["OrderID"];
        $userData = $this->db->querySList(
            'select Grade,ManageGroup from Account where ID = :ID',
            [":ID" => $userId]
        )[0];

        $grade = $userData['Grade'];
        if ($grade == SUPERGRADE) {
            $orderData = $this->db->queryAllList("OrderList", ["equation" => [":ID" => $id]]);
            $user = $orderData[0]['Payer'];
            if ($orderData[0]['PayerType'] == 2) {
                $user = $this->db->querySList(
                    'select A.Account from Account A
                join Account B on A.ID=B.ManageGroup where B.ID=:ID',
                    [':ID' => $orderData[0]['AccountID']]
                )[0]['Account'];
            }
        } elseif (in_array($grade, [COMMUNITYGRADE, PERSONGRADE, OFFICEGRADE])) {
            // ins层级因为多账号的原因，所以同个账号组发票都能下载
            $insId = $userData['ManageGroup'];
            $projects = $this->db->querySList(
                'select ID from Account where ManageGroup=:ManageGroup',
                [':ManageGroup' => $insId]
            );
            $projectIds = array_column($projects, 'ID');
            $orderData = $this->db->queryAllList("OrderList", ["equation" => [":ID" => $id]]);
            if (!in_array($orderData[0]['AccountID'], $projectIds)) {
                $orderData = [];
            }
        } else {
            $orderData = $this->db->queryAllList(
                "OrderList",
                ["equation" => [":ID" => $id, ":AccountID" => $userId]]
            );
        }
        $orderData = \util\time\setQueryTimeZone($orderData, $timeZone, $customizeForm)[0];
        $data = [];
        $data["Price"] = $this->outputComputedCount(
            $this->computedDiscount($orderData["FinalPrice"], $orderData["Discount"])
        );
        $data["Time"] = $orderData["CreateTime"];
        $data["OrderNumber"] = $orderData["OrderNumber"];
        \util\computed\setGAppData(["data" => $data, "user" => $user]);
    }

    public function getReceiptPdf()
    {
        $country = file_get_contents("country.json");
        $country = json_decode($country, true);
        $country = $country["Location"]["CountryRegion"];
        $params = ["data" => ""];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $orderNumber = $data["OrderNumber"];
        $time = $data["Time"];
        $buyerInfo = $data["BuyerInfo"];
        $billingTitle = $data["BillingTitle"];
        $attn = $data["Contactor"];
        $addr = $data["Street"] . " " . $data["Postcode"] . " " . $data["City"];
        $tel = $data["TelePhone"];
        $fax = $data["Fax"];
        $price = $data["Price"];
        $countryCode = $data["Country"];
        foreach ($country as $value) {
            if ($value["Code"] == $countryCode) {
                $countryCode = $value["Name"];
            }
        }
        require_once __DIR__ . "/../../plugin/tcpdf_min/tcpdf.php";
        $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $html = <<<EOD
            <div style="text-align:center;font-size:14px;line-height:18px;margin:0;padding:0;line-height:18px;">
                <b>
                SMART-PLUS PTE. LTD.
                </b>
            </div>
            <div style="text-align:center;font-size:10px;;margin:0;padding:0;line-height:18px;">6 RAFFLES QUAY #14-06, Singapore(048580)</div>
            <div style="text-align:center;font-size:10px;">Tel: +(65) 6717 0088 &nbsp;&nbsp;&nbsp;&nbsp; VAT No.: 202116626G &nbsp;&nbsp;&nbsp;&nbsp; Fax: +(65) 6717 0088</div>
            <div style="text-align:center;font-size:14px;line-height:18px;">
                <b>
                    INVOICE
                </b>
            </div>
            <div style="border-top: 1px solid #000;"></div>

            <table style="font-size:10px;">
                <tr style="line-height:20px;">
                    <td>INVOICE NO.:</td>
                    <td>$orderNumber</td>
                    <td>DATE:</td>
                    <td>$time</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>BUYER INFORMATION:</td>
                    <td colspan="3">$buyerInfo</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>COMPANY NAME:</td>
                    <td>$billingTitle</td>
                    <td>COUNTRY:</td>
                    <td>$countryCode</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ATTN:</td>
                    <td colspan="3">$attn</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>ADD:</td>
                    <td colspan="3">$addr</td>
                </tr>
                <tr style="line-height:20px;">
                    <td>TEL:</td>
                    <td>$tel</td>
                    <td>FAX:</td>
                    <td>$fax</td>
                </tr>
            </table>
            <br><br>
            <table style="font-size:10px;text-align:center;" border="1" cellspacing="0" cellpadding="0">
                <tr style="line-height:25px;">
                    <td><b>COMMODIFY DESCRIPTION</b></td>
                    <td><b>AMOUNT</b></td>
                </tr>
                <tr>
                    <td>Akuvox Cloud Service Ordered</td>
                    <td>US$$price</td>
                </tr>
                <tr>
                    <td style="text-align:right;">TOTAL</td>
                    <td>US$$price</td>
                </tr>
            </table>
EOD;
        $pdf->SetPrintHeader(false);
        $pdf->SetFont('arialuni', '', 14, true);
        $pdf->AddPage();
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->setHtmlVSpace(
            [
                "div" => [
                    ["h" => 1],
                    ["h" => 1],
                    ["h" => 1],
                    ["h" => 1],
                    ["h" => 1],
                    ["h" => 1]
                ]
            ]
        );
        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->lastPage();
        $pdf->Output("$orderNumber.pdf", "D");
    }

    public function export()
    {
        global $cMessage;
        include_once __DIR__ . "/../../database/redis.php";
        $params = [
            "BeginTime" => "",
            "EndTime" => "",
            "Status" => "",
            "ManageID" => "",
            "InstallID" => "",
            "Type" => "",
            "token" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $begin = $params["BeginTime"];
        $end = $params["EndTime"];
        $status = $params["Status"];
        $manage = $params["ManageID"];
        $install = $params["InstallID"];
        $type = $params["Type"];
        $token = $params["token"];

        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        $account = $redis->get($token);
        $data = $this->db->querySList(
            'select Grade,TimeZone,CustomizeForm from Account where Account = :Account',
            [":Account" => $account]
        )[0];
        $grade = $data["Grade"];
        if ($grade != SUPERGRADE) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }


        // 手动设置时间时区
        $timeZone = $data["TimeZone"];
        $customizeForm = $data["CustomizeForm"];

        $where = "";
        $bindArray = [];
        if ($begin !== "") {
            $where .= " and CreateTime > :Begin";
            $bindArray[":Begin"] = $begin;
        }
        if ($end !== "") {
            $where .= " and CreateTime < :End";
            $bindArray[":End"] = $end;
        }
        if ($status !== "all") {
            $where .= " and Status = :Status";
            $bindArray[":Status"] = $status;
        }
        if ($manage !== "all") {
            $where .= " and AreaManageID = :AreaManageID";
            $bindArray[":AreaManageID"] = $manage;
        }
        if ($install !== "all") {
            $data = $this->db->querySList(
                "select ID from Account where ManageGroup = :ManageGroup",
                [":ManageGroup" => $install]
            );
            $ids = [];
            foreach ($data as $val) {
                array_push($ids, $val["ID"]);
            }
            $ids = implode(",", $ids);
            $where .= " and InstallID in ( $ids )";
        }
        if ($type !== "all") {
            $where .= " and Type = :Type";
            $bindArray[":Type"] = $type;
        }

        include_once __DIR__ . "/../../plugin/PHPExcel/PHPExcel.php";
        $objPHPExcel = new \PHPExcel();//实例化PHPExcel类 (相当于创建了一个excel表格)
        $objSheet = $objPHPExcel->getActiveSheet();//获取当前活动sheet的操作对象
        $objSheet->setTitle("Statistics");

        $statusArray = [
            MSGTEXT["processing"],
            MSGTEXT["successed"],
            MSGTEXT["failed"],
            MSGTEXT["timeOut"],
            MSGTEXT["abnormal"],
            MSGTEXT["cancel"],
            MSGTEXT["systemProcessing"]
        ];
        $typesArray = [
            "",
            MSGTEXT["activation"],
            MSGTEXT["subscription"],
            MSGTEXT["additionalApp"],
            MSGTEXT["subscription"],
            MSGTEXT["featureActivationFee"],
            MSGTEXT["featureMonthlyFee"],
            MSGTEXT["featurePriceDifferences"],
            MSGTEXT["subscription"]
        ];
        $payerArray = [MSGTEXT["family"], MSGTEXT["community"]];
        $payerRoleArray = [
            MSGTEXT["endUser"],
            MSGTEXT["propertyManager"],
            MSGTEXT["installer"],
            MSGTEXT["distributor"],
            MSGTEXT['subDistributor']
        ];

        $objSheet->setCellValue("A1", "Order Number")->setCellValue("B1", "Total Price")->setCellValue(
            "C1",
            "Type"
        )->setCellValue("D1", "Project")->setCellValue("E1", "Payer Role")->setCellValue(
            "F1",
            "Payer"
        )->setCellValue(
            "G1",
            "Create Time"
        )->setCellValue("H1", "Status")->setCellValue("I1", "Final Price")->setCellValue("J1", "Coupon");


        $read = true;
        $offset = 0;
        $index = 2;
        while ($read) {
            $data = $this->db->querySList(
                "select ID,OrderNumber,TotalPrice,Discount,Type,Payer,PayerType,CreateTime,Status,InstallID,CouponCount,FinalPrice,AccountID from OrderList where ID is not null $where limit $offset,1000",
                $bindArray
            );
            $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
            foreach ($data as $val) {
                $endData = $this->db->querySList(
                    "select ProjectName from OrderEndUserList where OrderID = :OrderID",
                    [":OrderID" => $val["ID"]]
                );
                $batchProject = implode(',', array_unique(array_column($endData, 'ProjectName')));

                $orderNumber = $val["OrderNumber"] . " ";
                $totalPrice = \util\computed\outputComputedCount(
                    \util\computed\computedDiscount($val["TotalPrice"], $val["Discount"])
                );
                $finalPrice = \util\computed\outputComputedCount(
                    \util\computed\computedDiscount($val["FinalPrice"], $val["Discount"])
                );
                $couponCount = \util\computed\outputComputedCount($val["CouponCount"]);
                $type = $typesArray[$val["Type"]];
                $payer = $val["Payer"];
                $payerType = $payerArray[$val["PayerType"]];
                $payerRole = $payerRoleArray[$val["PayerType"]];
                $createTime = $val["CreateTime"];
                $status = $statusArray[$val["Status"]];
                $pcMngData = $this->db->querySList(
                    'select Grade,Location from Account where ID = :ID',
                    [":ID" => $val['InstallID']]
                );
                if (count($pcMngData) == 0) {
                    $project = "";
                } else {
                    $pcMngData = $pcMngData[0];
                    if ($pcMngData["Grade"] == PERSONGRADE) {
                        $project = "";
                    } else {
                        $project = $pcMngData["Location"];
                    }
                }
                // V6.5.1 修复批量支付小区订单导出问题
                $project = $batchProject === '' ? $project:$batchProject;
                if ($val['PayerType'] == 2) {
                    $payer = $this->db->querySList('select A.Account from Account A
                    join Account B on A.ID=B.ManageGroup where B.ID=:ID', [':ID' => $val['AccountID']])[0]['Account'];
                }

                $objSheet->setCellValue("A$index", $orderNumber)->setCellValue(
                    "B$index",
                    $totalPrice
                )->setCellValue(
                    "C$index",
                    $type
                )->setCellValue("D$index", $project)->setCellValue("E$index", $payerRole)->setCellValue(
                    "F$index",
                    $payer
                )->setCellValue("G$index", $createTime)->setCellValue("H$index", $status)->setCellValue(
                    "I$index",
                    $finalPrice
                )->setCellValue("J$index", $couponCount);
                $index++;
            }

            if (count($data) < 1000) {
                break;
            }
            $offset += 1000;
        }


        $fileName = \util\computed\getNow() . ".xlsx";
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");//按照指定的格式生成excel文件
        //赋值结束，开始输出
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        header('Cache-Control: max-age=0');
        $objWriter->save('php://output');
    }
}
