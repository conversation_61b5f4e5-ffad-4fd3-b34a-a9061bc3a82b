<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

include_once __DIR__."/../basic/user.php";
class CUpdate {
    function queryForPCMng () {
        $params = [
            "Status"=>"",
            "Version"=>"",
            "userAlias"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "Key"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $status = $params["Status"];
        $version = $params["Version"];
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $key = $params["Key"];
        list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        
        $myData = $this->db->queryAllList("Account",["equation"=>[":Account"=>$user]])[0];
        $grade = $myData["Grade"];
        $where = '';
        $bindArray = [':OwnerAccount'=>$user];
        if($status != null && $status != '') {
            $bindArray[':Status'] = $status;
            $where = "$where AND U.Status = :Status";
        }
        $update = array();
        if($grade == SUPERGRADE){
            switch ($key){
                case 'Version':
                    $where.= ' and U.Version like :Search';
                    $bindArray[':Search'] = "%$version%";
                    break;
                case 'Mac':
                    $where.= ' and UD.MAC like :Search';
                    $bindArray[':Search'] = "%$version%";
                    break;
                case 'Installer':
                    $where.= ' and Ins.Account like :Search';
                    $bindArray[':Search'] = "%$version%";
                    break;
                case 'Distributor':
                    $where.= ' and Dis.Account like :Search';
                    $bindArray[':Search'] = "%$version%";
                    break;
                default:
                    break;
            }
            $update['total'] = $this->db->querySList(
                "select count(DISTINCT(U.ID)) as total from UpgradeRomVersion U left join UpgradeRomDevices UD on U.ID = UD.UpgradeRomVerID
                 left join Account Ins on Ins.UUID = U.InsUUID
                 left join Account Dis on Dis.ID = Ins.ParentID
                where U.OwnerAccount = :OwnerAccount $where",
                $bindArray
            )[0]['total'];

            $data = $this->db->querySList(
                "select DISTINCT(U.ID),U.Version,U.Status,U.ID,U.CreateTime,U.UpdateTime,U.ProjectType,R.Model,Ins.ID as InsID,Ins.Account as Install,Dis.Account as AreaManage,Dis.ID as DisID 
                from UpgradeRomVersion U 
                left join RomVersion R on U.Version = R.Version
                left join UpgradeRomDevices UD on U.ID = UD.UpgradeRomVerID
                left join Account Ins on Ins.UUID = U.InsUUID
                left join Account Dis on Dis.ID = Ins.ParentID
                where U.OwnerAccount = :OwnerAccount $where order by U.ID desc",
                $bindArray
            );
        }else{
            $bindArray[':Version'] = "%$version%";
            $where = "$where AND U.Version like :Version";
            $data = $this->db->querySList("select ID from UpgradeRomVersion U where U.OwnerAccount = :OwnerAccount $where",$bindArray);
            $update['total'] = count($data);
            $data = $this->db->querySList("select U.Version,U.Status,U.ID,U.CreateTime,U.UpdateTime,R.Model from UpgradeRomVersion U left join RomVersion R on U.Version = R.Version where U.OwnerAccount = :OwnerAccount $where order by U.ID desc limit $offset,$rows",$bindArray);
            $deviceTabel = $grade == PERSONGRADE ? "PersonalDevices" : "Devices";
        }

        $data = \util\time\setQueryTimeZone($data,$timeZone,$customizeForm);
        $update['row'] = array();


        for($i=0; $i<count($data); $i++)
        {
            $row = $data[$i];
            $curKey = array();
            $curKey['Version'] = $row['Version'];
            $curKey['Status'] = $row['Status'];
            $curKey['ID'] = $row['ID'];
            $curKey['CreateTime'] = $row['CreateTime'];
            $curKey['UpdateTime'] = $row['UpdateTime'];
            $curKey['Model'] = $row['Model'];
            if($grade == SUPERGRADE){
                $curKey['ProjectType'] = $row['ProjectType'];
                $curKey['InsID'] = $row['InsID'];
                $curKey['Install'] = $row['Install'];
                $curKey['AreaManage'] = $row['AreaManage'];
                $curKey['DisID'] = $row['DisID'];
                $deviceTabel = $row['ProjectType'] == 3 ? "PersonalDevices" : "Devices";
            }
            $sql = "select D.Location,D.MAC from $deviceTabel D left join UpgradeRomDevices U on D.MAC = U.MAC where U.UpgradeRomVerID = :ID";
            
            $simtArray1 = $this->db->querySList($sql,[":ID"=>$row["ID"]]);
            $curKey['Device'] = array();
            $curKey['DeviceList'] = array();
            foreach($simtArray1 as $value)
            {
                array_push($curKey['Device'],$value['MAC']);
                array_push($curKey['DeviceList'],$value['Location']?$value['Location']:$value['MAC']);
            }
            $curKey['DeviceList'] = implode(',', $curKey['DeviceList']);
            array_push($update['row'],$curKey);
        }
        $update['detail'] = $update['row'];
        \util\computed\setGAppData(["data"=>$update]);
    }

    function getVersion () {
        $params = [
            "userAliasId"=>"",
            "Model"=>"",
            "DisID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $model = $params["Model"];
        $userId = $params["userAliasId"];
        $areaManageID = $params['DisID'];
        $myData = $this->db->queryAllList("Account",["equation"=>[":ID"=>$userId]])[0];
        $grade = $myData["Grade"];
        if($grade == SUPERGRADE){
            empty($areaManageID) && $areaManageID = 0;
        }elseif(in_array($grade, PROJECT_ROLE)){
            $areaManageID = $myData["ParentID"];
        }

        $data = $this->db->querySList(
            "select distinct(Version),Log from RomVersion where Model = :Model and AllManage = 1 
            union select distinct(R.Version),Log from RomVersion R join ReleaseRomVersion V on R.ID = V.VersionID 
            where V.MngID = $areaManageID and R.Model = :Model",[':Model'=>$model]);
        \util\computed\setGAppData(["data"=>$data]);
    }

    function add () {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
            "userAlias"=>"",
            "Device"=>"",
            "UpdateTime"=>"",
            "Version"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            'InsID' => '',
            'ProjectType' => '',
            'UpgradeType' => ''
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $device = $params["Device"];
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $time = $params["UpdateTime"];
        $version = $params["Version"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $insID = $params['InsID'];
        $projectType = $params['ProjectType'];
        //0-马上升级，1-择期时间
        $upgradeType = $params['UpgradeType'];
        $info = $this->db->querySList("select Grade from Account where ID = :ID", [':ID' => $userId])[0];
        if(intval($info['Grade']) === SUPERGRADE){
            //super操作校验ins参数跟设备参数是否合法
            $this->checkDeviceBelongIns($insID, $projectType, $device);
        }
        //之前如果是马上升级是由前端传值（本地时间），用户如果切换项目时区，此时存到数据库是不准确的，顾要取服务器时间
        if($upgradeType === '1'){
            $time = \util\time\setTimeZone($time,$timeZone,"","-");
        }else{
            $time = date('Y-m-d H:i:s');
        }
        $nowTime = time();
        $updateTime = strtotime($time);
        $updateTime = $updateTime < $nowTime ? $nowTime :$updateTime;
        if (strtotime("+1 hour",$updateTime)<=$nowTime) $cMessage->echoErrorMsg(StateTimeLessCurrent);

        $now = date('Y-m-d H:i:s', $nowTime);
        $time = date('Y-m-d H:i:s', $updateTime);
        $romVersionData = [":Version"=>$version,":CreateTime"=>$now,":UpdateTime"=>$time,":OwnerAccount"=>$user];
        if(intval($info['Grade']) === SUPERGRADE){
            $insInfo = $this->db->querySList("select UUID from Account where ID = :ID", [':ID' => $insID])[0];
            $romVersionData[':InsUUID'] = $insInfo['UUID'];
            $romVersionData[':ProjectType'] = $projectType;
        }
        $this->db->insert2List("UpgradeRomVersion", $romVersionData);
        $versionID = $this->db->lastInsertId();
        foreach($device as $value) {
            $this->db->insert2List("UpgradeRomDevices",[":MAC"=>$value,":UpgradeRomVerID"=>$versionID]);
        }
    }

    /**
     * @description:验证super操作时 选择的设备所属项目跟Ins是否属于同个账号组
     * @author:lwj 2023-03-21 15:51:19 V6.5.4
     * @lastEditor:lwj 2023-03-21 15:51:19 V6.5.4
     * @param $insID
     * @param $projectType
     * @param $device
     * @param:
     * @throws \Exception
     */
    public function checkDeviceBelongIns($insID, $projectType, $device)
    {
        global $cMessage;
        $macList = \util\arr\implodeWithQuotation($device);
        $deviceTotal = 0;
        //ProjectType:1-社区，2-办公，3-单住户
        if($projectType == 1){
            $projectList = $this->db->querySList(
                "select ID from Account where Grade in (" . COMMUNITYGRADE . "," . PERSONGRADE . ") and ManageGroup =:ManageGroup ",
                [':ManageGroup' => $insID]
            );
            $projectIDList = \util\arr\implodeWithQuotation(array_column($projectList, 'ID'));
            if(!empty($projectIDList)){
                $deviceTotal = $this->db->querySList(
                    "select count(*) as total from Devices where MAC in ($macList) and MngAccountID in($projectIDList)"
                )[0]['total'];
            }
        }elseif ($projectType == 2){
            $projectList = $this->db->querySList(
                "select ID from Account where Grade in (" . OFFICEGRADE .") and ManageGroup =:ManageGroup ",
                [':ManageGroup' => $insID]
            );
            $projectIDList = \util\arr\implodeWithQuotation(array_column($projectList, 'ID'));

            if(!empty($projectIDList)){
                $deviceTotal = $this->db->querySList(
                    "select count(*) as total from Devices where MAC in ($macList) and MngAccountID in($projectIDList)"
                )[0]['total'];
            }
        }elseif ($projectType == 3){
            $projectList = $this->db->querySList(
                "select Account from Account where Grade in (" . COMMUNITYGRADE . "," . PERSONGRADE . ") and ManageGroup =:ManageGroup ",
                [':ManageGroup' => $insID]
            );
            $accountList = \util\arr\implodeWithQuotation(array_column($projectList, 'Account'));
            if(!empty($accountList)){
                $deviceTotal = $this->db->querySList(
                    "select count(*) as total from PersonalDevices where MAC in ($macList) and Community in($accountList)"
                )[0]['total'];
            }
        }
        if(count($device) != intval($deviceTotal)){
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }

    function edit () {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
            "userAlias"=>"",
            "Device"=>"",
            "UpdateTime"=>"",
            "Version"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "ID"=>"",
            'InsID' => '',
            'ProjectType' => ''
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $device = $params["Device"];
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $time = $params["UpdateTime"];
        $version = $params["Version"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $id = $params["ID"];
        $insID = $params['InsID'];
        $projectType = $params['ProjectType'];
        $info = $this->db->querySList("select Grade from Account where ID = :ID", [':ID' => $userId])[0];
        if(intval($info['Grade']) === SUPERGRADE){
            //super操作校验ins参数跟设备参数是否合法
            $this->checkDeviceBelongIns($insID, $projectType, $device);
        }
        $time = \util\time\setTimeZone($time,$timeZone,"","-");
        $nowTime = time();
        $updateTime = strtotime($time);
        $updateTime = $updateTime < $nowTime ? $nowTime :$updateTime;
        if (strtotime("+1 hour",$updateTime)<=$nowTime) $cMessage->echoErrorMsg(StateTimeLessCurrent);

        $now = date('Y-m-d H:i:s', $nowTime);
        $time = date('Y-m-d H:i:s', $updateTime);
        $romVersionData = [":Version"=>$version,":ID"=>$id,":UpdateTime"=>$time];
        if(intval($info['Grade']) === SUPERGRADE){
            $insInfo = $this->db->querySList("select UUID from Account where ID = :ID", [':ID' => $insID])[0];
            $romVersionData[':InsUUID'] = $insInfo['UUID'];
            $romVersionData[':ProjectType'] = $projectType;
        }
        $this->db->update2ListWID("UpgradeRomVersion",$romVersionData);
        $this->db->delete2ListWKey("UpgradeRomDevices","UpgradeRomVerID",$id);
        foreach($device as $value) {
            $this->db->insert2List("UpgradeRomDevices",[":MAC"=>$value,":UpgradeRomVerID"=>$id]);
        }
    }

    function delete () {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];

        $this->db->delete2ListWKey("UpgradeRomDevices","UpgradeRomVerID",$id);
        $this->db->delete2ListWID("UpgradeRomVersion",$id);
    }

    function queryVersionForSup () {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $where = "";
        $bindArray = [];
        switch ($serchKey) {
            case 'Version':
                $where = "where Version like :Key";
                $bindArray = [":Key"=>"%$serchValue%"];
                break;
            case 'Model':
                $where = "where Model like :Key";
                $bindArray = [":Key"=>"%$serchValue%"];
                # code...
                break;
        }

        $total = $this->db->querySList("select count(*) from RomVersion $where",$bindArray)[0]["count(*)"];
        $data = $this->db->querySList("select * from RomVersion $where order by ID desc limit $offset,$rows",$bindArray);
        $rows = [];
        $data = \util\time\setQueryTimeZone($data,$timeZone,$customizeForm);
        foreach($data as &$value) {
            $data2 = $this->db->queryAllList("ReleaseRomVersion",["equation"=>[":VersionID"=>$value["ID"]]]);
            $ids = [];
            foreach($data2 as $value1)
                array_push($ids,$value1["MngID"]);
            $ids = implode(";", $ids);
            $value["ManageID"] = $ids;
            $row = $value;
            $curKey = array();
            $curKey['Version'] = $row['Version'];
            $curKey['Model'] = $row['Model'];
            $curKey['CreateTime'] = $row['CreateTime'];
            $curKey['ID'] = $row['ID'];
            $logArray = explode("\n", $row['Log']);
            $curKey['Log'] = $logArray;
            array_push($rows,$curKey);
        }
        \util\computed\setGAppData(["data"=>["total"=>$total,"row"=>$rows,"detail"=>$data]]);
    }

    function queryVersionForArea () {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];

        list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $total = count($this->db->querySList("select ID from RomVersion where AllManage = 1 union SELECT distinct R.ID from RomVersion R join ReleaseRomVersion V on R.ID = V.VersionID where V.MngID = ".$userId));
        $data = $this->db->querySList("select * from RomVersion where AllManage = 1 union SELECT distinct R.* from RomVersion R join ReleaseRomVersion V on R.ID = V.VersionID where V.MngID = $userId  order by ID desc limit $offset,$rows");
        $data = \util\time\setQueryTimeZone($data,$timeZone,$customizeForm);
        
        $rows = [];
        foreach($data as &$value) {
            $row = $value;
            $curKey = array();
            $curKey['Version'] = $row['Version'];
            $curKey['Model'] = $row['Model'];
            $curKey['CreateTime'] = $row['CreateTime'];
            $curKey['ID'] = $row['ID'];
            $logArray = explode("\n", $row['Log']);
            $curKey['Log'] = $logArray;
            array_push($rows,$curKey);
        }
        \util\computed\setGAppData(["data"=>["total"=>$total,"row"=>$rows,"detail"=>$data]]);
    }

    function addVersion () {
        global $cMessage;
        $params = [
            "Log"=>"",
            "Version"=>"",
            "Url"=>"",
            "AllManage"=>"",
            "ManageID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $log = $params["Log"];
        $version = $params["Version"];
        $url = $params["Url"];
        $allManage = $params["AllManage"];
        $mngID = $params["ManageID"];
        $allManage = $allManage == 1 ? 1 : 0;
        $temVersion = explode(".", $version)[0];

        $data = $this->db->querySList("select VersionName from VersionModel where VersionNumber = :VersionNumber",[":VersionNumber"=>$temVersion]);
        if(count($data) == 0) $cMessage->echoErrorMsg(StateErrorVersion);
        $model = $data[0]["VersionName"];
    
        $data = $this->db->querySList("select ID from RomVersion where Version = :Version",[":Version"=>$version]);
        if(count($data)>0) $cMessage->echoErrorMsg(StateVersionExit);
        $now = \util\computed\getNow();
        $this->db->insert2List("RomVersion",[":Version"=>$version,":Model"=>$model,":Log"=>$log,":CreateTime"=>$now,":Url"=>$url,":AllManage"=>$allManage]);
        $versionID = $this->db->lastInsertId();

        if($allManage == 0) {
            $mngID = explode(";", $mngID);
            foreach($mngID as $value) {
                $this->db->insert2List("ReleaseRomVersion",[":VersionID"=>$versionID,":MngID"=>$value]);
            }
        }
    }

    function deleteVersion () {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $this->db->delete2ListWID("RomVersion",$id);
        $this->db->delete2ListWKey("ReleaseRomVersion","VersionID",$id);
    }

    function editVersion () {
        $params = [
            "Log"=>"",
            "Version"=>"",
            "Url"=>"",
            "AllManage"=>"",
            "ManageID"=>"",
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $log = $params["Log"];
        $version = $params["Version"];
        $url = $params["Url"];
        $allManage = $params["AllManage"];
        $mngID = $params["ManageID"];
        $id = $params["ID"];
        $allManage = $allManage == 1 ? 1 : 0;
        $this->db->update2ListWID("RomVersion",[":Log"=>$log,":Version"=>$version,":Url"=>$url,":AllManage"=>$allManage,":ID"=>$id]);
    
        $this->db->delete2ListWKey("ReleaseRomVersion","VersionID",$id);
        if($allManage == 0) {
            $mngID = explode(";", $mngID);
            foreach($mngID as $value) {
                $this->db->insert2List("ReleaseRomVersion",[":VersionID"=>$id,":MngID"=>$value]);
            }
        }
    }

    function queryModel () {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        list($offset,$rows,$serchKey,$serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $total = $this->db->querySList("select count(*) from VersionModel")[0]["count(*)"];
        $data = $this->db->querySList("select * from VersionModel order by ID desc limit $offset,$rows");
        $data = \util\time\setQueryTimeZone($data,$timeZone,$customizeForm);
        $rows = [];
        foreach($data as $value) {
            $curr = [];
            $curr['ID'] = $value['ID'];
            $curr['Type'] = $value['Type'];
            $curr['VersionName'] = $value['VersionName'];
            $curr['CreateTime'] = $value['CreateTime'];
            $curr['Version'] = $value['VersionNumber'];
            array_push($rows,$curr);
        }
        \util\computed\setGAppData(["data"=>["total"=>$total,"row"=>$rows,"detail"=>$data]]);
    }

    function queryAllModel () {
        $data = $this->db->querySList("select VersionName from VersionModel");
        \util\computed\setGAppData(["data"=>$data]);
    }

    function addModel () {
        global $cMessage;
        $params = [
            "VersionName"=>"",
            "VersionNumber"=>"",
            'Type' => ''
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $name = $params["VersionName"];
        $number = $params["VersionNumber"];
        //设备类型
        $type = $params['Type'];

        $data = $this->db->querySList("select ID from VersionModel where VersionName = :VersionName OR VersionNumber = :VersionNumber",[":VersionName"=>$name,":VersionNumber"=>$number]);
        if(count($data) > 0) $cMessage->echoErrorMsg(StateVersionNameNumberExit);
        $now = \util\computed\getNow();
        $this->db->insert2List("VersionModel",[":VersionNumber"=>$number,":VersionName"=>$name,":CreateTime"=>$now, ':Type' => $type]);
    }

    function editModel () {
        global $cMessage;
        $params = [
            "VersionName"=>"",
            "VersionNumber"=>"",
            'Type' => '',
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $name = $params["VersionName"];
        $number = $params["VersionNumber"];
        //设备类型
        $type = $params['Type'];
        $id = $params["ID"];
        $info = $this->db->querySList("select * from VersionModel where ID = :ID",[ ":ID"=>$id])[0];
        $data = $this->db->querySList("select ID from VersionModel where (VersionName = :VersionName OR VersionNumber = :VersionNumber) AND ID != :ID",[":VersionName"=>$name,":VersionNumber"=>$number,":ID"=>$id]);
        if(count($data) > 0) $cMessage->echoErrorMsg(StateVersionNameNumberExit);
        $this->db->update2ListWID("VersionModel",[":VersionName"=>$name,":VersionNumber"=>$number, ':Type' => $type, ":ID"=>$id]);
        //更新RomVersion
        $list = $this->db->querySList(
            "select * from RomVersion where Model = :Model",
            [':Model' => $info['VersionName']]
        );
        foreach ($list as $v){
            $this->db->update2ListWID("RomVersion",[":Model"=> $name, ":ID"=>$v['ID']]);
        }
    }

    function deleteModel () {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $id = $params["ID"];
        $this->db->delete2ListWID("VersionModel",$id);
    }

    function getUpgradeMsg() {
        $params = [
            'type' => '',
            'version' => '',
            'role' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params['type'];
        $version = $params['version'];
        $role = $params['role'];
        // V6.4增加登陆拦截提示语
        if ($type === '2') {
            $messages = explode(';', MSGTEXT['textUpgradeMsg2']);
        } elseif ($type === '1') {
            $messages = explode(';', MSGTEXT['textUpgradeMsg1']);
        } else {
            if ($version < 6400) {
                $messages = explode(';', MSGTEXT['textUpgradeMsg']);
            } else {
                if (in_array($role, COMROLE)) {
                    $messages = explode(';', MSGTEXT['textUpgradeMsgForComRole']);
                } elseif (in_array($role, PERROLE)) {
                    $messages = explode(';', MSGTEXT['textUpgradeMsgForPerRole']);
                } elseif (in_array($role, OFFROLE)) {
                    $messages = explode(';', MSGTEXT['textUpgradeMsgForOffRole']);
                } elseif ($role == PMENDMROLE) {
                    $messages = explode(';', MSGTEXT['textUpgradeMsgForPMRole']);
                }
            }
        }
        \util\computed\setGAppData(["data"=>["txt"=>$messages]]);
    }

    function getAppWord() {
        $params = [
            'word_key' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $wordKey = $params['word_key'];

        // V6.4新增，用于app获取部分词条
        $messages = explode(';', MSGTEXT[$wordKey]);

        \util\computed\setGAppData(["data"=>["txt"=>$messages]]);
    }
}