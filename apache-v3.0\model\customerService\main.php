<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */
namespace model;
include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

class CCustomerService {
    function getCSData($user) {
        $sql = "select * from CustomerService where MngAccount = :MngAccount";
        return $this->db->querySList($sql,[":MngAccount"=>$user]);
    }

    /**
     * distributor
     */
    function query () {
        $params = [
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->getCSData($user);
        if(count($data) == 0) {
            $data = $this->getCSData("SuperManage");
        }
            
        $phone = $data[0]["Phone"];
        $email = $data[0]["Email"];
        \util\computed\setGAppData(["data"=>["Phone"=>$phone,"Email"=>$email]]);
    }

    /**
     * installer
     */
    function queryInstaller () {
        $params = [
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $user = $params["userAlias"];
        $userData = $this->db->querySList("select PA.Account as Distributor,INS.Account as Installer,INS.UUID as InstallerUUID from Account PA 
        join Account SU on SU.ParentID = PA.ID join Account INS on SU.ManageGroup = INS.ID where SU.Account = :Account",[":Account"=>$user]);

        $data = $this->getCSData($userData[0]["Installer"]);
        if(count($data) == 0) {
            // 判断ins是否被sub dis管理
            $subDis = $this->db->querySList('select DistributorUUID from SubDisMngList where InstallerUUID = :InstallerUUID', [
                ':InstallerUUID' => $userData[0]['InstallerUUID']]);
            if (count($subDis) > 0) {
                $subDisAccount = $this->db->querySList('select Account from Account where UUID = :UUID', [
                    ':UUID' => $subDis[0]['DistributorUUID']])[0]['Account'];
                $subDis = $this->getCSData($subDisAccount);
            }
            // sub dis有值展示否则展示dis的
            if (count($subDis) > 0) {
                \util\computed\setGAppData(["data"=>["Phone"=>$subDis[0]["Phone"],"Email"=>$subDis[0]["Email"]]]);
            } else {
                \util\computed\setGAppData(["userAlias"=>$userData[0]["Distributor"]]);
                $this->query();
            }
        }else{
            // 展示ins的客服信息
            $phone = $data[0]["Phone"];
            $email = $data[0]["Email"];
            \util\computed\setGAppData(["data"=>["Phone"=>$phone,"Email"=>$email]]);
        }
    }

    function querySelf () {
        global $gApp;
        $params = [
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $user = $params["userAlias"];
        $role = $gApp["role"];
        if($role == RCOMMUNITYGRADE) $user = $this->db->querySList("select INS.Account from Account INS join Account COM on INS.ID = COM.ManageGroup where COM.Account = :Account",[":Account"=>$user])[0]["Account"];
        $data = $this->getCSData($user);
        if(count($data) == 0) {
            $phone = "";
            $email = "";
        }else{
            $phone = $data[0]["Phone"];
            $email = $data[0]["Email"];
        }
        \util\computed\setGAppData(["data"=>["Phone"=>$phone,"Email"=>$email]]);
    }

    function update () {
        global $gApp;
        $params = [
            "Phone"=>"",
            "Email"=>"",
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $phone = $params["Phone"];
        $email = $params["Email"];
        $user = $params["userAlias"];
        $role = $gApp["role"];
        if($role == RCOMMUNITYGRADE) $user = $this->db->querySList("select INS.Account from Account INS join Account COM on INS.ID = COM.ManageGroup where COM.Account = :Account",[":Account"=>$user])[0]["Account"];
        $this->log->actionLog("#model#customerService#update#user=$user;phone=$phone;email=$email");
        
        $data = $this->getCSData($user);
        if(count($data) == 0)
            $this->add($phone,$email,$user);
        else
            $this->modify($phone,$email,$user);
    }

    function add ($phone,$email,$user) {
        $this->db->insert2List("CustomerService",[":Phone"=>$phone,":Email"=>$email,":MngAccount"=>$user]);
    }

    function modify ($phone,$email,$user) {
        $this->db->update2ListWKey("CustomerService",[":Phone"=>$phone,":Email"=>$email,":MngAccount"=>$user],"MngAccount");
    }

    private function checkValidServer () {

    }
}