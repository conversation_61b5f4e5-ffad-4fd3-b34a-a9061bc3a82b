<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2020-01-20 15:40:20
 * @LastEditors: cj
 */

namespace model\key;

trait remove
{
    public function deleteCom($account)
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $keyid = $params["ID"];

        $tabel = $this->tabelName[$this->type];
        $this->log->actionLog("#model#key#deleteCom#keyid=$keyid;tabel=$tabel");
        if ($account == null) {
            $account = $this->db->queryAllList($tabel, ["equation" => [":ID" => $keyid]])[0]["Node"];
        }
        $keyData = $this->db->queryAllList($tabel, ["equation" => [":ID" => $keyid]])[0];

        if ($this->type === 0) {
            $this->db->delete2List<PERSON>Key("PersonalAppTmpKeyList", "KeyID", $keyid);
        }
        $this->db->exec2ListWArray(
            "delete from $tabel where ID = :ID AND Node = :Node",
            [":ID" => $keyid, ":Node" => $account]
        );
        \util\computed\setGAppData(["Account" => $account, "keyData" => $keyData, "keyUrl" => $keyData['QrCodeUrl']]);
        $this->auditLog->setLog(
            [AuditCodeDeleteTemp, AuditCodeDeleteRf, AuditCodeDeletePin][$this->type],
            $this->env,
            [$this->type == 0 ? $keyData["TmpKey"] : $keyData["Code"]],
            $account
        );
    }


    public function deleteRF()
    {
        global $gApp;
        $role = $gApp["role"];

        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $this->type = 1;
        $this->deleteCom($user);

        $keyData = \util\model\getParamsFromContainer(["keyData" => ""], $this->dataContainer)["keyData"];
        $code = $keyData["Code"];
        if ($role === RPERENDMROLE) {
            $this->log->endUserLog(3, null, "delete RF card:$code");
        }
    }

    public function deletePri()
    {
        global $gApp;
        $role = $gApp["role"];
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $this->type = 2;
        $this->deleteCom($user);

        $keyData = \util\model\getParamsFromContainer(["keyData" => ""], $this->dataContainer)["keyData"];
        $code = $keyData["Code"];
        if ($role === RPERENDMROLE) {
            $this->log->endUserLog(3, null, "delete private key:$code");
        }
    }

    public function deleteTmp()
    {
        $this->type = 0;
        $this->deleteFCom();
    }

    public function deleteRFFc()
    {
        $this->type = 1;
        $this->deleteFCom();
    }

    public function deletePriFc()
    {
        $this->type = 2;
        $this->deleteFCom();
    }

    public function deleteFCom()
    {
        $this->deleteCom(null);
    }
}
