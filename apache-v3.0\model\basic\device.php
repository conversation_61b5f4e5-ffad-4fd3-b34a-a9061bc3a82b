<?php
namespace basic;

abstract class CDevice
{
    /**
     * @msg: installer将mac添加到mac库
     * @service: rps
     * @model：macLibrary
     */
    public function addPCMng2MacLibrary($mac, $userId)
    {
        $myData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
        $parentID = $myData["ParentID"];
        $this->models["macLibrary"]->importPerDevice($mac, $parentID, $userId);
    }
}
