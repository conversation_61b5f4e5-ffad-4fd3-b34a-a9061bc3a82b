<?php
/*
 * @Description: 中间件处理中心
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-10 10:17:56
 * @LastEditors  : kxl
 */

namespace container;
include_once __DIR__."/../util/string.php";
include_once __DIR__."/data.php";
function slice ($stack, $pipes) {
    return function() use ($stack, $pipes)
    {
        global $gApp;
        $pipe = $pipes["name"];
        
        // 自定义参数
        $params = $pipes["params"] ?: [];
        // 获取数据容器
        $dataContainerName = array_key_exists("dataContainer",$pipes) ? $pipes["dataContainer"] : "common";
        $dataContainer = \container\setData($gApp["plan"]["data"],$dataContainerName);

        include_once __DIR__."/../middleware/$pipe.php";
        $pipe = \util\string\getClassName($pipe);
        $pipe = "\\middleware\\$pipe";
        $pipe = new $pipe();
        $pipe->dataContainer = $dataContainer;
        $pipe->params = $params;
        return $pipe->handle($stack);
    };
};

function pipeFilter (array $middles) {
    $pipes = array_reverse($middles);
    $end = function () {};
    $start = array_reduce($pipes, "\\container\\slice",$end);
    $start();
}