<?php
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

include_once __DIR__."/../../notify/funcs.php";
class CAlexa
{
    public function goToLogin()
    {
        $params = [
            "state"=>"",
            "client_id"=>"",
            "scope"=>"",
            "response_type"=>"",
            "redirect_uri"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $state = $params["state"];
        $clientId = $params["client_id"];
        $scope = $params["scope"];
        $responseType = $params["response_type"];
        $redirectUri = $params["redirect_uri"];
        $url = "state=".urlencode($state)."&client_id=".urlencode($clientId)."&scope=".urlencode($scope)."&response_type=".urlencode($responseType)."&redirect_uri=".urlencode($redirectUri);
            
        $url = SERVERHOST."/alaxe/Login.html?".$url;
        header("Location:$url");
    }

    public function deleteToken()
    {
        $params = [
            "userId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $uid = $params["userId"];
        $this->db->update2ListWKey("Token", [":AlexaToken"=>"",":UID"=>$uid], "UID");
    }

    public function thirdLogin()
    {
        global $cMessage;
        $params = [
            "Account"=>"",
            "Passwd"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $passwd = $params["Passwd"];
        $data = $this->db->querySList(
            "select Account from PersonalAccount where (Account = :Account or Email = :Account) and Passwd = :Passwd and (Role = 10 or Role = 20)",
            [":Account"=>$account,":Passwd"=>md5($passwd)]
        );
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateAccountIncorrect);
        }
        $account = $data[0]["Account"];
        $token = \util\string\randString(6);
        $hasToken = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$account]]);
        if (count($hasToken) == 0) {
            $this->db->insert2List("Token", [":Account"=>$account,":AlexaToken"=>$token]);
        } else {
            $this->db->update2ListWKey("Token", [":Account"=>$account,":AlexaToken"=>$token], "Account");
        }
        
        \util\computed\setGAppData(["Account"=>$account,"Token"=>$token]);
        \alexaLoginNotify($account);
    }

    public function thirdAuth()
    {
        $params = [
            "code"=>"",
            "redirect_uri"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $code = $params["code"];
        $url = $params["redirect_uri"];
        $res = ["access_token"=>$code,
                "token_type"=>"example",
                "expires_in"=>3600*48,
                "refresh_token"=>$code,
                "example_parameter"=>"example_value"
        ];

        header("Cache-Control: no-cache");
        header("Pragma: no-cache");
        header("Content-Type: application/json;charset=UTF-8");
        echo json_encode($res);
    }

    public function setUID()
    {
        global $gApp;
        $params = [
            // request中的参数
            "userId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $uid = $params["userId"];
        $user = $gApp["user"];
        $this->db->update2ListWKey("Token", [":Account"=>$user,":UID"=>$uid], "Account");
    }

    public function getStatus()
    {
        global $gApp,$cMessage;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "MAC"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];

        // 验证设备和用户的所属性
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$user]])[0];
        $devicesTabel = in_array($userData["Role"], PERROLE) ? "PersonalDevices" : "Devices";
        $device = $this->db->queryAllList($devicesTabel, ["equation"=>[":MAC"=>$mac,":Node"=>$user]]);
        if (count($device) === 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        $arms = ["DISARMED","ARMED_STAY","ARMED_NIGHT","ARMED_AWAY"];
        $arm = $arms[$device[0]["Arming"]];
        $connect = ["UNREACHABLE","OK"];

        $alarms = $this->db->queryAllList("Alarms", ["equation"=>[":DevicesMAC"=>$mac,":Status"=>0]]);
        $alarm = count($alarms) === 0 ? "OK" : "ALARM";


        \util\computed\setGAppData(["mode"=>$arm,"connectivity"=>$connect[$device[0]["Status"]],"alarm"=>$alarm]);
    }

    /**
     * msg:设置设备布撤防状态
     */
    public function setAram()
    {
        global $gApp,$cMessage;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "MAC"=>"",
            "mode"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $mode = $params["mode"];

        // 验证设备和用户的所属性
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$user]])[0];
        $devicesTabel = in_array($userData["Role"], PERROLE) ? "PersonalDevices" : "Devices";
        $device = $this->db->queryAllList($devicesTabel, ["equation"=>[":MAC"=>$mac,":Node"=>$user]]);
        if (count($device) === 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $status = ["DISARMED"=>0,"ARMED_STAY"=>1,"ARMED_NIGHT"=>2,"ARMED_AWAY"=>3];
        if (!array_key_exists($mode, $status)) {
            $msg = ["msg"=>"Invalid mode.","code"=>1];
            echo json_encode($msg);
            die;
        }

        if (in_array($status[$mode], [1,2,3])) {
            $offset = ["3"=>1,"1"=>2,"2"=>3][$status[$mode]];

            $flag = intval($device[0]["Flags"]);


            if ($flag >> $offset & 1) {
                $res = 1;
                $msg = ["msg"=>"","code"=>$res];
                echo json_encode($msg);
                die;
            }
        }

        $res = 0;
        $msg = ["msg"=>"","code"=>$res];
        echo json_encode($msg);
        \util\computed\setGAppData(["mac"=>$mac,"mode"=>$status[$mode]]);
    
        \alexaSetArmingNotify($mac, $status[$mode]);
    }

    /**
     * msg:aws验证
     */
    public function setAuthorization()
    {
        global $gApp;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "grant_code"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $grantCode = $params["grant_code"];
        $url = "https://api.amazon.com/auth/o2/token";
        $postData = "grant_type=authorization_code&code=$grantCode&client_id=amzn1.application-oa2-client.7d80c1a283394b1391aac1c8fab82499&client_secret=a56d39a012d0ed462e65390f19b19a19c09c5d1bfe505a134329619a125186a1";
        
        $res = $this->proxyPost($url, $postData, ["Content-Type: application/x-www-form-urlencoded"]);
        if ($res === false) {
            $msg = ["code"=>1,"msg"=>"Set authorization failed."];
        } else {
            $access = $res["access_token"];
            $reflash = $res["refresh_token"];
            $this->db->update2ListWKey("Token", [":Account"=>$user,":AlexaAccessToken"=>$access,":AlexaReflashToken"=>$reflash], "Account");
            $msg = ["code"=>0,"msg"=>"Set authorization sucessful."];
        }
        echo json_encode($msg);
    }

    public function changeArm()
    {
        global $gApp;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "mode"=>"",
            "mac"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $arm = $params["mode"];
        $mac = $params["mac"];
        $arms = ["DISARMED","ARMED_STAY","ARMED_NIGHT","ARMED_AWAY"];
        $arm = $arms[$arm];
        $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
        $type = 1;
        $status = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$mac]])[0]["Status"];
        $states = ["UNREACHABLE","OK"];
        $status = $states[$status];
        $alarms = $this->db->queryAllList("Alarms", ["equation"=>[":DevicesMAC"=>$mac,":Status"=>0]]);
        $alarm = count($alarms) === 0 ? "OK" : "ALARM";
        $res = $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        if ($res == 401) {
            $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
            $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        }
    }

    public function changeStatus()
    {
        global $gApp;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "status"=>"",
            "mac"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $status = $params["status"];
        $mac = $params["mac"];

        $states = ["UNREACHABLE","OK"];
        $status = $states[$status];
        $arms = ["DISARMED","ARMED_STAY","ARMED_NIGHT","ARMED_AWAY"];
        $arm = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$mac]])[0]["Arming"];
        $arm = $arms[$arm];
        $type = 2;
        $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
        $alarms = $this->db->queryAllList("Alarms", ["equation"=>[":DevicesMAC"=>$mac,":Status"=>0]]);
        $alarm = count($alarms) === 0 ? "OK" : "ALARM";
        $res = $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        if ($res == 401) {
            $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
            $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        }
    }

    public function changeAlarm()
    {
        global $gApp;
        $user = $gApp["user"];
        $params = [
            // request中的参数
            "alarm"=>"",
            "mac"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $alarm = $params["alarm"];
        $mac = $params["mac"];

        $arms = ["DISARMED","ARMED_STAY","ARMED_NIGHT","ARMED_AWAY"];
        $arm = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$mac]])[0]["Arming"];
        $arm = $arms[$arm];
        $status = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$mac]])[0]["Status"];
        $states = ["UNREACHABLE","OK"];
        $status = $states[$status];
        $type = 3;
        $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
        $alarm = ["OK","ALARM"][$alarm];
        $res = $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        if ($res == 401) {
            $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
            $this->changeReportControl($mac, $tokens, $arm, $status, $alarm, $type);
        }
    }

    public function changeReportControl($mac, $tokens, $arm, $status, $alarm, $type)
    {
        $now = date('Y-m-d\TH:i:s\Z', time() - date('Z'));
        $arm = [
            "namespace"=>"Alexa.SecurityPanelController",
            "name"=>"armState",
            "value"=>$arm,//布撤防
            "timeOfSample"=>$now,
            "uncertaintyInMilliseconds"=>0
        ];

        $status = [
            "namespace"=>"Alexa.EndpointHealth",
            "name"=>"connectivity",
            "value"=>[
                "value"=>$status
            ],
            "timeOfSample"=>$now,
            "uncertaintyInMilliseconds"=>0
        ];

        $alarm = [
            "namespace"=> "Alexa.SecurityPanelController",
            "name"=> "burglaryAlarm",
            "value"=> [
                "value"=> $alarm
            ],
            "timeOfSample"=>$now,
            "uncertaintyInMilliseconds"=> 0
        ];

        if ($type === 1) {
            $context = [$status,$alarm];
            $change = $arm;
        } elseif ($type === 2) {
            $context = [$arm,$alarm];
            $change = $status;
        } else {
            $context = [$arm,$status];
            $change = $alarm;
        }

        $url = "https://api.amazonalexa.com/v3/events";
        $data = [
            "context"=>[
                "properties"=>$context,
            ],
            "event"=>[
                "header"=>[
                    "messageId"=>"abc-123-def-456",
                    "namespace"=>"Alexa",
                    "name"=>"ChangeReport",
                    "payloadVersion"=>"3"
                ],
                "endpoint"=>[
                    "scope"=>[
                        "type"=>"BearerToken",
                        "token"=>$tokens["AlexaAccessToken"]
                    ],
                    "endpointId"=>$mac
                ],
                "payload"=>[
                    "change"=>[
                        "cause"=>[
                            "type"=>"PHYSICAL_INTERACTION"
                        ],
                        "properties"=>[$change]
                    ]
                ]
            ]
        ];
        return $this->proxyPost($url, json_encode($data));
    }

    /**
     * msg:token过期时刷新token
     */
    public function refresh()
    {
        global $gApp;
        $user = $gApp["user"];
        $refreshToken = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0]["AlexaReflashToken"];
        
        $url = "https://api.amazon.com/auth/o2/token";
        $postData = "grant_type=refresh_token&refresh_token=$refreshToken&client_id=amzn1.application-oa2-client.7d80c1a283394b1391aac1c8fab82499&client_secret=a56d39a012d0ed462e65390f19b19a19c09c5d1bfe505a134329619a125186a1";
        $res = $this->proxyPost($url, $postData, ["Content-Type: application/x-www-form-urlencoded"]);
        if ($res === false) {
            return false;
        } else {
            $access = $res["access_token"];
            $reflash = $res["refresh_token"];
            $this->db->update2ListWKey("Token", [":Account"=>$user,":AlexaAccessToken"=>$access,":AlexaReflashToken"=>$reflash], "Account");
            return true;
        }
    }

    /**
     * msg:清空token
     */
    public function clearToken()
    {
        global $gApp;
        $user = $gApp["user"];
        $this->db->update2ListWKey("Token", [":Account"=>$user,":AlexaAccessToken"=>"",":AlexaReflashToken"=>""], "Account");
    }

    public function addOrUpdateReport($mac, $location)
    {
        global $gApp;
        $user = $gApp["user"];
        $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
        if ($tokens["AlexaAccessToken"]) {
            $res = $this->addOrUpdateReportControl($tokens, $mac, $location);
            if ($res == 401) {
                $tokens = $this->db->queryAllList("Token", ["equation"=>[":Account"=>$user]])[0];
                $this->addOrUpdateReportControl($tokens, $mac, $location);
            }
        }
    }

    public function addOrUpdateReportControl($tokens, $mac, $location)
    {
        $url = "https://api.amazonalexa.com/v3/events";
        $data = [
            "event"=>[
                "header"=>[
                    "namespace"=>"Alexa.Discovery",
                    "name"=>"Discover.Response",
                    "payloadVersion"=>"3",
                    "messageId"=>"3a98205a-a59a-4ee9-8e44-aa2947c05e91"
                ],
                "payload"=>[
                    "endpoints"=>[
                        [
                            "endpointId"=>$mac,
                            "manufacturerName"=>"Akuvox Company",
                            "friendlyName"=>$location,
                            "description"=>"Security Panel connected via Akuvox",
                            "displayCategories"=>["SECURITY_PANEL"],
                            "cookie"=>[
                                "key1"=>"but they should only be used for reference purposes.",
                                "key2"=>"This is not a suitable place to maintain current endpoint state."
                            ],
                            "capabilities"=>[
                                [
                                    "type"=>"AlexaInterface",
                                    "interface"=>"Alexa",
                                    "version"=>"3"
                                ],
                                [
                                    "type"=>"AlexaInterface",
                                    "interface"=>"Alexa.EndpointHealth",
                                    "version"=>"3",
                                    "properties"=>[
                                        "supported"=>[
                                            [
                                                "name"=>"connectivity"
                                            ]
                                        ],
                                        "proactivelyReported"=>true,
                                        "retrievable"=>true
                                    ]
                                ],
                                [
                                    "interface"=>"Alexa.SecurityPanelController",
                                    "version"=>"3",
                                    "type"=>"AlexaInterface",
                                    "properties"=>[
                                        "supported"=>[
                                            ["name"=>"armState"],
                                            ["name"=>"burglaryAlarm"]
                                        ],
                                        "proactivelyReported"=>true,
                                        "retrievable"=>true
                                    ],
                                    "configuration"=>[
                                        "supportsArmInstant"=>true,
                                        "supportedArmStates"=>[
                                            ["value"=>"ARMED_AWAY"],
                                            ["value"=>"ARMED_STAY"],
                                            ["value"=>"ARMED_NIGHT"],
                                            ["value"=>"DISARMED"],
                                        ]
                                    ]
                                ]
                            ],

                        ]
                    ],
                    "scope"=>[
                        "type"=>"BearerToken",
                        "token"=>$tokens["AlexaAccessToken"]
                    ]
                ]
            ],
        ];
        $token = ["Authorization: Bearer ".$tokens["AlexaAccessToken"],"Content-Type: application/json"];
        return $this->proxyPost($url, json_encode($data), $token);
    }

    public function proxyGet($url, $token=[])
    {
        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $token);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //执行命令
        $data = curl_exec($curl);
        //关闭URL请求
        curl_close($curl);
        //显示获得的数据
        $pdata = json_decode($data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        return $pdata;
    }

    public function proxyPost($url, $postData, $token=[])
    {
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出 0不输出header 1输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $token);
        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        //超时，只需要设置一个秒的数量就可以
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
        //执行命令
        $data = curl_exec($curl);
    
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        // if($httpCode == 401 || $httpCode == 403) return $httpCode;
        //关闭URL请求
        curl_close($curl);
        if ($httpCode == 401) {
            $res = $this->refresh();
            if ($res) {
                return 401;
            } else {
                return false;
            }
        }
        if ($httpCode == 403) {
            $this->clearToken();
            return 403;
        }

        if (!in_array($httpCode, ["200","202"])) {
            return false;
        }
        //显示获得的数据
        $pdata = json_decode($data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        return $pdata;
    }
}
