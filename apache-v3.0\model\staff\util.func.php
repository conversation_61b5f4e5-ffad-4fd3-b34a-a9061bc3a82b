<?php

namespace model\staff;

require_once __DIR__ . "/../access/util.func.php";

const TABLES = ['Delivery', 'Staff'];
const ACCESS_TABLES = ['DeliveryAccess', 'StaffAccess'];
const ID_KEYS = ['DeliveryID', 'StaffID'];
// 梯控控制全部楼层值为129
const LADDER_CONTROL_ALL_FLOOR = 129;
// 楼层可选范围
const LADDER_CONTROL_MAX_FLOOR = 128;
const LADDER_CONTROL_MIN_FLOOR = 1;
// 楼层最大可选数目
const LADDER_CONTROL_FLOOR_NUM = 10;
// Staff/Delivery可达楼层管理表
const LADDER_CONTROL_TABLES = ['DeliveryLadderControlList', 'StaffLadderControlList'];
const UUID_KEYS = ['DeliveryUUID', 'StaffUUID'];
function checkValid($id, $communityId, $type)
{
    global $db, $cMessage;
    $bindArray = [":CommunityID" => $communityId, ":ID" => $id];
    $count = $db->querySList('select count(*) from ' . TABLES[$type] . ' where CommunityID = :CommunityID and ID = :ID', $bindArray)[0]['count(*)'];
    if ($count == 0) {
        $cMessage->echoErrorMsg(StateNotPermission);
    }
}

function checkAccessValid($ids, $communityId)
{
    foreach ($ids as $id) {
        \model\access\checkValid($id, $communityId);
    }
}

/**
 * @param $type 0:delivery 1 :staff 2:resident 3:office
 */
function checkPinCardUnique($pin, $communityId, $type, $codeType, $id = null, $stay = false)
{
    global $db, $cMessage;
    if ($pin != null) {
        $whereDelivery = "";
        $whereStaff = "";
        $wherePerson = "";
        $bindArray = [":Code" => $pin, ":CommunityID" => $communityId];
        $bindArrayDelivery = $bindArray;
        $bindArrayStaff = $bindArray;
        $bindArrayPerson = $bindArray;
        if ($id) {
            $where = " and ID != :ID";
            $bindArray[":ID"] = $id;
        }
        if (isDelivery($type)) {
            $whereDelivery = $where;
            $bindArrayDelivery = $bindArray;
        } elseif (isStaff($type)) {
            $whereStaff = $where;
            $bindArrayStaff = $bindArray;
        } else {
            $wherePerson = $where;
            $bindArrayPerson = $bindArray;
        }
        if ($codeType == 'PIN') {
            $countDelivery = $db->querySList("select count(*) as total from Delivery where PinCode = :Code and CommunityID = :CommunityID $whereDelivery", $bindArrayDelivery)[0]['total'];
            $countPerson = $db->querySList("select count(*) as total from CommPerPrivateKey where Code = :Code and CommunityID = :CommunityID $wherePerson", $bindArrayPerson)[0]['total'];
            if (($countDelivery + $countPerson) != 0 && !$stay) {
                $cMessage->echoErrorMsg(StatePrivateKeyExists);
            } elseif (($countDelivery + $countPerson) != 0 && $stay) {
                return true;
            }
        } else {
            $countDelivery = $db->querySList("select count(*) as total from Delivery where CardCode = :Code and CommunityID = :CommunityID $whereDelivery", $bindArrayDelivery)[0]['total'];
            $countStaff = $db->querySList("select count(*) as total from Staff where CardCode = :Code and CommunityID = :CommunityID $whereStaff", $bindArrayStaff)[0]['total'];

            $countPerson = $db->querySList("select count(*) as total from CommPerRfKey where Code = :Code and CommunityID = :CommunityID $wherePerson", $bindArrayPerson)[0]['total'];
            
            if (($countDelivery + $countPerson + $countStaff) != 0 && !$stay) {
                $cMessage->echoErrorMsg(StateRFCardExit);
            } elseif (($countDelivery + $countPerson + $countStaff) != 0 && $stay) {
                return true;
            }
        }
    }
}

function isDelivery($type)
{
    return $type == 0;
}

function isStaff($type)
{
    return $type == 1;
}

function isOfficePeople($type)
{
    return $type == 3;
}

function getGroup($id, $type)
{
    global $db;
    $groups = $db->querySList('select T.AccessGroupID from ' . ACCESS_TABLES[$type] . ' T right join AccessGroup A 
    on T.AccessGroupID = A.ID where T.' . ID_KEYS[$type] . ' = :ID', [':ID' => $id]);
    foreach ($groups as &$val) {
        $val = $val['AccessGroupID'];
    }
    unset($val);

    return $groups;
}

function getList($param, $type)
{
    global $db;

    $communityId = $param["CommunityID"];
    $searchKey = $param["SearchKey"];
    $searchValue = $param["SearchValue"];
    $timeZone = $param["SelfTimeZone"];
    $customizeForm = $param["SelfCustomizeForm"];
    $row = $param["Row"];
    $offset = $param["Offset"];

    $where = "";
    $bindArray = [":CommunityID" => $communityId];
    switch ($searchKey) {
        case 'Name':
            $where = ' and Name like :Name';
            $bindArray[':Name'] = "%$searchValue%";
            break;
        case 'Pin':
            if ($searchValue != '') {
                $where = ' and PinCode like :PinCode';
                $bindArray[':PinCode'] = "%$searchValue%";
            }
            break;
        case 'Card':
            if ($searchValue != '') {
                $where = ' and CardCode like :CardCode';
                $bindArray[':CardCode'] = "%$searchValue%";
            }
            break;
    }
    // 区别社区和办公
    $projectGrade = $db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];

    $type = intval($type);
    $count = $db->querySList('select count(*) from ' . TABLES[$type] . ' where CommunityID = :CommunityID ' . $where, $bindArray)[0]['count(*)'];
    $data = $db->querySList("select * from " . TABLES[$type] . " where CommunityID = :CommunityID $where order by ID desc limit $offset,$row", $bindArray);

    $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

    if ($type === 1) {
        foreach ($data as &$row) {
            $row['PinCode'] = \util\computed\setPinIsEncryptPin($communityId, $row['PinCode']);
            $row['HaveFace'] = $row['FaceUrl'] === '' ? false: true;
        }
    } else {
        // 判断是否加密
        foreach ($data as &$row) {
            $row['PinCode'] = \util\computed\setPinIsEncryptPin($communityId, $row['PinCode']);
        }
    }
    unset($row);
    foreach ($data as &$value) {
        $access = $db->querySList('select AG.Name, AG.UnitID from AccessGroup AG join ' . ACCESS_TABLES[$type] . ' T on AG.ID = T.AccessGroupID  where T.' . ID_KEYS[$type] . ' = :ID', [":ID" => $value["ID"]]);
        foreach ($access as &$val) {
            if ($val['UnitID'] == 0) {
                $val = $val['Name'];
            } else {
                $unitName = $db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$val['UnitID']])[0]['UnitName'];
                if ($projectGrade == COMMUNITYGRADE) {
                    $val = vsprintf(MSGTEXT['BuildAccessName'], [$unitName]);
                } elseif ($projectGrade == OFFICEGRADE) {
                    $val = vsprintf(MSGTEXT['departmentAccessName'], [$unitName]);
                }
            }
        }
        unset($val);
        $value['AccessGroupName'] = implode(';', $access);
        $value["ID"] = intval($value["ID"]);
        $ladderControlData = $db->querySList('select UnitID,Floor from '. LADDER_CONTROL_TABLES[$type]. ' where '. UUID_KEYS[$type] .'= :UUID', [':UUID' => $value['UUID']]);
        $value['AccessibleFloors'] = '';
        $buildCount = $db->querySList('select count(*) from CommunityUnit where MngAccountID = :MngAccountID', [":MngAccountID"=>$communityId])[0]['count(*)'];
        foreach ($ladderControlData as $ladderControl) {
            if (intval($ladderControl['Floor']) === LADDER_CONTROL_ALL_FLOOR) {
                $floor = MSGTEXT['allFloors'];
            } else {
                $floor = str_replace(';', ', ', $ladderControl['Floor']);
            }
            // UnitID为0表示All Build的意思,或者只有一个楼栋，即只展示Floor
            if ($ladderControl['UnitID'] === '0' || $buildCount === '1') {
                $value['AccessibleFloors'] = $floor;
            } else {
                $value['AccessibleFloors'] .= $db->querySList('select UnitName from CommunityUnit where ID = :ID', [':ID'=>$ladderControl['UnitID']])[0]['UnitName'].'-'.$floor.'; ';
            }
        }
        $value['AccessibleFloors'] = rtrim($value['AccessibleFloors'], '; ');
    }
    unset($value);

    return [$data, $count];
}

function getInfo($id, $type)
{
    global $db;
    $data = $db->querySList('select * from ' . TABLES[$type] . ' where ID = :ID', [":ID" => $id])[0];
    $data['AccessGroup'] = getGroup($id, $type);
    $ladderControlData = $db->querySList('select UnitID,Floor from '.LADDER_CONTROL_TABLES[$type].' where '.UUID_KEYS[$type] .'= :UUID', [':UUID' => $data['UUID']]);
    $data['Build'] = [];
    $data['Floor'] = [];
    foreach ($ladderControlData as $ladderControl) {
        if ($ladderControl['UnitID'] === '0') {
            $ladderControl['UnitID'] = 'all';
        }
        if (intval($ladderControl['Floor']) === LADDER_CONTROL_ALL_FLOOR) {
            $ladderControl['Floor'] = 'all';
        }
        if ($ladderControl['Floor'] !== '') {
            array_push($data['Build'], $ladderControl['UnitID']);
            array_push($data['Floor'], $ladderControl['Floor']);
        }
    }
    if ($type === 1) {
        $data['HaveFace'] = $data['FaceUrl'] === '' ? false: true;
    }
    return $data;
}

function delete($id, $type)
{
    global $db;
    $uuid = $db->querySList('select UUID from '. TABLES[$type]. ' where ID = :ID', [':ID' => $id])[0]['UUID'];
    $db->delete2ListWID(TABLES[$type], $id);
    $db->delete2ListWKey(ACCESS_TABLES[$type], ID_KEYS[$type], $id);
    $db->delete2ListWKey(LADDER_CONTROL_TABLES[$type], UUID_KEYS[$type], $uuid);
}

function add($params, $communityId, $type)
{
    global $db;
    $name = $params["Name"];
    $pin = $params["PinCode"];
    $card = $params["CardCode"];
    $access = $params["AccessGroup"];
    // V6.5.2 只有新小区才有楼栋可控功能 mod:V6.5.3办公新增提供功能
    $grade = $db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];
    $isNew = $db->querySList('select IsNew from CommunityInfo where AccountID = :AccountID', [':AccountID' => $communityId])[0]['IsNew'];
    if ($isNew === '1') {
        // 检测输入楼栋和楼层的正确性
        $buildFloorData = checkBuildFloorValid($params['Build'], $params['Floor'], $communityId);
    } elseif (intval($grade) === OFFICEGRADE) {
        $buildFloorData = checkBuildFloorValid(['all'], $params['Floor'], $communityId);
    } else {
        $buildFloorData = [];
    }
    $uuid = \util\string\uuid();

    $bindArray = [
        ":Name" => $name,
        ":CardCode" => $card,
        ":CommunityID" => $communityId,
        ":CreateTime" => \util\computed\getNow(),
        ':UUID' => $uuid
    ];

    if (isDelivery($type)) {
        $bindArray[":PinCode"] = $pin;
    }

    $db->insert2List(TABLES[$type], $bindArray);
    $id = $db->lastInsertId();

    foreach ($access as $val) {
        $db->insert2List(ACCESS_TABLES[$type], [
            ":AccessGroupID" => $val,
            ":" . ID_KEYS[$type] => $id
        ]);
    }

    foreach ($buildFloorData as $build => $floor) {
        $db->insert2List(LADDER_CONTROL_TABLES[$type], [
            ':'.UUID_KEYS[$type] => $uuid,
            ':UnitID' => $build,
            ':Floor' => $floor
        ]);
    }

    return $id;
}

function update($params, $communityId, $type)
{
    global $db;
    $name = $params["Name"];
    $pin = $params["PinCode"];
    $card = $params["CardCode"];
    $access = $params["AccessGroup"];
    $id = $params["ID"];
    
    // V6.5.2 只有新小区才有楼栋可控功能 mod:V6.5.3办公新增提供功能
    $grade = $db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];
    $isNew = $db->querySList('select IsNew from CommunityInfo where AccountID = :AccountID', [':AccountID' => $communityId])[0]['IsNew'];
    if ($isNew === '1') {
        // 检测输入楼栋和楼层的正确性
        $buildFloorData = checkBuildFloorValid($params['Build'], $params['Floor'], $communityId);
    } elseif (intval($grade) === OFFICEGRADE) {
        $buildFloorData = checkBuildFloorValid(['all'], $params['Floor'], $communityId);
    } else {
        $buildFloorData = [];
    }
    $bindArray = [
        ":ID" => $id,
        ":Name" => $name,
        ":CardCode" => $card,
        ":CommunityID" => $communityId,
    ];

    if (isDelivery($type) && $pin !== '****') {
        $bindArray[":PinCode"] = $pin;
    }

    $uuid = $db->querySList('select UUID from '. TABLES[$type]. ' where ID = :ID', [':ID' => $id])[0]['UUID'];
    $db->update2ListWID(TABLES[$type], $bindArray);
    $db->delete2ListWKey(ACCESS_TABLES[$type], ID_KEYS[$type], $id);
    $db->delete2ListWKey(LADDER_CONTROL_TABLES[$type], UUID_KEYS[$type], $uuid);

    foreach ($access as $val) {
        $db->insert2List(ACCESS_TABLES[$type], [
            ":AccessGroupID" => $val,
            ":" . ID_KEYS[$type] => $id
        ]);
    }
    
    foreach ($buildFloorData as $build => $floor) {
        $db->insert2List(LADDER_CONTROL_TABLES[$type], [
            ':'.UUID_KEYS[$type] => $uuid,
            ':UnitID' => $build,
            ':Floor' => $floor
        ]);
    }
}

/**
 * @description: 检测staff/Delivery 管理Floor的正确性
 * @author: cj 2022-12-6 16:47:05 V6.5.2
 * @LastEditor: cj 2022-12-6 16:47:05 V6.5.2
 * @param {*} $floor 楼层，用分号拼接，设备端最多支持10个
 * @return {*}
 */
function checkFloorValid($floor)
{
    global $cMessage;
    $allFloor = explode(';', $floor);
    if (array_search('all', $allFloor) !== false) {
        $floor = LADDER_CONTROL_ALL_FLOOR;
    } elseif ($floor !== '') {
        if (count($allFloor) > LADDER_CONTROL_FLOOR_NUM) {
            $cMessage->echoErrorMsg(StateNotPermission);
        } else {
            foreach ($allFloor as $val) {
                if ($val < LADDER_CONTROL_MIN_FLOOR || $val > LADDER_CONTROL_MAX_FLOOR) {
                    $cMessage->echoErrorMsg(StateNotPermission);
                }
            }
        }
    }
    return $floor;
}

/**
 * @description: 检测staff/Delivery 管理Build的正确性
 * @author: cj 2022-12-6 17:13:12 V6.5.2
 * @LastEditor: cj 2022-12-6 17:13:12 V6.5.2
 * @param {*} $builds 楼栋ID数组，value为单个BuildID
 * @param {*} $floors 楼层ID数组，value为多个Floor用分号拼接
 * @param {*} $communityId 社区ID
 * @return {array} data形如['BuildID' => 'Floor'] BuildID为0表示所有楼栋
 */
function checkBuildFloorValid($builds, $floors, $communityId)
{
    global $db, $cMessage;
    $isAllBuild = array_search('all', $builds);
    $data = [];
    if ($isAllBuild !== false) {
        // 勾选all
        $floor = checkFloorValid($floors[$isAllBuild]);
        if ($floor !== '') {
            $data = [0 => $floor];
        }
        return $data;
    } else {
        // 无勾选全部楼栋的情况，判断对应楼栋是否在对应小区下
        foreach ($builds as $key => $buildId) {
            if ($buildId !== '') {
                $count = $db->querySList('select count(*) from CommunityUnit where ID = :ID and MngAccountID =:MngAccountID', [':ID' => $buildId, ':MngAccountID' => $communityId])[0]['count(*)'];
                if ($count === '0') {
                    $cMessage->echoErrorMsg(StateNotPermission);
                }
                $floor = checkFloorValid($floors[$key]);
                if ($floor !== '') {
                    $data[$buildId] = $floor;
                }
            }
        }
    }
    return $data;
}
