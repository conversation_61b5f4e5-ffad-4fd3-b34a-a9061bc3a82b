<?php
namespace plan\process;

const ORDER_PROCESS = [
    "queryPerSubPay"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPerUserSubPay"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["addAppFee"=>"data->addAppFee","money"=>"data->addAppFee", "addAppSaveFee"=>"data->addAppSaveFee", 'introduction'=>"data->introduction"]
        ],
    ],
    "queryComSubPay"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryComUserSubPay"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["addAppFee"=>"data->addAppFee","money"=>"data->addAppFee", "addAppSaveFee"=>"data->addAppSaveFee", 'introduction'=>"data->introduction"]
        ],
    ],
    "queryPerCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPersonal"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "queryPerMonthAppFee"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"changeParamValue","params"=>["Type"=>PAYSUBDRCIPTION]],
                ["name"=>"changeParamName","params"=>["userAliasId"=>"ID"]],
                ["name"=>"setMainToPCMngAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPersonal"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["amount"=>"data->MonthlyFee"]
        ],
    ],
    "queryPerAddAppFee"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"changeParamValue","params"=>["Type"=>PAYADDAPP]]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPersonal"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["addAppFee"=>"data->AddAppFee","money"=>"data->AddAppFee", "addAppSaveFee"=>"data->AddAppSaveFee", 'introduction'=>"data->Introduction"]
        ],
    ],
    "queryPerChargeSup"=>[
        [
            "type"=>"model",
            "model"=>"chargePlan.queryPersonal"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "queryComCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryCommunity",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "queryComMonthAppFee"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"changeParamValue","params"=>["Type"=>PAYSUBDRCIPTION]],
                ["name"=>"changeParamName","params"=>["userAliasId"=>"ID"]],
                ["name"=>"setMainToPCMngAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryCommunity"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["amount"=>"data->MonthlyFee"]
        ],
    ],
    "queryComAddAppFee"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"changeParamValue","params"=>["Type"=>PAYADDAPP]]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryCommunity"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["addAppFee"=>"data->AddAppFee","money"=>"data->AddAppFee","addAppSaveFee"=>"data->AddAppSaveFee", 'introduction'=>"data->Introduction"]
        ],
    ],
    "getUserOrderList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserTimeZone"],
                ["name"=>"getUserId"],
                ["name"=>"setUserPayType"],
                // ["name"=>"setUserOrderType"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getOrderList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "getMngOrderList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getOrderList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "getSupOrderList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getOrderList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "exportOrder"=>[
        [
            "type"=>"model",
            "model"=>"order.export"
        ]
    ],
    "getOrderInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.checkOrderExit"
        ],[
            "type"=>"model",
            "model"=>"order.getOrderInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "getSupOrderInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getOrderInfo"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    // "setOrderDiscount"=>[
    //     [
    //         "type"=>"model",
    //         "model"=>"order.setDiscount"
    //     ],[
    //         "type"=>"echo",
    //         "code"=>StateSetSuccess
    //     ],
    // ],
    "getPerMonthlyPay"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPersonal"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComMonthlyPay"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryCommunity"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "deleteUserOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.deleteOrder"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "createUserOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"order.createUserOrder"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ]
    ],
    "createInstallOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"order.createInstallerOrder"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ]
    ],
    "getPMReceipt"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"propertyData.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"order.getReceiptPdf"
        ]
    ],
    "getUserReceipt"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"userData.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"order.getReceiptPdf"
        ]
    ],
    "getInstallReceipt"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getUserId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getReceipt"
        ],[
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPCMngToInstaller"],
                ["name"=>"getAliasId"],
                ["name"=>"changeParamName","params"=>["userAlias"=>"user","userAliasId"=>"userId"]],
            ]
        ],[
            "type"=>"model",
            "model"=>"manageData.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"order.getReceiptPdf"
        ]
    ],
    "createdCommunityOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"getUserId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getChargePlan"
        ],[
            "type"=>"model",
            "model"=>"order.createdCommunityOrder"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ]
    ],
    "getOrderListForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"],
                ["name"=>"setPMAlias"],
                ["name"=>"setPMPayType"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getOrderList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    
    "getSysSet"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.querySysSet"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryManageCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryManage"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "setSupCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.setSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "setManageCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.setManage"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "createAreaInstallOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"order.createAreaInstallOrder"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ],
    ],
    "createAreaCommunityOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"order.createAreaCommunityOrder"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ],
    ],
    "createAreaOfficeOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"order.createAreaOfficeOrder"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["orderID"=>"orderID","bmurl"=>"bmurl"]
        ],
    ],
    "getAreaReceipt"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"order.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"manageData.getReceipt"
        ],[
            "type"=>"model",
            "model"=>"order.getReceiptPdf"
        ]
    ],
    "getDisCharge"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                // ["name"=>"manageInAMngCheck"]
            ]
        ],[
            "type"=>"event",
            "event"=>"getPCBranch",
            "action"=>"on"
        ],[
            "type"=>"event",
            "event"=>"getPCBranch",
            "params"=>["Manage"],
            "action"=>"emit"
        ],[
            "type"=>"branches",
            "branches"=>[
                "getPerBranch"=>[
                    [
                        "type"=>"model",
                        "model"=>"chargePlan.queryPersonal",
                        "dataContainer"=>"setManageToAliasId"
                    ]
                ],
                "getComBranch"=>[
                    [
                        "type"=>"model",
                        "model"=>"chargePlan.queryCommunity",
                        "dataContainer"=>"setManageToAliasId"
                    ]
                ]
            ]
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getStripePubKey"=>[
        [
            "type"=>"model",
            "model"=>"chargePlan.queryStripeKey"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["PubKey"=>"PubKey"]
        ]
    ],
    "queryComSubPayApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryComUserSubPay"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "queryPerSubPayApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.queryPerUserSubPay"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "captureOrder"=>[
        [
            "type"=>"model",
            "model"=>"order.capture"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "userBillingInfo" => [
        [
            "type" => "model",
            "model" => "chargePlan.userBillingInfo"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "createDifferenceOrder"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"order.createDifferenceOrder"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data" => "data"]
        ]
    ]
];
