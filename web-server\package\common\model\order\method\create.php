<?php
/**
 * @description 创建订单
 * <AUTHOR>
 * @date 2022-03-30 14:55:27
 * @version V6.4
 * @lastEditor cj
 * @lastEditTime 2024-05-25 15:48:45
 * @lastVersion V6.7.3
 */

namespace package\common\model\order\method;

use DateTime;
use package\common\model\order\config\Code;
use function util\string\uuid;

trait Create
{
    /*
     *@description 激活用户，可以是主账户，从账户，pmApp的混合激活,禁止跨类型混合激活，例如社区和办公一起激活
     *<AUTHOR> 2022-03-30 15:52:59 V6.4
     *@lastEditor kxl 2022-03-30 15:52:59 V6.4
     *@param {array} Users 需要激活的用户,参数是账号ID的数组
     *@param {number} PayerId 购买人的ID
     *@param {number} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
     *@param {string} ProjectType enum('multiple','single','office')
     *@param {number} PayType 单独购买从账户，传值 PAY_ADD_APP，激活可省略
     *@return
     */
    public function createActiveUser()
    {
        $params = ['Users', 'PayerId', 'PayerType', 'ProjectType', 'PayType'];
        list($users, $payerId, $payerType, $projectType, $payType) = $this->getParams($params);

        $users = json_decode($users, true);

        $payerType = intval($payerType);
        $payType = $payType === PAY_ADD_APP ? PAY_ADD_APP : PAY_ACTIVE;

        $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
        if ($payType === PAY_ADD_APP && (count($usersInfo['main']) !== 0 || count($usersInfo['pm']) !== 0)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PAY_ADD_APP_FAIL]);
        }

        // 检查支付权限
        $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
        $this->loadUtil('billingInfo');
        $this->loadUtil('account');
        $accounts = $this->utils->account->changeIDToAccount($users);

        // 检查锁订单
        $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['active']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = 1;
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            $payType,
            $count,
            ['Users' => $usersInfo]
        );

        $isBatch = 1;
        /* 单个项目直接使用项目ID作为installerId，因为单个项目入口可能是社区/办公内部入口，也可能是批量的入口
         因此，直接使用这个项目id作为insId字段，payerId可以直接使用任意ins下的社区/办公Id，因为目前只有一个订单入口
         不需要区分是否从批量入口支付单个项目还是直接从单个社区里面支付
        */
        if (1 === count($projectIds)) {
            $installerId = $projectIds[0];
            $isBatch = 0;
        } else {
            // 多个项目时记录installerId
            $installerId = $this->utils->account->getManagerInfo($projectIds[0])['ManageGroup'];
            if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                $payerId = $installerId;
            }
        }

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => $payType,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'isBatch' => $isBatch,
            'bmOrderNumber' => $result['Number'],
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'usersInfo' => $usersInfo['all'],
            'projectType' => $projectType
        ]);

        // 锁订单,额外从账户的也使用active，后面createBuyApp也需要使用active
        $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['active']);
        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createRentManagerRenewByMonth()
    {
        //rentManager订单都是INS按月付费
        $params = ['Count', 'RentManagerCustomerList', 'PayerId', 'PayerType', 'DisId', 'InsUUID', 'DisUUID', 'TotalPrice'];
        list($count, $rentManagerCustomerList, $payerId, $payerType, $disId, $insUUID, $disUUID, $totalPrice) = $this->getParams($params);

        $this->loadUtil('rentManager', true);
        $this->loadUtil('account', true);
        $this->loadProvider('billsysUtil');
        $this->loadUtil('billingInfo');

        $monthlyFee = $this->utils->_common->rentManager->getRentManagerMonthlyFee($disUUID, $insUUID);
        $targetTotalPrice = $this->utils->_common->rentManager->computedPriceForRentManager($rentManagerCustomerList, RENT_MANAGER_COMPUTED_ORDER_TOTAL_PRICE_TYPE, $count, $disUUID, $insUUID);

        //校验总金额
        if ($this->share->util->inputComputedCount($targetTotalPrice) != $this->share->util->inputComputedCount($totalPrice)) {
            $this->output->echoErrorMsg(STATE_PARAMS_ERROR, ['externalErrorObj' => Code::EXT_STATE_PRICE_ERROR]);
        }

        // 获取对应Dis和Ins的Account
        $insAccount = $this->utils->_common->account->accountSelectByKey('ID', $payerId, 'Account')[0]['Account'];
        // 获取RentManager信息
        $rentManagerCustomerInfos = $this->utils->_common->rentManager->getRentManagerCustomerByArray([['UUID', $rentManagerCustomerList]]);

        $data = $this->utils->billingInfo->isLockPayData($rentManagerCustomerList, PAY_TYPE['rentManagerIntegration']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }

        //调计费系统创建订单
        $result = $this->services->billsysUtil->createRentManager($totalPrice, $insAccount);
        // 创建云平台订单
        $data = [
            'PayerId' => $payerId, //INS 的 id
            'Token' => $result['Token'],
            'TotalPrice' => $this->share->util->inputComputedCount($totalPrice),
            'Payer' => $insAccount,
            'PayerType' => $payerType,
            'AreaManageID' => $disId,
            'Months' => $count,
            'BmOrderNumber' => $result['Number'],
            'IsBatch' => 1,
            'PayCode' => $result['Code'],
            'SubscriptionUUID' => "",
            'PaypalOrder' => "",
            'RentManagerCustomerInfoList' => $rentManagerCustomerInfos,
            'MonthlyFee' => $monthlyFee,
            'Status' => ORDER_STATUS_TYPE['paying']
        ];
        list($orderId, $orderNumber) = $this->createRentManagerOrder($data);
        $this->utils->billingInfo->lockPayData($orderNumber, $rentManagerCustomerList, PAY_TYPE['rentManagerIntegration']);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createRentManagerOrder($data)
    {
        if (!array_key_exists('PayerId', $data)
            || !array_key_exists('Token', $data)
            || !array_key_exists('TotalPrice', $data)
            || !array_key_exists('Payer', $data)
            || !array_key_exists('PayerType', $data)
            || !array_key_exists('AreaManageID', $data)
            || !array_key_exists('Months', $data)
            || !array_key_exists('BmOrderNumber', $data)
            || !array_key_exists('IsBatch', $data)
            || !array_key_exists('SubscriptionUUID', $data)
            || !array_key_exists('PaypalOrder', $data)
            || !array_key_exists('RentManagerCustomerInfoList', $data)
            || !array_key_exists('Status', $data)
            || !array_key_exists('PayCode', $data)) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception('createRentManagerOrder params is error');
        }

        $this->log->debug('createRentManagerOrderParams:data={data}', ['data' => $data]);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $subscriptionUUID = empty($data['SubscriptionUUID']) ? "" : $data['SubscriptionUUID'];
        $paypalOrder = empty($data['PaypalOrder']) ? "" : $data['PaypalOrder'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['rentManagerIntegration']);
        $orderParam = [
            ':OrderNumber' => $orderNumber,
            ':AccountID' => $data['PayerId'],
            ':WebHookToken' => $data['Token'],
            ':CreateTime' => $this->share->util->getNow(),
            ':TotalPrice' => $data['TotalPrice'],
            ':Type' => PAY_TYPE['rentManagerIntegration'],
            ':Payer' => $data['Payer'],
            ':PayerType' => $data['PayerType'],
            ':InstallID' => $data['PayerId'],
            ':AreaManageID' => $data['AreaManageID'],
            ':FinalPrice' => $data['TotalPrice'],
            ':Months' => $data['Months'],
            ':BmOrderNumber' => $data['BmOrderNumber'],
            ':IsBatch' => $data['IsBatch'],
            ':PayCode' => $data['PayCode'],
            ':NextTime' => '0000-00-00 00:00:00',
            ':SubscriptionUUID' => $subscriptionUUID,
            ':PaypalOrder' => $paypalOrder,
            ':Status' => $data['Status'],
            ':Days' => 0, // 因为是按月付费，所以days为0,
            ':MixType' => $mixType,
            ':ProjectType' => 2,
            ':UUID' => \share\util\uuid(),
        ];

        $this->db->insert2List(PROXY_TABLES['orderList'], $orderParam);
        $orderId = $this->db->lastInsertId();

        // 插入 RentManagerOrderList
        $rentManagerCustomerInfos = $data['RentManagerCustomerInfoList'];

        foreach ($rentManagerCustomerInfos as $customerInfo) {
            $insertData = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'RentManagerCustomerUUID' => $customerInfo['UUID'],
                'MonthlyFee' => $this->share->util->inputComputedCount($data['MonthlyFee']),
                'ChargeData' => json_encode($customerInfo)
            ];
            $this->dao->rentManagerOrderList->insert($insertData);
        }

        return [$orderId, $orderNumber];
    }

    /*
     *@description 创建按月续费的订单
     *<AUTHOR> 2022-04-07 11:17:59 V6.4
     *@lastEditor kxl 2022-04-07 11:17:59 V6.4
     *@param {*} Users 需要激活的用户,参数是主账号ID的数组，包含PM APP
     *@param {*} PayerId 购买人的ID
     *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
     *@param {*} Type enum('multiple','single','office')
     *@param {*} Payer 终端用户是支付人邮箱，管理员是账号
     *@param {*} Count 续费月份数
     *@param {*} InstallerId
     *@return
     */
    public function createRenewByMonth()
    {
        $params = ['Users', 'PayerId', 'PayerType', 'ProjectType', 'Count'];
        list($users, $payerId, $payerType, $projectType, $count) = $this->getParams($params);
        $users = json_decode($users, true);
        $payerType = intval($payerType);

        $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
        // 混合支付检查支付权限
        $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);

        $this->loadUtil('account');

        $isBatch = 1;
        /* 单个项目直接使用项目ID作为installerId，因为单个项目入口可能是社区/办公内部入口，也可能是批量的入口
         因此，直接使用这个项目id作为insId字段，payerId可以直接使用任意ins下的社区/办公Id，因为目前只有一个订单入口
         不需要区分是否从批量入口支付单个项目还是直接从单个社区里面支付
        */
        if (1 === count($projectIds)) {
            $installerId = $projectIds[0];
            $isBatch = 0;
        } else {
            // 多个项目时记录installerId
            $installerId = $this->utils->account->getManagerInfo($projectIds[0])['ManageGroup'];
            if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                $payerId = $installerId;
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_SUBSCRIPTION,
            $count,
            ['Users' => $usersInfo]
        );
        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_SUBSCRIPTION,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'isBatch' => $isBatch,
            'bmOrderNumber' => $result['Number'],
            'Month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'usersInfo' => $usersInfo['all'],
            'projectType' => $projectType
        ]);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /*
     *@description 按天续费
     *<AUTHOR> 2022-04-07 17:36:14 V6.4
     *@lastEditor kxl 2022-04-07 17:36:14 V6.4
     *@param {*} Users 需要激活的用户,参数是主账号ID的数组，包含PM APP
     *@param {*} PayerId 购买人的ID
     *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
     *@param {*} ProjectType enum('multiple','single','office')
     *@param {*} Payer 终端用户是支付人邮箱，管理员是账号
     *@param {*} NextTime 下次到期时间
     *@param {*} InstallerId
     *@return
     */
    public function createRenewByDay()
    {
        // 按天续费需要锁住续费人员
        $params = ['Users', 'PayerId', 'PayerType', 'ProjectType', 'NextTime'];
        list($users, $payerId, $payerType, $projectType, $nextTime) = $this->getParams($params);
        $users = json_decode($users, true);
        $payerType = intval($payerType);

        $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
        // 混合支付检查支付权限
        $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
        // 检查在同一个时区
        $this->callSelfFunc('checkProjectInSameTimeZone', [$projectIds]);

        $this->loadUtil('account', true);
        //验证所有用户过期时间是否都大于下次时间
        foreach ($usersInfo['all'] as $user) {
            if (intval($user['Role']) === PERENDMROLE) {
                // 单住户时区看自己
                $timeZone = $user['TimeZone'];
            } else {
                // 其他时区看项目
                $project = $this->utils->_common->account->getManagerInfo($user['ProjectId']);
                $timeZone = $project['TimeZone'];
            }
            $expireTime = $user['ExpireTime'];
            // 把expiretime转换为客户时区进行计算,因为按天只算年月日,这样才准确.nextTime本来就是按客户时区,不用转换
            $expireTime = \share\util\setTimeZone($expireTime, $timeZone, 3);
            if (strtotime($nextTime) <= strtotime($expireTime)) {
                $this->output->echoErrorMsg(STATE_PAY_NEXTTIME_INVALID);
            }
        }


        $this->loadUtil('billingInfo');
        $this->loadUtil('account');
        $accounts = $this->utils->account->changeIDToAccount($users);
        // 检查锁订单
        $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['renewToDay']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }

        $isBatch = 1;
        $projectData = $this->utils->account->getManagerInfo($projectIds[0]);
        /* 单个项目直接使用项目ID作为installerId，因为单个项目入口可能是社区/办公内部入口，也可能是批量的入口
         因此，直接使用这个项目id作为insId字段，payerId可以直接使用任意ins下的社区/办公Id，因为目前只有一个订单入口
         不需要区分是否从批量入口支付单个项目还是直接从单个社区里面支付
        */
        if (1 === count($projectIds)) {
            $installerId = $projectIds[0];
            $isBatch = 0;
        } else {
            // 多个项目时记录installerId
            $installerId = $projectData['ManageGroup'];
            if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                $payerId = $installerId;
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = 1;
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_SUBSCRIPTION_BY_DAY,
            $count,
            ['Users' => $usersInfo],
            ['NextTime' => $nextTime]
        );

        // 从用户时区转换为服务器时区,billsysUtil->createOrder还不能转换,里面需要使用客户时区的nextTime
        $nextTime = $this->share->util->setTimeZone($nextTime, $projectData['TimeZone'], 3, '-');

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_SUBSCRIPTION_BY_DAY,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'isBatch' => $isBatch,
            'bmOrderNumber' => $result['Number'],
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'usersInfo' => $usersInfo['all'],
            'NextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['renewToDay']);
        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /*
     *@description 落地续费
     *<AUTHOR> 2022-04-07 17:40:23 V6.4
     *@lastEditor kxl 2022-04-07 17:40:23 V6.4
     *@param {*} Users 需要激活的用户,参数是主账号ID的数组，包含PM APP
     *@param {*} PayerId 购买人的ID
     *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员4.sub dis
     *@param {*} Type enum('multiple','single','office')
     *@param {*} Payer 终端用户是支付人邮箱，管理员是账号
     *@param {*} Count 续费月份数
     *@param {*} InstallerId
     *@return
     */
    public function createRenewLandline()
    {
        $params = ['Users', 'PayerId', 'PayerType', 'ProjectType', 'Count'];
        list($users, $payerId, $payerType, $projectType, $count) = $this->getParams($params);
        $users = json_decode($users, true);
        $payerType = intval($payerType);

        $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
        // 混合支付检查支付权限
        $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);

        $this->loadUtil('account');
        // 单个项目直接使用项目ID作为installerId
        $installerId = $projectIds[0];

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_LANDLINE,
            $count,
            ['Users' => $usersInfo]
        );
        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_LANDLINE,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'Month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'usersInfo' => $usersInfo['all'],
            'projectType' => $projectType
        ]);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /*
  *@description 创建单住户纯视频存储手动订单
  *@param {*} PayerId 购买人的ID
  *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
  *@param {*} ProjectType enum('multiple','single','office')
  *@param {*} Users 需要激活的用户
  *@param {*} VideoStorage 视频存储的单住户
  *@param {*} Count 月份
  *@return
  */
    public function createSingleRenewByVideoStorage()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'VideoSites', 'Count'];
        list($payerId, $payerType, $projectType, $videoSites, $count) = $this->getParams($params);
        $videoSitesArr = json_decode($videoSites, true);

        $videoStorageInfo = $this->callSelfFunc('getSubscribeVideoStorageInfo', [$videoSitesArr, $projectType]);
        $this->loadUtil('order', true);
        $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $payerId, $payerType);
        $installerId = $this->callSelfFunc('getInstallerIDByVideoStorageInfo', [$videoStorageInfo, $payerType]);
        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_SINGLE_VIDEO_STORAGE,
            $count,
            ['VideoStorages' => $videoStorageInfo],
            ['Count' => $count]
        );
        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_SINGLE_VIDEO_STORAGE,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['Site'],
            'usersInfo' => null,
            'videoStorages' => $videoStorageInfo,
            'projectType' => $projectType,
        ]);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /*
     *@description 创建社区纯视频存储手动订单
     *@param {*} PayerId 购买人的ID
     *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
     *@param {*} Type enum('multiple','single','office')
     *@param {*} Users 需要激活的用户
     *@param {*} VideoStorage 视频存储的单住户
     *@param {*} Count 月份
     *@return
     */
    public function createCommunityRenewByVideoStorage()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'VideoSites', 'NextTime'];
        list($payerId, $payerType, $projectType, $videoSites, $nextTime) = $this->getParams($params);
        $videoSitesArr = json_decode($videoSites, true);

        $videoStorageInfo = $this->callSelfFunc('getSubscribeVideoStorageInfo', [$videoSitesArr, $projectType]);
        //校验视频存储支付权限
        $this->loadUtil('order', true);
        $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $payerId, $payerType);
        $installerId = $this->callSelfFunc('getInstallerIDByVideoStorageInfo', [$videoStorageInfo, $payerType]);
        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_COMMUNITY_VIDEO_STORAGE,
            $count,
            ['VideoStorages' => $videoStorageInfo],
            ['NextTime' => $nextTime]
        );

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_COMMUNITY_VIDEO_STORAGE,
            'payerType' => $payerType,
            'installerId' => $installerId,//统一记录ins的id
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 1,//统一记成批量支付
            'month' => 0,
            'code' => $result['Code'],
            'userCharge' => $result['Site'],
            'usersInfo' => null,
            'videoStorages' => $videoStorageInfo,
            'nextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createCommunityRenewThirdLock()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'ThirdLockUUIDs', 'NextTime'];
        list($payerId, $payerType, $projectType, $thirdLockUUIDs, $nextTime) = $this->getParams($params);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
        //校验三方锁支付权限
        $this->loadUtil('order', true);
        $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        // 检查在同一个时区
        $this->callSelfFunc('checkProjectInSameTimeZone', [$projectIds]);
        $installerId = $this->callSelfFunc('getInstallerIDByThirdLockInfo', [$thirdLockInfo, $payerType]);

        $this->loadUtil('billingInfo');
        $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['communityThirdLockRenew']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }
        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_COMMUNITY_THIRDLOCK_RENEW,
            $count,
            ['ThirdLocks' => $thirdLockInfo],
            ['NextTime' => $nextTime]
        );

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_COMMUNITY_THIRDLOCK_RENEW,
            'payerType' => $payerType,
            'installerId' => $installerId,//统一记录ins的id
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 1,//统一记成批量支付
            'month' => 0,
            'code' => $result['Code'],
            'userCharge' => null,
            'usersInfo' => null,
            'thirdLockCharge' => $result['ThirdLock'],
            'ThirdLocks' => $thirdLockInfo,
            'nextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        // 锁订单
        $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['communityThirdLockRenew']);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createCommunityActiveThirdLock()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'ThirdLockUUIDs', 'NextTime'];
        list($payerId, $payerType, $projectType, $thirdLockUUIDs, $nextTime) = $this->getParams($params);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
        //校验三方锁支付权限
        $this->loadUtil('order', true);
        $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        // 检查在同一个时区
        $this->callSelfFunc('checkProjectInSameTimeZone', [$projectIds]);
        $installerId = $this->callSelfFunc('getInstallerIDByThirdLockInfo', [$thirdLockInfo, $payerType]);
        // 检查锁
        $this->loadUtil('billingInfo');
        $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['communityThirdLockActive']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }
        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_COMMUNITY_THIRDLOCK_ACTIVE,
            $count,
            ['ThirdLocks' => $thirdLockInfo],
            ['NextTime' => $nextTime]
        );

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_COMMUNITY_THIRDLOCK_ACTIVE,
            'payerType' => $payerType,
            'installerId' => $installerId,//统一记录ins的id
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 1,//统一记成批量支付
            'month' => 0,
            'code' => $result['Code'],
            'userCharge' => null,
            'usersInfo' => null,
            'thirdLockCharge' => $result['ThirdLock'],
            'ThirdLocks' => $thirdLockInfo,
            'nextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        // 锁订单
        $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['communityThirdLockActive']);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /*
     *@description 创建社区续费
     *@param {*} Users 需要激活的用户
     *@param {*} VideoStorage 视频存储的社区
     *@param {*} NextTime 下次到期时间
     *@param {*} PayerId 购买人的ID
     *@param {*} ProjectType enum('multiple','single','office')
     *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
     *@return
     */
    public function createCommunityRenewMixOrder()
    {
        // 按天续费需要锁住续费人员
        $params = ['User', 'VideoStorage', 'ThirdLock', 'NextTime', 'PayerId', 'ProjectType', 'PayerType'];
        list($user, $videoStorage, $thirdLock, $nextTime, $payerId, $projectType, $payerType) = $this->getParams($params);

        $users = $user['Users'];
        $videoSites = $videoStorage['VideoSites'];
        $thirdLockUUIDs = $thirdLock['ThirdLockUUIDs'];
        $users = json_decode($users, true);
        $videoSites = json_decode($videoSites, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);
        $payerType = intval($payerType);

        $videoStorageInfo = $thirdLockInfo = $usersInfo = $projectIds = [];
        $this->loadUtil('order', true);

        // 混合支付检查支付权限
        if (!empty($videoSites)) {
            $videoStorageInfo = $this->callSelfFunc('getSubscribeVideoStorageInfo', [$videoSites, $projectType]);
            // 校验视频存储支付权限
            $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $payerId, $payerType);
            $videProjectIds = $videoStorageInfo['projectIds'];
            $projectIds = array_merge($projectIds, $videProjectIds);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
            $thirdLockProjectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
            $projectIds = array_merge($projectIds, $thirdLockProjectIds);
        }
        if (!empty($users)) {
            $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
            $userProjectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
            $projectIds = array_merge($projectIds, $userProjectIds);
        }

        $projectIds = array_unique($projectIds);
        // 检查在同一个时区
        $this->callSelfFunc('checkProjectInSameTimeZone', [$projectIds]);

        $this->loadUtil('account', true);

        //验证所有的过期时间是否都大于下次时间
        $timeCheckArr = [];
        foreach ($usersInfo['all'] as $user) {
            $project = $this->utils->_common->account->getManagerInfo($user['ProjectId']);

            $timeCheckArr[] = [
                'TimeZone' => $project['TimeZone'],
                'ExpireTime' => $user['ExpireTime']
            ];
        }
        foreach ($videoStorageInfo['site'] as $site) {
            $timeCheckArr[] = [
                'TimeZone' => $site['TimeZone'],
                'ExpireTime' => $site['VideoStorageExpireTime']
            ];
        }
        if (!empty($thirdLockInfo)) {
            $lockSite = array_column($thirdLockInfo['site'], null, 'UUID');
            foreach ($thirdLockInfo['config'] as $lockItem) {
                $timeCheckArr[] = [
                    'TimeZone' => $lockSite[$lockItem['AccountUUID']]['TimeZone'],
                    'ExpireTime' => $lockItem['ExpireTime']
                ];
            }
        }
        foreach ($timeCheckArr as $item) {
            $expireTime = \share\util\setTimeZone($item['ExpireTime'], $item['TimeZone'], 3);
            if (strtotime($nextTime) <= strtotime($expireTime)) {
                $this->output->echoErrorMsg(STATE_PAY_NEXTTIME_INVALID);
            }
        }

        $this->loadUtil('billingInfo');
        $this->loadUtil('account');
        // 检查锁订单
        if (!empty($users)) {
            $accounts = $this->utils->account->changeIDToAccount($users);
            $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['renewToDay']);
            if ($data !== true) {
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$data['OrderNumber'], $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], []);
                }
            }
        }

        // 检测视频存储
        if (!empty($videoSites)) {
            $data = $this->utils->billingInfo->isLockPayData($videoSites, PAY_TYPE['communityVideoStorage']);
            if ($data !== true) {
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$data['OrderNumber'], $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], []);
                }
            }
        }

        // 检查三方锁订单
        if (!empty($thirdLockUUIDsArr)) {
            $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['communityThirdLockRenew']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }


        $projectData = $this->utils->account->getManagerInfo($projectIds[0]);
        /* 单个项目直接使用项目ID作为installerId，因为单个项目入口可能是社区/办公内部入口，也可能是批量的入口
         因此，直接使用这个项目id作为insId字段，payerId可以直接使用任意ins下的社区/办公Id，因为目前只有一个订单入口
         不需要区分是否从批量入口支付单个项目还是直接从单个社区里面支付
        */
        if (1 === count($projectIds)) {
            $installerId = $projectIds[0];
            $isBatch = 0;
        } else {
            $isBatch = 1;
            // 多个项目时记录installerId
            $installerId = $projectData['ManageGroup'];
            if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                $payerId = $installerId;
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = 1;
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_MIX,
            $count,
            ['Users' => $usersInfo, 'VideoStorages' => $videoStorageInfo, 'ThirdLocks' => $thirdLockInfo],
            ['NextTime' => $nextTime]
        );

        // 从用户时区转换为服务器时区,billsysUtil->createOrder还不能转换,里面需要使用客户时区的nextTime
        $nextTime = $this->share->util->setTimeZone($nextTime, $projectData['TimeZone'], 3, '-');

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_MIX,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => $isBatch,
            'month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'videoCharge' => $result['Site'],
            'thirdLockCharge' => $result['ThirdLock'],
            'usersInfo' => $usersInfo,
            'videoStorages' => $videoStorageInfo,
            'ThirdLocks' => $thirdLockInfo,
            'nextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        if (!empty($users)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['renewToDay']);
        }
        if (!empty($videoSites)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $videoSites, PAY_TYPE['communityVideoStorage']);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['communityThirdLockRenew']);
        }

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];

    }

    // 创建社区激活混合支付订单
    public function createCommunityActiveMixOrder()
    {
        $params = ['User', 'ThirdLock', 'NextTime', 'PayerId', 'ProjectType', 'PayerType'];
        list($user, $thirdLock, $nextTime, $payerId, $projectType, $payerType) = $this->getParams($params);

        $users = $user['Users'];
        $thirdLockUUIDs = $thirdLock['ThirdLockUUIDs'];
        $users = json_decode($users, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $payerType = intval($payerType);

        $thirdLockInfo = $usersInfo = $projectIds = [];
        $this->loadUtil('order', true);

        // 混合支付检查支付权限
        if (!empty($thirdLockUUIDsArr)) {
            $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
            $thirdLockProjectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
            $projectIds = array_merge($projectIds, $thirdLockProjectIds);
        }
        if (!empty($users)) {
            $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
            $userProjectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
            $projectIds = array_merge($projectIds, $userProjectIds);
        }

        $projectIds = array_unique($projectIds);

        $this->loadUtil('account');
        $projectData = $this->utils->account->getManagerInfo($projectIds[0]);
        /* 单个项目直接使用项目ID作为installerId，因为单个项目入口可能是社区/办公内部入口，也可能是批量的入口
         因此，直接使用这个项目id作为insId字段，payerId可以直接使用任意ins下的社区/办公Id，因为目前只有一个订单入口
         不需要区分是否从批量入口支付单个项目还是直接从单个社区里面支付
        */
        if (1 === count($projectIds)) {
            $installerId = $projectIds[0];
            $isBatch = 0;
        } else {
            $isBatch = 1;
            // 多个项目时记录installerId
            $installerId = $projectData['ManageGroup'];
            if ($this->callSelfFunc('checkIsPayerTypeIns', [$payerType])) {
                $payerId = $installerId;
            }
        }

        // 检查锁订单
        $this->loadUtil('billingInfo');
        if (!empty($users)) {
            $accounts = $this->utils->account->changeIDToAccount($users);
            $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['active']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }
        if (!empty($thirdLockUUIDsArr)) {
            $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['communityThirdLockActive']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = 1;
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_MIX_ACTIVE,
            $count,
            ['Users' => $usersInfo, 'ThirdLocks' => $thirdLockInfo],
            ['NextTime' => $nextTime]
        );

        // 从用户时区转换为服务器时区,billsysUtil->createOrder还不能转换,里面需要使用客户时区的nextTime
        $nextTime = $this->share->util->setTimeZone($nextTime, $projectData['TimeZone'], 3, '-');

        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_MIX_ACTIVE,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => $isBatch,
            'month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'thirdLockCharge' => $result['ThirdLock'],
            'usersInfo' => $usersInfo,
            'ThirdLocks' => $thirdLockInfo,
            'nextTime' => $nextTime,
            'projectType' => $projectType
        ]);

        // 锁订单
        if (!empty($users)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['active']);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['communityThirdLockActive']);
        }

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];

    }

    /*
   *@description 创建单住户手动续费订单
   *@param {*} Users 需要激活的用户
   *@param {*} VideoStorage 视频存储的单住户
   *@param {*} Count 几个月
   *@param {*} PayerId 购买人的ID
   *@param {*} ProjectType enum('multiple','single','office')
   *@param {*} PayerType 支付类型:终端用户:0,物业:1,2:install,3:区域管理员,4.sub dis
   *@return
   */
    public function createSingleRenewMixOrder()
    {
        $params = ['User', 'VideoStorage', 'ThirdLock', 'Count', 'PayerId', 'ProjectType', 'PayerType'];
        list($user, $videoStorage, $thirdLock, $count, $payerId, $projectType, $payerType) = $this->getParams($params);

        $users = $user['Users'];
        $videoSites = $videoStorage['VideoSites'];
        $thirdLockUUIDs = $thirdLock['ThirdLockUUIDs'];
        $usersArr = json_decode($users, true);
        $videoSitesArr = json_decode($videoSites, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $payerType = intval($payerType);

        $videoStorageInfo = $thirdLockInfo = $usersInfo = [];
        // 混合支付检查支付权限
        $this->loadUtil('order', true);
        if (!empty($videoSitesArr)) {
            $videoStorageInfo = $this->callSelfFunc('getSubscribeVideoStorageInfo', [$videoSitesArr, $projectType]);
            $projectIds = $this->utils->_common->order->checkPayPermissionByVideoStorage($videoStorageInfo, $payerId, $payerType);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
            $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        }
        if (!empty($usersArr)) {
            $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$usersArr]);
            $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
        }
        // 单个项目直接使用项目ID作为installerId
        $installerId = $projectIds[0];

        // 检查锁订单
        $this->loadUtil('billingInfo');
        $this->loadUtil('account');
        if (!empty($usersArr)) {
            $accounts = $this->utils->account->changeIDToAccount($usersArr);
            // 检查锁订单
            $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['landline']);
            if ($data !== true) {
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$data['OrderNumber'], $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], []);
                }
            }
        }
        if (!empty($thirdLockUUIDsArr)) {
            $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['singleThirdLockRenew']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }

        // 向计费系统请求多类型订单
        $this->loadProvider('billsysUtil');
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_MIX,
            $count,
            ['Users' => $usersInfo, 'VideoStorages' => $videoStorageInfo, 'ThirdLocks' => $thirdLockInfo]
        );
        // 创建云订单
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_MIX,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'videoCharge' => $result['Site'],
            'thirdLockCharge' => $result['ThirdLock'],
            'usersInfo' => $usersInfo,
            'videoStorages' => $videoStorageInfo,
            'ThirdLocks' => $thirdLockInfo,
            'projectType' => $projectType,
        ]);

        if (!empty($usersArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['landline']);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['singleThirdLockRenew']);
        }

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createSingleActiveMixOrder()
    {
        $params = ['User', 'ThirdLock', 'Count', 'PayerId', 'ProjectType', 'PayerType'];
        list($user, $thirdLock, $count, $payerId, $projectType, $payerType) = $this->getParams($params);

        $users = $user['Users'];
        $thirdLockUUIDs = $thirdLock['ThirdLockUUIDs'];
        $usersArr = json_decode($users, true);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $payerType = intval($payerType);

        // 混合支付检查支付权限
        $this->loadUtil('order', true);
        $usersInfo = $thirdLockInfo = [];
        if (!empty($thirdLockUUIDsArr)) {
            $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
            $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        }
        if (!empty($usersArr)) {
            $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$usersArr]);
            $projectIds = $this->callSelfFunc('checkPayPermission', [$usersInfo, $payerId, $payerType]);
        }
        // 单个项目直接使用项目ID作为installerId
        $installerId = $projectIds[0];

        // 检查锁订单
        $this->loadUtil('billingInfo');
        $this->loadUtil('account');
        if (!empty($usersArr)) {
            $accounts = $this->utils->account->changeIDToAccount($usersArr);
            $data = $this->utils->billingInfo->isLockPayData($accounts, PAY_TYPE['active']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }
        if (!empty($thirdLockUUIDsArr)) {
            $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['singleThirdLockActive']);
            if ($data !== true) {
                $oldOrderAccounts = array_column($data, 'DataKey');
                $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
                if (empty($adminAccount)) {
                    // 同一个管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
                } else {
                    //不同管理员
                    $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
                }
            }
        }

        // 向计费系统请求多类型订单
        $this->loadProvider('billsysUtil');
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_MIX_ACTIVE,
            $count,
            ['Users' => $usersInfo, 'ThirdLocks' => $thirdLockInfo]
        );
        // 创建云订单
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_MIX_ACTIVE,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'month' => $count,
            'code' => $result['Code'],
            'userCharge' => $result['User'],
            'thirdLockCharge' => $result['ThirdLock'],
            'usersInfo' => $usersInfo,
            'ThirdLocks' => $thirdLockInfo,
            'projectType' => $projectType,
        ]);

        // 锁订单
        if (!empty($usersArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $accounts, PAY_TYPE['active']);
        }
        if (!empty($thirdLockUUIDsArr)) {
            $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['singleThirdLockActive']);
        }

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    public function createOrder($data)
    {
        $payType = $data['payType'];
        if ($payType === PAY_LANDLINE) {
            return $this->createPayLandlineOrder($data);
        } elseif ($payType === PAY_ACTIVE) {
            return $this->createPayActiveOrder($data);
        } elseif ($payType === PAY_SUBSCRIPTION) {
            return $this->createPaySubscriptionOrder($data);
        } elseif ($payType === PAY_SUBSCRIPTION_BY_DAY) {
            return $this->createPaySubscriptionByDayOrder($data);
        } elseif ($payType === PAY_COMMUNITY_VIDEO_STORAGE) {
            return $this->createCommunityVideoStorageOrder($data);
        } elseif ($payType === PAY_SINGLE_VIDEO_STORAGE) {
            return $this->createSingleVideoStorageOrder($data);
        } elseif ($payType === PAY_SINGLE_THIRDLOCK_ACTIVE || $payType === PAY_SINGLE_THIRDLOCK_RENEW) {
            return $this->createSingleThirdLockOrder($data);
        } elseif ($payType === PAY_COMMUNITY_THIRDLOCK_ACTIVE || $payType === PAY_COMMUNITY_THIRDLOCK_RENEW) {
            return $this->createCommunityThirdLockOrder($data);
        } elseif ($payType === PAY_MIX) {
            return $this->createMixOrder($data);
        } elseif ($payType === PAY_MIX_ACTIVE) {
            return $this->createMixActiveOrder($data);
        } elseif ($payType === PAY_TYPE['autoRenew']) {
            return $this->createAutoRenewOrderparam($data);
        } else {
            return $this->createPayOutAppOrderparam($data);
        }
    }

    // 创建额外app订单
    private function createPayOutAppOrderparam($data)
    {
        // 验证额外app订单的参数信息
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $projectType = $data['projectType'];
        $subscriptionUUID = '';
        $paypalOrder = '';
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $nextTime = '0000-00-00 00:00:00';
        $month = empty($month) ? 0 : $data['month'];
        $days = 0;

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['buyOutApp'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['buyOutApp']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        $orderEndUserType = PAY_SUB_TYPE['buyOutApp'];
        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];

            $amount = $this->share->util->inputComputedCount($user['AddAppFee']);
            $disCount = $user['AddAppFeePercent'];

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    // 创建自动续费订单
    private function createAutoRenewOrderparam($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'installerId',
            'isBatch', 'bmOrderNumber', 'code', 'userCharge', 'videoCharge',
            'usersInfo', 'NextTime', 'Status', 'Discount',
            'PayPlatform', 'SubscriptionUUID', 'PaypalOrder', 'videoStorages', 'projectType', 'MixType',
            'thirdLockCharge', 'ThirdLocks'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create autoRenew Order params is error, " . json_encode($data) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $month = 1;
        $nextTime = $data['NextTime'];
        $status = $data['Status'];
        $orderDisCount = $data['Discount'];
        $payPlatform = $data['PayPlatform'];
        $subscriptionUUID = $data['SubscriptionUUID'];
        $paypalOrder = $data['PaypalOrder'];
        $videoStorages = $data['videoStorages'];
        $videoCharges = $data['videoCharge'];
        $projectType = $data['projectType'];
        $mixType = $data['MixType'];
        $thirdLockCharge = $data['thirdLockCharge'];
        $thirdLocks = $data['ThirdLocks'];

        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);
        $videoSites = $this->share->util->arrayColumnAsKey($videoStorages['site'], 'UUID');

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        //查询订阅信息，确认续费天数
        $this->loadUtil('subscription');
        $subscription = $this->utils->subscription->getSubscriptionInfoByUUID($subscriptionUUID);


        switch ($subscription['IntervalType']) {
            case "0": //按月
                $days = 30;
                break;
            case "1": //按季度 todo 有按季度支付时，按实际规则修改
                $days = 90;
                break;
            case "2": //按年 todo 有按年支付时，按实际规则修改
                $days = 365;
                break;
            case "3": //按天
                $days = 1;
                break;
            default:
                $days = 30;
        }


        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['autoRenew'];

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'PayPlatform' => $payPlatform, 'Status' => $status, 'MixType' => $mixType,
            'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];

            $amount = $this->share->util->inputComputedCount($user['SubscriptionPrice']);
            $disCount = $orderDisCount;
            $orderEndUserType = $this->getRenewType($id, $usersInfo);

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }


        $orderEndUserType = PAY_SUB_TYPE['singleVideoStorage'];
        $orderThirdLockSubType = PAY_SUB_LOCK_TYPE['singleThirdLockRenew'];
        if ($projectType === PAY_TYPE_SINGLE) {
            $orderEndUserType = PAY_SUB_TYPE['singleVideoStorage'];
            $orderThirdLockSubType = PAY_SUB_LOCK_TYPE['singleThirdLockRenew'];
        } elseif ($projectType === PAY_TYPE_MULTIPLE) {
            $orderEndUserType = PAY_SUB_TYPE['communityVideoStorage'];
            $orderThirdLockSubType = PAY_SUB_LOCK_TYPE['communityThirdLockRenew'];
        } else {
            // 视频存储没有办公
        }

        foreach ($videoCharges as $video) {
            $siteUUID = $video['SiteUUID'];
            if ($projectType === PAY_TYPE_SINGLE) {
                $object = $videoSites[$siteUUID]['Name'];
            } elseif ($projectType === PAY_TYPE_MULTIPLE) {
                $object = $videoSites[$siteUUID]['Location'];
            }
            $object = empty($object) ? "" : $object;
            $projectUUID = $videoSites[$siteUUID]['ProjectUUID'];
            $projectName = $videoSites[$siteUUID]['ProjectName'];
            $model = json_decode($video['ChargeData'], true);
            $amount = $this->share->util->inputComputedCount($model['SubscriptionPrice']);
            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => null,
                'Object' => $object, 'Discount' => 100,
                'DiscountInfo' => '',
                'ActivityUUID' => null, 'ChargeData' => $video['ChargeData'],
                'ProjectUUID' => $projectUUID, 'ProjectName' => $projectName,
                'Days' => $video['Days'] ?: 0,
                'SiteUUID' => $siteUUID
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        //插入三方锁
        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');
        foreach ($thirdLockCharge as $item) {
            $lockUUID = $item['LockUUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $model = json_decode($item['ChargeData'], true);
            $amount = $this->share->util->inputComputedCount($model['SubscriptionPrice']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => $orderThirdLockSubType,
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($model),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    // 创建续费到指定日期订单
    private function createPaySubscriptionByDayOrder($data)
    {
        // 验证激活月费订单的参数信息
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'NextTime', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = $data['NextTime'];
        $days = 0;
        $month = 0;
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['renewToDay'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['renewToDay']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];

            // 区分主账户和PMApp
            $amount = $this->share->util->inputComputedCount($user['DayFee']);
            $orderEndUserType = PAY_SUB_TYPE['renewToDay'];
            $disCount = $user['MonthlyFeePercent'];
            if ($role === PMENDMROLE) {
                $orderEndUserType = PAY_SUB_TYPE['renewPM'];
            }

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    // 创建激活月费订单
    private function createPayActiveOrder($data)
    {
        // 验证激活月费订单的参数信息
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create Active Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = '0000-00-00 00:00:00';
        $days = 0;
        $month = 0;
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];
        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['active'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['active']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['ActiveFee']);

            $disCount = $user['ActiveFeePercent'];
            $orderEndUserType = PAY_SUB_TYPE['active'];

            // 混合激活时从账户要使用AddAppFee
            if (in_array($role, SUBROLE)) {
                $amount = $this->share->util->inputComputedCount($user['AddAppFee']);
                $disCount = $user['AddAppFeePercent'];
                $orderEndUserType = PAY_SUB_TYPE['buyOutApp'];
            } elseif ($role === PMENDMROLE) {
                $orderEndUserType = PAY_SUB_TYPE['activePM'];
            }

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    // 创建月费订单
    private function createPaySubscriptionOrder($data)
    {
        // 验证月费订单的参数信息
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'Month', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create SubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = '0000-00-00 00:00:00';
        $days = 0;
        $month = $data['Month'];
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['renewToMonth'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['renewToMonth']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        $orderEndUsertype = PAY_SUB_TYPE['renewToMonth'];
        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['MonthlyFee']);
            $disCount = $user['MonthlyFeePercent'];


            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUsertype,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }


    // 创建单住户落地月费订单
    private function createPayLandlineOrder($data)
    {
        // 验证单住户落地月费订单的参数信息
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'Month', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create SubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payType = $data['payType'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $users = $data['userCharge'];
        $usersInfo = $data['usersInfo'];
        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = '0000-00-00 00:00:00';
        $days = 0;
        $month = $data['Month'];
        $projectType = $data['projectType'];

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['landline'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['landline']);
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        $orderEndUsertype = PAY_SUB_TYPE['landline'];
        foreach ($users as $user) {
            $id = $user['ID'];
            $info = $usersInfo[$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['MonthlyFee']);
            $disCount = $user['MonthlyFeePercent'];

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUsertype,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    private function getPayerAndPayerID($payerType, $payerId, $isBatch)
    {
        $endUserPayerType = 0;
        $insPayerType = 2;
        //终端用户
        if ($payerType === $endUserPayerType) {
            $userData = $this->utils->account->getUserInfo($payerId);
            $payer = $userData['Email'] ?: $userData['Account'];
        } else {
            $payerData = $this->utils->account->getManagerInfo($payerId);
            $payer = $payerData['Account'];
            /* V6.4版本只有社区有批量支付，IsBatch这个字段先暂时表示社区的批量支付。
             后续如果推广到办公，可以让IsBatch=0+AccountID=office/community来区分单独社区办公支付，
             IsBatch=1+AccountID=insId表示批量社区支付,IsBatch=2+AccountID=insId表示批量办公支付
            */
            if ($payerType === $insPayerType && $isBatch === 1) {
                $insId = $payerData['ManageGroup'];
                $payerId = $insId;
                $payer = $this->utils->account->getManagerInfo($insId)['Account'];
            }
        }
        return array($payer, $payerId);
    }

    public function createFeature()
    {
        $params = ['Type', 'Count', PROXY_ROLE['projectId'], 'PayerId', 'PayerType', 'ProjectType'];
        list($type, $month, $projectId, $payerId, $payerType, $projectType) = $this->getParams($params);
        if ($type === '5') {
            //云订单Type=5 对应 bm订单的Type=4
            list($orderID, $bmOrderNumber, $token, $code) = $this->createFeatureOnce($payerId, $payerType, $projectId, $projectType);
        } elseif ($type === '6') {
            //云订单Type=6 对应 bm订单的Type=5
            list($orderID, $bmOrderNumber, $token, $code) = $this->createFeatureMonthly($payerId, $payerType, $projectId, $month, $projectType);
        }

        return ['orderID' => $orderID, 'bmurl' => BMAPYURL . "?order=$bmOrderNumber&token=$token&code=$code"];
    }

    private function createFeatureOnce($payerId, $payerType, $projectId, $projectType)
    {
        $this->loadUtil('billingInfo');
        // 检查锁订单
        $data = $this->utils->billingInfo->isLockPayData([$projectId], PAY_TYPE['featureOnce']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }

        $this->loadUtil('account');

        $payerData = $this->utils->account->getManagerInfo($payerId);
        $projectData = $this->utils->account->getManagerInfo($projectId);
        $disData = $this->utils->account->getManagerInfo($projectData['ParentID']);

        $this->loadUtil('featurePlan');
        $feature = $this->utils->featurePlan->getInfoByProject($projectId);
        $feeUUID = $feature['FeeUUID'];
        $featureID = $feature['FeatureID'];

        // 向计费系统请求创建订单
        $this->loadProvider('billsysUtil');
        $data = $this->services->billsysUtil->createFeatureOrder($payerId, $payerType, 4, 1, $feeUUID);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];
        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        // 支付类型
        $type = PAY_TYPE['featureOnce'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['featureOnce']);

        $now = $this->share->util->getNow();
        $orderID = $this->dao->orderList->insert([
            'OrderNumber' => $orderNumber,
            'AccountID' => $payerId,
            'WebHookToken' => $data['Token'],
            'CreateTime' => $now,
            'TotalPrice' => $this->share->util->inputComputedCount($orderData['TotalPrice']),
            'Type' => $type,
            'Payer' => $payerData['Account'],
            'PayerType' => $payerType,
            'InstallID' => $projectId,
            'AreaManageID' => $disData['ID'],
            'FinalPrice' => $this->share->util->inputComputedCount($orderData['FinalPrice']),
            'BmOrderNumber' => $orderData['Number'],
            'PayCode' => $orderData['Code'],
            'ProjectType' => $projectTypeMap[$projectType],
            'MixType' => $mixType
        ]);
        $this->dao->orderEndUserList->insert([
            'OrderID' => $orderID,
            'Type' => $type,
            'Amount' => $this->share->util->inputComputedCount($orderData['TotalPrice']),
            'AppID' => $featureID,
            'ChargeData' => json_encode($chargeData)
        ]);
        $this->utils->billingInfo->lockPayData($orderNumber, [$projectId], PAY_TYPE['featureOnce']);
        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }

    private function createFeatureMonthly($payerId, $payerType, $projectId, $month, $projectType)
    {
        $this->loadUtil('account');
        $payerData = $this->utils->account->getManagerInfo($payerId);
        $projectData = $this->utils->account->getManagerInfo($projectId);
        $disData = $this->utils->account->getManagerInfo($projectData['ParentID']);

        $this->loadUtil('featurePlan');
        $feature = $this->utils->featurePlan->getInfoByProject($projectId);
        $feeUUID = $feature['FeeUUID'];
        $featureID = $feature['FeatureID'];

        // 向计费系统请求创建订单
        $this->loadProvider('billsysUtil');
        $data = $this->services->billsysUtil->createFeatureOrder($payerId, $payerType, 5, $month, $feeUUID);
        $orderData = $data['OrderData'];
        $chargeData = $data['ChargeData'];
        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        // 支付类型
        $type = PAY_TYPE['featureMonth'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['featureMonth']);
        $now = $this->share->util->getNow();
        $orderID = $this->dao->orderList->insert([
            'OrderNumber' => $orderNumber,
            'AccountID' => $payerId,
            'WebHookToken' => $data['Token'],
            'CreateTime' => $now,
            'TotalPrice' => $this->share->util->inputComputedCount($orderData['TotalPrice']),
            'Type' => $type,
            'Payer' => $payerData['Account'],
            'PayerType' => $payerType,
            'InstallID' => $projectId,
            'AreaManageID' => $disData['ID'],
            'Months' => $month,
            'FinalPrice' => $this->share->util->inputComputedCount($orderData['FinalPrice']),
            'BmOrderNumber' => $orderData['Number'],
            'PayCode' => $orderData['Code'],
            'ProjectType' => $projectTypeMap[$projectType],
            'MixType' => $mixType
        ]);
        $this->dao->orderEndUserList->insert([
            'OrderID' => $orderID,
            'Type' => $type,
            'Amount' => $this->share->util->inputComputedCount($orderData['TotalPrice']),
            'AppID' => $featureID,
            'ChargeData' => json_encode($chargeData)
        ]);
        return [$orderID, $orderData['Number'], $data['Token'], $orderData['Code']];
    }


    /*
     *@description 创建订阅用户三方平台账号
     *<AUTHOR> 2023-12-22 16:06:50 V6.7.0
     *@lastEditor cj 2023-12-22 16:06:50 V6.7.0
     *@param {*} PayPlatID:string-required
     *@param {*} Email?:string
     *@param {*} PayerUUID:uuid
     *@param {*} PayPlatform?:enum(0,1)
     *@return void
     */
    public function createStripeUser()
    {
        $params = ['PayPlatID:string-required', 'Email?:string', 'PayerUUID:uuid', 'PayPlatform?:enum(0,1)'];
        list($payPlatID, $email, $payerUUID, $payPlatform) = $this->getParams($params);
        $uuid = $this->share->util->uuid();
        $payPlatform = $payPlatform === null ? 1 : $payPlatform;
        $this->dao->subscriptionUsers->insert([
            'PayPlatform' => $payPlatform,
            'PayPlatID' => $payPlatID,
            'Email' => $email,
            'PayerUUID' => $payerUUID,
            'UUID' => $uuid
        ]);
    }

    /**
     * @description:获得续费类型
     * @param: {type}
     * @param {int} $userID  用户ID
     * @param {array} $userList 用户列表
     * @return int
     * @author: shoubin.chen 2024-01-24 15:40:47 v6.7.1
     * @lastEditor: shoubin.chen 2024-01-24 15:40:47 v6.7.1
     */
    private function getRenewType($userID, $userList)
    {
        $user = $userList[$userID];
        $role = intval($user['Role']);
        $type = PAY_SUB_TYPE['renewToMonth'];

        if ($role === PERENDMROLE) {
            //单住户主账户是落地费
            $type = PAY_SUB_TYPE['landline'];
        } else if (in_array($role, OFFROLE)) {
            //办公用户是月费
            $type = PAY_SUB_TYPE['renewToMonth'];
        } else if ($role === COMENDMROLE) {
            //社区主账号是按日期续费
            $type = PAY_SUB_TYPE['renewToDay'];
        } else if ($role === PMENDMROLE) {
            //pm app是pm app续费
            $type = PAY_SUB_TYPE['renewPM'];
        }
        return $type;
    }

    private function createCommunityVideoStorageOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'videoStorages', 'nextTime', 'projectType'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $charges = $data['userCharge'];
        $videoStorages = $data['videoStorages'];
        $nextTime = $data['nextTime'];
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = empty($nextTime) ? '0000-00-00 00:00:00' : $nextTime;
        $days = $month = 0;

        $videoSites = $this->share->util->arrayColumnAsKey($videoStorages['site'], 'UUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['communityVideoStorage'];
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['communityVideoStorage']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        $orderEndUserType = PAY_SUB_TYPE['communityVideoStorage'];
        foreach ($charges as $user) {
            $siteUUID = $user['UUID'];
            $projectUUID = $videoSites[$siteUUID]['ProjectUUID'];
            $projectName = $videoSites[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($user['ItemFee']);

            $object = $projectName;

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => null,
                'Object' => $object, 'Discount' => 100,
                'DiscountInfo' => '',
                'ActivityUUID' => null, 'ChargeData' => json_encode($user['Model']),
                'ProjectUUID' => $projectUUID, 'ProjectName' => $projectName,
                'Days' => $user['Days'] ?: 0,
                'SiteUUID' => $siteUUID
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createSingleVideoStorageOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'videoStorages', 'projectType', 'month'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $charges = $data['userCharge'];
        $month = isset($data['month']) ? $data['month'] : 0;
        $videoStorages = $data['videoStorages'];
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = '0000-00-00 00:00:00';
        $days = 0;

        $videoSites = $this->share->util->arrayColumnAsKey($videoStorages['site'], 'UUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['singleVideoStorage']);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => PAY_TYPE['singleVideoStorage'],
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        $orderEndUserType = PAY_SUB_TYPE['singleVideoStorage'];
        foreach ($charges as $user) {
            $siteUUID = $user['UUID'];
            $projectUUID = $videoSites[$siteUUID]['ProjectUUID'];
            $projectName = $videoSites[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($user['ItemFee']);
            $object = $videoSites[$siteUUID]['Name'];

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => null,
                'Object' => $object, 'Discount' => 100,
                'DiscountInfo' => '',
                'ActivityUUID' => null, 'ChargeData' => json_encode($user['Model']),
                'ProjectUUID' => $projectUUID, 'ProjectName' => $projectName,
                'Days' => $user['Days'] ?: 0,
                'SiteUUID' => $siteUUID
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createSingleThirdLockOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code',
            'thirdLockCharge', 'ThirdLocks', 'projectType', 'month'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $charges = $data['thirdLockCharge'];
        $month = isset($data['month']) ? $data['month'] : 0;
        $thirdLocks = $data['ThirdLocks'];
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $subscriptionUUID = '';
        $paypalOrder = '';
        $nextTime = '0000-00-00 00:00:00';
        $days = 0;

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $payType = $data['payType'];
        if ($payType == PAY_SINGLE_THIRDLOCK_ACTIVE) {
            $type = PAY_TYPE['singleThirdLockActive'];
            $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['singleThirdLockActive']);
            $orderEndUserType = PAY_SUB_LOCK_TYPE['singleThirdLockActive'];
        } elseif ($payType == PAY_SINGLE_THIRDLOCK_RENEW) {
            $type = PAY_TYPE['singleThirdLockRenew'];
            $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['singleThirdLockRenew']);
            $orderEndUserType = PAY_SUB_LOCK_TYPE['singleThirdLockRenew'];
        }

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $type,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        foreach ($charges as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => $orderEndUserType,
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createCommunityThirdLockOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code',
            'thirdLockCharge', 'ThirdLocks', 'projectType', 'nextTime'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        $payerId = $data['payerId'];
        $token = $data['token'];
        $totalPrice = $data['totalPrice'];
        $payerType = $data['payerType'];
        $installerId = $data['installerId'];
        $isBatch = $data['isBatch'];
        $bmOrderNumber = $data['bmOrderNumber'];
        $code = $data['code'];
        $charges = $data['thirdLockCharge'];
        $nextTime = $data['nextTime'];
        $nextTime = empty($nextTime) ? '0000-00-00 00:00:00' : $nextTime;
        $thirdLocks = $data['ThirdLocks'];
        $projectType = $data['projectType'];
        $projectTypeMap = [
            PAY_TYPE_SINGLE => 1,
            PAY_TYPE_MULTIPLE => 2,
            PAY_TYPE_OFFICE => 3,
        ];

        $subscriptionUUID = '';
        $paypalOrder = '';
        $days = $month = 0;

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $payType = $data['payType'];
        if ($payType == PAY_COMMUNITY_THIRDLOCK_ACTIVE) {
            $type = PAY_TYPE['communityThirdLockActive'];
            $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['communityThirdLockActive']);
            $orderEndUserType = PAY_SUB_LOCK_TYPE['communityThirdLockActive'];
        } elseif ($payType == PAY_COMMUNITY_THIRDLOCK_RENEW) {
            $type = PAY_TYPE['communityThirdLockRenew'];
            $mixType = $this->share->util->getDecimalFromBits(PAY_TYPE['communityThirdLockRenew']);
            $orderEndUserType = PAY_SUB_LOCK_TYPE['communityThirdLockRenew'];
        }

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $type,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => $projectTypeMap[$projectType]
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        foreach ($charges as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => $orderEndUserType,
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    /**
     * @description: 创建单住户三方锁激活订单
     * @return array
     * @throws \Exception
     * @author: csc 2025/2/11 15:30 V7.1.0
     * @lastEditors: csc 2025/2/11 15:30 V7.1.0
     */
    public function createSingleActiveThirdLock()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'ThirdLockUUIDs', 'Count'];
        list($payerId, $payerType, $projectType, $thirdLockUUIDs, $count) = $this->getParams($params);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
        $this->loadUtil('order', true);
        $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        // 单个项目直接使用项目ID作为installerId
        $installerId = $projectIds[0];
        // 检查锁
        $this->loadUtil('billingInfo');
        $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['singleThirdLockActive']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_ACTIVE_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }
        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_SINGLE_THIRDLOCK_ACTIVE,
            $count,
            ['ThirdLocks' => $thirdLockInfo],
            ['Count' => $count]
        );
        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_SINGLE_THIRDLOCK_ACTIVE,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'month' => $count,
            'code' => $result['Code'],
            'thirdLockCharge' => $result['ThirdLock'],
            'ThirdLocks' => $thirdLockInfo,
            'projectType' => $projectType,
        ]);

        // 锁订单
        $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['singleThirdLockActive']);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    /**
     * @description: 创建单住户三方锁续费订单
     * @return array
     * @throws \Exception
     * @author: csc 2025/2/11 15:31 V7.1.0
     * @lastEditors: csc 2025/2/11 15:31 V7.1.0
     */
    public function createSingleRenewThirdLock()
    {
        $params = ['PayerId', 'PayerType', 'ProjectType', 'ThirdLockUUIDs', 'Count'];
        list($payerId, $payerType, $projectType, $thirdLockUUIDs, $count) = $this->getParams($params);
        $thirdLockUUIDsArr = json_decode($thirdLockUUIDs, true);

        $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDsArr, $projectType]);
        $this->loadUtil('order', true);
        $projectIds = $this->utils->_common->order->checkPayPermissionByThirdLock($thirdLockInfo, $payerId, $payerType);
        // 单个项目直接使用项目ID作为installerId
        $installerId = $projectIds[0];

        // 检查锁
        $this->loadUtil('billingInfo');
        $data = $this->utils->billingInfo->isLockPayData($thirdLockUUIDsArr, PAY_TYPE['singleThirdLockRenew']);
        if ($data !== true) {
            $oldOrderAccounts = array_column($data, 'DataKey');
            $adminAccount = $this->callSelfFunc('checkIsSamePayer', [$oldOrderAccounts, $payerId]);
            if (empty($adminAccount)) {
                // 同一个管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER]);
            } else {
                //不同管理员
                $this->output->echoErrorMsg(STATE_PAY_OUTSTANDING_DIFFERENT_PAYER, ['externalErrorObj' => Code::EXT_STATE_RENEW_DAY_WAITING_PAID_ORDER], [$adminAccount]);
            }
        }

        // 向计费系统请求订单
        $this->loadProvider('billsysUtil');
        $count = empty($count) ? 1 : intval($count);
        $result = $this->services->billsysUtil->createOrder(
            $payerId,
            $payerType,
            $projectType,
            null,
            PAY_SINGLE_THIRDLOCK_RENEW,
            $count,
            ['ThirdLocks' => $thirdLockInfo],
            ['Count' => $count]
        );
        // 插入OrderList
        list($orderId, $orderNumber) = $this->createOrder([
            'payerId' => $payerId,
            'token' => $result['Token'],
            'totalPrice' => $result['Price'],
            'payType' => PAY_SINGLE_THIRDLOCK_RENEW,
            'payerType' => $payerType,
            'installerId' => $installerId,
            'bmOrderNumber' => $result['Number'],
            'isBatch' => 0,
            'month' => $count,
            'code' => $result['Code'],
            'thirdLockCharge' => $result['ThirdLock'],
            'ThirdLocks' => $thirdLockInfo,
            'projectType' => $projectType,
        ]);

        // 锁订单
        $this->utils->billingInfo->lockPayData($orderNumber, $thirdLockUUIDsArr, PAY_TYPE['singleThirdLockRenew']);

        return [
            'ID' => $orderId,
            'Number' => $orderNumber,
            'Token' => $result['Token'],
            'Code' => $result['Code'],
            'bmUrl' => BMAPYURL . '?order=' . $result['Number'] . '&token=' . $result['Token'] . '&code=' . $result['Code']
        ];
    }

    // 创建单住户续费混合订单
    private function createSingleMixOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'videoCharge', 'usersInfo', 'videoStorages', 'month', 'thirdLockCharge', 'ThirdLocks'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        list($payerId, $token, $totalPrice, $payerType) = $this->share->util->extractArrayValues($data, ['payerId', 'token', 'totalPrice', 'payerType']);
        list($installerId, $isBatch, $bmOrderNumber, $code) = $this->share->util->extractArrayValues($data, ['installerId', 'isBatch', 'bmOrderNumber', 'code']);
        list($userCharges, $videoCharges, $month) = $this->share->util->extractArrayValues($data, ['userCharge', 'videoCharge', 'month']);
        list($usersInfo, $videoStorages) = $this->share->util->extractArrayValues($data, ['usersInfo', 'videoStorages']);
        list($thirdLockCharge, $thirdLocks) = $this->share->util->extractArrayValues($data, ['thirdLockCharge', 'ThirdLocks']);


        $subscriptionUUID = $paypalOrder = '';
        $days = 0;

        $nextTime = '0000-00-00 00:00:00';
        $month = empty($month) ? 0 : $data['month'];


        $videoSites = $this->share->util->arrayColumnAsKey($videoStorages['site'], 'UUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['mix'];
        $mixArr = [];
        if (!empty($userCharges)) {
            $mixArr[] = PAY_TYPE['landline'];
        }
        if (!empty($videoCharges)) {
            $mixArr[] = PAY_TYPE['singleVideoStorage'];
        }
        if (!empty($thirdLockCharge)) {
            $mixArr[] = PAY_TYPE['singleThirdLockRenew'];
        }
        $mixType = $this->share->util->getDecimalFromBits($mixArr);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => 1
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        //插入
        foreach ($userCharges as $user) {
            $id = $user['ID'];
            $info = $usersInfo['all'][$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['MonthlyFee']);
            $disCount = $user['MonthlyFeePercent'];

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => PAY_SUB_TYPE['landline'],
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0,
                'SiteUUID' => ''
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        foreach ($videoCharges as $user) {
            $siteUUID = $user['UUID'];
            $object = $videoSites[$siteUUID]['Name'];

            $projectUUID = $videoSites[$siteUUID]['ProjectUUID'];
            $projectName = $videoSites[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($user['ItemFee']);
            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => PAY_SUB_TYPE['singleVideoStorage'],
                'Amount' => $amount, 'AppID' => null,
                'Object' => $object, 'Discount' => 100,
                'DiscountInfo' => '',
                'ActivityUUID' => null, 'ChargeData' => json_encode($user['Model']),
                'ProjectUUID' => $projectUUID, 'ProjectName' => $projectName,
                'Days' => $user['Days'] ?: 0,
                'SiteUUID' => $siteUUID
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');
        foreach ($thirdLockCharge as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => PAY_SUB_LOCK_TYPE['singleThirdLockRenew'],
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createComnmunityMixOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'videoCharge', 'usersInfo', 'videoStorages', 'nextTime', 'thirdLockCharge', 'ThirdLocks'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        list($payerId, $token, $totalPrice, $payerType) = $this->share->util->extractArrayValues($data, ['payerId', 'token', 'totalPrice', 'payerType']);
        list($installerId, $isBatch, $bmOrderNumber, $code) = $this->share->util->extractArrayValues($data, ['installerId', 'isBatch', 'bmOrderNumber', 'code']);
        list($userCharges, $videoCharges) = $this->share->util->extractArrayValues($data, ['userCharge', 'videoCharge']);
        list($usersInfo, $videoStorages, $nextTime) = $this->share->util->extractArrayValues($data, ['usersInfo', 'videoStorages', 'nextTime']);
        list($thirdLockCharge, $thirdLocks) = $this->share->util->extractArrayValues($data, ['thirdLockCharge', 'ThirdLocks']);


        $subscriptionUUID = $paypalOrder = '';
        $month = $days = 0;

        $videoSites = $this->share->util->arrayColumnAsKey($videoStorages['site'], 'UUID');

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['mix'];

        $mixArr = [];
        if (!empty($userCharges)) {
            $mixArr[] = PAY_TYPE['renewToDay'];
        }
        if (!empty($videoCharges)) {
            $mixArr[] = PAY_TYPE['communityVideoStorage'];
        }
        if (!empty($thirdLockCharge)) {
            $mixArr[] = PAY_TYPE['communityThirdLockRenew'];
        }
        $mixType = $this->share->util->getDecimalFromBits($mixArr);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => 2
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        //插入
        foreach ($userCharges as $user) {
            $id = $user['ID'];
            $info = $usersInfo['all'][$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['DayFee']);
            $disCount = $user['MonthlyFeePercent'];
            $orderEndUserType = PAY_SUB_TYPE['renewToDay'];
            if ($role === PMENDMROLE) {
                $orderEndUserType = PAY_SUB_TYPE['renewPM'];
            }

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0, 'SiteUUID' => ''
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        foreach ($videoCharges as $user) {
            $siteUUID = $user['UUID'];
            $projectUUID = $videoSites[$siteUUID]['ProjectUUID'];
            $projectName = $videoSites[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($user['ItemFee']);
            $object = $projectName;

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => PAY_SUB_TYPE['communityVideoStorage'],
                'Amount' => $amount, 'AppID' => null,
                'Object' => $object, 'Discount' => 100,
                'DiscountInfo' => '',
                'ActivityUUID' => null, 'ChargeData' => json_encode($user['Model']),
                'ProjectUUID' => $projectUUID, 'ProjectName' => $projectName,
                'Days' => $user['Days'] ?: 0,
                'SiteUUID' => $siteUUID
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');
        foreach ($thirdLockCharge as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => PAY_SUB_LOCK_TYPE['communityThirdLockRenew'],
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createMixOrder($data)
    {
        $verifyKeys = ['projectType'];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create createMixOrder params is error, " . json_encode($verifyKeys) . " is need");
        }
        $projectType = $data['projectType'];
        if ($projectType === PAY_TYPE_SINGLE) {
            return $this->createSingleMixOrder($data);
        } elseif ($projectType === PAY_TYPE_MULTIPLE) {
            return $this->createComnmunityMixOrder($data);
        } else {
            throw new \Exception("No support this type" . $projectType);
        }
    }

    private function createMixActiveOrder($data)
    {
        $verifyKeys = ['projectType'];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create createMixOrder params is error, " . json_encode($verifyKeys) . " is need");
        }
        $projectType = $data['projectType'];
        if ($projectType === PAY_TYPE_SINGLE) {
            return $this->createSingleMixActiveOrder($data);
        } elseif ($projectType === PAY_TYPE_MULTIPLE) {
            return $this->createComnmunityMixActiveOrder($data);
        } else {
            throw new \Exception("No support this type" . $projectType);
        }
    }

    private function createSingleMixActiveOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'month', 'thirdLockCharge', 'ThirdLocks'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        // 插入订单和订单从表
        list($payerId, $token, $totalPrice, $payerType) = $this->share->util->extractArrayValues($data, ['payerId', 'token', 'totalPrice', 'payerType']);
        list($installerId, $isBatch, $bmOrderNumber, $code) = $this->share->util->extractArrayValues($data, ['installerId', 'isBatch', 'bmOrderNumber', 'code']);
        list($userCharges, $usersInfo, $month) = $this->share->util->extractArrayValues($data, ['userCharge', 'usersInfo', 'month']);
        list($thirdLockCharge, $thirdLocks) = $this->share->util->extractArrayValues($data, ['thirdLockCharge', 'ThirdLocks']);

        $subscriptionUUID = $paypalOrder = '';
        $days = 0;

        $nextTime = '0000-00-00 00:00:00';
        $month = empty($month) ? 0 : $data['month'];

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['mix'];
        $mixArr = [];
        if (!empty($userCharges)) {
            $mixArr[] = PAY_TYPE['active'];
        }
        if (!empty($thirdLockCharge)) {
            $mixArr[] = PAY_TYPE['singleThirdLockActive'];
        }
        $mixType = $this->share->util->getDecimalFromBits($mixArr);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => 1
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        //插入
        foreach ($userCharges as $user) {
            $id = $user['ID'];
            $info = $usersInfo['all'][$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['ActiveFee']);

            $disCount = $user['ActiveFeePercent'];
            $orderEndUserType = PAY_SUB_TYPE['active'];

            // 混合激活时从账户要使用AddAppFee
            if (in_array($role, SUBROLE)) {
                $amount = $this->share->util->inputComputedCount($user['AddAppFee']);
                $disCount = $user['AddAppFeePercent'];
                $orderEndUserType = PAY_SUB_TYPE['buyOutApp'];
            } elseif ($role === PMENDMROLE) {
                $orderEndUserType = PAY_SUB_TYPE['activePM'];
            }

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0, 'SiteUUID' => ''
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');
        foreach ($thirdLockCharge as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => PAY_SUB_LOCK_TYPE['singleThirdLockActive'],
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }

    private function createComnmunityMixActiveOrder($data)
    {
        $verifyKeys = [
            'payerId', 'token', 'totalPrice', 'payType', 'payerType',
            'installerId', 'isBatch', 'bmOrderNumber', 'code', 'userCharge',
            'usersInfo', 'nextTime', 'thirdLockCharge', 'ThirdLocks'
        ];
        $isPass = $this->share->util->validateArrayKeys($data, $verifyKeys);
        if (!$isPass) {
            $this->log->debug('{data} is error', ['data' => $data]);
            throw new \Exception("create PaySubscriptionByDay Order params is error, " . json_encode($verifyKeys) . " is need");
        }

        list($payerId, $token, $totalPrice, $payerType) = $this->share->util->extractArrayValues($data, ['payerId', 'token', 'totalPrice', 'payerType']);
        list($installerId, $isBatch, $bmOrderNumber, $code) = $this->share->util->extractArrayValues($data, ['installerId', 'isBatch', 'bmOrderNumber', 'code']);
        list($userCharges, $usersInfo, $nextTime) = $this->share->util->extractArrayValues($data, ['userCharge', 'usersInfo', 'nextTime']);
        list($thirdLockCharge, $thirdLocks) = $this->share->util->extractArrayValues($data, ['thirdLockCharge', 'ThirdLocks']);

        $subscriptionUUID = $paypalOrder = '';
        $month = $days = 0;
        $nextTime = empty($nextTime) ? '0000-00-00 00:00:00' : $nextTime;

        $totalPrice = $this->share->util->inputComputedCount($totalPrice);

        $this->loadUtil('account');
        $insData = $this->utils->account->getManagerInfo($installerId);
        $disId = $insData['ParentID'];

        list($payer, $payerId) = $this->getPayerAndPayerID($payerType, $payerId, $isBatch);

        $orderNumber = SERVERNUMBER . time() . rand(10000, 99999);
        $orderType = PAY_TYPE['mix'];
        $mixArr = [];
        if (!empty($userCharges)) {
            $mixArr[] = PAY_TYPE['active'];
        }
        if (!empty($thirdLockCharge)) {
            $mixArr[] = PAY_TYPE['communityThirdLockActive'];
        }
        $mixType = $this->share->util->getDecimalFromBits($mixArr);

        $orderParam = [
            'OrderNumber' => $orderNumber, 'AccountID' => $payerId,
            'WebHookToken' => $token, 'CreateTime' => $this->share->util->getNow(),
            'TotalPrice' => $totalPrice, 'Type' => $orderType,
            'Payer' => $payer, 'PayerType' => $payerType,
            'InstallID' => $installerId, 'AreaManageID' => $disId,
            'FinalPrice' => $totalPrice, 'Months' => $month,
            'BmOrderNumber' => $bmOrderNumber, 'IsBatch' => $isBatch,
            'PayCode' => $code, 'NextTime' => $nextTime,
            'SubscriptionUUID' => $subscriptionUUID, 'PaypalOrder' => $paypalOrder,
            'Days' => $days, 'MixType' => $mixType, 'ProjectType' => 2
        ];
        $orderId = $this->dao->orderList->insert($orderParam);

        //插入
        foreach ($userCharges as $user) {
            $id = $user['ID'];
            $info = $usersInfo['all'][$id];
            $role = intval($info['Role']);
            $object = $role === COMENDMROLE ? $info['APT'] : $info['Name'];
            $amount = $this->share->util->inputComputedCount($user['ActiveFee']);

            $disCount = $user['ActiveFeePercent'];
            $orderEndUserType = PAY_SUB_TYPE['active'];

            // 混合激活时从账户要使用AddAppFee
            if (in_array($role, SUBROLE)) {
                $amount = $this->share->util->inputComputedCount($user['AddAppFee']);
                $disCount = $user['AddAppFeePercent'];
                $orderEndUserType = PAY_SUB_TYPE['buyOutApp'];
            } elseif ($role === PMENDMROLE) {
                $orderEndUserType = PAY_SUB_TYPE['activePM'];
            }

            $orderEndUserItem = [
                'OrderID' => $orderId, 'Type' => $orderEndUserType,
                'Amount' => $amount, 'AppID' => $id,
                'Object' => $object, 'Discount' => $disCount,
                'DiscountInfo' => $user['Introduction'],
                'ActivityUUID' => $user['ActivityId'], 'ChargeData' => json_encode($user),
                'ProjectUUID' => $info['ProjectUUID'], 'ProjectName' => $info['ProjectName'],
                'Days' => $user['Days'] ?: 0, 'SiteUUID' => ''
            ];
            $this->dao->orderEndUserList->insert($orderEndUserItem);
        }

        $thirdLockSite = $this->share->util->arrayColumnAsKey($thirdLocks['site'], 'UUID');
        $thirdLockConfig = $this->share->util->arrayColumnAsKey($thirdLocks['config'], 'LockUUID');
        foreach ($thirdLockCharge as $item) {
            $lockUUID = $item['UUID'];
            $config = $thirdLockConfig[$lockUUID];
            $siteUUID = $config['AccountUUID'];
            $projectUUID = $thirdLockSite[$siteUUID]['ProjectUUID'];
            $projectName = $thirdLockSite[$siteUUID]['ProjectName'];
            $amount = $this->share->util->inputComputedCount($item['ItemFee']);

            $orderThirdLockItem = [
                'UUID' => $this->share->util->uuid(),
                'OrderID' => $orderId,
                'ProjectUUID' => $projectUUID,
                'ProjectName' => $projectName,
                'PersonalAccountUUID' => $config['PersonalAccountUUID'],
                'Brand' => $config['Brand'],
                'LockUUID' => $lockUUID,
                'ServiceType' => PAY_SUB_LOCK_TYPE['communityThirdLockActive'],
                'Amount' => $amount,
                'Days' => $item['Days'] ?: 0,
                'ChargeData' => json_encode($item['Model']),
                'CommunityUnitUUID' => $config['CommunityUnitUUID'],
                'UnitName' => $config['UnitName'],
                'AptName' => $config['AptName'],
                'LockName' => $config['LockName'],
            ];

            $this->dao->orderThirdLockList->insert($orderThirdLockItem);
        }

        return [$orderId, $orderNumber];
    }
}
