<?php
  const MSGTEXT = [ 

"accountExits"=>"Аккаунт уже существует",
"accountNotExit"=>"Аккаунт не существует",
"accountIncorrect"=>"Неверный логин или пароль",
"accountNIncorrect"=>"Неверный аккаунт",
"activeEmpty"=>"Требуется активное значение",
"addFail"=>"Не удалось добавить",
"addSuccess"=>"Добавлено.",
"addSuccessPw"=>"Добавлено, Пароль '%s'.",
"addTmpKeyFail"=>"Не удалось добавить ВРЕМЕННЫЙ ключ, попробуйте снова",
"aptDuplicated"=>"%s квартира дублируется",
"aptDigits"=>"Неверный номер квартиры. Число должно содержать от 1 до 6 чисел",
"aptExit"=>"%s квартира уже существует",
"abnormal"=>"Нарушение",
"activation"=>"Активация",
"additionalApp"=>"Дополнительное приложение",
"bindDevice"=>"Удалите все устройства, относящиеся к этому аккаунту",
"bindMAClibrary"=>"Удалите MAC-адрес из библиотеки",
"bindUser"=>"Удалите пользователей, относящихся к этому аккаунту",
"buildingBindDevice"=>"Удалите устройства, относящиеся к этому зданию",
"buildingBindUser"=>"Удалите пользователей, относящихся к этому зданию",
"buildingDigits"=>"Неверный %s здания. Не более 2 числовых знаков",
"buildingExit"=>"Здание уже существует",
"BindingDeviceFailed"=>"Не удалось привязать устройство. Возможно устройство уже привязано к другому пользователю, или MAC устройства еще не добавлен в библиотеку",
"chcekMacExits"=>"Не удалось добавить, неверный или уже записанный MAC адрес",
"changePasswdFail"=>"Не удалось изменить пароль",
"changePasswdPEmail"=>"Пароль, изменен, проверьте почту %s",
"community"=>"Сообщество",
"deleteFail"=>"Не удалось удалить",
"deleteSuccess"=>"Удалено",
"deviceTypeEmpty"=>"Нужно указать тип устройства",
"deviceNotFindUser"=>"Устройство не найдено, свяжитесь с администратором",
"dealSuccess"=>"Добавлено",
"doorUnit"=>"Входная группа",
"emailExits"=>"Почта уже зарегистрирована",
"emailPExit"=>"Почта %s уже зарегистрирована",
"emailNotExits"=>"Почта не существует.",
"emailDuplicated"=>"Почта %s повторяется",
"errorVersion"=>"Версия ошибки",
"emailOrAccountNotExit"=>"Почта не существует.",
"firstNameEmpty"=>"Имя обязательно к заполнению",
"failed"=>"Не удалось",
"family"=>"Семья",
"guardPhone"=>"Телефон охраны",
"incorrectSipAccount"=>"Нет доступных SIP аккаунтов",
"incorrectSipAccountGroup"=>"Нет доступных SIP групп",
"importDataSuccess"=>"Данные импортированы",
"importFailMACExit"=>"Не удалось импортировать данные, проверьте доступность или существование MAC адреса:\r\n%s",
"invaildDC"=>"Неверный код устройства",
"InvalidFile"=>"Неверный файл",
"invalidPEmail"=>"Неверная почта %s",
"invalidPName"=>"Неверное имя пользователя %s",
"invalidPCalltype"=>"Неверный тип звонка %s",
"invalidPPin"=>"Неверный PIN %s",
"invalidPActive"=>"Неверное значение %s",
"invalidPage"=>"Неверная страница",
"invalidPDeviceType"=>"Неверный тип устройства %s",
"invaildVerCode"=>"Неверный код подтверждения",
"invalidIdentity"=>"Неверные данные распознавания! Возможно вы авторизовались где-то еще, повторите попытку.",
"indoorMonitor"=>"Внутридомовой монитор",
"inactivated"=>"Не активен",
"normal"=>"Норма",
"expired"=>"Срок истек",
"lastNameEmpty"=>"Фамилия обязательна к заполению",
"locationEmpty"=>"Местоположение обязательно к заполению",
"locationPLoog"=>"%s слишком большое описание местоположения",
"locationLoog"=>"Слишком большое описание местоположения",
"loginError"=>"Ошибка входа",
"loginFail"=>"Не удалось войти",
"loginSuccess"=>"Вход выполнен",
"limitIP"=>"Слишком много попыток, повторите через 5 минут",
"limitDevice"=>"Номер Вашего устройство достиг лимита",
"MAC2PLibrary"=>"MAC адрес %s не верен, проверьте библиотеку MAC-адресов.",
"MAC2Library"=>"MAC адрес не верен, проверьте библиотеку MAC-адресов.",
"macExits"=>"MAC адрес уже существует",
"MACLength"=>"Длина MAC адреса должна быть 12 знаков.",
"modifySuccess"=>"Изменено",
"modifyFailed"=>"Не удалось изменить",
"maxHouse"=>"Количество пользователей достигло максимума, свяжитесь с администратором",
"modifyAptFail"=>"Не удалось сохранить! КВРТ № уже существует. Нужно удалить уже существующую запись.",
"nameloog"=>"Слишком длинное имя пользователя. Имя должно содержать не более 64 символов",
"nameExit"=>"Имя пользователя уже существует",
"notPermission"=>"У вас не хватает прав",
"noSip"=>"Нет такого SIP аккаунта",
"passwordIncorrect"=>"Неверный пароль",
"passwdChangeSuccess"=>"Пароль изменен",
"passwordResetSuccess"=>"Пароль сброшен",
"passwordReset2"=>"Пароль был сброшен на '%s'.",
"payTimeOut"=>"Время оплаты истекло",
"payFailed"=>"Не удалось оплатить",
"processing"=>"Обрабатываю",
"paySuccess"=>"Оплачено",
"redirectedOnRPS"=>"Этот MAC адрес переправлен на RPS.",
"registerFailed"=>"Не удалось зарегистрироваться",
"registerSuccess"=>"Зарегистрировано",
"roomNotExit"=>"Такой пользователь не существует!",
"RFCardExit"=>"Такая RF-карта уже зарегистрирована",
"registered"=>"Зарегистрировано",
"PrivateKeyExists"=>"Этот личный ключ уже существует",
"passwordCorrect"=>"Неверный пароль",
"timeLessCurrent"=>"Неверное время обновления",
"timeZoneChangeSuccess"=>"Часовой пояс изменен",
"timeOut"=>"Время вышло",
"unbindMACUser"=>"Сначала нужно отвязать %s от пользователя",
"unKnowDT"=>"Неизвестный тип устройства",
"userBindUser"=>"Сначала нужно удалить пользователей, относящихся к этому аккаунту",
"userNotExit"=>"Такой пользователь не существует",
"userMaxPLimt"=>"Не удалось создать. В %s можно добавить только членов семьи",
"unregistered"=>"Не зарегистрирован",
"validMAC"=>"Введите верный MAC-адрес",
"versionExit"=>"Версия уже существует",
"versionNameNumberExit"=>"Имя или номер версии уже существует",
"sipStatus"=>"Не удалось создать SIP аккаунт, попробуйте снова",
"sentCodeLater"=>"Мы уже отправляли Вам проверочный код, попробуйте попытку позже.",
"setSuccess"=>"Установка завершена",
"sendEmailSuccess"=>"Email отправлен",
"SetFailed"=>"Сбой настройки",
"stairPhone"=>"Лестничный телефон",
"successed"=>"Удалось",
"subscription"=>"Подписка",
"wallPhone"=>"Настенный телефон",
"emailMaxLen"=>"Почта должна быть длиной не более 64 символов.",
"serverUpgradeTips"=>"Обновление сервера завершено. Перед тем, как обновить страницу рекомендуем скопировать Вашу введенную информацию в другое место.",
"ActiveFamilyAccount"=>"Сначала нужно активировать аккаунт главы семьи.",
"weekly"=>"Еженедельно",
"daily"=>"Каждый день",
"never"=>"Никогда",
"calltypeEmpty"=>"Нужно указать тип звонка",
"addOutApt"=>"Вы можете добавить до %s комнат",
"call"=>"Звонок",
"unlock"=>"Разблокировать",
"tryUnlockCall"=>"Открыто дверь с помощью звонка",
"tryUnlockKey"=>"Открыто дверь с помощью ключа",
"tryUnlockCard"=>"Открыто дверь с помощью RF-карты",
"tryUnlockFace"=>"Открыто дверь через распознавание лиц",
"unlockApp"=>"Открыто с помощью SmartPlus",
"unlockIndoor"=>"Открыто с помощью внутридомового монитора",
"unlockNFC"=>"Открыто с помощью NFC",
"unlockBluetooth"=>"Открыто с помощью Bluetooth",
"unlockCard"=>"Открыто с помощью RF-карты",
"unlockPrivateKey"=>"Открыто с помощью личного ключа",
"unlockTempKey"=>"Открыто с помощью временного ключа",
"alarmDoorUnlock"=>"Дверь разблокирована",
"alarmInfrared"=>"Инфракрасный датчик",
"alarmSmoke"=>"Дым",
"alarmGas"=>"Газ",
"alarmUrgency"=>"Срочность",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Несанкционированный доступ",
"alarmGate"=>"Ворота",
"alarmDoor"=>"Дверь",
"alarmBedroom"=>"Спальня",
"alarmGuestRoom"=>"Гостевая",
"alarmHall"=>"Зал",
"alarmWindow"=>"Окно",
"alarmBalcony"=>"Балкон",
"alarmKitchen"=>"Кухня",
"alarmStudy"=>"Кабинет",
"alarmBathroom"=>"Ванная",
"alarmArea"=>"Зона",
"RFCardExit2"=>"RF-карта %s уже существует",
"RFCardDuplicated"=>"RF-карта %s дублируется",
"notMacBind"=>"Пользователь '%s' не имеет прав на открытие двери с помощью устройства '%s'.",
"accountNumLet"=>"Имя аккаунта должно содержать цифры и буквы",
"networkUnavailable"=>"Сеть недоступна.",
"notForModel"=>"Не для этой модели.",
"upgradeDevVersion"=>"Сначала нужно обновиться до последней версии.",
"unavailableService"=>"Сервис временно недоступен, повторите попытку позже.",
"cantDeletePin"=>"Вы не можете удалить PIN %s",
"residentInRoom"=>"Житель уже в комнате %s",
"noAnswer"=>"Нет ответа",
"indoorAndApp"=>"Внутридомовой монитор и приложение(глава)",
"indoorMonitorOnly"=>"Только внутридомовой монитор",
"appOnly"=>"Только приложение",
"endThanStart"=>"Конечное время не может быть раньше начального времени.",
"endThanStartFile"=>"Некорректный день или время на строке '%s'.",
"doorRelease"=>"Открытие двери",
"success"=>"Успешно",
"unlockFACE"=>"Открыто по лицу",
"unlockBLE"=>"Открыто по BLE",
"captureSmartPlus"=>"Захват с помощью SmartPlus",
"drmagnet"=>"Магнитная дверь",
"failedUnlock"=>"Ошибка при открытии",
"deviceDisconnected"=>"Устройство отключено.",
"low"=>"Низкий",
"motion"=>"Движение",
"capture"=>"Захват",
"failedImport"=>"Ошибка импорта",
"notValidMobile"=>"%s неверный номер мобильного телефона.",
"mobileExits"=>"Мобильный номер уже существует",
"mobileExits2"=>"Мобильный номер %s уже существует",
"mobileDuplicated"=>"Мобильный номер %s повторяется",
"mobileNumberExist"=>"Мобильный номер не существует",
"codeIncorrect"=>"Неверный код",
"sendCodeSuccess"=>"Код подтверждения успешно отправлен",
"codeCorrect"=>"Верно",
"mobileNumberEmpty"=>"Введите номер мобильного телефона",
"invalidUser"=>"Неверный пользователь %s",
"locationExits"=>"Адрес местонахождения уже существует",
"smartPlusIndoor"=>"SmartPlus или внутридомовые мониторы",
"phoneIndoor"=>"Телефон или внутридомовые мониторы",
"smartPlusIndoorBackup"=>"SmartPlus или внутридомовые мониторы, телефон в последнюю очередь",
"smartPlusBackup"=>"Внутридомовые мониторы, SmartPlus в последнюю очередь",
"indoorPhoneBackup"=>"Внутридомовые мониторы, телефон в последнюю очередь",
"indoorSmartPlusPhone"=>"Внутридомовые мониторы или SmartPlus, телефон в последнюю очередь",
"endUser"=>"Конечный пользователь",
"installer"=>"Установщик",
"distributor"=>"Дистрибутор",
"pm"=>"Управляющий",
"superManage"=>"СуперМенеджер",
"loginManagement"=>"Управление входом в систему",
"accessControl"=>"Контроль доступа",
"userManagement"=>"Управление пользователями",
"deviceManagement"=>"Управление устройствами",
"communityManagement"=>"Управление сообществом",
"auditLogin"=>"Вход: Web",
"auditLogout"=>"Выход: Web",
"auditAddTempKey"=>"Добавить временный ключ: {0}",
"auditEditTempKey"=>"Редактировать временный ключ: {0}",
"auditDeleteTempKey"=>"Удалить временный ключ: {0}",
"auditAddRFCard"=>"Добавить RF карту: {0}",
"auditEditRFCard"=>"Редактировать RF карту: {0}",
"auditDeleteRFCard"=>"Удалить RF карту: {0}",
"auditAddDis"=>"Добавить дистрибутора: {0}",
"auditEditDis"=>"Редактировать дистрибутора: {0}",
"auditDeleteDis"=>"Удалить дистрибутора: {0}",
"auditAddInstaller"=>"Добавить установщика: {0}",
"auditEditInstaller"=>"Редактировать установщика: {0}",
"auditDeleteInstaller"=>"Удалить установщика: {0}",
"auditAddPM"=>"Добавить управляющего: {0}",
"auditEditPM"=>"Редактировать управляющего: {0}",
"auditDeletePM"=>"Удалить управляющего: {0}",
"auditAddEndUser"=>"Добавить конечного пользователя: {0}",
"auditEditEndUser"=>"Редактировать конечного пользователя: {0}",
"auditDeleteEndUser"=>"Удалить конечного пользователя: {0}",
"auditSetOwnerTime"=>"Задать собственный часовой пояс {0}",
"auditSetOwnPassword"=>"Задать собственный пароль",
"auditAddPIN"=>"Добавить PIN: {0} ",
"auditEditPIN"=>"Изменить PIN: {0} ",
"auditDeletePIN"=>"Удалить PIN: {0} ",
"auditImportFace"=>"Импортировать лицо: {0}",
"auditDeleteFace"=>"Удалить лицо: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Задать последовательность вызова SmartPlus и абонентский монитор: {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"Задать последовательность вызова: телефонный номер и абонентский монитор: {0}&{1} ",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Задать последовательность вызова: SmartPlus и абонентский монитор, телефонный номер как резерв: {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"Задать последовательность вызова: Абонентский монитор - SmartPlus как резерв: {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Задать последовательность вызова: Абонентский монитор - телефонный номер как резерв: {0}&{1} ",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Задать последовательность вызова: Абонентский монитор - SmartPlus как резерв, телефонный номер в последнюю очередь: {0}&{1} ",
"auditDeleteDevice"=>"Удалить устройство: {0}",
"auditSetAPTCount"=>"Задать количество помещений/квартир {0}",
"auditEnableLandline"=>"Включить услугу звонков на номера фиксированной связи",
"auditDisableLandline"=>"Отключить услугу звонков на номера фиксированной связи",
"auditSetSubTime"=>"Задать часовой пояс {0}",
"auditSetChargeModeInstall"=>"Задать модель начисления за услугу \"Оплата установщиком\"",
"auditSetChargeModeUser"=>"Задать модель начисления за услугу \"Оплата пользователем/УК\"",
"auditSetConnectTypeDefault"=>"Задать тип соединения по умолчанию",
"auditSetConnectTypeTCP"=>"Задать тип соединения tcp",
"auditSetConnectTypeUDP"=>"Задать тип соединения udp",
"auditSetConnectTypeTLS"=>"Задать тип соединения tls",
"auditAddCommunity"=>"Добавить сообщество: {0}",
"auditDeleteCommunity"=>"Удалить сообщество: {0}",
"auditImportCommunity"=>"Импортировать сообщество: {0}",
"auditSetAPTNumber"=>"Задать {0} номер квартиры {1} ",
"auditSetEmail"=>"Задать email {0}: {1}",
"auditSetMobile"=>"Задать номер телефона {0}: {1}",
"auditDeviceTypeStair"=>"Задать тип Многоабонентская ВП: {0}",
"auditDeviceTypeDoor"=>"Задать тип Одноабонентская ВП: {0}",
"auditDeviceTypeIndoor"=>"Задать тип Абонентский Монитор: {0}",
"auditDeviceTypeGuardPhone"=>"Задать тип Телефон Охраны: {0}",
"auditDeviceTypeAccessControl"=>"Задайте тип Устройство КД: {0}",
"auditSetNetGroup"=>"Задайте сетевую группу {0}: {1}",
"auditEditCommunity"=>"Редактировать сообщество",
"deliveryMsg"=>"Вам доставлено %s отправлений. Проверьте своевременно!",
"deliveryTitle"=>"Вам посылка!",
"rfcardDuplicatedLines"=>"Номер RF карты дублируется в строке %s!",
"rfcardNameInvalid"=>"Недопустимое имя RF карты в строке %s!",
"rfcardExistLines"=>"RF карта уже существует в строке %s.",
"importFailMacExistLines"=>"MAC адрес уже существует в в строке %s.",
"exportExcelCountNull"=>"Нет логов за выбранную дату. Выберите другую.",
"keyIsEqualRoom"=>"Код для курьера не может быть равным номеру квартиры",
"visitor"=>"Посетитель",
"CommunityNameExist"=>"Такое имя сообщества уже есть",
"unlockGuardPhone"=>"Разблокировка с телефона охраны",
"auditLoginApp"=>"Вход: Приложение",
"auditLogoutApp"=>"Выход: Приложение",
"timeForYesterday"=>"Вчера",
"exportExcelDataBefore"=>"Слишком большие данные! Пожалуйста, сначала экспортируйте данные до %s , спасибо",
"tempkeyUsed"=>"Использован временный ключ",
"tempkeyContent"=>" %s использовал временный ключ.",
"accessNameExist"=>"Такое название группы доступа уже существует",
"addFaceFail"=>"Добавьте четкое фото лица анфас",
"userInvalid"=>"Неверный пользователь в строке %s.",
"groupsInvalid"=>"Неверная группа доступа в строке %s.",
"BuildAccessName"=>"Жилой дом %s",
"auditCodeLogEditApt"=>"редактировать квартиру/помещение:{0}",
"invalidTimeInLine"=>"Неверное время в строке %s.",
"cancel"=>"Отмена",
"cancelSuccess"=>"Успешно отменено",
"payOutstanding"=>"Пожалуйста, проверьте, есть ли неоплаченные заказы, если нет, обратитесь к поставщику услуг.",
"featureDeleteError"=>"Имеются привязанные услуги",
"beyondFamilyMember"=>"Вы не можете создать больше аккаунтов членов Семьи. Свяжитесь с поставщиком услуги, если требуются дополнительные аккаунты.",
"indoorMonitorRequired"=>"Как минимум один абонентский монитор требуется для каждого помещения/квартиры",
"featureActivationFee"=>"Услуга (Единовременный платеж)",
"systemProcessing"=>"Обработка системой",
"featureMonthlyFee"=>"Услуга (Ежемесячный платеж)",
"featurePriceDifferences"=>"Услуга (Различия в цене)",
"updatingSuccess"=>"Успешно обновлено!",
"featureNameBasic"=>"Базовый",
"featureNamePremium"=>"Премиум",
"indoorMacNotCorrect"=>"Введите корректный MAC-адрес абонентского монитора",
"off"=>"Выкл",
"enterValidAccount"=>"Введите действующий аккаунт",
"invalidKitImportMAC"=>"Проверьте, что MAC-адрес существует и действителен: %s",
"importLessData"=>"Пожалуйста импортируйте менее %s информации",
"invalidQRCode"=>"Ошибка идентификации. Отсканируйте правильный QR-код",
"cannotCreateFamilyMember"=>"Вы не можете создать больше аккаунтов членов Семьи",
"importProcessing"=>"Импорт, пожалуйста, повторите попытку позже",
"departmentAccessName"=>"%s Группа доступа",
"idExistsLine"=>"ID уже существует в строке %s",
"enterFirstNameLine"=>"Введите имя в строке %s",
"enterLastNameLine"=>"Введите фамилию в строке %s",
"departmentExist"=>"Отдел уже существует",
"idExist"=>"ID уже существует",
"layoutIdInvalid"=>"Макет недействителен",
"unlockAppHome"=>"Разблокировка AKHome",
"officeNameExist"=>"Имя офиса уже существует",
"departmentExit"=>"Такой отдел уже существует.",
"importOutTask"=>"Вы можете импортировать только шаблон за один раз.",
"idDuplicated"=>"ID %s дублируется",
"aptInvalidLine"=>"Недопустимый КВРТ в строке %s.",
"buildInvalidLine"=>"Недопустимое здание в строке %s.",
"departmentInvalidLine"=>"Недопустимый отдел в строке %s.",
"idInvalidLine"=>"Недопустимый ID в стоке %s.",
"propertyManager"=>"Управляющая компания",
"departmentBindDevice"=>"Пожалуйста, удалите устройства из этого отдела.",
"departmentBindUser"=>"Пожалуйста, удалите пользователей из этого отдела.",
"smartPlusValidLine"=>"Недопустимая функция внутренней связи Smartplus в строке %s.",
"identityValidLine"=>"Недопустимый идентификатор в строке %s.",
"eachDoorCount"=>"Единый план, позволяющий открывать каждую дверь по одному разу",
"textUpgradeMsg1"=>"Пожалуйста, обновите версию приложения, чтобы продолжить.",
"textUpgradeMsg2"=>"Не удалось войти",
"deleteCodeGetLimitTimes"=>"Неверный ключ. Попробуйте снова через 24 часа.",
"deleteCodeOverLimitTimes"=>"Попробуйте снова через 24 часа.",
"deleteCodeError"=>"Неверный ключ",
"textUpgradeMsg"=>"1.Оптимизирована функция tempkey.;2.Добавлена функция отмены учетной записи.;3.Исправлены некоторые ошибки.",
"paramsError"=>"Ошибка параметра",
"pmappStatusInvalid"=>"Пожалуйста, сначала включите приложение PM.",
"delivery_description"=>"Ключ времени доставки",
"webRelayIDInvalidLine"=>"Недопустимый идентификатор веб-ретранслятора в строке %s.",
"relayInvalid"=>"Недопустимое реле в строке %s.",
"cancelError"=>"Не удалось отменить.",
"textUpgradeMsgForComRole"=>"Обновление функции сообщества",
"textUpgradeMsgForPerRole"=>"Обновление функции персонала",
"textUpgradeMsgForOffRole"=>"Обновление функции офиса",
"textUpgradeMsgForPMRole"=>"Обновление функции PM ",
"lockApp"=>"SmartPlus блокировка",
"lock"=>"Заблокировано",
"versionLogMaxLen"=>"Размер журнала версий не может превышать %s символов",
"autoLock"=>"Автоблокировка",
"pinAndRFcardNotNullLines"=>"Необходимо заполнить хотя бы один из ПИН-кодов и RF-карт в строке %s!",
"pinExistLines"=>"ПИН-код уже существовал в строке %s.",
"pinInvalidLines"=>"Неверный ПИН в строке %s!",
"pinDuplicatedLines"=>"Дублированный ПИН-код в строке %s!",
"FaceImportLength"=>"Размер файла импорта лица не может быть больше %s",
"landlineServerNotActivate"=>"Это сообщество не активировало стационарную службу.",
"importFailDisNotExist"=>"Дистрибьютор не существует",
"importFailNotPermission"=>"У вас нет разрешения на добавление этого MAC-адреса.",
"importFailTooManyAdd"=>"Сбой импорта, только для одного дистрибьютора.",
"importFailAdded"=>"Этот MAC-адрес уже был добавлен другим пользователем.",
"macAssignToLimit"=>"Вы можете назначить только до 10 дистрибьюторов",
"macNumToLimit"=>"Вы можете загружать только до 1000 MAC-адресов одновременно.",
"addOutFloor"=>"Введите число в диапазоне от 1 до 128.",
"floor"=>"Этаж",
"PostalCodeInvalid"=>"Введите букву или цифру.",
"onceCodeInvalid"=>"Постоянный код должен состоять из 4-5 цифр.",
"permanentCodeInvalid"=>"Постоянный код должен состоять из 6 цифр.",
"onceCodeOutNum"=>"Вы можете добавить только до 10 одноразовых кодов.",
"permanentCodeOutNum"=>"Вы можете добавить только до 10 постоянных кодов.",
"onceCodeExist"=>"Одноразовый код уже существует.",
"permanentCodeExist"=>"Постоянный код уже существует.",
"addOutFloorLine"=>"Недопустимый номер этажа в строке %s.",
"auditManuallyUnlock"=>"Ручная разблокировка",
"auditManuallyLock"=>"Ручная блокировка",
"automaticallyUnlock"=>"Авто разблокировка",
"doorClose"=>"Дверь закрыта",
"PostalCodeNotEmpty"=>"Пожалуйста, введите хотя бы одну букву или цифру.",
"emergencyAlarm"=>"Аварийная сигнализация",
"doorSensor"=>"Сенсор двери",
"yaleBatteryWarning"=>"Предупреждение о заряде батареи",
"auditCodeManuallyUnlock"=>"Ручная разблокировка",
"auditCodeManuallyLock"=>"Ручная блокировка",
"2weekBatteryWarning"=>"%s - Предполагаемое оставшееся время автономной работы: 2 недели.",
"1weekBatteryWarning"=>"%s - Предполагаемое оставшееся время автономной работы: 1 неделя.",
"replaceBatteryWarning"=>"%s - Уровень заряда батареи крайне низок, пожалуйста, немедленно замените его.",
"open"=>"Открыть",
"close"=>"Закрыть",
"addContactFavoriteNum"=>"Не удалось добавить в избранное.Вы можете добавить только до 300 любимых квартир.",
"addContactBlockNum"=>"Не удалось добавить в список блокировок.вы можете добавить в блок-лист только до 100 квартир.",
"voiceTitle"=>"Голосовое сообщение",
"voiceContent"=>"У вас есть голосовое сообщение от %s",
"voiceMsgInvalid"=>"Голосовое сообщение не существует.",
"toggleFeaturePlan"=>"Вы не можете изменить план функций.",
"rtspAddresEmpty"=>"Введите RTSP адрес.",
"rtspAddresInvalid"=>"Недопустимый RTSP адрес.",
"rtspPortEmpty"=>"Введите порт.",
"rtspPortInvalid"=>"Недопустимый порт.",
"rtspPassWdEmpty"=>"Введите пароль.",
"rtspPassWdInvalid"=>"Пароль слишком длинный, пароль может содержать до 63 символов.",
"cameraExist"=>"Камера уже существует.",
"errorOnRPS"=>"Не удалось запросить сервер RPS",
"faceImportErrorSystem"=>"Ошибка системы",
"faceImportErrorView"=>"Вид не спереди",
"faceImportErrorWearMask"=>"Обнаружена маска",
"faceImportErrorLowResolution"=>"Разрешение слишком низкое",
"faceImportErrorWrongFormat"=>"Ошибка формата файла",
"faceImportErrorNoFace"=>"Лицо не обнаружено",
"faceImportErrorFileLarge"=>"Файл слишком большой",
"faceImportErrorFaceLarge"=>"Лицо слишком близко",
"faceImportErrorFaceSmall"=>"Лицо слишком далеко",
"faceImportErrorMultiFaces"=>"Более чем одно лицо",
"faceImportErrorWrongName"=>"Ошибочное имя файла.",
"faceImportErrorEmptyName"=>"Имя жителя пусто.",
"faceImportErrorNoAccountInfo"=>"Ошибка получения информации о личном счете.",
"faceImportErrorAccountInactive"=>"Личный счет не активен.",
"changeHomeFeatureInvalid"=>"Операция не удалась! Есть установщики этого дистрибьютора, использующие функцию 'Домашней автоматизации'.",
"changeInterComFeatureInvalid"=>"Операция не удалась! Есть установщики этого дистрибьютора, использующие функцию 'Домофон'.",
"offline"=>"Ошибка: нет соединения",
"allFloors"=>"Все этажи",
"uploadOversize"=>"Размер файла загрузки не может быть больше, чем %s",
"uploadInvalidType"=>"Загруженный тип файла не поддерживается",
"uploadFailed"=>"Загрузка не удалась, попробуйте позже",
"uploadScreenSaverImgTooMuch"=>"Фотографии Screensaver не могут быть более чем %S!",
"screenSaverImgTooLittle"=>"Картинки Screensaver не могут быть меньше, чем %s!",
"screenSaverImgTooMuch"=>"Фотографии Screensaver не могут быть более чем %S!",
"screenSaverDevicesOffline"=>"Сохранить не удалось.",
"saveFailed"=>"Сохранить не удалось.",
"importingInProgress"=>"Импорт в процессе, попробуйте еще раз позже.",
"importBuildingInvalidLine"=>"Недействительное здание в линии %s",
"importAptInvalidLine"=>"Недействительная подтяжка в линии %s",
"importAccountTypeInvalidLine"=>"Неверный тип учетной записи в линии %s",
"importFirstNameInvalidLine"=>"Неверное имя в линии %s",
"importLastNameInvalidLine"=>"Неверная фамилия в линии %s",
"importKeyInvalidLine"=>"Неверный ключ в линии %s",
"importKeyExistsLine"=>"Пин существует в линии %s",
"importCardInvalidLine"=>"Неверная радиочастотная карта в линии %s",
"importCardExistsLine"=>"РЧ -карта существует в линии %s",
"importAccessGroupInvalidLine"=>"Неверный идентификатор группы доступа в линии %s",
"importAccessGroupNoPermissionLine"=>"Нет идентификатора группы доступа разрешений в линии %s",
"importExceededNumberLine"=>"Превысил количество членов семьи в линии %s",
"importNoActiveMasterLine"=>"Импорт не удался в линии %s, пожалуйста, активируйте семейство Matser в первую очередь.",
"importMasterExistsLine"=>"Семейный мастер уже существует в линии %с.",
"importNoCreateMasterLine"=>"Импорт не удался в линии %s, сначала создайте семейный Matser.",
"PrivateKeysDataExist"=>"Частный ключ %уже существует.",
"PrivateKeyDataExists"=>"Частный ключ %уже существует.",
"landLineOpenToClosedFail"=>"Сохранить не удалось.",
"limitWithIp"=>"Вы стараетесь слишком часто, попробуйте еще раз через 5 минут. (IP: %s)",
"subDistributor"=>"Субпродаж",
"faceImportErrorNotClear"=>"Импортированная картина не ясна.",


  ];
