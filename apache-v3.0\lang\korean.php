<?php
  const MSGTEXT = [ 

"accountExits"=>"계정이 이미 존재합니다",
"accountNotExit"=>"계정이 존재하지 않습니다",
"accountIncorrect"=>"잘못된 사용자 이름 혹은 비밀번호",
"accountNIncorrect"=>"잘못된 계정",
"activeEmpty"=>"활성 값은 필수입니다",
"addFail"=>"추가 실패",
"addSuccess"=>"추가 성공.",
"addSuccessPw"=>"추가 성공,비밀번호는 '%s' 입니다.",
"addTmpKeyFail"=>"임시 키 추가 실패, 다시 시도해 주세요",
"aptDuplicated"=>"아파트 %s 가 중복되었습니다",
"aptDigits"=>"아파트 %s 가 유효하지 않습니다, 1~6자리 숫자여야 합니다.",
"aptExit"=>"아파트 %s 가 이미 존재합니다",
"abnormal"=>"비정상",
"activation"=>"활성화",
"additionalApp"=>"추가 앱",
"bindDevice"=>"이 계정에 디바이스를 삭제하세요",
"bindMAClibrary"=>"MAC 라이브러리에 MAC을 삭제하세요",
"bindUser"=>"이 계정에 사용자를 삭제하세요",
"buildingBindDevice"=>"이 APT에 디바이스를 삭제하세요",
"buildingBindUser"=>"이 APT에 사용자를 삭제하세요",
"buildingDigits"=>"APT %s 이 잘못되었습니다, 1~2자리 숫자여야 합니다",
"buildingExit"=>"빌딩이 이미 존재합니다",
"BindingDeviceFailed"=>"디바이스 바인드에 실패, 장치가 다른 사용자에게 결합되었거나 MAC 라이브러리에 추가되지 않았을 수 있습니다.",
"chcekMacExits"=>"추가 실패, MAC 주소가 잘못되었거나 이미 존재합니다",
"changePasswdFail"=>"비밀번호 수정 실패",
"changePasswdPEmail"=>"비밀번호 수정에 성공했습니다, 이메일 %s 에서 확인하세요",
"community"=>"커뮤니티",
"deleteFail"=>"삭제 실패",
"deleteSuccess"=>"삭제 성공",
"deviceTypeEmpty"=>"디바이스 유형은 필수입니다",
"deviceNotFindUser"=>"장치를 찾을 수 없습니다, 관리자에게 문의하세요",
"dealSuccess"=>"설정 성공",
"doorUnit"=>"도어 유닛",
"emailExits"=>"이메일이 이미 존재합니다",
"emailPExit"=>"이메일 %s 가 이미 존재합니다",
"emailNotExits"=>"존재하지 않는 이메일입니다.",
"emailDuplicated"=>"이메일 %s 가 중복되었습니다",
"errorVersion"=>"오류 버전",
"emailOrAccountNotExit"=>"존재하지 않는 이메일입니다.",
"firstNameEmpty"=>"이름은 필수입니다",
"failed"=>"실패",
"family"=>"가족",
"guardPhone"=>"경비실 전화기",
"incorrectSipAccount"=>"더 이상 사용 가능한 SIP 계정이 없습니다",
"incorrectSipAccountGroup"=>"더 이상 사용 가능한 SIP 그룹이 없습니다",
"importDataSuccess"=>"데이터 가져오기 성공",
"importFailMACExit"=>"가져오기에 실패했습니다. MAC 주소가 존재하는지 또는 유효한지 확인하세요:\r\n%s",
"invaildDC"=>"잘못된 디바이스 코드",
"InvalidFile"=>"잘못된 파일",
"invalidPEmail"=>"잘못된 이메일 %s",
"invalidPName"=>"잘못된 사용자 이름 %s",
"invalidPCalltype"=>"잘못된 통화 유형 %s",
"invalidPPin"=>"잘못된 PIN %s",
"invalidPActive"=>"잘못된 활성 값 %s",
"invalidPage"=>"잘못된 페이지",
"invalidPDeviceType"=>"잘못된 디바이스 유형 %s",
"invaildVerCode"=>"잘못된 인증코드",
"invalidIdentity"=>"잘못된 신원 정보입니다! 다른 곳에서 로그인했을 수 있습니다, 다시 로그인하십시오.",
"indoorMonitor"=>"실내 모니터",
"inactivated"=>"비활성화됨",
"normal"=>"정상",
"expired"=>"만료",
"lastNameEmpty"=>"성은 필수입니다",
"locationEmpty"=>"장소는 필수입니다",
"locationPLoog"=>"%s 장소가 너무 깁니다",
"locationLoog"=>"장소가 너무 깁니다",
"loginError"=>"로그인 오류",
"loginFail"=>"로그인 실패",
"loginSuccess"=>"로그인 성공",
"limitIP"=>"너무 자주 시도하고 있습니다, 5분 후에 다시 시도하십시오",
"limitDevice"=>"디바이스 수가 최대 한도에 도달했습니다",
"MAC2PLibrary"=>"MAC 주소:%s 가 잘못되었습니다. MAC 라이브러리를 확인하십시오.",
"MAC2Library"=>"MAC 주소가 잘못되었습니다. MAC 라이브러리를 확인하십시오",
"macExits"=>"MAC 주소가 이미 존재합니다",
"MACLength"=>"MAC 주소 길이는 12자리여야 합니다.",
"modifySuccess"=>"수정 성공",
"modifyFailed"=>"수정 실패",
"maxHouse"=>"사용자 수가 최대 한도에 도달했습니다. 관리자에게 문의하세요",
"modifyAptFail"=>"저장 실패! APT No.가 이미 존재합니다, 삭제를 먼저 해야합니다.",
"nameloog"=>"사용자 이름이 너무 깁니다. 사용자 이름은 최대 64자까지 가능합니다.",
"nameExit"=>"사용자 이름이 이미 존재합니다",
"notPermission"=>"작동 권한이 없습니다",
"noSip"=>"더 이상 SIP 계정이 없습니다",
"passwordIncorrect"=>"비밀번호 잘못됨",
"passwdChangeSuccess"=>"비밀번호 변경 성공",
"passwordResetSuccess"=>"비밀번호 재설정 성공",
"passwordReset2"=>"비밀번호가 '%s' 로 재설정되었습니다.",
"payTimeOut"=>"결제 시간초과",
"payFailed"=>"결제 실패",
"processing"=>"처리중",
"paySuccess"=>"결제 성공",
"redirectedOnRPS"=>"이 MAC 주소는 RPS에서 리디렉션됩니다.",
"registerFailed"=>"등록 실패",
"registerSuccess"=>"등록 성공",
"roomNotExit"=>"존재하지 않는 사용자입니다!",
"RFCardExit"=>"이 RF카드는 이미 존재합니다",
"registered"=>"등록됨",
"PrivateKeyExists"=>"이 개인 키는 이미 존재합니다",
"passwordCorrect"=>"잘못된 비밀번호",
"timeLessCurrent"=>"잘못된 업데이트 시간",
"timeZoneChangeSuccess"=>"시간대 변경 성공",
"timeOut"=>"시간초과",
"unbindMACUser"=>"사용자와 %s 의 바인딩을 먼저 해제하세요",
"unKnowDT"=>"알 수 없는 디바이스 유형",
"userBindUser"=>"먼저 이 계정의 사용자를 삭제하세요",
"userNotExit"=>"이 사용자는 존재하지 않습니다",
"userMaxPLimt"=>"생성에 실패했습니다. 최대 %s 명의 가족 구성원만 추가할 수 있습니다.",
"unregistered"=>"미등록",
"validMAC"=>"올바른 MAC 주소를 입력하세요",
"versionExit"=>"버전이 이미 존재합니다",
"versionNameNumberExit"=>"버전 이름 또는 번호가 이미 존재합니다.",
"sipStatus"=>"SIP 계정 할당에 실패했습니다. 다시 시도하십시오.",
"sentCodeLater"=>"인증 코드를 보냈습니다. 나중에 다시 시도해 주세요.",
"setSuccess"=>"설정 성공",
"sendEmailSuccess"=>"이메일 전송 성공",
"SetFailed"=>"설정 실패",
"stairPhone"=>"계단폰",
"successed"=>"성공",
"subscription"=>"구독",
"wallPhone"=>"벽 타입 전화",
"emailMaxLen"=>"이메일은 64자 미만이어야 합니다.",
"serverUpgradeTips"=>"서버 업그레이드가 완료되었습니다. 페이지를 새로고침하세요. 그 전에 입력한 데이터를 다른 곳에 복사할 수 있습니다.",
"ActiveFamilyAccount"=>"세대주의 계정을 먼저 활성화 하세요.",
"weekly"=>"주간",
"daily"=>"매일",
"never"=>"반복안함",
"calltypeEmpty"=>"통화타입은 필수입니다",
"addOutApt"=>"최대 %s 개의 방만 추가할 수 있습니다",
"call"=>"통화",
"unlock"=>"문열림",
"tryUnlockCall"=>"통화 문열림 실패",
"tryUnlockKey"=>"PIN 코드 문열림 실패",
"tryUnlockCard"=>"RF카드 문열림 실패",
"tryUnlockFace"=>"얼굴인식 문열림 실패",
"unlockApp"=>"앱으로 문열림",
"unlockIndoor"=>"실내 모니터로 문열림",
"unlockNFC"=>"NFC로 문열림",
"unlockBluetooth"=>"블루투스로 문열림",
"unlockCard"=>"RF카드로 문열림",
"unlockPrivateKey"=>"PIN 코드로 문열림",
"unlockTempKey"=>"임시 키로 문열림",
"alarmDoorUnlock"=>"문열림",
"alarmInfrared"=>"적외선",
"alarmSmoke"=>"연기",
"alarmGas"=>"가스",
"alarmUrgency"=>"긴급",
"alarmSOS"=>"SOS",
"alarmTamper"=>"알람경보",
"alarmGate"=>"게이트",
"alarmDoor"=>"도어",
"alarmBedroom"=>"침실",
"alarmGuestRoom"=>"게스트 룸",
"alarmHall"=>"홀",
"alarmWindow"=>"창문",
"alarmBalcony"=>"발코니",
"alarmKitchen"=>"부엌",
"alarmStudy"=>"알람인지",
"alarmBathroom"=>"화장실",
"alarmArea"=>"구역",
"RFCardExit2"=>"RF카드 %s 가 이미 존재합니다",
"RFCardDuplicated"=>"RF카드 %s 가 중복되었습니다.",
"notMacBind"=>"사용자 '%s' 는 '%s' 디바이스와 연결된 문을 열 권한이 없습니다 .",
"accountNumLet"=>"계정은 숫자와 문자로 구성되어야 합니다.",
"networkUnavailable"=>"사용할 수 없는 네트워크.",
"notForModel"=>"이 모델에는 해당되지 않습니다.",
"upgradeDevVersion"=>"최신 버전으로 업그레이드 하세요.",
"unavailableService"=>"서비스를 일시적으로 사용할 수 없습니다. 나중에 다시 시도해 주세요.",
"cantDeletePin"=>"pin을 삭제할 수 없습니다 %s",
"residentInRoom"=>"방에 이미 거주자가 있습니다 %s",
"noAnswer"=>"무응답",
"indoorAndApp"=>"실내모니터와  앱(마스터)",
"indoorMonitorOnly"=>"실내모니터",
"appOnly"=>"앱(마스터)",
"endThanStart"=>"종료 시간은 시작 시간보다 빠를 수 없습니다.",
"endThanStartFile"=>"날짜 또는 시간이 잘못되었습니다 '%s'.",
"doorRelease"=>"문열기",
"success"=>"성공",
"unlockFACE"=>"얼굴인식 문열림",
"unlockBLE"=>"블루투스 문열림",
"captureSmartPlus"=>"스마트플러스에서 캡처",
"drmagnet"=>"Drmagnet",
"failedUnlock"=>"문열림 실패",
"deviceDisconnected"=>"디바이스 연결이 끊어졌습니다.",
"low"=>"낮음",
"motion"=>"모션",
"capture"=>"캡처",
"failedImport"=>"불러오기 실패",
"notValidMobile"=>"%s 는 올바른 휴대폰 번호가 아닙니다.",
"mobileExits"=>"휴대폰 번호가 이미 존재합니다",
"mobileExits2"=>"휴대폰 번호 %s 가 존재합니다",
"mobileDuplicated"=>"휴대폰 번호 %s 가 중복되었습니다",
"mobileNumberExist"=>"휴대폰 번호가 존재하지 않습니다.",
"codeIncorrect"=>"잘못된 코드",
"sendCodeSuccess"=>"인증 코드를 성공적으로 전송",
"codeCorrect"=>"코드 수정",
"mobileNumberEmpty"=>"휴대폰 번호를 입력해 주세요.",
"invalidUser"=>"사용할 수 없는 사용자 %s",
"locationExits"=>"위치 주소가 이미 존재합니다",
"smartPlusIndoor"=>"SmartPlus와 실내 모니터로 설정",
"phoneIndoor"=>"유선전화와 실내 모니터로 설정",
"smartPlusIndoorBackup"=>"SmartPlus -> 실내 모니터-> 유선전화로 백업으로 설정",
"smartPlusBackup"=>"실내 모니터-> SmartPlus 백업으로 설정",
"indoorPhoneBackup"=>"실내 모니터-> 유선전화로 백업으로 설정",
"indoorSmartPlusPhone"=>"실내 모니터-> smartPlus -> 유선전화로 백업으로 설정",
"endUser"=>"사용자",
"installer"=>"인스톨러",
"distributor"=>"공급자(D/B)",
"pm"=>"오후",
"superManage"=>"SM(시스템관리자)",
"loginManagement"=>"로그인 관리",
"accessControl"=>"액세스 제어",
"userManagement"=>"사용자 관리",
"deviceManagement"=>"디바이스 관리",
"communityManagement"=>"커뮤니티 관리",
"auditLogin"=>"로그인: 웹",
"auditLogout"=>"로그아웃: 웹",
"auditAddTempKey"=>"PIN(임시키) 추가: {0}",
"auditEditTempKey"=>"PIN(임시키) 수정: {0}",
"auditDeleteTempKey"=>"PIN(임시키) 삭제: {0}",
"auditAddRFCard"=>"RF카드 추가: {0}",
"auditEditRFCard"=>"RF카드 수정: {0}",
"auditDeleteRFCard"=>"RF카드 삭제: {0}",
"auditAddDis"=>"공급자(D/B) 추가: {0}",
"auditEditDis"=>"공급자(D/B) 수정: {0}",
"auditDeleteDis"=>"공급자(D/B) 삭제: {0}",
"auditAddInstaller"=>"인스톨러 추가: {0}",
"auditEditInstaller"=>"인스톨러 수정: {0}",
"auditDeleteInstaller"=>"인스톨러 삭제: {0}",
"auditAddPM"=>"PM(건물관리자) 추가: {0}",
"auditEditPM"=>"PM(건물관리자) 수정: {0}",
"auditDeletePM"=>"PM(건물관리자) 삭제: {0}",
"auditAddEndUser"=>"사용자 추가: {0}",
"auditEditEndUser"=>"사용자 수정: {0}",
"auditDeleteEndUser"=>"사용자 삭제: {0}",
"auditSetOwnerTime"=>"자신의 시간대 설정 {0}",
"auditSetOwnPassword"=>"자신의 비밀번호 설정",
"auditAddPIN"=>"PIN 추가: {0}",
"auditEditPIN"=>"PIN 수정: {0}",
"auditDeletePIN"=>"PIN 삭제: {0}",
"auditImportFace"=>"얼굴 가져오기: {0}",
"auditDeleteFace"=>"얼굴 삭제: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"통화 타입 설정: smartPlus와 실내 모니터로 설정: {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"통화 타입 설정: 유선전화와 실내 모니터로 설정: {0}&{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"통화 타입 설정: SmartPlus -> 실내 모니터 -> 유선전화로 백업으로 설정: {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"통화 타입 설정: 실내 모니터-> SmartPlus 백업으로 설정: {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"통화 타입 설정: 실내 모니터-> 유선전화로 백업으로 설정: {0}&{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"통화 타입 설정: 실내 모니터->  SmartPlus-> 유선전화로 백업으로 설정: {0}&{1}",
"auditDeleteDevice"=>"디바이스 삭제: {0}",
"auditSetAPTCount"=>"APT 수 설정 {0}",
"auditEnableLandline"=>"유선 서비스 활성화",
"auditDisableLandline"=>"유선 서비스 비활성화",
"auditSetSubTime"=>"표준 시간 설정 {0}",
"auditSetChargeModeInstall"=>"인스톨러에 의한 지불 모드 설정",
"auditSetChargeModeUser"=>"사용자/PM의한 지불 모드 설정",
"auditSetConnectTypeDefault"=>"기본 연결타입 설정",
"auditSetConnectTypeTCP"=>"TCP연결타입 설정",
"auditSetConnectTypeUDP"=>"UDP 연결타입설정",
"auditSetConnectTypeTLS"=>"TLS연결타입 설정",
"auditAddCommunity"=>"커뮤니티 추가: {0}",
"auditDeleteCommunity"=>"커뮤니티 삭제: {0}",
"auditImportCommunity"=>"커뮤니티 가져오기: {0}",
"auditSetAPTNumber"=>"{0}Room 번호 {1} 설정",
"auditSetEmail"=>"이메일 설정 {0}: {1}",
"auditSetMobile"=>"전화번호(유료) 설정 {0}: {1}",
"auditDeviceTypeStair"=>"멀티 테넌트 도어폰 타입 설정: {0}",
"auditDeviceTypeDoor"=>"싱글 테넌트  도어폰 타입 설정: {0}",
"auditDeviceTypeIndoor"=>"실내 모니터타입 설정: {0}",
"auditDeviceTypeGuardPhone"=>"경비 전화 타입 설정: {0}",
"auditDeviceTypeAccessControl"=>"액세스 제어타입 설정: {0}",
"auditSetNetGroup"=>"네트워크 그룹 {0} 설정: {1}",
"auditEditCommunity"=>"커뮤니티 수정",
"deliveryMsg"=>"전달된 항목이 %s개 있습니다. 시간을 확인하세요",
"deliveryTitle"=>"새 패키지가 생겼습니다!",
"rfcardDuplicatedLines"=>"%s라인에 RF 카드 번호가 중복되었습니다!",
"rfcardNameInvalid"=>"%s 라인에 잘못된 RF 카드 이름이 있습니다!",
"rfcardExistLines"=>"RF 카드는 %s 라인에 이미 존재합니다.",
"importFailMacExistLines"=>"MAC 주소가 %s라인에 이미 존재하거나 유효합니다.",
"exportExcelCountNull"=>"해당 날짜의 로그 데이터가 없습니다! 다시 선택하세요.",
"keyIsEqualRoom"=>"PIN(임시키)는 APT 번호와 동일할 수 없습니다!",
"visitor"=>"방문자",
"CommunityNameExist"=>"커뮤니티 이름이 이미 존재합니다.",
"unlockGuardPhone"=>"경비실 폰 잠금 해제",
"auditLoginApp"=>"로그인: 앱",
"auditLogoutApp"=>"로그아웃: 앱",
"timeForYesterday"=>"어제",
"exportExcelDataBefore"=>"너무 큰 데이터! 먼저 %s 전에 데이터 용량를 체크 하세요.",
"tempkeyUsed"=>"사용된 임시키",
"tempkeyContent"=>"%s이(가) 임시키를 사용했습니다.",
"accessNameExist"=>"액세스 그룹명이 이미 존재합니다.",
"addFaceFail"=>"선명한 얼굴 사진을 입력해 주세요.",
"userInvalid"=>"%s 라인에 잘못된 사용자가 있습니다.",
"groupsInvalid"=>"%s 라인에 잘못된 액세스 그룹이 있습니다.",
"BuildAccessName"=>"입주민 빌딩 %s",
"auditCodeLogEditApt"=>"아파트 편집:{0}",
"invalidTimeInLine"=>"%s라인의 시간이 잘못되었습니다.",
"cancel"=>"취소",
"cancelSuccess"=>"취소 되었습니다.",
"payOutstanding"=>"미결제 주문이 있는지 확인하고, 없으면 서비스 제공업체(담인 1877-5369)에 문의하세요.",
"featureDeleteError"=>"부가기능이 결합됩니다.",
"beyondFamilyMember"=>"더 이상 가족 구성원 계정을 만들 수 없습니다. (주)담인 고객센타(1877-5369) 문의하여 계정을 만드세요.",
"indoorMonitorRequired"=>"각 세대에는 하나의 실내 모니터가 필요합니다.",
"featureActivationFee"=>"기능(1회 요금)",
"systemProcessing"=>"시스템 처리",
"featureMonthlyFee"=>"특징(월 이용료)",
"featurePriceDifferences"=>"기능(가격차이)",
"updatingSuccess"=>"업데이트 성공!",
"featureNameBasic"=>"베이직",
"featureNamePremium"=>"프리미엄",
"indoorMacNotCorrect"=>"올바른 실내 모니터의 MAC번호을 입력하세요.",
"off"=>"Off",
"enterValidAccount"=>"유효한 계정을 입력하세요.",
"invalidKitImportMAC"=>"MAC이 존재하거나 유효한지 확인하세요: %s",
"importLessData"=>"%s 미만의 데이터를 가져오세요.",
"invalidQRCode"=>"식별 실패, 올바른 QR 코드를 스캔하세요.",
"cannotCreateFamilyMember"=>"가족 구성원 계정을 더 만들 수 없습니다.",
"importProcessing"=>"가져오는 중입니다. 나중에 다시 시도해 주세요.",
"departmentAccessName"=>"%s액세스 그룹",
"idExistsLine"=>"%s라인에 ID가 존재합니다.",
"enterFirstNameLine"=>"%s라인에 이름을 입력하세요",
"enterLastNameLine"=>"%s 라인에 성을 입력하세요",
"departmentExist"=>"소속이 이미 존재합니다.",
"idExist"=>"아이디가 이미 존재합니다",
"layoutIdInvalid"=>"레이아웃이 잘못되었습니다.",
"unlockAppHome"=>"AK홈 문 열림",
"officeNameExist"=>"이름이 이미 존재합니다.",
"departmentExit"=>"소속이 이미 존재합니다.",
"importOutTask"=>"한 번에 템플릿만 가져올 수 있습니다.",
"idDuplicated"=>"ID %s이(가) 중복되었습니다.",
"aptInvalidLine"=>"%s라인에 잘못된 APT가 있습니다.",
"buildInvalidLine"=>"%s 라인에 잘못된 건물이 있습니다.",
"departmentInvalidLine"=>"%s라인에 소속이 잘못되었습니다.",
"idInvalidLine"=>"%s 라인에 잘못된 ID가 있습니다.",
"propertyManager"=>"PM(건물관리자)",
"departmentBindDevice"=>"이 소속에 속한 장치를 삭제하세요.",
"departmentBindUser"=>"해당 소속의 사용자를 삭제하세요.",
"smartPlusValidLine"=>"%s 라인에 잘못된 Smartplus 인터콤 기능이 있습니다.",
"identityValidLine"=>"%s라인에  ID 에러.",
"eachDoorCount"=>"각 도어을 한 번씩 열겠다는 단일 플랜",
"textUpgradeMsg1"=>"계속하려면 앱 버전을 업데이트하세요.",
"textUpgradeMsg2"=>"로그인 실패",
"deleteCodeGetLimitTimes"=>"유효하지 않은 키.  24시간 후에 다시 시도하세요.",
"deleteCodeOverLimitTimes"=>"24시간 후에 다시 시도해 주세요.",
"deleteCodeError"=>"잘못된 키",
"textUpgradeMsg"=>"블루투스 핸즈프리 기능 추가.;2.일부 버그 수정.",
"paramsError"=>"매개변수 오류",
"pmappStatusInvalid"=>"먼저 PM 앱을 활성화하세요.",
"delivery_description"=>"배달 임시 키",
"webRelayIDInvalidLine"=>"%s 줄에 잘못된 웹 릴레이 ID가 있습니다.",
"relayInvalid"=>"%s 라인에 잘못된 릴레이가 있습니다.",
"cancelError"=>"취소 실패!",
"textUpgradeMsgForComRole"=>"커뮤니티 역할 업그레이드",
"textUpgradeMsgForPerRole"=>"개인사용자 역할 업그레이드",
"textUpgradeMsgForOffRole"=>"관리자 역할 업그레이드",
"textUpgradeMsgForPMRole"=>"업그레이드 PM 역활",
"lockApp"=>"스마트플러스 락",
"lock"=>"잠금",
"versionLogMaxLen"=>"버전 로그는 %s자를 초과할 수 없습니다.",
"autoLock"=>"자동 잠금",
"pinAndRFcardNotNullLines"=>"%s 행의 PIN 및 RF 카드 중 하나 이상을 입력해야 합니다!",
"pinExistLines"=>"PIN이 이미 %s 줄에 있습니다.",
"pinInvalidLines"=>"라인 %s에 잘못된 PIN이 있습니다!",
"pinDuplicatedLines"=>"라인 %s에 중복된 PIN이 있습니다!",
"FaceImportLength"=>"얼굴 파일의 크기는 %s보다 클 수 없습니다.",
"landlineServerNotActivate"=>"이 커뮤니티는 유선 서비스를 활성화하지 않았습니다.",
"importFailDisNotExist"=>"배포자가 존재하지 않습니다",
"importFailNotPermission"=>"이 MAC 주소를 추가할 권한이 없습니다.",
"importFailTooManyAdd"=>"단일 배포자에 대해서,  가져오기에 실패했습니다.",
"importFailAdded"=>"이 MAC 주소는 다른 사용자가 이미 추가했습니다.",
"macAssignToLimit"=>"최대 10개까지 배포자를 할당할 수 있습니다.",
"macNumToLimit"=>"한 번에 최대 1000개의 MAC 주소만 업로드할 수 있습니다.",
"addOutFloor"=>"1~128 사이의 숫자를 입력하세요.",
"floor"=>"층",
"PostalCodeInvalid"=>"문자 또는 숫자를 입력하세요.",
"onceCodeInvalid"=>"1회 코드는 4~5자리여야 합니다.",
"permanentCodeInvalid"=>"영구 코드는 6자리여야 합니다.",
"onceCodeOutNum"=>"일회 코드는  최대 10개까지만 추가할 수 있습니다.",
"permanentCodeOutNum"=>"최대 10개의 영구 코드만 추가할 수 있습니다.",
"onceCodeExist"=>"1회 코드가 이미 존재합니다.",
"permanentCodeExist"=>"영구 코드가 이미 존재합니다.",
"addOutFloorLine"=>"%s 라인에 잘못된 층 번호입니다.",
"auditManuallyUnlock"=>"수동으로 잠금 해제",
"auditManuallyLock"=>"수동으로 잠금",
"automaticallyUnlock"=>"자동으로 잠금 해제",
"doorClose"=>"문 닫기",
"PostalCodeNotEmpty"=>"하나 이상의 문자 또는 숫자를 입력하세요.",
"emergencyAlarm"=>"비상 경보",
"doorSensor"=>"도어 센서",
"yaleBatteryWarning"=>"Yale 배터리 경고",
"auditCodeManuallyUnlock"=>"수동으로 잠금 해제",
"auditCodeManuallyLock"=>"수동 잠금",
"2weekBatteryWarning"=>"%s - 남은 예상 배터리 시간: 2주.",
"1weekBatteryWarning"=>"남은 예상 배터리 시간: 1주.",
"replaceBatteryWarning"=>"%s - 배터리 레벨이 매우 낮습니다. 즉시 교체하세요.",
"open"=>"오픈",
"close"=>"클로즈",
"addContactFavoriteNum"=>"즐겨찾기에 추가하지 못했습니다. 즐겨찾기 아파트는 최대 300개까지만 추가할 수 있습니다.",
"addContactBlockNum"=>"차단 목록에 추가 실패. 차단 목록에는 최대 100개의 아파트까지 추가할 수 있습니다.",
"voiceTitle"=>"음성 메시지",
"voiceContent"=>"%s의 음성 메시지가 있습니다.",
"voiceMsgInvalid"=>"음성 메시지가 만료되었습니다.",
"toggleFeaturePlan"=>"기능 계획은 변경할 수 없습니다.",
"rtspAddresEmpty"=>"RTSP 주소를 입력하세요.",
"rtspAddresInvalid"=>"잘못된 RTSP 주소.",
"rtspPortEmpty"=>"포트를 입력하세요.",
"rtspPortInvalid"=>"잘못된 포트.",
"rtspPassWdEmpty"=>"비밀번호를 입력 해주세요.",
"rtspPassWdInvalid"=>"암호가 너무 깁니다. 암호는 최대 63자입니다.",
"cameraExist"=>"카메라가 이미 존재합니다.",
"errorOnRPS"=>"RPS 서버를 요청하지 못했습니다.",
"faceImportErrorSystem"=>"시스템 오류",
"faceImportErrorView"=>"정면뷰가 아님",
"faceImportErrorWearMask"=>"마스크 감지됨",
"faceImportErrorLowResolution"=>"해상도가 너무 낮습니다.",
"faceImportErrorWrongFormat"=>"파일 형식 오류",
"faceImportErrorNoFace"=>"얼굴인식이 되지 않음",
"faceImportErrorFileLarge"=>"파일이 너무 큽니다.",
"faceImportErrorFaceLarge"=>"얼굴이 너무 큽니다",
"faceImportErrorFaceSmall"=>"얼굴이 너무 작다",
"faceImportErrorMultiFaces"=>"하나 이상의 얼굴 있습니다",
"faceImportErrorWrongName"=>"파일명이 오류입니다.",
"faceImportErrorEmptyName"=>"입주자 이름은 필수입니다.",
"faceImportErrorNoAccountInfo"=>"개인 계정 정보 가져오기 오류.",
"faceImportErrorAccountInactive"=>"개인 계정이 활성화되지 않았습니다.",
"changeHomeFeatureInvalid"=>"작업 실패! '홈 자동화' 기능을 사용하는 이 배포자의 설치 프로그램이 있습니다.",
"changeInterComFeatureInvalid"=>"작업 실패! '인터콤' 기능을 사용하는 이 배포자의 설치프로그램들이 있습니다.",
"offline"=>"실패: 오프라인",
"allFloors"=>"모든 층",
"uploadOversize"=>"업로드 파일의 크기는 %s보다 클 수 없습니다.",
"uploadInvalidType"=>"업로드 된 파일 유형은 지원되지 않습니다",
"uploadFailed"=>"업로드 실패, 나중에 시도하십시오",
"uploadScreenSaverImgTooMuch"=>"Screensaver 사진은 %s 이상일 수 없습니다!",
"screenSaverImgTooLittle"=>"Screensaver 사진은 %S보다 작을 수 없습니다!",
"screenSaverImgTooMuch"=>"Screensaver 사진은 %s 이상일 수 없습니다!",
"screenSaverDevicesOffline"=>"저장 실패.",
"saveFailed"=>"저장 실패.",
"importingInProgress"=>"진행중인 수입, 나중에 다시 시도하십시오.",
"importBuildingInvalidLine"=>"줄에 유효하지 않은 건물 %s",
"importAptInvalidLine"=>"부상 한 적절한 부위 %s",
"importAccountTypeInvalidLine"=>"유효하지 않은 계정 유형은 줄 %s입니다",
"importFirstNameInvalidLine"=>"부인의 이름이 잘못된 이름 %s",
"importLastNameInvalidLine"=>"부적절한 성직자 %s",
"importKeyInvalidLine"=>"부사장의 잘못된 키",
"importKeyExistsLine"=>"핀은 줄에 존재합니다",
"importCardInvalidLine"=>"부인의 잘못된 RF 카드",
"importCardExistsLine"=>"RF 카드는 %s 라인에 존재합니다",
"importAccessGroupInvalidLine"=>"부드러운 액세스 그룹 ID 라인 %s",
"importAccessGroupNoPermissionLine"=>"Line %s의 권한 액세스 그룹 ID가 없습니다",
"importExceededNumberLine"=>"줄에 가족 수를 초과했습니다.",
"importNoActiveMasterLine"=>"줄이 실패한 %s에서는 먼저 패밀리 매트를 활성화하십시오.",
"importMasterExistsLine"=>"가족 마스터는 이미 줄에 존재합니다.",
"importNoCreateMasterLine"=>"줄이 실패한 %s에서는 먼저 패밀리 매트를 작성하십시오.",
"PrivateKeysDataExist"=>"개인 키 %가 이미 존재합니다.",
"PrivateKeyDataExists"=>"개인 키 %는 이미 존재합니다.",
"landLineOpenToClosedFail"=>"저장 실패.",
"limitWithIp"=>"너무 자주 노력하고 있습니다. 5 분 안에 다시 시도하십시오. (IP : %s)",
"subDistributor"=>"하위 유통 업체",
"faceImportErrorNotClear"=>"수입 된 사진은 명확하지 않습니다.",


  ];
