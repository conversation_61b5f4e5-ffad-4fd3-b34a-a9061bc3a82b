<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 15:40:20
 * @LastEditors  : kxl
 */
namespace model\key;
trait update {
    function modifyCom ($account) {
        global $cMessage;
        $params = [
			"Allow"=>"",
			"ExpireTime"=>"",
			"UserID"=>"",
            "Code"=>"",
            "ID"=>"",
			"SelfTimeZone"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		// $allow = $params["Allow"];
		// $expireTime = $params["ExpireTime"];
		$userId = $params["UserID"];
        $code = $params["Code"];
        $id = $params["ID"];
        // $timeZone = $params["SelfTimeZone"];
        $tabel = $this->tabelName[$this->type];
        $this->log->actionLog("#model#key#modifyCom#id=$id;tabel=$tabel;userId=$userId;code=$code");
        //2018-01-12 kxl V3.2 新增rfcard 同一主账户下不可重复
        if($this->type == 1 || $this->type == 2) {
            $rfCard = $this->db->queryAllList($tabel,["equation"=>[":Code"=>$code,":Node"=>$account],"unEquation"=>[":ID"=>$id],"e2Un"=>"and"]);
            if(count($rfCard)>0) $cMessage->echoErrorMsg($this->type == 1?StateRFCardExit:StatePrivateKeyExists);
        }

        $role = $this->db->queryAllList("PersonalAccount",["equation"=>[":Account"=>$account]])[0]["Role"];
        $isPer = in_array($role, COMROLE) ? 0 : 1;
        $this->db->update2ListWID($tabel,[":Node"=>$account,":Code"=>$code,":AccountID"=>$userId,":ID"=>$id]);
        \util\computed\setGAppData(["ID"=>$id,"Type"=>$this->type,"IsPer"=>$isPer]);
        \util\computed\setGAppData(["Account"=>$account]);

        $this->auditLog->setLog($this->type == 1 ? AuditCodeEditRf : AuditCodeEditPin, $this->env, [$code],$account);
    }

    function modRF () {
        $this->type = 1;
        $params = [
            "Code"=>"",
			"userAlias"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$user = $params["userAlias"];
		$this->modifyCom($user);
    }

    function modPri () {
        $this->type = 2;
        $params = [
			"userAlias"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
		$user = $params["userAlias"];
		$this->modifyCom($user);
    }

    function modifyFC () {
        global $cMessage;
        $params = [
            "RoomID"=>"",
            "userAliasId"=>"",
            "Code"=>"",
            "ID"=>""
		];
		$params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $room = $params["RoomID"];
        $code = $params["Code"];
        $id = $params["ID"];
        $userId = $params["userAliasId"];
        $data = $this->db->queryAllList("PersonalAccount",["equation"=>[":ID"=>$room]]);
        $this->log->actionLog("#model#key#modifyFC#id=$id;room=$room");
        
        if(count($data) == 0) $cMessage->echoErrorMsg(StateAccountNotExit);
        $account = $data[0]["Account"];
        $data = $this->db->queryAllList($this->tabelName[$this->type],["equation"=>[":Code"=>$code,":MngAccountID"=>$userId],"unEquation"=>[":ID"=>$id],"e2Un"=>"AND"]);
        $tables = ["2"=>"PubPrivateKey","1"=>"PubRfcardKey"];
        $data2 = $this->db->queryAllList($tables[$this->type],["equation"=>[":Code"=>$code,":MngAccountID"=>$userId],"unEquation"=>[":ID"=>$id],"e2Un"=>"AND"]);
        if(count($data) != 0 || count($data2) != 0) $cMessage->echoErrorMsg($this->type == 1?StateRFCardExit:StatePrivateKeyExists);
        $this->modifyCom($account);
    }

    function modRFFC() {
		$this->type = 1;
		$this->modifyFC();
	}
	
	function modPriFC() {
		$this->type = 2;
		$this->modifyFC();
	}
}