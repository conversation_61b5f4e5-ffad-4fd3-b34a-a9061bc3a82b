<?php

require_once(dirname(__FILE__).'/adapt_define.php');
require_once(dirname(__FILE__).'/phpqrcode/qrlib.php');

class ParsePid
{
    //
    private $m_Pid;
    private $m_Area = "";
    private $m_Build = "";
    private $m_Unit = "";
    private $m_Floor = "";
    private $m_Room = "";
    private $m_Type = "";

    public function __construct($Pid)
    {
        $this->m_Pid = $Pid;
    }

    public function __destruct()
    {
    }
    //解析节点
    public function BeginParse()
    {
        @list($this->m_Area, $this->m_Build, $this->m_Unit, $this->m_Floor, $this->m_Room) = split('\.', $this->m_Pid);
    }

    public function GetArea()
    {
        return  $this->m_Area;
    }

    public function GetBuild()
    {
        return  $this->m_Build;
    }

    public function GetUnit()
    {
        return  $this->m_Unit;
    }

    public function GetFloor()
    {
        return  $this->m_Floor;
    }

    public function GetRoom()
    {
        return  $this->m_Room;
    }

    public function GetNodeType()
    {
        if ($this->m_Pid === "root") {
            return 0;
        }
        //根据字符串中.的个数判断节点类型
        $this->m_Type = substr_count($this->m_Pid, '.') + 1;
        return $this->m_Type;
    }
}

//////////////////////////// v3.2 /////////////////////////////
class AES128CBC
{
    //METHOD/QRCODE_SECRET_KEY当常量用会被加上public，会异常
    //public const METHOD = 'AES-128-CBC';
    //added by chenyc,新建用户,二维码发送加密秘钥
    //public const QRCODE_SECRET_KEY = '1234560000000000';

    public static function encrypt($text)
    {
        $iv = "0000000000000000";
        return openssl_encrypt($text, 'AES-128-CBC', '1234560000000000', 0, $iv);
    }

    public static function decrypt($text)
    {
        $iv = "0000000000000000";
        $opensslDecrypt = openssl_decrypt($text, 'AES-128-CBC', '1234560000000000', 0, $iv);
        return $opensslDecrypt;
    }
}
//递归创建路径
function mkDirs($dir)
{
    if (!is_dir($dir)) {
        if (!mkDirs(dirname($dir))) {
            return false;
        }
        if (!mkdir($dir, 0777)) {
            return false;
        }
    }
    chown($dir, 'nobody');
    chgrp($dir, 'nobody');
    return true;
}

function deldir($dir)
{
    //先删除目录下的文件：
    $dh = opendir($dir);
    while ($file = readdir($dh)) {
        if ($file != "." && $file!="..") {
            $fullpath = $dir."/".$file;
            if (!is_dir($fullpath)) {
                unlink($fullpath);
            } else {
                deldir($fullpath);
            }
        }
    }
    closedir($dh);
}

function randomkeys($length)
{
    $pattern = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLOMNOPQRSTUVWXYZ';
    $key = "";
    for ($i=0;$i<$length;$i++) {
        $key .= $pattern[mt_rand(0, 61)];    //生成php随机数
    }
    return $key;
}

//删除临时秘钥的二维码图片
function delTmpKeyQrCode2($uid, $sqlid, $url='')
{
    $head_flag = md5($uid . $sqlid);
    $file_name = $sqlid . $head_flag  .'*.png';
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/';
    $file_root_path = $file_path  . $file_name;
    shell_exec("rm ".$file_root_path);

    fdfs_del_pic_by_url($url);
}

//获取临时秘钥的二维码图片的本地路径
function getTmpKeyQrCodePath($uid, $sqlid, $fileUrl)
{
    $head_flag = md5($uid . $sqlid);
    $file_name = $sqlid . $head_flag  .'*.png';
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/';
    $file_root_path = $file_path  . $file_name;
    $file_root_path = shell_exec("ls ". $file_root_path. " | tr -d '\n'");
    return $file_root_path;
}

//获取临时秘钥的二维码图片的url 对于node
function createTmpKeyQrCodeUrl2($uid, $code, $sqlid)
{
    //$arr['tmp_key'] = (int)$code;
    $arr_str = json_encode($code);

    //加密,内部已经经过AES128CBC加密
    $ciphertext = AES128CBC::encrypt($arr_str);

    //生成二维码图片
    $randomKeys = randomkeys(32);
    $head_flag = md5($uid . $sqlid);
    $file_name = $sqlid . $head_flag . $randomKeys .'.png';
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/';
    mkDirs($file_path);

    $localFilePath = $file_path  . $file_name;
    QRcode::png($ciphertext, $localFilePath, QR_ECLEVEL_L, 10, 1);
    //add by chenzhx 20220324 类似getQrCodeUrl的说明
    chown($localFilePath, "nobody");
    chgrp($localFilePath, "nobody");

    chown("/var/www/download/per_qrcode/$uid_mod", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod", "nobody");

    chown("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");

    // 上传到fdfs
    $fdfsUploadRetUrl = UploadFileToFdfs($localFilePath);
    if ($fdfsUploadRetUrl) {
        return $fdfsUploadRetUrl;
    }

    global $cLog;
    $cLog->TRACE("shareTmpkeyEmailNotify failed, MngID = {MngID}, code = {code}", ["MngID"=>$uid, "code"=>$code]);

    // 上传到fdfs失败, 存在本地
    $localFileUrl = '/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/'.$file_name;
    return $localFileUrl;
}

//返回图片body和url  对于社区管理员的uid=社区管理员ID
function createTmpKeyQrCodeUrl3($uid, $code, $sqlid, &$qr_body)
{
    $arr_str = json_encode($code);

    //加密,内部已经经过AES128CBC加密
    $ciphertext = AES128CBC::encrypt($arr_str);

    //生成二维码图片
    $randomKeys = randomkeys(32);
    $head_flag = md5($uid . $sqlid);
    $file_name = $sqlid . $head_flag . $randomKeys .'.png';
    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = '/var/www/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/';
    mkDirs($file_path);

    $localFilePath = $file_path  . $file_name;
    QRcode::png($ciphertext, $localFilePath, QR_ECLEVEL_L, 10, 1);
    //add by chenzhx 20220324 类似getQrCodeUrl的说明
    chown($localFilePath, "nobody");
    chgrp($localFilePath, "nobody");

    chown("/var/www/download/per_qrcode/$uid_mod", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod", "nobody");

    chown("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");
    chgrp("/var/www/download/per_qrcode/$uid_mod/$uid", "nobody");

    $qr_body1 = file_get_contents($localFilePath);
    $qr_body=base64_encode($qr_body1);

    // 上传到fdfs
    $fdfsUploadRetUrl = UploadFileToFdfs($localFilePath);
    if ($fdfsUploadRetUrl) {
        return $fdfsUploadRetUrl;
    }

    global $cLog;
    $cLog->TRACE("shareTmpkeyEmailNotify failed, MngID = {MngID}, code = {code}", ["MngID"=>$uid, "code"=>$code]);

    // 上传到fdfs失败, 存在本地
    $localFileUrl = '/download/tmp_key_qrcode/' . $uid_mod . '/' . $uid . '/'.$file_name;
    return $localFileUrl;
}

function UploadFileToFdfs($localFilePath)
{
    $maxRetries = 3;
    $retryCount = 0;

    while (++$retryCount <= $maxRetries) {
        $fdfsUploadRet = storage_pic_to_fdfs($localFilePath, "");
        if ($fdfsUploadRet) {
            return $fdfsUploadRet; 
        }
    }
    return false;
}

function switchHandle($value, $pos)
{
    return ($value>>$pos)&1;
}

function fdfs_del_pic_by_url($url)
{
    $beforeFile = explode('/', $url, 3); //拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg
    //如果存在fdfs的图片链接，则对应删除
    if (strstr($beforeFile[1], 'group')) {
        fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);
        return true;
    } else {
        return false;
    }
}

function storage_pic_to_fdfs($faceFilePath, $beforePath)
{
    $ret = false;
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker) {
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store(FACE_FDFS_GROUP);
    if (!$storage) {
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server) {
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    if ($beforePath) {
        $beforeFile = explode('/', $beforePath, 3); //拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg
        if (strstr($beforeFile[1], 'group')) {  //如果存在fdfs的图片链接，则对应删除
            fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);    //param1:group param2:path
        }
    }

    $file_info = fastdfs_storage_upload_by_filename($faceFilePath, null, array(), null, $tracker, $storage);
    if ($file_info) {
        $group_name = $file_info['group_name'];
        $remote_filename = $file_info['filename'];
        $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}

function getFileBodyFromFdfs($fileUrl, &$qr_body)
{
    $fileUrl = ltrim($fileUrl, '/'); // 去掉开头的斜杠
    $fileParts = explode('/', $fileUrl, 2); // 拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg

    $group = $fileParts[0]; // 获取组名
    $filePath = $fileParts[1]; // 获取文件路径

    $ret = false;
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker) {
        return $ret;
    }

    $storage = fastdfs_tracker_query_storage_store($group);
    if (!$storage) {
        return $ret;
    }

    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server) {
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    $fileData = fastdfs_storage_download_file_to_buff($group, $filePath, 0, 0, $tracker, $storage);
    if ($fileData) {
        //file_put_contents($savePath, $fileData);
        $qr_body = $fileData;
        $ret = true;
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}

function AddAkcsAlarm($key, $description)
{
    $serverinfo = parse_ini_file("/etc/ip");
    $nsqd_addr = $serverinfo["SERVER_INNER_IP"];

    $data = array();
    $data["node"] = "web";
    $data["time"] = date('Y-m-d H:i:s');
    $data["description"] = $description;
    $data["key"] = $key;
    $data["ip"] = $serverinfo["SERVERIP"];
    $data["hostname"] = $serverinfo["AKCS_HOSTNAME"];
    $json_data = json_encode($data);
    global $cLog;
    $cLog->TRACE("push alarm, key:$key ,description:$description");

    $cmd = "curl -s -d '$json_data' " . 'http://' . $nsqd_addr . ':8513/pub?topic=akcs_alarm';
    $cLog->TRACE("cmd=" . $cmd);
    if (!shell_exec($cmd)) {
        $cLog->TRACE("push alarm error,description:" . $description);
    }
}

//$path 没有https://ip头
function CreateConfigFdfsLink($path, $timeout_before)
{
    $time = time() + $timeout_before;
    $token = base64_encode(md5("ak_download:$path:$time", true));
    $token = strtr($token, '+/', '-_');
    $token = str_replace('=', '', $token);
    return $path."?token=$token&e=$time";
}