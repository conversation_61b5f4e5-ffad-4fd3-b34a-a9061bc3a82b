<?php
namespace model\devicePersonal;

trait add
{
    /**
     * @name: installer管理员添加设备
     * @service sip,rps
     */
    public function addForMng($kitImport = false)
    {
        global $cMessage;
        $params = [
            "Type"=>"",
            "MAC"=>"",
            "Location"=>"",
            "Node"=>"",
            "Relay"=>"",
            "SecurityRelay"=>"",
            "userAliasId"=>"",
            "userAlias"=>""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $mac = $params["MAC"];
        $Location = $params["Location"];
        $node = $params["Node"];
        $relay = $params["Relay"];
        $securityRelay = isset($params["SecurityRelay"]) ? $params["SecurityRelay"] : '';
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];

        //securityRelay的个数不能超过2个
        $securityRelayList = explode(';', $securityRelay);
        if (count($securityRelayList) > 2) {
            $cMessage->echoErrorMsg(STATE_PARAMS_ERROR);
        }

        $mac = strtoupper($mac);

        $areaMngID = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0]["ParentID"];
        $now = \util\computed\getNow();
        $nodeData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$node]])[0];
        $addRps = $this->services["rps"]->modifyMap(["mac"=>[$mac],"type"=>0,"name"=>$nodeData["Name"],"mngID"=>$areaMngID,"permngID"=>$userId]);
        if (!$addRps) {
            $this->addPCMng2MacLibrary($mac, $userId);
            $addRps = $this->services["rps"]->modifyMap(["mac"=>[$mac],"type"=>0,"name"=>$nodeData["Name"],"mngID"=>$areaMngID,"permngID"=>$userId]);
            if (!$addRps) {
                $cMessage->echoErrorMsg(StateMAC2Library);
            }
        }
    
        $sip = $this->services["sip"]->assignSip($user, $type == '2'?'1':'8');
        
        if ($sip == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }
        $RTSPKey = \util\string\randString(6);
        $sipPw = \util\string\generatePw(12);
        $now = \util\computed\getNow();
        
        $enableGroup = $this->services["sip"]->getEnableGroup($node, $type);
        $groups = $this->db->queryAllList("SipGroup2", ["equation"=>[":Account"=>$node]]);
        if (count($groups) == 0) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccountGroup);
        }
        $group = $groups[0]["SipGroup"];

        $options = ["type"=>$type,
        "node"=>0,//主账号id
        "group"=>$group,
        "community"=>$userId,
        "communityType"=>1,
        "enableGroup"=>$enableGroup,//室内机群响铃
        "sip"=>$sip,
        "passwd"=>$sipPw];
        $this->services["sip"]->add2Freeswish($sip, $options);
        $expireTime = DEFAULTEXPIRETIME;
        $flag = 0;
        if ($kitImport) {
            $relay = '#,Relay1,1,1,0;0,Relay2,1,1,0';
        }
        $this->db->insert2List("PersonalDevices", [
            ":Type"=>$type,
            ":Node"=>$node,
            ":MAC"=>$mac,
            ":SipAccount"=>$sip,
            ":Location"=>$Location,
            ":Status"=>0,
            ":CreateTime"=>$now,
            ":Community"=>$user,
            ":RtspPwd"=>$RTSPKey,
            ":SipPwd"=>$sipPw,
            ":ExpireTime"=>$expireTime,
            ":Flag"=>$flag,
            ":Relay"=>$relay,
            ":SecurityRelay"=>$securityRelay,
            ":UUID"=> \util\string\uuid()
        ]);
        if ($kitImport) {
            $this->db->update2ListWKey("PersonalDevices", [":MAC"=>$mac,":Flags"=>520], "MAC");
        }

        \util\computed\setGAppData(["MAC"=>$mac,"Node"=>$node]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeDeviceTypeArray[$type], $this->env, [$mac], $account);
        \util\computed\setSmartHomeTask(['Key'=>$mac, 'Type'=>8]);
    }
}
