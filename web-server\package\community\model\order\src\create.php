<?php
/**
 * @description:
 * @author: zyc 2022/3/21 18:14 V6.4
 * @lastEditor cj
 */

namespace package\community\model\order\src;
use package\community\model\order\config\Code;

trait Create
{
    /*
     *@description 创建订单
     *<AUTHOR> 2022-04-12 11:13:43 V6.4
     *@lastEditor kxl 2022-04-12 11:13:43 V6.4
     *@param {*} Users 用户ID数组
     *@param {*} Type 支付类型：0=激活，1=按月续费，2=购买App，3=落地按月续费，6=按天续费
     *@param {*} Count
     *@param {*} NextTime
     *@param {*} userId'
     *@return
     */
    public function create()
    {
        $params = [
            'Users',
            'Type',
            'Count',
            'NextTime',
            'userId',
            'role',
            'VideoSites',
            'ThirdLockUUIDs'
        ];
        list($users, $type, $count, $nextTime, $userId, $role, $videoSites, $thirdLockUUIDs) = $this->getParams($params);

        // 不能超过10年，120个月，$count为月数，$nextTime为过期时间
        if (intval($count) > 120 || strtotime($nextTime) > (time() + 10 * 365 * 24 * 60 * 60)) {
            $this->output->echoErrorMsg(STATE_500);
        }

        $type = is_numeric($type) ? intval($type) : $type;
        $isRenew = in_array($type, [PAY_ACTIVE, PAY_ADD_APP, PAY_ACTIVE_PM]) ? false : true;

        $this->loadUtil('subscription', true);
        $payerType = $this->utils->_common->subscription->getPayerType($role);
        $mixCount = 0;
        if (!empty(json_decode($users, true))) {
            $mixCount++;
        }
        if (!empty(json_decode($videoSites, true))) {
            $mixCount++;
        }
        if (!empty(json_decode($thirdLockUUIDs, true))) {
            $mixCount++;
        }
        if ($mixCount > 1) {
            $type = true === $isRenew ? PAY_MIX : PAY_MIX_ACTIVE;
        } elseif (!empty(json_decode($videoSites, true))) {
            // 只有视频存储，则转化为视频存储续费类型
            $type = PAY_COMMUNITY_VIDEO_STORAGE;
        } elseif (!empty(json_decode($thirdLockUUIDs, true))) {
            // 只有三方锁，是续费类型，则转化为三方锁续费，否则为三方锁激活
            $type = true === $isRenew ? PAY_COMMUNITY_THIRDLOCK_RENEW : PAY_COMMUNITY_THIRDLOCK_ACTIVE;
        }

        $this->loadUtil('order', true);
        if ($type === PAY_ACTIVE) {
            $orderParams = ['Users' => $users, 'PayerType' => $payerType, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE];
            $result = $this->utils->_common->order->setAssocParams()->createActiveUser($orderParams);
        } elseif ($type === PAY_SUBSCRIPTION) {
            $orderParams = ['Users' => $users, 'PayerType' => $payerType, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'Count' => $count];
            $result = $this->utils->_common->order->setAssocParams()->createRenewByMonth($orderParams);
        } elseif ($type === PAY_ADD_APP) {
            $orderParams = ['Users' => $users, 'PayerType' => $payerType, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'PayType' => PAY_ADD_APP];
            $result = $this->utils->_common->order->setAssocParams()->createActiveUser($orderParams);
        } elseif ($type === PAY_LANDLINE) {
            $orderParams = ['Users' => $users, 'PayerType' => $payerType, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'Count' => $count];
            $result = $this->utils->_common->order->setAssocParams()->createRenewLandline($orderParams);
        } elseif ($type === PAY_SUBSCRIPTION_BY_DAY) {
            $orderParams = ['Users' => $users, 'PayerType' => $payerType, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'NextTime' => $nextTime];
            $result = $this->utils->_common->order->setAssocParams()->createRenewByDay($orderParams);
        } elseif ($type === PAY_COMMUNITY_VIDEO_STORAGE) {
            //纯视频存储付费
            $arr = ['VideoSites' => $videoSites, 'PayerType' => $payerType, 'NextTime' => $nextTime, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE];
            $result = $this->utils->_common->order->setAssocParams()->createCommunityRenewByVideoStorage($arr);
        } elseif ($type === PAY_COMMUNITY_THIRDLOCK_ACTIVE) {
            $arr = ['ThirdLockUUIDs' => $thirdLockUUIDs, 'PayerType' => $payerType, 'ProjectType' => PAY_TYPE_MULTIPLE,
                'PayType' => PAY_COMMUNITY_THIRDLOCK_ACTIVE, 'NextTime' => $nextTime, 'PayerId' => $userId];
            $result = $this->utils->_common->order->setAssocParams()->createCommunityActiveThirdLock($arr);
        } elseif ($type === PAY_COMMUNITY_THIRDLOCK_RENEW) {
            $arr = ['ThirdLockUUIDs' => $thirdLockUUIDs, 'PayerType' => $payerType, 'ProjectType' => PAY_TYPE_MULTIPLE,
                'PayType' => PAY_COMMUNITY_THIRDLOCK_RENEW, 'NextTime' => $nextTime, 'PayerId' => $userId];
            $result = $this->utils->_common->order->setAssocParams()->createCommunityRenewThirdLock($arr);
        } elseif ($type === PAY_MIX) {
            //多订单混合付费 - 续费
            $mixArr = [
                // 用户类型
                'User' => [
                    'Users' => $users, 'PayType' => PAY_SUBSCRIPTION_BY_DAY
                ],
                // 视频存储
                'VideoStorage' => [
                    'VideoSites' => $videoSites, 'PayType' => PAY_COMMUNITY_VIDEO_STORAGE
                ],
                // 三方锁
                'ThirdLock' => [
                    'ThirdLockUUIDs' => $thirdLockUUIDs, 'PayType' => PAY_COMMUNITY_THIRDLOCK_RENEW
                ],
                'NextTime' => $nextTime, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'PayerType' => $payerType
            ];
            $result = $this->utils->_common->order->setAssocParams()->createCommunityRenewMixOrder($mixArr);
        } elseif ($type === PAY_MIX_ACTIVE) {
            //多订单混合付费 - 激活
            $mixArr = [
                // 用户类型
                'User' => [
                    'Users' => $users, 'PayType' => PAY_ACTIVE
                ],
                // 三方锁
                'ThirdLock' => [
                    'ThirdLockUUIDs' => $thirdLockUUIDs, 'PayType' => PAY_COMMUNITY_THIRDLOCK_ACTIVE
                ],
                'NextTime' => $nextTime, 'PayerId' => $userId, 'ProjectType' => PAY_TYPE_MULTIPLE, 'PayerType' => $payerType
            ];
            $result = $this->utils->_common->order->setAssocParams()->createCommunityActiveMixOrder($mixArr);
        }
        return ['orderID' => $result['ID'], 'bmurl' => $result['bmUrl']];
    }

    public function createFeature()
    {
        $params = ['Type', 'Count', PROXY_ROLE['projectId'], 'PayerId', 'PayerType'];
        list($type, $month, $projectId, $payerId, $payerType) = $this->getParams($params);
        // 不能超过10年，120个月，$month为月数
        if (intval($month) > 120) {
            $this->output->echoErrorMsg(STATE_500);
        }
        $this->loadUtil('order', true);
        return $this->utils->_common->order->createFeature($type, $month, $projectId, $payerId, $payerType, PAY_TYPE_MULTIPLE);
    }

    public function rentManagerCreate()
    {
        $params = ['MonthCount', 'RentManagerCustomerList:string-required', PROXY_ROLE['distributorId'], 'userId', PROXY_ROLE['installerUUID'], PROXY_ROLE['distributorUUID'],'TotalPrice'];
        list($monthCount, $rentManagerCustomerList, $distributorId, $userId, $insUUID, $disUUID, $totalPrice) = $this->getParams($params);
        // 不能超过10年，120个月，$month为月数
        if (intval($monthCount) > 120) {
            $this->output->echoErrorMsg(STATE_500);
        }
        $rentManagerCustomerList = explode(",", $rentManagerCustomerList);
        //参数校验
        if (empty($rentManagerCustomerList)) {
            $this->output->echoErrorMsg(STATE_USER_NOT_EXITS, ['externalErrorObj' => Code::EXT_STATE_RENT_MANAGER_COMPANY_IS_EMPTY]);
        }

        // 校验支付权限
        $this->utils->self->checkRentManagerPayPermission($rentManagerCustomerList, $userId, $insUUID);

        $this->loadUtil('order', true);
        $orderParams = [
            'Count' => intval($monthCount),
            'RentManagerCustomerList' => $rentManagerCustomerList,
            'PayerId' => $userId,
            'PayerType' => 2,
            'DisId' => $distributorId,
            'InsUUID' => $insUUID,
            'DisUUID' => $disUUID,
            'TotalPrice'=> $totalPrice
        ];

        $result = $this->utils->_common->order->setAssocParams()->createRentManagerRenewByMonth($orderParams);
        return ['orderID' => $result['ID'], 'bmurl' => $result['bmUrl']];
    }
}
