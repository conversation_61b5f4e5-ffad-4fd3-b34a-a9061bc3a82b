<?php
/*
 * @Description: 终端用户name检查
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";
include_once __DIR__."/../util/string.php";
class CUserNameCheck implements IMiddleware {
    public $name;
    public function handle(\Closure $next) {
        global $cMessage;
        global $cLog;
        $params = ["Name"=>""];
        $name = \util\model\getParamsFromContainer($params,$this->dataContainer)["Name"];
        
        $cLog->actionLog("#middle#userNameCheck#Name=".$name);
        if(\util\string\checkByteLength($name,63) || $name === "" || $name === null) {
            $cMessage->echoErrorMsg(StateNameLong);
        }
        $next();
    }
}