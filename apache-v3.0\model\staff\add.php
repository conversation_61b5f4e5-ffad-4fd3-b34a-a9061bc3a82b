<?php
namespace model\staff;

trait add {
    function add ($type) {
        $params = [
            "Name"=>"",
            "PinCode"=>"",
            "CardCode"=>"",
            "AccessGroup"=>"",
            "userAliasId"=>"",
            'Build' => '',
            'Floor' => ''
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityId = $params['userAliasId'];

        $pin = $params["PinCode"];
        $pin = $pin == "" ? null : $pin;
        $card = $params["CardCode"];
        $card = $card == "" ? null : $card;
        $builds = json_decode($params['Build'], true);
        $floors = json_decode($params['Floor'], true);

        checkAccessValid($params['AccessGroup'], $communityId);
        if(isDelivery($type)) {
            checkPinCardUnique($pin, $communityId, $type, 'PIN');
            checkPinCardUnique($card, $communityId, $type, 'Card');
        } else {
            checkPinCardUnique($card, $communityId, $type, 'Card');
        }
        $id = add([
            "Name"=>$params["Name"],
            "PinCode"=>$pin,
            "CardCode"=>$card,
            "AccessGroup"=>$params["AccessGroup"],
            'Build' => $builds,
            'Floor' => $floors
        ], $communityId, $type);

        \util\computed\setGAppData(["ID"=>[$id], "Type"=>$type]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $communityId])[0]['Account'];
        if ($pin!==null){
            $this->auditLog->setLog(AuditCodeAddPin, $this->env, [$pin], $account);
        }
        if ($card!==null){
            $this->auditLog->setLog(AuditCodeAddRf, $this->env, [$card], $account);
        }
    }

    function deliveryAdd () {
        $this->add(0);
    }

    function staffAdd () {
        $this->add(1);
    }
}