<?php
/*
 * @Description: 根据PM APP的帐号设置PM代理的社区
 * @version: 
 * @Author: csc
 * @Date: 2022/4/11 15:13 V6.4
 * @LastEditors  : csc
 */

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
use \interfaces\middleware\main\IMiddleware;

class CSetPMAppAlias implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp,$cMessage;

        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setPMAppAlias#");
        $account = $gApp['userAlias'];
        $perData = $db->querySList('select Account, UUID, ParentID from PersonalAccount where Account = :Account', [':Account' => $account])[0];
        if (empty($perData)) $cMessage->echoErrorMsg(StateNotPermission);
        $pmMap = $db->querySList('select * from PmAccountMap where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $perData['UUID']])[0];
        if (empty($pmMap)) $cMessage->echoErrorMsg(StateNotPermission);
        $pmAccountData = $db->querySList('select * from Account where UUID = :UUID', [':UUID' => $pmMap['AccountUUID']])[0];
        if (empty($pmAccountData)) $cMessage->echoErrorMsg(StateNotPermission);
        $userAliasId = $pmAccountData["ID"];
        $communityId = $perData["ParentID"];
        $data = $db->querySList("select B.ID,B.TimeZone,B.CustomizeForm,B.Account from Account A join PropertyMngList M on A.ID = M.PropertyID join Account B on B.ID = M.CommunityID where M.CommunityID = :CommunityID and A.ID = :ID",
        [":ID"=>$userAliasId,":CommunityID"=>$communityId]);

        if(count($data) == 0) $cMessage->echoErrorMsg(StateNotPermission);

        $gApp["userAliasId"] = $communityId;
        $gApp["userAlias"] = $data[0]["Account"];
        $customizeForm = $data[0]["CustomizeForm"];
        $timeZone = $data[0]["TimeZone"];
        \util\computed\setGAppData(["SelfCustomizeForm"=>$customizeForm,"SelfTimeZone"=>$timeZone,"PMAppUserAccount"=>$perData,'PMAppPMAccount'=>$pmAccountData]);
        $next();
    }
}