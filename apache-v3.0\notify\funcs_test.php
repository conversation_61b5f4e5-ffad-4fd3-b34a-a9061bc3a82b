<?php
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');


function sendDelAccountMsg($account)
{
    $data[0] = $account;

    $socket = new CDelAppAccountNotifySocket();
    $socket->setMsgID(MSG_P2A_DELETE_APP_ACCOUNT);
    $socket->copy($data);
}

function oneTimeAutopDevices($macList, $config)
{
    if (!is_array($macList) || count($macList) == 0) {
        $cLog->TRACE("Send Once Autop Failed:config=$config;macList=" . json_encode($macs));
        return;
    }

    $chunkResult = array_chunk($macList, 50);
    foreach ($chunkResult as $macs) {
        $data[0] = $macs;
        $data[1] = $config;

        $socket = new COnceAutopNotifySocket();
        $socket->setMsgID(MSG_P2A_ONCE_AUTOP);
        $socket->copy($data);
    }
}


function regularAutopDevices($macList)
{
    if (!is_array($macList) || count($macList) == 0) {
        return;
    }

    $chunkResult = array_chunk($macList, 50);
    foreach ($chunkResult as $macs) {
        $data[0] = $macs;

        $socket = new CRegularAutopNotifySocket();
        $socket->setMsgID(MSG_P2A_REGULAR_AUTOP);
        $socket->copy($data);
    }
}

function pmEmergencyDoorControl($uuid, $type)
{
    global $cLog;
    $data[0] = $uuid;
    $data[1] = $type;
    
    $socket = new CPmEmergencyDoorControlSocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_PM_EMERGENCY_DOOR_CONTROL);
    $socket->copy($data);
    $cLog->TRACE("Send PmEmergencyDoorControl Success:uuid=$uuid, type=$type");
}

function commAddNewSite($account_uuid, $project_name, $apt_num, $email)
{
    global $cLog;

    $data[] = $account_uuid;
    $data[] = $project_name;
    $data[] = $email;
    $data[] = $apt_num;

    $cLog->TRACE('[commAddNewSite]begin to community enduser add new site, per_uuid is ' . $account_uuid . ' comm_name is ' . $project_name. ' apt_num is ' . $apt_num);
    $socket = new CPerAddNewSiteSocket();
    $socket->setMsgID(MSG_P2A_COMMUNITY_ADD_NEW_SITE);
    $socket->copy($data);
}

function officeAddNewSite($account_uuid, $office_name, $email)
{
    global $cLog;

    $data[] = $account_uuid;
    $data[] = $office_name;
    $data[] = $email;

    $cLog->TRACE('[officeAddNewSite]begin to office enduser add new site, per_uuid is ' . $account_uuid . ' office_name is ' . $office_name);
    $socket = new CPerAddNewSiteSocket();
    $socket->setMsgID(MSG_P2A_OFFICE_ADD_NEW_SITE);
    $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $socket->copy($data);
}

function perAddNewSite($account_uuid, $room_name, $email)
{
    global $cLog;

    $data[] = $account_uuid;
    $data[] = $room_name;
    $data[] = $email;

    $cLog->TRACE('[perAddNewSite]begin to personal enduser add new site, per_uuid is ' . $account_uuid . ' unit_name is ' . $room_name);
    $socket = new CPerAddNewSiteSocket();
    $socket->setMsgID(MSG_P2A_PERSONAL_ADD_NEW_SITE);
    $socket->copy($data);
}

function pmLinkNewSites($account_uuid, $project_name_list, $email, $project_type)
{
    $project_name_list = is_array($project_name_list) ? $project_name_list : [$project_name_list];
    $project_name_list_str = implode(",", $project_name_list);
    global $cLog;

    $data[] = $account_uuid;
    $data[] = $project_name_list_str;
    $data[] = $email;
    $data[] = $project_type;

    $cLog->TRACE('[pmLinkNewSites]begin to pm link new sites, account_uuid is ' . $account_uuid . ' new_sites are ' . $project_name_list_str);
    $socket = new CPmLinkNewSitesSocket();
    $socket->setMsgID(MSG_P2A_PM_LINK_NEW_SITES);
    $socket->copy($data);
}

function pmAppAddNewSite($account_uuid, $project_name, $email)
{
    global $cLog;

    $data[] = $account_uuid;
    $data[] = $project_name;
    $data[] = $email;

    $cLog->TRACE('[pmAppAddNewSite]begin to pm app add new site, per_uuid is ' . $account_uuid . ' project_name is ' . $project_name);
    $socket = new CPerAddNewSiteSocket();
    $socket->setMsgID(MSG_P2A_PM_APP_ADD_NEW_SITE);
    $socket->copy($data);
}

function pmCreateUser($uid, $pwd, $email)
{
    //delete old url
    deletePMOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdatePMQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;
    $cLog->TRACE('[pmCreateUser]begin to pm create uid, uid is ' . $uid . ' email is ' . $email);
    $socket = new CPerCreateUserSocket();
    $socket->setMsgID(MSG_P2A_PM_WEB_CREATE_UID);
    $socket->copy($data);
}

//删除PM旧的QrCode
function deletePMOldQrcode($uid)
{
    $url = "";
    GetPMQrUrl($uid, $url);

    $uid_int = (int)$uid;
    $uid_mod = $uid_int%16;
    $file_path = "/var/www/download/tmp_key_qrcode/$uid_mod/$uid/*";
    shell_exec("rm ".$file_path);
    fdfs_del_pic_by_url($url);
}

function pmChangePwd($uid, $pwd, $email)
{
    //delete old url
    deletePMOldQrcode($uid);

    global $cLog;
    $qrcode_url = "";
    $qrcode_body = "";
    $fdfs_url = "";
    CreateQrCodeUrl($uid, $pwd, $qrcode_url, $qrcode_body, $fdfs_url, $email);
    UpdatePMQrUrl($uid, $fdfs_url);

    $data[] = $uid; //32
    $data[] = $pwd; //64
    $data[] = $email; //64
    $data[] = $qrcode_body; //2048
    $data[] = $qrcode_url;
    $cLog->TRACE('[pmChangePwd]begin to pm pwd change, uid is ' . $uid . ' email is ' . $email);
    $socket = new CPerChangePwdSocket();
    $socket->setMsgID(MSG_P2A_PM_WEB_CHANGE_PWD);
    $socket->copy($data);
}

//发送短信验证码通用接口
function sendSMSVerficationCode($phone_info, $code, $type, $account_uuid="")
{
    global $cLog;
    $mobile_number = $phone_info['MobileNumber'];
    $phone_code = $phone_info['PhoneCode'];
    $language = $phone_info['Language'];
    
    $data[] = $mobile_number;
    $data[] = $phone_code;
    $data[] = $language;
    $data[] = $code;
    $data[] = $type;
    $data[] = $account_uuid;

    $socket = new CSendSMSVerficationCodeSocket();
    $socket->setMsgID(MSG_P2A_SEND_COMMON_SMS_CODE);
    $socket->copy($data);
    $cLog->TRACE("sendSMSVerficationCode Success, mobile_number:$account_uuid");
}

//发送邮件验证码通用接口
function sendEmailVerficationCode($email_info, $code, $type, $account_uuid="")
{
    global $cLog;
    $email = $email_info['Email'];
    $name = $email_info['Name'];
    $language = $email_info['Language'];
    
    $data[] = $email;
    $data[] = $name;
    $data[] = $language;
    $data[] = $code;
    $data[] = $type;
    $data[] = $account_uuid;

    $socket = new CSendEmailVerficationCodeSocket();
    $socket->setMsgID(MSG_P2A_SEND_COMMON_EMAIL_CODE);
    $socket->copy($data);
    $cLog->TRACE("sendEmailVerficationCode Success:$account_uuid");
}

//sendDelAccountMsg("**********");
//$macList[] = "A0A053210315";
//$config = "Config.Account1.GENERAL.DisplayName=A40";
//oneTimeAutopDevices($macList, $config);
//regularAutopDevices($macList);

$uuid = "333333333333333333333333333333333333";
$type = 1;
pmEmergencyDoorControl($uuid, $type);
