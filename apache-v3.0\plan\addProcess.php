<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 14:04:14
 * @LastEditors  : kxl
 */
class CAddProcess {
    private $data;
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    public function clear () {
        $this->data = [];
        return $this;
    }
    public function set ($key,$value) {
        if(is_string($key))
            $this->data[$key] = $value;
        else {
            foreach($key as $val) $this->data[$val] = $value;
        }
        return $this;
    }
    public function get () {
        return $this->data;
    }
    
    public function unshift ($el,$arr2) {
        array_unshift($arr2,$el);
        return $arr2;
    }

    public function push ($arr2,$el) {
        array_push($arr2,$el);
        return $arr2;
    }
}

