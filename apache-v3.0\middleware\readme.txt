#数据处理
-firstLastNameDeal                  
将first name 和 last name 拼接成name

-phoneDeal
phone相关字段的空值处理

-userEmailDeal
#用户#邮箱空值处理

-setUserAliasIdToId
将代理用户ID设置为ID参数




#数据获取，push进数据流

-getAliasId                         
将代理的userAliasId,userAlias设置进数据流中，key为userAliasId,userAlias

-getUserId                         
将访问者的userId,user设置进数据流中，key为userId,user

-getMngTimeCustomizeForm
-getMngTimeZone
获取#管理员#的时区

-getUserAccount                     
根据id参数获取#用户#Account

-getUserTimeCustomizeForm           
获取当前#用户#的时间格式化设置

-getUserTimeZone
获取#用户#时区设置

-setUserPayType
将payType设置为0

-setPMPayType
将payType设置为1

-setInstallPayType
将payType设置为2

-setAreaPayType
将payType设置为3

-getAliasMngTimeCustomizeForm
获取代理时间格式

-getAliasMngTimeZone
获取代理时区

-setUserOrderType
设置个人散户的续费订单的Type为4，因为个人是落地续费


#用户身份转换

-setSubToMainAlias
根据#从账户#设置代理为#主账户#

-setMainToPCMngAlias
根据#主账户#设置代理为#管理员#

-setPCMngToArea
install级别的管理员代理区域管理员

-setSupToComWDevId
超级管理员根据社区设备id设置代理为社区管理员

-setPMAlias
设置PM为小区代理，同时设置时区

-setMngToMainActiveAlias
设置管理员为激活的主账户代理

-setMngToMainAliasWSubId
设置管理员为激活的主账户代理,参数是从账户ID


#数据检测

-haveDevDeleteCheck
删除#用户#前检查是否存在设备

-haveSubUserDeleteCheck
删除#用户#前检查是否存在从账户

-loginCheck
账户密码验证

-mainUserInPCMngCheck
检测主账户在不在个个人和社区管理员下

-maxSubUserCheck
最大从账户个数验证

-recordDelData
删除重要数据前记录被删除的记录

-subUserInMainCheck
检查#从账户#是否在#主账户#下

-userEmailCheck
#用户#有邮箱重复性检测

-userNameCheck
#用户#名长度检测

-mainUserInAMngCheck
主账户是否在区域管理员下

-devLocationCheck
location 长度检测

-perDevInAreaCheck
个人设备是否在区域管理员下

-pcDevInPerMngCheck
个人设备是否在个人管理员下

-perDevInUser
个人设备是否在用户下

-macCheck
mac合法性检测,包含是否存在

-setPCMngToInstaller
获取installer级别的管理员的主账户(Installer)

-subInPCMngCheck
从账户在管理员下检测

-manageEmailCheck
管理员邮箱检测

-manageInAMngCheck
管理员在区域管理员下检测

-perMngSpecialCheck
installer是不是特殊账户的检测

-mngAccountCheck
管理员Account合法性检测