<?php
/*
 * @Description: 
 * @version: V7.0
 * @Author: cj
 * @Date: 2021-11-15 16:53:34
 * @LastEditors: cj
 * @LastEditTime: 2022-07-12 11:05:55
 */
namespace model\devicePersonal;

trait remove
{
    /**
     * @name: 删除personal设备
     * @service sip,rps
     * @param ID:设备id
     */
    public function deletePer()
    {
        global $cMessage;
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $data = $this->db->queryAllList("PersonalDevices", ["equation"=>[":ID"=>$id]])[0];
        $node = $data["Node"];
        $mac = $data["MAC"];
        $sip = $data["SipAccount"];
        $flag = $data["Flag"];
        $this->log->actionLog("#model#devicePersonal#deletePer#data=".json_encode($data));

        // 6.2特殊室内机不能删除
        $isSpecialDevice = $this->db->querySList("select S.ID from DevicesSpecial S join PersonalDevices D on D.MAC = S.MAC where D.ID = :ID", [":ID"=>$id]);
        if (count($isSpecialDevice) > 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        
        $data = $this->db->querySList("select A.Account,B.Account as PersonalManage,B.ID from Account A left join Account B on A.ID = B.ParentID left join PersonalAccount P on P.ParentID = B.ID where P.Account = :Account", [":Account"=>$node])[0];
        $areaMng = $data["Account"];
        $perMng = $data["PersonalManage"];
        $communityId = $data['ID'];

        if ($flag == 1) {
            $this->db->delete2ListWID("PersonalDevices", $id);
            $this->db->delete2ListWKey("PerNodeDevices", "PerDevID", $id);
            $this->db->delete2ListWKey("PersonalAccount", "Account", $node);
        } else {
            $this->db->delete2ListWID("PersonalDevices", $id);
        }

        // 6.1后数据巡检发现bug，增加删除alarm
        $this->db->delete2ListWKey("PersonalAlarms", "DevicesMAC", $mac);
        // kit相关设备表删除 V6.4删除用户才删除PendingRegUser
        // $this->db->delete2ListWKey("PendingRegUser", "MAC", $mac);
        
        $this->services["sip"]->sipCollecting($sip, $areaMng);
        $this->services["sip"]->del2Freeswish($sip);

        // motion
        $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 0);
        // capture
        $this->services['captureUtil']->deleteCaptureRowWKey('MAC', $mac, 1);
        
        $this->services["rps"]->modifyMap(["mac"=>[$mac],"type"=>5]);
        \util\computed\setGAppData(["Flag"=>$flag,"MAC"=>$mac,"Node"=>$node]);
        $this->auditLog->setLog(AuditCodeDeleteDevice, $this->env, [$mac], $node);
        \util\computed\setSmartHomeTask(['Key'=>$mac, 'Type'=>11, 'CommunityID' => $communityId]);
    }
}
