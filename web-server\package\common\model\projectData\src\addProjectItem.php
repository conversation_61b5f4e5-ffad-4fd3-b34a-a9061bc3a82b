<?php
/*
 * @Description: 添加办公/社区
 * @version: V7.0
 * @Author: cj
 * @Date: 2021-12-15 17:26:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-06-16 10:20:42
 */

namespace package\common\model\projectData\src;

use package\common\model\projectData\config\Code;

trait AddProjectItem
{
    /**
     * @author: cj
     * @name: addProjectItem
     * @msg: 添加办公/社区
     * @param {params} 目前值获取社区有关的数据，兼容社区这块数据需增加
     * @return {*}
     */
    public function addProjectItem()
    {
        $params = [PROXY_ROLE["projectId"], 'EnableLandline', 'FeaturePlan', 'Location', 'Street', 'City', 'PostalCode',
            'States', 'Country', 'TimeZone', 'CustomizeForm', 'ChargeMode', 'SendExpireEmailType', 'SendRenew', 'ProjectType',
            'EnableSmartHome', 'IsCommunityCalls', 'IsHaveAccessArea', 'NameDisplay?:enum("0", "1")', 'EnableScanToRegister?:enum("0","1","")',
            'EnableDormakaba?:enum("0","1","")'];
        list($projectId, $enableLandline, $featurePlan, $location, $street, $city, $postalCode, $states, $country, $timeZone,
            $customizeForm, $chargeMode, $sendExpireEmailType, $sendRenew, $projectType, $enableSmartHome,
            $isCommunityCalls, $isHaveAccessArea, $nameDisplay, $enableScanToRegister, $enableDormakaba) = $this->getParams($params);

        $nameDisplay = $nameDisplay === '1'? 1 : 0;
        //需要过滤前后的空格
        $postalCode = trim($postalCode);
        $sendExpireEmailType = $sendExpireEmailType ?: 2;
        $enableScanToRegister = $enableScanToRegister == null ? "0" : $enableScanToRegister;
        $enableDormakaba = $enableDormakaba == null ? "0" : $enableDormakaba;
        $this->log->debug('params={params}', ["params"=>json_encode($this->getParams($params))]);
        $this->loadUtil('common', true);
        $myData = $this->utils->_common->common->getAccountInfoWithID($projectId, ["ParentID","ManageGroup"]);
        if ($projectType == 0) {
            $infoTable = PROXY_TABLES["communityInfo"];
            $enableSmartHome = $enableSmartHome ? :0;
        } elseif ($projectType == 1) {
            $infoTable = PROXY_TABLES["officeInfo"];
            $enableSmartHome = 0;
        }
        $projectAccount = $this->addAutoProject($location, $myData["ParentID"], $projectType, [
            "timeZone" => $timeZone,
            "group" => $myData["ManageGroup"],
            "chargeMode" => $chargeMode,
            "enableLandline" => $enableLandline,
            "customizeForm" => $customizeForm,
            "sendExpireEmailType" => $sendExpireEmailType,
            "confusion" => 0,
            "sendRenew" => $sendRenew,
            "featurePlan" => $featurePlan,
            'enableSmartHome' => $enableSmartHome,
            'isCommunityCalls' => $isCommunityCalls,
            'isHaveAccessArea' => $isHaveAccessArea,
        ]);
        $this->loadUtil('account', true);
        $peoject = $this->utils->_common->account->accountSelectByKey('Account', $projectAccount, 'ID,UUID')[0];

        if ($projectType == 0) {
            $this->dao->communityInfo->update(
                ["AccountID" => $peoject['ID'], "Street" => $street, "City" => $city, "PostalCode" => $postalCode,
                    "Country" => $country, "States" => $states, 'NameDisplay' => $nameDisplay,
                    'CommunityPlan' => COMMUNITY_PLAN_AK, 'EnableScanToRegister' => $enableScanToRegister,
                    'EnableDormakaba' => $enableDormakaba], 'AccountID'
            );
        } elseif ($projectType == 1) {
            $this->dao->officeInfo->update(
                ["AccountUUID" => $peoject['UUID'], "Street" => $street, "City" => $city, "PostalCode" => $postalCode,
                    "Country" => $country, "States" => $states, 'NameDisplay' => $nameDisplay], 'AccountUUID'
            );
        }
        //通知go微服务,处理社区开启dormakaba的逻辑
        if ($enableDormakaba == "1") {
            $this->loadUtil('common');
            $insUUID = $this->utils->common->getAccountInfoWithID($myData["ManageGroup"], ['UUID'])['UUID'];

            $sql = 'select ThirdAccountUUID from '. PROXY_TABLES['DormakabaAccount'] .' where AccountUUID = :AccountUUID for update';
            $dormakabaInfo = $this->db->querySList($sql, [':AccountUUID' => $insUUID]);
            if(count($dormakabaInfo) === 0){
                $this->output->echoErrorMsg(STATE_DORMAKABA_ACCOUNT_NOT_EXISTS);
            } else {
                $siteParam = [
                    'InsUuid' => $insUUID,
                    'DormakabaAccountUuid' => $dormakabaInfo[0]['ThirdAccountUUID'],
                    'CommunityUuid' => $peoject['UUID'],
                    'CommunityName' => $location,
                    'Country' => $country,
                    'State' => $states,
                    'PostCode' => $postalCode,
                    'Type' => 2, //1=单住户,2=社区项目',
                ];
                $this->loadProvider('phpAdaptNotify');
                $this->services->phpAdaptNotify->addDormakabaSite($siteParam);
            }
        }
        // TODO 社区那还需要增加AptPinType和NumberOfApt字段

        // 调用这块Model需自行添加AuditLog
        return $projectAccount;
    }

    /**
     * @author: cj
     * @name: addAutoProject
     * @msg: 添加社区/办公
     * @param {location, parentId, projectType, options}  projectType 0:社区 1:办公
     * @return {*}
     */
    public function addAutoProject($location, $parentId, $projectType, $options)
    {
        $timeZone = $options['timeZone'];
        $group = $options['group'];
        $chargeMode = $options['chargeMode'] !== null ? $options['chargeMode'] : '0';
        $enableLandline = $options['enableLandline'] !== null ? $options['enableLandline'] : 1;
        $customizeForm = $options['customizeForm'] !== null ? $options['customizeForm'] : 3;
        $sendExpireEmailType = $options['sendExpireEmailType'] ?: 2;
        $confusion = $options['confusion'] !== null ? $options['confusion'] : 0;
        $sendRenew = $options['sendRenew'] !== null ? $options['sendRenew'] : 0;
        $featurePlan = $options['featurePlan'];
        $enableSmartHome = $options['enableSmartHome'];
        $isCommunityCalls = $options['isCommunityCalls'] !== null ? $options['isCommunityCalls'] : 0;
        $isHaveAccessArea = $options['isHaveAccessArea'] !== null ? $options['isHaveAccessArea'] : 0;
        $this->log->debug('location={location},parentId={parentId},options={options}', [
        "location"=>$location,"parentId"=>$parentId,"options"=>json_encode($options)]);
        // 6.4 补充ParentUUID
        $this->loadUtil('common');
        $parentUUID = $this->utils->common->getAccountInfoWithID($parentId, ['UUID'])['UUID'];

        //自动生成账号
        $proAccount = $this->getAutoAccount();
        if ($proAccount == false) {
            $this->output->echoErrorMsg(STATE_ADD_FAIL, ['externalErrorObj' => Code::EXT_STATE_AUTO_ACCOUNT_FAIL]);
        }

        $password = $this->share->util->generatePw(8);
        // 生成uuid
        $accountUUID = $this->share->util->uuid();
        $infoUUID = $this->share->util->uuid();
        $now = $this->share->util->getNow();
        $accountTable = PROXY_TABLES["account"];
        $manageFeature = PROXY_TABLES["manageFeature"];
        
        // 办公和社区信息表不同，这边有所区分
        if ($projectType == 0) {
            $accountGrade = COMMUNITYGRADE;
            $infoTable = PROXY_TABLES["communityInfo"];
        // $bindArrayInfo = [];
        } elseif ($projectType == 1) {
            $accountGrade = OFFICEGRADE;
            $infoTable = PROXY_TABLES["officeInfo"];
            $bindArrayInfo = [":Switch" => $this->share->util->bitOperation($enableLandline+4, $enableSmartHome, 5),
            ":UUID" => $infoUUID, ":AccountUUID" => $accountUUID, ":CreateTime" => $now];
            $isCommunityCalls = 0;
        }
        //户户通
        $confusion = $this->share->util->bitOperation($confusion, $isCommunityCalls, 5);
        $bindArray = [
            ":Account" => $proAccount, ":Grade" => $accountGrade,
            ":Role" => 2, ':ParentID' => $parentId, ':Location' => $location,
            ':Phone' => "", ':Info' => "", ':TimeZone' => $timeZone,
            ":Special" => 1, ":ManageGroup" => $group, ":ChargeMode" => $chargeMode,
            ":CustomizeForm" => $customizeForm, ":SendExpireEmailType" => $sendExpireEmailType,":UUID" => $accountUUID,
            ":Flags" => $confusion, ":SendRenew" => $sendRenew, ':ParentUUID' => $parentUUID, ':CreateTime' => $this->share->util->getNow()];
        $this->db->insert2List($accountTable, $bindArray);
        $id = $this->db->lastInsertId();
        $this->db->insert2List($manageFeature, [":AccountID" => $id,":FeatureID"=>$featurePlan]);
        
        if ($projectType === 0) {
            $bindArrayInfo = [':Switch' => $this->share->util->bitOperation($enableLandline+4, $enableSmartHome, 5),
            ':AccountID' => $id, ':IsNew' => 1,":AccountUUID" => $accountUUID];
            //pm门禁权限
            $bindArrayInfo[':Switch'] = $this->share->util->bitOperation($bindArrayInfo[':Switch'], $isHaveAccessArea, 7);
        }
        // 6.2需求高级收费如果0元直接刷新到2299年
        $this->loadUtil('common', true);
        // 根据dis获取UUID
        $feeUUID = $this->utils->_common->common->getTableInfo(
            ['AccountID' => $parentId, "FeatureID"=>$featurePlan],
            ['FeeUUID'],
            $manageFeature
        )[0]['FeeUUID'];
        $this->loadProvider('billsysUtil');
        $fee = $this->services->billsysUtil->queryFeaturePlan(["FeeUUID"=>$feeUUID])['data'][0];
        if ($fee["FeatureFee"] == 0) {
            $bindArrayInfo[":FeatureExpireTime"] = DEFAULTEXPIRETIME;
        } else {
            $bindArrayInfo[":FeatureExpireTime"] = null;
        }

        $this->db->insert2List($infoTable, $bindArrayInfo);

        //插入RBACDataGroup表
        $dataGroup = $this->dao->rBACDataGroup->getProjectDataGroup($accountUUID);
        $rbacDataGroupUUID = $this->share->util->uuid();
        $dataGroupData = [
            'UUID' => $rbacDataGroupUUID,
            'DataGroup' => $dataGroup
        ];
        $this->dao->rBACDataGroup->insert($dataGroupData);

        //添加AccountDataGroup表数据
        $accountDataGroupData = [
            'RBACDataGroupUUID' => $rbacDataGroupUUID,
            'AccountUUID' => $accountUUID,
            'UUID' => $this->share->util->uuid(),
        ];
        $this->dao->accountDataGroup->insert($accountDataGroupData);

        //添加RBACRoleMap表
        $rbacRoleUUID = $this->dao->rBACRole->getRBACRoleUUIDByGrade($accountGrade);
        $roleMapUUID = $this->share->util->uuid();
        $roleMapData = [
            'UUID' => $roleMapUUID,
            'RBACRoleUUID' => $rbacRoleUUID,
            'UserInfoUUID' => $accountUUID,
        ];
        $this->dao->rBACRoleMap->insert($roleMapData);

        return $proAccount;
    }

    /**
     * @author: cj
     * @name: getAutoAccount
     * @msg: 自动生成Account
     * @param {*}
     * @return {account值/false}
     */
    public function getAutoAccount()
    {
        $maxTimes = 10;
        $accountTable = PROXY_TABLES["account"];
        for ($i = 0; $i < $maxTimes; $i++) {
            $account = $this->share->util->randString(7);
            $count = $this->db->querySList("select count(*) from $accountTable where Account = :Account", [":Account"=>$account])[0]["count(*)"];
            if ($count > 0) {
                continue;
            }
            return $account;
        }
        return false;
    }
}
