<?php

namespace model\userData;

trait query
{

    /**
     * @msg: 修改密码前检查密码正确
     */
    public function checkPw()
    {
        global $cMessage;
        $params = [
            "PassWd" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $passwd = $params["PassWd"];
        $count = $this->db->querySList("select count(*) from PersonalAccount where ID=:ID and Passwd = :Passwd", [":ID" => $userId, ":Passwd" => md5($passwd)])[0]["count(*)"];
        if ($count == 0) {
            $cMessage->echoErrorMsg(StatePasswordIncorrect);
        }
    }

    /**
     * @msg: app查询通话记录列表
     * @service: callHistoryUtil
     */
    public function queryCallHistory()
    {
        // 特殊结构，直接获取全局用户和代理用户
        global $gApp;
        $self = $gApp["user"];
        $mainUser = $gApp["userAlias"];
        $params = [
            "SIP" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $sip = $params["SIP"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $sipGroup = $this->db->queryAllList("SipGroup2", ["equation" => [":Account" => $mainUser]])[0]["SipGroup"];

        $sql = "select * from %s where (CallerID = :Account or CalleeID = :Account or CalleeID = :MainAccount) order by StartTime desc";
        $bindArray = [':Account' => $self, ":MainAccount" => $mainUser];
        if ($sip) {
            $sql = "select * from %s where (CallerID = :Account AND CalleeID = :DevSipNum) or  (CallerID = :DevSipNum AND CalleeID = :Account) or  (CallerID = :DevSipNum AND CalleeID = :MainAccount) order by StartTime desc";
            $bindArray[':DevSipNum'] = $sip;
        }
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer, true);
        $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql, $bindArray);
        list($count, $simtArray) = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, $offset, $rows);

        $data = [];
        foreach ($simtArray as $key => $value) {
            $isCallee = ($self == $value['CalleeID'] || $sipGroup == $value['CalleeID']);
            $curl = [];
            $curl['ID'] =  $value['ID'];
            $starttime = $value['StartTime'];
            // $this->log->payLog("#starttime#time=$starttime"."#settimezone=".\util\time\setTimeZone($value['StartTime'], $timeZone, $customizeForm));
            $curl['Time'] = \util\time\setYesterday(\util\time\setTime(\util\time\setTimeZone($value['StartTime'], $timeZone, $customizeForm), $customizeForm), $timeZone);
            $aftertime = $curl['Time'][1];
            $this->log->payLog("#aftertime#time=$aftertime");
            $curl['DevSipNum'] = $isCallee ? $value['CallerID'] : $value['CalleeID'];
            $curl['DevLocation'] = $isCallee ? $value['CallerName'] : $value['CalleeName'];
            $curl['Status'] = $value['Status'];
            $curl['Type'] = $isCallee ? ($value['IsAnswer'] == 1 ? 2 : 1) : 3;
            array_push($data, $curl);
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: web获取通话记录列表
     */
    public function queryCallHistoryForWeb()
    {
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "BeginTime" => "",
            "EndTime" => "",
            "Name" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $name = $params["Name"];
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $where = "";
        $bindArray = [":Account" => $user];
        if ($beginTime) {
            $where = "$where AND StartTime > :BeginTime";
            $bindArray[':BeginTime'] = \util\time\setTimeZone($beginTime, $timeZone, "", "-");
        }
        if ($endTime) {
            $where = "$where AND StartTime < :EndTime";
            $bindArray[':EndTime'] = \util\time\setTimeZone($endTime, $timeZone, "", "-");
        }
        if ($name) {
            $where = "$where AND (CallerName like :Name or CalleeName like :Name)";
            $bindArray[':Name'] = "%$name%";
        }
        $sql = "select * from %s where Node = :Account $where order by ID desc";
        $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql, $bindArray);
        list($count, $simtArray) = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, $offset, $rows);
        $simtArray = \util\time\setQueryTimeZone($simtArray, $timeZone, $customizeForm);
        $data = [];
        $data["total"] = $count;
        $data["row"] = $simtArray;
        foreach ($data["row"] as &$value) {
            $value["CallTime"] = $value["IsAnswer"] != 1 ? MSGTEXT["noAnswer"] : $value["Duration"];
        }


        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 查询用户PersonalAccount中所有数据，密码类除外
     */
    public function queryOwernData()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $user]])[0];
        unset($data["Passwd"]);
        unset($data["SipPwd"]);

        $role = $data["Role"];
        if ($role == PERENDSROLE) {
            $mainUserData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $data["ParentID"]]])[0];
            $data["RoomNumber"] = $mainUserData["RoomNumber"];
            $data['mainUserEmail'] = $mainUserData['Email'];
            $data['mainUserPhone'] = $mainUserData['Phone'];
        }
        if ($role == COMENDMROLE) {
            $roomData = $this->db->queryAllList("CommunityRoom", ["equation" => [":ID" => $data["RoomID"]]])[0];
            $data["RoomNumber"] = $roomData["RoomName"];
            if ($roomData['Floor'] !== '') {
                $data['RoomNumber'] = $roomData['RoomName'].' ('.MSGTEXT['floor'].' '.$roomData['Floor'].')';
            }
        }
        if ($role == COMENDSROLE) {
            $roomData = $this->db->querySList("select R.RoomName,R.Floor,P1.Email,P1.Phone from CommunityRoom R join PersonalAccount P1 on R.ID = P1.RoomID where P1.ID = :ID", [":ID" => $data["ParentID"]])[0];
            $data["RoomNumber"] = $roomData['RoomName'];
            $data['mainUserEmail'] = $roomData['Email'];
            $data['mainUserPhone'] = $roomData['Phone'];
            if ($roomData['Floor'] !== '') {
                $data['RoomNumber'] = $roomData['RoomName'].' ('.MSGTEXT['floor'].' '.$roomData['Floor'].')';
            }
        }
        $data["EnableLandline"] = "1";
        if (in_array($role, COMROLE) or $role == PMENDMROLE) {
            $id = $data["ParentID"];
            if ($role == COMENDSROLE) {
                $id = $this->db->querySList("select ParentID from PersonalAccount where ID = :ID", [":ID" => $data["ParentID"]])[0]["ParentID"];
            }
            $data["EnableLandline"] = intval($this->db->querySList("select Switch from CommunityInfo where AccountID = :AccountID", [":AccountID" => $id])[0]["Switch"]) & 1;
        }

        if (in_array($role, OFFROLE)) {
            $id = $data["ParentID"];
            $data["department"] = $this->db->querySList(
                "SELECT UnitName FROM CommunityUnit WHERE ID = :ID",
                [":ID" => $data["UnitID"]]
            )[0]["UnitName"];
            $data["EmployeeID"] = $this->db->querySList('select EmployeeID from PersonalAccountOfficeInfo where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $data['UUID']])[0]['EmployeeID'];
        }

        // 如果是pmapp的用户，需要取对应pm的邮箱
        if ($role == PMENDMROLE) {
            $pmMap = $this->db->querySList('select * from PmAccountMap where PersonalAccountUUID = :PersonalAccountUUID', [':PersonalAccountUUID' => $data['UUID']])[0];
            $pmAccountData = $this->db->querySList('select Email from Account where UUID = :UUID', [':UUID' => $pmMap['AccountUUID']])[0];
            $data['Email'] = $pmAccountData['Email'];
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 获取app中首页log的未读条数
     */
    public function queryActiveUnRead()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $user = $userData["Account"];
        $role = $userData["Role"];
        $sql = "select ID from %s where CalleeID = :Account AND Status = 0";
        $bindArray = [':Account' => $user];
        $offset = 0;
        $rows = 100;
        $tables = $this->services["callHistoryUtil"]->getCallHistoryTablesInfo($sql, $bindArray);
        list($call) = $this->services["callHistoryUtil"]->getCallHistorySearchResult($tables, $sql, $bindArray, $offset, $rows);
        $motion = 0;
        if (in_array($role, PERROLE)) {
            $sql = "select ID,Status from %s where Node = :Node order by ID desc";
            $bindArray = [":Node" => $user];
            $offset = 0;
            $rows = 100;
            $tables = $this->services["captureUtil"]->getCaptureTablesInfo(0, $sql, $bindArray);
            list($count, $simtArray) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
            $motion = 0;
            foreach ($simtArray as $value) {
                if ($value["Status"] == 0) {
                    $motion++;
                }
            }
        }
        \util\computed\setGAppData(["callCount" => $call, "motionCount" => $motion]);
    }

    /**
     * @msg:
     */
    public function queryAllAD()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $role = $userData["Role"];
        $user = $userData["Account"];

        $apps = $this->db->querySList("select A.Name,A.ID from PersonalAccount A left join PersonalAccount B on A.ParentID = B.ID where B.Account = :Account AND (A.Role = 11 or A.Role = 21) AND A.ExpireTime != :ExpireTime", [":Account" => $user, ":ExpireTime" => DEFAULTEXPIRETIME]);
        $deviceTable = in_array($role, PERROLE) ? "PersonalDevices" : "Devices";

        $devices = $this->db->querySList("select Location,ID,Type from $deviceTable where Node = :Node", [":Node" => $user]);
        $doors = [];
        $indoors = [];
        foreach ($devices as $value) {
            if ($value["Type"] == 2) {
                array_push($indoors, $value);
            } else {
                array_push($doors, $value);
            }
        }
        \util\computed\setGAppData(["apps" => $apps, "doors" => $doors, "indoors" => $indoors]);
    }

    /**
     * @msg: 获取billinfo
     */
    public function queryBill()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->querySList("select * from PersonalBillingInfo where Account = :Account", [":Account" => $user])[0];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 获取用户的personalAccountCnf信息
     */
    public function queryCnf()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->queryAllList("PersonalAccountCnf", ["equation" => [":Account" => $user]])[0];
        $data["EnableIpDirect"] = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $user]])[0]["EnableIpDirect"];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 获取呼叫队列设置
     */
    public function queryCallSqueue()
    {
        $params = [
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];

        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID" => $userId])[0]["Role"];

        $apps = $this->db->querySList("select A.Name,A.ID,A.Phone,A.Phone2,A.Phone3,A.PhoneCode from PersonalAccount A left join PersonalAccount B on A.ParentID = B.ID where (B.Account = :Account AND (A.Role = 11 or A.Role = 21)) 
        union select Name,ID,Phone,Phone2,Phone3,PhoneCode from PersonalAccount where Account = :Account", [":Account" => $user]);
        $deviceTable = in_array($role, PERROLE) ? "PersonalDevices" : "Devices";
        $devices = $this->db->querySList("select Location,ID,Type from $deviceTable where Node=:Node and Type = 2", [":Node" => $user]);
        $data = [];
        foreach ($apps as $value) {
            array_push($data, ["Label" => $value["Name"], "Key" => "6-" . $value["ID"]]);
            if ($value["Phone"] != "") {
                array_push($data, ["Label" => "+" . $value["PhoneCode"] . $value["Phone"], "Key" => "7-" . $value["ID"]]);
            }
            if ($value["Phone2"] != "") {
                array_push($data, ["Label" => "+" . $value["PhoneCode"] . $value["Phone2"], "Key" => "8-" . $value["ID"]]);
            }
            if ($value["Phone3"] != "") {
                array_push($data, ["Label" => "+" . $value["PhoneCode"] . $value["Phone3"], "Key" => "9-" . $value["ID"]]);
            }
        }
        foreach ($devices as $value) {
            array_push($data, ["Label" => $value["Location"], "Key" => "2-" . $value["ID"]]);
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 个人phone信息
     */
    public function getPerData()
    {
        $params = [
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];

        $data = $this->db->querySList("select Name,Email,Phone,Phone2,Phone3,PhoneCode from PersonalAccount where ID = :ID", [":ID" => $userId])[0];
        $keyData = $this->db->queryAllList("PersonalPrivateKey", ["equation" => [":AccountID" => $userId, ':Special' => 1]]);
        $data["Code"] = count($keyData) == 0 ? "" : $keyData[0]["Code"];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 社区主账户信息，App上获取phone不适用这个，考虑从账户
     */
    public function getComData()
    {
        $params = [
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];

        $myData = $this->db->querySList("SELECT * FROM PersonalAccount WHERE ID = $userId")['0'];
        if (in_array($myData['Role'], COMROLE)) {
            if ($myData['Role'] == COMENDSROLE) {
                $mainId = $this->db->querySList("SELECT ID FROM PersonalAccount WHERE UUID = :UUID", [':UUID' => $myData['ParentUUID']])[0]['ID'];
            } else {
                $mainId = $userId;
            }
            $data = $this->db->querySList(
                "select P.FirstName,P.LastName,P.Name,P.SipAccount,P.Phone,P.Phone2,P.Phone3,P.PhoneCode,P.Email,P.TimeZone,R.RoomName,Pf.CallType 
            from PersonalAccount P join PersonalAccountCnf Pf on P.Account = Pf.Account
            join CommunityRoom R on P.RoomID = R.ID where P.ID = :ID",
                [":ID" => $mainId]
            )[0];
            // V6.5 兼容app从账号初始化Pin的欢迎用户
            if ($myData['Role'] == COMENDSROLE) {
                $data['FirstName'] = $myData['FirstName'];
                $data['LastName'] = $myData['LastName'];
                $data['Name'] = $myData['Name'];
                $data['Email'] = $myData['Email'];
            }

            /// 新旧社区
            $isNew = $this->db->querySList('select CI.IsNew from CommunityInfo CI join PersonalAccount P on CI.AccountID=P.ParentID
            where P.ID=:ID', [':ID' => $mainId])[0]['IsNew'];

            $this->log->actionLog('#model#userData#isNew=' . $isNew);
            if ($isNew == 0) {
                $keyData = $this->db->queryAllList("PersonalPrivateKey", ["equation" => [":AccountID" => $userId, ":Special" => 1]]);
                $data["Code"] = count($keyData) == 0 ? "" : $keyData[0]["Code"];
            } else {
                $keyData = $this->db->queryAllList("CommPerPrivateKey", ["equation" => [":Account" => $user, ":Special" => 1]]);
                $data["Code"] = count($keyData) == 0 ? "" : $keyData[0]["Code"];
            }
        }

        if (in_array($myData['Role'], OFFROLE) or $myData['Role'] == PMENDMROLE) {
            $data = $this->db->querySList(
                "select P.Name,P.FirstName,P.LastName,P.SipAccount,P.Phone,P.Phone2,P.Phone3,P.PhoneCode,P.Email,P.TimeZone,Pf.CallType 
                from PersonalAccount P join PersonalAccountCnf Pf on P.Account = Pf.Account where P.ID = :ID",
                [":ID" => $userId]
            )[0];

            $keyData = $this->db->queryAllList("CommPerPrivateKey", ["equation" => [":Account" => $user, ":Special" => 1]]);
            $data["Code"] = count($keyData) == 0 ? "" : $keyData[0]["Code"];
        }


        \util\computed\setGAppData(["data" => $data]);
    }

    public function getComSubData()
    {
        $params = [
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->querySList("select PhoneCode, Phone from PersonalAccount where ID = :ID", [":ID" => $userId])[0];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @msg: 获取剩余过期天数和过期时间
     */
    public function getExpireDay()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $expireTime = $myData["ExpireTime"];
        $expireTimer = strtotime($expireTime);
        $now = time();
        $day = ceil(($expireTimer - $now) / (60 * 60 * 24));
        $day = $day < 0 ? 0 : $day;
        $data = ["Day" => $day, "ExpireTime" => $expireTime];
        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm);
        \util\computed\setGAppData($data[0]);
    }

    /**
     * @msg: 获取落地剩余过期天数和过期时间
     */
    public function getLandlineTime()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $expireTime = $myData["PhoneExpireTime"];
        $expireTimer = strtotime($expireTime);
        $now = time();
        $day = ceil(($expireTimer - $now) / (60 * 60 * 24));
        $day = $day < 0 ? 0 : $day;
        $data = ["Day" => $day, "ExpireTime" => $expireTime];
        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm);
        \util\computed\setGAppData($data[0]);
    }

    public function getNextExpireTime()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "Month" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $month = $params["Month"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $role = $data["Role"];
        if (in_array($role, PERROLE)) {
            $expireTime = $data["PhoneExpireTime"];
        } else {
            $expireTime = $data["ExpireTime"];
        }
        $now = \util\computed\getNow();
        $nextNow = \util\computed\computedLastDate($now, $month);
        $nextExpireTime = \util\computed\computedLastDate($expireTime, $month);
        if (strtotime($nextExpireTime) < strtotime($nextNow)) {
            $nextExpireTime = $nextNow;
        }
        $data = ["ExpireTime" => $nextExpireTime];
        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm);
        \util\computed\setGAppData($data[0]);
    }

    public function getReceipt()
    {
        $params = [
            "user" => "",
            "data" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["user"];
        $data = $params["data"];
        $billData = $this->db->queryAllList("PersonalBillingInfo", ["equation" => [":Account" => $user]])[0];
        $data = array_merge($data, $billData);
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":Account" => $user]])[0];
        $name = $myData["Name"];
        $buyerInfo = $name . " " . $myData["Email"];
        $data["BuyerInfo"] = $buyerInfo;
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getTempKeyOpera()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $tempKeyPermission = $this->db->querySList("select TempKeyPermission from PersonalAccount where ID = :ID", [":ID" => $userId])[0]["TempKeyPermission"];
        // qrcode是否过期
        $expireTime = $this->db->querySList("select FeatureExpireTime from CommunityInfo C join PersonalAccount P on P.ParentID = C.AccountID where P.ID = :ID", [":ID" => $userId]);
        $now = \util\computed\getNow();
        if (strtotime($expireTime) < strtotime($now)) {
            $tempKeyPermission = '1';
        }
        \util\computed\setGAppData(["TempKeyPermission" => $tempKeyPermission]);
    }

    public function getChargeMode()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $role = $myData["Role"];
        $manageData = $this->db->querySList("select A.ChargeMode,A.ID,A.ParentID from Account A join PersonalAccount P on A.ID = P.ParentID where P.ID = :ID", [":ID" => $userId])[0];
        //modified by chenyc,2021-06-30,代码强制走‘pay by installer’
        //$chargeMode = $manageData["ChargeMode"];
        $chargeMode = 1;
        $disChargeMode = $this->db->querySList("select ChargeMode from Account where ID = :ID", [":ID" => $manageData["ParentID"]])[0]["ChargeMode"];
        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        $subscriptions = 1;

        $type = in_array($role, PERROLE) ? 'single' : (in_array($role, COMROLE) ? 'multiple' : 'office');
        $data = $this->services["billsysUtil"]->getCharge($type, $manageData["ID"], [$userId], PAYSUBDRCIPTION);
        $data = $data[$userId];

        \util\computed\setGAppData(["ChargeMode" => $chargeMode, "Subscriptions" => $subscriptions]);
    }

    /**
     * 获取落地缴费情况
     */
    public function getLandlineChargeRes()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAlias"];
        $phoneMsg = $this->db->querySList("select PhoneExpireTime, PhoneStatus from PersonalAccount where Account = :Account", [":Account" => $userId])[0];
        $expireTime = $phoneMsg['PhoneExpireTime'];
        $now = \util\computed\getNow();
        $phoneStatus = $phoneMsg['PhoneStatus'];
        // 落地开启、未激活情况，等同于关闭
        if ($phoneStatus == '1' && $expireTime == '2020-01-01 00:00:00') {
            $phoneStatus = '0';
        }
        // 落地开启，不是未激活但已过期
        if ($expireTime != '2020-01-01 00:00:00' && strtotime($expireTime) < strtotime($now)) {
            \util\computed\setGAppData(["LandlineChargeRes" => '0']);
        } else {
            \util\computed\setGAppData(["LandlineChargeRes" => '1']);
        }
        \util\computed\setGAppData(["LandlineStatus" => $phoneStatus]);
    }

    public function queryCommunityName()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $data = $this->db->querySList("select A.Location from Account A join PersonalAccount P on A.ID =P.ParentID where P.ID = :ID", [":ID" => $userId])[0];
        $location = $data["Location"];
        \util\computed\setGAppData(["Location" => $location]);
    }

    public function queryAccount()
    {
        $params = [
            "userAliasId" => "",
            "userAlias" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $users = $this->db->querySList("select Name,ExpireTime from PersonalAccount where (ParentID = :ParentID AND (Role = " . PERENDSROLE . " OR Role = " . COMENDSROLE . "))", [":ParentID" => $userId]);

        $myData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userId]])[0];
        $role = $myData["Role"];
        $nowTime = time();

        foreach ($users as &$value) {
            $value['ExpireTime'] = $value['ExpireTime'] == DEFAULTEXPIRETIME ?
                ["type" => 0, "content" => ""] : (strtotime($value['ExpireTime']) >= $nowTime ?
                    ["type" => 0, "content" => \util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)] :
                    ["type" => 1, "content" => \util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)]);
            $value['Type'] = 5;
        }
        $deviceTabel = in_array($role, PERROLE) ? "PersonalDevices" : "Devices";
        $devices = $this->db->querySList("select Location as Name,ExpireTime,Type from $deviceTabel where Node = :Node", [":Node" => $user]);

        foreach ($devices as &$value) {
            $value['ExpireTime'] = $value['ExpireTime'] == DEFAULTEXPIRETIME ?
                ["type" => 0, "content" => ""] : (strtotime($value['ExpireTime']) >= $nowTime ?
                    ["type" => 0, "content" => \util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)] :
                    ["type" => 1, "content" => \util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)]);
        }
        $data = array_merge($users, $devices);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function queryFreeSubApp()
    {
        $params = [
            "userAliasId" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];

        $userData = $this->db->querySList("select ParentID from PersonalAccount where ID = :ID", [":ID" => $userId])[0];
        $myCnf = $this->db->queryAllList("PersonalAccountCnf", ["equation" => [":Account" => $user]])[0];
        if (in_array($userData["Role"], PERROLE)) {
            $data = $this->services["billsysUtil"]->getCharge('single', $userData["ParentID"], [$userId], PAYADDAPP);
        } else {
            $data = $this->services["billsysUtil"]->getCharge('multiple', $userData["ParentID"], [$userId], PAYADDAPP);
        }
        $data = $data[$userId];
        \util\computed\setGAppData(["data" => ["number" => $data["AppNumber"] - 1 + $myCnf["FreeAppCount"]]]);
    }

    public function queryInfoForPM()
    {
        global $cMessage;
        $params = [
            "ID" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $data = $this->db->querySList('select Account,UnitID,ParentID,Role,RoomID,Name,FirstName,LastName,Active,ExpireTime,Initialization,Email,PhoneCode,MobileNumber,Phone,Phone2,Phone3
        from PersonalAccount where ID = :ID', [":ID" => $id])[0];
        $node = $data['Account'];
        if ($data['Role'] == 21) {
            $mainData = $this->db->querySList('select Account,UnitID,ParentID,RoomID,Email from PersonalAccount where ID=:ID', [':ID' => $data['ParentID']])[0];
            $data['UnitID'] = $mainData['UnitID'];
            $data['ParentID'] = $mainData['ParentID'];
            $data['RoomID'] = $mainData['RoomID'];
            $data['MainUserEmail'] = $mainData['Email'];
            $node = $mainData['Account'];
        }

        if ($data['ParentID'] != $communityId) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $data['UnitName'] = $this->db->querySList('select UnitName from CommunityUnit where ID=:ID', [":ID" => $data['UnitID']])[0]['UnitName'];
        $roomData = $this->db->querySList('select RoomName,Floor from CommunityRoom where ID=:ID', [":ID" => $data['RoomID']])[0];
        $data['RoomName'] = $roomData['RoomName'];
        if ($roomData['Floor'] !== '') {
            $data['RoomName'] = $roomData['RoomName'].' ('.MSGTEXT['floor'].' '.$roomData['Floor'].')';
        }
        if ($data['Active'] == 0) {
            $data['ActiveName'] = MSGTEXT['inactivated'];
        } elseif (strtotime($data['ExpireTime'] < time())) {
            $data['ActiveName'] = MSGTEXT['expired'];
        } else {
            $data['ActiveName'] = MSGTEXT['normal'];
        }

        $data['InitializationName'] = [MSGTEXT['unregistered'], MSGTEXT['registered']][$data['Initialization']];
        $data = \util\time\setQueryTimeZone([$data], $timeZone, $customizeForm)[0];
        if ($data['Active'] == 0) {
            $data['ExpireTime'] = '--';
        }
        $data['Device'] = $this->db->querySList('select ID, MAC, Location, Type from Devices where Node=:Node', [':Node' => $node]);
        $data['HasAccessDevice'] = '0';
        foreach ($data['Device'] as $val) {
            if (in_array($val['Type'], ['0', '1', '50'])) {
                $data['HasAccessDevice'] = '1';
            }
        }

        \util\computed\setGAppData(["data" => $data]);
    }
}
