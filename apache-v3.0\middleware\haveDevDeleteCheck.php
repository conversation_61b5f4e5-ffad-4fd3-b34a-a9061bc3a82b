<?php
/*
 * @Description: 
 * @version: 删除用户前设备检查
 * @Author: kxl
 * @Date: 2020-01-17 13:50:35
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
class CHaveDevDeleteCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $cMessage,$cLog;
        $params = ["ID"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);

        $id = $params["ID"];
        $db = \database\CDatabase::getInstance();
        $deleteData = $db->querySList("select Role,Account from PersonalAccount where ID=:ID",[":ID"=>$id])[0];
        $role = $deleteData["Role"];
        $account = $deleteData["Account"];
        $table = $role == PERENDMROLE ? "PersonalDevices" : "Devices";
        $count = $db->querySList("select count(*) from $table where Node = :Node",[":Node"=>$account])[0]["count(*)"];
        $cLog->actionLog("#middle#haveDevDeleteCheck#id=$id;devices=$count");
        if($count > 0) $cMessage->echoErrorMsg(StateBindDevice);
        $next();
    }
}