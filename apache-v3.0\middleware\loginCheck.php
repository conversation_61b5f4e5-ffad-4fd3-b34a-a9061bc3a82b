<?php
/*
 * @Description: 账户密码验证
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-10 10:55:48
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;

class CLoginCheck implements IMiddleware {
    public function handle(\Closure $next) {
        global $cMessage,$cLog;
        $params = ["Account"=>"","passwd"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $account = $params["Account"];
        $passwd = $params["passwd"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#loginCheck#account={$account};passwd={$passwd}");
        
        //ip是否被限制
        $ip = \util\computed\getIp();;
        $limit = \util\computed\getLimitIp($account,$ip);
        if($limit) $cMessage->echoErrorMsg(StateAccountIncorrect2,['time'=>$limit]);
        
        $data = $db->querySList("select ID from PersonalAccount where Role in (".PERENDMROLE.",".COMENDMROLE.") and (Account = :Account or Email = :Account or MobileNumber = :Account) and Passwd = :Passwd",
        [":Account"=>$account,":Passwd"=>$passwd]);
        if(count($data) == 0) {
            include_once __DIR__."/../util/computed.php";
            // TODO记录IP和次数
            $ip = \util\computed\getIp();;
            list($times,$number) = \util\computed\recordAccountIp($account,$ip);

            if($times === false) {
                $num = $number >= 3 ? (5 - $number) : -1;
                $cMessage->echoErrorMsg(StateAccountIncorrect,['number'=>$num]);
            }else{
                $cMessage->echoErrorMsg(StateEmailOrAccountNotExit,['time'=>$times]);
            }
        }
        $next();
    }
}