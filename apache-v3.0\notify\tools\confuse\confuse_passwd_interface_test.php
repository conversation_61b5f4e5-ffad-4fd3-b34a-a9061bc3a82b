<?php

require_once(dirname(__FILE__) . '/../../funcs_password_confuse.php');



for ($i=0; $i<100000; $i++) {
    $code_length = mt_rand(6, 25);
    $code = ConfuseRandomkeys($code_length);
    $confused_code = PasswdEncode($code);

    #插入数据 给c++程序解混淆
    file_put_contents("confuse_test.log", "$code,$confused_code\n", FILE_APPEND);
    $resoluted_code = PasswdDecode($confused_code);

    #echo $code.PHP_EOL;
    #echo $confused_code.PHP_EOL;
    #echo $resoluted_code.PHP_EOL;
    if ($code !== $resoluted_code) {
        echo "code:".$code.PHP_EOL;
        echo "confused_code:".$confused_code.PHP_EOL;
        echo "resoluted_code:".$resoluted_code.PHP_EOL;
        echo "ERROR".PHP_EOL;
        exit(1);
    }
}
