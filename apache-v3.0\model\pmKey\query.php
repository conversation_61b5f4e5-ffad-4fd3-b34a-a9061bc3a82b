<?php

namespace model\pmKey\query;

const RESIDENT = "0";
const DELIVERY = "1";
const STAFF = "2";
const OFFICEPERSONNEL = "30";
const OFFICESTAFF = "31";
const PIN = 0;
const RFCARD = 1;

namespace model\pmKey;

trait query
{
    private function selectForStaff()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $bindArray = [":MngAccountID" => $userId, ":OwnerType" => $this->owernType];

        switch ($serchKey) {
            case 'Name':
                $where = " and Name like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Key':
                $where = " and Code like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $sql = "select * from " . $this->tables[$this->type] . " where MngAccountID = :MngAccountID and OwnerType = :OwnerType $where ";
        $total = count($this->db->querySList($sql, $bindArray));

        $sql .= " order by ID desc limit $offset,$rows";
        $data = $this->db->querySList($sql, $bindArray);
        return [$total, $data];
    }

    private function selectForResent()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId" => "",
            "Build" => "",
            "Room" => "",
            "Key" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $build = $params["Build"];
        $room = $params["Room"];
        $key = $params["Key"];

        $where = "";
        $bindArray = [":Code" => "%$key%", ":MngAccountID" => $userId];
        if ($build != 'all') {
            $where .= ' and U.ID = :UnitID';
            $bindArray[':UnitID'] = $build;
        }
        if ($room != 'all') {
            $where .= ' and R.ID = :RoomID';
            $bindArray[':RoomID'] = $room;
        }

        // V6.5.1 兼容社区Pin和Card未删除干净的情况 key检索时展示错误数据允许删除，否则不展示
        if ($key !== '') {
            $join = 'left join';
        } else {
            $join = 'join';
            // 未住人
            $where .= ' and B.Special = 0';
        }

        $total = $this->db->querySList("select count(*) from " . $this->personalTables[$this->type] . " P $join PersonalAccount A on P.AccountID = A.ID $join PersonalAccount B on B.Account = P.Node $join CommunityRoom R on B.RoomID = R.ID $join CommunityUnit U on B.UnitID = U.ID where P.Code like :Code and P.MngAccountID = :MngAccountID $where", $bindArray)[0]["count(*)"];
        $data = $this->db->querySList("select P.*,A.Name,R.RoomName,R.Floor,U.UnitName from " . $this->personalTables[$this->type] . " P $join PersonalAccount A on P.AccountID = A.ID $join PersonalAccount B on B.Account = P.Node $join CommunityRoom R on B.RoomID = R.ID $join CommunityUnit U on B.UnitID = U.ID where P.Code like :Code and P.MngAccountID = :MngAccountID $where order by P.ID desc limit $offset,$rows", $bindArray);

        foreach ($data as &$val) {
            $id = $val["AccountID"];
            $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]])[0];
            if (in_array($userData["Role"], SUBROLE)) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $userData["ParentID"]]])[0];
            }
            $val["Build"] = $userData["UnitID"];
            $val["Room"] = $userData["RoomID"];
            if ($val['Floor'] !== '') {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
            $val['IsShowAction'] = 1;
            // 说明账号已被删除,不展示详情
            if (empty($val['Name'])) {
                $val['IsShowAction'] = 0;
            }
        }
        unset($val);
        return [intval($total), $data];
    }

    public function select()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $where = "";
        $bindArray = [":MngAccountID" => $userId, ":OwnerType" => $this->owernType];

        $data = [];
        if ($this->owernType == 2) {
            list($data["total"], $data["detail"]) = $this->selectForResent();
        } else {
            list($data["total"], $data["detail"]) = $this->selectForStaff();
        }
        $data["detail"] = \util\time\setQueryTimeZone($data["detail"], $timeZone, $customizeForm, ["BeginTime", "EndTime", "StartTime", "StopTime"]);
        foreach ($data["detail"] as &$val) {
            if ($this->owernType == 2) {
                $val["Devices"] = $this->db->querySList("select D.Location,U.UnitName,D.MAC,D.Relay,D.SecurityRelay,L.Relay as TRelay,L.SecurityRelay as TSecurityRelay from " . $this->personalListTables[$this->type] .
                    " L left join Devices D on L.MAC = D.MAC left join CommunityUnit U on U.ID = D.UnitID where L.KeyID = :ID", [":ID" => $val["ID"]]);
            } else {
                $val["Devices"] = $this->db->querySList("select D.Location,U.UnitName,D.MAC,D.Relay,D.SecurityRelay,L.Relay as TRelay,L.SecurityRelay as TSecurityRelay from " . $this->listTables[$this->type] .
                    " L left join Devices D on L.MAC = D.MAC left join CommunityUnit U on U.ID = D.UnitID where L.KeyID = :ID", [":ID" => $val["ID"]]);
            }
            $arr = [];
            foreach ($val["Devices"] as $val2) {
                array_push($arr, $val2["Location"]);
            }
            $relayStatus = [1, 2, 4, 8];
            $securityRelayStatus = [1, 2];
            foreach ($val["Devices"] as &$value) {
                $tRelay = $value["TRelay"];
                $relay = $value["Relay"];
                $relay = explode(";", $relay);
                $temRelay = [];
                foreach ($relayStatus as $key => $relayValue) {
                    if (($tRelay & $relayValue) == $relayValue) {
                        array_push($temRelay, $key);
                    }
                }
                $value["Relay"] = implode(";", $temRelay);

                $tSecurityRelay = $value["TSecurityRelay"];
                $securityRelay = $value["SecurityRelay"];
                $securityRelay = explode(";", $securityRelay);
                $temSecurityRelay = [];
                foreach ($securityRelayStatus as $key => $securityRelayValue) {
                    if (($tSecurityRelay & $securityRelayValue) == $securityRelayValue) {
                        array_push($temSecurityRelay, $key);
                    }
                }
                $value["SecurityRelay"] = implode(";", $temSecurityRelay);
            }
            unset($value);
            $val["DevicesText"] = implode(";", $arr);
            $val["DateFlag"] = \util\computed\getDateScript($val["DateFlag"]);
        }
        $data["row"] = $data["detail"];
        foreach ($data["row"] as &$val) {
            $val["Repeats"] = [MSGTEXT["never"], MSGTEXT["daily"], MSGTEXT["weekly"]][$val["SchedulerType"]];
        }
        unset($val);

        //V6.1修改 判断dis是否有加密，加密返回****
        if (($this->owernType == 2 && $this->type == 0) || ($this->owernType == 1 && $this->type == 0)) {
            foreach ($data['row'] as &$row) {
                $row['Code'] = \util\computed\setPinIsEncryptPin($userId, $row['Code']);
            }
            foreach ($data['detail'] as &$detail) {
                $detail['Code'] = \util\computed\setPinIsEncryptPin($userId, $detail['Code']);
            }
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    // 快递员（开门密码）
    public function selAPKey()
    {
        $this->owernType = 1;
        $this->type = 0;
        $this->select();
    }

    public function selARKey()
    {
        $this->owernType = 1;
        $this->type = 1;
        $this->select();
    }

    public function selSPKey()
    {
        $this->owernType = 0;
        $this->type = 0;
        $this->select();
    }

    public function selSRKey()
    {
        $this->owernType = 0;
        $this->type = 1;
        $this->select();
    }

    // 住户授权（开门密码）
    public function selRPKey()
    {
        $this->owernType = 2;
        $this->type = 0;
        $this->select();
    }

    public function selRRKey()
    {
        $this->owernType = 2;
        $this->type = 1;
        $this->select();
    }

    public function selAllPinOrCard()
    {
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "Building" => "",
            "Apt" => "",
            "Type" => ""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $building = $params["Building"];
        $apt = $params["Apt"];
        $Type = $params["Type"];

        // 办公、社区标志
        $projectType = $this->db->querySList("select Grade from Account where ID = :ID", [
            ":ID"=>$userId])[0]["Grade"]=="23"?"office":"community";

        // 住户查询条件
        $whereForPerson = "";
        // 快递员查询条件
        $whereForDeliveryOrStaff = "P.CommunityID = :MngAccountID";
        $bindArray = [":MngAccountID" => $userId];

        //  楼栋、房间搜索只对resident有效
        if ($building != 'all') {
            $accountId = [];
            $account = $this->db->querySList("select Account from PersonalAccount where UnitID = :UnitID", [":UnitID" => $building]);
            foreach ($account as $val) {
                array_push($accountId, $val["Account"]);
            }
            if (count($accountId) == 0) {
                $whereForPerson = "1 = 0";
            } else {
                $accountId = implode(",", $accountId);
                $whereForPerson = "P.Account in ($accountId)";
            }
            $whereForDeliveryOrStaff .= " and 1 = 0";
        }
        if ($apt != 'all') {
            $accountId = [];
            $account = $this->db->querySList("select Account from PersonalAccount where RoomID = :ID 
            union all select A.Account from PersonalAccount A join PersonalAccount B on A.ParentID = B.ID where B.RoomID = :ID", [":ID" => $apt]);
            foreach ($account as $val) {
                array_push($accountId, $val["Account"]);
            }
            $accountId = implode(",", $accountId);
            $whereForPerson = "P.Account in ($accountId)";
            $whereForDeliveryOrStaff .= " and 1 = 0";
        }
        if ($whereForPerson == "") {
            $whereForPerson = "P.CommunityID = :MngAccountID";
        } else {
            $whereForPerson .= " and P.CommunityID = :MngAccountID";
        }

        // 区分pin、rfcard搜索字段
        $caseKey = $this->type == query\PIN ? 'PIN' : 'RF Card';
        $join = 'join';
        $specialWhere = " and P.Special = 0";
        $memberWhere = ' B.Role = 21 ';
        switch ($serchKey) {
            case $caseKey:
                // V6.5.1 兼容社区Pin和Card未删除干净的情况 key检索时展示错误数据允许删除，否则不展示
                if ($serchValue !== '') {
                    $join = 'left join';
                    // 避免出现账号(主从),主账号不会删除数据,已删除但是自己添加的Pin无删除的情况
                    // 脏数据无从账号为初始化数据的情况 B.Special = 1未住人
                    $specialWhere = " and (P.Special = 0 or (P.Special = 1 and B.Special = 1)) ";
                    $memberWhere = ' (B.Role = 21 or B.Role is null) ';
                } else {
                    $join = 'join';
                    $specialWhere = " and P.Special = 0 and B.Special = 0";
                    $memberWhere = ' B.Role = 21 ';
                }
                // Card不检索的时候不需要展示未删除脏数据
                if ($serchKey === 'RF Card' && $serchValue === '') {
                    $whereForPerson .= " and P.Code like :Key and B.Special = 0";
                } else {
                    $whereForPerson .= " and P.Code like :Key";
                }
                $whereForDeliveryOrStaff .= $this->type == query\PIN ? " and P.PinCode like :Key" : " and P.CardCode like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            case 'Name':
                $whereForPerson .= " and B.Name like :Key";
                $whereForDeliveryOrStaff .= " and P.Name like :Key";
                $bindArray[":Key"] = "%$serchValue%";
                break;
            default:
                break;
        }

        $data = [];
        $sqlMaster = "select P.ID,'0' as Type,P.Code,B.Name,U.UnitName,R.RoomName,R.Floor,P.CreateTime from ".$this->commPerTables[$this->type]." P $join PersonalAccount B on P.Account = B.Account join CommunityRoom R on B.RoomID = R.ID join CommunityUnit U on B.UnitID = U.ID where $whereForPerson and B.Role = 20 and P.Code is not null and P.Code != ''";
        $sqlMember = "select P.ID,'0' as Type,P.Code,B.Name,U.UnitName,R.RoomName,R.Floor,P.CreateTime from ".$this->commPerTables[$this->type]." P $join PersonalAccount B on P.Account = B.Account $join PersonalAccount A on A.ID = B.ParentID $join CommunityRoom R on A.RoomID = R.ID $join CommunityUnit U on B.UnitID = U.ID where $whereForPerson and $memberWhere and P.Code is not null and P.Code != ''";
        if ($this->type == query\PIN) {
            $sqlMaster .= $specialWhere;
            $sqlMember .= $specialWhere;
        }
        if ($Type == query\RESIDENT) {
            if ($this->type == 0) {
                $whereForPerson .= $specialWhere;
            }
            $total = $this->db->querySList("select count(*) from ".$this->commPerTables[$this->type]." P $join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null and P.Code != ''", $bindArray)[0]["count(*)"];
            $pinOrCardData = $this->db->querySList("select * from ($sqlMaster union $sqlMember) P order by P.CreateTime desc limit $offset,$rows", $bindArray);
        } elseif ($Type == query\DELIVERY) {
            if ($this->type == query\PIN) {
                $total = $this->db->querySList("select count(*) from Delivery P where $whereForDeliveryOrStaff and P.PinCode is not null and P.PinCode != ''", $bindArray)[0]["count(*)"];
                $pinOrCardData = $this->db->querySList("select P.ID, '1' as Type, P.PinCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Delivery P where $whereForDeliveryOrStaff and P.PinCode is not null and P.PinCode != '' order by P.CreateTime desc limit $offset,$rows", $bindArray);
            } elseif ($this->type == query\RFCARD) {
                $total = $this->db->querySList("select count(*) from Delivery P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != ''", $bindArray)[0]["count(*)"];
                $pinOrCardData = $this->db->querySList("select P.ID, '1' as Type, P.CardCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Delivery P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != '' order by P.CreateTime desc limit $offset,$rows", $bindArray);
            }
        } elseif ($Type == query\STAFF) {
            $total = $this->db->querySList("select count(*) from Staff P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != ''", $bindArray)[0]["count(*)"];
            $pinOrCardData = $this->db->querySList("select P.ID, '2' as Type, P.CardCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Staff P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != '' order by P.CreateTime desc limit $offset,$rows", $bindArray);
        } elseif ($Type == query\OFFICEPERSONNEL) {
            if ($this->type == 0) {
                $whereForPerson .= " and P.Special = 0";
            }
            $total = $this->db->querySList("select count(*) from ".$this->commPerTables[$this->type]." P
                join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null
                and P.Code != '' and B.Role = ".query\OFFICEPERSONNEL, $bindArray)[0]["count(*)"];
            $pinOrCardData = $this->db->querySList("select P.ID, '30' as Type, P.Code, B.Name, U.UnitName,
                P.CreateTime from ".$this->commPerTables[$this->type]." P join PersonalAccount B on P.Account
                = B.Account join CommunityUnit U on B.UnitID = U.ID where $whereForPerson and B.Role = "
                .query\OFFICEPERSONNEL." and P.Code is not null and P.Code != '' order by
                P.CreateTime desc limit $offset,$rows", $bindArray);
        } elseif ($Type == query\OFFICESTAFF) {
            if ($this->type == 0) {
                $whereForPerson .= " and P.Special = 0";
            }
            $total = $this->db->querySList("select count(*) from ".$this->commPerTables[$this->type]." P
                join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null
                and P.Code != '' and B.Role = ".query\OFFICESTAFF, $bindArray)[0]["count(*)"];
            $pinOrCardData = $this->db->querySList("select P.ID, '31' as Type, P.Code, B.Name, U.UnitName,
                P.CreateTime from ".$this->commPerTables[$this->type]." P join PersonalAccount B on P.Account
                = B.Account join CommunityUnit U on B.UnitID = U.ID where $whereForPerson and B.Role = "
                .query\OFFICESTAFF." and P.Code is not null and P.Code != '' order by
                P.CreateTime desc limit $offset,$rows", $bindArray);
        } else {
            if ($projectType == 'office') {
                if ($this->type == 0) {
                    $whereForPerson .= " and P.Special = 0";
                }
                // V6.5 增加Office Delivery情况
                if ($this->type == query\PIN) {
                    $codeType = 'PinCode';
                } elseif ($this->type == query\RFCARD) {
                    $codeType = 'CardCode';
                }
                $total = $this->db->querySList("select count(*) from (select P.ID from ".$this->commPerTables[$this->type]." P
            join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null
            and P.Code != '' and B.Role in (".query\OFFICESTAFF.", ".query\OFFICEPERSONNEL.") Union all select P.ID from 
            Delivery P where $whereForDeliveryOrStaff and P.$codeType is not null and P.$codeType != '') P", $bindArray)[0]["count(*)"];
                $pinOrCardData = $this->db->querySList("select * from (select P.ID, B.Role as Type, P.Code, B.Name, U.UnitName,
                P.CreateTime from ".$this->commPerTables[$this->type]." P join PersonalAccount B on P.Account
            = B.Account join CommunityUnit U on B.UnitID = U.ID where $whereForPerson and B.Role in ("
                .query\OFFICESTAFF.", ".query\OFFICEPERSONNEL.") and P.Code is not null and P.Code != '' 
                union select P.ID, '1' as Type, P.$codeType as Code, P.Name, '' as UnitName, P.CreateTime from Delivery P where $whereForDeliveryOrStaff 
                and P.$codeType is not null and P.$codeType != '') P order by P.CreateTime desc limit $offset,$rows", $bindArray);
            } elseif ($projectType == 'community') {
                // pin查询resident、delivery
                if ($this->type == query\PIN) {
                    $whereForPerson .= $specialWhere;
                    $total = $this->db->querySList("select count(*) from (select P.ID from ".$this->commPerTables[$this->type]." P $join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null and P.Code != ''
                UNION ALL select ID from Delivery P where $whereForDeliveryOrStaff and P.PinCode is not null and P.PinCode != '') P", $bindArray)[0]["count(*)"];
                    $pinOrCardData = $this->db->querySList("select * from ($sqlMaster union $sqlMember
                    union select P.ID, '1' as Type, P.PinCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Delivery P where $whereForDeliveryOrStaff and P.PinCode is not null and P.PinCode != '') P order by P.CreateTime desc limit $offset,$rows", $bindArray);
                } elseif ($this->type == query\RFCARD) {
                    $total = $this->db->querySList("select count(*) from (select P.ID from ".$this->commPerTables[$this->type]." P join PersonalAccount B on P.Account = B.Account where $whereForPerson and P.Code is not null and P.Code != ''
                    UNION ALL select ID from Delivery P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != ''
                    UNION ALL select ID from Staff P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != '') P", $bindArray)[0]["count(*)"];
                    $pinOrCardData = $this->db->querySList("$sqlMaster union all $sqlMember
                    UNION ALL select P.ID, '1' as Type, P.CardCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Delivery P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != ''
                    UNION ALL select P.ID, '2' as Type, P.CardCode as Code, P.Name, '' as UnitName, '' as RoomName,'' as Floor, P.CreateTime from Staff P where $whereForDeliveryOrStaff and P.CardCode is not null and P.CardCode != '' order by CreateTime desc limit $offset,$rows", $bindArray);
                }
            }
        }
        $data["total"] = $total;
        // ID格式
        foreach ($pinOrCardData as &$val) {
            $val['ID'] = $val['Type'] . '_' . $val['ID'];
            if ($val['Floor'] !== '' && $val['Floor'] !== null) {
                $val['RoomName'] = $val['RoomName'].' ('.MSGTEXT['floor'].' '.$val['Floor'].')';
            }
            // 查询住户Pin时Name为null即用户账号已被删除,不能展示用户(未删除)自己添加的正确Pin
            if (($Type == query\RESIDENT || $Type === 'all') && empty($val['Name'])) {
                $val['IsShowAction'] = 0;
            } else {
                $val['IsShowAction'] = 1;
            }
        }
        unset($val);
        $data["detail"] = $pinOrCardData;
        $data["detail"] = \util\time\setQueryTimeZone($data["detail"], $timeZone, $customizeForm, ["BeginTime", "EndTime", "StartTime", "StopTime"]);
        $data["row"] = $data["detail"];

        if ($this->type == query\PIN) {
            // pin加密
            foreach ($data['row'] as &$row) {
                $row['Code'] = \util\computed\setPinIsEncryptPin($userId, $row['Code']);
            }
            foreach ($data['detail'] as &$detail) {
                $detail['Code'] = \util\computed\setPinIsEncryptPin($userId, $detail['Code']);
                ;
            }
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    public function selAllPin()
    {
        $this->type = 0;
        $this->selAllPinOrCard();
    }

    public function selAllCard()
    {
        $this->type = 1;
        $this->selAllPinOrCard();
    }
}
