<?php

namespace package\common\model\featurePlan\method;


use package\common\model\featurePlan\config\Code;

trait Get
{
    public function getExpireTime()
    {
        $params = ['DisId', 'FeatureID'];
        list($disId, $featureId) = $this->getParams($params);
        $this->loadUtil('common', true);
        // 根据dis获取UUID
        $disFeatureInfo = $this->utils->_common->common->getTableInfo(
            ['AccountID' => $disId, "FeatureID" => $featureId],
            ['FeeUUID'],
            PROXY_TABLES['manageFeature']
        )[0];
        if (empty($disFeatureInfo)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_PLAN_NOT_BELONG_DIS]);
        }
        //获取方案过期时间
        $this->loadProvider('billsysUtil');
        $fee = $this->services->billsysUtil->queryFeaturePlan(['FeeUUID' => $disFeatureInfo['FeeUUID']])['data'][0];
        return $fee["FeatureFee"] == 0 ? DEFAULTEXPIRETIME : null;
    }

    public function getInfoByProject()
    {
        $params = [PROXY_ROLE['projectId']];
        list($projectId) = $this->getParams($params);
        $this->loadUtil('account');
        $data = $this->utils->account->getManagerInfo($projectId);
        $disId = $data['ParentID'];
        $fData = $this->dao->manageFeature->selectByKey('AccountID', $projectId)[0];
        $fId = $fData['FeatureID'];
        $fData = $this->dao->manageFeature->selectByArray([['AccountID', $disId], ['FeatureID', $fId]])[0];
        $featurePlan = $this->dao->featurePlan->selectByID($fId, 'Item,Name')[0];
        return ['FeatureID'=>$fId, 'FeeUUID'=>$fData['FeeUUID'], 'Item' => $featurePlan['Item'], 'FeaturePlanName' => $featurePlan['Name']];
    }

    /**
     * @description: openapi根据参数获取社区基础、高级方案的ID
     * @param {int} 1-社区基础 2-社区高级
     * @return mixed
     * @throws \Exception
     * @author: csc 2023/8/22 17:53 V6.7.0
     * @lastEditors: csc 2023/8/22 17:53 V6.7.0
     */
    public function getFeatureForOpenApi()
    {
        $params = ['FeaturePlan'];
        list($featurePlan) = $this->getParams($params);
        if (intval($featurePlan) === 1) {
            $featurePlan = $this->dao->featurePlan->selectByArray([['Name', 'Basic'], ['Type', '0']], 'ID')[0]['ID'];
        } else {
            $featurePlan = $this->dao->featurePlan->selectByArray([['Name', 'Premium'], ['Type', '0']], 'ID')[0]['ID'];
        }
        return $featurePlan;
    }
}