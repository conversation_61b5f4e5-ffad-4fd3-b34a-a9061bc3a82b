<?php
/*
 * @Description: 设备是否在区域理员下 参数ID
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 17:52:34
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";

class CPerDevInAreaCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $gApp,$cMessage;
        global $cLog;
        $userId = $gApp["userAliasId"];
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        $db = \database\CDatabase::getInstance();
        $data = $db->querySList("select D.ID from PersonalDevices D join Account A on D.Community = A.Account where A.ParentID = :ParentID and D.ID = :ID",[":ParentID"=>$userId,":ID"=>$id]);
        $cLog->actionLog("#middle#perDevInAreaCheck#");
        if(!count($data)) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}