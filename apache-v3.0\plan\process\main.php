<?php
namespace plan\process;
include_once __DIR__."/alarm.php";
include_once __DIR__."/access.php";
include_once __DIR__."/call.php";
include_once __DIR__."/capture.php";
include_once __DIR__."/community.php";
include_once __DIR__."/device.php";
include_once __DIR__."/key.php";
include_once __DIR__."/manager.php";
include_once __DIR__."/message.php";
include_once __DIR__."/order.php";
include_once __DIR__."/other.php";
include_once __DIR__."/users.php";
include_once __DIR__."/apartment.php";
include_once __DIR__."/staff.php";
include_once __DIR__."/users-v2.php";


function getProcess () {
    $process = [ALARM_PROCESS,ACCESS_PROCESS,CALL_PROCESS,CAPTURE_PROCESS,COMMUNITY_PROCESS,
    DEVICE_PROCESS,KEY_PROCESS,MANAGER_PROCESS,MESSAGE_PROCESS,ORDER_PROCESS,OTHER_PROCESS,USER_PROCESS,
        APARTMENT_PROCESS, STAFF_PROCESS, USER_PROCESS_V2];
    $result = [];
    for ($i=0; $i<count($process); $i++) {
        $intersect = array_intersect_key($result,$process[$i]);
        // 存在重复key
        if(count($intersect) != 0) {
            $keys = array_keys($intersect);
            $keys = implode(";",$keys);
            throw new \Exception("Process' key:$keys is duplicate.");
        }else {
            $result = array_merge($result,$process[$i]);
        }
    }
    return $result;
};


