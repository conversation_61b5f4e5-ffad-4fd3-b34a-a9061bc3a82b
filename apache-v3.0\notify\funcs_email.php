<?php

require_once(dirname(__FILE__) . '/../config/dynamic_config.php');
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/utility.php');
require_once(dirname(__FILE__) . '/funcs_update_data_version.php');
require_once(dirname(__FILE__) . '/funcs_face_interface.php');
require_once(dirname(__FILE__) . '/funcs_office.php');
require_once(dirname(__FILE__) . '/funcs_common.php');
require_once(dirname(__FILE__) . '/funcs_password_confuse.php');
require_once(dirname(__FILE__) . '/funcs.php');
require_once(dirname(__FILE__) . '/funcs_user.php');
require_once(dirname(__FILE__) . '/funcs_kafka.php');
require_once(dirname(__FILE__) . '/socket_office.php');
require_once(dirname(__FILE__) . '/data_confusion.php');
require_once(dirname(__FILE__) . '/../../dao/account.php');
require_once(dirname(__FILE__) . '/../../dao/accountUserInfo.php');
require_once(dirname(__FILE__) . '/../../dao/personalAccount.php');
require_once(dirname(__FILE__) . '/../../dao/personalAccountUserInfo.php');
require_once(dirname(__FILE__) . '/../../dao/insAppFeedback.php');
require_once(dirname(__FILE__) . '/../../dao/installerBillingInfo.php');
require_once(dirname(__FILE__) . '/../../dao/subscriptionList.php');
require_once(dirname(__FILE__) . '/../../dao/subscriptionUsers.php');

//接收ins app feedback的内部邮箱
const FEEDBACK_SEND_EMAIL_MAIN = "<EMAIL>"; 
const FEEDBACK_SNED_EMAIL_CC_LIST = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];

const PROJECT_TYPE_COMMUNITY_NAME = "Community";
const PROJECT_TYPE_OFFICE_NAME = "Office";
const PROJECT_TYPE_VILLA_NAME = "Villa";

const AUTO_PAY_ORDER_STATUS_ACTIVE = 0;
const AUTO_PAY_ORDER_STATUS_AMOUNT_CHANGE = 1;
const AUTO_PAY_ORDER_STATUS_PAY_SUCCESS = 2;
const AUTO_PAY_ORDER_STATUS_PAY_FAILED = 3;
const AUTO_PAY_ORDER_STATUS_CANCELED = 4;

const AUTO_PAY_ORDER_TYPE_SINGLE = 1;
const AUTO_PAY_ORDER_TYPE_COMMUNITY = 2;
const AUTO_PAY_ORDER_TYPE_OFFICE = 3;
const AUTO_PAY_ORDER_TYPE_RENT = 4;
const AUTO_PAY_ORDER_TYPE_SINGLE_VIDEORECORD = 5;
const AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEORECORD = 6;
const AUTO_PAY_ORDER_TYPE_MIX = 7;
const AUTO_PAY_ORDER_TYPE_SINGLE_THIRD_LOCK = 8;
const AUTO_PAY_ORDER_TYPE_COMMUNITY_THIRD_LOCK = 9;

const AUTO_PAY_ORDER_PAYER_TYPE_ENDUSER = 0;
const AUTO_PAY_ORDER_PAYER_TYPE_PM = 1;
const AUTO_PAY_ORDER_PAYER_TYPE_INS = 2;
const AUTO_PAY_ORDER_PAYER_TYPE_DIS = 3;
const AUTO_PAY_ORDER_PAYER_TYPE_SUB_DIS = 4;

const SUBSCRIPTIONLIST_MIXTYPE_SINGLE = 1;
const SUBSCRIPTIONLIST_MIXTYPE_COMMUNITY = (1 << 1);
const SUBSCRIPTIONLIST_MIXTYPE_OFFICE    = (1 << 2);

const OEM_NAME_AZER = "Azerbaijan";

const TRIGGER_EMAIL = "<EMAIL>";
const HAGER_TEST_DOMAIN = "hager.test.akuvox.com";

function sendEmailNotify($emailInfo, $oem = OEM_NAME, $suboem = "")
{
    $payload = ["ver" => "1",
                "OEM" => $oem,
                "SUBOEM" => $suboem,
                "app_type" => "email",
                "data" => json_encode($emailInfo)
            ];

    if (isset($emailInfo['email']) && $emailInfo['email'] != null) {
        $data[] = $emailInfo['email'];
        $data[] = json_encode($payload);

        $sendEmailNotifySocket = new CSendEmailNotifySocket();
        $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_NOTIFY);
        $sendEmailNotifySocket->copy($data);
    }
}

function AzerSendEmailNotify($email_info)
{
    global $cLog;
    $email = $email_info['email'];
    $payload = ["ver" => "1",
                "OEM" => OEM_NAME,
                "SUBOEM" => OEM_NAME_AZER,
                "app_type" => "email",
                "data" => json_encode($email_info)
            ];

    $data[] = $email;
    $data[] = json_encode($payload);

    $cLog->TRACE('Manual Send Azer Bill Email, email = {email}', ["email" => $email]);

    $sendEmailNoitfySocket = new CSendEmailNotifySocket();
    $sendEmailNoitfySocket->setMsgID(MSG_P2A_SEND_EMAIL_NOTIFY);
    $sendEmailNoitfySocket->setMsgFrom(PROJECT_TYPE_RESIDENCE);
    $sendEmailNoitfySocket->copy($data);
}

// openapi socket链路监控
function triggerOpenapiHealthCheckNotify($msg)
{
    global $cLog;
    $cLog->TRACE('triggerOpenapiHealthCheckNotify msg = {msg}', ["msg" => $msg]);

    $data[] = $msg;
    $openApiHealthCheckSocket = new COpenApiHealthChecklSocket();
    $openApiHealthCheckSocket->setMsgID(MSG_C2S_OPENAPI_SOCKET_HEALTH_CHECK);
    $openApiHealthCheckSocket->copy($data);
}

function getPersonalAccountInfoByAccount($account)
{
    $personalAccount = new \dao\PersonalAccount;
    return $personalAccount->selectByAccount($account)[0];

   // global $db;
   // $accountInfo = $db->querySList("select Account,Name,Role,Language,ParentUUID,ParentID from PersonalAccount P where Account = :account;", [":account" => $uid])[0];
   // return $accountInfo;
}

function getPersonalAccountInfoByID($id)
{
    $personalAccount = new \dao\PersonalAccount;
    return $personalAccount->selectByID($id)[0];

    //global $db;
    //$accountInfo = $db->querySList("select Account,Name,Role,Language,ParentUUID,ParentID from PersonalAccount P where ID = :id;", [":id" => $id])[0];
    //return $accountInfo;
}

function getAccountInfoByUUID($uuid)
{
    $account = new \dao\Account;
    return $account->selectByUUID($uuid)[0];

    //global $db;
    //$accountInfo = $db->querySList("select Language,Account,ParentUUID,Location,Grade from Account where UUID = :uuid;", [":uuid" => $uuid])[0];
    //return $accountInfo;
}

function getAccountInfoByAccount($uid)
{
    $account = new \dao\Account;
    return $account->selectByAccount($uid)[0];
}

function getEmailByUUID($uuid)
{
    $personalAccount = new \dao\PersonalAccount;
    $personalAccountUserInfo = new \dao\PersonalAccountUserInfo;

    $userInfoUUID = $personalAccount->selectByUUID($uuid)[0]["UserInfoUUID"];
    return $personalAccountUserInfo->selectByUUID($userInfoUUID, "Email")[0]["Email"];

    // global $db;
    // $emailInfo = $db->querySList("select U.Email from PersonalAccount P left join PersonalAccountUserInfo U on P.UserInfoUUID = U.UUID where P.UUID = :uuid;", [":uuid" => $uuid])[0];
    // return $emailInfo['Email'];
}

function getCommunityByAccountID($accountID)
{
    global $db;
    $communityInfo = $db->querySList("select Location from Account where ID = :id;", [":id" => $accountID])[0];
    return $communityInfo['Location'];
}

function getInsFeedbackInfoByFeedbackUUID($uuid)
{
    $insAppFeedback = new \dao\InsAppFeedback;
    return $insAppFeedback->selectByUUID($uuid)[0];

    // global $db;
    // $emailInfo = $db->querySList("select AccountUserInfoUUID,ContactEmail,Content,FileList from InsAppFeedback where UUID = :uuid;", [":uuid" => $uuid])[0];
    // return $emailInfo;
}

function getLoginAccountByUserInfoUUID($userinfo_uuid)
{
    global $db;
    $loginAccount = $db->querySList("select LoginAccount from AccountUserInfo where UUID = :uuid limit 1" , [":uuid" => $userinfo_uuid])[0]["LoginAccount"];
    return $loginAccount;
}

function getAccountUUIDByUserInfoUUID($userinfoUUID)
{
    global $db;
    $accountUUID = $db->querySList("select AccountUUID from AccountMap where UserInfoUUID = :uuid limit 1;", [":uuid" => $userinfoUUID])[0]["AccountUUID"];
    return $accountUUID;
}

function getCustomerServiceInfoByAccount($account)
{
    global $db;
    $customerServiceInfo = $db->querySList("select ReceiveFeedback,Email from CustomerService where MngAccount = :account", [":account" => $account])[0];
    return $customerServiceInfo;
}

function getSubDisUUIDByInsUUID($uuid)
{
    global $db;
    $subDisUUID = $db->querySList("select DistributorUUID from SubDisMngList where InstallerUUID = :uuid", [":uuid" => $uuid])[0]["DistributorUUID"];
    return $subDisUUID;
}

function addAppFeedbackReceiverList($account,$feedbackUUID)
{
    $uuid = \util\string\uuid();
    global $db;
    $db->insert2List('InsAppFeedbackReceiverList', [":ReceiverAccount"=>$account, ":FeedbackUUID"=>$feedbackUUID, ":UUID"=>$uuid]);
}

function getSubscriptionOrderListByUUID($uuid)
{
    $subscriptionList = new \dao\SubscriptionList;
    return $subscriptionList->selectByUUID($uuid)[0];

   // global $db;
   // $order_info = $db->querySList("select Status,EndReason,TotalPrice,Type,PayerUUID,PayerEmail,PayerType,ProjectUUID,IsBatch,PayPlatform,LastPayTime,NextPayTime,UUID,TimeZone from SubscriptionList where UUID = :uuid", [":uuid" => $uuid])[0];
   // return $order_info;
}

function GetProjectTypeByOrderInfo($order_info)
{
    $order_pay_type = $order_info['Type'];
    $mix_type = $order_info['MixType'];

    //pay_type 转成 project_type
    if ($order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY || $order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEORECORD || $order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY_THIRD_LOCK) {
        return PROJECT_TYPE_RESIDENCE;
    } else if ($order_pay_type == AUTO_PAY_ORDER_TYPE_OFFICE) {
        return PROJECT_TYPE_OFFICE;
    } else if ($order_pay_type == AUTO_PAY_ORDER_TYPE_MIX) {
        if ($mix_type & SUBSCRIPTIONLIST_MIXTYPE_COMMUNITY) {
            return PROJECT_TYPE_RESIDENCE;
        } else if ($mix_type & SUBSCRIPTIONLIST_MIXTYPE_OFFICE) {
            return PROJECT_TYPE_OFFICE;
        }
    }
    
    return PROJECT_TYPE_PERSONAL;
}

function getProjectNameStrByOrderInfo($order_info)
{
    global $db;
    $project_type = GetProjectTypeByOrderInfo($order_info);

    // 单住户的批量扣费，直接返回用户的名字列表
    if ($order_info['IsBatch'] && $project_type != PROJECT_TYPE_PERSONAL) {
        return getBatchProjectNames($order_info);
    }

    if ($project_type == PROJECT_TYPE_PERSONAL) {
        return getSingleAutoPayUsers($order_info['UUID']);
    } else if ($project_type == PROJECT_TYPE_RESIDENCE || $project_type == PROJECT_TYPE_OFFICE) {
        return $db->querySList("select Location from Account where UUID = :uuid", [":uuid" => $order_info['ProjectUUID']])[0]["Location"];
    }

    return '';
}

function getBatchProjectNames($order_info)
{
    global $db;
    $project_name_arr = array();
    $project_uuids = $db->querySList("select DISTINCT ProjectUUID from SubscriptionEndUserList where SubscriptionUUID = :uuid", [":uuid" => $order_info['UUID']]);
    foreach ($project_uuids as $project_uuid) {
        $project_name = $db->querySList("select Location from Account where UUID = :uuid", [":uuid" => $project_uuid['ProjectUUID']])[0]["Location"];
        array_push($project_name_arr, $project_name);
    }
    //如果名称本身包含","会有异常，当前因为模板就是通过，作为分隔符展示所以不做修改
    $str = implode(",", $project_name_arr);
    return $str;
}

function getSingleAutoPayUsers($order_uuid)
{
    $room_names = [];
    global $db;
    $personalAccountInfoList = $db->querySList("select A.Name,A.Role,A.RoomNumber,A.ParentUUID from PersonalAccount A left join SubscriptionEndUserList B on A.UUID = B.PersonalAccountUUID where B.SubscriptionUUID = :uuid", [":uuid" => $order_uuid]);

    $personalAccountDao = new \dao\PersonalAccount;
    foreach ($personalAccountInfoList as $key => $val) {
        $personalAccountInfoList[$key] = $personalAccountDao->dataArrDecode($val, ['Name']);
    }

    foreach ($personalAccountInfoList as $personalAccountInfo) {
        if ($personalAccountInfo['RoomNumber']) {
            array_push($room_names, $personalAccountInfo['RoomNumber']);
        } else if ($personalAccountInfo['Role'] == ACCOUNT_ROLE_PERSONNAL_MAIN) {
            array_push($room_names, $personalAccountInfo['Name']);
        } else if ($personalAccountInfo['Role'] == ACCOUNT_ROLE_PERSONNAL_ATTENDANT){
            $mainAccount = $personalAccountDao->selectByUUID($personalAccountInfo['ParentUUID'])[0];
            //$mainAccount = $db->querySList("select Name,Role,RoomNumber from PersonalAccount where UUID = :uuid", [":uuid" => $account_info['ParentUUID']])[0];
            if ($mainAccount['RoomNumber']) {
                array_push($room_names, $personalAccountInfo['RoomNumber']);
            } else {
                array_push($room_names, $personalAccountInfo['Name']);
            }
        }
    }

    //如果名称本身包含","会有异常，当前因为模板就是通过，作为分隔符展示所以不做修改
    $room_names = array_unique($room_names);
    $str = implode(",", $room_names);
    return $str;
}

function getPayerEmailInfoByUUID($uuid, $payer_type, &$email_userinfo)
{
    global $db;
    if ($payer_type == AUTO_PAY_ORDER_PAYER_TYPE_PM) {
        $email_userinfo['user'] = "PropertyManager";

        $ret = $db->querySList("select A.Email,C.Language from AccountUserInfo A left join AccountMap B on A.UUID = B.UserInfoUUID left join Account C on C.UUID = B.AccountUUID where C.UUID = :uuid", [":uuid" => $uuid])[0];

        $accountUserInfoDao = new \dao\AccountUserInfo;
        $ret  = $accountUserInfoDao->dataArrDecode($ret, ['Email']);

        $email_userinfo['email'] = $ret['Email'];
        $email_userinfo['language'] = $ret['Language'];
    } else if ($payer_type == AUTO_PAY_ORDER_PAYER_TYPE_DIS || $payer_type == AUTO_PAY_ORDER_PAYER_TYPE_SUB_DIS || $payer_type == AUTO_PAY_ORDER_PAYER_TYPE_INS){
        $accountDao = new \dao\Account;
        $ret = $accountDao->selectByUUID($uuid)[0];

        $email_userinfo['user'] = $ret['Account'];
        $email_userinfo['language'] = $ret['Language'];

        $installerBillingInfoDao = new \dao\InstallerBillingInfo();
        $insBillInfo = $installerBillingInfoDao->selectByAccount($ret["Account"])[0];
        $email_userinfo['email'] = $insBillInfo["Email"];
    }
}

// 获取超管设置的运营人员
function getSuperAdministratorEmail()
{
    global $cLog;
    $accountDao = new \dao\Account;
    $accountInfo = $accountDao->selectByKey("Grade", 1, 'Account')[0];

    if(isset($accountInfo['Account']) && !empty($accountInfo['Account'])) {
        $customerServiceDao = new \dao\CustomerService;
        $customerServiceInfo = $customerServiceDao->selectByKey("MngAccount", $accountInfo['Account'], 'EmailForRentManager')[0];
        return $customerServiceInfo['EmailForRentManager'];
    }

    return "";
}

// 根据pm_uuid，获取PM的姓名、邮箱
function getPropertyInfoByPmUUID($pm_uuid)
{
    global $db;
    $sql = "SELECT A.UUID,A.Language,P.FirstName,P.LastName,AU.Email,AU.Phone 
            FROM Account A 
            INNER JOIN PropertyInfo P ON P.AccountID = A.ID 
            INNER JOIN AccountMap AP ON AP.AccountUUID=A.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID 
            WHERE A.UUID=:pm_uuid;";

    $pm_info = $db->querySList($sql, [":pm_uuid" => $pm_uuid])[0];
    $pm_info['Email'] = DataConfusion::getInstance()->decrypt($pm_info['Email']);
    return $pm_info;
}

// Rent付费功能，根据订单获取相关的PM列表
function getRentPropertyListByOrderID($order_id){
    global $db;
    
    $sql = "SELECT RC.RentManagerCompanyName,RC.RentManagerCompanyCode,RC.PmUUID 
            FROM RentManagerOrderList RO 
            INNER JOIN RentManagerCustomer RC ON RC.UUID = RO.RentManagerCustomerUUID 
            WHERE RO.OrderID=:order_id";

    $pm_list = $db->querySList($sql, [":order_id" => $order_id]);
    return $pm_list;
}

function getUserInfoByEmail($email, $accountType)
{
    if($accountType == 0) {
        // EndUser
        $personalAccountUserInfoDao = new \dao\PersonalAccountUserInfo;
        return $personalAccountUserInfoDao->selectByEmail($email)[0];

        //$userInfo = $db->querySList("select AppMainUserAccount,UUID from PersonalAccountUserInfo where Email = :email", [":email" => $email])[0];
        //return $userInfo;
    } else if($accountType == 1 || $accountType == 2) {
        //PM
        $accountUserInfoDao = new \dao\AccountUserInfo;
        return $accountUserInfoDao->selectByKey("Email", $email)[0];
        //$userInfo = $db->querySList("select AppMainUserAccount,UUID from AccountUserInfo where Email = :email", [":email" => $email])[0];
        //return $userInfo;
    } else {
        return null;
    }
}

function getPerAccountInfoListByUserInfoUUID($userInfoUUID)
{
    $personalAccountDao = new \dao\PersonalAccount;
    return $personalAccountDao->selectByKey('UserInfoUUID', $userInfoUUID);

    //global $db;
    //$perAccountInfoList = $db->querySList("select Role, Name, UnitID, RoomID, ParentUUID from PersonalAccount where UserInfoUUID = :uuid", [":uuid" => $userInfoUUID]);
    //return $perAccountInfoList;
}

function getPerAccountInfoByUUID($UUID)
{
    $personalAccountDao = new \dao\PersonalAccount;
    return $personalAccountDao->selectByUUID($UUID)[0];

    //global $db;
    //$perAccountInfo = $db->querySList("select Role, Name, UnitID, RoomID from PersonalAccount where UUID = :uuid", [":uuid" => $UUID])[0];
    //return $perAccountInfo;
}

function getUnitNameByUnitID($unitID)
{
    global $db;
    $unitName = $db->querySList("select UnitName from CommunityUnit where ID = :unitID", [':unitID' => $unitID])[0]['UnitName'];
    return $unitName;
}
function getAptNumByRoomID($roomID)
{
    global $db;
    $aptNum = $db->querySList("select RoomName from CommunityRoom where ID = :roomID", [':roomID' => $roomID])[0]['RoomName'];
    return $aptNum;
}
//拼接获得社区对应信息
function getCommunityUserDetailInfo($unitID, $roomID)
{
    $unitName = getUnitNameByUnitID($unitID);
    $aptNum = getAptNumByRoomID($roomID);
    return $unitName . '-' . $aptNum;
}

//根据role判断是否是新办公
function checkIsNewOfficeByRole($role)
{
    if ($role == ACCOUNT_ROLE_OFFICE_NEW_PER || $role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        return true;
    }
    return false;
}

//拼接获得办公对应信息
//旧办公填department+name;新办公填company+name
function getOfficeUserDetailInfo($per_account_info)
{
    global $db;
    $name = $per_account_info['Name'];
    if (checkIsNewOfficeByRole($per_account_info['Role']))
    {
        $user_table_name = "";
        if ($per_account_info['Role'] == ACCOUNT_ROLE_OFFICE_NEW_PER)
        {
            $user_table_name = "OfficePersonnel";
        }
        else if ($per_account_info['Role'] == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
        {
            $user_table_name = "OfficeAdmin";
        }
        if (!$user_table_name)
        {
            return "";
        }
        $company_name = $db->querySList("select OC.Name from OfficeCompany OC left join $user_table_name O on OC.UUID = O.OfficeCompanyUUID
                                                                    where O.PersonalAccountUUID = :perUUID", [':perUUID' => $per_account_info['UUID']])[0]['Name'];
        return $company_name . " - " . $name;
    }
    else
    {
        $department_name = getUnitNameByUnitID($per_account_info['UnitID']);
        return $department_name . " - " . $name;
    }
    return "";
}



function changePwdHandle($uid, $pwd, $email)
{
    global $cLog;

    $emailInfo['uid'] = $uid;
    $emailInfo['pwd'] = $pwd;
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "change_pwd";
    
    if (DetectProjectTypeByAccount($uid) == PROJECT_TYPE_OFFICE) {
        $emailInfo['email_type'] = "office_change_pwd";
        $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
    }

    getQrCodeInfo($uid, $email, $pwd, $qrcodeUrl, $qrcodeBody);

    if (strlen($uid) <= 0 || strlen($qrcodeUrl) <= 0) {
        $cLog->TRACE('[changePwdHandle] param error, uid is ' . $uid . ',qrcode_url is ' . $qrcodeUrl);
        return;
    }

    $accountInfo = getPersonalAccountInfoByAccount($uid);
    
    $communityID = $accountInfo['ParentID'];
    $emailInfo['gw_code'] = GATEWAY_NUM;
    $emailInfo['user'] = $accountInfo['Name'];
    $emailInfo['role'] =  $accountInfo['Role'];
    $emailInfo['language'] = $accountInfo['Language'];
 
    // 未填邮箱的从账号,发送给主账号
    if (strlen($email) <= 0 && ($accountInfo['Role'] == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || $accountInfo['Role'] == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)) {
        $emailInfo['email_type'] = "family_change_pwd";
        $emailInfo['email'] = getEmailByUUID($accountInfo['ParentUUID']);
        $parentInfo = getPersonalAccountInfoByID($accountInfo['ParentID']);
        $emailInfo['user'] = $parentInfo['Name']; // 填主账号的名称
        $communityID = $parentInfo['ParentID'];
    }

    if ($accountInfo['Role'] == ACCOUNT_ROLE_COMMUNITY_PM) {
        $emailInfo['user'] = $uid;  //pm 邮件模板的user的是sip 账号
        $emailInfo['email_type'] = "change_pm_pwd";
    }

    if (strlen($emailInfo['email']) <= 0 ) {
        $cLog->TRACE('[changePwdHandle] did not get email to send, uid is ' . $uid);
        return;
    }

    if ($emailInfo['role'] == ACCOUNT_ROLE_PERSONNAL_MAIN || $emailInfo['role'] == ACCOUNT_ROLE_PERSONNAL_ATTENDANT) {
        $emailInfo['community'] = ''; // 单住户不展示community
    } else {
        $emailInfo['community'] = getCommunityByAccountID($communityID);
    }

    $emailInfo['enable_smarthome'] = CheckUidEnalbeSmarthome($uid);
    $cLog->TRACE('[changePwdHandle]begin to pwd change, uid = {uid}, email = {email}, enable_smarthome = {enable_smarthome}',
                 ["uid" => $uid, "email" => $email, "enable_smarthome" => $emailInfo['enable_smarthome']]);

    $emailInfo['qrcode_url'] = $qrcodeUrl;
    $emailInfo['qrcode_body'] = $qrcodeBody;

    sendEmailNotify($emailInfo);
}

// link后的pmweb修改密码
function pmChangePwdHandle($account_uuid, $pwd, $email)
{
    global $cLog;

    $uuid_hash = sprintf('%u', crc32($account_uuid));
    getQrCodeInfo($uuid_hash, $email, $pwd, $qrcodeUrl, $qrcodeBody);

    $emailInfo['pwd'] = $pwd;
    $emailInfo['user'] = $email;
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "pm_web_change_pwd";
    $emailInfo['gw_code'] = GATEWAY_NUM;
    $accountInfo = getAccountInfoByUUID($account_uuid);
    $emailInfo['language'] = $accountInfo['Language'];

    $cLog->TRACE('[pmWebChangePwd]begin to pm pwd change, account_uuid = {account_uuid}, email = {email}', ["account_uuid" => $account_uuid, "email" => $email]);

    $emailInfo['qrcode_url'] = $qrcodeUrl;
    $emailInfo['qrcode_body'] = $qrcodeBody;

    sendEmailNotify($emailInfo);
}

function insAppFeedbackHandle($feedbackUUID)
{
    global $cLog;

    $cLog->TRACE('[insAppFeedback]send feedback, feedbackUUID:' . $feedbackUUID);

    $emailData = getInsFeedbackInfoByFeedbackUUID($feedbackUUID);

    //根据AccountUserInfoUUID获取loginAccount
    $loginAccount = getLoginAccountByUserInfoUUID($emailData['AccountUserInfoUUID']);

    //邮件通用字段
    $emailInfo['ins_name'] = $loginAccount;
    $emailInfo['contact_email'] = $emailData['ContactEmail'];
    $emailInfo['content'] = $emailData['Content'];
    $emailInfo['file_list'] = $emailData['FileList'];
    $emailInfo['email_type'] = "ins_feedback";
    $emailInfo['project_type'] = 0;//邮件推送类型字段
    $emailInfo['user'] = $loginAccount;

    $insUUID = getAccountUUIDByUserInfoUUID($emailData['AccountUserInfoUUID']);    
    //根据uuid找到sub-dis
    $subDisUUID = getSubDisUUIDByInsUUID($insUUID);
    if($subDisUUID) {
        //根据uuid找到sub dis
        $subDisInfo = getAccountInfoByUUID($subDisUUID);
        //获取sub dis的customer service信息
        $subDisCustomerService = getCustomerServiceInfoByAccount($subDisInfo['Account']);
        if(1 == $subDisCustomerService["ReceiveFeedback"]) {
            $emailInfo['send_to_type'] = "dis";
            $emailInfo['dis_name'] = $subDisInfo['Account'];
            $emailInfo['language'] = $subDisInfo['Language'];
            $emailInfo['email'] = $subDisCustomerService['Email'];

            $cLog->TRACE('[insAppFeedback]begin to send feedback, dis_name = {dis_name}, email = {email}', ["account_uuid" => $subDisInfo['Account'], "email" => $subDisCustomerService['Email']]);

            sendEmailNotify($emailInfo);
            addAppFeedbackReceiverList($subDisInfo['Account'], $feedbackUUID);
        }
    }

    //根据uuid找到ins
    $insInfo = getAccountInfoByUUID($insUUID);

    //根据ins的parent_uuid找到dis
    $disInfo = getAccountInfoByUUID($insInfo['ParentUUID']);

    //获取dis的customer service信息
    $disCustomerService = getCustomerServiceInfoByAccount($disInfo['Account']);

    //dis有开启customer service则发送
    if(1 == $disCustomerService["ReceiveFeedback"]) {
        $emailInfo['send_to_type'] = "dis";
        $emailInfo['dis_name'] = $disInfo['Account'];
        $emailInfo['language'] = $disInfo['Language'];
        $emailInfo['email'] = $disCustomerService['Email'];

        $cLog->TRACE('[insAppFeedback]begin to send feedback, dis_name = {dis_name}, email = {email}', ["account_uuid" => $disInfo['Account'], "email" => $disCustomerService['Email']]);

        sendEmailNotify($emailInfo);
        addAppFeedbackReceiverList($disInfo['Account'], $feedbackUUID);
    }
    //内部邮箱发给主接收邮箱，其余抄送                                                                                                                                                                                           
    $emailInfo['email'] = FEEDBACK_SEND_EMAIL_MAIN;
    $emailInfo['cc_list'] = implode(",", FEEDBACK_SNED_EMAIL_CC_LIST);
    $emailInfo['language'] = "en";//内部邮箱发英文
    $emailInfo['send_to_type'] = "company";

    sendEmailNotify($emailInfo);
}
//accountType:PM-0,EndUser-1,Admin-2
function deleteAccountConfirmHandle($email, $accountType)
{
    global $cLog;
    $cLog->TRACE('[deleteAccountConfirm]send delete confirm, email = {email}, accountType = {accountType}', ["email" => $email, "accountType" => $accountType]);

    //accountType合法性判断
    if($accountType != 0 && $accountType != 1 && $accountType != 2) {
        $cLog->TRACE('param error. accountType wrong');
        return;
    }

    $userInfo = getUserInfoByEmail($email, $accountType);
    if(!$userInfo) {
        $cLog->TRACE('param error. email wrong');
        return;
    }

    //兼容多套房的情况
    $perAccountInfoList = getPerAccountInfoListByUserInfoUUID($userInfo['UUID']);
    $accountInfoList = array();

    foreach($perAccountInfoList as $key => $value) {
        $accountInfo = array();

        if($value['Role'] == ACCOUNT_ROLE_COMMUNITY_MAIN) {
            $accountInfo['project_type'] = PROJECT_TYPE_COMMUNITY_NAME;
            //社区填building+apt
            $accountInfo['detail_info'] = getCommunityUserDetailInfo($value['UnitID'], $value['RoomID']);
        } else if($value['Role'] == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) {
            //根据主账号查出相关信息
            $masterAccountInfo = getPerAccountInfoByUUID($value['ParentUUID']);
            
            if(!$masterAccountInfo) {
                $cLog->TRACE('cannot find community main account');
                continue;
            }

            $accountInfo['project_type'] = PROJECT_TYPE_COMMUNITY_NAME;
            //社区填building+apt
            $accountInfo['detail_info'] = getCommunityUserDetailInfo($masterAccountInfo['UnitID'], $masterAccountInfo['RoomID']);
        } else if($value['Role'] == ACCOUNT_ROLE_PERSONNAL_MAIN || $value['Role'] == ACCOUNT_ROLE_PERSONNAL_ATTENDANT) {
            //单住户
            $accountInfo['project_type'] = PROJECT_TYPE_VILLA_NAME;
            //单住户填Name
            $accountInfo['detail_info'] = $value['Name'];
            //办公无主从之分
        } else if($value['Role'] == ACCOUNT_ROLE_OFFICE_MAIN || $value['Role'] == ACCOUNT_ROLE_OFFICE_ADMIN || $value['Role'] == ACCOUNT_ROLE_OFFICE_NEW_PER || $value['Role'] == ACCOUNT_ROLE_OFFICE_NEW_ADMIN) {
            $accountInfo['project_type'] = PROJECT_TYPE_OFFICE_NAME;
            //新旧办公规则不同
            $accountInfo['detail_info'] = getOfficeUserDetailInfo($value);
        } else if($value['Role'] == ACCOUNT_ROLE_COMMUNITY_PM) {
            $projectInfo = getAccountInfoByUUID($value['ParentUUID']);
            
            if($projectInfo['Grade'] == ACCOUNT_GRADE_COMMUNITY) {
                $accountInfo['project_type'] = PROJECT_TYPE_COMMUNITY_NAME;
            } else if($projectInfo['Grade'] == ACCOUNT_GRADE_OFFICE) {
                $accountInfo['project_type'] = PROJECT_TYPE_OFFICE_NAME;
            } else {
                $cLog->TRACE('cannot find pm project');
                continue;
            }

            $accountInfo['detail_info'] = $projectInfo['Location'];
        }
        array_push($accountInfoList, $accountInfo);
    }
    //主站点账号信息
    $mainAccountInfo = getPersonalAccountInfoByAccount($userInfo['AppMainUserAccount']);

    $emailInfo['email'] = $email;
    $emailInfo['cc_list'] = FEEDBACK_SEND_EMAIL_MAIN;//每封删除账号的邮件都抄送给技术支持
    $emailInfo['account_type'] = $accountType;
    $emailInfo['main_user'] = $mainAccountInfo['Name']; //主站点Account的Name字段
    $emailInfo['language'] = $mainAccountInfo['Language'];
    $emailInfo['account_info_list'] = $accountInfoList;
    $emailInfo['email_type'] = "delete_confirm";
    $emailInfo['project_type'] = 0; //cspush适配字段
    $emailInfo['gw_code'] = GATEWAY_NUM; //网关编号

    $cLog->TRACE('[deleteAccountConfirm] begin to send confirm email, email = {email}, accountType = {accountType}', ["email" => $email, "accountType" => $accountType]);

    sendEmailNotify($emailInfo);

}

//自动扣费邮件通知
function AutoPayNotify($order_uuid, $order_status, $project_name = "")
{
    global $cLog;
    $auto_pay_orderinfo = getSubscriptionOrderListByUUID($order_uuid);
    $project_name_str = getProjectNameStrByOrderInfo($auto_pay_orderinfo);
    $project_type = GetProjectTypeByOrderInfo($auto_pay_orderinfo);
    $order_pay_type = $auto_pay_orderinfo['Type'];
    $is_batch = $auto_pay_orderinfo['IsBatch'];

    $email_info = array();
    //community字段用于cspush拼接邮件标签，只有社区和办公需要该字段
    if (!$is_batch && ($project_type == PROJECT_TYPE_RESIDENCE || $project_type == PROJECT_TYPE_OFFICE)) {
        $email_info['community'] = $project_name_str;
    }

    //特殊处理，用户删除时查数据库查不到用户所属的项目名，因此需要web告知
    if ($project_name) {
        $email_info['community'] = $project_name;
        $email_info['content_project'] = $project_name;
    } else {
        $email_info['content_project'] = $project_name_str;
    }
    
    // RentManager续费设置名称
    if ($order_pay_type == AUTO_PAY_ORDER_TYPE_RENT){
        $email_info['content_project'] = 'Rent Manager';
    }

	//数据库保存的TotalPrice是实际金额*100的值，实际展示需要换算回来
    $email_info['pay_amount'] = $auto_pay_orderinfo['TotalPrice'] / 100;

    if ($auto_pay_orderinfo['NextPayTime']){
        $next_pay_date = \util\time\setTimeZone($auto_pay_orderinfo['NextPayTime'], $auto_pay_orderinfo['TimeZone'], 3, "+");
        $next_datetime = DateTime::createFromFormat("Y-m-d H:i:s", $next_pay_date);
        $next_pay_date = $next_datetime->format("Y-m-d");
    } else {
        $next_pay_date = "--";
    }

    if ($auto_pay_orderinfo['LastPayTime']){
        $last_pay_date = \util\time\setTimeZone($auto_pay_orderinfo['LastPayTime'], $auto_pay_orderinfo['TimeZone'], 3, "+");
        $last_datetime = DateTime::createFromFormat("Y-m-d H:i:s", $last_pay_date);
        $last_pay_date = $last_datetime->format("Y-m-d");
    } else {
        $last_pay_date = "--";
    }

    $email_info['project_type'] = $project_type;
    $email_info['next_pay_date'] = $next_pay_date;
    $email_info['last_pay_date'] = $last_pay_date;
    $email_info['pay_method'] = $auto_pay_orderinfo['PayPlatform'] == 0 ? "Paypal" : "Stripe";
    getPayerEmailInfoByUUID($auto_pay_orderinfo['PayerUUID'], $auto_pay_orderinfo['PayerType'], $email_info);

    switch ($order_status){
        case AUTO_PAY_ORDER_STATUS_ACTIVE:
            $email_info['email_type'] = "auto_pay_active";
            break;
        case AUTO_PAY_ORDER_STATUS_AMOUNT_CHANGE:
            $email_info['email_type'] = "auto_pay_amount_change";
            break;
        case AUTO_PAY_ORDER_STATUS_PAY_SUCCESS:
            $email_info['email_type'] = "auto_pay_success";
            break;
        case AUTO_PAY_ORDER_STATUS_PAY_FAILED:
            $email_info['email_type'] = "auto_pay_failed";
            break;
        case AUTO_PAY_ORDER_STATUS_CANCELED:
            $email_info['email_type'] = "auto_pay_canceled";
            break;
        default:
            $cLog->TRACE('[AutoPayNotify]order status is not exist, order_uuid: ' . $order_uuid . 'order_status: '.$order_status);
            break;
    }   
    
    $cLog->TRACE('[AutoPayNotify]order_uuid: ' . $order_uuid . 'order_status: '.$order_status);
    sendEmailNotify($email_info);
}

//Rent开启功能邮件通知运营人员
function RentEnableNotify($order_id, $order_status, $project_name = "")
{
    $cLog = \share\util\getLog();
    $email_info = array();
    $cLog->TRACE("[RentEnableNotify] order_id:$order_id, order_status:$order_status, project_name:$project_name");

    // 获取被通知人信息
    $admin_emails = getSuperAdministratorEmail();
    if (empty($admin_emails)) {
        return;
    }

    // 获取PM列表
    $pm_list = array();
    $property_list = getRentPropertyListByOrderID($order_id);
    $cLog->TRACE("[RentEnableNotify] property_list" . json_encode($property_list));
    foreach ($property_list as $item) {
        $pm_info = getPropertyInfoByPmUUID($item['PmUUID']);
        if (empty($pm_info["Email"])) {
            continue;
        }


        array_push($pm_list, array(
            "pm_email" => $pm_info["Email"],
            "pm_name" => $pm_info["FirstName"] . " " . $pm_info["LastName"],
            "company_name" => $item["RentManagerCompanyName"],
            "company_code" => $item["RentManagerCompanyCode"],
        ));
    }

    if (is_array($property_list) && count($pm_list) > 0) {
        //特殊处理，用户删除时查数据库查不到用户所属的项目名，因此需要web告知
        //community字段用于cspush拼接邮件标签，只有社区和办公需要该字段
        if ($project_name) {
            $email_info['community'] = $project_name;
        }

        $email_info['email_type'] = "rent_customer_has_paid_to_admin";
        $email_info['user'] = 'Administrator';
        $email_info['pm_list'] = $pm_list;
        $email_info['language'] = 'en';

        $email_items = explode(",", $admin_emails);
        foreach ($email_items as $email) {
            $email_info['email'] = $email;
            sendEmailNotify($email_info, OEM_NAME, "RentManager");
            $cLog->TRACE('[RentEnableNotify] send email to: ' . $email);
        }
    }
}

// hager删除账号发送邮件,oemType(oem类型)，uid(账号),email(邮箱),language(语言),sendToMaster(发送给主账号),name(删除的账号名称)
function sendDeleteUserEmail($oemType, $uid, $email, $language, $sendToMaster, $name)
{
    // 目前只有hager的才发删除邮件
    if ($oemType == DIS_OEM_TYPE_HAGER) {
        $emailInfo['uid'] = $uid;
        $emailInfo['name'] = $name;
        $emailInfo['email'] = $email;
        $emailInfo['language'] = $language;
        $emailInfo['gw_code'] = GATEWAY_NUM;

        if ($sendToMaster) {
            $emailInfo['email_type'] = "family_delete_app_account";
        } else {
            $emailInfo['email_type'] = "delete_app_account";
        }

        global $cLog;

        $cLog->TRACE('[sendDeleteUserEmail] oemType = {oemType}, email = {email}, uid = {uid},language={language}', ["email" => $email, "uid" => $uid, "oemType" => $oemType, "language" => $language]);

        sendEmailNotify($emailInfo, "hager");
    }
}

function perResetPwd($uid, $email, $token, $oemType, $roleType)
{
    global $cLog;
    $cLog->TRACE('[perResetPwd]begin to personnal pwd reset, uid = {uid}, email = {email}, token = {token}, oemType = {oemType}', ["uid" => $uid, "email" => $email, "token" => $token, "oemType" => $oemType]);
    $roleType = strval($roleType);
    $data = [$uid,$email,$token,$roleType];
    $resetPasswdSocket = new \CResetPasswdSocket();
    if (DetectProjectTypeByAccount($uid) == PROJECT_TYPE_OFFICE) {
        $resetPasswdSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
        $resetPasswdSocket->setMsgID(MSG_P2A_OFFICE_RESETPWD);
    }
    $resetPasswdSocket->copyUserInfo($data);
}

// hager创建账号
function HagerCreateUser($account, $email, $token)
{
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "create_uid";

    if (WEB_DOMAIN === HAGER_TEST_DOMAIN) {
        $emailInfo['createpasswordlink'] = "https://api-hager.test.akuvox.com/Auth/LoginHager.html#/setPwd?oem=hager&tmp_token=" . $token;
    } else {
        $emailInfo['createpasswordlink'] = "https://intercom.hager-iot.com/Auth/LoginHager.html#/setPwd?oem=hager&tmp_token=" . $token;
    }

    $accountInfo = getPersonalAccountInfoByAccount($account);
    $emailInfo['role'] = $accountInfo['Role'];
    $emailInfo['language'] = $accountInfo['Language'];

    $cLog = \share\util\getLog();
    $cLog->TRACE('[HagerCreateUser] account = {account}, email = {email}, token = {token}', ["account" => $account, "email" => $email, "token" => $token]);
    sendEmailNotify($emailInfo, "hager");
}

// hager 重置密码
function HagerResetPassword($account, $email, $token)
{
    $emailInfo['token'] = $token;
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "reset_pwd";

    if (WEB_DOMAIN === HAGER_TEST_DOMAIN) {
        $emailInfo['resetpasswordlink'] = "https://api-hager.test.akuvox.com/Auth/LoginHager.html#/resetPwd?oem=hager&tmp_token=" . $token;
    } else {
        $emailInfo['resetpasswordlink'] = "https://intercom.hager-iot.com/Auth/LoginHager.html#/resetPwd?oem=hager&tmp_token=" . $token;
    }

    $accountInfo = getPersonalAccountInfoByAccount($account);
    $emailInfo['language'] = $accountInfo['Language'];

    $cLog = \share\util\getLog();
    $cLog->TRACE('[HagerResetPassword] account = {account}, email = {email}, token = {token}', ["account" => $account, "email" => $email, "token" => $token]);
    sendEmailNotify($emailInfo, "hager");
}

function getPropertyInfoByAccountID($accountId)
{
    $propertyInfo = new \dao\PropertyInfo;
    return $propertyInfo->selectByAccountID($accountId)[0];
}

function sendTwoFactorAuthCode($code, $email, $account_name)
{
    global $cLog;
    $emailInfo['code'] = $code;
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "two_factor_auth";
    $accountType = 1;
    $userInfo = getUserInfoByEmail($email, $accountType);
    $accountUuid = getAccountUUIDByUserInfoUUID($userInfo['UUID']);
    $accountInfo = getAccountInfoByUUID($accountUuid);

    $emailInfo['language'] = $accountInfo['Language'];
    $emailInfo['user'] = $account_name;
    $cLog->TRACE('[sendTwoFactorAuthCode]begin to send two factor auth code,  email = {email}', ["email" => $email]);
    sendEmailNotify($emailInfo);
}

function managerResetPwd($uid, $email, $token, $account_name)
{
    global $cLog;
    $cLog->TRACE('[managerResetPwd]begin to manager pwd reset, uid = {uid}, email = {email}, token = {token}', ["uid" => $uid, "email" => $email, "token" => $token]);

    $emailInfo['email'] = $email;
    $emailInfo['user'] = $account_name;
    $emailInfo['email_type'] = "manager_reset_pwd";
    $accountInfo = getAccountInfoByAccount($uid);
    $emailInfo['language'] = $accountInfo['Language'];

    if (strlen(WEB_DOMAIN) > 0) {
        $emailInfo['url'] = "http://" . WEB_DOMAIN . "/manager/#/resetPassword?tmp_token=" . $token . "&account_name=" . $account_name;
    } else {
        $emailInfo['url'] = "http://" . WEB_IP . "/manager/#/resetPassword?tmp_token=" . $token . "&account_name=" . $account_name;
    }
    sendEmailNotify($emailInfo);
}

