<?php

namespace package\common\model\subscription\method;

trait Check
{
    /**
     * @description: 检查用户是否在订阅中
     * @param {string or array} PersonalAccountUUID
     * @return array 返回已存在订阅的用户的UUID，如果没有则返回空数组
     * @throws \Exception
     * @author: csc 2023/12/20 14:55 V6.7.1
     * @lastEditors: csc 2023/12/20 14:55 V6.7.1
     */
    public function checkItemInSubscription()
    {
        //获取参数
        $params = ['PersonalAccountUUID'];
        list($personalAccountUUID) = $this->getParams($params);

        //查询包含用户的订阅
        $subscriptionEndUserList = $this->dao->subscriptionEndUserList->selectByArray([['PersonalAccountUUID', $personalAccountUUID],
            ['Type', [SUBSCRIBE_END_USER_TYPE['singleRenew'], SUBSCRIBE_END_USER_TYPE['communityRenew'], SUBSCRIBE_END_USER_TYPE['officeRenew']]]], 'SubscriptionUUID,PersonalAccountUUID');
        if (empty($subscriptionEndUserList)) {
            return [];
        }
        $endUserList = [];
        foreach ($subscriptionEndUserList as $val) {
            if (isset($endUserList[$val['SubscriptionUUID']])) {
                $endUserList[$val['SubscriptionUUID']][] = $val['PersonalAccountUUID'];
            } else {
                $endUserList[$val['SubscriptionUUID']] = [$val['PersonalAccountUUID']];
            }
        }

        $subscriptionUUIDList = array_values(array_unique(array_column($subscriptionEndUserList, 'SubscriptionUUID')));
        $subscriptionList = $this->dao->subscriptionList->selectByArray([['UUID', $subscriptionUUIDList], ['Status', [0, 1]]], 'UUID,Status,SubscriptionNumber');

        //在订阅中或者订阅成功正常中的用户UUID集合
        $existsItem = [];
        foreach ($subscriptionList as $val) {
            $existsItem = array_merge($existsItem, $endUserList[$val['UUID']]);
        }
        $existsItem = array_values(array_unique($existsItem));

        return $existsItem;
    }

    /**
     * @description:检查用户是否在订阅中
     * @param:
     * @return array 返回已存在订阅的用户的UUID，如果没有则返回空数组
     * @author: shoubin.chen 2024/12/2 17:17:28 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/2 17:17:28  V7.1.0
     */
    public function checkVideoStorageItemInSubscription()
    {
        //获取参数
        $params = ['SiteUUID'];
        list($siteUUID) = $this->getParams($params);

        //查询包含用户的订阅
        $subscriptionEndUserList = $this->dao->subscriptionEndUserList->selectByArray([['SiteUUID', $siteUUID],
            ['Type', [SUBSCRIBE_END_USER_TYPE['singleVideoStorageRenew'], SUBSCRIBE_END_USER_TYPE['communityVideoStorageRenew']]]], 'SubscriptionUUID,SiteUUID');
        if (empty($subscriptionEndUserList)) {
            return [];
        }
        $endUserList = [];
        foreach ($subscriptionEndUserList as $val) {
            if (isset($endUserList[$val['SubscriptionUUID']])) {
                $endUserList[$val['SubscriptionUUID']][] = $val['SiteUUID'];
            } else {
                $endUserList[$val['SubscriptionUUID']] = [$val['SiteUUID']];
            }
        }

        $subscriptionUUIDList = array_values(array_unique(array_column($subscriptionEndUserList, 'SubscriptionUUID')));
        $subscriptionList = $this->dao->subscriptionList->selectByArray([['UUID', $subscriptionUUIDList], ['Status', [0, 1]]], 'UUID,Status,SubscriptionNumber');

        //在订阅中或者订阅成功正常中的用户UUID集合
        $existsItem = [];
        foreach ($subscriptionList as $val) {
            $existsItem = array_merge($existsItem, $endUserList[$val['UUID']]);
        }
        $existsItem = array_values(array_unique($existsItem));

        return $existsItem;
    }

    /**
     * @description:检查三方锁是否在订阅中
     * @param:
     * @return array 返回已存在订阅的三方锁的UUID，如果没有则返回空数组
     * @author: csc 2025/2/11 14:30 V7.1.0
     * @lastEditors: csc 2025/2/11 14:30 V7.1.0
     */
    public function checkThirdLockItemInSubscription()
    {
        //获取参数
        $params = ['ThirdLockUUID'];
        list($thirdLockUUID) = $this->getParams($params);

        //查询包含用户的订阅
        $subscriptionEndUserList = $this->dao->subscriptionEndUserList->selectByArray([['LockUUID', $thirdLockUUID],
            ['Type', [SUBSCRIBE_END_USER_TYPE['singleThirdLockRenew'], SUBSCRIBE_END_USER_TYPE['communityThirdLockRenew']]]], 'SubscriptionUUID,LockUUID');
        if (empty($subscriptionEndUserList)) {
            return [];
        }
        $endUserList = [];
        foreach ($subscriptionEndUserList as $val) {
            if (isset($endUserList[$val['SubscriptionUUID']])) {
                $endUserList[$val['SubscriptionUUID']][] = $val['LockUUID'];
            } else {
                $endUserList[$val['SubscriptionUUID']] = [$val['LockUUID']];
            }
        }

        $subscriptionUUIDList = array_values(array_unique(array_column($subscriptionEndUserList, 'SubscriptionUUID')));
        $subscriptionList = $this->dao->subscriptionList->selectByArray([['UUID', $subscriptionUUIDList], ['Status', [0, 1]]], 'UUID,Status,SubscriptionNumber');

        //在订阅中或者订阅成功正常中的用户UUID集合
        $existsItem = [];
        foreach ($subscriptionList as $val) {
            $existsItem = array_merge($existsItem, $endUserList[$val['UUID']]);
        }
        $existsItem = array_values(array_unique($existsItem));

        return $existsItem;
    }

    /**
     * @Author: chenpl
     * @Description: 检查rentManager是否在订阅中
     * @Params:
     * @Return: 返回已存在订阅的rentManager的UUID，如果没有则返回空数组
     * @Date: 2024/11/4
     */
    public function checkRentManagerInSubscription()
    {
        $params = ['RentManagerCustomerList'];
        list($rentManagerCustomerList) = $this->getParams($params);
        $rentManagerSubscriptionList = $this->dao->rentManagerSubscriptionList->selectByArray([['RentManagerCustomerUUID', $rentManagerCustomerList]], 'SubscriptionUUID,RentManagerCustomerUUID');

        if (empty($rentManagerSubscriptionList)) {
            return [];
        }

        //区分有订阅过和没有订阅过的rentManager
        $rentManagerList = [];
        foreach ($rentManagerSubscriptionList as $val) {
            if (isset($endUserList[$val['SubscriptionUUID']])) {
                $rentManagerList[$val['SubscriptionUUID']][] = $val['RentManagerCustomerUUID'];
            } else {
                $rentManagerList[$val['SubscriptionUUID']] = [$val['RentManagerCustomerUUID']];
            }
        }

        $subscriptionUUIDList = array_values(array_unique(array_column($rentManagerSubscriptionList, 'SubscriptionUUID')));

        $subscriptionList = $this->dao->subscriptionList->selectByArray([['UUID', $subscriptionUUIDList], ['Status', [0, 1]]], 'UUID,Status,SubscriptionNumber');

        //在订阅中或者订阅成功正常中的用户UUID集合
        $existsItem = [];
        foreach ($subscriptionList as $val) {
            $existsItem = array_merge($existsItem, $rentManagerList[$val['UUID']]);
        }
        $existsItem = array_values(array_unique($existsItem));

        return $existsItem;
    }

    public function checkDisSubscriptionInIns()
    {
        $params = [PROXY_ROLE_CHECK['distributorId'], PROXY_ROLE_CHECK['installerId']];
        list($disId, $insId) = $this->getParams($params);
        $this->loadUtil('account', true);
        $disInfo = $this->utils->_common->account->getManagerInfo($disId);
        $disUuid = $disInfo['UUID'];
        $insInfo = $this->utils->_common->account->getManagerInfo($insId);
        $insUuid = $insInfo['UUID'];
        $count = $this->dao->subscriptionList->selectByArray([['PayerUUID', $disUuid], ['InsUUID', $insUuid], ['Status', [0,1]]], 'count(*)')[0]['count(*)'];
        return $count === '0';
    }
}
