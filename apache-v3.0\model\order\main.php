<?php
/*
 * @Description: 订单相关
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;
include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";

include_once __DIR__."/query.php";
include_once __DIR__."/add.php";
include_once __DIR__."/update.php";
class COrder {
    use \model\order\query;
    use \model\order\update;
    use \model\order\add;
    private function inputComputedCount ($data) {
        return \util\computed\inputComputedCount($data);
    }
    private function outputComputedCount ($data) {
        return \util\computed\outputComputedCount($data);
    }
    private function computedDiscount ($price,$discount) {
        return \util\computed\computedDiscount($price,$discount);
    }
}