<?php
/*
 * @Description: 删除用户和删除后续
 * @version:
 * @Author: kxl
 * @Date: 2020-01-17 10:40:56
 * @LastEditors: cj
 */
namespace model\user;

trait remove
{
    public function delete()
    {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->log->actionLog("#model#user#delete#id=$id");
        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]])[0];
        $this->log->actionLog("#model#user#delete#data=".json_encode($data));
        $account = $data["Account"];
        $myID = $data['ParentID'];
        // 6.2删除设备
        $this->db->delete2ListWKey('DevicesSpecial', 'Account', $account);
        $this->deleteDevice($account);
        $this->db->delete2ListWID("PersonalAccount", $id);
        $this->deleteMainUserToken($account);
        $this->db->delete2ListWKey('APPSpecial', 'Account', $account);
        \util\computed\setGAppData(["Account"=>$account,"data"=>$data]);
        // 社区删除房间不记录
        if ($data['Role'] == PERENDMROLE) {
            $this->auditLog->setLog(AuditCodeDeleteFamilyMaster, $this->env, [$account], $account);
        } elseif ($data['Role'] == PERENDSROLE || $data['Role'] == COMENDSROLE) {
            $this->auditLog->setLog(AuditCodeDeleteFamilyMember, $this->env, [$account], $account);
            $myID = $this->db->querySList('select ParentID from Account where ID = :ID', [':ID' => $myID])[0]['ParentID'];
        }

        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        $roomSip = null;
        if ($data['Role'] == COMENDMROLE) {
            $roomSip = $data['Account'];
        } elseif ($data['Role'] == COMENDSROLE) {
            $roomSip = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID'=>$data['ParentID']])[0]['Account'];
        }

        \util\computed\setSmartHomeTask(['Type' => ($data['Role'] == PERENDSROLE || $data['Role'] == COMENDSROLE) ? 10 : 9, 'Key' => $account, 'CommunityID' => $myID, 'RoomSip'=>$roomSip]);
    }

    /**
     * 6.2单住户删除用户可以同时删除设备
     * @param account: 主账户account
     */
    public function deleteDevice($account)
    {
        $devices = $this->db->querySList("select ID from PersonalDevices where Node = :Node", [":Node" => $account]);
        foreach ($devices as $device) {
            $this->pcDevInPerMngCheck($device['ID']);
            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData(["ID"=>$device["ID"]]);
            $this->models["devicePersonal"]->deletePer();
            $this->models["notify"]->devPerDelete();
            $resetParams();
        }
    }

    /**
     * 设备是否在个人终端管理员下
     * @param id: 设备id号
     */
    public function pcDevInPerMngCheck($id)
    {
        global $gApp, $cLog, $cMessage;
        $user = $gApp["userAlias"];
        $userId = $gApp["userAliasId"];
        $data = $this->db->querySList("select ID from PersonalDevices where Community = :Community and ID = :ID", [":Community" => $user,":ID" => $id]);
        if (!count($data)) {
            $data = $this->db->querySList("select ID from Devices where MngAccountID = :MngAccountID and ID = :ID", [":MngAccountID" => $userId,":ID" => $id]);
        }
        $cLog->actionLog("#middle#perDevInPerMngCheck#");
        if (!count($data)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
    }

    public function deleteRoom()
    {
        $params = [
            "ID"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->deleteRoomControl($id);
    }

    public function deleteRoomControl($id)
    {
        $data = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]]);
        $account = $data[0]["Account"];

        // V6.1 新pm表删除
        $access = $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account' => $account]);
        $this->db->delete2ListWKey('AccountAccess', 'Account', $account);
        $this->db->delete2ListWKey('CommPerRfKey', 'Account', $account);
        $this->db->delete2ListWKey('CommPerPrivateKey', 'Account', $account);
        // V6.2 新DevicesSpecial表删除
        $this->db->delete2ListWKey('DevicesSpecial', 'Account', $account);
        // 删除自己的权限组
        $accessGroupId = $this->db->querySList(
            'select ID from UserAccessGroup where Account = :Account',
            [':Account'=>$account]
        )[0]['ID'];
        $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $accessGroupId);
        $this->db->delete2ListWKey('UserAccessGroup', 'ID', $accessGroupId);

        $subDevs = $this->db->queryAllList("Devices", ["equation"=>[":Node"=>$account]]);
        $macs = [];
        $accounts = [$account];
        $this->log->actionLog("#model#user#deleteRoom#subDevs=".json_encode($subDevs));
        foreach ($subDevs as $subDev) {
            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData(["ID"=>$subDev["ID"],"userAliasId"=>$data[0]["ParentID"]]);
            $this->models["deviceCommunity"]->delete();
            $this->models["deviceCommunity"]->afterDelete();
            $resetParams();
            array_push($macs, $subDev["MAC"]);
        }
        // 删除从账户
        $subs = $this->db->querySList("select ID,Account from PersonalAccount where ParentID = :ParentID and Role in (".PERENDSROLE.",".COMENDSROLE.")", [":ParentID"=>$id]);
        $this->log->actionLog("#model#user#deleteRoom#subData=".json_encode($subs));
        $faceData = [];
        foreach ($subs as $sub) {
            array_push($accounts, $sub["Account"]);
            $this->db->delete2ListWID("PersonalAccount", $sub["ID"]);
            // V6.1 新pm表删除
            $access = array_merge($access, $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account'=>$sub["Account"]]));
            $this->db->delete2ListWKey('AccountAccess', 'Account', $sub["Account"]);
            $this->db->delete2ListWKey('CommPerRfKey', 'Account', $sub["Account"]);
            $this->db->delete2ListWKey('CommPerPrivateKey', 'Account', $sub["Account"]);
            // 删除自己的权限组
            $accessGroupId = $this->db->querySList(
                'select ID from UserAccessGroup where Account = :Account',
                [':Account'=>$sub["Account"]]
            )[0]['ID'];
            $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $accessGroupId);
            $this->db->delete2ListWKey('UserAccessGroup', 'ID', $accessGroupId);

            $faceData = array_merge($faceData, $this->db->querySList("select FaceUrl,PersonalAccountID from FaceMng where PersonalAccountID=:PersonalAccountID", [":PersonalAccountID"=>$id]));
        }
        $this->delete();
        // 收集mac
        \util\computed\setGAppData(["macs"=>$macs,"FaceData"=>$faceData]);
        // 权限组变更
        foreach ($access as &$val) {
            $val = $val['AccessGroupID'];
        }
        unset($val);
        \util\computed\setGAppData(["Accounts"=>$accounts,"AccessGroup"=>$access]);
    }

    /**
     * @name: 删除个人主账户后
     */
    public function afterDeletePerMain()
    {
        // 兼容个人社区版，删除关联公共设备表
        
        $params = [
            "ID"=>"",
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $account = $params["Account"];
        $this->db->delete2ListWKey("PerNodeDevices", "NodeID", $id);
        $this->log->actionLog("#model#user#afterDeletePerMain#id=$id");
    }

    public function afterDeleteRoom()
    {
        $params = [
            "ID"=>"",
            "Account"=>"",
            "data"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $account = $params["Account"];
        $roomID = $params["data"]["RoomID"];
        $this->db->delete2ListWID("CommunityRoom", $roomID);
        $this->log->actionLog("#model#user#afterDeleteRoom#id=$id;account=$account");
    }


    /**
     * @name: 删除从账户后,删除关联数据，调整免费app等
     * @service: charge
     */
    public function afterDeleteSub()
    {
        global $gApp;
        $mainUserId = $gApp["userAliasId"];
        $params = [
            "ID"=>"",
            "Account"=>"",
            "data"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $account = $params["Account"];
        $data = $params["data"];
        $this->log->actionLog("#model#user#afterDeleteSub#id=$id;account=$account");
        $this->db->delete2ListWKey("PersonalPrivateKey", "AccountID", $id);
        $this->db->delete2ListWKey("PersonalRfcardKey", "AccountID", $id);
        // 6.1删除权限组等

        // 删除用户自己的权限组
        $userGroups = $this->db->querySList('select ID from UserAccessGroup where Account = :Account', [":Account"=>$account]);
        $this->db->delete2ListWKey('UserAccessGroup', 'Account', $account);
        foreach ($userGroups as $userGroup) {
            $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $userGroup['ID']);
        }

        // V6.1 新pm表删除
        $access = $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account'=> $account]);
        foreach ($access as &$val) {
            $val = $val['AccessGroupID'];
        }
        unset($val);
        $this->db->delete2ListWKey('AccountAccess', 'Account', $account);
        $this->db->delete2ListWKey('CommPerRfKey', 'Account', $account);
        $this->db->delete2ListWKey('CommPerPrivateKey', 'Account', $account);

        // 删除token
        $this->deleteSubUserToken($account);
        // active调整
        $mainUserData = $this->db->querySList("select Role,ParentID,ExpireTime,Account from PersonalAccount where ID=:ID", [":ID"=>$data['ParentID']])[0];
        $perComMngId = $mainUserData["ParentID"];
        $node = $mainUserData["Account"];
        $role = $mainUserData["Role"];
        $expireTime = $mainUserData["ExpireTime"];
        $this->log->actionLog("#model#user#afterDeleteSub#;role=$role;expireTime=$expireTime");
        // $chargeService = $this->services["charge"];
        // if($role == PERENDMROLE) {
        //     $charges = $chargeService->getCharge($perComMngId,2);
        // }else{
        //     $charges = $chargeService->getCharge($perComMngId,1);
        // }
        // $users = $this->db->querySList("select ID from PersonalAccount where Active = 1 and  Role in (".PERENDMROLE.",".COMENDMROLE.") and ParentID = :ParentID",[":ParentID"=>$mainUserId]);
        // $appCount = $charges["AppsNumber"]-1;
        // $this->log->actionLog("#model#user#afterDeleteSub#userCount=".count($users).";appCount=$appCount");
        // if(count($users) < $appCount)
        // {
        //     $activeNum = $appCount - count($users);
        //     $inactiveUsers = $this->db->querySList("select ID from PersonalAccount where Active = 0 and Role in (".PERENDMROLE.",".COMENDMROLE.") and ParentID = :ParentID order by ID",[":ParentID"=>$mainUserId]);
        //     $this->log->actionLog("#model#user#afterDeleteSub#inactiveUsers=$inactiveUsers");
        //     foreach($inactiveUsers as $inactiveUser)
        //         if($activeNum > 0)
        //         {
        //             $activeNum--;
        //             $this->db->update2ListWID("PersonalAccount",[":ID"=>$inactiveUser["ID"],":Active"=>1,":ExpireTime"=>$expireTime]);
        //             // V5.3调整sipEnable
        // 			$this->db->insert2List("LocalSipTransaction",[":Sip"=>$inactiveUser["SipAccount"],":Message"=>json_encode(["messageType"=>"3","sip"=>$inactiveUser["SipAccount"],"sipEnable"=>"1"])]);
        //         }
        // }
        // 更改数据流
        \util\computed\setGAppData(["unitId"=>$mainUserData["UnitID"], "Node"=>$node, 'AccessGroup'=>$access, "CommunityId" => $perComMngId]);
        $this->log->endUserLog(2, null, "delete user:$account");
    }

    /**
     * @msg: 删除主账号后删除关联数据
     * @services: callHistoryUtil
     */
    public function afterDeleteMain()
    {
        $params = [
            "ID"=>"",
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $account = $params["Account"];
        $this->log->actionLog("#model#user#afterDeleteMain#id=$id;account=$account");
        // 删除群组通话记录TODO
        $sipGroup = $this->db->querySList("select SipGroup from SipGroup2 where Account = :Account", [":Account"=>$account])[0]["SipGroup"];
        $this->services["callHistoryUtil"]->deleteCallHistoryRowWKey("CalleeID", $sipGroup);
        $this->log->actionLog("#model#user#afterDeleteMain#sipGroup=$sipGroup");
        // 删除sip群组
        $this->db->delete2ListWKey("SipGroup2", "Account", $account);

        $this->db->delete2ListWKey("PersonalBillingInfo ", "Account", $account);
        $this->db->delete2ListWKey("PersonalAccountCnf", "Account", $account);
        $this->db->delete2ListWKey("PersonalAppTmpKey", "Node", $account);
        $this->db->delete2ListWKey("PersonalPrivateKey", "Node", $account);
        $this->db->delete2ListWKey("PersonalRfcardKey", "Node", $account);
        
        $this->db->delete2ListWKey("VideoLength", "Node", $account);
        $this->db->delete2ListWKey("VideoList", "Node", $account);
        $this->db->delete2ListWKey("VideoSchedule", "Node", $account);
        // 删除redis角色的TOKEN TODO
        // 更改数据流
        \util\computed\setGAppData(["SipGroup"=>$sipGroup]);
    }

    /**
     * @name: 删除账户（包含主从）后，回收sip等
     * @service：sip
     */
    public function afterDelete()
    {
        global $gApp;
        $perComMngId = $gApp["userAliasId"];
        $params = [
            "ID"=>"",
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $sip = $params["Account"];
        $id = $params["ID"];

        $areaMngAccount = $this->db->querySList("select A.Account from Account A join Account B on A.ID = B.ParentID where B.ID=:ID", [":ID"=>$perComMngId])[0]["Account"];
        $this->log->actionLog("#model#user#afterDelete#sip=$sip;areaMngAccount=$areaMngAccount");
        $sipService = $this->services["sip"];
        $sipService->sipCollecting($sip, $areaMngAccount);
        $sipService->del2Freeswish($sip);
        $this->deleteFace();

        // 更改数据流
        \util\computed\setGAppData(["AreaAccount"=>$areaMngAccount]);
    }

    public function deleteFace()
    {
        $params = [
            "ID"=>"",
            "FaceData"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $faceData = $params["FaceData"] ?: [];
        // V5.4删除人脸数据
        $faceData = array_merge($faceData, $this->db->querySList("select FaceUrl,PersonalAccountID from FaceMng where PersonalAccountID=:PersonalAccountID", [":PersonalAccountID"=>$id]));
    
        $this->db->delete2ListWKey("FaceMng", "PersonalAccountID", $id);

        // 更改数据流
        \util\computed\setGAppData(["FaceData"=>$faceData]);
    }

    /**
     * @name: 可以提供给外部的删除token
     */
    public function deleteUserToken()
    {
        $params = [
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $this->log->actionLog("#model#user#deleteUserToken#account=$account");
        $this->deleteMainUserToken($account);
        // $this->deleteSubUserToken($account);
    }

    private function deleteMainUserToken($account)
    {
        include_once __DIR__."/../../database/redis.php";
        // 删除AppToken
        $this->db->delete2ListWKey("Token", "Account", $account);
        $redis = \database\CRedis::getInstance();
        $redis->select(REDISDB2TOKEN);
        $oldToken = $redis->get($account);
        $redis->del($oldToken);
        $redis->del($account);
    }

    private function deleteSubUserToken($account)
    {
        $this->db->delete2ListWKey("Token", "Account", $account);
    }


    public function deleteComMainUser()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $userId = $params["userAliasId"];

        $accounts = $this->db->querySList('select Account,ParentID from PersonalAccount where ID=:ID', [':ID'=>$id]);
        $node = $accounts[0]['Account'];
        $myID = $accounts[0]['ParentID'];
        $accounts = array_merge(
            $accounts,
            $this->db->querySList(
                'select Account from PersonalAccount where ParentID=:ID and Role = 21',
                [':ID'=>$id]
            )
        );

        $access = [];
        foreach ($accounts as &$val) {
            $val = $val['Account'];
            if ($node != $val) {
                // 通知智能家居增加房间主账号字段  @LastEditors: cj
                \util\computed\setSmartHomeTask(['Type' => 10, 'Key' => $val, 'CommunityID' => $myID, 'RoomSip'=>$node]);
            }
            $access = array_merge($access, $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account'=>$val]));
        }
        unset($val);

        foreach ($access as &$val) {
            $val = $val['AccessGroupID'];
        }
        unset($val);

        $this->deleteComUserControl($userId, [$id]);
        \util\computed\setGAppData(["AccessGroup"=>$access, "Account"=>$accounts, "Node"=>$node]);
    }

    public function betchDeleteComMainUser()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $ids = explode(";", $id);
        $userId = $params["userAliasId"];

        $accounts = [];
        $node = [];
        $access = [];
        foreach ($ids as $id) {
            $account = $this->db->querySList(
                'select Account from PersonalAccount where ID=:ID',
                [':ID'=>$id]
            );
            $roomSip = $account[0]['Account'];
            $myID = $this->db->querySList(
                'select ParentID from PersonalAccount where ID=:ID',
                [':ID'=>$id]
            )[0]['ParentID'];
            $accounts = array_merge(
                $accounts,
                $account
            );
            array_push($node, $account[0]['Account']);
            $account = $this->db->querySList(
                'select Account from PersonalAccount where ParentID=:ID and Role = 21',
                [':ID'=>$id]
            );
            // 通知智能家居增加房间主账号字段  @LastEditors: cj
            foreach ($account as $val) {
                \util\computed\setSmartHomeTask(['Type' => 10, 'Key' => $val['Account'], 'CommunityID' => $myID, 'RoomSip'=>$roomSip]);
            }
            $accounts = array_merge(
                $accounts,
                $account
            );
        }

        $access = [];
        foreach ($accounts as &$val) {
            $val = $val['Account'];
            array_push($access, $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account'=>$val]));
        }
        unset($val);

        $this->deleteComUserControl($userId, $ids);
        \util\computed\setGAppData(["AccessGroup"=>$access, "Account"=>$accounts, "Node"=>$node]);
    }

    public function deleteEndUserForPM()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $id = explode(";", $id);
        $userId = $params["userAliasId"];
        // 拆分主从账户
        $node = [];
        $main = [];
        $sub = [];
        $access = [];
        $allAccount = [];
        foreach ($id as $val) {
            $data = $this->db->querySList('select Role,Account from PersonalAccount where ID = :ID', [':ID'=>$val])[0];
            $role = $data["Role"];
            if ($role == COMENDMROLE) {
                array_push($main, $val);
                array_push($node, $data['Account']);
            } elseif ($role == COMENDSROLE) {
                array_push($sub, $val);
            }

            $accessIds = $this->db->querySList(
                'select AccessGroupID from AccountAccess where Account = :Account',
                [':Account' => $data["Account"]]
            );
            foreach ($accessIds as $accessId) {
                array_push($access, $accessId['AccessGroupID']);
            }

            array_push($allAccount, $data["Account"]);
            // 删除主账户会同时删除从账户，需要一起记录
            if ($role == COMENDMROLE) {
                $subAccounts = $this->db->querySList('select Account from PersonalAccount where ParentID=:ParentID and Role = 21', [':ParentID'=>$val]);
                foreach ($subAccounts as $subAccount) {
                    array_push($allAccount, $subAccount["Account"]);
                    $accessIds = $this->db->querySList(
                        'select AccessGroupID from AccountAccess where Account = :Account',
                        [':Account' => $subAccount["Account"]]
                    );
                    foreach ($accessIds as  $accessId) {
                        array_push($access, $accessId['AccessGroupID']);
                    }
                }
            }
        }

        // 检查从账户是否能删
        foreach ($sub as $val) {
            $subData = $this->db->querySList(
                'select count(*),P1.Account from PersonalAccount P join PersonalAccount P1 on P.ParentID = P1.ID where P1.ParentID = :ParentID and P.ID=:ID and P.Role = 21',
                [':ParentID'=>$userId, ':ID'=>$val]
            )[0];
            $count = $subData['count(*)'];
            if ($count == 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            \util\computed\setGAppData(["ID"=>$val]);

            $this->delete();
            $this->afterDeleteSub();
            $this->afterDelete();
            array_push($node, $subData['Account']);
        }

        // 使用之前旧PM批量删除用户的逻辑删除主账户
        $this->deleteComUserControl($userId, $main);
        foreach ($main as $val) {
            \util\computed\setGAppData(["ID"=>$val]);
            $this->deleteFace();
        }
        $allAccount = array_unique($allAccount);
        \util\computed\setGAppData(["AccessGroup"=>$access, "Account"=>$allAccount, "Node"=>$node]);
    }

    /**
     * @description 根据当前要注销的帐号，查询赋值调用deleteEndUserForPM所需的参数
     * <AUTHOR> 2022/3/15 14:44 V6.4
     * @return void
     * @lastEditor csc 2022/3/15 14:44 V6.4
     */
    public function setDelAccountParams()
    {
        $params = [
            'userAliasId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $userData = $this->db->querySList('select ParentID, Role, Account from PersonalAccount where ID = :ID', [':ID' => $userId])[0];
        $role = $userData["Role"];
        if ($role == COMENDMROLE) {
            //主账户，parentId是社区管理员id
            $comManageId = $userData['ParentID'];
        } else {
            //从帐号，通过主帐号查找社区管理员id
            $subUserData = $this->db->querySList('select ParentID, Role, Account from PersonalAccount where ID = :ID', [':ID' => $userData['ParentID']])[0];
            $comManageId = $subUserData['ParentID'];
        }

        \util\computed\setGAppData(['ID' => $userId, 'userAliasId' => $comManageId]);
    }

    /**
     * @description 设置删除参数并删除从帐号
     * @return void
     * @lastEditor csc 2022/3/15 11:52 V6.4
     * @author: csc 2022/3/15 11:52 V6.4
     */
    public function setParamsAndDelSub()
    {
        $params = [
            'userAliasId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        //查询出所有从帐号信息并删除
        $subUsers = $this->db->querySList("select ID, Account from PersonalAccount where Role in (".PERENDSROLE.") and ParentID = :ParentID", [':ParentID' => $userId]);
        foreach ($subUsers as $subUser) {
            \util\computed\setGAppData(['ID' => $subUser['ID'], 'userAliasId' => $subUser['ID'], 'userAlias' => $subUser['Account']]);
            $this->delSingleSubAccount();
        }

        //赋予installer相关id进行删除
        $userData = $this->db->querySList('select ParentID,Account from PersonalAccount where ID = :ID', [':ID'=>$userId])[0];
        $installerData = $this->db->querySList('select ID, Account from Account where ID = :ID', [':ID'=>$userData['ParentID']])[0];
        global $gApp;
        $gApp["userAlias"] = $installerData['Account'];
        $gApp["userAliasId"] = $installerData['ID'];

        \util\computed\setGAppData(['ID' => $userId]);
    }

    /**
     * @description 删除单住户从账号
     * @param ID 待删除从账号的ID
     * @return void
     * @lastEditor csc 2022/3/15 11:45 V6.4
     * @author: csc 2022/3/15 11:45 V6.4
     */
    public function delSingleSubAccount()
    {
        $params = [
            'userAliasId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        \util\computed\setGAppData(['ID' => $userId]);
        //赋予主账号相关id进行删除
        $userData = $this->db->querySList('select ParentID,Account from PersonalAccount where ID = :ID', [':ID'=>$userId])[0];
        $mainUserData = $this->db->querySList('select ID, Account from PersonalAccount where ID = :ID', [':ID'=>$userData['ParentID']])[0];
        global $gApp;
        $gApp["userAlias"] = $mainUserData['Account'];
        $gApp["userAliasId"] = $mainUserData['ID'];

        $this->delete();
        $this->afterDeleteSub();
        $this->afterDelete();
        $this->models["notify"]->userSubDelete();
        $this->models["notify"]->newSetUser();
    }
}
