<?php
namespace plan\process;

const OTHER_PROCESS = [
    "getCustomerService"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"customerService.query"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getSelfCustomerService"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"customerService.querySelf"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getInstallerCustomerService"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"customerService.queryInstaller"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "editCustomerService"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"customerService.update"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "getUpgradeList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.queryForPCMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getUpgradeVersion"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.getVersion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addUpgrade"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.add"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "editUpgrade"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.edit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "deleteUpgrade"=>[
        [
            "type"=>"model",
            "model"=>"update.delete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "getVersionForSup"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.queryVersionForSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getVersionForArea"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getMngTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.queryVersionForArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addVersion"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"versionLogCheck"],
            ]
        ], [
            "type"=>"model",
            "model"=>"update.addVersion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "deleteVersion"=>[
        [
            "type"=>"model",
            "model"=>"update.deleteVersion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "editVersion"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"versionLogCheck"],
            ]
        ], [
            "type"=>"model",
            "model"=>"update.editVersion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "queryModel"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"update.queryModel"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryAllModel"=>[
        [
            "type"=>"model",
            "model"=>"update.queryAllModel"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addModel"=>[
        [
            "type"=>"model",
            "model"=>"update.addModel"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "editModel"=>[
        [
            "type"=>"model",
            "model"=>"update.editModel"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "deleteModel"=>[
        [
            "type"=>"model",
            "model"=>"update.deleteModel"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "addManager"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"mngAccountCheck","params"=>["Account"]],
                ["name"=>"manageEmailCheck","params"=>["Email"]],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.add",
            // "models"=>["chargePlan.setManage"]
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAddPw,
            "params"=>["password"]
        ]
    ],
    "editManager"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"manageEmailCheck","params"=>["Email","ID"]],
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.edit",
            // "models"=>["chargePlan.setManage"]
        ],[
            "type"=>"model",
            "model"=>"chargePlan.afterSetMonthFee"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "deleteManager"=>[
        [
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.delete"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "resetManagerPw"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.resetPw"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessResetPw,
            "params"=>["password"]
        ]
    ],
    "getAreaMngList"=>[
        [
            "type"=>"model",
            "model"=>"manage.queryArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getInstallListForSup"=>[
        [
            "type"=>"model",
            "model"=>"manage.queryPerManageForSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getInstallListForArea"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.queryPerManageForArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAllAreaMng"=>[
        [
            "type"=>"model",
            "model"=>"manage.queryAllArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "changeAreaIdentity"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.changeAreaToPer"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["token"=>"token","grade"=>"grade","account"=>"account","timeZone"=>"timeZone","Role"=>"Role"]
        ]
    ],
    "installerIsSpecial"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"perMngSpecialCheck"]
            ]
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery
        ]
    ],
    "changePerToCom"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.changePerToCom"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["token"=>"token","grade"=>"grade","Role"=>"Role"]
        ]
    ],
    "getComGroupList"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.queryComGroupList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "goToAlexaLogin"=>[
        [
            "type"=>"model",
            "model"=>"alexa.goToLogin"
        ],
    ],
    "deleteAlexaToken"=>[
        [
            "type"=>"model",
            "model"=>"alexa.deleteToken"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "alexaLogin"=>[
        [
            "type"=>"model",
            "model"=>"alexa.thirdLogin"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["Account"=>"Account","token"=>"Token"]
        ],[
            "type"=>"model",
            "model"=>"notify.alexaLogin"
        ]
    ],
    "alexaToken"=>[
        // 接口内部直接输出
        [
            "type"=>"model",
            "model"=>"alexa.thirdAuth"
        ]
    ],
    "alexaSetUid"=>[
        [
            "type"=>"model",
            "model"=>"alexa.setUID"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "alexaGetStatus"=>[
        [
            "type"=>"model",
            "model"=>"alexa.getStatus"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit,
            "options"=>["mode"=>"mode","connectivity"=>"connectivity","alarm"=>"alarm"]
        ]
    ],
    "alexaSetAram"=>[
        [
            "type"=>"model",
            "model"=>"alexa.setAram"
        ],[
            "type"=>"model",
            "model"=>"notify.alexaSetArming"
        ]
    ],
    "alexaSetAuthorization"=>[
        [
            "type"=>"model",
            "model"=>"alexa.setAuthorization"
        ]
    ],
    "alexaChangeArm"=>[
        [
            "type"=>"model",
            "model"=>"alexa.changeArm"
        ]
    ],
    "alexaChangeStatus"=>[
        [
            "type"=>"model",
            "model"=>"alexa.changeStatus"
        ]
    ],
    "alexaChangeAlarm"=>[
        [
            "type"=>"model",
            "model"=>"alexa.changeAlarm"
        ]
    ],
    "sendTempKeyEmail"=>[
        [
            "type"=>"echo",
            "code"=>StateSuccessSendEmail
        ],[
            "type"=>"model",
            "model"=>"notify.tempKeyShare"
        ]
    ],
    "getToolMenu"=>[
        [
            "type"=>"model",
            "model"=>"system.queryMenuDownList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAllAudit"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"log.queryAudit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAppUpgradeTXT"=>[
        [
            "type"=>"model",
            "model"=>"update.getUpgradeMsg"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessGetUpgrade,
            "options"=>["data"=>"data"]
        ]
    ],
    'importKit' => [
        [
            "type"=>"model",
            "model"=>"user.importKit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "getAppWord"=>[
        [
            "type"=>"model",
            "model"=>"update.getAppWord"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];
