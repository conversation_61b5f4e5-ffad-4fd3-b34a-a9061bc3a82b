<?php
/*
 * @Description: 对于email为空字符串的处理
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 17:06:35
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";

include_once __DIR__."/../util/computed.php";
class CUserEmailDeal implements IMiddleware {
    public $email;
    function handle (\Closure $next) {
        global $cLog;
        $params = ["Email"=>""];
        $email = \util\model\getParamsFromContainer($params,$this->dataContainer)["Email"];
        
        $cLog->actionLog("#middle#userEmailDeal#email=".$email);
        if($email === "") {
            \util\computed\setGAppData(["Email"=>null]);
        }
        $next();
    }
}