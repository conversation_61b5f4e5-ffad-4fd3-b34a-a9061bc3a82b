<?php
/*
 * @Description: 终端用户手机号重复性验证
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:51:45
 * @LastEditors  : kxl
 */

namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";

include_once __DIR__."/../database/main.php";
class CUserMobileCheck implements IMiddleware {
    public function handle (\Closure $next) {
        global $cMessage;
        global $cLog;
        $params = ["MobileNumber"=>"","ID"=>""];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        $mobile = $params["MobileNumber"];
        $id = $params["ID"];
        
        $cLog->actionLog("#middle#userMobileCheck#mobile=$mobile;ID=$id");
        if($mobile != null || $mobile != "") {
            $db = \database\CDatabase::getInstance();
            if($id)
                $data = $db->querySList("select ID from PersonalAccount where MobileNumber = :MobileNumber and ID != :ID",[":MobileNumber"=>$mobile,":ID"=>$id]);
            else
                $data = $db->querySList("select ID from PersonalAccount where MobileNumber = :MobileNumber",[":MobileNumber"=>$mobile]);
            $cLog->actionLog("#middle#userMobileCheck#exits=".count($data));
            if(count($data) > 0) $cMessage->echoErrorMsg(StateMobileExits);
        }
        $next();
    }
}