<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

require_once __DIR__.'/util.func.php';

const OFFLINE_BITE = 2;
const USERPIN_BITE = 3;
const SIM_BITE = 4;
class CCommunityData
{
    public function getAllCount()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        //build
        $builds = count($this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]));
        //room
        $rooms = count($this->db->querySList(
            "select R.ID from CommunityRoom R join CommunityUnit U on R.UnitID = U.ID where U.MngAccountID = :MngAccountID",
            [":MngAccountID" => $userId]
        ));
        // inactiveUser
        $inactiveUsers = count($this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and Role = 20 and Active = 0", [":ParentID" => $userId]));
        // expriUsers
        $expriUsers = count($this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and Active = 1 and ExpireTime < :ExpireTime", [":ParentID" => $userId, ":ExpireTime" => \util\computed\getNow()]));

        //door phones
        $doors = count($this->db->querySList(
            "select ID from Devices where MngAccountID = :MngAccountID AND (Type = :Type or Type = :Type2)",
            [":MngAccountID" => $userId, ":Type" => 1, ":Type2" => 0]
        ));
        //offline door
        $offDoors = count($this->db->querySList(
            "select ID from Devices where MngAccountID = :MngAccountID AND (Type = :Type or Type = :Type2) and Status = :Status",
            [":MngAccountID" => $userId, ":Type" => 1, ":Type2" => 0, ":Status" => 0]
        ));
        //indoor
        $indoors = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId, ":Type" => 2]]));
        //offindoor
        $offIndoors = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId, ":Type" => 2, ":Status" => 0]]));


        //access
        $access = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId, ":Type" => 50]]));
        //offaccess
        $offAccess = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId, ":Type" => 50, ":Status" => 0]]));

        //all end user
        $endUsers = count($this->db->querySList(
            "select ID from PersonalAccount where ParentID = :ParentID and Role = 20  and Special = 0 union
                            select P.ID from PersonalAccount P join PersonalAccount A on P.ParentID = A.ID where P.Role = 21 and A.Role = 20 and A.ParentID = :ParentID",
            [":ParentID" => $userId]
        ));
        //register user
        $endRUsers = count($this->db->querySList(
            "select ID from PersonalAccount where ParentID = :ParentID and Role = 20 and Initialization = 1 and Special = 0 union
                            select P.ID from PersonalAccount P join PersonalAccount A on P.ParentID = A.ID where P.Role = 21 and A.Role = 20 and P.Initialization = 1 and A.ParentID = :ParentID and A.Special = 0",
            [":ParentID" => $userId]
        ));
        $endURUser = $endUsers - $endRUsers;
        //app
        $apps = count($this->db->querySList(
            "select ID from PersonalAccount where ParentID = :ParentID and Role = 20 and appLoginStatus = 1 and Special = 0 union
        select P.ID from PersonalAccount P join PersonalAccount A on P.ParentID = A.ID where P.Role = 21 and A.Role = 20 and P.appLoginStatus = 1 and A.ParentID = :ParentID",
            [":ParentID" => $userId]
        ));

        //phones
        $phones = count($this->db->querySList(
            "select ID from PersonalAccount where ParentID = :ParentID and Role = 20 
            and MobileNumber is not null and Special = 0
            union select A.ID from PersonalAccount A join PersonalAccount B on A.ParentID = B.ID 
            where B.ParentID = :ParentID and A.Role = 21 and A.MobileNumber is not null and B.Special = 0",
            [":ParentID" => $userId]
        ));

        //inactive
        $inactive = count($this->db->queryAllList("PersonalAccount", ["equation" => [":ParentID" => $userId, ":Role" => 20, ":Active" => 0]]));

        //tmpkeys
        $tmpkeys = count($this->db->querySList("select ID from PubAppTmpKey where MngAccountID = :ID union all select ID from PersonalAppTmpKey where MngAccountID = :ID", [":ID" => $userId]));
        //rfcards
        $rfcards = count($this->db->querySList("select ID from PubRfcardKey where MngAccountID = :ID union all select ID from PersonalRfcardKey where MngAccountID = :ID", [":ID" => $userId]));
        //calls
        $callTables = $this->services["callHistoryUtil"]->getCallHistoryTables();
        $calls = count($this->services["callHistoryUtil"]->getCallHistoryResult($callTables, "select ID from %s  where MngAccountID = :MngAccountID", [":MngAccountID" => $userId]));

        list($todayRelease, $todayCount) = $this->getodayDoor();
        list($yearRelease, $yearCount) = $this->getMonDoor();
        $data = ["builds" => $builds, "rooms" => $rooms, "inactiveUsers" => $inactiveUsers, "expriUsers" => $expriUsers, "doors" => $doors, "offdoors" => $offDoors, "indoors" => $indoors, "offindoor" => $offIndoors, "access" => $access, "offaccess" => $offAccess,
            "endusers" => $endUsers, "endrusers" => $endRUsers, "endurusers" => $endURUser, "apps" => $apps, "phones" => $phones, "inactive" => $inactive,
            "tmpkeys" => $tmpkeys, "rfcards" => $rfcards, "calls" => $calls, "todayRelease" => $todayRelease, "todayCount" => $todayCount,
            "yearRelease" => $yearRelease, "yearCount" => $yearCount];
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getodayDoor()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $sql = "select ID from PersonalCapture where MngAccountID = :MngAccountID and CaptureType < 102";
        $now = \util\computed\getNow();
        $now = \util\time\setTimeZone($now, $timeZone, "", "+");
        $day = explode(" ", $now)[0];
        $zero = $day . " 00:00:00";
        $two = $day . " 02:00:00";
        $four = $day . " 04:00:00";
        $six = $day . " 06:00:00";
        $eight = $day . " 08:00:00";
        $ten = $day . " 10:00:00";
        $twelve = $day . " 12:00:00";
        $fourteen = $day . " 14:00:00";
        $sixteen = $day . " 16:00:00";
        $eighteen = $day . " 18:00:00";
        $twty = $day . " 20:00:00";
        $twtytwo = $day . " 22:00:00";
        $twtyfour = $day . " 24:00:00";
        $times = [$zero, $two, $four, $six, $eight, $ten, $twelve, $fourteen, $sixteen, $eighteen, $twty, $twtytwo, $twtyfour];
        $data = [];
        for ($i = 0; $i < count($times) - 1; $i++) {
            array_push($data, count($this->db->querySList($sql . " and CaptureTime >= '" . \util\time\setTimeZone($times[$i], $timeZone, "", "-") . "' and CaptureTime <= '" . \util\time\setTimeZone($times[$i + 1], $timeZone, "", "-") . "'", [":MngAccountID" => $userId])));
        }

        $count = 0;
        foreach ($data as $val) {
            $count += $val;
        }

        return [$data, $count];
    }

    public function getMonDoor()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];

        $sql = "select count(ID) from PersonalCapture where MngAccountID = :MngAccountID and CaptureType < 102";
        $count = $this->db->querySList($sql, [":MngAccountID" => $userId])[0]["count(ID)"];
        $now = \util\computed\getNow();
        $day = explode(" ", $now)[0];
        $year = explode("-", $day)[0];
        $month = explode("-", $day)[1];
        $data = [];
        for ($i = 1; $i < intval($month); $i++) {
            if ($i < 10) {
                $i = "0$i";
            }
            $time = $year . $i;
            $captureCount = $this->db->querySList("select C.* from MonthCaptureCounted C join MonthSliceTableName T on C.MonthSliceTableNameID = T.ID where T.YearMonth = :YearMonth and C.MngAccountID = :MngAccountID", [":YearMonth" => $time, ":MngAccountID" => $userId]);
            if (count($captureCount) == 0) {
                array_push($data, 0);
                continue;
            }
            array_push($data, intval($captureCount[0]["DoorCount"]));
        }
        array_push($data, intval($count));
        while (count($data) < 12) {
            array_push($data, 0);
        }


        $count = 0;
        foreach ($data as $val) {
            $count += $val;
        }

        return [$data, $count];
    }

    public function getData()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        // 6.3 区别社区和办公
        $projectData = $this->db->querySList("select TimeZone,CustomizeForm,UUID,Grade from Account where ID = :ID", [':ID' => $userId])[0];
        if ($projectData['Grade'] == OFFICEGRADE) {
            $data = $this->db->querySList("select EnableMotion,MotionTime from OfficeInfo where AccountUUID = :AccountUUID", [':AccountUUID' => $projectData['UUID']])[0];
        } elseif ($projectData['Grade'] == COMMUNITYGRADE) {
            $data = $this->db->querySList("select EnableMotion,MotionTime from CommunityInfo where AccountID = :AccountID", [':AccountID' => $userId])[0];
        }
        $data['TimeZone'] = $projectData['TimeZone'];
        $data['CustomizeForm'] = $projectData['CustomizeForm'];
        \util\computed\setGAppData(["data" => $data]);
    }

    public function chagneTime()
    {
        $params = [
            "userAliasId" => "",
            "TimeZone" => "",
            "CustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["TimeZone"];
        $customizeForm = $params["CustomizeForm"];
        $this->log->actionLog("#model#communityData#chagneTime#userId=$userId;timeZone=$timeZone;customizeForm=$customizeForm;");
        $this->db->update2ListWID("Account", [":TimeZone" => $timeZone, ":CustomizeForm" => $customizeForm, ":ID" => $userId]);

        $account = $this->db->querySList('select Account from Account where ID=:ID', [":ID" => $userId])[0]['Account'];
        $this->auditLog->setLog(AuditCodeSetCommunityTime, $this->env, ["GTM$timeZone"], $account);
    }

    public function setMotion()
    {
        $params = [
            "userAliasId" => "",
            "EnableMotion" => "",
            "MotionTime" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $enable = $params["EnableMotion"];
        $time = $params["MotionTime"];

        // 6.3 区别社区和办公
        $projectData = $this->db->querySList("select UUID,Grade from Account where ID = :ID", [':ID' => $userId])[0];
        if ($projectData['Grade'] == COMMUNITYGRADE) {
            $this->db->update2ListWKey("CommunityInfo", [":EnableMotion" => $enable, ":MotionTime" => $time, ":AccountID" => $userId], "AccountID");
        } elseif ($projectData['Grade'] == OFFICEGRADE) {
            $this->db->update2ListWKey("OfficeInfo", [":EnableMotion" => $enable, ":MotionTime" => $time, ":AccountUUID" => $projectData['UUID']], "AccountUUID");
        }
        $this->log->actionLog("#model#communityData#setMotion#userId=$userId;enable=$enable;time=$time;");

        \util\computed\setGAppData(["ID" => $userId]);
    }

    public function getMyCommunityList()
    {
        $params = [
            "user" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["user"];
        $data = $this->db->querySList("select A.ID,A.Location,A.Grade from Account A join PropertyMngList M on A.ID = M.CommunityID join Account B on M.PropertyID = B.ID where B.Account = :Account order by A.Location", [":Account" => $user]);
        // V6.3 区别社区和办公项目
        foreach ($data as &$val) {
            if ($val['Grade'] == COMMUNITYGRADE) {
                $val['IsNew'] = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [":AccountID" => $val["ID"]])[0]['IsNew'];
            } elseif ($val['Grade'] == OFFICEGRADE) {
                $val['IsNew'] = '1';
            }
        }
        unset($val);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getLastFiveDoorLog()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $offset = 0;
        $rows = 5;
        $sql = "select Location,Initiator,CaptureTime,Response,CaptureType from %s where MngAccountID = :MngAccountID  and (CaptureType = 103 or CaptureType <102) order by ID desc";
        $bindArray = [":MngAccountID" => $userId];
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
        list($count, $data) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
        $types = CAPTURE_TYPE;
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome) {
            $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                $types = CAPTURE_TYPE_HOME;
            }
        }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$val) {
            if ($val["Initiator"] == 'visitor' || $val["CaptureType"] == 4) {
                $val["Initiator"] = MSGTEXT['visitor'];
            }
            $val["CaptureType"] = $types[$val["CaptureType"]];
            $val["CaptureAction"] = $types[$val["CaptureType"]];
            $val["Response"] = $val["Response"] == 0 ? MSGTEXT["success"] : MSGTEXT["failed"];
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getWeekData()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        //时区计算
        $now = \util\computed\getNow();
        $now = \util\time\setTimeZone($now, $timeZone, "", "-");
        $today = explode(" ", $now)[0] . " 00:00:00";

        $week = date("w", strtotime($today));
        $week = $week == 0 ? 7 : $week;
        $week -= 1;
        $startTime = date("Y-m-d H:i:s", strtotime("$today - $week day"));
        $startTime = $startTime;
        $data = [];

        $tables = $this->services["captureUtil"]->getCaptureTables(1);
        $currTables = [];
        foreach ($tables as $key => $table) {
            array_push($currTables, $table);
            if ($key >= 1) {
                break;
            }
        }


        $sql = "select ID from %s where MngAccountID = :MngAccountID and CaptureType < 102 and CaptureTime >='" . $startTime . "'";
        $bindArray = [":MngAccountID" => $userId];

        $data["door"] = count($this->services["captureUtil"]->getCaptureResult($currTables, $sql, $bindArray));

        $callTables = $this->services["callHistoryUtil"]->getCallHistoryTables();
        $data["call"] = count($this->services["callHistoryUtil"]->getCallHistoryResult($callTables, "select ID from %s 
                        where  MngAccountID = :MngAccountID and StartTime >='" . $startTime . "'", [":MngAccountID" => $userId]));
        $data["users"] = count($this->db->querySList("select ID from PersonalAccount where ParentID = :ParentID and Role = 20 and Special = 0 and CreateTime >='" . $startTime . "'
                    union select P.ID from PersonalAccount P join PersonalAccount P2 on P.ParentID = P2.ID where P2.ParentID = :ParentID and P2.Special = 0 and P.Role = 21 and P.CreateTime >='" . $startTime . "'", [":ParentID" => $userId]));

        $data["tmpKey"] = count($this->db->querySList("select ID from PersonalAppTmpKey where MngAccountID = :MngAccountID and BeginTime >= '" . $startTime . "' 
                            union all select ID from PubAppTmpKey  where MngAccountID = :MngAccountID and BeginTime >= '" . $startTime . "'", [":MngAccountID" => $userId]));

        \util\computed\setGAppData(["data" => $data]);
    }

    public function getCommunityDetail()
    {
        global $cMessage;
        $params = [
            "userAlias" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            'userId' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        // 6.3 区别社区和办公项目
        $projectData = $this->db->querySList("select Grade,UUID,ManageGroup from Account where ID = :ID", [":ID" => $userId])[0];
        $projectGrade = $projectData["Grade"];
        if ($projectGrade == COMMUNITYGRADE) {
            // 获取PM管理的社区的相关权限,需兼容PM link情况
            $pmId = $params['userId'];
            $pmUUID = $this->db->querySList("select UUID from Account where ID = :ID", [":ID" => $pmId])[0]['UUID'];
            $userInfoUUID = $this->db->querySList("select UserInfoUUID from AccountMap where AccountUUID = :AccountUUID", [":AccountUUID" => $pmUUID])[0]['UserInfoUUID'];
            $pmUUIDs = $this->db->querySList("select AccountUUID from AccountMap where UserInfoUUID = :UserInfoUUID", [":UserInfoUUID" => $userInfoUUID]);
            $pmUUIDs = array_column($pmUUIDs, 'AccountUUID');
            list($bindString, $bindArray) = \util\arr\getImplodeData($pmUUIDs);
            $pmIds = $this->db->querySList("select ID from Account where UUID in ($bindString)", $bindArray);
            $pmIds = array_column($pmIds, 'ID');
            list($bindString, $bindArray) = \util\arr\getImplodeData($pmIds);
            $bindArray[':CommunityID'] = $userId;
            $pmMngPermission = $this->db->querySList("select EnableDeleteAccount, EnableShowLog from PropertyMngList where PropertyID in ($bindString) and CommunityID = :CommunityID", $bindArray)[0];
            
            $data = $this->db->querySList("select A.Location,I.*,A.TimeZone,A.CustomizeForm,A.Grade,A.ChargeMode,A.ParentID from Account A left join CommunityInfo I on A.ID = I.AccountID where A.ID = :ID", [":ID" => $userId])[0];
            $FeatureExpireTime = $this->db->querySList("select FeatureExpireTime from CommunityInfo where AccountID = :ID", [":ID" => $userId])[0]['FeatureExpireTime'];
            $data['IsHaveAccessArea'] = 1;
            if($data['IsNew'] === '1'){
                //只有新社区有pm权限门禁控制
                $data['IsHaveAccessArea'] = \util\computed\getSpecifyBitLE($data['Switch'], 7);
            }
            $data['EnableDeleteAccount'] = intval($pmMngPermission['EnableDeleteAccount']);
            $data['EnableShowLog'] = intval($pmMngPermission['EnableShowLog']);
        } elseif ($projectGrade == OFFICEGRADE) {
            $data = $this->db->querySList("select A.Location,I.*,A.TimeZone,A.CustomizeForm,A.Grade from Account A left join OfficeInfo I on A.UUID = I.AccountUUID where A.ID = :ID", [":ID" => $userId])[0];
            $FeatureExpireTime = $this->db->querySList("select FeatureExpireTime from OfficeInfo where AccountUUID = :UUID", [":UUID" => $projectData["UUID"]])[0]['FeatureExpireTime'];
            $data["IsNew"] = "1";
            $data['IsHaveAccessArea'] = 1;
            $data['EnableDeleteAccount'] = 1;
            $data['EnableShowLog'] = 1;
        }

        $data['TriggerAction'] = \util\computed\getSpecifyBitLE($data['Switch'], 6);
        $data["EnableSIMWarning"] = \util\computed\getSpecifyBitLE($data["Switch"], 4);
        $data["EnableUserPin"] = \util\computed\getSpecifyBitLE($data["Switch"], 3);
        $data["DevOfflineNotify"] = \util\computed\getSpecifyBitLE($data["Switch"], 2);
        $data["EnableLandline"] = \util\computed\getSpecifyBitLE($data["Switch"], 1);
        //v6.1 修改为了获取对应dis对pin是否加密
        $dis = \util\role\getDisForInstaller($userId);
        $data['IsEncryptPin'] = $dis['IsEncryptPin'];
        $this->log->actionLog("#model#officeData#getOfficeDetail#userId=$userId");

        $Item = $this->db->querySList("select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID", [":ID" => $userId])[0]['Item'];
        $IsDefaultFeature = $this->db->querySList('select FeatureID from ManageFeature where AccountID = :ID', [':ID' => $userId])[0]['FeatureID'];
        $FeatureItem = communityData\checkFeaturePlan($FeatureExpireTime, $Item, $IsDefaultFeature);
        $data['LimitCreatePin'] = $FeatureItem[1];
        $data['LimitQRCode'] = $FeatureItem[2];
        // 高级功能，第七位社区第三方设备
        $data['ThirdPartyDevices'] = \util\computed\getSpecifyBitLE($Item, 7);
        // 判断是否展示三方中控设备
        $data['ControlDevices'] = $this->db->querySList('select count(*) from Devices where MngAccountID = :MngAccountID and Brand = 1', [":MngAccountID" => $userId])[0]['count(*)'] > 0 ? 1: 0;
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome) {
            $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                $data['SmartHomeUUID'] = $this->db->querySList(
                    'select SmartHomeUUID from SmartHomeManageMap where Account=:Account',
                    [':Account' => $user]
                )[0]['SmartHomeUUID'];
            }
        }
        //V6.7.1 新增返回IsAutoRenew，pm开关跟随ins
        $insData = $this->db->querySList("select IsAutoRenew from Account where ID = :ID", [":ID" => $projectData['ManageGroup']])[0];
        $data['IsAutoRenew'] = intval($insData['IsAutoRenew']);

        \util\computed\setGAppData(["data" => $data]);
    }

    public function setCommunityDetail()
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "Location" => "",
            "Street" => "",
            "City" => "",
            "PostalCode" => "",
            "States" => "",
            "Country" => "",
            "AptPinType" => "",
            "DevOfflineNotify" => "",
            "EnableSIMWarning" => "",
            "EnableUserPin" => "",
            "PhoneCode" => "",
            "MobileNumber" => "",
            'TriggerAction' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $location = $params["Location"];
        $street = $params["Street"];
        $city = $params["City"];
        //校验邮编格式
        communityData\checkPostalCode($params["PostalCode"]);
        //邮编需要过滤前后的空格
        $postalCode = trim($params["PostalCode"]);
        $states = $params["States"];
        $country = $params["Country"];
        $aptPinType = $params["AptPinType"];
        $devOfflineNotify = $params["DevOfflineNotify"];
        $enableSIMWarning = $params["EnableSIMWarning"];
        $enableUserPin = $params["EnableUserPin"];
        $phoneCode = $params["PhoneCode"];
        $mobileNumber = $params["MobileNumber"];
        $triggerAction = $params['TriggerAction'];
        $this->log->actionLog("#model#communityData#setCommunityDetail#userId=$userId;location=$location;street=$street;city=$city;postalCode=$postalCode;states=$states;country=$country;aptPinType=$aptPinType;devOfflineNotify=$devOfflineNotify");

        // 6.3 区别社区和办公，修改信息的表不同
        $projectInfo = [
            ":Street" => $street,
            ":City" => $city,
            ":PostalCode" => $postalCode,
            ":States" => $states,
            ":Country" => $country,
            ":PhoneCode" => $phoneCode,
            ":MobileNumber" => $mobileNumber
        ];
        $projectData = $this->db->querySList('select UUID,Grade,Location from Account where ID = :ID', [':ID' => $userId])[0];
        $projectGrade = $projectData['Grade'];
        if ($projectGrade == COMMUNITYGRADE) {
            $infoTable = "CommunityInfo";
            $infoField = 'AccountID';
            $data = $this->db->querySList("select Switch,IsNew from $infoTable where AccountID = :AccountID for update", [':AccountID' => $userId])[0];
            $projectInfo[':AptPinType'] = $aptPinType;
            $projectInfo[':AccountID'] = $userId;
            // V6.5 PM增加打开警报触发自动开门的开关(新小区),Switch第6位
            if ($data['IsNew'] === '1') {
                $data['Switch'] = \util\computed\bitOperation($data["Switch"], $triggerAction, 6);
            }
        } elseif ($projectGrade == OFFICEGRADE) {
            $infoTable = "OfficeInfo";
            $infoField = 'AccountUUID';
            $data = $this->db->querySList("select Switch from $infoTable where AccountUUID = :AccountUUID", [':AccountUUID' => $projectData['UUID']])[0];
            $projectInfo[':AccountUUID'] = $projectData['UUID'];
        }
        $oldLocation = $projectData['Location'];

        // 判断流量超额状态是否改变
        $initSIM = \util\computed\getSpecifyBit($data['Switch'], 3);
        $initPIN = \util\computed\getSpecifyBit($data['Switch'], 2);
        $simStateChange = $initSIM == $enableSIMWarning ? false : true;
        $pinStateChange = $initPIN == $enableUserPin ? false : true;

        $tempSwitchForOffline = \util\computed\bitOperation($data["Switch"], $devOfflineNotify, OFFLINE_BITE);
        $tempSwitchForPin = \util\computed\bitOperation($tempSwitchForOffline, $enableUserPin, USERPIN_BITE);
        $switch = \util\computed\bitOperation($tempSwitchForPin, $enableSIMWarning, SIM_BITE);

        $projectInfo[':Switch'] = $switch;
        $this->db->update2ListWID("Account", [":Location" => $location, ":ID" => $userId]);
        $this->db->update2ListWKey($infoTable, $projectInfo, $infoField);

        $reload = 0;
        if ($oldLocation != $location) {
            $reload = 1;
        }

        \util\computed\setGAppData(["reload" => $reload, "simStateChange" => $simStateChange, "pinStateChange" => $pinStateChange]);
        // if($aptPinType != $data["AptPinType"])
        //     communityAptPinChange($userId);
        // updateCommunityAllPubDevNotify($userId);
    }

    public function getBuildRoom()
    {
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $builds = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]);
        $rooms = $this->db->querySList("select R.*,P.RoomNumber, P.Account from CommunityRoom R join CommunityUnit U on U.ID = R.UnitID join PersonalAccount P on P.RoomID = R.ID where U.MngAccountID = :MngAccountID", [":MngAccountID" => $userId]);
        $useRoom = $this->db->querySList("select R.*,P.RoomNumber from CommunityRoom R join CommunityUnit U on U.ID = R.UnitID join PersonalAccount P on P.RoomID = R.ID where U.MngAccountID = :MngAccountID and P.Special = 0", [":MngAccountID" => $userId]);
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome) {
            $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                foreach ($rooms as &$room) {
                    $room['HomeID'] = $this->db->querySList(
                        'select HomeID from SmartHomeUserMap where Account=:Account',
                        [':Account' => $room['Account']]
                    )[0]['HomeID'];
                }
                unset($room);
            }
        }

        $data = ["build" => $builds, "room" => $rooms, "useRoom" => $useRoom];
        \util\computed\setGAppData(["data" => $data]);
    }

    /**
     * @description 根据CommunityID获取dis下的房间信息
     * <AUTHOR> 2022/3/15 14:36 V6.4
     * @param $CommunityID 社区ID
     * @return void
     * @lastEditor csc 2022/3/15 14:36 V6.4
     */
    public function getBuildRoomForDis()
    {
        $params = [
            'userAliasId' => '',
            'CommunityID' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userAliasId'];
        $mngAccountID = $params['CommunityID'];

        $builds = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $mngAccountID]]);

        //查询该mngAccountID是否归属于该dis下的installer
        $this->models['manage']->queryAllInstaller();
        $installs = \util\computed\getGAppDataValue('data');

        $mngAccountIDs = [];
        foreach ($installs as $install) {
            if (!empty($install['Community'])) {
                $mngAccountIDs = array_merge($mngAccountIDs, array_column($install['Community'], 'ID', null));
            }
        }

        $data = ["build" => [], "room" => [], "useRoom" => []];
        if (in_array($mngAccountID, $mngAccountIDs)) {
            $rooms = $this->db->querySList("select R.*,P.RoomNumber, P.Account from CommunityRoom R join CommunityUnit U on U.ID = R.UnitID join PersonalAccount P on P.RoomID = R.ID where U.MngAccountID = :MngAccountID", [":MngAccountID" => $mngAccountID]);
            $useRoom = $this->db->querySList("select R.*,P.RoomNumber from CommunityRoom R join CommunityUnit U on U.ID = R.UnitID join PersonalAccount P on P.RoomID = R.ID where U.MngAccountID = :MngAccountID and P.Special = 0", [":MngAccountID" => $mngAccountID]);
            $data = ["build" => $builds, "room" => $rooms, "useRoom" => $useRoom];
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    public function getChargePlan()
    {
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        \util\computed\setGAppData(["CommunityID" => $userId]);
    }

    // V4.6获取社区收费模式
    public function getChargeMode()
    {
        $params = [
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $manageData = $this->db->querySList("select ChargeMode from Account where ID = :ID", [":ID" => $userId])[0];
        $chargeMode = $manageData["ChargeMode"];
        $disChargeMode = $this->db->querySList("select ChargeMode from Account where ID = :ID", [":ID" => $manageData["ParentID"]])[0]["ChargeMode"];

        $chargeMode = intval($chargeMode) | intval($disChargeMode);
        \util\computed\setGAppData(["ChargeMode" => $chargeMode]);
    }

    public function setVisitor()
    {
        $params = [
            "userAliasId" => "",
            "IDCardVerification" => "",
            "FaceEnrollment" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $idCardVerification = $params["IDCardVerification"];
        $faceEnrollment = $params["FaceEnrollment"];
        $this->log->actionLog("#model#communityData#setVisitor#userId=$userId;idCardVerification=$idCardVerification;faceEnrollment=$faceEnrollment");
        $this->db->update2ListWKey("CommunityInfo", [":IDCardVerification" => $idCardVerification, ":FaceEnrollment" => $faceEnrollment, ":AccountID" => $userId], "AccountID");
        // updateCommunityAllPubDevNotify($this->communityID);
    }

    /**
     * @msg: 获取社区communityInfo数据
     */
    public function queryInfo()
    {
        $params = [
            // 社区ID
            "userAliasId" => "",
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        // V6.3 兼容社区和办公
        $projectData = $this->db->querySList('select Grade,UUID from Account where ID = :ID', [':ID' => $userId])[0];
        if ($projectData['Grade'] == COMMUNITYGRADE) {
            $data = $this->db->querySList(
                "select * from CommunityInfo where AccountID = :AccountID",
                [":AccountID" => $userId]
            )[0];
            $data["EnableSmartHome"] = \util\computed\getSpecifyBitLE($data["Switch"], 5);
        } elseif ($projectData['Grade'] == OFFICEGRADE) {
            $data = $this->db->querySList(
                "select * from OfficeInfo where AccountUUID = :AccountUUID",
                [":AccountUUID" => $projectData['UUID']]
            )[0];
        }
        $data['AccountID'] = $userId;
        $data["EnableLandline"] = intval($data["Switch"]) & 1;
        list($ableSmartHome) = $this->models['system']->getSmartHomeCnf();
        if ($ableSmartHome && $projectData['Grade'] == COMMUNITYGRADE) {
            $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
            $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
            if ($smartHomeSwitch == 1) {
                $smartHomeUUID = $this->db->querySList(
                    'select SmartHomeUUID from SmartHomeManageMap where Account=:Account',
                    [":Account" => $user]
                );
                $data["SmartHomeUUID"] = count($smartHomeUUID) == 0 ? '' : $smartHomeUUID[0]['SmartHomeUUID'];
            }
        }
        $featureItem = $this->db->querySList('select Item from FeaturePlan F join ManageFeature M on F.ID = M.FeatureID where M.AccountID = :AccountID', [':AccountID' => $userId])[0]['Item'];
        // 高级功能，第七位社区第三方设备
        $data['ThirdPartyDevices'] = \util\computed\getSpecifyBitLE($featureItem, 7);
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getRoomResident()
    {
        $params = [
            // 社区ID
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $builds = $this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]);
        $rooms = [];
        $allRooms = $this->db->querySList(
            "select R.* from CommunityRoom R join CommunityUnit U on R.UnitID = U.ID where U.MngAccountID=:MngAccountID",
            [':MngAccountID'=>$userId]
        );
        $allUsers = $this->db->querySList(
            "select ID,Name,RoomID,RoomNumber,Role,Special from PersonalAccount where ParentID=:MngAccountID and Role = 20 
            union select P.ID,P.Name,P2.RoomID,'' as RoomNumber,P.Role,0 from PersonalAccount P join PersonalAccount P2 on P2.ID = P.ParentID where P2.ParentID=:MngAccountID and P.Role = 21
            ",
            [':MngAccountID'=>$userId]
        );
        foreach ($allRooms as $val) {
            if (!array_key_exists($val['UnitID'], $rooms)) {
                $rooms[$val['UnitID']] = [];
            }
            $temp = $val;
            foreach ($allUsers as $val2) {
                if ($val2['Role'] == '20' && $val2['RoomID'] == $val['ID']) {
                    $temp["RoomNumber"] = $val2['RoomNumber'];
                }
            }
            array_push($rooms[$val['UnitID']], $temp);
        }

        $users = [];
        foreach ($allUsers as $val) {
            if ($val['Special'] == '0') {
                if (!array_key_exists($val['RoomID'], $users)) {
                    $users[$val['RoomID']] = [];
                }
                array_push($users[$val['RoomID']], ['ID' => $val['ID'], 'Name' => $val['Name']]);
            }
        }
        \util\computed\setGAppData(["data" => ["build" => $builds, "room" => $rooms, "user" => $users]]);
    }

    public function getUserAssociateDev()
    {
        $params = [
            // 社区房间ID
            "RoomID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["RoomID"];

        $userData = $this->db->queryAllList("PersonalAccount", ["equation" => [":RoomID" => $id]])[0];
        $unitName = $this->db->queryAllList("CommunityUnit", ["equation" => [":ID" => $userData["UnitID"]]])[0]["UnitName"];
        // 楼栋的用户只能开被管理的最外层公共设备
        $data = $this->db->querySList(
            "select ID,MAC,Status,Relay,SecurityRelay,Location,Type,'' as UnitName from Devices where Grade = 1 and MngAccountID = :MngAccountID and Type in (0,1,50)
        and (ID in (select DevicesID from PubDevMngList where UnitID = :UnitID) or Flags & 8 = 8)
        union all select ID,MAC,Status,Relay,SecurityRelay,Location,Type,'temp' as UnitName from Devices where Grade = 2 and UnitID = :UnitID and Type in (0,1,50)
        union all select ID,MAC,Status,Relay,SecurityRelay,Location,Type,'temp' as UnitName from Devices  where Grade = 3 and Node=:Node and Type in (0,1,50)",
            [":MngAccountID" => $userData["ParentID"], ":UnitID" => $userData["UnitID"], ":Node" => $userData["Account"]]
        );

        // 防止楼栋存在不合法字符
        foreach ($data as &$val) {
            if ($val["UnitName"] == 'temp') {
                $val["UnitName"] = $unitName;
            }
        }

        \util\computed\setGAppData(["data" => $data]);
    }

    public function getPMAllData()
    {
        $params = [
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $sqlMaster = "from PersonalAccount where ParentID = :ParentID and Role = 20 and Special = 0";
        $sqlMember = "from PersonalAccount P join PersonalAccount P1 on P.ParentID = P1.ID where P1.ParentID = :ParentID and P.Role = 21";

        // offline devicess
        $offlineDevices = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId, ":Status" => 0]]));
        // inactivated residents
        $total = $this->db->querySList("select count(*) $sqlMaster and Active = 0 union all select count(*) $sqlMember and P.Active = 0", [":ParentID" => $userId]);
        $inactivatedResidents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // expiring residents
        $expiringTime = date('Y-m-d H:i:s', strtotime('+7 day'));
        $nowTime = \util\computed\getNow();
        $total = $this->db->querySList("select count(*) $sqlMaster and Active = 1 and ExpireTime < :ExpiringTime and ExpireTime > :NowTime union all select count(*) $sqlMember and P.Active = 1 and P.ExpireTime < :ExpiringTime and P.ExpireTime > :NowTime", [":ParentID" => $userId,":ExpiringTime" => $expiringTime, ":NowTime" => $nowTime]);
        $expiringResidents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // expired residents
        $total = $this->db->querySList("select count(*) $sqlMaster and Active = 1 and ExpireTime < :ExpiringTime union all select count(*) $sqlMember and P.Active = 1 and P.ExpireTime < :ExpiringTime", [":ParentID" => $userId,":ExpiringTime" => $nowTime]);
        $expiredResidents = $total[0]['count(*)'] + $total[1]['count(*)'];

        // buidings
        $buildings = count($this->db->queryAllList("CommunityUnit", ["equation" => [":MngAccountID" => $userId]]));
        // apartments
        $apartments = count($this->db->querySList("select R.ID from CommunityRoom R join CommunityUnit U on R.UnitID = U.ID where U.MngAccountID = :MngAccountID", [":MngAccountID" => $userId]));
        // residents
        $total = $this->db->querySList("select count(*) $sqlMaster union all select count(*) $sqlMember", [":ParentID" => $userId]);
        $residents = $total[0]['count(*)'] + $total[1]['count(*)'];
        // devices
        $devices = count($this->db->queryAllList("Devices", ["equation" => [":MngAccountID" => $userId]]));

        // resident access
        $residentPin = count($this->db->querySList("select * from CommPerPrivateKey where CommunityID = :CommunityID and Code is not null", [":CommunityID" => $userId]));
        $residentRfCard = count($this->db->querySList("select * from CommPerRfKey where CommunityID = :CommunityID and Code is not null", [":CommunityID" => $userId]));
        $residentFace = count($this->db->queryAllList("FaceMng", ["equation" => [":MngAccountID" => $userId]]));
        $total = $this->db->querySList("select count(*) $sqlMaster and Initialization = 1 union all select count(*) $sqlMember and P.Initialization = 1", [":ParentID" => $userId]);
        $residentApp = $total[0]['count(*)'] + $total[1]['count(*)'];

        // tempkey access
        $tempKeys = count($this->db->querySList("select ID from PubAppTmpKey Pu where Pu.MngAccountID = :MngAccountID
        union all select ID from PersonalAppTmpKey Pe where Pe.MngAccountID = :MngAccountID", [":MngAccountID" => $userId]));

        // delivery access
        $deliveryPin = count($this->db->querySList("select * from Delivery where PinCode is not null and CommunityID = :CommunityID", [":CommunityID" => $userId]));
        $deliveryRfCard = count($this->db->querySList("select * from Delivery where CardCode is not null and CommunityID = :CommunityID", [":CommunityID" => $userId]));

        // staff access
        $staffRfCard = count($this->db->querySList("select * from Staff where CardCode is not null and CommunityID = :CommunityID", [":CommunityID" => $userId]));

        // today door
        list($pinCount, $rfCardCount, $faceCount, $callCount, $appCount) = $this->getTodayDoorForPM();

        // 30 day door
        list($pinMonthCount, $rfCardMonthCount, $faceMonthCount, $callMonthCount, $appMonthCount) = $this->getMonthDoorForPM();

        $data = ["Building" => $buildings, "Apt" => $apartments, "Device" => ["All" => $devices, "Offline" => $offlineDevices], "App" => ["Inactivated" => $inactivatedResidents, "Expiring" => $expiringResidents, "Expired" => $expiredResidents, "All" => $residents],
            "Key" => ["Resident" => [$residentPin, $residentRfCard, $residentFace, $residentApp], "TempKey" => [$tempKeys], "Delivery" => [$deliveryPin, $deliveryRfCard], "Staff" => [$staffRfCard]],
            "DoorLog" => ["Day" => [$pinCount, $rfCardCount, $faceCount, $callCount, $appCount], "Month" => [$pinMonthCount, $rfCardMonthCount, $faceMonthCount, $callMonthCount, $appMonthCount]]];
        \util\computed\setGAppData(["data" => $data]);
    }

    public function getTodayDoorForPM()
    {
        $params = [
            "SelfTimeZone" => "",
            "userAliasId" => ""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $userId = $params["userAliasId"];
        $now = \util\time\setTimeZone(\util\computed\getNow(), $timeZone, "", "+");
        $day = explode(" ", $now)[0];
        $startDay = $day . " 00:00:00";
        $baseSql = "select * from PersonalCapture where MngAccountID = :MngAccountID and Response = 0 and CaptureTime >= '" . \util\time\setTimeZone($startDay, $timeZone, "", "-") . "'";
        $bindArray = [":MngAccountID" => $userId];
        $captures = $this->db->querySList($baseSql, $bindArray);
        // $pinCount = count($this->db->querySList($baseSql . " and CaptureType = 2", $bindArray));
        // $rfCardCount = count($this->db->querySList($baseSql . " and CaptureType = 3", $bindArray));
        // $faceCount = count($this->db->querySList($baseSql . " and CaptureType = 4", $bindArray));
        // $callCount = count($this->db->querySList($baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))", $bindArray));
        // $appCount = count($this->db->querySList($baseSql . " and CaptureType = 5", $bindArray));
        // V6.4 kxl 一次查询，通过循环来统计条数
        $pinCount = 0;
        $rfCardCount = 0;
        $faceCount = 0;
        $callCount = 0;
        $appCount = 0;
        foreach ($captures as $capture) {
            $captureType = $capture['CaptureType'];
            if ($captureType === '2') {
                $pinCount ++;
            } elseif ($captureType === '3') {
                $rfCardCount ++;
            } elseif ($captureType === '4') {
                $faceCount++;
            } elseif ($captureType === '5') {
                $appCount++;
            } elseif ($captureType === '0' || ($captureType > 5 && $captureType < 9)) {
                $callCount++;
            }
        }

        return [$pinCount, $rfCardCount, $faceCount, $callCount, $appCount];
    }

    public function getMonthDoorForPM()
    {
        $params = [
            "SelfTimeZone" => "",
            "userAliasId" => ""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $userId = $params["userAliasId"];
        //时区计算
        $now = \util\computed\getNow();
        $now = \util\time\setTimeZone($now, $timeZone, "", "+");
        $today = explode(" ", $now)[0] . " 00:00:00";

        $startTime = date("Y-m-d H:i:s", strtotime("$today - 30 day"));
        $startTime = \util\time\setTimeZone($startTime, $timeZone, "", "-");

        $tables = $this->services["captureUtil"]->getCaptureTables(1);
        $currTables = [];
        foreach ($tables as $key => $table) {
            array_push($currTables, $table);
            if ($key >= 1) {
                break;
            }
        }
        $bindArray = [":MngAccountID" => $userId];
        $baseSql = "select ID,CaptureType from %s where MngAccountID = :MngAccountID and Response = 0 and CaptureTime >='" . $startTime . "'";
        // $pinSql = $baseSql . " and CaptureType = 2";
        // $rfCardSql = $baseSql . " and CaptureType = 3";
        // $faceSql = $baseSql . " and CaptureType = 4";
        // $callSql = $baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))";
        // $appSql = $baseSql . " and CaptureType = 5";

        // $pinCount = count($this->services["captureUtil"]->getCaptureResult($currTables, $pinSql, $bindArray));
        // $rfCardCount = count($this->services["captureUtil"]->getCaptureResult($currTables, $rfCardSql, $bindArray));
        // $faceCount = count($this->services["captureUtil"]->getCaptureResult($currTables, $faceSql, $bindArray));
        // $callCount = count($this->services["captureUtil"]->getCaptureResult($currTables, $callSql, $bindArray));
        // $appCount = count($this->services["captureUtil"]->getCaptureResult($currTables, $appSql, $bindArray));
        $pinCount = 0;
        $rfCardCount = 0;
        $faceCount = 0;
        $callCount = 0;
        $appCount = 0;
        $captures = $this->services["captureUtil"]->getCaptureResult($currTables, $baseSql, $bindArray);
        foreach ($captures as $capture) {
            $captureType = $capture['CaptureType'];
            if ($captureType === '2') {
                $pinCount ++;
            } elseif ($captureType === '3') {
                $rfCardCount ++;
            } elseif ($captureType === '4') {
                $faceCount++;
            } elseif ($captureType === '5') {
                $appCount++;
            } elseif ($captureType === '0' || ($captureType > 5 && $captureType < 9)) {
                $callCount++;
            }
        }

        return [$pinCount, $rfCardCount, $faceCount, $callCount, $appCount];
    }

    public function getLastFifteenDoorLog()
    {
        $params = [
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $offset = 0;
        $rows = 15;

        $sql = "select Initiator, CaptureTime, CaptureType, SPicUrl, PicUrl, MAC from %s A where A.MngAccountID = :MngAccountID and CaptureType <102 and Response = 0 order by ID desc";
        $bindArray = [":MngAccountID" => $userId];
        $tables = $this->services["captureUtil"]->getCaptureTablesInfo(1, $sql, $bindArray);
        list($count, $data) = $this->services["captureUtil"]->getCaptureSearchResult($tables, $sql, $bindArray, $offset, $rows);
        $types = CAPTURE_TYPE;
        // if (ABLE_SMART_HOME) {
        //     $switch = $this->db->querySList('select Switch from CommunityInfo where AccountID = :AccountID', [':AccountID' => $userId])[0]['Switch'];
        //     $smartHomeSwitch = \util\computed\getSpecifyBitLE($switch, COMMUNITY_SMART_HOME_SWITCH_POSITION);
        //     if ($smartHomeSwitch == 1) {
        //         $types = CAPTURE_TYPE_HOME;
        //     }
        // }
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $ip = \util\computed\getIp();;

        if (count(explode(".", $ip)) == 4) {
            $ipFix = IPV4IMG;
        } else {
            $ipFix = IPV6IMG;
        }

        if (count($data) != 0) {
            $macCondition = [];
            $macBindArray = [];
            foreach ($data as $key => $val) {
                array_push($macCondition, ":MAC$key");
                $macBindArray[":MAC$key"] = $val['MAC'];
            }

            $devices = $this->db->querySList(
                'select MAC, Location from Devices where MAC in ('.implode(',', $macCondition).')',
                $macBindArray
            );

            $locations = [];
            foreach ($devices as $device) {
                $locations[$device['MAC']] = $device['Location'];
            }
        }
        

        foreach ($data as &$val) {
            if ($val["Initiator"] == 'visitor' || $val["Initiator"] == 'Unknown') {
                $val["Initiator"] = MSGTEXT['visitor'];
            }
            $val["CaptureType"] = $types[$val["CaptureType"]];
            $pic = $val['PicUrl'];
            $spic = $val['SPicUrl'];
            $val['PicUrl'] = $this->models['capture']->setCaptureIp($val['CaptureTime'], $pic);
            $val['SPicUrl'] = $this->models['capture']->setCaptureIp($val['CaptureTime'], $pic, $spic);
            $val['Location'] = $locations[$val['MAC']];
        }
        \util\computed\setGAppData(["data" => $data]);
    }
}
