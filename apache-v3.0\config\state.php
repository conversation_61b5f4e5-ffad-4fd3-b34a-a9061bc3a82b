<?php
/*
 * @Description: 状态码定义
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-19 16:13:48
 * @LastEditors: cj
 */
// 成功状态
const StateSuccessAdd = 1;
const StateSuccessDelete = 2;
const StateSuccessEdit = 3;
const StateSuccessQuery = 4;
const StateSuccessChangePw = 5;
const StateSuccessRegister = 6;
const StatePasswordCorrect = 10;
const StatePasswdChange = 11;
const SateSuccessImport = 13;
const StateSetSuccess = 14;
const StateDealSuccess = 15;
const StateSuccessAddPw = 16;
const StateSuccessResetPw = 17;
const StateSuccessPay = 18;
const StateSuccessSendEmail = 19;
const StateSuccessSendCode = 20;//成功发送验证码
const StateCorrectCode = 21;//验证码正确
const StateSuccessGetUpgrade = 22;//成功获取升级内容
const StateSuccessUpdate = 23;//成功刷新计费模型

// 错误状态
const State404 = 0;
const State405 = 1;
const State500 = 2;
const StateEmailExits101 =101;
const StateInvaildDC102 = 102;
const StateInvalidIdentity = 110;
const StateDeviceLoginError = 1000;
const StateAccountIncorrect = 1001;
const StateAccountIncorrect2 = 1002;
const StateEmailOrAccountNotExit = 1004;
const StateEmailNotExits = 1005;
const StateInvalidPage = 1006;
const StateEmailExits = 1010;
const StateNotPermission = 1007;
const StateEmailExits1008 = 1008;
const StateSentCodeLater = 1009;
const StateEmailExits1010 = 1010;
const StateLimitIP = 1011;
const StateInvaildVerCode = 1012;
const StateInvaildDC1013 = 1013;
const StateIncorrectSipAccount = 1014;
const StateIncorrectSipAccountGroup = 1015;
const StateSipStatus = 1016;
const StateInvaildDC = 1020;
const StateLimitIP2 = 1021;
const StateUnKnowDT = 1017;
const StateDeviceNotFindUser = 1019;
const STATE_LIMIT_WITH_IP = 1030;
const StateMacExits = 2001;
const StateNameLong = 2003;
const StateLocationLong = 2004;
const StateUserNotExits = 2005;
const StateRoomNotExit = 2006;
const StateRFCardExit = 3001;
const StatePrivateKeyExists = 3002;
const StatePayFailed = 5003;
const StatePasswordIncorrect = 6003;
const StateAccountNotExit = 7002;
const StateAddTmpKeyFail = 8001;
const StateNoActiveWeb = 10010;
const StateAccountExpire = 10011;
const StateExpireLogin = 10011;
const StateMobileNumberNotExist = 10012;//新增手机号
const StateCodeIncorrect = 10013;//验证码错误
const StateMobileNumberEmpty = 10014;//手机号为空
const StateJCloudServer = 10015;
const StateRedirectedOnRPS = 20002;
const StateChcekMacExits = 20003;
const StateUnbindMACUser = 20004;
const StateInvalidFile = 20005;
const StateImportFailMACExit = 20006;
const StateMAC2Library = 20008;
const StateMACLength = 20009;
const STATE_ERROR_ON_RPS = 20016;
const StatePostalCodeInvalid = 20110;
const StateVoiceMsgInvalid = 20111;
//人脸导入错误信息
const StateFaceImportErrorSystem = 20029;
const StateFaceImportErrorView = 20030;
const StateFaceImportErrorWearMask = 20031;
const StateFaceImportErrorLowResolution = 20032;
const StateFaceImportErrorWrongFormat = 20033;
const StateFaceImportErrorNoFace = 20034;
const StateFaceImportErrorFileLarge = 20035;
const StateFaceImportErrorFaceLarge = 20036;
const StateFaceImportErrorFaceSmall = 20037;
const StateFaceImportErrorMultiFaces = 20038;
const StateFaceImportErrorWrongName = 20039;
const StateFaceImportErrorEmptyName = 20040;
const StateFaceImportErrorNoAccountInfo = 20041;
const StateFaceImportErrorAccountInactive = 20042;

const StateTimeLessCurrent = 30001;
const StateDeleteFail = 30002;
const StateAddFail = 30004;

const StateAccountExits = 40001;
const StateAccountNIncorrect = 40002;
const StateNoSip = 40004;
const StateBindUser = 40005;
const StateBindDevice = 40007;
const StateBindMAClibrary = 40006;
const StateUserBindUser = 40009;
const StateUserMaxPLimit = 40010;


const StateAptDuplicated = 50002;
const StateAptDigiAts = 50003;
const StateAptExit = 50004;
const StateEmailPExit = 50005;
const StateFirstNameEmpty = 50006;
const StateLastNameEmpty = 50007;
const StateInvalidPName = 50008;
const StateInvalidPEmail = 50009;
const StateEmailDuplicated = 50010;
const StateCalltypeEmpty = 50011;
const StateInvalidPCalltype = 50012;
const StateInvalidPPin = 50013;
const StateActiveEmpty = 50014;
const StateInvalidPActive = 50015;
const StateInvalidPDeviceType = 50016;
const StateDeviceTypeEmpty = 50017;
const StateLocationEmpty = 50018;
const StateLocationPLoog = 50019;
const StateBuildingExit = 50021;
const StateBuildingBindDevice = 50022;
const StateBuildingBindUser = 50023;

const StateErrorVersion = 60001;
const StateVersionExit = 60002;
const StateVersionNameNumberExit = 60003;
const StateVersionLogMaxLen = 60004;

const StateActiveFamilyAccount = 70001;
const StateAddOutApt = 70002;
const StateRFCardDuplicated = 70003;
const StateRFCardExit2 = 70004;
const StateNotMacBind = 70005;
const StateAccountNumLet = 70006;

const StateCantDeletePin = 70007;
const StateResidentInRoom = 70008;
const StateEndThanStart = 70009;
const StateEndThanStartFile = 70010;
const StateServerUpgradeTips = 70011;
const StateNotPermissionCode5 = 70012;
const StateNotImportFailed = 70013;
const StateMobileExits = 70014;
const StateNotMobile = 70015;
const StateMobileExits2 = 70016;
const StateMobileDuplicated = 70017;
const StateInvalidUser = 70018;
const StateLocationExits = 70019;

const StateRFCardDuplicatedLines = 70020;
const StateRFCardNameInvalid = 70021;
const StateRFCardExistLines = 70022;
const StateImportFailMACExistLines = 70023;

const StateExportExcelCountNull = 70024;

const StatePmKeyIsEqualRoom = 70025;

const StateCommunityNameExits = 70026;
const StateExportExcelDataBefore = 70027;
const StateAccessNameExist = 70028;
const StateAddFaceFail = 70029;

const StateUserInvalid = 70030;
const StateGroupInvalid = 70031;
const StateTimeInLineInvalid = 70032;
const StatePayOutstanding = 70033;
const StateIndoorMonitorRequired = 70034;
const StateFamilyMemberBeyond = 70035;
const StateIndoorMacNotCorrect = 70036;
const StateEnterValidAccount = 70037;
const StateInvalidKitImportMAC = 70038;
const StateImportLessData = 70039;
const StateQRCodeInValid = 70040;
const StateFamilyMemberCannotCreate = 70041;
const StateImportProcessing = 70042;
const StateDepartmentExit= 70043;
const StateInvalidLayout = 80000;
const StateDepartmentBindUser = 80001;
const StateDepartmentBindDevice = 80002;
const StateRelayInvalid = 80003;

const StatePinDuplicatedLines = 80100;
const StatePinExistLines = 80101;
const StatePinInvalidLines = 80102;
const StatePinAndRfcardNotNullLines = 80103;

const STATE_DELCODE_GET_LIMIT_TIMES = 90001; //注销接口错误次数达到最大值
const STATE_DELCODE_OVER_LIMIT_TIMES = 90002; //注销接口错误次数超过最大值
const STATE_DELCODE_ERROR = 90003; //注销接口验证码错误

const STATE_PARAMS_ERROR = 100001; //参数错误

const STATE_PARAMS_FOR_REST = 110000; //rest接口的错误码开头,用户对接第三方设备,web和h5调用的接口
