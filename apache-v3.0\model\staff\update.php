<?php
namespace model\staff;

trait update {
    function edit ($type) {
        $params = [
            "ID"=>"",
            "Name"=>"",
            "PinCode"=>"",
            "CardCode"=>"",
            "AccessGroup"=>"",
            "userAliasId"=>"",
            'Build' => '',
            'Floor' => ''
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityId = $params['userAliasId'];
        $id = $params["ID"];

        $pin = $params["PinCode"];
        $pin = $pin == "" ? null : $pin;
        $card = $params["CardCode"];
        $card = $card == "" ? null : $card;
        $builds = json_decode($params['Build'], true);
        $floors = json_decode($params['Floor'], true);

        checkAccessValid($params['AccessGroup'], $communityId);
        if(isDelivery($type)) {
            // 加密不判断
            if ($pin!=='****'){
                checkPinCardUnique($pin, $communityId, $type, 'PIN', $id);
            }
            checkPinCardUnique($card, $communityId, $type, 'Card', $id);
        } else {
            checkPinCardUnique($card, $communityId, $type, 'Card', $id);
        }

        // 查询出旧的group
        $oldGroup = getGroup($id, $type);

        update([
            "ID"=>$id,
            "Name"=>$params["Name"],
            "PinCode"=>$pin,
            "CardCode"=>$card,
            "AccessGroup"=>$params["AccessGroup"],
            'Build' => $builds,
            'Floor' => $floors
        ], $communityId, $type);

        \util\computed\setGAppData(["ID"=>[$id],"Type"=>$type,"AccessGroup"=>array_unique(array_merge($oldGroup, $params['AccessGroup']))]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $communityId])[0]['Account'];
        if ($pin!==null){
            $this->auditLog->setLog(AuditCodeEditPin, $this->env, [$pin], $account);
        }
        if ($card!==null){
            $this->auditLog->setLog(AuditCodeEditRf, $this->env, [$card], $account);
        }

    }

    function deliveryEdit () {
        $this->edit(0);
    }

    function staffEdit () {
        $this->edit(1);
    }
}