<?php
namespace model\pmKey\add;

const RESIDENT = 0;
const STAFF = 1;
// 6.3办公人员
const PEOPLE = 2;

namespace model\pmKey;

include_once __DIR__."/../../util/arr.php";
trait add
{
    public function addForPM()
    {
        $params = [
            "Name"=>"",
            "Key"=>"",
            "MAC"=>"",
            "AccountID"=>"",
            "DateFlag"=>"",
            "StartTime"=>"",
            "StopTime"=>"",
            "BeginTime"=>"",
            "EndTime"=>"",
            "SchedulerType"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        if ($this->owernType == 2) {
            $name = $params["AccountID"];
        }
        $key = $params["Key"];
        $macs = $params["MAC"];
        $userId = $params["userAliasId"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $schedulerType = $params["SchedulerType"];

        // $macs = explode(";", $macs);
        $this->addControl($userId, $name, $key, $macs, $dateFlag, $startTime, $stopTime, $beginTime, $endTime, $schedulerType);
    }

    public function addControl($userId, $name, $key, $macs, $dateFlag, $startTime, $stopTime, $beginTime, $endTime, $schedulerType)
    {
        global $cMessage;
        if ($this->deviceVailCheck($userId, $macs) === false) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        if ($schedulerType == 0) {
            if (strtotime($endTime) <= strtotime($beginTime)) {
                $cMessage->echoErrorMsg(StateEndThanStart);
            }
        } else {
            if (strtotime("2000-01-01 $stopTime") <= strtotime("2000-01-01 $startTime")) {
                $cMessage->echoErrorMsg(StateEndThanStart);
            }
        }


        if ($this->owernType == 2) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$name]])[0];
            if (in_array($userData["Role"], SUBROLE)) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userData["ParentID"]]])[0];
            }
            if ($this->userDeviceVailCheck($macs, $userData) === false) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            \util\computed\setGAppData(["Account"=>$userData["Account"]]);
        }

        if ($this->db->isExistFiled($this->tables[$this->type], [":Code"=>$key,":MngAccountID"=>$userId])
            || $this->db->isExistFiled($this->personalTables[$this->type], [":Code"=>$key,":MngAccountID"=>$userId])) {
            $cMessage->echoErrorMsg($this->type == 0 ? StatePrivateKeyExists : StateRFCardExit);
        }
        $now = \util\computed\getNow();

        $dateFlag = explode(";", $dateFlag);
        $tmpDate = 0;
        foreach ($dateFlag as $value) {
            $tmpDate += DATEFLAG[$value];
        }
        $dateFlag = $tmpDate;
        if ($dateFlag == 127 && $schedulerType == 2) {
            $schedulerType = 1;
        }

        if ($this->owernType == 2) {
            $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
            $this->db->insert2List(
                $this->personalTables[$this->type],
                [":MngAccountID"=>$userId,":CreateTime"=>$now,":AccountID"=>$name,":Code"=>$key,":Node"=>$userData["Account"],":UnitID"=>$userData["UnitID"],
                ":DateFlag"=>$dateFlag,":StartTime"=>$startTime,":StopTime"=>$stopTime,":BeginTime"=>$beginTime,":EndTime"=>$endTime,":SchedulerType"=>$schedulerType]
            );
        } else {
            $this->db->insert2List($this->tables[$this->type], [":MngAccountID"=>$userId,":CreateTime"=>$now,":OwnerType"=>$this->owernType,":Name"=>$name,":Code"=>$key,
            ":DateFlag"=>$dateFlag,":StartTime"=>$startTime,":StopTime"=>$stopTime,":BeginTime"=>$beginTime,":EndTime"=>$endTime,":SchedulerType"=>$schedulerType]);
        }

        $id = $this->db->lastInsertId();
        // foreach($macs as $val) $this->db->insert2List($this->listTables[$this->type],[":KeyID"=>$id,":MAC"=>$val]);
        $macArray = [];
        foreach ($macs as $val) {
            $relay = $val["Relay"];
            $relays = explode(";", $relay);
            $relay = 0;
            foreach ($relays as $value) {
                $relay += \util\computed\getRelayValue($value);
            }

            $securityRelay = $val["SecurityRelay"];
            $securityRelays = explode(";", $securityRelay);
            $securityRelay = 0;
            foreach ($securityRelays as $value) {
                $securityRelay += \util\computed\getRelayValue($value);
            }

            if ($this->owernType == 2) {
                $this->db->insert2List($this->personalListTables[$this->type], [":KeyID"=>$id,":Relay"=>$relay,":SecurityRelay"=>$securityRelay,":MAC"=>$val["MAC"]]);
            } else {
                $this->db->insert2List($this->listTables[$this->type], [":KeyID"=>$id,":Relay"=>$relay,":SecurityRelay"=>$securityRelay,":MAC"=>$val["MAC"]]);
            }
            array_push($macArray, $val["MAC"]);
        }
        \util\computed\setGAppData(["macs"=>implode(";", $macArray)]);

        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->auditLog->setLog($this->type == 0 ? AuditCodeAddPin : AuditCodeAddRf, $this->env, [$key], $account);
    }

    public function afterAdd()
    {
    }
    public function deviceVailCheck($userId, $macs)
    {
        foreach ($macs as $val) {
            if (count($this->db->queryAllList("Devices", ["equation"=>[":MAC"=>$val["MAC"],":MngAccountID"=>$userId]])) == 0) {
                return false;
            }
        }
        return true;
    }


    public function userDeviceVailCheck($macs, $userData)
    {
        $devices = $this->db->querySList("select MAC from Devices where Node = :Node and Grade = 3 union all 
        select MAC from Devices where UnitID = :UnitID and Grade = 2 union all 
        select MAC from Devices where MngAccountID = :MngAccountID and Grade = 1", [":Node"=>$userData["Account"],":UnitID"=>$userData["UnitID"],":MngAccountID"=>$userData["ParentID"]]);

        $userMAC = [];
        foreach ($devices as $val) {
            array_push($userMAC, $val["MAC"]);
        }

        foreach ($macs as $val) {
            if (!in_array($val["MAC"], $userMAC)) {
                return false;
            }
        }

        return true;
    }

    public function addAPKey()
    {
        $this->type = 0;
        $this->owernType = 1;
        $this->addForPM();
    }

    public function addARKey()
    {
        $this->type = 1;
        $this->owernType = 1;
        $this->addForPM();
    }

    public function addSPKey()
    {
        $this->type = 0;
        $this->owernType = 0;
        $this->addForPM();
    }

    public function addSRKey()
    {
        $this->type = 1;
        $this->owernType = 0;
        $this->addForPM();
    }

    public function addRPKey()
    {
        $this->type = 0;
        $this->owernType = 2;
        $this->addForPM();
    }

    public function addRRKey()
    {
        $this->type = 1;
        $this->owernType = 2;
        $this->addForPM();
    }

    public function import()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $file = $_FILES["file"];
        $fileName = $file["tmp_name"];
        require_once __DIR__.'/../../plugin/PHPExcel/PHPExcel/IOFactory.php';
        $objPHPExcel = \PHPExcel_IOFactory::load($fileName);

        //获取表格行数
        $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
        //获取表格列数
        $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();
        $header2Key = [
            "RF Card"=>"card",
            "User"=>"user",
            "Door"=>"device",
            "RepeatDay"=>"repeat",
            "StartDay"=>"startDay",
            "StartTime"=>"startTime",
            "EndDay"=>"endDay",
            "EndTime"=>"endTime"
        ];

        $dataArr = [];
        $keyColumn = [];

        $startRow = 1;
        $headerNull = 0;
        for ($row = $startRow; $row <= $rowCount; $row++) {
            if ($row != $startRow) {
                $data = ["row"=>$row];
            }

            // 列循环
            for ($column = 'A'; $column <= $columnCount; $column++) {
                // 获取单元格值
                $value = $objPHPExcel->getActiveSheet()->getCell($column.$row)->getValue();
                $value = $value === null ? "" : $value;
                // 获取第一行值
                if ($row == $startRow) {
                    $key = $header2Key[$value];
                    if (!$key) {
                        $headerNull ++;
                    }
                    $keyColumn[$column] = $key;
                } else {
                    $cellText = $value;
                    $key = $keyColumn[$column];
                    if (!$key) {
                        continue;
                    } elseif (($key == "card" && $value == "") || ($key == "user" && $value == "")) {
                        continue 2;
                    } elseif ($key == "startDay" || $key == "endDay") {
                        $value = date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($value));
                    } elseif ($key == "startTime" || $key == "endTime") {
                        $value = gmdate("H:i:s", \PHPExcel_Shared_Date::ExcelToPHP($value));
                    }
                    $data[$key] = $value;
                }
            }
            if ($row != $startRow) {
                array_push($dataArr, $data);
            }
        }
        if ($headerNull == count($keyColumn)) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }
        $this->log->actionLog('#model#pmkey#import#fileData='.json_encode($dataArr));
        // 卡号数据收集
        $cards = [];
        $datas = [];

        // 获取所有用户的邮箱
        $allEmail = [];
        $email2MainId = [];

        $allUser = $this->db->querySList("select Email,ID,UnitID,MobileNumber from PersonalAccount where Role = 20 and ParentID = :ParentID 
        union all select A.Email,B.ID,B.UnitID,A.MobileNumber from PersonalAccount A join PersonalAccount B on A.ParentID = B.ID where B.ParentID = :ParentID and A.Role = 21", [":ParentID"=>$userId]);
        foreach ($allUser as $val) {
            if ($val["Email"]) {
                array_push($allEmail, $val["Email"]);
                $email2MainId[$val["Email"]] = ["ID"=>$val["ID"],"UnitID"=>$val["UnitID"]];
            }
            if ($val["MobileNumber"]) {
                $email2MainId[$val["MobileNumber"]] = ["ID"=>$val["ID"],"UnitID"=>$val["UnitID"]];
                array_push($allEmail, $val["MobileNumber"]);
            }
        }
        // 获取所有用户和对应设备
        list($pubDev, $unitDev, $userDev) = $this->getAllUserDev($userId);
        foreach ($dataArr as $val) {
            if (in_array($val["card"], $cards)) {
                $cMessage->echoErrorMsg(StateRFCardDuplicated, [], [$val["card"]]);
            }
            array_push($cards, $val["card"]);

            // 邮箱是否在社区下
            if (!in_array($val["user"], $allEmail)) {
                $cMessage->echoErrorMsg(StateInvalidUser, [], [$val["user"]]);
            }

            $mainId = $email2MainId[$val["user"]]["ID"];
            $unitId = $email2MainId[$val["user"]]["UnitID"];

            // array_push($emails,$val["user"]);
            // 设备是否在用户名下
            $deviceList = explode(";", $val["device"]);
            $macs = [];
            foreach ($deviceList as $deviceItem) {
                if ($deviceItem == "") {
                    continue;
                }
                $device = explode(":", $deviceItem);
                $mac = $device[0];
                $relay = $device[1];

                if (!in_array($mac, $pubDev) && !in_array($mac, $unitDev[$unitId]) && !in_array($mac, $userDev[$mainId])) {
                    $cMessage->echoErrorMsg(StateNotMacBind, [], [$val["user"],$mac]);
                }

                $relays = explode(",", $relay);
                $relay = 0;
                // 获取原有mac的Relay
                $deviceRelay = $this->db->querySList('select Relay from Devices where MAC = :MAC', [':MAC' => $mac])[0]['Relay'];
                $deviceRelays = explode(';', $deviceRelay);
                foreach ($relays as $value) {
                    if (1 != explode(',', $deviceRelays[$value-1])[4]) {
                        $cMessage->echoErrorMsg(StateRelayInvalid, [], [$val["row"]]);
                    }
                    $relay += \util\computed\getRelayValue($value-1) ?: 0;
                }
                array_push($macs, ["MAC"=>$mac,"Relay"=>$relay]);
            }

            $dateFlag = $val["repeat"];

            $schedulerType = 0;
            $dateFlag = $dateFlag == "" ? [] : explode(",", $dateFlag);
            $tmpDate = 0;
            foreach ($dateFlag as $value) {
                // TODO 导入周几确定
                if (!in_array(intval($value), [1,2,3,4,5,6,7])) {
                    continue;
                }
                $tmpDate += pow(2, intval($value)-1);
            }

            $dateFlag = $tmpDate;
            if ($dateFlag != 0) {
                $schedulerType = 2;
            }
            if ($dateFlag == 127 && $schedulerType == 2) {
                $schedulerType = 1;
            }


            if ($schedulerType == 0) {
                if (strtotime($val["endDay"]." ".$val["endTime"]) <= strtotime($val["startDay"]." ".$val["startTime"])) {
                    $cMessage->echoErrorMsg(StateTimeInLineInvalid, [], [$val["row"]]);
                }
            } else {
                if (strtotime("2000-01-01 ".$val["endTime"]) <= strtotime("2000-01-01 ".$val["startTime"])) {
                    $cMessage->echoErrorMsg(StateTimeInLineInvalid, [], [$val["row"]]);
                }
            }


            array_push($datas, ["card"=>$val["card"],"devices"=>$macs,"user"=>$val["user"],"schedulerType"=>$schedulerType,"dateFlag"=>$dateFlag,"unitID"=>$unitId,
            "startTime"=>$val["startTime"],"stopTime"=>$val["endTime"],"beginTime"=>$val["startDay"]." ".$val["startTime"],"endTime"=>$val["endDay"]." ".$val["endTime"]]);
        }
        // 卡是否存在
        $cardExits = $this->db->querySList("select Code from PersonalRfcardKey where MngAccountID = :MngAccountID and Code in ('".implode("','", $cards)."')", [":MngAccountID"=>$userId]);
        $cardExist2 = $this->db->querySList("select Code from PubRfcardKey where MngAccountID = :MngAccountID and Code in ('".implode("','", $cards)."')", [":MngAccountID"=>$userId]);
        if (count($cardExits) != 0 || count($cardExist2) !== 0) {
            $resultCard = [];
            foreach ($cardExits as $val) {
                array_push($resultCard, $val["Code"]);
            }
            foreach ($cardExist2 as $val) {
                array_push($resultCard, $val["Code"]);
            }
            $cMessage->echoErrorMsg(StateRFCardExit2, [], [implode(",", $resultCard)]);
        }


        $communityData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
        $now = \util\computed\getNow();
        $times = 0;
        $nodes = [];
        $this->db->begin();
        foreach ($datas as $val) {
            // 每一百条提交一次事务
            if ($times == 100) {
                $times = 0;
                $this->db->commit();
                $this->db->begin();
            }
            $userData = $this->db->querySList("select * from  PersonalAccount where Email = :Email or MobileNumber = :Email", [":Email"=>$val["user"]])[0];
            $node = $userData["Account"];
            if ($userData["Role"] == PERENDSROLE || $userData["Role"] == COMENDSROLE) {
                $node = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userData["ParentID"]]])[0]["Account"];
            }
            array_push($nodes, $node);
            $accountId = $userData["ID"];
            $this->db->insert2List("PersonalRfcardKey", [":MngAccountID"=>$userId,":CreateTime"=>$now,":AccountID"=>$accountId,":Code"=>$val["card"],":Node"=>$node,":UnitID"=>$val["unitID"],
            ":DateFlag"=>$val["dateFlag"],":StartTime"=>$val["startTime"],":StopTime"=>$val["stopTime"],":BeginTime"=>$val["beginTime"],":EndTime"=>$val["endTime"],":SchedulerType"=>$val["schedulerType"]]);
            $id = $this->db->lastInsertId();
            foreach ($val["devices"] as $device) {
                $this->db->insert2List("PersonalRfcardKeyList", [":KeyID"=>$id,":Relay"=>$device["Relay"],":MAC"=>$device["MAC"]]);
            }
            $this->auditLog->setLog(AuditCodeAddRf, $this->env, [$val["card"]], $node);
        }
        $this->db->commit();
        // communityUpdateAllDevNotify($userId);
        \util\computed\setGAppData(["Account"=>$node]);
    }

    public function importsr()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $file = $_FILES["file"];
        $fileName = $file["tmp_name"];
        require_once __DIR__.'/../../plugin/PHPExcel/PHPExcel/IOFactory.php';
        $objPHPExcel = \PHPExcel_IOFactory::load($fileName);

        //获取表格行数
        $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
        //获取表格列数
        $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();
        $header2Key = [
            "RF Card"=>"card",
            "RF Card Name"=>"rfname",
            "Door"=>"device",
            "RepeatDay"=>"repeat",
            "StartDay"=>"startDay",
            "StartTime"=>"startTime",
            "EndDay"=>"endDay",
            "EndTime"=>"endTime"
        ];

        $dataArr = [];
        $keyColumn = [];

        $startRow = 1;
        $headerNull = 0;
        for ($row = $startRow; $row <= $rowCount; $row++) {
            if ($row != $startRow) {
                $data = ["row"=>$row];
            }

            // 列循环
            for ($column = 'A'; $column <= $columnCount; $column++) {
                // 获取单元格值
                $value = $objPHPExcel->getActiveSheet()->getCell($column.$row)->getValue();
                $value = $value === null ? "" : $value;
                // 获取第一行值
                if ($row == $startRow) {
                    $key = $header2Key[$value];
                    // 无效列表头
                    if (!$key) {
                        $headerNull ++;
                    }
                    $keyColumn[$column] = $key;
                } else {
                    $key = $keyColumn[$column];
                    if (!$key) {
                        continue;
                    } elseif ($key == "card" && $value == "") {
                        continue 2;
                    } elseif ($key == "startDay" || $key == "endDay") {
                        $value = date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($value));
                    } elseif ($key == "startTime" || $key == "endTime") {
                        $value = gmdate("H:i:s", \PHPExcel_Shared_Date::ExcelToPHP($value));
                    }
                    $data[$key] = $value;
                }
            }
            if ($row != $startRow) {
                array_push($dataArr, $data);
            }
        }
        if ($headerNull == count($keyColumn)) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }

        // 卡号数据收集
        $cards = [];
        $datas = [];
        $cardDup = [];
        $rfnameInvalid = [];
        $cardEx = [];
        $macEx = [];
        $timeInvalid = [];


        // 获取公共设备
        list($pubDev, $unitDev) = $this->getAllUserDev($userId);

        foreach ($dataArr as $val) {
            //卡号名称为空
            if ($val["rfname"] == "") {
                array_push($rfnameInvalid, $val["row"]);
            }

            //卡号重复行数
            if (in_array($val["card"], $cards)) {
                array_push($cardDup, $val["row"]);
            } else {
                array_push($cards, $val["card"]);
            }
        }

        //卡号存在
        $cardExist = $this->db->querySList("select Code from PersonalRfcardKey where MngAccountID = :MngAccountID and Code in ('".implode("','", $cards)."')", [":MngAccountID"=>$userId]);
        $cardExist1 = $this->db->querySList("select Code from PubRfcardKey where MngAccountID = :MngAccountID and Code in ('".implode("','", $cards)."')", [":MngAccountID"=>$userId]);

        foreach ($dataArr as $val) {
            //重复天数
            $dateFlag = $val["repeat"];
            $schedulerType = 0;
            $dateFlag = $dateFlag == "" ? [] : explode(",", $dateFlag);
            $tmpDate = 0;
            foreach ($dateFlag as $value) {
                // TODO 导入周几确定
                if (!in_array(intval($value), [1,2,3,4,5,6,7])) {
                    continue;
                }
                $tmpDate += pow(2, intval($value)-1);
            }

            $dateFlag = $tmpDate;
            if ($dateFlag != 0) {
                $schedulerType = 2;
            }
            if ($dateFlag == 127 && $schedulerType == 2) {
                $schedulerType = 1;
            }

            if ($schedulerType == 0) {
                if (strtotime($val["endDay"]." ".$val["endTime"]) <= strtotime($val["startDay"]." ".$val["startTime"])) {
                    array_push($timeInvalid, $val["row"]);
                }
            } else {
                if (strtotime("2000-01-01 ".$val["endTime"]) <= strtotime("2000-01-01 ".$val["startTime"])) {
                    array_push($timeInvalid, $val["row"]);
                }
            }

            //卡号存在
            foreach ($cardExist as $val1) {
                if ($val["card"] == $val1["Code"]) {
                    array_push($cardEx, $val["row"]);
                    continue 2;
                }
            }
            foreach ($cardExist1 as $val2) {
                if ($val["card"] == $val2["Code"]) {
                    array_push($cardEx, $val["row"]);
                    continue 2;
                }
            }
        }
        foreach ($dataArr as $val) {
            // 设备是否在用户名下
            $deviceList = explode(";", $val["device"]);
            $macs = [];
            foreach ($deviceList as $deviceItem) {
                if ($deviceItem == "") {
                    continue;
                }
                $device = explode(":", $deviceItem);
                $mac = $device[0];
                $relay = $device[1];

                if (!in_array($mac, $pubDev) && !(\util\arr\deep_in_array($mac, $unitDev, 3))) {
                    array_push($macEx, $val["row"]);
                    continue 2;
                }
                $relays = explode(",", $relay);
                $relay = 0;
                // 获取原有mac的Relay
                $deviceRelay = $this->db->querySList('select Relay from Devices where MAC = :MAC', [':MAC' => $mac])[0]['Relay'];
                $deviceRelays = explode(';', $deviceRelay);
                foreach ($relays as $value) {
                    if (1 != explode(',', $deviceRelays[$value-1])[4]) {
                        $cMessage->echoErrorMsg(StateRelayInvalid, [], [$val["row"]]);
                    }
                    $relay += \util\computed\getRelayValue($value-1) ?: 0;
                }
                array_push($macs, ["MAC"=>$mac,"Relay"=>$relay]);
            }
        }

        $cMessage->obStart();

        if (count($cardDup) != 0) {
            $cMessage->echoErrorMsg(StateRFCardDuplicatedLines, [], [implode(",", $cardDup)]);
        }
        if (count($cardEx) != 0) {
            $cMessage->echoErrorMsg(StateRFCardExistLines, [], [implode(",", $cardEx)]);
        }
        if (count($rfnameInvalid) != 0) {
            $cMessage->echoErrorMsg(StateRFCardNameInvalid, [], [implode(",", $rfnameInvalid)]);
        }
        if (count($macEx) != 0) {
            $cMessage->echoErrorMsg(StateImportFailMACExistLines, [], [implode(",", $macEx)]);
        }
        if (count($timeInvalid) != 0) {
            $cMessage->echoErrorMsg(StateEndThanStartFile, [], [implode(",", $timeInvalid)]);
        }
        $cMessage->obEnd();

        //如果都没有错误
        foreach ($dataArr as $val) {
            // 设备是否在用户名下
            $deviceList = explode(";", $val["device"]);
            $macs = [];
            foreach ($deviceList as $deviceItem) {
                if ($deviceItem == "") {
                    continue;
                }
                $device = explode(":", $deviceItem);
                $mac = $device[0];
                $relay = $device[1];
                $relays = explode(",", $relay);
                $relay = 0;
                foreach ($relays as $value) {
                    $relay += \util\computed\getRelayValue($value-1) ?: 0;
                }
                array_push($macs, ["MAC"=>$mac,"Relay"=>$relay]);
            }

            //重复天数
            $dateFlag = $val["repeat"];
            $schedulerType = 0;
            $dateFlag = $dateFlag == "" ? [] : explode(",", $dateFlag);
            $tmpDate = 0;
            $mon = 1;
            $tue = 2;
            $wed = 3;
            $thu = 4;
            $fri = 5;
            $sat = 6;
            $sun = 7;
            foreach ($dateFlag as $value) {
                // TODO 导入周几确定
                if (!in_array(intval($value), [$mon,$tue,$wed,$thu,$fri,$sat,$sun])) {
                    continue;
                }
                $tmpDate += pow(2, intval($value)-1);
            }

            $dateFlag = $tmpDate;
            if ($dateFlag != 0) {
                $schedulerType = 2;
            }
            if ($dateFlag == 127 && $schedulerType == 2) {
                $schedulerType = 1;
            }

            array_push($datas, ["card"=>$val["card"],"devices"=>$macs,"rfname"=>$val["rfname"],"schedulerType"=>$schedulerType,"dateFlag"=>$dateFlag,
            "startTime"=>$val["startTime"],"stopTime"=>$val["endTime"],"beginTime"=>$val["startDay"]." ".$val["startTime"],"endTime"=>$val["endDay"]." ".$val["endTime"]]);
        }

        $now = \util\computed\getNow();
        $times = 0;
        $owernType = 0;
        $this->db->begin();
        foreach ($datas as $val) {
            // 每一百条提交一次事务
            if ($times == 100) {
                $times = 0;
                $this->db->commit();
                $this->db->begin();
            }

            $this->db->insert2List("PubRfcardKey", [":MngAccountID"=>$userId,":CreateTime"=>$now,":OwnerType"=>$owernType,":Name"=>$val["rfname"],":Code"=>$val["card"],
            ":DateFlag"=>$val["dateFlag"],":StartTime"=>$val["startTime"],":StopTime"=>$val["stopTime"],":BeginTime"=>$val["beginTime"],":EndTime"=>$val["endTime"],":SchedulerType"=>$val["schedulerType"]]);

            $id = $this->db->lastInsertId();
            foreach ($val["devices"] as $device) {
                $this->db->insert2List("PubRfcardKeyList", [":KeyID"=>$id,":Relay"=>$device["Relay"],":MAC"=>$device["MAC"]]);
            }
            $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
            $this->auditLog->setLog(AuditCodeAddRf, $this->env, [$val["card"]], $account);
        }
        $this->db->commit();
        $tempMac = [];
        foreach ($macs as $mac) {
            array_push($tempMac, $mac['MAC']);
        }
        \util\computed\setGAppData(["macs"=>implode(";", $tempMac)]);
    }

    public function getAllUserDev($communityID)
    {
        $data = $this->db->querySList("select MAC,ID,Flags from Devices where Grade = 1 and MngAccountID = :MngAccountID", [":MngAccountID"=>$communityID]);
        $pubDev = [];
        $unitDev = [];
        foreach ($data as $val) {
            if (intval($val['Flags']) & 8) {
                array_push($pubDev, $val["MAC"]);
                continue;
            }

            $units = $this->db->querySList('select UnitID from PubDevMngList where DevicesID = :DevicesID', [":DevicesID"=>$val["ID"]]);
            foreach ($units as $unit) {
                if (!array_key_exists($unit["UnitID"], $unitDev)) {
                    $unitDev[$unit["UnitID"]] = [];
                }
                array_push($unitDev[$unit["UnitID"]], $val["MAC"]);
            }
        }
        $data = $this->db->querySList("select MAC,UnitID from Devices where Grade = 2 and MngAccountID = :MngAccountID", [":MngAccountID"=>$communityID]);
        foreach ($data as $val) {
            if (!array_key_exists($val["UnitID"], $unitDev)) {
                $unitDev[$val["UnitID"]] = [];
            }
            array_push($unitDev[$val["UnitID"]], $val["MAC"]);
        }

        // 获取所有房间设备
        $userDev = [];
        $data = $this->db->querySList("select A.ID,D.MAC from PersonalAccount A left join Devices D on A.Account = D.Node where A.Role = 20 and A.ParentID = :ParentID", [":ParentID"=>$communityID]);

        foreach ($data as $val) {
            if (!array_key_exists($val["ID"], $userDev)) {
                $userDev[$val["ID"]] = [];
            }
            array_push($userDev[$val["ID"]], $val["MAC"]);
        }

        return [$pubDev,$unitDev,$userDev];
    }

    public function importResRfCard()
    {
        global $cMessage;
        // 6.3区别办公和社区的RfCard导入
        // 6.5区别社区RfCard和pin的导入，办公RfCard导入
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $projectGarde = $this->db->querySList('select Grade from Account where ID = :ID', ['ID' => $userId])[0]['Grade'];
        if ($projectGarde == COMMUNITYGRADE) {
            //新旧社区判断
            $isNew = $this->db->querySList(
                'select IsNew from CommunityInfo where AccountID =:AccountID',
                [':AccountID' => $userId]
            )[0]['IsNew'];
            if($isNew === '0'){
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $this->type = add\RESIDENT;
            $this->importPmRfCardAndPin();
        } elseif ($projectGarde == OFFICEGRADE) {
            $this->type = add\PEOPLE;
            $this->importPmRfCard();
        }
    }

    public function importStaffCard()
    {
        $this->type = add\STAFF;
        $this->importPmRfCard();
    }

    public function importPmRfCard()
    {
        global $cMessage;
        $params = [
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];

        $file = $_FILES["file"];
        $fileName = $file["tmp_name"];
        if ($this->type == add\RESIDENT) {
            $headerKey = [
                "RF Card"=>"card",
                "User"=>"user"
            ];
        } elseif ($this->type == add\STAFF) {
            $headerKey = [
                "RF Card"=>"card",
                "Staff Name"=>"user",
                "Access Group ID"=>"group"
            ];
        } elseif ($this->type == add\PEOPLE) {
            // 6.3导入办公的RFCard模板修改
            $headerKey = [
                "People"=>"user",
                "RF Card"=>"card"
            ];
        }
        // 保存除标题外表格每行内容
        $dataArr = $this->getExcelData($headerKey, $fileName);

        $rfCards = [];    // 收集本excel所有卡号
        $rdCardsNull = [];   // 卡号为空
        $rfCardsDuplicate = [];  // excel内部卡号重复
        $rfCardsNotUnique = [];    // 卡号唯一性
        $userNotExist = [];    // 用户存在
        $groupNotExist = [];    // 权限组是否存在

        foreach ($dataArr as $val) {
            //卡号为空行数
            if ($val['card'] == null) {
                array_push($rdCardsNull, $val['row']);
            }
            //卡号重复行数
            if (in_array($val['card'], $rfCards)) {
                array_push($rfCardsDuplicate, $val["row"]);
            } else {
                array_push($rfCards, $val["card"]);
            }
            
            // 卡号唯一性
            if (\model\staff\checkPinCardUnique($val['card'], $userId, 2, 'RFCard', null, true)) {
                array_push($rfCardsNotUnique, $val["row"]);
            }
            if ($this->type == add\RESIDENT) {
                // 用户是否存在
                $resMaster = $this->db->querySList("select * from PersonalAccount where Role = 20 and ParentID = :UserId and (Email = :User or MobileNumber = :User)", [":User"=>$val['user'], ":UserId"=>$userId]);
                $resMember = $this->db->querySList("select * from PersonalAccount A join PersonalAccount B on A.ParentID = B.ID where A.Role = 21 and B.ParentID = :UserId and (A.Email = :User or A.MobileNumber = :User)", [":UserId"=>$userId, ":User"=>$val['user']]);
                if (!$resMaster && !$resMember) {
                    array_push($userNotExist, $val['row']);
                }
            } elseif ($this->type == add\STAFF) {
                // 检查权限组是否存在
                $groups = explode(";", $val['group']);
                if ($val['group'] != '' && end($groups) == '') {
                    array_pop($groups);
                }
                foreach ($groups as $groupVal) {
                    $res = $this->db->querySList("select * from AccessGroup where CommunityID = :CommunityID and ID = :ID", [":CommunityID"=>$userId, ":ID"=>$groupVal]);
                    $this->log->actionLog("#model#pmKey#add#checkAccessGroup=".json_encode($res));
                    if (!$res) {
                        array_push($groupNotExist, $val['row']);
                        continue 2;
                    }
                }
            } elseif ($this->type == add\PEOPLE) {
                // 办公用户是否存在
                $resOfficePeople = $this->db->querySList("select * from PersonalAccount A join PersonalAccountOfficeInfo I on A.UUID = I.PersonalAccountUUID 
                where A.Role in (30,31) and I.EmployeeID = :User and A.ParentID = :UserId", [":User"=>$val['user'], ":UserId"=>$userId]);
                if (!$resOfficePeople) {
                    array_push($userNotExist, $val['row']);
                }
            }
        }
        $cMessage->obStart();
        if (count($rdCardsNull) != 0) {
            $cMessage->echoErrorMsg(StateRFCardNameInvalid, [], [implode(",", $rdCardsNull)]);
        }
        if (count($rfCardsDuplicate) != 0) {
            $cMessage->echoErrorMsg(StateRFCardDuplicatedLines, [], [implode(",", $rfCardsDuplicate)]);
        }
        if (count($rfCardsNotUnique) != 0) {
            $cMessage->echoErrorMsg(StateRFCardExistLines, [], [implode(",", $rfCardsNotUnique)]);
        }
        if (($this->type == add\RESIDENT || $this->type == add\PEOPLE) && count($userNotExist) != 0) {
            $cMessage->echoErrorMsg(StateUserInvalid, [], [implode(",", $userNotExist)]);
        }
        if ($this->type == add\STAFF && count($groupNotExist) != 0) {
            $cMessage->echoErrorMsg(StateGroupInvalid, [], [implode(",", $groupNotExist)]);
        }
        $cMessage->obEnd();

        if (count($dataArr) > 1000) {
            $cMessage->echoErrorMsg(StateImportLessData, [], [1000]);
        }

        $now = \util\computed\getNow();
        $times = 0;
        $accounts = [];
        $accessGroupsId = [];
        $staffIds = [];
        $addCode = [];
        $this->db->begin();
        foreach ($dataArr as $val) {
            // 每一百条提交一次事务
            if ($times == 100) {
                $times = 0;
                $this->db->commit();
                $this->db->begin();
            }
            if ($this->type == add\RESIDENT) {
                $account = $this->db->querySList("select Account from PersonalAccount where Email = :User or MobileNumber = :User", [":User"=>$val['user']])[0]['Account'];
                if (!in_array($account, $accounts)) {
                    array_push($accounts, $account);
                }
                $this->db->insert2List("CommPerRfKey", [":Account"=>$account, ":Code"=>$val['card'], ":CommunityID"=>$userId, ":CreateTime"=>$now]);
                array_push($addCode, $val['card']);
            } elseif ($this->type == add\STAFF) {
                $this->db->insert2List("Staff", [":Name"=>$val['user'], ":CardCode"=>$val['card'], ":CommunityID"=>$userId, ":CreateTime"=>$now, ':UUID' => \util\string\uuid()]);
                array_push($addCode, $val['card']);
                $staffId = $this->db->lastInsertId();
                if (!in_array($staffId, $staffIds)) {
                    array_push($staffIds, $staffId);
                }
                $groups = explode(";", $val['group']);
                if ($val['group'] != '' && end($groups) == '') {
                    array_pop($groups);
                }
                foreach ($groups as $groupVal) {
                    if (!in_array($groupVal, $accessGroupsId)) {
                        array_push($accessGroupsId, $groupVal);
                    }
                    $this->db->insert2List("StaffAccess", [":StaffID"=>$staffId, ":AccessGroupID"=>$groupVal]);
                }
            } elseif ($this->type == add\PEOPLE) {
                $account = $this->db->querySList("select Account from PersonalAccount A join PersonalAccountOfficeInfo I on A.UUID = I.PersonalAccountUUID 
                where I.EmployeeID = :User and A.ParentID = :UserId", [":User"=>$val['user'], ":UserId"=>$userId])[0]['Account'];
                if (!in_array($account, $accounts)) {
                    array_push($accounts, $account);
                }
                $this->db->insert2List("CommPerRfKey", [":Account"=>$account, ":Code"=>$val['card'], ":CommunityID"=>$userId, ":CreateTime"=>$now]);
                array_push($addCode, $val['card']);
            }
        }
        $this->db->commit();
        $user = $this->db->querySList("select Account from Account where ID=:ID", [":ID"=>$userId])[0]['Account'];
        $this->log->actionLog("#model#pmKey#add#addCode=".json_encode($addCode));
        foreach ($addCode as $val) {
            $this->auditLog->setLog(AuditCodeAddRf, $this->env, [$val], $user);
        }
        \util\computed\setGAppData(["communityId"=>$userId, "Account"=>$accounts, "staffIds"=>$staffIds, "deliveryIds"=>[], "accessGroupsId"=>$accessGroupsId]);
    }


    /**
     * @description: 获取excel的数据
     * @author:lwj 2022/7/28 9:12
     * @lastEditor: lwj 2022/7/28 9:12
     * @param array headerKey 首行
     * @param string fileName 文件名称
     * @return array
     */
    public function getExcelData($headerKey, $fileName)
    {
        global $cMessage;
        require_once __DIR__ . '/../../plugin/PHPExcel/PHPExcel/IOFactory.php';
        $objPHPExcel = \PHPExcel_IOFactory::load($fileName);

        //获取表格行数
        $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
        //获取表格列数
        $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();

        $dataArr = []; // 保存除标题外表格每行内容
        $keyColumn = [];
        $startRow = 1;
        $headerNull = 0;
        for ($row = $startRow; $row <= $rowCount; $row++) {
            if ($row != $startRow) {
                $data = ['row' => $row];
            }
            // 列循环
            for ($column = 'A'; $column <= $columnCount; $column++) {
                // 获取单元格值
                $value = $objPHPExcel->getActiveSheet()->getCell($column . $row)->getValue();
                $value = $value === null ? '' : $value;
                // 获取第一行值
                if ($row == $startRow) {
                    $key = $headerKey[$value];
                    // 无效列表头
                    if (!$key) {
                        $headerNull++;
                    }
                    $keyColumn[$column] = $key;
                } else {
                    $key = $keyColumn[$column];
                    if (!$key) {
                        continue;
                    } elseif ($key == 'user' && $value == '') {
                        continue 2;
                    }
                    $data[$key] = $value;
                }
            }
            if ($row != $startRow) {
                array_push($dataArr, $data);
            }
        }
        if ($headerNull == count($keyColumn)) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }
        return $dataArr;
    }

    /**
     * @description: pm导入社区的Rfcard和Pin
     * @author:lwj 2022/7/28 9:12
     * @lastEditor: lwj 2022/7/28 9:12
     */
    public function importPmRfCardAndPin()
    {
        global $cMessage;
        $params = ['userAliasId' => ''];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params['userAliasId'];

        $file = $_FILES['file'];
        $fileName = $file['tmp_name'];
        $headerKey = [
            'RF Card' => 'card',
            'User' => 'user',
            'PIN' => 'pin'
        ];
        // 保存除标题外表格每行内容
        $dataArr = $this->getExcelData($headerKey, $fileName);

        if (count($dataArr) > 1000) {
            $cMessage->echoErrorMsg(StateImportLessData, [], [1000]);
        }

        $rfCards = [];    // 收集本excel所有卡号
        $rfCardsDuplicate = [];  // excel内部卡号重复
        $rfCardsNotUnique = [];    // 卡号唯一性
        $rfCardsInvalid = []; //rfcard不符合规则
        $rfCardsRule = '/^\+?[0-9A-F]*$/';//纯数字
        $userNotExist = [];    // 用户存在
        $pinAndRfcardNotNull = []; //收集excel pin和rfcard都为空
        $pin = []; //收集excel pin
        $pinDuplicate = []; //pin内部重复
        $pinNotUnique = []; //pin唯一性
        $pinInvalid = []; //pin不符合规则
        $pinRule = '/^\d{2,8}$/';//2-8位的纯数字

        foreach ($dataArr as $val) {
            //Rfcard和pin至少要有一个填写
            if (($val['card'] === '' || $val['card'] === null) && ($val['pin'] === '' || $val['pin'] === null)) {
                array_push($pinAndRfcardNotNull, $val['row']);
            }

            //卡号为空行数
            if ($val['card'] !== '' && $val['card'] !== null) {
                //卡号重复行数
                if (in_array($val['card'], $rfCards)) {
                    array_push($rfCardsDuplicate, $val["row"]);
                } else {
                    array_push($rfCards, $val["card"]);
                }
                //纯数字
                if (!preg_match($rfCardsRule, $val['card'])) {
                    array_push($rfCardsInvalid, $val["row"]);
                }
                // 卡号唯一性
                if (\model\staff\checkPinCardUnique($val['card'], $userId, 2, 'RFCard', null, true)) {
                    array_push($rfCardsNotUnique, $val["row"]);
                }
            }

            //pin为空
            if ($val['pin'] !== '' && $val['pin'] !== null) {
                //pin重复行数
                if (in_array($val['pin'], $pin)) {
                    array_push($pinDuplicate, $val["row"]);
                } else {
                    array_push($pin, $val["pin"]);
                }
                // PIN唯一性
                if (\model\staff\checkPinCardUnique($val['pin'], $userId, 2, 'PIN', null, true)) {
                    array_push($pinNotUnique, $val["row"]);
                }
                // pin2-8位的纯数字
                if (!preg_match($pinRule, $val['pin'])) {
                    array_push($pinInvalid, $val["row"]);
                }
            }
            // 用户是否存在
            $resMaster = $this->db->querySList(
                "select count(*) as total from PersonalAccount where Role = 20 and ParentID = :UserId and (Email = :User or MobileNumber = :User)",
                [":User" => $val['user'], ":UserId" => $userId]
            )[0]['total'];
            $resMember = $this->db->querySList(
                "select count(*) as total  from PersonalAccount A join PersonalAccount B on A.ParentID = B.ID 
                where A.Role = 21 and B.ParentID = :UserId and (A.Email = :User or A.MobileNumber = :User)",
                [":UserId" => $userId, ":User" => $val['user']]
            )[0]['total'];
            if (!$resMaster && !$resMember) {
                array_push($userNotExist, $val['row']);
            }

        }
        $cMessage->obStart();
        if (count($pinAndRfcardNotNull) !== 0) {
            $cMessage->echoErrorMsg(StatePinAndRfcardNotNullLines, [], [implode(',', $pinAndRfcardNotNull)]);
        }
        if (count($rfCardsDuplicate) !== 0) {
            $cMessage->echoErrorMsg(StateRFCardDuplicatedLines, [], [implode(',', $rfCardsDuplicate)]);
        }
        if (count($rfCardsNotUnique) !== 0) {
            $cMessage->echoErrorMsg(StateRFCardExistLines, [], [implode(',', $rfCardsNotUnique)]);
        }
        if (count($rfCardsInvalid) !== 0) {
            $cMessage->echoErrorMsg(StateRFCardNameInvalid, [], [implode(',', $rfCardsInvalid)]);
        }


        if (count($pinDuplicate) != 0) {
            $cMessage->echoErrorMsg(StatePinDuplicatedLines, [], [implode(',', $pinDuplicate)]);
        }
        if (count($pinNotUnique) !== 0) {
            $cMessage->echoErrorMsg(StatePinExistLines, [], [implode(',', $pinNotUnique)]);
        }
        if (count($pinInvalid) !== 0) {
            $cMessage->echoErrorMsg(StatePinInvalidLines, [], [implode(',', $pinInvalid)]);
        }

        if (count($userNotExist) !== 0) {
            $cMessage->echoErrorMsg(StateUserInvalid, [], [implode(',', $userNotExist)]);
        }
        //返回错误信息
        $cMessage->obEnd(State405, true);

        $now = \util\computed\getNow();
        $times = 0;
        $accounts = [];
        $addCode = [];
        $this->db->begin();
        foreach ($dataArr as $val) {
            // 每一百条提交一次事务
            if ($times === 100) {
                $times = 0;
                $this->db->commit();
                $this->db->begin();
            }

            $account = $this->db->querySList(
                'select Account from PersonalAccount where Email = :User or MobileNumber = :User',
                [':User' => $val['user']]
            )[0]['Account'];
            if (!in_array($account, $accounts)) {
                array_push($accounts, $account);
            }
            if (!empty($val['card'])) {
                //插入rfcard
                $this->db->insert2List('CommPerRfKey', [':Account' => $account, ':Code' => $val['card'], ':CommunityID' => $userId, ':CreateTime' => $now]);
                array_push($addCode, ['type' => 'Rfcard', 'val' => $val['card']]);
            }

            if (!empty($val['pin'])) {
                //插入pin
                $this->db->insert2List('CommPerPrivateKey', [':Account' => $account, ':Code' => $val['pin'], ':CommunityID' => $userId, ':CreateTime' => $now]);
                array_push($addCode, ['type' => 'pin', 'val' => $val['pin']]);
            }
            $times++;
        }
        $this->db->commit();
        $user = $this->db->querySList('select Account from Account where ID=:ID', [':ID' => $userId])[0]['Account'];
        $this->log->actionLog('#model#pmKey#add#addCode=' . json_encode($addCode));
        foreach ($addCode as $val) {
            $auditCode = $val['type'] === 'Rfcard' ? AuditCodeAddRf : AuditCodeAddPin;
            $this->auditLog->setLog($auditCode, $this->env, [$val['val']], $user);
        }
        \util\computed\setGAppData(['communityId' => $userId, 'Account' => $accounts]);
    }
}
