<?php
namespace model\devicePersonal;

trait query
{
    /**
     * @name: app查询设备列表
     */
    public function queryPerForApp()
    {
        $params = [
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->querySList("select ID,Location,SipAccount,RtspPwd,MAC,Type,Status from PersonalDevices where Node = :Node", [":Node"=>$user]);
        \util\computed\setGAppData(["data"=>$data]);
    }
    /**
     * @name: app查询设备列表（社区）
     */
    public function queryComForApp()
    {
        $params = [
            "userAlias"=>"",
            "Public"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $publicd = $params["Public"];
        $data = $this->db->querySList("select ID,Location,SipAccount,MAC,Type,RtspPwd,Status,ArmingFunction from Devices where Node = :Node", [":Node"=>$user]);
        $pub = [];
        if ($publicd == 1) {
            $myData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$user]])[0];
            $mngID = $myData["ParentID"];
            $buildId = $myData["UnitID"];
            $pub = $this->db->querySList("select ID,Location,SipAccount,MAC,RtspPwd,Type,Status,ArmingFunction from Devices where MngAccountID = :MngAccountID and (Grade = 1 or (Grade = 2 and UnitID = :UnitID))", [":MngAccountID"=>$mngID,":UnitID"=>$buildId]);
        }
        $data = array_merge($pub, $data);
        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @name: app公共查询设备列表
     * @param userAlias：主账户
     */
    public function queryPerPubForApp()
    {
        $params = [
            // userAlias：
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $perMng = $this->db->querySList("select A.Account from Account A join PersonalAccount P on A.ID = P.ParentID where P.Account = :Account", [":Account"=>$user])[0]["Account"];
        $data = $this->db->querySList("select ID,Location,SipAccount,MAC,RtspPwd,Type,Status from PersonalDevices where Community = :Community AND Flag = 1", [":Community"=>$perMng]);
        \util\computed\setGAppData(["data"=>$data]);
    }

    /**
     * @name: app公共查询设备列表
     * @param userAlias：主账户
     */
    public function queryComPubForApp()
    {
        $params = [
            // 主账户
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $myData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":Account"=>$user]])[0];
        $unitID = $myData["UnitID"];
        $comMngId = $myData["ParentID"];
        
        $data = $this->db->querySList("select ID,Location,SipAccount,MAC,RtspPwd,Type,Status from Devices where (MngAccountID = $comMngId AND Grade = 1) OR (MngAccountID = $comMngId AND UnitID = $unitID AND Grade = 2)");
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function queryForWeb()
    {
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "MAC"=>"",
            "Location"=>"",
            "Type"=>"",
            "userAliasId"=>"",
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $mac = $params["MAC"];
        $location = $params["Location"];
        $type = $params["Type"];
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID"=>$userId])[0]["Role"];

        $userType = in_array($role, PERROLE) ? 0 : 1;
        $deviceTabel = $userType == 0 ? "PersonalDevices" : "Devices";
        $where = '';
        $bindArray = [":Node"=>$user,":MAC"=>"%$mac%",":Location"=>"%$location%"];
        if ($type !== "" && $type !== null) {
            $where = "AND Type = :Type";
            $bindArray[":Type"] = $type;
        }
            
        $total = $this->db->querySList("select count(*) as total from $deviceTabel where Node = :Node AND MAC like :MAC AND Location like :Location $where", $bindArray)[0]['total'];
        $data = $this->db->querySList("select * from $deviceTabel where Node = :Node AND MAC like :MAC AND Location like :Location $where order by ID desc limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm, ["ExpireTime"]);
        $row = [];
        foreach ($data as $value) {
            $tem = [];
            $tem['ID'] = $value['ID'];
            $tem['MAC'] = $value['MAC'];
            $tem['Location'] = $value['Location'];
            $tem['SipAccount'] = $value['SipAccount'];
            $tem['Status'] = $value['Status'];
            $tem['Device Type'] = $value['Type'];
            $tem['Binding Time'] = $value['LastConnection'];
            $tem['ExpireTime'] = $value['ExpireTime'] == null ?
            ["type"=>0,"content"=>null] : (strtotime($value['ExpireTime']) >= time() ?
            ["type"=>0,"content"=>\util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)] :
            ["type"=>1,"content"=>\util\time\setTimeZone($value['ExpireTime'], $timeZone, $customizeForm)]);
            array_push($row, $tem);
        }

        \util\computed\setGAppData(["data"=>["detail"=>$data,"row"=>$row,"total"=>$total]]);
    }

    /**
     * @name: 管理员查询个人设备列表
     * @param
     */
    public function queryForMng()
    {
        list($offset, $rows, $searchKey, $searchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
            "userAliasId"=>"",
            "userAlias"=>"",
            "User"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userId = $params["userAliasId"];
        $user = $params["userAlias"];
        $person = $params["User"];

        $grade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID"=>$userId])[0]["Grade"];
        $where = '';
        $bindArray = [];
        switch ($searchKey) {
            case 'MAC':
                $where .= "AND D.MAC like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Location':
                $where .= "AND D.Location like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Node':
                $where = "AND D.Node like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Sip':
                $where = "AND D.SipAccount like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Install':
                $where = "AND A2.Account like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Manage':
                $where = "AND C.Account like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
            case 'Owner':
                $where = "AND A.Name like :searchValue";
                $bindArray[":searchValue"] = "%$searchValue%";
                break;
        }

        if ($grade == SUPERGRADE) {
            $sql = "select count(D.ID) as total from PersonalDevices D left join PersonalAccount A on D.Node = A.Account join Account B on B.ID = A.ParentID join Account A2 on B.ManageGroup = A2.ID join Account C on C.ID = B.ParentID where A.ParentID like '%%' $where";
        } elseif ($grade == AREAGRADE) {
            $sql = "select count(D.ID) as total from PersonalDevices D left join PersonalAccount A on D.Node = A.Account left join Account B on A.ParentID = B.ID join Account A2 on B.ManageGroup = A2.ID where B.ParentID = :ParentID $where";
            $bindArray[":ParentID"] = $userId;
        } else {
            $sql = "select count(D.ID) as total from PersonalDevices D left join PersonalAccount A on D.Node = A.Account where A.ParentID = :ParentID and D.Flag = 0 and D.Node like :Person $where";
            $bindArray[":ParentID"] = $userId;
            $bindArray[":Person"] = "%$person%";
        }

        $data = array();
        $data['total'] = $this->db->querySList($sql, $bindArray)[0]["total"];


        if ($grade == SUPERGRADE) {
            $sql = "select C.Account as Manage,A.Name,A.Account,D.*,A2.Account as Installer
            from PersonalDevices D 
            join PersonalAccount A on A.Account = D.Node
            join Account B on B.ID = A.ParentID
            join Account C on C.ID = B.ParentID
            join Account A2 on B.ManageGroup = A2.ID
            where A.ParentID like'%%' $where order by D.ID desc limit $offset,$rows";
        } elseif ($grade == AREAGRADE) {
            $sql = "select A.Name,A.Account,D.*,A2.Account as Installer
            from PersonalDevices D 
            left join PersonalAccount A on A.Account = D.Node
            join Account B on A.ParentID = B.ID 
            join Account A2 on B.ManageGroup = A2.ID
            where B.ParentID = :ParentID $where order by D.ID desc limit $offset,$rows";
            $bindArray[":ParentID"] = $userId;
        } else {
            $sql = "select D.ID,D.SipType,D.Relay,D.SecurityRelay,D.Location,D.MAC,D.LastConnection,D.Config,D.Type,D.Status,D.CreateTime,A.Name,D.Node,A.Account,D.Firmware,D.Hardware,D.IPAddress,D.SubnetMask,D.Gateway,D.PrimaryDNS,D.SecondaryDNS,D.SipAccount,A2.Account as Installer,'0' as Special
            from PersonalDevices D left join PersonalAccount A on A.Account = D.Node
            join Account B on A.ParentID = B.ID join Account A2 on B.ManageGroup = A2.ID 
            where A.ParentID = :ParentID and D.Flag = 0 and D.Node like :Person $where order by D.ID desc limit $offset,$rows";
            $bindArray[":ParentID"] = $userId;
            $bindArray[":Person"] = "%$person%";
        }


        $results = $this->db->querySList($sql, $bindArray);

        $results = \util\time\setQueryTimeZone($results, $timeZone, $customizeForm);
        
        foreach ($results as &$value) {
            // if($value["ExpireTime"] != DEFAULTEXPIRETIME) $value["ExpireTime"] = self::setTimeZone($value['ExpireTime']);
            // $value["LastConnection"] = $value['LastConnection']?self::setTimeZone($value['LastConnection']):$value['LastConnection'];
            $value["SipType"] = ["UDP","TCP","TLS"][$value["SipType"]];
            unset($value["PrivatekeyMD5"]);
            unset($value["RfidMD5"]);
            unset($value["ConfigMD5"]);
            unset($value["ContactMD5"]);
            unset($value["SipPwd"]);
            unset($value["RtspPwd"]);
            unset($value["outerIP"]);
            unset($value["AuthCode"]);
            // 查询个人设备下是不是特殊设备（6.2新增）
            if ($grade == PERSONGRADE) {
                $special = $this->db->querySList("select D.ID from DevicesSpecial D join PersonalAccount P on D.Account = P.Account 
                where P.Account = :Account and D.MAC = :MAC", [":Account"=>$value['Account'], ":MAC"=>$value['MAC']]);
                if ($special) {
                    $value['Special'] = '1';
                }
            }
        }
        $data["detail"] = $results;
        $data["row"] = [];

        $nowTime = time();
        foreach ($results as $row) {
            $curDevice = array();
            if ($grade == SUPERGRADE) {
                $curDevice['Manage'] = $row['Manage'];
            }
            $curDevice['ID'] = $row['ID'];
            $curDevice['MAC'] = $row['MAC'];
            $curDevice['UID'] = $row['Account'];
            $curDevice['Install'] = $row['Installer'];
            $curDevice['SipAccount'] = $row['SipAccount'];
            $curDevice['Owner'] = $row['Name']?$row['Name']:'--';
            $curDevice['Location'] = $row['Location'];

            $curDevice['ExpireTime'] = ($row['ExpireTime'] == null || $row['ExpireTime'] == DEFAULTEXPIRETIME)?
                ["type"=>0,"content"=>$row['ExpireTime']] :
                (strtotime($row['ExpireTime']) >= $nowTime ?
                ["type"=>0,"content"=>$row['ExpireTime']] :
                ["type"=>1,"content"=>$row['ExpireTime']]);
                                    
            $curDevice['Status'] = $row['Status'];
            $curDevice['Device Type'] = $row['Type'];
            $curDevice['Binding Time'] = $row['LastConnection']?:'--';
            array_push($data['row'], $curDevice);
        }
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function getAccountDevice()
    {
        $params = [
            "ID"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$id]])[0];
        if (in_array($userData["Role"], SUBROLE)) {
            $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$userData["ParentID"]]])[0];
        }
    
        $deviceTable = in_array($userData["Role"], PERROLE) ? "PersonalDevices" : "Devices";
        $data = $this->db->queryAllList($deviceTable, ["equation"=>[":Node"=>$userData["Account"]]]);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function queryForUpgrade()
    {
        $params = [
            "Key"=>"",
            "UnSelect"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $key = $params["Key"];
        $unSelect = $params["UnSelect"];
        $userId = $params["userAliasId"];

        $model = explode('.', $unSelect);
        $model = $model[0].'.';
        $unSelect = explode('_', $unSelect)[0];
        $myData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$userId]])[0];
        $grade = $myData["Grade"];
        if ($grade == PERSONGRADE) {
            $sql = "select D.ID,D.MAC,D.Location,D.Firmware,D.Status,A.Name from PersonalDevices D left join PersonalAccount A on A.Account = D.Node where (D.MAC like :MAC OR A.Name like :Node OR D.Location like :Location) AND D.Firmware != :Firmware AND D.Firmware like :Model AND Status = 1 AND A.ParentID = :ParentID";
        } else {
            $sql = "select D.ID,D.MAC,D.Location,D.Firmware,D.Status,A.Name from Devices D left join PersonalAccount A on A.Account = D.Node where (D.MAC like :MAC OR A.Name like :Node OR D.Location like :Location) AND D.Firmware != :Firmware AND D.Firmware like :Model AND D.Status = 1 AND D.MngAccountID = :ParentID";
        }
        
        $data = $this->db->querySList($sql, [":Firmware"=>$unSelect,":ParentID"=>$userId,":Model"=>"%$model%",":MAC"=>"%$key%",":Node"=>"%$key%",":Location"=>"%$key%"]);
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function queryDevWithType()
    {
        $params = [
            "Type"=>"",
            "userAlias"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $user = $params["userAlias"];

        $deviceTabel = "PersonalDevices";
        $data = $this->queryAllDevWithType($type, $deviceTabel, $user);
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function queryAllDevWithType($type, $deviceTabel, $user)
    {
        if ($type == "0") {
            $sql = "select Location,SipAccount,ID,MAC,Type,Status from $deviceTabel where Node = :Node AND (Type = 0 or Type = 1 or Type = 4)";
        } elseif ($type == "1") {
            $sql = "select Location,SipAccount,ID,MAC,Type,Status from $deviceTabel where Node = :Node AND (Type = 0 or Type = 1)";
        } elseif ($type == "2") {
            $sql = "select Location,SipAccount,ID,MAC,Type,Status from $deviceTabel where Node = :Node AND Type = 2";
        } else {
            $sql = "select Location,SipAccount,ID,MAC,Type,Status from $deviceTabel where Node = :Node";
        }

        $data = $this->db->querySList($sql, [":Node"=>$user]);
        if ($type == 1) {
            for ($i=0;$i<count($data);$i++) {
                if (!$data[$i]['Location']) {
                    $data[$i]['Location'] = $data[$i]['MAC'];
                }
            }
        }
        return $data;
    }
}
