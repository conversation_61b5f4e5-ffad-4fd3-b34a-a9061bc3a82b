<?php
namespace model\notify;

trait pay
{
    public function pay()
    {
        $params = [
            "OrderID"=>"",
            "type"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $orderID = $params["OrderID"];
        $type = $params["type"];
        $this->log->actionLog("#model#notify#pay#orderID=$orderID");

        $type2 = $type == 4 ? 5 : $type;
        // 子订单type不一样
        $users = $this->db->queryAllList("OrderEndUserList", ["equation"=>[":OrderID"=>$orderID,":Type"=>$type2]]);
        
        if ($type == 2) {
            $userids = [];
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                array_push($userids, $userData[0]["Account"]);
            }
            \RenewServer($userids);
        } elseif ($type == 4) {
            $userSips = [];
            $userids = [];
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                array_push($userSips, ["sip"=>$userData[0]["Account"],"enableGroup"=>0]);
                array_push($userids, $userData[0]["Account"]);
                \webPersonalModifyNotify(WEB_PER_PHONE_PAY_SUCC, $userData[0]["Account"]);
            }
            $this->services["sip"]->addMul2Freeswish($userSips);
            \RenewServer($userids, 1);
        } else {
            foreach ($users as $user) {
                $userData = $this->db->queryAllList("PersonalAccount", ["equation"=>[":ID"=>$user["AppID"]]]);
                \accountActiveEmailNotify(1, $userData[0]["Name"], $userData[0]["Email"]);
            }
        }
    }

    // public function changeCommunityCharge()
    // {
    //     $params = [
    //         "Account"=>"",
    //         "MonthlyFee"=>""
    //     ];
    //     $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
    //     $account = $params["Account"];
    //     $monthlyFee = $params["MonthlyFee"];
    //     $this->log->actionLog("#model#notify#changeCommunityCharge#account=$account;monthlyFee=$monthlyFee");
    //     // 只有0元才通知变更
    //     if ($monthlyFee == 0) {
    //         \UpdateCommMonthlyFee($account);
    //     }
    // }
}
