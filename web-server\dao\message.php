<?php

namespace dao;
use framework\BasicDao;

class Message extends BasicDao
{
    //当前表名
    public $table = 'Message';

    //需要数据混淆的字段
    public $confusionField = [];

    //主键
    protected $primaryKey = 'ID';


    
    public function __construct()
    {
        parent::__construct($this->table);
    }
    
    /**
     * @description: 插入数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function insert(array $data = [])
    {
        return parent::insert($data);
    }

    /**
     * @description: 通用根据某个字段更新数据方法
     * @param array $data 例 ['ID' => 1, 'Account' => 'sisen']
     * @param string $key 更新根据的字段，默认为ID
     * @return mixed
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function update(array $data, $key = 'ID')
    {
        return parent::update($data, $key);
    }

    /**
     * @description: 通用根据某个字段删除数据方法
     * @param {string} $val 字段值
     * @param {string} $key 字段名，默认为ID
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function delete($val, $key = 'ID')
    {
        parent::delete($val, $key);
    }

    /**
     * @description: 根据指定字段和值搜索数据
     * @param {string} $key 字段名
     * @param {*} $val 字段值
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByKey($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKey($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description:根据指定字段和值（数组）搜索数据
     * @param {string} $key 字段名
     * @param {array} $val 字段值 使用wherein条件拼接字段
     * @param {string} $fields 查询的字段
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByKeyWArray($key, $val, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByKeyWArray($key, $val, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 根据多个条件查询
     * @param [array] $array 查询的参数数组，例如 [["ID", 1], ["ManageGroup", 0, "!="], ["Account", "sisen", "%"], ["Email", ["email1", "email2"]], ["Email", ["email3", "email4"], "not in"]]
     * 以上array意思为 ID = 1 and ManageGroup != 0 and Account like "%sisen%" and Email in ("email1", "email2") and Email not in ("email3", "email4");
     * @param {string} $fields 查询的字段 不填默认为全部
     * @param {bool} $debugSql 为true时只返回sql不进行查询
     * @return array|string
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByArray($array, $fields = '*', $debugSql = false, $autoDecode = true)
    {
        return parent::selectByArray($array, $fields, $debugSql, $autoDecode);
    }

    /**
     * @description: 获取最后执行的sql
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function getLastSql()
    {
        return parent::getLastSql();
    }

    /**
     * @description: order排序
     * @param {string} $orderby order的条件，例如： ID ASC
     * @return $this
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function orderBy($orderby = '') {
        return parent::orderBy($orderby);
    }

    /**
     * @description: limit限制
     * @param {string} $limit limit的条件， 例如 10 或者 10,20
     * @return $this
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function limit($limit = '') {
        return parent::limit($limit);
    }
    
    /**
     * @description: 根据ID的值查询对应数据
     * @param {string} $id ID的值
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByID($id, $fields = '*')
    {
        return $this->selectByKey('ID', $id, $fields);
    }

    /**
     * @description: 根据ID的值查询对应数据
     * @param {array} $ids ID的值(数组)
     * @param {string} $fields 待查询的字段，不填默认为全部字段
     * @return void
     * @throws \Exception
     * @author: systemCreator 2023/03/21 16:38 V6.5.4
     * @lastEditors: systemCreator 2023/03/21 16:38 V6.5.4
     */
    public function selectByIDWArray($ids, $fields = '*')
    {
        return $this->selectByKeyWArray('ID', $ids, $fields);
    }

    /**
     * @description:获取消息列表（包括语音消息）
     * @author:lwj 2023-04-13 19:32:49 V6.6
     * @lastEditor:lwj 2023-04-13 19:32:49 V6.6
     * @param {array} $bindArray 查询条件数组['CreateTime' => '创建时间', 'Account' => 'User Account', 'PersonalAccountUUID' => 'User UUID']
     * @param {array} $searchArray 分页、模糊查询条件
     * @return array
     * @throws \Exception
     */
    public function getListForApp($bindArray, $searchArray)
    {
        list($offset, $rows) = $searchArray;
        $account = $bindArray['Account'];
        $uuids = $bindArray['PersonalAccountUUID'];

        list($accountString, $accountArray) = $this->share->util->getImplodeData($account, 'Account');
        list($uuidString, $uuidArray) = $this->share->util->getImplodeData($uuids, 'UUID');
        if (empty($accountString) or empty($uuidString)) {
            return [];
        }

        //messageSql
        $messageTable = $this->table;
        $messageAccountListTable = $this->dao->messageAccountList->table;
        $whereArray = [
            ':ClientType' => MESSAGE_CLIENT_TYPE['app'],
            ':CreateTime' => $bindArray['CreateTime'],
        ];
        $whereArray = array_merge($whereArray, $accountArray, $uuidArray);
        $messageSql = "select L.ID, M.Title, M.Content, M.CreateTime, M.Type, M.ExtensionField, L.Status, 0 as `NoticeType`, '' as Location, L.Account as User
            from $messageTable M
            left join $messageAccountListTable L on M.ID = L.MessageID
            where M.CreateTime >= :CreateTime and L.ClientType =:ClientType and L.Account in (".$accountString.")";

        //语音Sql
        $personalVoiceMsgTable = $this->dao->personalVoiceMsg->table;
        $personalVoiceMsgListTable = $this->dao->personalVoiceMsgList->table;
        $voiceSql = "select L.ID,'' as Title,'' as Content, M.CreateTime, '' as Type, '' as ExtensionField, L.Status, 1 as `NoticeType`, M.Location, L.PersonalAccountUUID as User 
            from $personalVoiceMsgTable M 
            left join $personalVoiceMsgListTable L on M.UUID = L.MsgUUID
            where M.CreateTime >= :CreateTime and L.PersonalAccountUUID in (".$uuidString.")";

        $list = $this->execute(
            "$messageSql union all $voiceSql order by CreateTime desc limit $offset,$rows",
            $whereArray
        );

        return $list;
    }
    /**
     * @description:消息列表
     * @author:lwj 2023-04-20 18:38:35 V6.6
     * @lastEditor:lwj 2023-04-20 18:38:35 V6.6
     * @param {array} $bindArray [AccountID=> '项目ID']
     * @param {array} $searchArray 分页、模糊数组
     * @return array
     * @throws \Exception
     */
    public function getListForMng($bindArray, $searchArray)
    {
        list($offset, $rows, $searchKey, $searchValue) = $searchArray;
        $whereArray = [
            ':AccountID' => $bindArray['AccountID'],
        ];
        $where = ' AccountID = :AccountID ';

        if (isset($bindArray['DisableMessgeType'])) {
            $where .= ' AND Type != :Type';
            $whereArray[':Type'] = $bindArray['DisableMessgeType'];
        }

        switch ($searchKey){
            case 'Content':
            case 'Message':
                $where.= ' AND Content like :SearchValue';
                $whereArray[':SearchValue'] = "%$searchValue%";
                break;
            case 'Receiver':
                $where.= ' AND NickNames like :SearchValue';
                $whereArray[':SearchValue'] = "%$searchValue%";
                break;
            default:
                break;
        }
        $messageTable = $this->table;
        $sql = "select count(*) from $messageTable  where $where";
        if (isset($bindArray['Account'])){
            $sql = "select count(*) from (
                select * from $messageTable  where $where
                union all
                select M.* from Message M left JOIN MessageAccountList MA on M.ID=MA.MessageID where MA.Account=:PmAccount
                ) as total";
            $whereArray[':PmAccount'] = $bindArray['Account'];
        }
        $total = $this->execute($sql, $whereArray)[0]['count(*)'];

        $sql = "select * from $messageTable where $where ";
        if (isset($bindArray['Account'])){
            $sql.= "union all select M.* from Message M left JOIN MessageAccountList MA on M.ID=MA.MessageID where MA.Account=:PmAccount ";
            $whereArray[':PmAccount'] = $bindArray['Account'];
        }
        $sql .="order by ID desc limit $offset, $rows";
        $data = $this->execute($sql, $whereArray);

        return [$total, $data];
    }
}