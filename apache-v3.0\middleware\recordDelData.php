<?php
/*
 * @Description: 记录重要的被删除的数据,并将数据发送至数据流,方便后续出问题查看log
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-17 15:51:12
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
class CRecordDelData implements IMiddleware {
    public $id;
    public $table;
    public function handle (\Closure $next) {
        global $cLog;
        $table = $this->table;
        $id = $this->id;
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#recordDelData#id=$id;table=$table");
        $data = $db->queryAllList($table,["equation"=>[":ID"=>$id]])[0];
        $cLog->actionLog("#middle#recordDelData#".json_encode($data));
        \util\computed\setGAppData($data);
        $next();
    }
}