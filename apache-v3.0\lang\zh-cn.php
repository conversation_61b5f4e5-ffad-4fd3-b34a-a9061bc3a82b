<?php
  const MSGTEXT = [ 

"accountExits"=>"账号已存在",
"accountNotExit"=>"账号不存在",
"accountIncorrect"=>"无效的用户名或密码",
"accountNIncorrect"=>"无效的账号",
"activeEmpty"=>"请输入激活值",
"addFail"=>"添加失败",
"addSuccess"=>"添加成功",
"addSuccessPw"=>"添加成功，密码是'%s'",
"addTmpKeyFail"=>"添加临时密码失败，请重新尝试",
"aptDuplicated"=>"房间%s重复",
"aptDigits"=>"房间%s无效，请使用1-6的数字命名",
"aptExit"=>"房间%s已存在",
"abnormal"=>"异常",
"activation"=>"激活",
"additionalApp"=>"额外的小睿账号",
"bindDevice"=>"请删除此账号下的所有设备",
"bindMAClibrary"=>"请删除MAC库的MAC地址",
"bindUser"=>"请删除此账号下的所有用户",
"buildingBindDevice"=>"请删除这栋楼的所有设备",
"buildingBindUser"=>"请删除这栋楼的所有用户",
"buildingDigits"=>"楼栋%s无效，请使用1-2的数字命名",
"buildingExit"=>"楼栋已存在",
"BindingDeviceFailed"=>"绑定设备失败，此设备可能已经绑定在其它用户下或还未添加到MAC库",
"chcekMacExits"=>"添加失败，MAC地址无效或已经存在",
"changePasswdFail"=>"密码修改失败",
"changePasswdPEmail"=>"密码修改成功，请到邮箱%s查收",
"community"=>"社区",
"deleteFail"=>"删除失败",
"deleteSuccess"=>"删除成功",
"deviceTypeEmpty"=>"请输入设备类型",
"deviceNotFindUser"=>"未发现设备，请联您的管理人员",
"dealSuccess"=>"设置成功",
"doorUnit"=>"门口机",
"emailExits"=>"邮箱已存在",
"emailPExit"=>"邮箱%s已存在",
"emailNotExits"=>"邮箱不存在",
"emailDuplicated"=>"邮箱%s重复使用",
"errorVersion"=>"版本错误",
"emailOrAccountNotExit"=>"邮箱不存在",
"firstNameEmpty"=>"请输入姓",
"failed"=>"失败",
"family"=>"家庭",
"guardPhone"=>"管理机",
"incorrectSipAccount"=>"没有更多可用的SIP帐户",
"incorrectSipAccountGroup"=>"没有更多可用的SIP群组",
"importDataSuccess"=>"导入成功",
"importFailMACExit"=>"导入失败，请检查此MAC地址是否是有效值：\r\n%s",
"invaildDC"=>"设备码无效",
"InvalidFile"=>"文件无效",
"invalidPEmail"=>"邮箱%s无效",
"invalidPName"=>"用户名%s无效",
"invalidPCalltype"=>"呼叫方式%s无效",
"invalidPPin"=>"PIN%s无效",
"invalidPActive"=>"激活值%s无效",
"invalidPage"=>"页面无效",
"invalidPDeviceType"=>"设备类型%s无效",
"invaildVerCode"=>"验证码无效",
"invalidIdentity"=>"无效的身份信息，您可能在其它终端登录过，请重新登录",
"indoorMonitor"=>"室内机",
"inactivated"=>"未激活",
"normal"=>"正常",
"expired"=>"过期",
"lastNameEmpty"=>"请输入姓",
"locationEmpty"=>"请输入地点",
"locationPLoog"=>"地点名字%s长度过长",
"locationLoog"=>"地点名字长度过长",
"loginError"=>"登录错误",
"loginFail"=>"登录失败",
"loginSuccess"=>"登录成功",
"limitIP"=>"由于您尝试次数过多，请在5分钟后再次尝试",
"limitDevice"=>"您的设备号已达到最大限制",
"MAC2PLibrary"=>"无效MAC地址：%s，请检查您的MAC库",
"MAC2Library"=>"无效的MAC地址，请检查您的MAC库",
"macExits"=>"MAC地址已存在",
"MACLength"=>"MAC地址长度必须是12位",
"modifySuccess"=>"修改成功",
"modifyFailed"=>"修改失败",
"maxHouse"=>"用户数已达到最大限制，请与管理员联系",
"modifyAptFail"=>"保存失败!该房间号已存在，您需要先将其删除",
"nameloog"=>"用户名太长，用户名最多可以包含64个字符",
"nameExit"=>"用户名已存在",
"notPermission"=>"您没有操作许可",
"noSip"=>"SIP帐户不够分配",
"passwordIncorrect"=>"密码不正确",
"passwdChangeSuccess"=>"修改密码成功",
"passwordResetSuccess"=>"重置密码成功",
"passwordReset2"=>"密码已重置为 '%s'",
"payTimeOut"=>"支付超时",
"payFailed"=>"支付失败",
"processing"=>"处理中",
"paySuccess"=>"付款成功",
"redirectedOnRPS"=>"此MAC地址在RPS上重定向",
"registerFailed"=>"注册失败",
"registerSuccess"=>"注册成功",
"roomNotExit"=>"用户不存在",
"RFCardExit"=>"此门禁卡已存在",
"registered"=>"已注册",
"PrivateKeyExists"=>"此私钥已存在",
"passwordCorrect"=>"密码无效",
"timeLessCurrent"=>"更新时间无效",
"timeZoneChangeSuccess"=>"更改时区成功",
"timeOut"=>"超时",
"unbindMACUser"=>"请先解除%s与用户的绑定",
"unKnowDT"=>"未知的设备类型",
"userBindUser"=>"请删除此账号下的所有用户",
"userNotExit"=>"用户不存在",
"userMaxPLimt"=>"添加失败，最多只能添加%s个家庭成员",
"unregistered"=>"未注册",
"validMAC"=>"请输入有效的MAC地址",
"versionExit"=>"此版本已存在",
"versionNameNumberExit"=>"版本名或版本号已存在",
"sipStatus"=>"SIP帐户分配失败，请重试",
"sentCodeLater"=>"我们已向您发送了验证码，请稍后再试",
"setSuccess"=>"安装成功",
"sendEmailSuccess"=>"邮件发送成功",
"SetFailed"=>"安装失败",
"stairPhone"=>"梯口机",
"successed"=>"成功",
"subscription"=>"续费",
"wallPhone"=>"围墙机",
"emailMaxLen"=>"邮箱长度必须少于64个字符",
"serverUpgradeTips"=>"服务器升级已完成，请刷新页面。在刷新页面之前，您可以将刚刚输入的内容复制以备使用。",
"ActiveFamilyAccount"=>"请先激活您的家庭主账号",
"weekly"=>"每周",
"daily"=>"每天",
"never"=>"不重复",
"calltypeEmpty"=>"请选择呼叫方式",
"addOutApt"=>"最多只能添加%s个房间",
"call"=>"呼叫",
"unlock"=>"开门",
"tryUnlockCall"=>"通话开门失败",
"tryUnlockKey"=>"密码开门失败",
"tryUnlockCard"=>"门禁卡开门失败",
"tryUnlockFace"=>"人脸开门失败",
"unlockApp"=>"小睿APP开门",
"unlockIndoor"=>"室内机开门",
"unlockNFC"=>"NFC开门",
"unlockBluetooth"=>"蓝牙开门",
"unlockCard"=>"门禁卡开门",
"unlockPrivateKey"=>"密码开门",
"unlockTempKey"=>"临时密码开门",
"alarmDoorUnlock"=>"开门",
"alarmInfrared"=>"红外线",
"alarmSmoke"=>"吸烟",
"alarmGas"=>"煤气",
"alarmUrgency"=>"紧急",
"alarmSOS"=>"SOS",
"alarmTamper"=>"防拆",
"alarmGate"=>"大门",
"alarmDoor"=>"门",
"alarmBedroom"=>"卧室",
"alarmGuestRoom"=>"客房",
"alarmHall"=>"大厅",
"alarmWindow"=>"窗口",
"alarmBalcony"=>"阳台",
"alarmKitchen"=>"厨房",
"alarmStudy"=>"书房",
"alarmBathroom"=>"浴室",
"alarmArea"=>"区域",
"RFCardExit2"=>"门禁卡%s已经存在",
"RFCardDuplicated"=>"门禁卡%s重复",
"notMacBind"=>"用户'%s'没有打开设备'%s'的门的权限",
"accountNumLet"=>"账号名必须由数字和字母组成",
"networkUnavailable"=>"网络不可用",
"notForModel"=>"此型号暂不适用",
"upgradeDevVersion"=>"请先升级到最新版本",
"unavailableService"=>"服务暂时不可用，请稍后再试",
"cantDeletePin"=>"你不能删除开门密码%s",
"residentInRoom"=>"%s房间已经有住户了",
"noAnswer"=>"未接听",
"indoorAndApp"=>"室内机和小睿APP",
"indoorMonitorOnly"=>"室内机",
"appOnly"=>"小睿APP",
"endThanStart"=>"结束时间必须大于开始时间",
"endThanStartFile"=>"无效的日期或者时间在文件第'%s'行",
"doorRelease"=>"开门",
"success"=>"成功",
"unlockFACE"=>"人脸开门",
"unlockBLE"=>"蓝牙开门",
"captureSmartPlus"=>"小睿APP上截图",
"drmagnet"=>"门磁",
"failedUnlock"=>"开门失败",
"deviceDisconnected"=>"设备不在线",
"low"=>"偏低",
"motion"=>"移动侦测",
"capture"=>"截图",
"failedImport"=>"导入失败",
"notValidMobile"=>"无效的手机号码 %s",
"mobileExits"=>"手机号已经存在",
"mobileExits2"=>"手机号 %s 已经存在",
"mobileDuplicated"=>"重复的手机号 %s",
"mobileNumberExist"=>"手机号不存在",
"codeIncorrect"=>"验证码错误",
"sendCodeSuccess"=>"发送验证码成功",
"codeCorrect"=>"正确",
"mobileNumberEmpty"=>"请输入您的手机号码",
"invalidUser"=>"无效的用户 %s",
"locationExits"=>"地点名已经存在",
"smartPlusIndoor"=>"小睿APP和室内机",
"phoneIndoor"=>"手机号和室内机",
"smartPlusIndoorBackup"=>"先呼小睿APP和室内机，没人接再呼手机号",
"smartPlusBackup"=>"先呼室内机，没人接再呼小睿APP",
"indoorPhoneBackup"=>"先呼室内机，没人接再呼手机号",
"indoorSmartPlusPhone"=>"先呼室内机，没人接再呼小睿APP，最后呼手机号",
"endUser"=>"终端用户",
"installer"=>"安装商",
"distributor"=>"代理商",
"pm"=>"物业管理员",
"superManage"=>"超级管理员",
"loginManagement"=>"登录管理",
"accessControl"=>"门禁",
"userManagement"=>"用户管理",
"deviceManagement"=>"设备管理",
"communityManagement"=>"社区管理",
"auditLogin"=>"网页登入",
"auditLogout"=>"网页登出",
"auditAddTempKey"=>"添加临时密码: {0}",
"auditEditTempKey"=>"编辑临时密码: {0}",
"auditDeleteTempKey"=>"删除临时密码: {0}",
"auditAddRFCard"=>"添加门禁卡: {0}",
"auditEditRFCard"=>"编辑门禁卡: {0}",
"auditDeleteRFCard"=>"删除门禁卡: {0}",
"auditAddDis"=>"添加代理商: {0}",
"auditEditDis"=>"编辑代理商: {0}",
"auditDeleteDis"=>"删除代理商: {0}",
"auditAddInstaller"=>"添加安装商: {0}",
"auditEditInstaller"=>"编辑安装商: {0}",
"auditDeleteInstaller"=>"删除安装商: {0}",
"auditAddPM"=>"添加物业管理员: {0}",
"auditEditPM"=>"编辑物业管理员: {0}",
"auditDeletePM"=>"删除物业管理员: {0}",
"auditAddEndUser"=>"添加终端用户: {0}",
"auditEditEndUser"=>"编辑终端用户: {0}",
"auditDeleteEndUser"=>"删除终端用户: {0}",
"auditSetOwnerTime"=>"设置自己的时区{0}",
"auditSetOwnPassword"=>"设置自己的密码",
"auditAddPIN"=>"添加密码: {0}",
"auditEditPIN"=>"编辑密码: {0}",
"auditDeletePIN"=>"删除密码: {0}",
"auditImportFace"=>"导入人脸: {0}",
"auditDeleteFace"=>"删除人脸: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"设置呼叫类型smartPlus和室内监视器: {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"设置通话类型的电话和室内监视器: {0}&{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"设置呼叫类型smartPlus和室内监视器，以电话作为备用: {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"使用SmartPlus设置呼叫类型的室内监视器作为备份: {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"设置带有电话作为备份的呼叫类型室内监视器: {0}&{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"设置带有SmartPlus作为备份的呼叫类型室内监视器，最后设置电话: {0}&{1}",
"auditDeleteDevice"=>"删除设备: {0}",
"auditSetAPTCount"=>"设置房间号{0}",
"auditEnableLandline"=>"启用落地服务",
"auditDisableLandline"=>"禁用落地服务",
"auditSetSubTime"=>"设置时区{0}",
"auditSetChargeModeInstall"=>"由安装人员设置收费模式",
"auditSetChargeModeUser"=>"按用户/ PM设置收费模式",
"auditSetConnectTypeDefault"=>"设置连接类型为默认",
"auditSetConnectTypeTCP"=>"设置连接类型为tcp",
"auditSetConnectTypeUDP"=>"设置连接类型为udp",
"auditSetConnectTypeTLS"=>"设置连接类型为tls",
"auditAddCommunity"=>"添加社区: {0}",
"auditDeleteCommunity"=>"删除社区：{0}",
"auditImportCommunity"=>"导入社区：{0}",
"auditSetAPTNumber"=>"设置{0}房间号{1}",
"auditSetEmail"=>"设置邮箱{0}:{1}",
"auditSetMobile"=>"设置电话号码{0}:{1}",
"auditDeviceTypeStair"=>"设置类型多住户门铃：{0}",
"auditDeviceTypeDoor"=>"设置类型单住户门铃：{0}",
"auditDeviceTypeIndoor"=>"设置类型室内监控：{0}",
"auditDeviceTypeGuardPhone"=>"设置类型警卫电话：{0}",
"auditDeviceTypeAccessControl"=>"设置类型访问控制：{0}",
"auditSetNetGroup"=>"设置网络组 {0}:{1}",
"auditEditCommunity"=>"编辑社区",
"deliveryMsg"=>"您有%s个快递已送达，请及时领取",
"deliveryTitle"=>"您有新的快递",
"rfcardDuplicatedLines"=>"第%s行的卡号重复了！",
"rfcardNameInvalid"=>"第%s行的卡名称无效！",
"rfcardExistLines"=>"第%s行的卡号已经存在。",
"importFailMacExistLines"=>"第%s行的mac地址存在或无效",
"exportExcelCountNull"=>"导出失败！所选的日期内没有记录，请重新选择",
"keyIsEqualRoom"=>"密码不能与APT号码相同！",
"visitor"=>"访客",
"CommunityNameExist"=>"社区名字已经存在",
"unlockGuardPhone"=>"管理机开门",
"auditLoginApp"=>"App登录",
"auditLogoutApp"=>"App退出",
"timeForYesterday"=>"昨天",
"exportExcelDataBefore"=>"请先导出 %s 之前的数据",
"tempkeyUsed"=>"临时密码使用",
"tempkeyContent"=>"%s 使用了临时密码。",
"accessNameExist"=>"权限组名称已经存在",
"addFaceFail"=>"请导入一张清晰的人脸照片",
"userInvalid"=>"第%s行的用户无效。",
"groupsInvalid"=>"第%s行的权限组无效。",
"BuildAccessName"=>"住户-楼栋 %s",
"auditCodeLogEditApt"=>"编辑房间: {0}",
"invalidTimeInLine"=>"第%s行的时间无效。",
"cancel"=>"取消",
"cancelSuccess"=>"取消成功",
"payOutstanding"=>"请检查是否有待支付订单。如果没有，请联系您的服务提供商",
"beyondFamilyMember"=>"无法添加更多家庭成员，请联系服务提供商创建。",
"indoorMonitorRequired"=>"每间公寓至少需要一台室内机",
"featureActivationFee"=>"功能（一次性收费）",
"systemProcessing"=>"系统处理中",
"featureMonthlyFee"=>"功能（月费）",
"featurePriceDifferences"=>"功能（差价）",
"updatingSuccess"=>"更新成功",
"featureNameBasic"=>"基础",
"featureNamePremium"=>"高级",
"indoorMacNotCorrect"=>"请输入正确的室内机MAC",
"off"=>"关闭",
"enterValidAccount"=>"请输入有效的账号",
"invalidKitImportMAC"=>"请检查是否MAC已存在或无效: %s",
"importLessData"=>"请输入小于 %s 数据",
"invalidQRCode"=>"识别失败，请扫描正确的二维码。",
"cannotCreateFamilyMember"=>"无法创建更多家庭成员账号",
"importProcessing"=>"导入中，请稍后再试",
"departmentAccessName"=>"%s 权限组",
"idExistsLine"=>"第%s行的工号已存在",
"enterFirstNameLine"=>"请在第%s行输入姓",
"enterLastNameLine"=>"请在第%s行输入名",
"departmentExist"=>"部门已存在",
"idExist"=>"工号已存在",
"officeNameExist"=>"办公名字已存在",
"departmentExit"=>"部门已存在",
"importOutTask"=>"您只能同时导入1个模板",
"idDuplicated"=>"工号%s重复",
"aptInvalidLine"=>"第%s行房间无效",
"buildInvalidLine"=>"第%s行楼栋无效",
"departmentInvalidLine"=>"第%s行部门无效",
"idInvalidLine"=>"第%s行工号无效",
"propertyManager"=>"物业管理员",
"departmentBindDevice"=>"请删除部门下的设备",
"departmentBindUser"=>"请删除部门下的员工",
"smartPlusValidLine"=>"第%s行\"小睿对讲功能\"无效",
"identityValidLine"=>"第%s行身份无效",
"textUpgradeMsg1"=>"请升级APP版本",
"textUpgradeMsg2"=>"登陆失败",
"deleteCodeGetLimitTimes"=>"无效的密码，请24小时后再试",
"deleteCodeOverLimitTimes"=>"请24小时后再试",
"deleteCodeError"=>"无效的密码",
"textUpgradeMsg"=>"1.优化临时密码功能;2.增加账号注销功能;3.修复一些问题",
"pmappStatusInvalid"=>"请先开启物业管理员App",
"delivery_description"=>"快递员临时密码",
"relayInvalid"=>"第%s行的relay无效。",
"cancelError"=>"取消失败。",
"lockApp"=>"小睿APP关门",
"lock"=>"关门",
"versionLogMaxLen"=>"版本日志不能大于 %s 个字符",
"autoLock"=>"自动关门",
"pinAndRFcardNotNullLines"=>"第%s行的PIN和RF Card至少有一个要填写！",
"pinExistLines"=>"第%s行的PIN已经存在。",
"pinInvalidLines"=>"第%s行的PIN无效",
"pinDuplicatedLines"=>"第 %s 行的PIN重复！",
"FaceImportLength"=>"人脸导入文件的大小不能大于 %s",
"landlineServerNotActivate"=>"社区未开通落地功能",
"importFailDisNotExist"=>"区域管理员账号不存在",
"importFailNotPermission"=>"您没有权限添加此MAC地址",
"importFailTooManyAdd"=>"导入失败，只能填写以为区域管理员",
"importFailAdded"=>"MAC地址已被添加",
"macAssignToLimit"=>"最多只能分配给10个区域管理员。",
"macNumToLimit"=>"您一次最多只能上传 1000 个 MAC 地址。",
"addOutFloor"=>"请输入1个在 -5~128之间的值。",
"floor"=>"楼层",
"PostalCodeInvalid"=>"请输入字母或数字",
"onceCodeInvalid"=>"一次性密码必须为4-5位数字",
"permanentCodeInvalid"=>"永久密码必须为6位数字",
"onceCodeOutNum"=>"您最多只能添加10个一次性密码",
"permanentCodeOutNum"=>"您最多只能添加10个永久密码",
"onceCodeExist"=>"一次性密码已存在",
"permanentCodeExist"=>"永久密码已存在",
"addOutFloorLine"=>"第 %s 行的楼层号无效",
"auditManuallyUnlock"=>"手动开门",
"auditManuallyLock"=>"手动关门",
"automaticallyUnlock"=>"自动开门",
"doorClose"=>"关门",
"PostalCodeNotEmpty"=>"请至少输入一个字母或数字",
"emergencyAlarm"=>"紧急告警",
"doorSensor"=>"门磁",
"yaleBatteryWarning"=>"Yale锁低电量提醒",
"auditCodeManuallyUnlock"=>"手动开门",
"auditCodeManuallyLock"=>"手动关门",
"2weekBatteryWarning"=>"%s - 预计电池剩余时间：2 周。",
"1weekBatteryWarning"=>"%s - 预计电池剩余时间：1 周。",
"replaceBatteryWarning"=>"%s - 电池电量极低，请立即更换。",
"open"=>"	门打开",
"close"=>"门关闭",
"addContactFavoriteNum"=>"添加到收藏失败，您最多只能收藏300 个房间",
"addContactBlockNum"=>"添加到黑名单失败，您最多只能将 100 套公寓添加到黑名单。",
"voiceTitle"=>"语音留言",
"voiceContent"=>"您有一条来自%s的语音消息",
"voiceMsgInvalid"=>"语音留言已过期",
"toggleFeaturePlan"=>"您不能修改高级功能",
"rtspAddresEmpty"=>"请输入RTSP地址",
"rtspAddresInvalid"=>"无效的RTSP地址",
"rtspPortEmpty"=>"请输入端口号",
"rtspPortInvalid"=>"无效的端口",
"rtspPassWdEmpty"=>"请输入密码",
"rtspPassWdInvalid"=>"密码过长，长度超过了63个字符",
"cameraExist"=>"摄像头已存在",
"errorOnRPS"=>"请求RPS服务器失败",
"faceImportErrorSystem"=>"系统错误",
"faceImportErrorView"=>"没有检测到正脸",
"faceImportErrorWearMask"=>"检测到口罩",
"faceImportErrorLowResolution"=>"分辨率太低",
"faceImportErrorWrongFormat"=>"文件格式错误",
"faceImportErrorNoFace"=>"没有检测到人脸",
"faceImportErrorFileLarge"=>"文件太大",
"faceImportErrorFaceLarge"=>"人脸太大",
"faceImportErrorFaceSmall"=>"人脸太小",
"faceImportErrorMultiFaces"=>"检测到多张人脸",
"faceImportErrorWrongName"=>"文件名错误",
"faceImportErrorEmptyName"=>"用户名为空",
"faceImportErrorNoAccountInfo"=>"获取用户信息出错",
"faceImportErrorAccountInactive"=>"账号未激活。",
"changeHomeFeatureInvalid"=>"操作失败！此代理商下，已有安装商开通了智能家居业务",
"changeInterComFeatureInvalid"=>"操作失败！此代理商下，已有安装商开通了对讲业务",
"offline"=>"失败：离线",
"allFloors"=>"所有楼层",
"uploadOversize"=>"上传的文件不能大于 %s",
"uploadInvalidType"=>"不支持上传的文件类型",
"uploadFailed"=>"上传失败，请稍后重试",
"uploadScreenSaverImgTooMuch"=>"屏保图片不能多于 %s 张",
"screenSaverImgTooLittle"=>"屏保图片不能少于 %s 张",
"screenSaverImgTooMuch"=>"屏保图片不能多于 %s 张",
"screenSaverDevicesOffline"=>"保存失败，设备已离线",
"saveFailed"=>"保存失败",
"importingInProgress"=>"正在导入中，请稍后再试",
"importBuildingInvalidLine"=>"第 %s 行楼栋无效",
"importAptInvalidLine"=>"第 %s 行房间无效",
"importAccountTypeInvalidLine"=>"第 %s 行账户类型无效",
"importFirstNameInvalidLine"=>"第 %s 行姓无效",
"importLastNameInvalidLine"=>"第 %s 行姓名无效",
"importKeyInvalidLine"=>"第 %s 行密码无效",
"importKeyExistsLine"=>"第 %s 行密码已存在",
"importCardInvalidLine"=>"第 %s 行RF卡无效",
"importCardExistsLine"=>"第 %s 行RF卡已存在",
"importAccessGroupInvalidLine"=>"无效的权限组ID",
"importAccessGroupNoPermissionLine"=>"第 %s 行填写的权限组ID不允许添加给该用户",
"importExceededNumberLine"=>"第 %s 行家庭成员数量超出限制",
"importNoActiveMasterLine"=>"第 %s 行导入失败，请先激活家庭主账号",
"importMasterExistsLine"=>"第 %s 行家庭主账号已存在",
"importNoCreateMasterLine"=>"第 %s 行导入失败，请先创建家庭主账号",
"PrivateKeysDataExist"=>"私钥%s已存在。",
"PrivateKeyDataExists"=>"私钥%s已存在。",
"landLineOpenToClosedFail"=>"保存失败。请确保项目中房间的呼叫类型为\“小睿APP和室内机\”或者\“先呼室内机，没人接再呼小睿APP\”。",
"limitWithIp"=>"操作频繁，请5分钟后再尝试。（IP：%s）",
"subDistributor"=>"子代理商",
"faceImportErrorNotClear"=>"导入图片不清晰",
"featureDeleteError"=>"Feature Plan Be bound.",
"layoutIdInvalid"=>"Layout is invalid",
"unlockAppHome"=>"AKHome Unlock",
"eachDoorCount"=>"A single plan to open each door once",
"paramsError"=>"Parameter error",
"webRelayIDInvalidLine"=>"Invalid Web Relay ID in line %s.",
"textUpgradeMsgForComRole"=>"upgrade community role",
"textUpgradeMsgForPerRole"=>"upgrade Personnel role",
"textUpgradeMsgForOffRole"=>"upgrade office role",
"textUpgradeMsgForPMRole"=>"upgrade PM role",


  ];
