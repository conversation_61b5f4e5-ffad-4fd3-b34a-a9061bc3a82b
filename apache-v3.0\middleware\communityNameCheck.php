<?php
/*
 * @Description: 检查社区location合法性
 * @version:
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors: cj
 */
namespace middleware;

include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";
use \interfaces\middleware\main\IMiddleware;

class CCommunityNameCheck implements IMiddleware
{
    public function handle(\Closure $next)
    {
        global $cMessage;
        $db = \database\CDatabase::getInstance();
        $params = ["ID"=>"","Location"=>"","userAliasId"=>"","ProjectGrade"=>""];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $id = $params["ID"];
        $location = $params["Location"];
        $projectGrade = $params["ProjectGrade"];

        $manageGroupId = $db->querySList(
            "select ManageGroup from Account where ID = :ID",
            [":ID"=>$userId]
        )[0]['ManageGroup'];

        $sql = "select count(*) from Account where ManageGroup = :ManageGroup and Location = :Location and Grade = :Grade";
        $bindArray = [":Location"=>$location, ":ManageGroup"=>$manageGroupId, ":Grade"=>$projectGrade];
        if ($id) {
            $bindArray[":ID"] = $id;
            $data = $db->querySList($sql." and ID != :ID", $bindArray)[0]["count(*)"];
        } else {
            $data = $db->querySList($sql, $bindArray)[0]["count(*)"];
        }
        
        if ($data > 0) {
            $cMessage->echoErrorMsg(StateCommunityNameExits);
        }
        $next();
    }
}
