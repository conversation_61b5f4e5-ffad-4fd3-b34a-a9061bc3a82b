<?php
/*
 * @Description:
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: cj
 */

namespace model;

include_once __DIR__ . "/../../util/model.php";
include_once __DIR__ . "/../../util/string.php";
include_once __DIR__ . "/../../util/computed.php";
include_once __DIR__ . "/../../util/time.php";

class CLog
{
    public function query() {
        $params = [
            "DeviceID" => "",
            "Result" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "Type" => "",
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $userId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $device = $params["DeviceID"];
        $result = $params["Result"];
        $beginTime = $params["BeginTime"];
        $endTime = $params["EndTime"];
        $type = $params["Type"];

        $where = "";
        $bindArray = [':AccountID' => $userId];
        if ($device) {
            $where = "$where AND L.DevicesID = :DeviceID";
            $bindArray[':DeviceID'] = $device;
        }
        if ($result) {
            $where = "$where AND L.Result = :Result";
            $bindArray[':Result'] = $result;
        }
        if ($beginTime) {
            $where = "$where AND L.Time > :BeginTime";
            //$beginTime = strtotime($beginTime);
            $bindArray[':BeginTime'] = \util\time\setTimeZone($beginTime, $timeZone, "", '-');
        }
        if ($endTime) {
            $where = "$where AND L.Time < :EndTime";
            //$endTime = strtotime($endTime);
            $bindArray[':EndTime'] = \util\time\setTimeZone($endTime, $timeZone, "", '-');
        }
        if ($type) {
            $where = "$where AND L.Action like :Action";
            $bindArray[':Action'] = "%$type%";
        }

        $total = $this->db->querySList("select count(*) as total from PersonalLogs L where L.AccountID = :AccountID $where", $bindArray)[0]["total"];
        $data = $this->db->querySList("select L.Time,L.ID,L.Operator,L.Action,L.Result,D.Location from PersonalLogs L left join PersonalDevices D on L.DevicesID = D.ID where L.AccountID = :AccountID $where order by ID desc limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        $rows = [];
        foreach ($data as $row) {
            $cur_device = array();
            $cur_device['ID'] = $row['ID'];
            $cur_device['Happened On'] = $row['Time'];
            $cur_device['Device'] = $row['Location'] ? $row['Location'] : '--';
            $cur_device['Initiated By'] = $row['Operator'];
            $cur_device['Action'] = $row['Action'];
            $cur_device['Response'] = $row['Result'];
            array_push($rows, $cur_device);
        }
        \util\computed\setGAppData(["data" => ["total" => $total, "row" => $rows, "detail" => $data]]);
    }

    public function queryAudit() {
        $params = [
            "UserType" => "",
            "BeginTime" => "",
            "EndTime" => "",
            "Type" => "",
            "userAliasId" => "",
            "userId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => "",
        ];
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);

        $userType = $params["UserType"];
        $begin = $params["BeginTime"];
        $end = $params["EndTime"];
        $type = $params["Type"];
        $userId = $params["userId"];
        $userAliasId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $allType = [
            'Login' => AuditCategoryLogin,
            'Access' => AuditCategoryAccess,
            'User' => AuditCategoryUser,
            'Community' => AuditCategoryCommunity,
            'Device' => AuditCategoryDevice
        ];

        $typeLabel = [
            'Login' => MSGTEXT['loginManagement'],
            'Access' => MSGTEXT['accessControl'],
            'User' => MSGTEXT['userManagement'],
            'Community' => MSGTEXT['communityManagement'],
            'Device' => MSGTEXT['deviceManagement']
        ];

        $where = "where 1 = 1";
        $bindArray = [];
        if ($userType != 'all' && $userType != "Enduser") {
            $where .= " and OperaType = :OperaType";
            $bindArray[':OperaType'] = $userType;
        } elseif ($userType == "Enduser") {
            $where .= " and OperaType in ('SingleMaster', 'SingleMember', 'CommunityMaster', 'CommunityMember')";
        }

        if ($type != 'all') {
            $where .= " and Type in (" . implode(',', $allType[$type]) . ")";
        }

        if ($begin) {
            $where .= " and CreateTime >= :BeginTime";
            // V6.5.1 修复审计日志检索未按时区检索问题
            $bindArray[':BeginTime'] = \util\time\setTimeZone($begin, $timeZone, '', '-');
        }

        if ($end) {
            $where .= " and CreateTime <= :EndTime";
            $bindArray[':EndTime'] = \util\time\setTimeZone($end, $timeZone, '', '-');
        }

        //v6.1 PM的审计日志
        $account = $this->db->querySlist('select ID,Account,Grade from Account where ID=:ID', [':ID' => $userId])[0];
        $community = $this->db->querySlist('select ID,Account,Grade from Account where ID=:ID', [':ID' => $userAliasId])[0];
        if ($account['Grade'] == PROPERTYMANAGE) {
            $where .= " and Operator = :Operator and (Community = :Community or Type in (" . AuditCodeLoginWeb . "," .AuditCodeSelfPassword. "," .AuditCodeManuallyLock. "," .AuditCodeManuallyUnlock."))";
            $bindArray[':Operator'] = $account['Account'];
            $bindArray[':Community'] = $community['Account'];
        }

        $operators = [
            "SuperManager" => MSGTEXT['superManage'],
            "Distributor" => MSGTEXT['distributor'],
            "Installer" => MSGTEXT['installer'],
            "PM" => MSGTEXT['pm'],
            "SingleMaster" => MSGTEXT['endUser'],
            "SingleMember" => MSGTEXT['endUser'],
            "CommunityMaster" => MSGTEXT['endUser'],
            "CommunityMember" => MSGTEXT['endUser']
        ];

        $total = $this->db->querySList("select count(*) as total from AuditLog $where", $bindArray)[0]["total"];
        $data = $this->db->querySList("select ID,IP,Operator,CreateTime,Type,KeyInfo,OperaType,Distributor, Installer, Community from AuditLog $where order by ID desc limit $offset,$rows", $bindArray);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);

        $dis = \util\role\getDisForInstaller($userAliasId);
        if ($dis['IsEncryptPin'] == ENCRYPTION){
            foreach ($data as &$row) {
                if (in_array(intval($row['Type']), [3, 4, 5, 9, 10, 11])) {
                    $row['KeyInfo']=json_decode($row['KeyInfo']);
                    $row['KeyInfo'][0]=\util\computed\setPinIsEncryptPin($userAliasId,$row['KeyInfo'][0]);
                    $row['KeyInfo']=json_encode($row['KeyInfo']);
                }
            }
        }
        foreach ($data as &$val) {
            $val["OperaType"] = $operators[$val["OperaType"]];
            $val["Action"] = \util\string\format(AuditWords[$val['Type']], json_decode($val["KeyInfo"], true));
            foreach ($allType as $key => $type) {
                if (in_array($val["Type"], $type)) {
                    $val["Type"] = $typeLabel[$key];
                }
            }
            $val["Community"] = $this->db->querySList("select Location from Account where Account = :Account",[":Account"=>$val["Community"]])[0]["Location"];
        }

        \util\computed\setGAppData(["data" => ["row" => $data, "detail" => $total, "total" => $total]]);
    }
}
