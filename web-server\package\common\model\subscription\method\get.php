<?php

namespace package\common\model\subscription\method;
use package\common\model\subscription\config\Code;
trait Get
{
    /**
     * @description:获得SubscriptionList的UUID
     * @param: {string} BmSubscriptionNumber 计费系统订单号
     * @author: shoubin.chen 2024-01-08 11:07:39 v6.7.1
     * @lastEditor: shoubin.chen 2024-01-08 11:07:39 v6.7.1
     */
    public function getSubscriptionUUID()
    {
        $params = ['BmSubscriptionNumber:string-required'];
        list($bmSubscriptionNumber) = $this->getParams($params);
        $uuid = $this->dao->subscriptionList->selectByKey('BmSubscriptionNumber', $bmSubscriptionNumber, 'UUID')[0]['UUID'];
        if ($uuid === null) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_OR_NOT_PERMISSION]);
        }
        return $uuid;
    }

    /**
     * @description: 根据UUID获取订阅信息
     * @param {string} UUID
     * @return mixed
     * @throws \Exception
     * @author: csc 2024/1/15 11:50 V6.7.1
     * @lastEditors: csc 2024/1/15 11:50 V6.7.1
     */
    public function getSubscriptionInfoByUUID()
    {
        $params = ['UUID:uuid'];
        list($uuid) = $this->getParams($params);
        $subscription = $this->dao->subscriptionList->selectByKey('UUID', $uuid)[0];

        return $subscription;
    }

    /**
     * @description:获得计费系统订阅URL
     * @param: {string} UUID订阅UUID
     * @return string 计费系统订阅URL
     * @author: shoubin.chen 2024-01-23 16:49:02 v6.7.1
     * @lastEditor: shoubin.chen 2024-01-23 16:49:02 v6.7.1
     */
    public function getBmURL()
    {
        $params = ['UUID:uuid'];
        list($uuid) = $this->getParams($params);
        $subscription = $this->callSelfFunc('getSubscriptionInfoByUUID', [$uuid]);
        if ($subscription === null) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION, ['externalErrorObj' => Code::EXT_STATE_SUBSCRIPTION_NOT_EXIST_OR_NOT_PERMISSION]);
        }

        if (intval($subscription['statue']) === SUBSCRIBE_STATUS['creating']) {
            $bmURL = BMSUBSCRIPTIONURL . '?subscriptionNumber=' . $subscription['BmSubscriptionNumber'] . '&token=' . $subscription['WebHookToken'] . '&code=' . $subscription['PayCode'];
            return $bmURL;
        }
        return '';
    }

    public function getPayerType()
    {
        $params = ['Role:string'];
        list($role) = $this->getParams($params);
        if (in_array($role, R_END_USER_ROLE)) {
            // 终端用户，里面包含了PMAPP
            $payerType = 0;
        } elseif ($role === RPROPERTYMANAGE) {
            // pm
            $payerType = 1;
        } elseif (in_array($role, R_PROJECT_ROLE)) {
            // installer
            $payerType = 2;
        } elseif ($role === RSUBDISTRIBUTOR) {
            // sub dis
            $payerType = 4;
        } else {
            // dis
            $payerType = 3;
        }
        return $payerType;
    }

    /**
     * @description: dis获得订阅的项目类型
     * @param: Type 订阅实际的类型
     * @author: shoubin.chen 2024/12/4 15:10:17 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/4 15:10:17  V7.1.0
     */
    public function getSubscriptionProjectTypeByDis()
    {
        $params = ['MixType'];
        list($mixType) = $this->getParams($params);
        $mixTypeArr = $this->share->util->getBitPositions($mixType);
        $typeMap = [
            1 => 1, // 单住户续费 app
            2 => 2, // 社区续费 app
            3 => 3, // 办公续费 app
            4 => 2, // rentManage续费
            5 => 1, // 单住户视频存存储续费
            6 => 2, // 社区视频存储续费
            8 => 1, // 单住户三方锁
            9 => 2, // 社区三方锁
        ];
        foreach ($mixTypeArr as $type) {
            return (string)$typeMap[$type];
        }
        return 1;
    }

    /**
     * @description: dis获得订阅的项目类型
     * @param: Type 项目类型 1：单住户 2=社区 3=办公
     * @author: shoubin.chen 2024/12/4 15:10:17 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/4 15:10:17  V7.1.0
     */
    public function getSubscriptionType()
    {
        $params = ['Type:enum("1","2","3")'];
        list($type) = $this->getParams($params);
        $typeArr = [];
        if ($type == 1) {
            $typeArr = [
                SUBSCRIBE_TYPE['singleRenew'],
                SUBSCRIBE_TYPE['singleVideoStorageRenew'],
                SUBSCRIBE_TYPE['singleMixRenew'],
            ];
        } elseif ($type == 2) {
            $typeArr = [
                SUBSCRIBE_TYPE['communityRenew'],
                SUBSCRIBE_TYPE['communityVideoStorageRenew'],
                SUBSCRIBE_TYPE['communityMixRenew'],
            ];
        } else {
            $typeArr = [
                SUBSCRIBE_TYPE['officeRenew'],
            ];
        }
        return $typeArr;
    }

    public function subscriptionListSelectByArray()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->subscriptionList->selectByArray($array, $fields);
    }

    /**
     * @description: 处理订阅的类型
     * @param:{string} Type 订阅的类型
     * @return array 1=app,2=视频存储,3=三方锁 4=rentmanager
     * @author: shoubin.chen 2024/11/6 15:12:45 V7.1.0
     * @lastEditors: csc 2025/2/17 16:49 V7.1.0
     */
    public function dealSubscriptionFunction()
    {
        $params = ['Type', 'MixType'];
        list($type, $mixType) = $this->getParams($params);
        $result = [];
        if ($type == SUBSCRIBE_TYPE['mixRenew']) {
            $mixTypeArr = $this->share->util->getBitPositions($mixType);
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['singleRenew'], SUBSCRIBE_TYPE['communityRenew'], SUBSCRIBE_TYPE['officeRenew']])) {
                $result[] = 1;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['communityVideoStorageRenew'], SUBSCRIBE_TYPE['singleVideoStorageRenew']])) {
                $result[] = 2;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['singleThirdLockRenew'], SUBSCRIBE_TYPE['communityThirdLockRenew']])) {
                $result[] = 3;
            }
            if (array_intersect($mixTypeArr, [SUBSCRIBE_TYPE['rentManagerRenew']])) {
                $result[] = 4;
            }
        } else if ($type == SUBSCRIBE_TYPE['communityVideoStorageRenew'] || $type == SUBSCRIBE_TYPE['singleVideoStorageRenew']) {
            $result[] = 2;
        } else if ($type == SUBSCRIBE_TYPE['rentManagerRenew']) {
            $result[] = 4;
        } else if ($type == SUBSCRIBE_TYPE['singleThirdLockRenew'] || $type == SUBSCRIBE_TYPE['communityThirdLockRenew']) {
            $result[] = 3;
        } else {
            $result[] = 1;
        }
        return $result;
    }

    //根据三方锁UUID获取相关信息
    public function getThirdLockInfoByLockUUID()
    {
        $params = ['LockUUID'];
        list($lockUUID) = $this->getParams($params);
        $result = [
            'UUID' => $lockUUID,
            'Brand' => '',
            'Active' => '',
            'ActiveTime' => '',
            'ExpireTime' => '',
            'ProjectType' => '',
            'AccountUUID' => '',
            'CommunityUnitUUID' => '',
            'PersonalAccountUUID' => '',
            'ProjectName' => '',
            'UnitName' => '',
            'AptName' => '',
            'LockName' => '',
            'RoomName' => '',
            'RoomNumber' => '',
        ];

        $lockRelateInfo = $this->dao->thirdLockRelateInfo->selectByLockUUID($lockUUID)[0];
        if (empty($lockRelateInfo)) {
            return $result;
        }
        $result['Brand'] = $lockRelateInfo['Brand'];
        $result['Active'] = $lockRelateInfo['Active'];
        $result['ActiveTime'] = $lockRelateInfo['ActiveTime'];
        $result['ExpireTime'] = $lockRelateInfo['ExpireTime'];
        $result['ProjectType'] = $lockRelateInfo['ProjectType'];
        $result['AccountUUID'] = $lockRelateInfo['AccountUUID'];
        $result['CommunityUnitUUID'] = $lockRelateInfo['CommunityUnitUUID'];
        $result['PersonalAccountUUID'] = $lockRelateInfo['PersonalAccountUUID'];

        if ($lockRelateInfo['Brand'] == '3') {
            $this->loadUtil('dormakaba', true);
            $result['LockName'] = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $lockUUID]])[0]['Name'];
        } else if ($lockRelateInfo['Brand'] == '6') {
            $this->loadUtil('itec', true);
            $result['LockName'] = $this->utils->_common->itec->getItecLock([['UUID', $lockUUID]])[0]['Name'];
        }

        $this->loadUtil('account', true);
        $project = $this->utils->_common->account->accountSelectByKey('UUID', $lockRelateInfo['AccountUUID'],'ID,Location')[0];
        $result['ProjectName'] = $project['Location'];

        if (!empty($lockRelateInfo['PersonalAccountUUID'])) {
            if ($lockRelateInfo['ProjectType'] == 1) {
                // 单住户
                $personalAccountList = $this->utils->_common->account->personalAccountSelectByArray([['UUID', $lockRelateInfo['PersonalAccountUUID']]]);
                $result['AptName'] = $result['RoomName'] = $personalAccountList[0]['Name'];
                $result['RoomNumber'] = $personalAccountList[0]['RoomNumber'];
            } else if ($lockRelateInfo['ProjectType'] == 2) {
                // 社区
                $personalAccountList = $this->utils->_common->account->getUnitAndAptInfoByUUID($lockRelateInfo['PersonalAccountUUID']);
                $result['AptName'] = $result['RoomName'] = $personalAccountList[0]['RoomName'];
                $result['RoomNumber'] = $personalAccountList[0]['RoomNumber'];
                $result['CommunityUnitUUID'] = $personalAccountList[0]['UnitUUID'];
                $result['UnitName'] = $personalAccountList[0]['UnitName'];
            }
        } else if (!empty($lockRelateInfo['CommunityUnitUUID'])) {
            $this->loadUtil('communityUnit', true);
            $unitInfo = $this->utils->_common->communityUnit->getUnitInfoByKey('UUID', $lockRelateInfo['CommunityUnitUUID']);
            $result['UnitName'] = $unitInfo['UnitName'];
        }

        return $result;
    }

}
