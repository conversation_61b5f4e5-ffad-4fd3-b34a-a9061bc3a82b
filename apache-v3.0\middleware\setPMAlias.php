<?php
/*
 * @Description: 根据HTTP_X_COMMUNITY_ID设置代理为PM代理的社区
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:29:52
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";
use \interfaces\middleware\main\IMiddleware;
class CSetPMAlias implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp,$cMessage;
        $userAliasId = $gApp["userAliasId"];
        $communityId = $_SERVER["HTTP_X_COMMUNITY_ID"];
        $db = \database\CDatabase::getInstance();
        $cLog->actionLog("#middle#setPMAlias#");

        $userInfoUUID = $db->querySList('select AM.UserInfoUUID from AccountMap AM join Account A on A.UUID=AM.AccountUUID where A.ID=:ID', [':ID'=>$userAliasId])[0]['UserInfoUUID'];
        $data = $db->querySList('select AccountUUID from AccountMap where UserInfoUUID=:UserInfoUUID', [':UserInfoUUID'=>$userInfoUUID]);
        $accountUUIDs = array_column($data, 'AccountUUID');
        list($bindString, $bindArray) = \util\arr\getImplodeData($accountUUIDs);

        $data = $db->querySList("select B.ID,B.TimeZone,B.CustomizeForm,B.Account from Account A join PropertyMngList M on A.ID = M.PropertyID join Account B on B.ID = M.CommunityID where M.CommunityID = :CommunityID and A.UUID in ($bindString)",
        array_merge([":CommunityID"=>$communityId], $bindArray));
        if(count($data) == 0) $cMessage->echoErrorMsg(StateNotPermission);

        $gApp["userAliasId"] = $communityId;
        $gApp["userAlias"] = $data[0]["Account"];
        $customizeForm = $data[0]["CustomizeForm"];
        $timeZone = $data[0]["TimeZone"];
        \util\computed\setGAppData(["SelfCustomizeForm"=>$customizeForm,"SelfTimeZone"=>$timeZone]);
        $next();
    }
}