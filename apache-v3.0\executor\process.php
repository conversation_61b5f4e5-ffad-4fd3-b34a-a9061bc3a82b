<?php
/*
 * @Description: 流程执行者(@单例)，负责执行计划流程，整个系统的ioc容器
 * 是否有必要实现一个container，用来调用和移除类??
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-18 15:23:46
 * @LastEditors  : kxl
 * @LastEditTime : 2020-01-21 14:38:45
 */
namespace executor;
include_once __DIR__."/basic.php";
include_once __DIR__."/../interfaces/executor/main.php";
include_once __DIR__."/../container/data.php";
include_once __DIR__."/../util/string.php";
include_once __DIR__."/../container/model.php";

class CProcess extends \executor\Basic implements \interfaces\executor\main\IExecutor {
    // 执行计划，需要被解析
    public $plan;
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    public function parse () {
        global $gApp;
        $role = $gApp["role"];
        $plan = array_key_exists($role,$this->plan) ? $this->plan[$role] : (
            array_key_exists("common",$this->plan) ? $this->plan["common"] : $this->plan
        );
        $this->plan = $plan;
    }
    public function exec () {
        // 计划执行
        $plan = $this->plan;
        $this->exePlan($plan);
    }

    private function exePlan ($plan) {
        global $cLog,$gApp;
        $cLog->actionLog("#executor#process#exePlan#".json_encode($plan));
        $plan = is_array($plan) ? $plan : [];
        foreach ($plan as $step) {
            $type = $step["type"];
            // $cLog->actionLog("#executor#process#data#".json_encode($gApp));
            if($type === "model") {
                $this->exeModel($step);
            }elseif($type === "middle") {
                $this->exeMiddle($step);
            }elseif($type === "database") {
                $this->exeDatabase($step);
            }elseif($type === "event"){
               $this->exeEvent($step);
            }elseif($type === "branches"){
                $this->exeBranch($step);
            }elseif($type === "echo"){
                $this->exeEcho($step);
            }
        }   
    }

    // model解析
    private function exeModel ($step) {
        global $gApp,$cLog;
        $cLog->actionLog("#executor#process##exeModel#");
        // 模块调用
        list($modelName,$methodName) = \util\string\getModelMethod($step["model"]);
        // 暂时不调用通知
        // if($modelName == "notify") return;
        $modelContainer = \container\model::getInstance();
        // env设置为normal，表示最外层调用
        $model = $modelContainer->make($modelName,["env"=>"normal"]);
        // 设置数据容器
        $dataContainerName = array_key_exists("dataContainer",$step) ? $step["dataContainer"] : "common";
        // 设置数据容器数据
        $dataContainer = \container\setData($gApp["plan"]["data"],$dataContainerName);
        // 添加数据容器
        $modelContainer->setDataContainer($model,$dataContainer);

        // 从全局变量MODELS中获取注入的service和model
        $step = array_key_exists($step["model"],MODELS) ? MODELS[$step["model"]] : [];
        // 服务注入
        include_once __DIR__."/../container/service.php";
        $serviceContainer = \container\service::getInstance();
        // 不设置providers值默认为空数组
        $providers = array_key_exists("providers",$step) ? $step["providers"] : [];
        foreach($providers as $serviceName) {
            $serviceContainer->addService($model,$serviceName);
        }
        // model注入
        $models = array_key_exists("models",$step) ? $step["models"] : [];
        foreach($models as $addModelName) {
            // env设置为normal，表示模块内部调用
            $this->addModel($model,$addModelName,["env"=>"subModel"]);
        }

        $model->$methodName();
    }

    // 添加模块
    function addModel ($model,$addModelName) {
        $modelContainer = \container\model::getInstance();
        $provider = explode(".",$addModelName);
        // 返回的是一个sub 容器型的对象,实际使用model对象是sub里面的model属性
        $subModel = $modelContainer->addModels($model,$provider[0],$provider[1]);
        if(array_key_exists($addModelName,MODELS)) {
            $step = MODELS[$addModelName];
            
            $serviceContainer = \container\service::getInstance();
            $providers = array_key_exists("providers",$step) ? $step["providers"] : [];
            foreach($providers as $serviceName) {
                $serviceContainer->addService($subModel->model,$serviceName);
            }
            $models = array_key_exists("models",$step) ? $step["models"] : [];
            foreach($models as $addModelName) {
                $this->addModel($subModel->model,$addModelName);
            }
        }
    }

    private function exeMiddle ($step) {
        global $cLog;
        $cLog->actionLog("#executor#process#exeMiddle#");
        include_once __DIR__."/../container/middle.php";
        $middles = $step["queue"];
        \container\pipeFilter($middles);
    }

    private function exeDatabase ($step) {
        global $cLog;
        $cLog->actionLog("#executor#process#exeDatabase#");
        $method = $step["method"];
        include_once __DIR__."/../database/main.php";
        $db = \database\CDatabase::getInstance();
        $db->$method();
    }

    private function exeEvent ($step) {
        global $cLog;
        $cLog->actionLog("#executor#process#exeEvent#");
        include_once __DIR__."/../container/event.php";
        $eventContainer = \container\event::getInstance();
        $action = $step["action"];
        $params = $step["params"];
        $event = $step["event"];
        $dataContainerName =  array_key_exists("dataContainer",$step) ? $step["dataContainer"] : "common";
        $cLog->actionLog("#executor#process#exeEvent#dataContainerName=$dataContainerName");
        // 设置数据容器数据
        $dataContainer = \container\setData($gApp["plan"]["data"],$dataContainerName);
        $eventContainer->$action($event,$params,$dataContainer);
    }

    private function exeBranch ($step) {
        global $gApp,$cLog;
        $cLog->actionLog("#executor#process#exeBranch#");
        $branches = array_key_exists("branches",$gApp["plan"]) ? $gApp["plan"]["branches"] : [];
        foreach($branches as $val) {
            if(array_key_exists($val,$step["branches"])) {
                $branch = $step["branches"][$val];
                $this->exePlan($branch);
            }
        }  
    }

    private function exeEcho ($step) {
        global $gApp,$cMessage,$cLog;
        function getDataValue ($value,$keys) {
            if(!is_array($value)) throw new \Exception("$value is not array");
            $value = $value[$keys[0]];
            array_shift($keys);
            if(count($keys) == 0) return $value;
            else return getDataValue($value,$keys);
        }
        $cLog->actionLog("#executor#process#exeEcho#");
        $code = $step["code"];
        $options = array_key_exists("options",$step) ? $step["options"] : [];
        $params = array_key_exists("params",$step) ? $step["params"] : [];
        $echoOptions = [];
        foreach($options as $key=>$option) {
            if(is_string($option)) {
                $option = explode("->",$option);
                $echoOptions[$key] = getDataValue($gApp["plan"]["data"],$option);
            }
        }
        $echoParams = [];
        foreach ($params as $param) {
            array_push($echoParams,$gApp["plan"]["data"][$param]);
        }
        $cLog->actionLog("#executor#process#exeEcho#code=$code;echoOptions=".json_encode($echoOptions).";echoParams=".json_encode($echoParams));
        $cMessage->echoSuccessMsg($code,["options"=>$echoOptions],$echoParams);
    }
}
