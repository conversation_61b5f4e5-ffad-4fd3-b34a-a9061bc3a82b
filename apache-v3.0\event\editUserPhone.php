<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 17:14:52
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CEditUserPhone {
    private $phone;
    private $phone2;
    private $phone3;
    function on ($id) {
        global $cLog;
        $db = \database\CDatabase::getInstance();
        $data = $db->querySList("select Phone,Phone2,Phone3 from PersonalAccount where ID=:ID",[":ID"=>$id])[0];
        $cLog->actionLog("#event#editUserPhone.on#".json_encode($data));
        $this->phone = $data["Phone"];
        $this->phone2 = $data["Phone2"];
        $this->phone3 = $data["Phone3"];
    }
    function emit ($phone,$phone2,$phone3) {
        global $cLog;
        $cLog->actionLog("#event#editUserPhone.emit#phone=$phone;phone2=$phone2;phone3=$phone3");
        if($phone != $this->phone || $phone2 != $this->phone2 || $phone3 != $this->phone3) {
            include_once __DIR__."/../util/computed.php";
            \util\computed\setGAppBranch("changePhone");
            \util\computed\setGAppData(["PhoneOld"=>$this->phone,"Phone2Old"=>$this->phone2,"Phone3Old"=>$this->phone3]);
        }
    }
}