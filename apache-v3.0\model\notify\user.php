<?php

namespace model\notify;

trait user
{
    public function userCreateEmail()
    {
        $params = [
            "Account" => "",
            "Passwd" => "",
            "Email" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $password = $params["Passwd"];
        $email = $params["Email"];
        $this->log->actionLog("#model#notify#userCreateEmail#account=$account,email=$email");
        \perCreateUser($account, $password, $email);
    }

    public function userCreateComEmail()
    {
        $params = [
            "Account" => "",
            "Passwd" => "",
            "Email" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $password = $params["Passwd"];
        $email = $params["Email"];
        $this->log->actionLog("#model#notify#userCreateComEmail#account=$account,email=$email");
        \communityCreateUser($account, $password, $email);
    }

    public function userSubPerAdd()
    {
        $params = [
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $this->log->actionLog("#model#notify#userSubPerAdd#user=$user");
        \webPersonalModifyNotify(WEB_PER_ADD_SLAVE_USER, $user);
    }

    public function userSubComAdd()
    {
        global $gApp;
        $params = [
            "userAlias" => "",
            "userAliasId" => "",
            //从账户ID
            "ID" => "",
            "RoomID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $userId = $params["userAliasId"];
        $roomID = $params["RoomID"];

        $data = $this->db->querySList("select ParentID,UnitID from PersonalAccount where ID = :ID", [":ID" => $userId]);

        if ($roomID != '' && count($data) == 0) {
            $grade = $this->db->querySList("select Grade from Account where ID = :ID", [":ID" => $userId])[0]['Grade'];
            if ($grade == 21) {
                $mainData = $this->db->querySList(
                    "select ID,Account from PersonalAccount where ParentID = :ParentID AND RoomID = :RoomID AND Role = 20",
                    [":ParentID" => $userId, ":RoomID" => $roomID]
                )[0];
                $data = $this->db->querySList("select ParentID,UnitID from PersonalAccount where ID = :ID", [":ID" => $mainData['ID']]);
                $user = $mainData['Account'];
            }
        }
        $data = $data[0];
        $comMngId = $data["ParentID"];
        $buildId = $data["UnitID"];
        $this->log->actionLog("#model#notify#userSubComAdd#user=$user;comMngId=$comMngId;buildId=$buildId");
        \webCommunityModifyNotify(WEB_COMM_ADD_SLAVE_USER, $user, "", $comMngId, $buildId);
    }

    public function userMainPerAdd()
    {
        $params = [
            "Account" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["Account"];
        $userId = $params["ID"];
        $this->log->actionLog("#model#notify#userMainPerAdd#user=$user");
        \webPersonalModifyNotify(WEB_PER_ADD_USER, $user);
    }

    public function userMainComAdd()
    {
        $params = [
            "Account" => "",
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#userMainComAdd#param=" . json_encode($params));
        $user = $params["Account"];
        $userId = $params["ID"];
        $data = $this->db->querySList("select ParentID,UnitID from PersonalAccount where ID = :ID", [":ID" => $userId])[0];
        $comMngId = $data["ParentID"];
        $buildId = $data["UnitID"];
        $this->log->actionLog("#model#notify#userMainComAdd#user=$user;comMngId=$comMngId;buildId=$buildId");
        \webCommunityModifyNotify(WEB_COMM_ADD_USER, $user, "", $comMngId, $buildId);
    }

    public function userMainPerUpdate()
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]])[0];
        $account = $data["Account"];
        $this->log->actionLog("#model#notify#userMainPerUpdate#account=$account");
        \webPersonalModifyNotify(WEB_PER_MODIFY_USER, $account);
    }


    public function userSubPerUpdate()
    {
        $params = [
            // 从账户ID
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $data = $this->db->querySList("select A.Account from PersonalAccount A join PersonalAccount B on A.ID = B.ParentID where B.ID = :ID", [":ID" => $id])[0];
        $node = $data["Account"];
        $this->log->actionLog("#model#notify#userSubPerUpdate#node=$node");
        \webPersonalModifyNotify(WEB_PER_MODIFY_SLAVE_USER, $node);
    }

    public function userSubComUpdate()
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $mainData = $this->db->querySList("select A.Account,A.ParentID,A.UnitID from PersonalAccount A join PersonalAccount B on A.ID=B.ParentID where B.ID=:ID", [":ID" => $id])[0];
        $mainUser = $mainData["Account"];
        $comMngId = $mainData["ParentID"];
        $unitId = $mainData["UnitID"];
        $this->log->actionLog("#model#notify#userSubComUpdate#id=$id;mainUser=$mainUser;comMngId=$comMngId;unitId=$unitId");
        \webCommunityModifyNotify(WEB_COMM_MODIFY_SLAVE_USER, $mainUser, "", $comMngId, $unitId);
    }

    public function userMainComUpdate()
    {
        $params = [
            "ID" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $this->log->actionLog("#model#notify#userMainComUpdate#id=$id");
        $ids = explode(";", $id);
        foreach ($ids as $id) {
            $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $id]])[0];
            $account = $data["Account"];
            \webCommunityModifyNotify(WEB_COMM_MODIFY_USER, $account, "", $data["ParentID"], $data["UnitID"]);
        }
    }

    public function userChangePw()
    {
        $params = [
            "Account" => "",
            "Email" => "",
            "Passwd" => "",
            "unSend" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $email = $params["Email"];
        $password = $params["Passwd"];
        $unSend = $params["unSend"];
        $this->log->actionLog("#model#notify#userChangePw#account=$account;email=$email;unSend=$unSend");
        if ($unSend) {
            return;
        }
        \perChangePwd($account, $password, $email);
    }

    public function userMainPerDelete()
    {
        $params = [
            "Account" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $this->log->actionLog("#model#notify#userMainPerDelete#account=$account");
        \webPersonalModifyNotify(WEB_PER_DEL_USER, $account);
        $this->comDeleteFace();
    }

    public function userMainComDelete()
    {
        $params = [
            "data" => "",
            "macs" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $data = $params["data"];
        $macs = $params["macs"];
        $this->log->actionLog("#model#notify#userMainComDelete#");
        $macs = $macs ?: [];
        $macs = implode(";", $macs);
        \webCommunityModifyNotify(WEB_COMM_DEL_USER, $data["Account"], $macs, $data["ParentID"], $data["UnitID"]);
        $this->comDeleteFace();
    }

    public function userSubDelete()
    {
        $params = [
            // 主账户
            "userAlias" => "",
            "unitId" => "",
            // 被删除从账户数据
            "data" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mainUser = $params["userAlias"];
        $data = $params["data"];
        $mainUserData = $this->db->querySList("select ParentID,UnitID,Account from PersonalAccount where ID = :ID", [":ID" => $data['ParentID']])[0];
        $perComMngId = $mainUserData["ParentID"];
        $unitId = $mainUserData["UnitID"];
        $mainUser = $mainUserData["Account"];
        $this->log->actionLog("#model#notify#userSubDelete#");
        $role = $data["Role"];
        if ($role == PERENDSROLE) {
            \webPersonalModifyNotify(WEB_PER_DEL_SLAVE_USER, $mainUser, "", $perComMngId);
        } else {
            \webCommunityModifyNotify(WEB_COMM_DEL_SLAVE_USER, $mainUser, "", $perComMngId, $unitId);
        }
        $this->comDeleteFace();
    }

    public function userTimeZoneUpdate()
    {
        $params = [
            // 主账户
            "userAlias" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mainUser = $params["userAlias"];
        $mainUserId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#userTimeZoneUpdate#");
        $role = $this->db->querySList("select Role from PersonalAccount where ID=:ID", [":ID" => $mainUserId])[0]["Role"];
        if ($role == PERENDMROLE) {
            \webPersonalModifyNotify(WEB_PER_UPDATE_TIMEZONE, $mainUser);
        } else {
            // \webCommunityModifyNotify(WEB_COMM_NODE_UPDATE,$mainUser);
        }
    }

    public function userPerDataUpdate()
    {
        // V5.5修改未用户资料更改通知
        $params = [
            // 主账户
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->querySList("select ParentID,Role from PersonalAccount where Account = :Account", [":Account" => $user])[0];
        $this->log->actionLog("#model#notify#userPerDataUpdate#user=$user");
        if ($data["Role"] == PERENDMROLE) {
            \webPersonalModifyNotify(WEB_PER_MODIFY_USER, $user);
        } else {
            \webPersonalModifyNotify(WEB_PER_MODIFY_SLAVE_USER, $user);
        }
    }

    public function userNodeUpdate()
    {
        $params = [
            // 主账户
            "userAlias" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->querySList("select ParentID,Role from PersonalAccount where Account = :Account", [":Account" => $user])[0];
        if ($data["Role"] == PERENDMROLE) {
            $mainUser = $user;
        } else {
            $mainUser = $this->db->querySList("select Account from PersonalAccount where ID = :ID", [":ID" => $data["ParentID"]])[0]["Account"];
        }
        $this->log->actionLog("#model#notify#userNodeUpdate#mainUser=$mainUser");
        \webPersonalModifyNotify(WEB_PER_NODE_UPDATE, $mainUser);
    }

    public function userComDataUpdate()
    {
        $params = [
            "userAlias" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mainUser = $params["userAlias"];
        $mainUserId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#userComDataUpdate#params=" . json_encode($params));
        $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $mainUserId]])[0];
        if ($data["Role"] == COMENDSROLE) {
            $data = $this->db->queryAllList("PersonalAccount", ["equation" => [":ID" => $data["ParentID"]]])[0];
            $mainUser = $data["Account"];
        }
        \webCommunityModifyNotify(WEB_COMM_NODE_UPDATE, $mainUser, "", $data["ParentID"], $data["UnitID"]);
    }

    public function userMotionUpdate()
    {
        $params = [
            // 主账户
            "userAlias" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mainUser = $params["userAlias"];
        $mainUserId = $params["userAliasId"];
        $this->log->actionLog("#model#notify#userMotionUpdate#");
        $data = $this->db->querySList("select Role,ParentID,UnitID from PersonalAccount where ID=:ID", [":ID" => $mainUserId])[0];
        $role = $data["Role"];
        $communityId = $data["ParentID"];
        $unitId = $data["UnitID"];
        if ($role == PERENDMROLE) {
            \webPersonalModifyNotify(WEB_PER_NODE_UPDATE, $mainUser);
        } else {
            \webCommunityModifyNotify(WEB_COMM_NODE_UPDATE, $mainUser, "", $communityId, $unitId);
        }
    }

    public function userForgetPw()
    {
        $params = [
            // 主账户
            "account" => "",
            "email" => "",
            "token" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["account"];
        $email = $params["email"];
        $token = $params["token"];

        \perResetPwd($account, $email, $token);
    }



    public function createPropertyEmail()
    {
        $params = [
            // 主账户
            "Name" => "",
            "Email" => "",
            "Passwd" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $email = $params["Email"];
        $passwd = $params["Passwd"];
        $this->log->actionLog("#model#notify#createPropertyEmail#");
        \createPropertyWorkEmailNotify($email, $name, $passwd);
    }

    public function userSendCode()
    {
        $params = [
            "Type" => "",
            "MobileNumber" => "",
            "Code" => "",
            "Account" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $code = $params["Code"];
        // $areacode = $params["PhoneCode"];
        $phone = $params["MobileNumber"];
        $areacode = $this->db->querySList("select PhoneCode from PersonalAccount where MobileNumber=:MobileNumber", [":MobileNumber" => $phone])[0]['PhoneCode'];
        $this->log->actionLog("#model#notify#userSendCode#" . json_encode($params));
        \sendVerificationCode($type, $code, $areacode, $phone);
    }

    public function newSetUser()
    {
        $params = [
            "Account" => "",
            "AccessGroup" => "",
            "Node" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#newAddUser#param=" . json_encode($params));
        $accessGroup = $params['AccessGroup'] ? $params['AccessGroup'] : [];
        $node = $params['Node'] ? $params['Node'] : [];

        \webCommunityAccountModifyNotify($params['userAliasId'], $params['Account'], $accessGroup, $node);
        // 删除才传node
        if ($node) {
            foreach ($node as $val) {
                $unitID = $this->db->querySList(
                    'select UnitID from PersonalAccount where Account = :Account',
                    [':Account' => $val]
                )[0]['UnitID'];
                \webCommunityModifyNotify(WEB_COMM_MODIFY_USER, $val, "", $params['userAliasId'], $unitID);
            }
        }

        // 修改参数的通知，区分主从
        $data = $this->db->querySList(
            'select Role, UnitID, ParentID from PersonalAccount where Account = :Account',
            [':Account' => $params['Account']]
        )[0];
        if ($data['Role'] == COMENDMROLE) {
            \webCommunityModifyNotify(WEB_COMM_MODIFY_USER, $params['Account'], "", $params['userAliasId'], $data["UnitID"]);
        } elseif ($data['Role'] == COMENDSROLE) {
            $data = $this->db->querySList(
                'select Role, UnitID, Account from PersonalAccount where ID = :ID',
                [':ID' => $data['ParentID']]
            )[0];
            \webCommunityModifyNotify(WEB_COMM_MODIFY_SLAVE_USER, $data['Account'], "", $params['userAliasId'], $data["UnitID"]);
        }
    }
}
