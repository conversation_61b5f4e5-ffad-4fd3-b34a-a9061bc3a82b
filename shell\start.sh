#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   start.sh
# Version       :
# Description   :   web_backend 构建脚本
# Modifier      :   sicen 2024-02-20 V6.8 构建脚本整改为通用的common
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3              #需要部署的中间件
IMAGE_ACR="${12}"          #镜像仓库地址

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

###################### 环境、参数检测、变量定义 ######################
INSTALL_OPTIONS=$MIDDLEWARE
if [ -z "$INSTALL_OPTIONS" ];then
    echo "请选择需要部署的中间件";
    exit 1;
fi
# 必须以 root 身份运行 pm2，否则会另起一个 pm2 进程
ROOT_UID=0
if [ "$UID" -ne "$ROOT_UID" ]; then
    echo '必须以 root 身份运行此脚本'
    exit 1
fi

PKG_ROOT=$RSYNC_PATH
APP_NAME=akcs_web    # 安装包中 bin 目录下应用程序的文件名
DOWNLOAD_ROOT=/var/www


###################### 开始安装 ######################
echo "Begin to install $APP_NAME"
create_dirs="/var/www/upload/face /var/www/upload/feedback \
/var/www/download/UserDetail /var/www/download/UserMeta \
/var/www/download/template /var/www/download/DST \
/var/www/download/personal /var/www/download/community \
/var/www/download/tmp_key_qrcode /var/www/download/per_qrcode \
/var/www/download/versionfile /var/www/download/UserAll  \
/var/www/download/Schedule /usr/local/web"
for i in $create_dirs
do
    if [ ! -d "$i" ]; then
        mkdir -p "$i"
        chown nobody:nogroup -R "$i"
    fi
done

###################### 执行不同包的安装脚本 ######################
array=(${INSTALL_OPTIONS//,/ })
for var in ${array[@]}
do
    if [ "$var" == "webtask" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/webtask.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE
    elif [ "$var" == "apache" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/apache.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE
    elif [ "$var" == "webserver" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE webserver web-server $IMAGE_ACR
    elif [ "$var" == "openapi" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/openapi.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE $IMAGE_ACR
    elif [ "$var" == "wb_insapp" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE insapp wb_insapp $IMAGE_ACR
    elif [ "$var" == "smarthome2" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE smartHome2 smartHome2 $IMAGE_ACR
    elif [ "$var" == "web_office" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE office office $IMAGE_ACR
    elif [ "$var" == "web_community" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE community community $IMAGE_ACR
    elif [ "$var" == "web_single" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE single single $IMAGE_ACR
    elif [ "$var" == "web_log" ];then
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE log log $IMAGE_ACR
    elif [ "$var" == "download" ];then
        #注意：download 目录不能删除
        cp -rf "$PKG_ROOT"/download $DOWNLOAD_ROOT/
    else
        bash -x "$PKG_ROOT"/shell/package_start_shell/common.sh $PKG_ROOT $PROJECT_RUN_PATH $MIDDLEWARE $var $var $IMAGE_ACR
    fi
done


echo "$APP_NAME install complete."
