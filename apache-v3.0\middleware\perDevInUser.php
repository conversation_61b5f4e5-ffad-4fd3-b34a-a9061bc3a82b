<?php
/*
 * @Description: 设备是否在个人终端管理员下 参数ID
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 17:52:34
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/model.php";

class CPerDevInUser implements IMiddleware {
    public function handle (\Closure $next) {
        global $gApp,$cMessage;
        global $cLog;
        $user = $gApp["userAlias"];
        $db = \database\CDatabase::getInstance();
        $params = ["ID"=>""];
        $id = \util\model\getParamsFromContainer($params,$this->dataContainer)["ID"];
        $cLog->actionLog("#middle#perDevInUser#id=$id;user=$user");
        $data = $db->querySList("select ID from PersonalDevices where Node = :Node and ID = :ID",[":Node"=>$user,":ID"=>$id]);
        
        if(count($data) == 0) $cMessage->echoErrorMsg(StateNotPermission);
        $next();
    }
}