<?php
/*
 * @Description:
 * @version: V7.0
 * @Author: cj
 * @Date: 2021-12-24 18:12:31
 * @LastEditors: cj
 * @LastEditTime: 2023-05-23 17:44:15
 */
namespace package\common\model\projectData\method;

trait GetInfo
{
    /**
     * @author: cj
     * @Description: 获取当天开门次数
     * @param {*}
     * @return {*}
     */
    public function getTodayDoorForPM()
    {
        $params = ["SelfTimeZone",PROXY_ROLE['projectId']];
        list($timeZone,$officeId) = $this->getParams($params);
        
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($officeId);
        $hashKey = $projectData['UUID'];
        
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($hashKey);
        $tables = [$tables[0], $tables[1]];

        $now = $this->share->util->setTimeZone($this->share->util->getNow(), $timeZone, "", "+");
        $day = explode(" ", $now)[0];
        $startDay = $day . " 00:00:00";
        $baseSql = "select count(*) from %s where MngAccountID = :MngAccountID and Response = 0 and CaptureTime >= '" . $this->share->util->setTimeZone($startDay, $timeZone, "", "-") . "'";
        $bindArray = [":MngAccountID" => $officeId];
        $pinCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 2", $bindArray)[0]['count(*)'];
        $rfCardCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 3", $bindArray)[0]['count(*)'];
        $faceCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 4", $bindArray)[0]['count(*)'];
        $callCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))", $bindArray)[0]['count(*)'];
        $appCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 5", $bindArray)[0]['count(*)'];

        return [intval($pinCount), intval($rfCardCount), intval($faceCount), intval($callCount), intval($appCount)];
    }

    /**
     * @author: cj
     * @Description: 获取当月开门次数
     * @param {*}
     * @return {*}
     */
    public function getMonthDoorForPM()
    {
        $params = ["SelfTimeZone",PROXY_ROLE['projectId']];
        list($timeZone,$officeId) = $this->getParams($params);
        //时区计算
        $now = $this->share->util->getNow();
        $now = $this->share->util->setTimeZone($now, $timeZone, "", "-");
        $today = explode(" ", $now)[0] . " 00:00:00";

        $startTime = date("Y-m-d H:i:s", strtotime("$today - 30 day"));
        $this->loadProvider('captureUtil');
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($officeId);
        $tables = $this->services->captureUtil->getCaptureTables($projectData['UUID']);
        $currTables = [];
        foreach ($tables as $key => $table) {
            array_push($currTables, $table);
            if ($key >= 4) {
                break;
            }
        }
        $bindArray = [":MngAccountID" => $officeId];
        $baseSql = "select count(*) from %s where MngAccountID = :MngAccountID and Response = 0 and CaptureTime >='" . $startTime . "'";
        $pinSql = $baseSql . " and CaptureType = 2";
        $rfCardSql = $baseSql . " and CaptureType = 3";
        $faceSql = $baseSql . " and CaptureType = 4";
        $callSql = $baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))";
        $appSql = $baseSql . " and CaptureType = 5";

        $pinCount = $this->services->captureUtil->getCaptureResult($currTables, $pinSql, $bindArray)[0]['count(*)'];
        $rfCardCount = $this->services->captureUtil->getCaptureResult($currTables, $rfCardSql, $bindArray)[0]['count(*)'];
        $faceCount = $this->services->captureUtil->getCaptureResult($currTables, $faceSql, $bindArray)[0]['count(*)'];
        $callCount = $this->services->captureUtil->getCaptureResult($currTables, $callSql, $bindArray)[0]['count(*)'];
        $appCount = $this->services->captureUtil->getCaptureResult($currTables, $appSql, $bindArray)[0]['count(*)'];

        return [intval($pinCount), intval($rfCardCount), intval($faceCount), intval($callCount), intval($appCount)];
    }

    /**
     * @author: kzr
     * @Description: 新办公admin获取当天开门次数
     * @param {*}
     * @return {*}
     */
    public function getTodayDoorForAdmin()
    {
        $params = ["TimeZone",PROXY_ROLE['adminUUID']];
        list($timeZone,$adminUUID) = $this->getParams($params);
        
        $adminInfo = $this->dao->officeAdmin->selectByKey('AccountUUID',$adminUUID)[0];
        $officeId = $this->dao->account->selectByKey('UUID',$adminInfo['OfficeUUID'],'ID')[0]['ID'];

        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($officeId);
        $hashKey = $projectData['UUID'];
        
        $this->loadProvider('captureUtil');
        $tables = $this->services->captureUtil->getCaptureTables($hashKey);
        $tables = [$tables[0], $tables[1]];

        $now = $this->share->util->setTimeZone($this->share->util->getNow(), $timeZone, "", "+");
        $day = explode(" ", $now)[0];
        $startDay = $day . " 00:00:00";
        $baseSql = "select count(*) from %s where MngAccountID = :MngAccountID and OfficeCompanyUUID = :OfficeCompanyUUID and Response = 0 and CaptureTime >= '" . $this->share->util->setTimeZone($startDay, $timeZone, "", "-") . "'";
        $bindArray = [":MngAccountID" => $officeId,":OfficeCompanyUUID"=>$adminInfo['OfficeCompanyUUID']];
        $pinCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 2", $bindArray)[0]['count(*)'];
        $rfCardCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 3", $bindArray)[0]['count(*)'];
        $faceCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 4", $bindArray)[0]['count(*)'];
        $callCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))", $bindArray)[0]['count(*)'];
        $appCount = $this->services->captureUtil->getCaptureResult($tables, $baseSql . " and CaptureType = 5", $bindArray)[0]['count(*)'];

        return [intval($pinCount), intval($rfCardCount), intval($faceCount), intval($callCount), intval($appCount)];
    }

    /**
     * @author: kzr
     * @Description: 新办公admin获取当月开门次数
     * @param {*}
     * @return {*}
     */
    public function getMonthDoorForAdmin()
    {
        $params = ["TimeZone",PROXY_ROLE['adminUUID']];
        list($timeZone,$adminUUID) = $this->getParams($params);

        $adminInfo = $this->dao->officeAdmin->selectByKey('AccountUUID',$adminUUID)[0];
        $officeId = $this->dao->account->selectByKey('UUID',$adminInfo['OfficeUUID'],'ID')[0]['ID'];

        //时区计算
        $now = $this->share->util->getNow();
        $now = $this->share->util->setTimeZone($now, $timeZone, "", "-");
        $today = explode(" ", $now)[0] . " 00:00:00";

        $startTime = date("Y-m-d H:i:s", strtotime("$today - 30 day"));
        $this->loadProvider('captureUtil');
        $this->loadUtil('account', true);
        $projectData = $this->utils->_common->account->getManagerInfo($officeId);
        $tables = $this->services->captureUtil->getCaptureTables($projectData['UUID']);
        $currTables = [];
        foreach ($tables as $key => $table) {
            array_push($currTables, $table);
            if ($key >= 4) {
                break;
            }
        }
        $bindArray = [":MngAccountID" => $officeId,":OfficeCompanyUUID"=>$adminInfo['OfficeCompanyUUID']];
        $baseSql = "select count(*) from %s where MngAccountID = :MngAccountID and OfficeCompanyUUID = :OfficeCompanyUUID and Response = 0 and CaptureTime >='" . $startTime . "'";
        $pinSql = $baseSql . " and CaptureType = 2";
        $rfCardSql = $baseSql . " and CaptureType = 3";
        $faceSql = $baseSql . " and CaptureType = 4";
        $callSql = $baseSql . " and (CaptureType = 0 or (CaptureType > 5 and CaptureType < 9))";
        $appSql = $baseSql . " and CaptureType = 5";

        $pinCount = $this->services->captureUtil->getCaptureResult($currTables, $pinSql, $bindArray)[0]['count(*)'];
        $rfCardCount = $this->services->captureUtil->getCaptureResult($currTables, $rfCardSql, $bindArray)[0]['count(*)'];
        $faceCount = $this->services->captureUtil->getCaptureResult($currTables, $faceSql, $bindArray)[0]['count(*)'];
        $callCount = $this->services->captureUtil->getCaptureResult($currTables, $callSql, $bindArray)[0]['count(*)'];
        $appCount = $this->services->captureUtil->getCaptureResult($currTables, $appSql, $bindArray)[0]['count(*)'];

        return [intval($pinCount), intval($rfCardCount), intval($faceCount), intval($callCount), intval($appCount)];
    }

    /**
     * @author: cj
     * @Description: 获取高级功能的状态
     * @param {*} featureSwitch: 指高级功能的开关状态 hasFeaturePlan: 指高级功能某一功能是否开启
     *            isLimit: 指高级功能限制开关，true开，false关
     * @return {*} FeatureStatus 0:无高级功能 1:有该高级功能且未过期 2:有高级功能但过期 SwitchStatus 开关状态
     */
    public function getFeatureStatus()
    {
        $params = ['FeatureSwitch','HasFeaturePlan','FeatureExpireTime','IsLimit'];
        list($featureSwitch,$hasFeaturePlan,$featureExpireTime,$isLimit) = $this->getParams($params);
        $this->log->debug('params={params}', ["params"=>json_encode($this->getParams($params))]);
        $now = $this->share->util->getNow();
        if ($hasFeaturePlan == 0) {
            $featureStatus = 0;
            // 如果高级功能是限制功能允许创建，那么无高级功能时开关为on
            $switchStatus = $isLimit ? 1:0;
        } else {
            $switchStatus = $featureSwitch;
            if ($featureExpireTime == null) {
                $featureStatus = 0;
            } elseif (strtotime($featureExpireTime) >= strtotime($now)) {
                $featureStatus = 1;
            } else {
                $featureStatus = 2;
            }
        }
        return ['FeatureStatus' => $featureStatus, 'SwitchStatus' => $switchStatus];
    }

    /**
     * @description:处理高级功能的过期时间
     * @author:lwj 2023-03-30 19:41:16 V6.5.4
     * @lastEditor:lwj 2023-03-30 19:41:16 V6.5.4
     * @param:{date} FeatureExpireTime 项目的高级功能过期时间
     * @return string
     */
    public function getFeatureExpireTime()
    {
        $params = ['FeatureExpireTime'];
        list($featureExpireTime) = $this->getParams($params);
        //未开通或者永久展示--
        if($featureExpireTime === null || $featureExpireTime === DEFAULTEXPIRETIME || $featureExpireTime === DEFAULT_EMPTY_TIME){
            $featureExpireTime = '--';
        }
        return $featureExpireTime;
    }

    /**
     * @description:根据projectID获取管理该项目的所有PM数据，展示为一行
     * @param $id 项目ID
     * @return string
     * @author: shoubin.chen 2023-09-20 14:30:24 v6.7
     * @lastEditor: shoubin.chen 2023-09-20 14:30:24 v6.7
     */
    public function getWorks()
    {
        $params = ['ID:string-required'];
        list($id) = $this->getParams($params);
        $this->loadUtil('manage', true);
        $pmData = $this->utils->_common->manage->getPmListByProject($id);
        $works = [];
        foreach ($pmData as $val) {
            array_push($works, $val["FirstName"] . " " . $val["LastName"]);
        }

        $works = implode(",", $works);
        return $works;
    }

    public function getAllOfficeForIns()
    {
        $params = ['InsID'];
        list($insId) = $this->getParams($params);

        $tableAccount = PROXY_TABLES['account'];
        $tableOfficeInfo = PROXY_TABLES['officeInfo'];

        $sql = "select A.ID,A.Location as Name,A.UUID,A.Grade,A.Special,OI.IsNew from $tableAccount A left join $tableOfficeInfo OI 
                on OI.AccountUUID = A.UUID where Grade = 23 and A.ManageGroup = :ManageGroup";

        return $this->db->querySList($sql, [':ManageGroup' => $insId]);
    }

    /**
     * @description: 根据projectID获取管理该项目的数据以及对应info数据
     * @param {int} ID 项目ID
     * @return array
     * @throws \Exception
     * @author: csc 2024/8/19 14:17 V7.0.0
     * @lastEditors: csc 2024/8/19 14:17 V7.0.0
     */
    public function getInfo()
    {
        $params = ['ID'];
        list($id) = $this->getParams($params);
        $this->loadUtil('account', true);
        // 基础信息
        $basicData = $this->utils->_common->account->getManagerInfo($id);
        if ($basicData['Grade'] == COMMUNITYGRADE) {
            // 额外信息
            $projectInfo = $this->dao->communityInfo->selectByAccountID($id)[0];
        } else if ($basicData['Grade'] == OFFICEGRADE) {
            // 额外信息
            $projectInfo = $this->dao->officeInfo->selectByAccountUUID($basicData['UUID'])[0];
        }

        return ['basic' => $basicData, 'info' => $projectInfo];
    }
}
