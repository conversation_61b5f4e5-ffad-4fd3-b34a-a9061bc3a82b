<?php
namespace model\devicePersonal;

use function util\string\checkMAC;

trait update
{
    public function editPerLocation()
    {
        $params = [
            "ID"=>"",
            "Location"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $location = $params["Location"];
        $this->log->actionLog("#model#devicePersonal#editPerLocation#location=$location;id=$id");
        
        $this->db->update2ListWID("PersonalDevices", [":Location"=>$location,":ID"=>$id]);
        $this->log->endUserLog(4, $id, "modify location:$location");
    }

    public function editComLocation()
    {
        $params = [
            "ID"=>"",
            "Location"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $location = $params["Location"];
        $this->log->actionLog("#model#devicePersonal#editPerLocation#location=$location;id=$id");
        $this->db->update2ListWID("Devices", [":Location"=>$location,":ID"=>$id]);

        $data = $this->db->queryAllList("Devices", ["equation"=>[":ID"=>$id]])[0];
        \util\computed\setGAppData(["data"=>$data,"PcMngId"=>$data["MngAccountID"]]);
    }

    /**
     * @name: 设备配置
     */
    public function setDevConfig()
    {
        global $cMessage;
        $params = [
            "MAC"=>"",
            "SipType"=>"",
            "Config"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["MAC"];
        $sipType = $params["SipType"];
        $config = $params["Config"];
        $this->log->actionLog("#model#devicePersonal#editPerLocation#mac=$mac;sipType=$sipType;config=$config");
        
        $device = $this->db->queryAllList("Devices", ["equation"=>[":MAC"=>$mac]]);
        $table = "Devices";
        
        if (count($device) === 0) {
            $device = $this->db->queryAllList("PersonalDevices", ["equation"=>[":MAC"=>$mac]]);
            $table = "PersonalDevices";
            if (count($device) === 0) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
        }
        $this->db->update2ListWKey($table, [":MAC"=>$mac,":SipType"=>$sipType,":Config"=>$config], "MAC");
    }


    /**
     * @name: installer管理员修改设备
     * @service sip,rps
     */
    public function editForPerMng()
    {
        global $cMessage;
        $params = [
            "Type"=>"",
            "ID"=>"",
            "Location"=>"",
            "Node"=>"",
            "Relay"=>"",
            "MAC"=>"",
            "SecurityRelay"=>""
        ];

        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $type = $params["Type"];
        $id = $params["ID"];
        $Location = $params["Location"];
        $node = $params["Node"];
        $relay = $params["Relay"];
        $mac = $params["MAC"];
        $securityRelay = $params["SecurityRelay"];
        $mac = strtoupper($mac);

        //securityRelay的个数不能超过2个
        $securityRelayList = explode(';', $securityRelay);
        if (count($securityRelayList) > 2) {
            $cMessage->echoErrorMsg(STATE_PARAMS_ERROR);
        }

        // $relay = $type == 2 ? "" : $relay;
        $device = $this->db->queryAllList("PersonalDevices", ["equation"=>[":ID"=>$id]])[0];
        $node = $device["Node"];
        $oldType = $device["Type"];
        $sip = $device["SipAccount"];
        $enableGroup = $this->services["sip"]->getEnableGroup($node, $type);

        // 6.2个人设备可修改mac值
        // 判断是否为特殊设备
        $special = $this->db->querySList("select ID from DevicesSpecial where MAC = :MAC", [":MAC"=>$device["MAC"]]);
        if ($device['MAC'] == $mac) {
            // mac值不变，同原本逻辑
            $this->services["sip"]->addMul2Freeswish([["enableGroup"=>$enableGroup,"type"=>$type,"sip"=>$sip]]);
            if ($special) {
                $this->db->update2ListWID("PersonalDevices", [":ID"=>$id,":Location"=>$Location,":Relay"=>$relay,":SecurityRelay"=>$securityRelay]);
            } else {
                $this->db->update2ListWID("PersonalDevices", [":ID"=>$id,":Type"=>$type,":Location"=>$Location,":Relay"=>$relay,":SecurityRelay"=>$securityRelay]);
            }
            
            if ($oldType != $type) {
                $this->auditLog->setLog(AuditCodeDeviceTypeArray[$type], $this->env, [$device['MAC']], $node);
            }
            $this->models["notify"]->devPerUpdate();
        } else {
            // mac值变化，先删除原有设备，再新增新设备
            if ($special) {
                $this->db->delete2ListWKey("DevicesSpecial", "MAC", $device["MAC"]);
            }
            // 保存新mac值
            $resetParams = \util\model\saveParams();//保存
            \util\computed\setGAppData(["MAC" => $mac]);
            $this->models["devicePersonal"]->deletePer();
            $this->models["notify"]->devPerDelete();
            $resetParams();//重置

            // 走添加新设备路径
            $this->macCheck($mac);
            $this->devLocationCheck($Location);
            $this->models["devicePersonal"]->addForMng();
            // 加入devicespecial表
            if ($special) {
                $this->db->insert2List("DevicesSpecial", [
                    ":Account"=>$node,
                    ":MAC"=>$mac
                ]);
                // 6.4 kit方案用户设备MAC修改同步
                $isKit = $this->db->querySList('select ID from PendingRegUser where Account = :Account', [':Account' => $node]);
                if (count($isKit) > 0) {
                    $deviceFlags = $this->db->querySList('select Flags from PersonalDevices where MAC = :MAC for update', [':MAC' => $mac])[0]['Flags'];
                    $this->db->update2ListWKey("PersonalDevices", [":MAC"=>$mac,":Flags"=>\util\computed\bitOperation($deviceFlags, 1, 10)], "MAC");
                    $this->db->update2ListWID('PendingRegUser', [':ID' => $isKit[0]['ID'], ':MAC' => $mac]);
                }
            }
            $this->models["userData"]->afterPerAddDev();
            $this->models["notify"]->devPerAddForManage();
        }
    }

    public function macCheck($mac)
    {
        global $cMessage;
        $this->log->actionLog("#middle#macCheck#mac=".$mac);
        if (\util\string\checkMAC($mac)) {
            $cMessage->echoErrorMsg(StateMACLength);
        }
        if ($this->db->isExistFiled("Devices", [":MAC"=>$mac], null) || $this->db->isExistFiled("PersonalDevices", [":MAC"=>$mac], null)) {
            $cMessage->echoErrorMsg(StateMacExits);
        }
    }

    public function devLocationCheck($location)
    {
        global $cMessage;
        $this->log->actionLog("#middle#devLocationCheck#location=".$location);
        if (\util\string\checkByteLength($location, 63) || !$location) {
            $cMessage->echoErrorMsg(StateLocationLong);
        }
    }
}
