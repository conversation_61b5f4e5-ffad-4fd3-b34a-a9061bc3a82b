<?php
const AuditCodeLogin = 1;
const AuditCodeLogOut = 2;
const AuditCodeAddTemp = 3;
const AuditCodeEditTemp = 4;
const AuditCodeDeleteTemp = 5;
const AuditCodeAddRf = 6;
const AuditCodeEditRf = 7;
const AuditCodeDeleteRf = 8;
const AuditCodeAddPin = 9;
const AuditCodeEditPin = 10;
const AuditCodeDeletePin = 11;
const AuditCodeAddFamilyMaster = 12;
const AuditCodeEditFamilyMaster = 13;
const AuditCodeDeleteFamilyMaster = 14;
const AuditCodeAddPM = 15;
const AuditCodeEditPM = 16;
const AuditCodeDeletePM = 17;
const AuditCodeAddInstaller = 18;
const AuditCodeEditInstaller = 19;
const AuditCodeDeleteInstaller = 20;
const AuditCodeAddDis = 21;
const AuditCodeEditDis = 22;
const AuditCodeDeleteDis = 23;
const AuditCodeSelfTime = 24;
const AuditCodeSelfPassword = 25;
const AuditCodeImportFace = 26;
const AuditCodeDeleteFace = 27;
const AuditCodeDeleteDevice = 28;
const AuditCodeAPTCount = 29;
const AuditCodeEnableLandline = 30;
const AuditCodeDisableLandline = 31;
const AuditCodeSubManageTime = 32;
const AuditCodeChargeModelByInstaller = 33;
const AuditCodeChargeModelByUser = 34;
const AuditCodeConnectDefault = 35;
const AuditCodeConnectTCP = 36;
const AuditCodeConnectUDP = 37;
const AuditCodeConnectTLS = 38;
const AuditCodeAddCommunity = 39;
// const AuditCodeEditCommunity = 51;
const AuditCodeDeleteCommunity = 40;
const AuditCodeImportCommunity = 41;
const AuditCodeSetAptNumber = 42;
const AuditCodeUserEmail = 43;
const AuditCodeUserMobile = 44;
// const AuditCodeDeviceType = 45;
const AuditCodeDeviceNetGroup = 46;
const AuditCodeAddFamilyMember = 47;
const AuditCodeEditFamilyMember = 48;
const AuditCodeDeleteFamilyMember = 49;
const AuditCodeEditCommunity = 50;
const AuditCodeSetCommunityTime = 51;
const AuditCodeCommunityChargeModelByInstaller = 52;
const AuditCodeCommunityChargeModelByUser = 53;
const AuditCodeSetCallTypeSmartPlusIndoor = 54;
const AuditCodeSetCallTypePhoneIndoor = 55;
const AuditCodeSetCallTypeSmartPlusIndoorBackup = 56;
const AuditCodeSetCallTypeSmartPlusBackup = 57;
const AuditCodeSetCallTypeIndoorPhoneBackup = 58;
const AuditCodeSetCallTypeIndoorSmartPlusPhone = 59;
const AuditCodeDeviceTypeStair = 60;
const AuditCodeDeviceTypeDoor = 61;
const AuditCodeDeviceTypeIndoor = 62;
const AuditCodeDeviceTypeGuardPhone = 63;
const AuditCodeDeviceTypeAccessControl = 64;
const AuditCodeLoginWeb = 65;
const AuditCodeLogOutWeb = 66;
const AuditCodeLogEditApt = 67;
const AuditCodeManuallyUnlock = 70;
const AuditCodeManuallyLock = 71;

// 设备类型
const AuditCodeDeviceTypeArray = [
    "0"=>AuditCodeDeviceTypeStair,
    "1"=>AuditCodeDeviceTypeDoor,
    "2"=>AuditCodeDeviceTypeIndoor,
    "3"=>AuditCodeDeviceTypeGuardPhone,
    "50"=>AuditCodeDeviceTypeAccessControl
];

// calltype
const AuditCodeCallTypeArray = [
    AuditCodeSetCallTypeSmartPlusIndoor,
    AuditCodeSetCallTypePhoneIndoor,
    AuditCodeSetCallTypeSmartPlusIndoorBackup,
    AuditCodeSetCallTypeSmartPlusBackup,
    AuditCodeSetCallTypeIndoorPhoneBackup,
    AuditCodeSetCallTypeIndoorSmartPlusPhone
];

// login
const AuditCategoryLogin = [AuditCodeLogin, AuditCodeLogOut, AuditCodeLoginWeb, AuditCodeLogOutWeb];
const AuditCategoryAccess = [
    //temp key
    AuditCodeAddTemp, AuditCodeEditTemp, AuditCodeDeleteTemp,
    //rf card
    AuditCodeAddRf, AuditCodeEditRf, AuditCodeDeleteRf,
    //pin
    AuditCodeAddPin, AuditCodeEditPin, AuditCodeDeletePin,
    //face
    AuditCodeImportFace, AuditCodeDeleteFace,
    // 一键开/关门
    AuditCodeManuallyUnlock, AuditCodeManuallyLock
];
const AuditCategoryUser = [
    // 主账户
    AuditCodeAddFamilyMaster, AuditCodeEditFamilyMaster, AuditCodeDeleteFamilyMaster,
    // 从账户
    AuditCodeAddFamilyMember, AuditCodeEditFamilyMember, AuditCodeDeleteFamilyMember,
    // PM
    AuditCodeAddPM, AuditCodeEditPM, AuditCodeDeletePM,
    // installer
    AuditCodeAddInstaller, AuditCodeEditInstaller, AuditCodeDeleteInstaller,
    // dis
    AuditCodeAddDis, AuditCodeEditDis, AuditCodeDeleteDis,
    // 设置时区
    AuditCodeSelfTime, AuditCodeSubManageTime,
    //收费模式
    AuditCodeChargeModelByInstaller, AuditCodeChargeModelByUser,
    // 连接类型
    AuditCodeConnectDefault, AuditCodeConnectTCP, AuditCodeConnectUDP, AuditCodeConnectTLS,
    // 房间号
    AuditCodeSetAptNumber,
    // 邮箱，手机号
    AuditCodeUserEmail, AuditCodeUserMobile,
    // 更改密码
    AuditCodeSelfPassword
];

const AuditCategoryCommunity = [
    // call type
    AuditCodeSetCallTypeSmartPlusIndoor, AuditCodeSetCallTypePhoneIndoor, AuditCodeSetCallTypeSmartPlusIndoorBackup, AuditCodeSetCallTypeSmartPlusBackup, AuditCodeSetCallTypeIndoorPhoneBackup, AuditCodeSetCallTypeIndoorSmartPlusPhone,
    // 设置房间数量
    AuditCodeAPTCount,
    // 落地
    AuditCodeEnableLandline, AuditCodeDisableLandline,
    // 添加删除社区
    AuditCodeAddCommunity, AuditCodeDeleteCommunity, AuditCodeEditCommunity, AuditCodeImportCommunity,
    // 时区
    AuditCodeSetCommunityTime,
    // 收费模式
    AuditCodeCommunityChargeModelByInstaller, AuditCodeCommunityChargeModelByUser,
    AuditCodeLogEditApt
];

const AuditCategoryDevice = [
    AuditCodeDeviceNetGroup, AuditCodeDeleteDevice,
    AuditCodeDeviceTypeStair, AuditCodeDeviceTypeDoor, AuditCodeDeviceTypeIndoor, AuditCodeDeviceTypeGuardPhone, AuditCodeDeviceTypeAccessControl
];

const AuditWords = [
    AuditCodeLogin => MSGTEXT['auditLoginApp'],
    AuditCodeLogOut => MSGTEXT['auditLogoutApp'],
    AuditCodeLoginWeb => MSGTEXT['auditLogin'],
    AuditCodeLogOutWeb => MSGTEXT['auditLogout'],
    AuditCodeAddTemp => MSGTEXT['auditAddTempKey'],
    AuditCodeEditTemp => MSGTEXT['auditEditTempKey'],
    AuditCodeDeleteTemp => MSGTEXT['auditDeleteTempKey'],
    AuditCodeAddRf => MSGTEXT['auditAddRFCard'],
    AuditCodeEditRf => MSGTEXT['auditEditRFCard'],
    AuditCodeDeleteRf => MSGTEXT['auditDeleteRFCard'],
    AuditCodeAddPin => MSGTEXT['auditAddPIN'],
    AuditCodeEditPin => MSGTEXT['auditEditPIN'],
    AuditCodeDeletePin => MSGTEXT['auditDeletePIN'],
    AuditCodeAddFamilyMaster => MSGTEXT['auditAddEndUser'],
    AuditCodeEditFamilyMaster => MSGTEXT['auditEditEndUser'],
    AuditCodeDeleteFamilyMaster => MSGTEXT['auditDeleteEndUser'],
    AuditCodeAddPM => MSGTEXT['auditAddPM'],
    AuditCodeEditPM => MSGTEXT['auditEditPM'],
    AuditCodeDeletePM => MSGTEXT['auditDeletePM'],
    AuditCodeAddInstaller => MSGTEXT['auditAddInstaller'],
    AuditCodeEditInstaller => MSGTEXT['auditEditInstaller'],
    AuditCodeDeleteInstaller => MSGTEXT['auditDeleteInstaller'],
    AuditCodeAddDis => MSGTEXT['auditAddDis'],
    AuditCodeEditDis => MSGTEXT['auditEditDis'],
    AuditCodeDeleteDis => MSGTEXT['auditDeleteDis'],
    AuditCodeSelfTime => MSGTEXT['auditSetOwnerTime'],
    AuditCodeSelfPassword => MSGTEXT['auditSetOwnPassword'],
    AuditCodeImportFace => MSGTEXT['auditImportFace'],
    AuditCodeDeleteFace => MSGTEXT['auditDeleteFace'],
    AuditCodeDeleteDevice => MSGTEXT['auditDeleteDevice'],
    AuditCodeAPTCount => MSGTEXT['auditSetAPTCount'],
    AuditCodeEnableLandline => MSGTEXT['auditEnableLandline'],
    AuditCodeDisableLandline => MSGTEXT['auditDisableLandline'],
    AuditCodeSubManageTime => MSGTEXT['auditSetSubTime'],
    AuditCodeChargeModelByInstaller => MSGTEXT['auditSetChargeModeInstall'],
    AuditCodeChargeModelByUser => MSGTEXT['auditSetChargeModeUser'],
    AuditCodeConnectDefault => MSGTEXT['auditSetConnectTypeDefault'],
    AuditCodeConnectTCP => MSGTEXT['auditSetConnectTypeTCP'],
    AuditCodeConnectUDP => MSGTEXT['auditSetConnectTypeUDP'],
    AuditCodeConnectTLS => MSGTEXT['auditSetConnectTypeTLS'],
    AuditCodeAddCommunity => MSGTEXT['auditAddCommunity'],
    AuditCodeDeleteCommunity => MSGTEXT['auditDeleteCommunity'],
    AuditCodeImportCommunity => MSGTEXT['auditImportCommunity'],
    AuditCodeSetAptNumber => MSGTEXT['auditSetAPTNumber'],
    AuditCodeUserEmail => MSGTEXT['auditSetEmail'],
    AuditCodeUserMobile => MSGTEXT['auditSetMobile'],
    AuditCodeDeviceNetGroup => MSGTEXT['auditSetNetGroup'],
    AuditCodeAddFamilyMember => MSGTEXT['auditAddEndUser'],
    AuditCodeEditFamilyMember => MSGTEXT['auditEditEndUser'],
    AuditCodeDeleteFamilyMember => MSGTEXT['auditDeleteEndUser'],
    AuditCodeEditCommunity => MSGTEXT['auditEditCommunity'],
    AuditCodeSetCommunityTime => MSGTEXT['auditSetSubTime'],
    AuditCodeCommunityChargeModelByInstaller => MSGTEXT['auditSetChargeModeInstall'],
    AuditCodeCommunityChargeModelByUser => MSGTEXT['auditSetChargeModeUser'],
    AuditCodeSetCallTypeSmartPlusIndoor => MSGTEXT['auditSetCallTypeSmartPlusIndoor'],
    AuditCodeSetCallTypePhoneIndoor => MSGTEXT['auditSetCallTypePhoneIndoor'],
    AuditCodeSetCallTypeSmartPlusIndoorBackup => MSGTEXT['auditSetCallTypeSmartPlusIndoorBackup'],
    AuditCodeSetCallTypeSmartPlusBackup => MSGTEXT['auditSetCallTypeSmartPlusBackup'],
    AuditCodeSetCallTypeIndoorPhoneBackup => MSGTEXT['auditSetCallTypeIndoorPhoneBackup'],
    AuditCodeSetCallTypeIndoorSmartPlusPhone => MSGTEXT['auditSetCallTypeIndoorSmartPlusPhone'],
    AuditCodeDeviceTypeStair => MSGTEXT['auditDeviceTypeStair'],
    AuditCodeDeviceTypeDoor => MSGTEXT['auditDeviceTypeDoor'],
    AuditCodeDeviceTypeIndoor => MSGTEXT['auditDeviceTypeIndoor'],
    AuditCodeDeviceTypeGuardPhone => MSGTEXT['auditDeviceTypeGuardPhone'],
    AuditCodeDeviceTypeAccessControl => MSGTEXT['auditDeviceTypeAccessControl'],
    AuditCodeLogEditApt=>MSGTEXT['auditCodeLogEditApt'],
    AuditCodeManuallyUnlock=>MSGTEXT['auditCodeManuallyUnlock'],
    AuditCodeManuallyLock=>MSGTEXT['auditCodeManuallyLock']
];
