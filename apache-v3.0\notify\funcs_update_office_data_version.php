<?php

require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/funcs_common.php');
require_once(dirname(__FILE__) . '/utility.php');

function updateAllOfficeAccountDataVersion($mngid)
{
    $role = GetOfficeRoleStr();
    global $cLog;
    global $db;
    $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where ParentID=:MngID and Role in (:role)', [":MngID"=> $mngid, ":role"=> $role]);
    $cLog->TRACE("[updateAllOfficeAccountDataVersion] mngid=[" . $mngid . "]");
}

function updateOfficeAccountDataVersion($accounts)
{
    global $cLog;
    global $db;
    foreach ($accounts as $value) {
        $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where Account=:Account', [":Account"=> $value]);
        $cLog->TRACE("[updateOfficeAccountDataVersion] Account=[" . $value . "]");
    }
}

function updateOfficeDataVersionByAccessGroupID($ag_id)
{
    global $cLog;
    global $db;
	if (strlen($ag_id) > 0 )
	{
        $datas = $db->querySList("select DeliveryID from DeliveryAccess where AccessGroupID in ($ag_id)", []);
        $ids = DbData2Ids($datas, "DeliveryID");
        if (strlen($ids) > 0 )
        {
            $db->exec2ListWArray("update Delivery set Version=UNIX_TIMESTAMP() where ID in($ids)");
        }

        $datas = $db->querySList("select Account from AccountAccess where AccessGroupID in ($ag_id)", []);
        $accounts = DbData2Ids($datas, "Account");
        if (strlen($accounts) > 0 )
        {
            $sth = $db->exec2ListWArray("update PersonalAccount set Version=UNIX_TIMESTAMP() where Account in ($accounts)");
        }        
	}
    $cLog->TRACE("[updateOfficeDataVersionByAccessGroupID] PersonalAccount/Delivery: AccessGroupID ID=[$ag_id]"); 
}


//关联这个设备的权限组（包括默认权限组）和对应的用户数据版本都有修改
function updateOfficeDataVersionByUnitMac($unitid, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateOfficeDataVersionByUnitMac] unitid=$unitid mac=$mac");
    global $db;
    $datas = $db->querySList("select ID From AccessGroup where UnitID=:UnitID union select AccessGroupID AS ID From AccessGroupDevice where MAC=:MAC", [":UnitID"=>$unitid, ":MAC"=>$mac]);
    updateOfficeDataVersionByAccessGroupID(DbData2Ids($datas, "ID"));
}

function updateOfficeDataVersionByPubMac($communityid, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateOfficeDataVersionByPubMac] communityid=$communityid mac=$mac");
    global $db;
    $datas = $db->querySList("select ID From AccessGroup where UnitID != 0 and CommunityID=:CommunityID union select AccessGroupID AS ID From AccessGroupDevice where MAC=:MAC", [":CommunityID"=>$communityid, ":MAC"=>$mac]);
    updateOfficeDataVersionByAccessGroupID(DbData2Ids($datas, "ID"));
}

/*更新住户下所有用户的数据版本*/
function updateOfficeDataVersionByPerMac($node, $mac)
{
    global $cLog;
    $cLog->TRACE("[updateOfficeDataVersionByPerMac] node=$node mac=$mac");
    global $db;
    $db->exec2ListWArray('update PersonalAccount set Version=UNIX_TIMESTAMP() where Account=:node', [":node"=> $node]);
}
