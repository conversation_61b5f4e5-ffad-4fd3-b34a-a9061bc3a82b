<?php

namespace plan\process;

const USER_PROCESS = [
    // 终端用户网页登陆
    "userLogin" => [
        [
            "type" => "middle",
            "queue" => [
                [
                    "name" => "loginCheck",
                    "params" => ["Account", "passwd"],
                ]
            ]
        ], [
            "type" => "model",
            "model" => "login.endUserLogin"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["token" => "token", "name" => "name", "role" => "role", "UID" => "UID", "email" => "email", "timeZone" => "timeZone"]
        ],

    ],

    // 终端用户网页登陆
    "userLoginCode" => [
        [
            "type" => "middle",
            "queue" => [
                [
                    "name" => "loginCodeCheck",
                ]
            ]
        ], [
            "type" => "model",
            "model" => "login.endUserLogin"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["token" => "token", "name" => "name", "role" => "role", "UID" => "UID", "email" => "email", "timeZone" => "timeZone"]
        ],

    ],

    // 个人主账户添加从账户
    "addPSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "maxSubUserCheck"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addPerSubApp",
        ], [
            "type" => "model",
            "model" => "user.afterAddPerSubUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateEmail",
        ], [
            "type" => "model",
            "model" => "notify.userSubPerAdd",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd,
            "options" => ["id" => "ID", "active" => "Active"]
        ]
    ],
    // 社区主账户添加从账户
    "addCSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "maxSubUserCheck"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addComSubUser",
        ], [
            "type" => "branches",
            "branches" => [
                "addAccess" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "changeParamValue", "params" => ["Step" => 0]]
                        ]
                    ], [
                        "type" => "model",
                        "model" => "access.addUser"
                    ], [
                        "type" => "model",
                        "model" => "notify.newSetUser",
                        "dataContainer" => "setCommunityIDToAlias"
                    ],
                ]
            ]
        ], [
            "type" => "model",
            "model" => "user.afterAddComSubUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateComEmail",
        ], [
            "type" => "model",
            "model" => "notify.userSubComAdd",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd,
            "options" => ["id" => "ID", "active" => "Active"]
        ]
    ],

    // App社区主账户添加从账户
    "addAppCSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "maxSubUserCheck"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addAppComSubUser",
        ], [
            "type" => "branches",
            "branches" => [
                "addAccess" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "changeParamValue", "params" => ["Step" => 0]]
                        ]
                    ], [
                        "type" => "model",
                        "model" => "access.addUser"
                    ], [
                        "type" => "model",
                        "model" => "notify.newSetUser",
                        "dataContainer" => "setCommunityIDToAlias"
                    ],
                ]
            ]
        ], [
            "type" => "model",
            "model" => "user.afterAddComSubUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateComEmail",
        ], [
            "type" => "model",
            "model" => "notify.userSubComAdd",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd,
            "options" => ["id" => "ID", "active" => "Active"]
        ]
    ],

    // 添加个人主账户
    "addPMUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addPerMainUser"
        ], [
            "type" => "branches",
            "branches" => [
                "addIndoorMonitor" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "getAliasId"],
                            ["name" => "macCheck", "params" => "MAC"],
                            ["name" => "devLocationCheck", "params" => "Location"],
                        ]
                    ], [
                        "type" => "model",
                        "model" => "devicePersonal.addForMng"
                    ], [
                        "type" => "model",
                        "model" => "userData.afterPerAddDev"
                    ], [
                        "type" => "model",
                        "model" => "notify.devPerAddForManage"
                    ]
                ]
            ]
        ], [
            "type" => "model",
            "model" => "user.afterAddPerMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateEmail",
        ], [
            "type" => "model",
            "model" => "notify.userMainPerAdd",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],

    // 删除个人主账户
    "deletePMUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "haveSubUserDeleteCheck", "params" => "ID"],
                // ["name"=>"recordDelData","params"=>["ID",["name"=>"table","type"=>"const","value"=>"PersonalAccount"]]],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.delete",
        ], [
            "type" => "model",
            "model" => "user.afterDeletePerMain",
        ], [
            "type" => "model",
            "model" => "user.afterDeleteMain",
        ], [
            "type" => "model",
            "model" => "user.afterDelete"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "notify.userMainPerDelete",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],

    // 删除从账户
    "deleteSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                // ["name"=>"recordDelData","params"=>["ID",["name"=>"table","type"=>"const","value"=>"PersonalAccount"]]],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.delete",
        ], [
            "type" => "model",
            "model" => "user.afterDeleteSub"
        ], [
            "type" => "model",
            "model" => "user.afterDelete"
        ], [
            "type" => "model",
            "model" => "notify.userSubDelete"
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
            "dataContainer" => "setCommunityIDToAlias"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],

    // 更新个人主账户
    "updatePMUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "phoneDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
                ["name" => "getUserAccount"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updatePerMain",
        ], [
            "type" => "branches",
            "branches" => [
                "addIndoorMonitor" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "getAliasId"],
                            ["name" => "devLocationCheck", "params" => "Location"],
                        ]
                    ], [
                        "type" => "model",
                        "model" => "devicePersonal.addForMng"
                    ], [
                        "type" => "model",
                        "model" => "userData.afterPerAddDev"
                    ]
                ]
            ]
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => ["ID", "Email"],
            "action" => "emit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "branches",
            "branches" => [
                "addIndoorMonitor" => [
                    [
                        "type" => "model",
                        "model" => "notify.devPerAddForManage"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "branches",
            "branches" => [
                "changeEmail" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateEmail"
                    ], [
                        "type" => "model",
                        "model" => "notify.userCreateEmail",
                    ],
                ],
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "model",
            "model" => "user.afterUpdatePerMain"
        ], [
            "type" => "model",
            "model" => "notify.userMainPerUpdate"
        ],
    ],
    // 更新个人从账户
    "updatePSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "phoneDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserName",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updatePerSub",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "event",
            "event" => "editUserName",
            "params" => "Name",
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeName" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePerSName"
                    ]
                ],
            ]
        ], [
            "type" => "model",
            "model" => "notify.userSubPerUpdate"
        ]
    ],
    // 更新社区从账户
    "updateCSUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "firstLastNameDeal"],
                ["name" => "phoneDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserName",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updateComSub",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "event",
            "event" => "editUserName",
            "params" => "Name",
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeName" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateComSName"
                    ]
                ],
            ]
        ], [
            "type" => "model",
            "model" => "notify.userSubComUpdate"
        ]
    ],

    // installer查询个人主账户
    "getPerMainUserFormPMng" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getMngTimeZone"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryPerMain"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 个人终端账户获取个人从账户
    "getPerSubUserFormUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryPerSub"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 社区终端账户获取社区从账户
    "getComSubUserFormUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryComSub"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 获取用户详情
    "getUserInfo" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getMngTimeZone"]
            ]
        ], [
            "type" => "model",
            "model" => "user.queryInfo"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 修改用户密码
    "changeUserPw" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.changePw"
        ], [
            "type" => "model",
            "model" => "user.deleteUserToken"
        ], [
            "type" => "model",
            "model" => "user.deleteUserToken"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StatePasswdChange
        ], [
            "type" => "model",
            "model" => "user.afterChangePw"
        ], [
            // "type"=>"model",
            // "model"=>"notify.userChangePw"
        ]
    ],

    // 设置主账户过期时间
    "setMainUserExpireTime" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getMngTimeZone"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.setExpireTime"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    // 检查密码正确
    "checkUserPw" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.checkPw"
        ], [
            "type" => "echo",
            "code" => StatePasswordCorrect
        ],
    ],

    // 修改用户时区
    "changeMainUserTimeZone" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.changeTimeZone"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "model",
            "model" => "notify.userTimeZoneUpdate"
        ],
    ],
    // 修改用户名
    "changePerUserName" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.changePerName"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "model",
            "model" => "notify.userPerDataUpdate"
        ],
    ],
    // 通话记录
    "queryCallForWeb" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryCallHistoryForWeb"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    "queryUserOwnerData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryOwernData"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    "changeUserData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "userNameCheck", "params" => "Name"],
                ["name" => "phoneDeal", "params" => ["Phone", "Phone2", "Phone3", "PhoneCode"]],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "model",
            "model" => "userData.changePerData",

        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "branches",
            "branches" => [
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "model",
            "model" => "notify.userPerDataUpdate"
        ],
    ],
    // ##########APP##########
    // 获取家庭账户列表
    "getUserFromApp" => [
        [
            "type" => "model",
            "model" => "user.queryFromApp"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 获取一个家庭下所有从账户
    "queryAApps" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"]
            ]
        ], [
            "type" => "model",
            "model" => "user.queryAllSubApps"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 获取一个家庭下所有账户Name和ID
    "queryFamilyUser" => [
        [
            "type" => "model",
            "model" => "user.queryFamilyUser"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],

    // 通话记录
    "queryCallForApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryCallHistory"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryActiveUnReadForApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryActiveUnRead"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["callCount" => "callCount", "motionCount" => "motionCount"]
        ],
    ],
    "setPhoneRead" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.setPhoneRead"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "setMotionRead" => [
        [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.setMotionRead"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "queryAllAD" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryAllAD"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["apps" => "apps", "doors" => "doors", "indoors" => "indoors"]
        ],
    ],
    "queryUserBill" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryBill"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "editUserBill" => [
        [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.modifyBill"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit,
        ],
    ],
    "closeAccount" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "userPwCheck"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.closeAccount"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],
    "getCnf" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryCnf"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getCallQueue" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryCallSqueue"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "setCall" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.setCall"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "setMotion" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.setMotion"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "model",
            "model" => "notify.userMotionUpdate"
        ]
    ],
    "setInit" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal", "params" => ["Phone", "Phone2", "Phone3", "PhoneCode"]],
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "dataContainer" => "setAliasIdToId",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.setInit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "model",
            "model" => "user.afterChangePw"
        ], [
            // 不发邮件
            // "type"=>"model",
            // "model"=>"notify.userChangePw"
        ], [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ],
    ],
    "getPerData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getPerData"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getComData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getComData"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getComSubData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getComSubData"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "setComData" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "firstLastNameDeal", "params" => ["FirstName", "LastName"]],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "dataContainer" => "setAliasIdToId",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.setData"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "model",
            "model" => "notify.priKeyComUpdate"
        ], [
            "type" => "model",
            "model" => "notify.userComDataUpdate"
        ]
    ],
    "getExpireDay" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getExpireDay"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["day" => "Day", "expiretime" => "ExpireTime"]
        ],
    ],
    "getLandlineTime" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getLandlineTime"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["day" => "Day", "expiretime" => "ExpireTime"]
        ],
    ],
    "TempKeyPermission" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getTempKeyOpera"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["TempKeyPermission" => "TempKeyPermission"]
        ]
    ],
    "getUserChargeMode" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getChargeMode"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["ChargeMode" => "ChargeMode", "Subscriptions" => "Subscriptions"]
        ]
    ],
    "getLandlineCharge" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getLandlineChargeRes"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["LandlineChargeRes" => "LandlineChargeRes", "LandlineStatus" => "LandlineStatus"]
        ]
    ],
    "getUserCommunityName" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryCommunityName"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["Location" => "Location"]
        ]
    ],
    "getNextExpiretime" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.getNextExpireTime"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["time" => "ExpireTime"]
        ]
    ],
    // TODO 通知平台参数需要datacontainer
    "register" => [
        [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "login.register"
        ], [
            "type" => "model",
            "model" => "devicePersonal.addDevWCode"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "login.afterRegister"
        ], [
            "type" => "model",
            "model" => "user.afterAddPerMainUser"
        ], [
            "type" => "model",
            "model" => "user.afterAddMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateEmail",
        ], [
            "type" => "model",
            "model" => "notify.userMainPerAdd",
        ], [
            "type" => "echo",
            "code" => StateSuccessRegister
        ]
    ],
    "emailCheckValid" => [
        [
            "type" => "model",
            "model" => "login.emailCheckValid",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery
        ]
    ],
    "getVerCode" => [
        [
            "type" => "model",
            "model" => "login.getVerCode",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery
        ]
    ],
    "deviceCodeCheck" => [
        [
            "type" => "model",
            "model" => "login.deviceCodeCheck",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery
        ]
    ],
    "sendRsPwEmail" => [
        [
            "type" => "model",
            "model" => "login.sendEmail",
        ], [
            "type" => "echo",
            "code" => StateSuccessSendEmail,
            "options" => ["TipCode" => "TipCode"]
        ], [
            "type" => "model",
            "model" => "notify.userForgetPw",
        ],
    ],
    "resetPwUserPw" => [
        [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "login.resetPw",
        ], [
            "type" => "model",
            "model" => "user.deleteUserToken",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit,
        ],
        // TODO notify
    ],
    "getResetPwName" => [
        [
            "type" => "model",
            "model" => "login.getName",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getUserLog" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getUserTimeZone"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "log.query",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "addComMainUserForCom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addComMainUser"
        ], [
            "type" => "branches",
            "branches" => [
                "addAccess" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "changeParamValue", "params" => ["Step" => 0]]
                        ]
                    ], [
                        "type" => "model",
                        "model" => "access.addUser"
                    ], [
                        "type" => "model",
                        "model" => "notify.newSetUser",
                    ],
                ]
            ]
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterAddComMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateComEmail",
        ], [
            "type" => "model",
            "model" => "notify.userMainComAdd",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "addComRoom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
                ["name" => "userMonitorDeal"],
                ["name" => "macCheck"],
                ["name" => "devLocationCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "event",
            "event" => "addRoomUser",
            "action" => "on"
        ], [
            "type" => "model",
            "model" => "user.addRoom"
        ], [
            "type" => "branches",
            "branches" => [
                "addAccess" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "changeParamValue", "params" => ["Step" => 0]]
                        ]
                    ], [
                        "type" => "model",
                        "model" => "access.addUser"
                    ], [
                        "type" => "model",
                        "model" => "notify.newSetUser",
                    ],
                ]
            ]
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterAddComMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userMainComAdd",
        ], [
            "type" => "event",
            "event" => "addRoomUser",
            "params" => ["IsAddUser"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "addRoomUser" => [
                    [
                        "type" => "model",
                        "model" => "notify.userCreateComEmail"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "eidtComMainUserFromCom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "mainUserInPCMngCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updateComMainUser",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterUpdateComMain",
        ], [
            "type" => "model",
            "model" => "notify.userMainComUpdate",
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => ["ID", "Email"],
            "action" => "emit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeEmail" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateEmail"
                    ], [
                        "type" => "model",
                        "model" => "notify.userCreateComEmail"
                    ]
                ],
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "eidtRoom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "mainUserInPCMngCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
                ["name" => "userMonitorDeal"],
                ["name" => "devLocationCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updateRoom"
        ], [
            "type" => "branches",
            "branches" => [
                "addAccess" => [
                    [
                        "type" => "middle",
                        "queue" => [
                            ["name" => "changeParamValue", "params" => ["Step" => 0]]
                        ]
                    ], [
                        "type" => "model",
                        "model" => "access.addUser"
                    ], [
                        "type" => "model",
                        "model" => "notify.newSetUser",
                    ],
                ]
            ]
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterUpdateComMain",
        ], [
            "type" => "model",
            "model" => "notify.userMainComUpdate",
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => ["ID", "Email"],
            "action" => "emit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeEmail" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateEmail"
                    ], [
                        "type" => "model",
                        "model" => "notify.userCreateComEmail",
                    ],
                ],
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "deleteComUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.deleteComMainUser"
        ], [
            "type" => "model",
            "model" => "user.deleteFace"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ], [
            "type" => "model",
            "model" => "notify.userMainComUpdate",
        ], [
            "type" => "model",
            "model" => "notify.comDeleteFace",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ],
    ],
    "betchDeleteComUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.betchDeleteComMainUser"
        ], [
            "type" => "model",
            "model" => "user.deleteFace"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ], [
            "type" => "model",
            "model" => "notify.userMainComUpdate",
        ], [
            "type" => "model",
            "model" => "notify.comDeleteFace",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ],
    ],
    "deleteRoom" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
                ["name" => "haveSubUserDeleteCheck", "params" => "ID"],
                // ["name"=>"recordDelData","params"=>["ID",["name"=>"table","type"=>"const","value"=>"PersonalAccount"]]],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.deleteRoom",
            "models" => ["deviceCommunity.delete"]
        ], [
            "type" => "model",
            "model" => "user.afterDeleteRoom",
        ], [
            "type" => "model",
            "model" => "user.afterDeleteMain",
        ], [
            "type" => "model",
            "model" => "user.afterDelete",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ], [
            "type" => "model",
            "model" => "notify.userMainComDelete",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
            "dataContainer" => "setAccountsToAccount"
        ],
    ],
    "queryAllBuildComUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryAllBuildComUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryComUserInfoForMng" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryComUserInfoForMng",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryComUserForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryComUserForPM",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryComSubForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
            ]
        ], [
            "type" => "model",
            "model" => "user.queryComSubForPM",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "queryUserInfoForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "mainUserInPCMngCheck", "params" => "ID"],
            ]
        ], [
            "type" => "model",
            "model" => "user.getInfoForPM",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "addComMainForPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addComMainForPM",

        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterAddComMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateEmail",
        ], [
            "type" => "model",
            "model" => "notify.userMainComAdd",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "eidtComMainUserFromPM" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "mainUserInPCMngCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.updateComMainUserForPM",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterUpdateComMain",
        ], [
            "type" => "model",
            "model" => "notify.userMainComUpdate",
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => ["ID", "Email"],
            "action" => "emit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeEmail" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateEmail"
                    ], [
                        "type" => "model",
                        "model" => "notify.userCreateEmail",
                    ],
                ],
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "getComActiveOrUn" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.getComActiveOrUn"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data", "total" => "total"]
        ],
    ],
    "getPerActiveOrUn" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "user.getPerActiveOrUn"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data", "total" => "total"]
        ],
    ],
    "getComNextExpireTimeUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"],
            ]
        ], [
            "type" => "model",
            "model" => "user.getComNextExpireTimeUser"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getLandlineExpireTimeUsers" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"],
            ]
        ], [
            "type" => "model",
            "model" => "user.getLandlineExpireTimeUsers"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getSubForMng" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"],
            ]
        ], [
            "type" => "model",
            "model" => "user.querySubForMng"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "activeAccount" => [
        [
            "type" => "model",
            "model" => "user.activeAccount"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "queryMainUserForSupArea" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"]
            ]
        ], [
            "type" => "model",
            "model" => "user.queryMainForSupArea"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getAccount" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getUserTimeZone"],
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryAccount"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "getAllPerMainUserForPerMng" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ],
        ], [
            "type" => "model",
            "model" => "user.queryAllPerMainUser"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getComMainUserForMng" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"]
            ],
        ], [
            "type" => "model",
            "model" => "user.queryComUser"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ],
    ],
    "getDisUserActiveOrUn" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
                ["name" => "getMngTimeZone"]
                // ["name"=>"manageInAMngCheck"]
            ]
        ], [
            "type" => "event",
            "event" => "getPCBranch",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "getPCBranch",
            "params" => ["Manage"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "getPerBranch" => [
                    [
                        "type" => "model",
                        "model" => "user.getPerActiveOrUn",
                        "dataContainer" => "setManageToAliasId"
                    ]
                ],
                "getComBranch" => [
                    [
                        "type" => "model",
                        "model" => "user.getComActiveOrUn",
                        "dataContainer" => "setManageToAliasId"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data", "total" => 'total']
        ]
    ],
    "getDisExpireTimeUsers" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getMngTimeZone"],
                ["name" => "getAliasId"],
                // ["name"=>"manageInAMngCheck"]
            ]
        ], [
            "type" => "event",
            "event" => "getPCBranch",
            "action" => "on"
        ], [
            "type" => "event",
            "event" => "getPCBranch",
            "params" => ["Manage"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "getPerBranch" => [
                    [
                        "type" => "model",
                        "model" => "user.getLandlineExpireTimeUsers"
                    ]
                ],
                "getComBranch" => [
                    [
                        "type" => "model",
                        "model" => "user.getComNextExpireTimeUser"
                    ]
                ]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "setUserLang" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "userData.setLang"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],
    "getFreeSubApp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryFreeSubApp"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "resetUserPw" => [
        [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.resetPw"
        ], [
            "type" => "echo",
            "code" => StateSuccessChangePw,
            "params" => ["Email"]
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterChangePw"
        ], [
            "type" => "model",
            "model" => "notify.userChangePw"
        ]
    ],
    "setMianUserExpieTime" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getMngTimeZone"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.setExpireTime"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ],
    ],
    "setPerPhone" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => "ID",
            "dataContainer" => "setAliasIdToId",
            "action" => "on"
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "userData.setPerPhone"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "event",
            "event" => "editUserPhone",
            "params" => ["Phone", "Phone2", "Phone3"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changePhone" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdatePhone"
                    ]
                ]
            ]
        ], [
            "type" => "model",
            "model" => "notify.userPerDataUpdate"
        ]
    ],
    "sendmobilevercodeapp" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "userMobileDeal"]
            ]
        ], [
            "type" => "model",
            "model" => "login.sendCode"
        ], [
            "type" => "model",
            "model" => "notify.userSendCode"
        ], [
            "type" => "echo",
            "code" => StateSuccessSendCode
        ]
    ],
    "checkverifycode" => [
        [
            "type" => "model",
            "model" => "login.checkCode"
        ], [
            "type" => "echo",
            "code" => StateCorrectCode,
            "options" => ["data" => "data"]
        ]
    ],
    "getNewPMUserList" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "user.queryNewPMList"
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],
    "updateBillList" => [
        [
            "type" => "model",
            "model" => "user.updateBillList"
        ], [
            "type" => "echo",
            "code" => StateSuccessUpdate,
            "options" => ["data" => "data"]
        ],
    ],
    // kit注册用户
    "registerPMUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.registerPerMainUser"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ], [
            "type" => "model",
            "model" => "notify.userMainPerUpdate"
        ],
    ],
];
