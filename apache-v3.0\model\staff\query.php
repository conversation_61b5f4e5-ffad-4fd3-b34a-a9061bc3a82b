<?php

namespace model\staff;

trait query
{
    public function query($type)
    {
        $params = [
            "userAliasId" => "",
            "SelfTimeZone" => "",
            "SelfCustomizeForm" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        list($offset, $rows, $searchKey, $searchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        list($data, $count) = getList([
            "CommunityID" => $params["userAliasId"],
            "SelfTimeZone" => $params["SelfTimeZone"],
            "SelfCustomizeForm" => $params["SelfCustomizeForm"],
            "Row" => $rows,
            "Offset" => $offset,
            "SearchKey" => $searchKey,
            "SearchValue" => $searchValue
        ], $type);

        \util\computed\setGAppData(["data" => ['row' => $data, 'detail' => $data, 'total' => $count]]);
    }

    public function queryDelivery()
    {
        $this->query(0);
    }

    public function queryStaff()
    {
        $this->query(1);
    }

    public function queryInfo($type)
    {
        $params = [
            "ID" => "",
            "userAliasId" => "",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $communityId = $params['userAliasId'];
        $id = $params['ID'];

        checkValid($id, $communityId, $type);
        $data = getInfo($id, $type);
        if ($type === 0 || $type === 1) {
            $data['PinCode'] = \util\computed\setPinIsEncryptPin($communityId, $data['PinCode']);
        }
        \util\computed\setGAppData(["data" => $data]);
    }

    public function deliveryInfo()
    {
        $this->queryInfo(0);
    }

    public function staffInfo()
    {
        $this->queryInfo(1);
    }
}
