<?php
  const MSGTEXT = [ 

"accountExits"=>"Konto już istnieje",
"accountNotExit"=>"Konto nie istnieje",
"accountIncorrect"=>"Błędna nazwa użytkownika lub hasło",
"accountNIncorrect"=>"Błędne konto",
"activeEmpty"=>"Wartość aktywna jest wymagana",
"addFail"=>"Błąd dodawania",
"addSuccess"=>"Pomyślnie dodano.",
"addSuccessPw"=>"Pomyślnie dodano, hasło to: '%s'.",
"addTmpKeyFail"=>"Błąd dodawania klucza tymczasowego, spróbuj ponownie",
"aptDuplicated"=>"Mieszkanie %s jest powielone",
"aptDigits"=>"Mieszkanie %s jest błędne, musu być między 1 a 6 cyfrową liczbą",
"aptExit"=>"Mieszkanie %s już istnieje",
"abnormal"=>"Coś poszło nie tak",
"activation"=>"Aktywacja",
"additionalApp"=>"Dodatkowa aplikacja",
"bindDevice"=>"Usuń wszystkie urządzenia przypisane do tego konta",
"bindMAClibrary"=>"Usuń adres MAC z Bazy adresów MAC",
"bindUser"=>"Usuń użytkowników przypisanych do tego konta",
"buildingBindDevice"=>"Usuń urządzenia przypisane do tego budynku",
"buildingBindUser"=>"Usuń użytkowników przypisanych do tego budynku",
"buildingDigits"=>"Nieprawidłowy budynek %s, powinien być jedno lub dwu cyfrowy",
"buildingExit"=>"Budynek już istnieje",
"BindingDeviceFailed"=>"Błąd parowania urządzenia, może być już sparowane z innym użytkownikiem lub nie zostało dodane do bazy adresów MAC",
"chcekMacExits"=>"Błąd dodawania, Adres MAC jest błędny lub już istnieje",
"changePasswdFail"=>"Błąd podczas edycji hasła",
"changePasswdPEmail"=>"Pomyślnie edytowano hasło, sprawdź email %s",
"community"=>"Społeczność",
"deleteFail"=>"Błąd podczas usuwania",
"deleteSuccess"=>"Pomyślnie usunięto",
"deviceTypeEmpty"=>"Typ urządzenia jest wymagany",
"deviceNotFindUser"=>"Nie znaleziono urządzenia, skontaktuj się z administratorem",
"dealSuccess"=>"Pomyślnie ustawiono",
"doorUnit"=>"Panel zewnętrzny",
"emailExits"=>"Email już istnieje",
"emailPExit"=>"Email %s już istnieje.",
"emailNotExits"=>"Ten email nie istnieje.",
"emailDuplicated"=>"Email %s jest powielony",
"errorVersion"=>"Błąd wersji",
"emailOrAccountNotExit"=>"Ten email nie istnieje.",
"firstNameEmpty"=>"Imię jest wymagane",
"failed"=>"Błąd",
"family"=>"Rodzina",
"guardPhone"=>"Telefon ochrony",
"incorrectSipAccount"=>"Brak dostępnych kont SIP",
"incorrectSipAccountGroup"=>"Brak dostępnych grup SIP",
"importDataSuccess"=>"Pomyślnie zaimportowano dane",
"importFailMACExit"=>"Błąd podczas importowania, sprawdź czy adres MAC jest prawidłowy lub czy istnieje:\r\n%s",
"invaildDC"=>"Nieprawidłowy kod urządzenia",
"InvalidFile"=>"Nieprawidłowy plik",
"invalidPEmail"=>"Nieprawidłowy email %s",
"invalidPName"=>"Nieprawidłowa nazwa użytkownika %s",
"invalidPCalltype"=>"Nieprawidłowy typ połączenia %s",
"invalidPPin"=>"Nieprawidłowy PIN %s",
"invalidPActive"=>"Nieprawidłowa aktywna wartość %s",
"invalidPage"=>"Nieprawidłowa strona",
"invalidPDeviceType"=>"Nieprawidłowy typ urządzenia %s",
"invaildVerCode"=>"Błędny kod weryfikacji",
"invalidIdentity"=>"Błędne dane identyfikacyjne! Możesz być zalogowany w innej lokalizacji, spróbuj ponownie.",
"indoorMonitor"=>"Monitor wewnętrzny",
"inactivated"=>"Nieaktywne",
"normal"=>"Normalny",
"expired"=>"Wygasło",
"lastNameEmpty"=>"Nazwisko jest wymagane",
"locationEmpty"=>"Lokalizacja jest wymagana",
"locationPLoog"=>"%s zbyt długa lokalizacja",
"locationLoog"=>"Zbyt długa lokalizacja",
"loginError"=>"Błąd logowania",
"loginFail"=>"Błąd logowania",
"loginSuccess"=>"Pomyślnie zalogowano",
"limitIP"=>"Zbyt wiele prób w krótkim odstępie czasu. Spróbuj ponownie za 5 minut.",
"limitDevice"=>"Osiągnięto maksymalną liczbę urządzeń",
"MAC2PLibrary"=>"Adres MAC: %s jest błędny, sprawdź swoją bazę adresów MAC.",
"MAC2Library"=>"Ten adres MAC jest błędny, sprawdź swoją bazę adresów MAC",
"macExits"=>"Ten adres MAC już istnieje",
"MACLength"=>"Długość adresu MAC to 12 znaków.",
"modifySuccess"=>"Edycja przebiegła pomyślnie",
"modifyFailed"=>"Błąd edycji",
"maxHouse"=>"Osiągnięto maksymalną liczbę użytkowników. Skontaktuj się z administratorem.",
"modifyAptFail"=>"Błąd zapisywania. Nr mieszkania już istnieje. Powinieneś najpierw go usunąć.",
"nameloog"=>"Nazwa użytkownika jest zbyt długa, może zawierać do 64 znaków.",
"nameExit"=>"Nazwa użytkownika już istnieje",
"notPermission"=>"Nie masz wystarczających uprawnień",
"noSip"=>"Brak dostępnych kont SIP",
"passwordIncorrect"=>"Błędne hasło",
"passwdChangeSuccess"=>"Pomyślnie zmieniono hasło",
"passwordResetSuccess"=>"Pomyślnie zresetowano hasło",
"passwordReset2"=>"Zresetowano hasło na: '%s'.",
"payTimeOut"=>"Upłynął czas dokonania płatności",
"payFailed"=>"Płatność zakończyła się błędem",
"processing"=>"Przetwarzanie",
"paySuccess"=>"Płatność ukończona",
"redirectedOnRPS"=>"Ten adres MAC jest przekierowany na RPS.",
"registerFailed"=>"Błąd podczas rejestracji",
"registerSuccess"=>"Pomyślnie zarejestrowano",
"roomNotExit"=>"Taki użytkownik nie istnieje!",
"RFCardExit"=>"Taka karta RF już istnieje",
"registered"=>"Zarejestrowano",
"PrivateKeyExists"=>"Taki klucz prywatny już istnieje",
"passwordCorrect"=>"Błędne hasło",
"timeLessCurrent"=>"Błąd podczas aktualizacji czasu",
"timeZoneChangeSuccess"=>"Pomyślnie zmieniono strefę czasową",
"timeOut"=>"Koniec czasu",
"unbindMACUser"=>"Najpierw rozparuj %s z użytkownikiem",
"unKnowDT"=>"Nieznany typ urządzenia",
"userBindUser"=>"Usuń najpierw użytkowników dodanych do tego konta.",
"userNotExit"=>"Użytkownik nie istnieje",
"userMaxPLimt"=>"Błąd tworzenia, możesz dodać do %s członków rodziny",
"unregistered"=>"Nie zarejestrowano",
"validMAC"=>"Wprowadź właściwy adres MAC",
"versionExit"=>"Ta wersja już istnieje",
"versionNameNumberExit"=>"Nazwa wersji lub jej numer już istnieje.",
"sipStatus"=>"Błąd podczas przydzielania konta SIP, spróbuj ponownie później.",
"sentCodeLater"=>"Wysłaliśmy Ci kod weryfikacyjny, spróbuj ponownie później.",
"setSuccess"=>"Pomyślnie ustawiono",
"sendEmailSuccess"=>"Pomyślnie wysłano maila",
"SetFailed"=>"Błąd podczas zapisywania ustawień",
"stairPhone"=>"Telefon na klatce",
"successed"=>"Powodzenie",
"subscription"=>"Subskrypcja",
"wallPhone"=>"Telefon naścienny",
"emailMaxLen"=>"Email musi być krótszy niż 64 znaki.",
"serverUpgradeTips"=>"Uaktualniono serwer, odśwież stronę. Wcześniej możesz skopiować dane, które właśnie wprowadziłeś, do innego miejsca.",
"ActiveFamilyAccount"=>"Najpierw aktywuj główne konto.",
"weekly"=>"Tygodniowy",
"daily"=>"Dzienny",
"never"=>"Nigdy",
"calltypeEmpty"=>"Wymagany jest typ połączenia",
"addOutApt"=>"Możesz dodać do %s pokoi",
"call"=>"Połączenie",
"unlock"=>"Otwórz",
"tryUnlockCall"=>"Spróbuj otworzyć drzwi podczas połączenia",
"tryUnlockKey"=>"Spróbuj otworzyć drzwi za pomocą kodu",
"tryUnlockCard"=>"Spróbuj otworzyć drzwi za pomocą karty RF",
"tryUnlockFace"=>"Spróbuj otworzyć drzwi za pomocą Rozpoznawania twarzy",
"unlockApp"=>"Otwórz za pomocą SmartPlus",
"unlockIndoor"=>"Otwórz za pomocą monitora wewnętrznego",
"unlockNFC"=>"Otwórz za pomocą NFC",
"unlockBluetooth"=>"Otwórz za pomocą Bluetooth",
"unlockCard"=>"Otwórz za pomocą karty RF",
"unlockPrivateKey"=>"Otwórz za pomocą kodu prywatnego",
"unlockTempKey"=>"Otwórz za pomocą kodu tymczasowego",
"alarmDoorUnlock"=>"Otwarcie drzwi",
"alarmInfrared"=>"Podczerwień",
"alarmSmoke"=>"Dym",
"alarmGas"=>"Gaz",
"alarmUrgency"=>"Nagły wypadek",
"alarmSOS"=>"SOS",
"alarmTamper"=>"Sabotaż",
"alarmGate"=>"Brama",
"alarmDoor"=>"Drzwi",
"alarmBedroom"=>"Sypialnia",
"alarmGuestRoom"=>"Pokój gościnny",
"alarmHall"=>"Przedpokój",
"alarmWindow"=>"Okno",
"alarmBalcony"=>"Balkon",
"alarmKitchen"=>"Kuchnia",
"alarmStudy"=>"Biuro",
"alarmBathroom"=>"Łazienka",
"alarmArea"=>"Obszar",
"RFCardExit2"=>"Karta RF %s już istnieje",
"RFCardDuplicated"=>"Karta RF %s jest powielona",
"notMacBind"=>"Uzytkownik '%s' nie ma uprawień do otwierania drzwi połączonych z urządzeniem '%s'.",
"accountNumLet"=>"Konto musi składać się z cyfr i liter",
"networkUnavailable"=>"Sieć niedostępna.",
"notForModel"=>"Nie dla tego urzadzenia.",
"upgradeDevVersion"=>"Zaktualizuj do najnowszej wersji.",
"unavailableService"=>"Usługa jest tymczasowo niedostępna, proszę spróbować ponownie później.",
"cantDeletePin"=>"Nie możesz usunąć PINu %s",
"residentInRoom"=>"Pod numerem mieszkania %s są już mieszkańcy",
"noAnswer"=>"Bez odpowiedzi",
"indoorAndApp"=>"Monitor wewnętrzny i aplikacja(główna)",
"indoorMonitorOnly"=>"Tylko monitor wewnętrzny",
"appOnly"=>"Tylko aplikacja(główna)",
"endThanStart"=>"Czas zakończenia nie może być wcześniejszy niż czas rozpoczęcia.",
"endThanStartFile"=>"Nieprawidłowy czas lub data w linii '%s'.",
"doorRelease"=>"Odblokowanie drzwi",
"success"=>"Powodzenie",
"unlockFACE"=>"Otwórz za pomocą rozpoznawania twarzy",
"unlockBLE"=>"Otwórz za pomocą Bluetooth",
"captureSmartPlus"=>"Przechwyć na SmartPlus",
"drmagnet"=>"Kontaktron",
"failedUnlock"=>"Błąd podczas otwierania",
"deviceDisconnected"=>"Urządzenie zostało rozłączone.",
"low"=>"Niski",
"motion"=>"Ruch",
"capture"=>"Przechwycenie",
"failedImport"=>"Błąd importowania",
"notValidMobile"=>"Numer %s nie jest prawidłowym numerem telefonu",
"mobileExits"=>"Numer telefonu komórkowego już istnieje",
"mobileExits2"=>"Numer telefonu komórkowego %s już istnieje",
"mobileDuplicated"=>"Numer telefonu komórkowego %s jest powielony",
"mobileNumberExist"=>"Numer telefonu nie istnieje.",
"codeIncorrect"=>"Nieprawidłowy kod",
"sendCodeSuccess"=>"Pomyślnie wysłano kod QR",
"codeCorrect"=>"Poprawny",
"mobileNumberEmpty"=>"Wprowadź numer telefonu.",
"invalidUser"=>"Błędny użytkownik %s",
"locationExits"=>"Adres lokalizacji już istnieje.",
"smartPlusIndoor"=>"SmartPlus i monitor wewnętrzny",
"phoneIndoor"=>"Numer telefonu i monitor wewnętrzny",
"smartPlusIndoorBackup"=>"SmartPlus i monitor wewnętrzny z numerem telefonu jako kopia zapasowa",
"smartPlusBackup"=>"Monitor wewnętrzny i SmartPlus jako kopia zapasowa",
"indoorPhoneBackup"=>"Monitor wewnętrzny i numer telefonu jako kopia zapasowa",
"indoorSmartPlusPhone"=>"Monitor wewnętrzny i SmartPlus jako kopia zapasowa, następnie numer telefonu",
"endUser"=>"Użytkownik końcowy",
"installer"=>"Instalator",
"distributor"=>"Dystrybutor",
"pm"=>"Zarządca",
"superManage"=>"Super zarządzanie",
"loginManagement"=>"Zarządzanie logowaniem",
"accessControl"=>"Kontrola dostępu",
"userManagement"=>"Zarządzenie użytkownikami",
"deviceManagement"=>"Zarządzanie urządzeniami",
"communityManagement"=>"Zarządzanie społecznością",
"auditLogin"=>"Zaloguj się: web",
"auditLogout"=>"Wyloguj się: web",
"auditAddTempKey"=>"Dodawanie klucza tymczasowego: {0}",
"auditEditTempKey"=>"Edycja klucza tymczasowego: {0}",
"auditDeleteTempKey"=>"Usuwanie klucza tymczasowego: {0}",
"auditAddRFCard"=>"Dodawanie karty RF: {0}",
"auditEditRFCard"=>"Edycja karty RF: {0}",
"auditDeleteRFCard"=>"Usuwanie karty RF: {0}",
"auditAddDis"=>"Dodawanie dystrybutora: {0}",
"auditEditDis"=>"Edycja dystrybutora: {0}",
"auditDeleteDis"=>"Usuwanie dystrybutora: {0}",
"auditAddInstaller"=>"Dodawanie instalatora: {0}",
"auditEditInstaller"=>"Edycja instalatora: {0}",
"auditDeleteInstaller"=>"Usuwanie instalatora: {0}",
"auditAddPM"=>"Dodawanie zarządcy: {0}",
"auditEditPM"=>"Edycja zarządcy: {0}",
"auditDeletePM"=>"Usuwanie zarządcy: {0}",
"auditAddEndUser"=>"Dodawanie użytkownika końcowego: {0}",
"auditEditEndUser"=>"Edycja użytkownika końcowego: {0}",
"auditDeleteEndUser"=>"Usuwanie użytkownika końcowego: {0}",
"auditSetOwnerTime"=>"Ustaw strefę czasową: {0}",
"auditSetOwnPassword"=>"Ustaw własne hasło",
"auditAddPIN"=>"Dodawanie PIN: {0}",
"auditEditPIN"=>"Edycja PIN: {0}",
"auditDeletePIN"=>"Usuwanie PIN: {0}",
"auditImportFace"=>"Import twarzy: {0}",
"auditDeleteFace"=>"Usuwanie twarzy: {0}",
"auditSetCallTypeSmartPlusIndoor"=>"Ustaw typ połączenia jako SmartPlus i monitory: {0} oraz {1}",
"auditSetCallTypePhoneIndoor"=>"Ustaw typ połączenia jako numer telefonu i monitory: {0} oraz {1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Ustaw typ połączenia jako SmartPlus i monitory wraz z numerem jako kopia zapasowa: {0} oraz {1}",
"auditSetCallTypeSmartPlusBackup"=>"Ustaw typ połączenia jako monitory ze SmartPlus jako kopia: {0} oraz {1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Ustaw typ połączenia jako monitory z numerem telefonu jako kopia: {0} oraz {1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Ustaw typ połączenia jako monitory i SmartPlus jako kopia, następnie numer telefonu: {0} oraz {1}",
"auditDeleteDevice"=>"Usuwanie urządzenia: {0}",
"auditSetAPTCount"=>"Wybierz ilość mieszkań: {0}",
"auditEnableLandline"=>"Włącz telefon stacjonarny",
"auditDisableLandline"=>"Wyłącz telefon stacjonarny",
"auditSetSubTime"=>"Ustaw strefę czasową: {0}",
"auditSetChargeModeInstall"=>"Ustaw płatność przez instalatora",
"auditSetChargeModeUser"=>"Ustaw płatność przez użytkownika",
"auditSetConnectTypeDefault"=>"Ustawienie typu połączenia - domyślne",
"auditSetConnectTypeTCP"=>"Ustawienie typu połączenia - TCP",
"auditSetConnectTypeUDP"=>"Ustawienie typu połączenia - UDP",
"auditSetConnectTypeTLS"=>"Ustawienie typu połączenia - TLS",
"auditAddCommunity"=>"Dodawanie społeczności: {0}",
"auditDeleteCommunity"=>"Usuwanie społeczności: {0}",
"auditImportCommunity"=>"Import społeczności: {0}",
"auditSetAPTNumber"=>"Ustaw {0} numer mieszkania {1}",
"auditSetEmail"=>"Ustaw email {0}: {1}",
"auditSetMobile"=>"Ustawianie numeru telefonu {0}: {1}",
"auditDeviceTypeStair"=>"Ustawianie typu panela wielorodzinnego: {0}",
"auditDeviceTypeDoor"=>"Ustawienia typu panela jednorodzinnego: {0}",
"auditDeviceTypeIndoor"=>"Ustawianie typu monitora wewnętrznego: {0}",
"auditDeviceTypeGuardPhone"=>"Ustawianie typu konsoli portiera: {0}",
"auditDeviceTypeAccessControl"=>"Ustawianie typu kontroli dostępu: {0}",
"auditSetNetGroup"=>"Ustawianie grupy sieci {0}: {1}",
"auditEditCommunity"=>"Edycja społeczności",
"deliveryMsg"=>"%s elementów zostało Ci dostarczonych, sprawdź to.",
"deliveryTitle"=>"Masz nową paczkę!",
"rfcardDuplicatedLines"=>"Powielony numer karty RF w linii %s!",
"rfcardNameInvalid"=>"Błędna nazwa karty RF w linii %s!",
"rfcardExistLines"=>"Ta karta RF już istnieje w linii %s.",
"importFailMacExistLines"=>"Adres MAC już istnieje lub obowiązuje w linii %s.",
"exportExcelCountNull"=>"Brak dzienników do wyeksportowania! Wybierz ponownie.",
"keyIsEqualRoom"=>"Kod dostawy nie może być taki sam jak numer mieszkania!",
"visitor"=>"Gość",
"CommunityNameExist"=>"Nazwa społeczności już istnieje",
"unlockGuardPhone"=>"Otwieranie przez portiera",
"auditLoginApp"=>"Logowanie: aplikacja",
"auditLogoutApp"=>"Wylogowanie: aplikacja",
"timeForYesterday"=>"Wczoraj",
"exportExcelDataBefore"=>"Zbyt dużo danych! najpierw wyeksportuj dane przed %s.",
"tempkeyUsed"=>"Klucz tymczasowy użyty",
"tempkeyContent"=>"%s użył Klucza tymczasowego.",
"accessNameExist"=>"Nazwa Grupy dostępu już istnieje",
"addFaceFail"=>"Wczytaj wyraźne zdjęcie twarzy.",
"userInvalid"=>"Błędny użytkownik w linii %s.",
"groupsInvalid"=>"Błędna grupa dostępowa w linii %s.",
"BuildAccessName"=>"Mieszkaniec-Budynek %s",
"auditCodeLogEditApt"=>"Edycja mieszkania: {0}",
"invalidTimeInLine"=>"Błędny czas w linii %s.",
"cancel"=>"Anuluj",
"cancelSuccess"=>"Anulowano.",
"payOutstanding"=>"Sprawdź, czy są jakieś nieopłacone zamówienia, jeśli nie, skontaktuj się z usługodawcą",
"featureDeleteError"=>"Plan został powiązany.",
"beyondFamilyMember"=>"Nie możesz dodać więcej członków rodziny, skontaktuj się z dostawcą.",
"indoorMonitorRequired"=>"Wymagany co najmniej jeden monitor wewnętrzny w mieszkaniu.",
"featureActivationFee"=>"Plan (jednorazowa opłata)",
"systemProcessing"=>"Przetwarzanie",
"featureMonthlyFee"=>"Plan (miesięczna opłata)",
"featurePriceDifferences"=>"Plan (różnica cenowa)",
"updatingSuccess"=>"Zaktualizowano!",
"featureNameBasic"=>"Podstawowy",
"featureNamePremium"=>"Premium",
"indoorMacNotCorrect"=>"Wpisz prawidłowy adres MAC monitora wewnętrznego.",
"off"=>"Wył",
"enterValidAccount"=>"Wprowadź poprawne konto.",
"invalidKitImportMAC"=>"Sprawdź poprawność oraz istnienie w bazie adresu MAC %s.",
"importLessData"=>"Wczytaj mniej niż %s danych.",
"invalidQRCode"=>"Błąd identyfikacji, zeskanuj prawidłowy kod QR.",
"cannotCreateFamilyMember"=>"Nie możesz dodać więcej członków rodziny",
"importProcessing"=>"Wczytywanie, spróbuj później",
"departmentAccessName"=>"Grupa dostępu - %s",
"idExistsLine"=>"ID już istnieje w linii %s.",
"enterFirstNameLine"=>"Wpisz imię w linii %s.",
"enterLastNameLine"=>"Wpisz nazwisko w linii %s.",
"departmentExist"=>"Wydział już istnieje",
"idExist"=>"ID już istnieje",
"layoutIdInvalid"=>"Błędny układ",
"unlockAppHome"=>"Otwieranie AKHome",
"officeNameExist"=>"Biuro już istnieje.",
"departmentExit"=>"Wydział już istnieje.",
"importOutTask"=>"Możesz wyłącznie wczytać szablon.",
"idDuplicated"=>"ID %s jest powielone.",
"aptInvalidLine"=>"Błędne Mieszkanie w linii %s.",
"buildInvalidLine"=>"Błędny Budynek w linii %s.",
"departmentInvalidLine"=>"Błędny Wydział w linii %s.",
"idInvalidLine"=>"Błędne ID w linii %s.",
"propertyManager"=>"Zarządca społeczności",
"departmentBindDevice"=>"Usuń urządzenia tego działu.",
"departmentBindUser"=>"Usuń użytkowników tego działu.",
"smartPlusValidLine"=>"Błąd interkomu SmartPlus w linii %s.",
"identityValidLine"=>"Błędne ID w linii %s.",
"eachDoorCount"=>"Pojedynczy plan do jednorazowego otwarcia wejścia",
"textUpgradeMsg1"=>"Zaktualizuj aplikację, aby kontynuować.",
"textUpgradeMsg2"=>"Błąd logowania",
"deleteCodeGetLimitTimes"=>"Błędny kod. Spróbuj ponownie za 24 godziny.",
"deleteCodeOverLimitTimes"=>"Spróbuj ponownie za 24 godziny.",
"deleteCodeError"=>"Błędny kod",
"textUpgradeMsg"=>"1.Optimized the tempkey function.;2.Added the account cancellation function.;3.Fixed some bugs.",
"paramsError"=>"Błąd parametru",
"pmappStatusInvalid"=>"Włącz najpierw aplikację PM.",
"delivery_description"=>"Klucz temperatury dostawy",
"webRelayIDInvalidLine"=>"Nieprawidłowy identyfikator przekaźnika internetowego w linii %s.",
"relayInvalid"=>"Nieprawidłowy przekaźnik w linii %s.",
"cancelError"=>"Anuluj nieudany.",
"textUpgradeMsgForComRole"=>"Uaktualnij rolę społeczności",
"textUpgradeMsgForPerRole"=>"Uaktualnij rolę personelu",
"textUpgradeMsgForOffRole"=>"Uaktualnij rolę w biurze",
"textUpgradeMsgForPMRole"=>"Uaktualnij rolę PM",
"lockApp"=>"SmartPlus Lock",
"lock"=>"Zamek",
"versionLogMaxLen"=>"Dziennik wersji nie może być większy niż %S znaków",
"autoLock"=>"Auto Lock",
"pinAndRFcardNotNullLines"=>"Co najmniej jedna z karty PIN i RF w linii %S musi zostać wypełniona!",
"pinExistLines"=>"PIN istniał już w linii %s.",
"pinInvalidLines"=>"Nieprawidłowy pin w linii %s!",
"pinDuplicatedLines"=>"Zduplikowane pin w linii %s!",
"FaceImportLength"=>"Rozmiar pliku importu twarzy nie może być większy niż %s",
"landlineServerNotActivate"=>"Ta społeczność nie aktywuje usług stacjonarnych.",
"importFailDisNotExist"=>"Dystrybutor nie istnieje",
"importFailNotPermission"=>"Nie masz zgody na dodanie tego adresu MAC.",
"importFailTooManyAdd"=>"Import nie powiódł się tylko dla pojedynczego dystrybutora.",
"importFailAdded"=>"Ten adres MAC został już dodany przez innego użytkownika.",
"macAssignToLimit"=>"Możesz przypisać tylko 10 dystrybutorów",
"macNumToLimit"=>"Możesz przesyłać tylko do 1000 adresów MAC na raz.",
"addOutFloor"=>"Wprowadź numer od 1 ~ 128.",
"floor"=>"Podłoga",
"PostalCodeInvalid"=>"Wprowadź list lub numer.",
"onceCodeInvalid"=>"Kod raz musi wynosić 4-5 cyfr.",
"permanentCodeInvalid"=>"Kod stały musi wynosić 6 cyfr.",
"onceCodeOutNum"=>"Możesz dodać do 10 raz po kodzie.",
"permanentCodeOutNum"=>"Możesz dodać tylko do 10 stałych kodów.",
"onceCodeExist"=>"Niegdyś kod już istnieje.",
"permanentCodeExist"=>"Stały kod już istnieje.",
"addOutFloorLine"=>"Nieprawidłowy numer podłogi w linii %s.",
"auditManuallyUnlock"=>"Ręcznie odblokować",
"auditManuallyLock"=>"Ręcznie zamek",
"automaticallyUnlock"=>"Automatycznie odblokować",
"doorClose"=>"Zamykane drzwi",
"PostalCodeNotEmpty"=>"Wprowadź co najmniej jedną literę lub numer.",
"emergencyAlarm"=>"Alarm awaryjny",
"doorSensor"=>"Czujnik drzwi",
"yaleBatteryWarning"=>"Ostrzeżenie o baterii Yale",
"auditCodeManuallyUnlock"=>"Ręcznie odblokować",
"auditCodeManuallyLock"=>"Ręcznie zamek",
"2weekBatteryWarning"=>"%S - Szacowany czas baterii Pozostały: 2 tygodnie.",
"1weekBatteryWarning"=>"%S - Szacowany czas baterii Pozostały: 1 tydzień.",
"replaceBatteryWarning"=>"%S - Poziom baterii jest wyjątkowo niski, natychmiast wymienić.",
"open"=>"otwarty",
"close"=>"Zamknąć",
"addContactFavoriteNum"=>"Dodanie do ulubionych nie powiodło się. Możesz dodać tylko do 300 ulubionych mieszkań.",
"addContactBlockNum"=>"Dodanie do listy bloków nie powiodło się. Możesz zsumować tylko 100 mieszkań, aby lista bloków.",
"voiceTitle"=>"Wiadomość głosowa",
"voiceContent"=>"Masz wiadomość głosową od %s",
"voiceMsgInvalid"=>"Wiadomość głosowa wygasła.",
"toggleFeaturePlan"=>"Nie możesz zmienić planu funkcji.",
"rtspAddresEmpty"=>"Wprowadź adres RTSP.",
"rtspAddresInvalid"=>"Nieprawidłowy adres RTSP.",
"rtspPortEmpty"=>"Wprowadź port.",
"rtspPortInvalid"=>"Nieprawidłowy port.",
"rtspPassWdEmpty"=>"Proszę wpisać hasło.",
"rtspPassWdInvalid"=>"Hasło zbyt długie, hasło może zawierać do 63 znaków.",
"cameraExist"=>"Kamera już istnieje.",
"errorOnRPS"=>"Nie udało się żądać serwera RPS",
"faceImportErrorSystem"=>"Błąd systemu",
"faceImportErrorView"=>"Nie widok z przodu",
"faceImportErrorWearMask"=>"Wykryto maskę",
"faceImportErrorLowResolution"=>"Rozdzielczość jest zbyt niska",
"faceImportErrorWrongFormat"=>"Błąd formatu pliku",
"faceImportErrorNoFace"=>"Brak wykrycia twarzy",
"faceImportErrorFileLarge"=>"Plik jest zbyt większy",
"faceImportErrorFaceLarge"=>"Twarz jest zbyt większa",
"faceImportErrorFaceSmall"=>"Twarz jest zbyt mała",
"faceImportErrorMultiFaces"=>"Więcej niż jedna twarz",
"faceImportErrorWrongName"=>"Nazwa pliku to błąd.",
"faceImportErrorEmptyName"=>"Nazwa rezydenta jest pusta.",
"faceImportErrorNoAccountInfo"=>"Uzyskaj błąd informacji PersonalAccount.",
"faceImportErrorAccountInactive"=>"PersonalCount nie jest aktywny.",
"changeHomeFeatureInvalid"=>"Operacja nie powiodła się!",
"changeInterComFeatureInvalid"=>"Operacja nie powiodła się!",
"offline"=>"Nieudane: offline",
"allFloors"=>"Wszystkie podłogi",
"uploadOversize"=>"Rozmiar pliku przesyłania nie może być większy niż %s",
"uploadInvalidType"=>"Przesłany typ pliku nie jest obsługiwany",
"uploadFailed"=>"Prześlij nie powiodło się, spróbuj później",
"uploadScreenSaverImgTooMuch"=>"Zdjęcia ekranu nie mogą być więcej niż %s!",
"screenSaverImgTooLittle"=>"Zdjęcia ekranu nie mogą być mniejsze niż %s!",
"screenSaverImgTooMuch"=>"Zdjęcia ekranu nie mogą być więcej niż %s!",
"screenSaverDevicesOffline"=>"Zapisywanie Nie powiodło się.",
"saveFailed"=>"Zapisywanie Nie powiodło się.",
"importingInProgress"=>"Importowanie w toku, spróbuj ponownie później.",
"importBuildingInvalidLine"=>"Nieprawidłowy budynek w linii %s",
"importAptInvalidLine"=>"Nieprawidłowy apt w linii %s",
"importAccountTypeInvalidLine"=>"Nieprawidłowy typ rachunku w linii %s",
"importFirstNameInvalidLine"=>"Nieprawidłowe imię w linii %s",
"importLastNameInvalidLine"=>"Nieprawidłowe nazwisko w linii %s",
"importKeyInvalidLine"=>"Nieprawidłowy klucz w linii %s",
"importKeyExistsLine"=>"Pin istnieje w linii %s",
"importCardInvalidLine"=>"Nieprawidłowa karta RF w linii %s",
"importCardExistsLine"=>"Karta RF istnieje w linii %s",
"importAccessGroupInvalidLine"=>"Nieprawidłowy identyfikator grupy dostępu w linii %s",
"importAccessGroupNoPermissionLine"=>"Brak zgody ID grupy dostępu w linii %s",
"importExceededNumberLine"=>"Przekroczył liczbę członków rodziny w linii %s",
"importNoActiveMasterLine"=>"Importowanie nie powiodło się w linii, najpierw aktywuj rodzinny Matser.",
"importMasterExistsLine"=>"Mistrz rodziny już istnieje w linii %s.",
"importNoCreateMasterLine"=>"Importowanie nie powiodło się w linii, najpierw utwórz rodzinny Matser.",
"PrivateKeysDataExist"=>"Klucz prywatny %już istnieje.",
"PrivateKeyDataExists"=>"Klucz prywatny %już istnieje.",
"landLineOpenToClosedFail"=>"Zapisywanie Nie powiodło się.",
"limitWithIp"=>"Próbujesz zbyt często, spróbuj ponownie za 5 minut. (IP: %S)",
"subDistributor"=>"Podkreślenie",
"faceImportErrorNotClear"=>"Zaimportowany obraz nie jest jasny.",


  ];
