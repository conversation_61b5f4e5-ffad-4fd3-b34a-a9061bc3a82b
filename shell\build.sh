#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   web_backend 构建脚本
# Modifier      :   sicen 2024-02-20 V6.8 构建脚本统一common.sh，删除多余脚本
# ****************************************************************************

set -euo pipefail

###################### 环境、参数检测、变量定义 ######################
PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径
MIDDLEWARE=$5

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

AKCS_SRC_ROOT=$PROJECT_PATH
source "$AKCS_SRC_ROOT"/shell/package_build_shell/source.sh

if [ -z "$MIDDLEWARE" ];then
    echo "请选择需要部署的中间件";
    exit 1
fi

###################### 开始编译 ######################
#清理上次安装包
clean

#自动生成proto php代码
ts=$(date +%s.%N)
WORKSPACE_PATH=/opt/jenkins/workspace
image=registry.cn-hangzhou.aliyuncs.com/ak_system/app_backend_for_cicd:akcloud-1.0
docker run --rm --name "create_proto-$ts" -v $WORKSPACE_PATH:$WORKSPACE_PATH $image /bin/bash -x ${PROJECT_PATH}/shell/create_proto.sh ${PROJECT_PATH}
if [ ! -f ${PROJECT_PATH}/apache-v3.0/notify/proto.php ] || [ ! -f ${PROJECT_PATH}/openapi/common/proto.php ]; then
    echo "生成proto异常";
    exit 1
fi


#校验词条，打包web，webtask不需要校验词条和打包web，其余目前的项目都需要
if [ "$MIDDLEWARE" != "webtask" ]; then
    check_articles
    integrate_package
fi

#download
if [ ! -d $AKCS_SRC_DOWNLOAD ]; then mkdir -p $AKCS_SRC_DOWNLOAD; fi
cp -rf $AKCS_SRC_DOWNLOAD $AKCS_PACKAGE_ROOT/


array=(${MIDDLEWARE//,/ })
for var in ${array[@]}
do
    if [ "$var" == "wb_insapp" ]; then
        echo "执行 insapp 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH insapp wb_insapp installerApp
    elif [ "$var" == "openapi" ]; then
        echo "执行 openapi 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/openapi.sh $PROJECT_PATH $SRC_PATH
    elif [ "$var" == "webtask" ]; then
        echo "执行 webtask 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/webtask.sh $PROJECT_PATH $SRC_PATH
    elif [ "$var" == "smarthome2" ]; then
        echo "执行 smarthome2 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH smartHome2
    elif [ "$var" == "web_office" ]; then
        echo "执行 office 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH office
    elif [ "$var" == "web_community" ]; then
            echo "执行 community 打包"
            bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH community
    elif [ "$var" == "web_single" ]; then
            echo "执行 single 打包"
            bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH single
    elif [ "$var" == "web_log" ]; then
            echo "执行 log 打包"
            bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH log
    elif [ "$var" != "apache" ] && [ "$var" != "webserver" ] && [ "$var" != "download" ]; then
        echo "执行 $var 打包"
        bash -x "$AKCS_SRC_ROOT"/shell/package_build_shell/common.sh $PROJECT_PATH $SRC_PATH $var
    fi
done


