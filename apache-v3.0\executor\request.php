<?php
/*
 * @Description: 请求执行者(@单例),处理前端发送的请求
 * @version: 
 * @Author: kxl
 * @Date: 2019-12-18 17:36:38
 * @LastEditors  : kxl
 */
namespace executor;
include_once __DIR__."/basic.php";


class CRequest extends \executor\Basic {
    public $plan;//暂无用
    private $uri;
    private $method;
    private static $instance;
    private function __construct () {}
    private function __clone () {}
    public static function getInstance () {
        if(!self::$instance) self::$instance = new self();
        return self::$instance;
    }
    /**
     * @name: parse 
     * @msg: 解析请求的url和请求方法
     * @param  无
     * @return: null
     */
    public function parse () {
        $uri = $_SERVER['REQUEST_URI'];
        $pro = strpos($uri,'?');
        if($pro !== false) {
            $uri = substr($uri,0,$pro);
        }
        //url解码
        $this->uri = rawurldecode($uri);
        $this->method = $_SERVER['REQUEST_METHOD'];
    }

    public function exec () {
        global $gApp,$cLog;
        $gApp["plan"]["name"] = $this->uri;
        $gApp["plan"]["method"] = $this->method;
        $cLog->actionLog("#executor#request#uri=$this->uri");
    }
}