<?php
/*
 * @Description: 终端用户pw检查
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 16:26:29
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../util/model.php";
include_once __DIR__."/../database/main.php";
class CUserPwCheck implements IMiddleware {
    public function handle(\Closure $next) {
        global $cMessage;
        global $cLog,$gApp;
        $params = ["Password"=>""];
        $password = \util\model\getParamsFromContainer($params,$this->dataContainer)["Password"];
        
        $userId = $gApp["userAliasId"];
        $cLog->actionLog("#middle#userPwCheck#");
        $db = \database\CDatabase::getInstance();
        $count = $db->querySList("select count(*) from PersonalAccount where ID=:ID and Passwd = :Passwd",[":Passwd"=>md5($password),":ID"=>$userId])[0]["count(*)"];
        if($count == 0) $cMessage->echoErrorMsg(StatePasswordIncorrect);
        $next();
    }
}