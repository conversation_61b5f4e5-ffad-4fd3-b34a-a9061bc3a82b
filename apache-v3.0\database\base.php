<?php
/*
 * @Description:
 * @version:
 * @Author: kxl
 * @Date: 2019-12-19 20:27:49
 * @LastEditors: Please set LastEditors
 */
namespace database;

use PDO;

trait DataBaseBasic
{
    public $db;
    public $sql;
    private $DBName = "mysql:host=".DATABASEIP.";port=".DATABASEPORT.";dbname=".DATABASENAME;
    private $User = DATABASEUSER;
    private $parssword = DATABASEPW;

    private $RpsDBName = 'mysql:host=**************;dbname=RPS';
    private $RpsUser = "root";
    private $RpsParssword = "Akuvox2!3#0^1W!";
    public function connect()
    {
        $db = new PDO($this->DBName, $this->User, $this->parssword, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $db->query('set names utf8;');
        return $db;
    }

    public function contectRps()
    {
        return new PDO($this->RpsDBName, $this->RpsUser, $this->RpsParssword, [\PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION]);
    }
}
