<?php

namespace package\common\model\iTec\method;
trait Get
{
    /**
     * @description: 获取社区itec锁列表，未被link的
     * @param:
     * @return mixed
     * @author: shoubin.chen 2025/1/15 11:36:48 V7.1.0
     * @lastEditor: shoubin.chen 2025/1/15 11:36:48  V7.1.0
     */
    public function getCommunityItecLockList()
    {
        $params = [PROXY_ROLE_CHECK['projectUUID']];
        list($projectUUID) = $this->getParams($params);
        $array = [
            ['Grade', [1, 2]],
            ['ProjectType', 2],
            ['AccountUUID', $projectUUID]
        ];

        $gatewayList = $this->dao->iTecGateway->selectByArray($array);
        $gatewayUuidList = array_column($gatewayList, 'UUID');
        $itecLockList = $this->dao->iTecLock->selectByArray([['ITecGatewayUUID', $gatewayUuidList]]);

        $formatGatewayList = $this->share->util->arrayColumnAsKey($gatewayList, 'UUID');
        $communityUnitUUIDList = array_column($gatewayList, 'CommunityUnitUUID');
        $this->loadUtil('communityUnit', true);
        $unitNameList = $this->utils->_common->communityUnit->getUnitInfoByArr([['UUID', $communityUnitUUIDList]], 'UnitName,UUID');
        $unitNameList = $this->share->util->arrayColumnAsKey($unitNameList, 'UUID');

        $itecLockListRes = [];
        foreach ($itecLockList as $itec) {
            if (!empty($itec['DeviceUUID'])) {
                continue;
            }
            $gatewayUuid = $itec['ITecGatewayUUID'];
            $communityUnitUUID = $formatGatewayList[$gatewayUuid]['CommunityUnitUUID'];
            $item['UUID'] = $itec['UUID'];
            $item['Name'] = $itec['Name'];
            $item['Building'] = $unitNameList[$communityUnitUUID]['UnitName'];
            $item['LockID'] = $itec['LockId'];
            $item['Brand'] = THIRD_PARTY_LOCK['ITec'];
            $itecLockListRes[] = $item;
        }

        return $itecLockListRes;
    }

    /**
     * @description: 获取社区最外层的itec锁列表，未被link的
     * @param:
     * @return mixed
     * @author: kzr 2025/3/14 14:36:48 V7.1.0
     * @lastEditor: kzr 2025/3/14 14:36:48  V7.1.0
     */
    public function getCommunityPublicItecLockList()
    {
        $params = [PROXY_ROLE_CHECK['projectUUID']];
        list($projectUUID) = $this->getParams($params);
        $array = [
            ['Grade', [1]],
            ['ProjectType', 2],
            ['AccountUUID', $projectUUID]
        ];

        $gatewayList = $this->dao->iTecGateway->selectByArray($array);
        $gatewayUuidList = array_column($gatewayList, 'UUID');
        $itecLockList = $this->dao->iTecLock->selectByArray([['ITecGatewayUUID', $gatewayUuidList]]);

        $formatGatewayList = $this->share->util->arrayColumnAsKey($gatewayList, 'UUID');
        $communityUnitUUIDList = array_column($gatewayList, 'CommunityUnitUUID');
        $this->loadUtil('communityUnit', true);
        $unitNameList = $this->utils->_common->communityUnit->getUnitInfoByArr([['UUID', $communityUnitUUIDList]], 'UnitName,UUID');
        $unitNameList = $this->share->util->arrayColumnAsKey($unitNameList, 'UUID');

        $itecLockListRes = [];
        foreach ($itecLockList as $itec) {
            if (!empty($itec['DeviceUUID'])) {
                continue;
            }
            $gatewayUuid = $itec['ITecGatewayUUID'];
            $communityUnitUUID = $formatGatewayList[$gatewayUuid]['CommunityUnitUUID'];
            $item['UUID'] = $itec['UUID'];
            $item['Name'] = $itec['Name'];
            $item['Building'] = $unitNameList[$communityUnitUUID]['UnitName'];
            $item['LockID'] = $itec['LockId'];
            $item['Brand'] = THIRD_PARTY_LOCK['ITec'];
            $itecLockListRes[] = $item;
        }

        return $itecLockListRes;
    }

    public function getCommunityItecLockInfo()
    {
        $params = ['LockUUID:uuid', 'Virtual'];
        list($lockUuid, $virtual) = $this->getParams($params);
        if (empty($virtual)) {
            $virtual = false;
        }

        $iTecLock = $this->dao->iTecLock->selectByArray([['UUID', $lockUuid]])[0];
        $gatewayInfo = $this->dao->iTecGateway->selectByKey('UUID', $iTecLock['ITecGatewayUUID'])[0];
        $this->loadUtil('communityUnit', true);
        $unitInfo = $this->utils->_common->communityUnit->getUnitInfoByKey('UUID', $gatewayInfo['CommunityUnitUUID']);
        $iTecInfo = [
            'Building' => $unitInfo['UnitName'],
            'Name' => $iTecLock['Name'],
            'UUID' => $iTecLock['UUID'],
            'LockId' => $iTecLock['LockId'],
            'Brand' => THIRD_PARTY_LOCK['ITec']
        ];
        $this->loadUtil('thirdLock', true);
        if ($virtual) {
            $uuid = $this->utils->_common->thirdLock->getVirtualLockUUID($iTecLock['UUID'], THIRD_PARTY_LOCK['ITec']);
            $iTecInfo['UUID'] = $uuid;
        }

        return $iTecInfo;
    }

    /**
     * @description: 获取iTecLock信息
     * @author: kzr 2025/01/18 15:06:36 V7.1.0
     * @lastEditor: kzr 2025/01/18 15:06:36 V7.1.0
     */
    public function getITecLock()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->iTecLock->selectByArray($array, $fields);
    }

    /**
     * @description: 获取iTecGateway信息
     * @author: kzr 2025/01/18 15:06:36 V7.1.0
     * @lastEditor: kzr 2025/01/18 15:06:36 V7.1.0
     */ 
    public function getITecGateway()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }
        
        return $this->dao->iTecGateway->selectByArray($array, $fields);
    }

    public function getRelayInUse()
    {
        $params = ['DeviceUUID', 'MAC'];
        list($deviceUUID, $mac) = $this->getParams($params);

        $this->loadUtil('smartLock');
        $bindLockRelayList = $this->utils->smartLock->getThirdPartyLockDevice([['MAC', $mac]], "Relay");
        $relayInUse = [];

        $this->loadUtil('device');
        foreach (array_column($bindLockRelayList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }

        $this->loadUtil('iTec',true);
        $this->loadUtil('salto', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('ttLock', true);
        $this->loadUtil('smartLockSL20', true);
        $bindSaltoList = $this->utils->_common->salto->getSaltoLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindDormakabaList = $this->utils->_common->dormakaba->getDormakabaLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindITecList = $this->callSelfFunc('getITecLock', [[['DeviceUUID', $deviceUUID]], "Relay"]);
        $bindTtLockList = $this->utils->_common->ttLock->getTtLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindSL20List = $this->utils->_common->smartLockSL20->getSL20Lock([['DeviceUUID', $deviceUUID]], "Relay");
        foreach (array_column($bindDormakabaList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSaltoList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindITecList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindTtLockList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSL20List, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        return array_map('intval',$relayInUse);
    }

    
    /**
     * @description: 获取iTecAccount的信息
     * @author: kzr 2025/01/18 15:06:36 V7.1.0
     * @lastEditor: kzr 2025/01/18 15:06:36 V7.1.0
     */
    public function getITecAccount()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->iTecAccount->selectByArray($array, $fields);
    }


    /**
     * @description: 获取终端用户绑定的iTec锁个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: kzr 2025/01/18 15:06:36 V7.1.0
     * @lastEditor: kzr 2025/01/18 15:06:36 V7.1.0
     */
    public function getITecLockNumByEndUser()
    {
        $params = ['PersonalAccountUUID:uuid','ProjectID'];
        list($personalAccountUUID,$projectId) = $this->getParams($params);

        $this->loadUtil('account');
        $this->loadUtil('smartLock',true);
        $mainUser = $this->utils->account->personalAccountSelectByKey('UUID', $personalAccountUUID)[0];
        $lockNum = 0;
        $mainUserUUID = $mainUser['UUID']; 

        if ($mainUser['Role'] == PERENDMROLE) {
            $bindArray=[
                'PersonalAccountUUID'=>$mainUserUUID
            ];
            $lockNum = $this->dao->iTecLock->selectLockCountByEndUser($bindArray);
        } else if ($mainUser['Role'] == COMENDMROLE) {
            //所属社区
            $projectUUID = $mainUser['ParentUUID'];
            //所属楼栋
            $unitID = $mainUser['UnitID'];
            $this->loadUtil('communityUnit');
            $unitUUID = $this->utils->communityUnit->getUnitInfoByKey('ID', $unitID)['UUID'];
            $bindArray=[
                'AccountUUID'=>$projectUUID,
                'CommunityUnitUUID'=>$unitUUID,
                'PersonalAccountUUID'=>$mainUserUUID
            ];
            
            list($lockPublicDatas, $lockPersonnelNum) = $this->dao->iTecLock->selectLockCountByComMainUser($bindArray);
            $lockPublicLinkDoorDatas = [];
            $lockPublicWithoutLinkDoorDatas = [];
            foreach($lockPublicDatas as $lockData){
                if (!Empty($lockData['DeviceUUID'])&&!Empty($lockData['Relay'])){
                    $lockPublicLinkDoorDatas[] = $lockData;
                }else {
                    $lockPublicWithoutLinkDoorDatas[] = $lockData;
                }
            }
            $lockPublicNum = count($lockPublicLinkDoorDatas);
            foreach($lockPublicWithoutLinkDoorDatas as $lockData){
                $isAuth = $this->utils->_common->smartLock->checkThirdLockAuth($lockData['UUID'],THIRD_PARTY_LOCK['ITec'],$mainUser['Account'],$projectId);
                if ($isAuth){
                    $lockPublicNum++;
                }
            }
            $lockNum = $lockPublicNum + $lockPersonnelNum;
        }

        return $lockNum;
    } 
}

