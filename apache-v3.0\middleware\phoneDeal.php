<?php
/*
 * @Description: phone2和phone3空值处理
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-12 15:05:31
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;

include_once __DIR__."/../util/model.php";
include_once __DIR__."/../util/computed.php";
class CPhoneDeal implements IMiddleware {
    public $phone;
    public $phone2;
    public $phone3;
    public $phoneCode;
    public function handle(\Closure $next) {
        global $cLog;
        $params = [
            "Phone"=>"",
            "Phone2"=>"",
            "Phone3"=>"",
            "PhoneCode"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        
        $phone = $params["Phone"];
        $phone2 = $params["Phone2"];
        $phone3 = $params["Phone3"];
        $phoneCode = $params["PhoneCode"];

        $cLog->actionLog("#middle#phoneDeal#phone=$phone;phone2=$phone2;phone3=$phone3;phoneCode=$phoneCode");
        $phone = $phone ?: "";
        $phone2 = $phone2 ?: "";
        $phone3 = $phone3 ?: "";
        $phoneCode = $phoneCode ?: "";
        \util\computed\setGAppData(["Phone"=>$phone,"Phone2"=>$phone2,"Phone3"=>$phone3,"PhoneCode"=>$phoneCode]);
        $next();
    }
}