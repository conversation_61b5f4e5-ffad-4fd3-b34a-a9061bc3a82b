<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-19 17:14:52
 * @LastEditors  : kxl
 */
namespace event;
include_once __DIR__."/../database/main.php";
class CGetPCBranch {
    function on () {
    }
    function emit ($manage) {
        global $cLog;
        $id = $manage;
        $cLog->actionLog("#event#getPCBranch.emit#id=$id");
        $db = \database\CDatabase::getInstance();
        $grade = $db->querySList("select Grade from Account where ID = :ID",[":ID"=>$id])[0]["Grade"];
        \util\computed\setGAppData(["userAliasId"=>$id]);
        include_once __DIR__."/../util/computed.php";
        if($grade == PERSONGRADE) {
            $cLog->actionLog("#event#getPCBranch.emit#branch=getPerBranch");
            \util\computed\setGAppBranch("getPerBranch");
        }else{
            $cLog->actionLog("#event#getPCBranch.emit#branch=getComBranch");
            \util\computed\setGAppBranch("getComBranch");
        }
    }
}