<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors: Please set LastEditors
 */
namespace model;

include_once __DIR__."/../../util/model.php";
include_once __DIR__."/../../util/string.php";
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/time.php";
require_once __DIR__.'/../communityData/util.func.php';

class CPropertyData
{
    public function getBill()
    {
        $params = [
            "userAlias"=>"",
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $data = $this->db->queryAllList("PropertyBillingInfo", ["equation"=>[":Account"=>$user]])[0];
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function modifyBill()
    {
        $params = [
            "userAlias"=>"",
            "BillingTitle"=>"",
            "Contactor"=>"",
            "Street"=>"",
            "City"=>"",
            "Postcode"=>"",
            "Country"=>"",
            "TelePhone"=>"",
            "Fax"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $title = $params["BillingTitle"];
        $contactor = $params["Contactor"];
        $street = $params["Street"];
        $city = $params["City"];
        //校验邮编格式
        communityData\checkPostalCode($params["Postcode"]);
        //邮编设置去除前后空格
        $postcode = trim($params["Postcode"]);
        $country = $params["Country"];
        $telePhone = $params["TelePhone"];
        $fax = $params["Fax"];

        $this->db->update2ListWKey("PropertyBillingInfo", [
            ":Account"=>$user,
            ":BillingTitle"=>$title,
            ":Contactor"=>$contactor,
            ":Street"=>$street,
            ":City"=>$city,
            ":Postcode"=>$postcode,
            ":Country"=>$country,
            ":TelePhone"=>$telePhone,
            ":Fax"=>$fax
        ], "Account");
    }

    public function getReceipt()
    {
        $params = [
            "user"=>"",
            "data"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["user"];
        $data = $params["data"];
        $billData = $this->db->queryAllList("PropertyBillingInfo", ["equation"=>[":Account"=>$user]])[0];
        $data = array_merge($data, $billData);
        $myData = $this->db->queryAllList("Account", ["equation"=>[":Account"=>$user]])[0];
        $info = $this->db->queryAllList("PropertyInfo", ["equation"=>[":AccountID"=>$myData["ID"]]])[0];
        $name = $info["FirstName"]." ".$info["LastName"];
        $buyerInfo = $name." ".$myData["Email"];
        $data["BuyerInfo"] = $buyerInfo;
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function add()
    {
        global $cMessage;
        $params = [
            "userAlias"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "Name"=>"",
            "Email"=>"",
            "Language"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $name = $params["Name"];
        $email = $params["Email"];
        $lang = $params["Language"];
        $myData = $this->db->queryAllList("Account", ["equation"=>[":Account"=>$user]])[0];
        $emails = $this->db->queryAllList("Account", ["equation"=>[":Email"=>$email]]);
        if (count($emails) != 0) {
            $cMessage->echoErrorMsg(StateEmailExits);
        }

        $groupData = $this->db->queryAllList("Account", ["equation"=>[":ID"=>$myData["ManageGroup"]]])[0];

        $passwd = \util\string\generatePw(8);
        $now = \util\computed\getNow();
        $this->db->insert2List("Account", [":Email"=>$email,":Account"=>$email,":LoginAccount"=>$email,":Grade"=>PROPERTYMANAGE,":Passwd"=>md5($passwd),":Language"=>$lang,
            ":ParentID"=>$myData["ManageGroup"],":ManageGroup"=>$myData["ManageGroup"],":TimeZone"=>$groupData["TimeZone"], ":UUID" => \util\string\uuid(),
            ':ParentUUID'=>$groupData['UUID'], ':CreateTime' => $now]);
        $id = $this->db->lastInsertId();
        $this->db->insert2List("PropertyInfo", [":AccountID"=>$id,":FirstName"=>$firstName,":LastName"=>$lastName]);
        $this->db->insert2List("PropertyBillingInfo", [":Account"=>$email]);
        \util\computed\setGAppData(["Passwd"=>$passwd]);
        // createPropertyWorkEmailNotify($email, $name, $passwd);
        $this->auditLog->setLog(AuditCodeAddPM, $this->env, [$email], $email);
    }

    public function edit()
    {
        $params = [
            "userAlias"=>"",
            "FirstName"=>"",
            "LastName"=>"",
            "ID"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $firstName = $params["FirstName"];
        $lastName = $params["LastName"];
        $id = $params["ID"];
        $data = $this->db->querySList('select Account,UUID from Account where ID = :ID', [':ID'=>$id])[0];
        $this->db->update2ListWKey("PropertyInfo", [":AccountID"=>$id,":FirstName"=>$firstName,":LastName"=>$lastName], "AccountID");
        //修改pm名字同步修改pm app中的name
        $pmMap = $this->db->querySList('select PersonalAccount from PmAccountMap where AccountUUID = :AccountUUID', [':AccountUUID' => $data['UUID']]);
        if (!empty($pmMap)) {
            $Name = $firstName .' '. $lastName;
            $personalAccounts = \util\arr\implodeWithQuotation(array_column($pmMap, 'PersonalAccount'));
            $this->db->exec2ListWArray("update PersonalAccount set Name = :Name, FirstName = :FirstName, LastName = :LastName where Account in ({$personalAccounts})", [
                ':Name' => $Name, ':FirstName' => $firstName, ':LastName' => $lastName
            ]);
        }

        $this->auditLog->setLog(AuditCodeEditPM, $this->env, [$data['Account']], $data['Account']);
    }

    public function delete()
    {
        global $cMessage;
        $params = [
            "userAlias"=>"",
            "ID"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $id = $params["ID"];
        $datas = $this->db->querySList("select A.ID,A.Account from Account A left join Account B on A.ManageGroup = B.ManageGroup where A.ID = :ID and B.Account = :Account", [":ID"=>$id,":Account"=>$user]);
        if (count($datas) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $this->db->delete2ListWKey("PropertyInfo", "AccountID", $id);
        $this->db->delete2ListWKey("PropertyMngList", "PropertyID", $id);
        $this->db->delete2ListWKey("PropertyBillingInfo", "Account", $datas[0]["Account"]);
        $this->db->delete2ListWID("Account", $id);
        // V6.5 TODO 删除对应的PM APP
        $this->auditLog->setLog(AuditCodeDeletePM, $this->env, [$datas[0]["Account"],$datas[0]["Account"]]);
    }

    public function query()
    {
        $params = [
            "userAlias"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];
        $data = $this->db->querySList("select I.FirstName,I.LastName,A.* from Account A
            left join Account B on A.ManageGroup = B.ManageGroup left join PropertyInfo I on I.AccountID = A.ID
            where B.Account = :Account and A.Grade = 31 order by A.ID desc", [":Account"=>$user]);
        $data = \util\time\setQueryTimeZone($data, $timeZone, $customizeForm);
        foreach ($data as &$val) {
            $val["Name"] = $val["FirstName"]." ".$val["LastName"];
            $community = $this->db->querySList("select A.Location from Account A left join PropertyMngList M on A.ID = M.CommunityID where PropertyID = :PropertyID", [":PropertyID"=>$val["ID"]]);
            $val["Community"] = [];
            foreach ($community as $value) {
                array_push($val["Community"], $value["Location"]);
            }
            $val["Community"] = implode(",", $val["Community"]);
        }
        \util\computed\setGAppData(["data"=>$data]);
    }

    public function resetpw()
    {
        global $cMessage;
        $params = [
            "userAlias"=>"",
            "ID"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $user = $params["userAlias"];
        $id = $params["ID"];

        $passwd = \util\string\generatePw(8);
        $data = $this->db->querySList(
            "select A.ID from Account A join Account B on A.ManageGroup = B.ManageGroup where B.Account = :Account and A.ID = :ID and A.Grade = 31",
            [":Account"=>$user,":ID"=>$id]
        );
        if (count($data) == 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $this->db->update2ListWID("Account", [":ID"=>$id,":Passwd"=>md5($passwd)]);
        \util\computed\setGAppData(["passwd"=>$passwd]);
    }
}
