<?php
namespace model;

require_once __DIR__.'/util.func.php';
require_once __DIR__.'/../staff/util.func.php';

class CAccess
{
    /**
     * @description 添加权限组
     * <AUTHOR> @params Name 权限组名称
     * @params Device 设备配置数组，增加SecurityRelay配置
     * @params DateFlag 重复模式每周时配置的
     * @params StartTime 重复模式每天：开始时间
     * @params StopTime 重复模式每天：结束时间
     * @params StartDay 重复模式不重复：开始日期
     * @params StopDay 重复模式不重复：结束日期
     * @params SchedulerType 重复模式 0-不重复 1-每天 2-每周
     * @return void
     * @lastEditor csc 2022/3/29 15:02 V6.4
     */
    public function add()
    {
        $params = [
            "Name"=>"",
            "Device"=>"",
            "DateFlag"=>"",
            "StartTime"=>"",
            "StopTime"=>"",
            "StartDay"=>"",
            "StopDay"=>"",
            "SchedulerType"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params["Name"];
        $devices = $params["Device"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $startDay = $params["StartDay"];
        $stopDay = $params["StopDay"];
        $schedulerType = $params["SchedulerType"];
        $communityId = $params["userAliasId"];

        access\checkAccessMacValid($devices, $communityId);
        access\checkStartLessStopTime($params);
        access\checkNameUnique($name, $communityId);

        list($dateFlag, $schedulerType) = access\dealDataFlag($dateFlag, $schedulerType);
        $beginTime = "$startDay 00:00:00";
        $endTime = "$stopDay 23:59:59";
        $now = \util\computed\getNow();
        $this->db->insert2List("AccessGroup", [
            ":Name"=>$name,
            ":CreateTime"=>$now,
            ":CommunityID"=>$communityId,
            ":SchedulerType"=>$schedulerType,
            ":DateFlag"=>$dateFlag,
            ":BeginTime"=>$beginTime,
            ":EndTime"=>$endTime,
            ":StartTime"=>$startTime,
            ":StopTime"=>$stopTime
        ]);
        $id = $this->db->lastInsertId();

        $macs = [];
        foreach ($devices as $device) {
            array_push($macs, $device['MAC']);
            $relay = access\getRelay($device['Relay']);
            $securityRelay = access\getRelay($device['SecurityRelay']);
            $this->db->insert2List('AccessGroupDevice', [':AccessGroupID'=>$id, ':MAC'=>$device['MAC'], ":Relay"=>$relay, ":SecurityRelay"=>$securityRelay]);
        }
        \util\computed\setGAppData(["mac"=>$macs, "ID"=>$id]);
    }

    /**
     * @description 修改权限组
     * <AUTHOR> @params ID
     * @params Name 权限组名称
     * @params Device 设备配置数组，增加SecurityRelay配置
     * @params DateFlag 重复模式每周时配置的
     * @params StartTime 重复模式每天：开始时间
     * @params StopTime 重复模式每天：结束时间
     * @params StartDay 重复模式不重复：开始日期
     * @params StopDay 重复模式不重复：结束日期
     * @params SchedulerType 重复模式 0-不重复 1-每天 2-每周
     * @return void
     * @lastEditor csc 2022/3/29 15:05 V6.4
     */
    public function edit()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "Name"=>"",
            "Device"=>"",
            "DateFlag"=>"",
            "StartTime"=>"",
            "StopTime"=>"",
            "StartDay"=>"",
            "StopDay"=>"",
            "SchedulerType"=>"",
            "userAliasId"=>"",
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $name = $params["Name"];
        $devices = $params["Device"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $startDay = $params["StartDay"];
        $stopDay = $params["StopDay"];
        $schedulerType = $params["SchedulerType"];
        $communityId = $params["userAliasId"];

        // V6.5.2.5 编辑时，需要根据旧的权限组情况进行不同的判断逻辑。如果旧的设备只有最外围公共，只能编辑最外围公共
        // 如果有某个building公共，只能编辑这个building设备，且至少一台
        access\checkAccessMacValid($devices, $communityId);
        access\checkStartLessStopTime($params);
        access\checkNameUnique($name, $communityId, $id);

        list($dateFlag, $schedulerType) = access\dealDataFlag($dateFlag, $schedulerType);
        $beginTime = "$startDay 00:00:00";
        $endTime = "$stopDay 23:59:59";

        $this->db->update2ListWID('AccessGroup', [
            ":ID"=>$id,
            ":Name"=>$name,
            ":CommunityID"=>$communityId,
            ":SchedulerType"=>$schedulerType,
            ":DateFlag"=>$dateFlag,
            ":BeginTime"=>$beginTime,
            ":EndTime"=>$endTime,
            ":StartTime"=>$startTime,
            ":StopTime"=>$stopTime
        ]);

        // 旧设备
        $oldDevices = $this->db->querySList('select MAC from AccessGroupDevice where AccessGroupID=:AccessGroupID', [":AccessGroupID"=>$id]);
        // 判断旧设备是全部公共还是存在楼栋设备
        $oldMacs = array_column($oldDevices, 'MAC');
        $buildingId = [];
        if (count($oldMacs) !== 0) {
            $data = $this->db->querySList('select UnitID from Devices where Grade=2 and MAC in ("'.implode('","',$oldMacs).'")');
            $buildingId = count($data) === 0 ? [] : array_column($data, 'UnitID');
        }
        $buildingId = array_unique($buildingId);
        
        
        // 删除所有设备
        $this->db->exec2ListWArray("delete from AccessGroupDevice where AccessGroupID=:AccessGroupID", [":AccessGroupID" => $id]);

        $haveBuildDevice = [];
        $macs = [];
        foreach ($devices as $device) {
            array_push($macs, $device['MAC']);
            $relay = access\getRelay($device['Relay']);
            $securityRelay = access\getRelay($device['SecurityRelay']);

            // 只更新最外围公共设备和这个楼栋的设备，若有其他楼栋设备则忽略，防止数据异常导致更新失败
            $count = $this->db->querySList(
                "select count(*) as total from Devices where MAC = :MAC and Grade = 1",
                [":MAC"=>$device['MAC']]
            )[0]['total'];
            if ($count == 0) {
                if (count($buildingId) === 0) {
                    continue;
                }

                $deviceData = $this->db->querySList(
                    "select Grade,UnitID from Devices where MAC = :MAC",
                    [":MAC"=>$device['MAC']]
                );
                if ($deviceData[0]['Grade'] !== '2' || !in_array($deviceData[0]['UnitID'], $buildingId)) {
                    continue;
                }

                $haveBuildDevice[] = $deviceData[0]['UnitID'];
            }
            $this->db->insert2List('AccessGroupDevice', [':AccessGroupID'=>$id, ':MAC'=>$device['MAC'], ":Relay"=>$relay, ":SecurityRelay"=>$securityRelay]);
        }

        $haveBuildDevice = array_unique($haveBuildDevice);
        if (count($haveBuildDevice) !== count($buildingId)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        foreach ($oldDevices as $device) {
            array_push($macs, $device['MAC']);
        }
        $macs = array_unique($macs);
        \util\computed\setGAppData(["mac"=>$macs]);
    }

    public function delete()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $userId = $params['userAliasId'];
        $this->log->actionLog("#model#access#delete#id=$id;userId=$userId");
        access\checkValid($id, $userId);

        // 查询MAC需要通知后台
        $macs = access\getAccessMACArray($id);
        access\deleteAccess($id);
        \util\computed\setGAppData(["mac"=>$macs]);
    }

    public function addPerson()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            // User: [{ID:1, Type: 'Delivery'}.....]
            "User"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $addUsers = $params['User'];
        $userId = $params['userAliasId'];
        access\checkValid($id, $userId);

        $delMainUserArr = [];
        // 检查被添加用户是否能被添加
        list($delivery, $staff, $accounts) = access\getPersonNotInAccess($id);
        foreach ($delivery as &$val) {
            $val = $val['ID'];
        }
        unset($val);
        foreach ($staff as &$val) {
            $val = $val['ID'];
        }
        unset($val);
        foreach ($accounts as &$val) {
            $val = $val['ID'];
        }
        unset($val);
        foreach ($addUsers as $addUser) {
            $type = $addUser['Type'];
            $users = ['Delivery'=>$delivery, 'Staff'=>$staff, 'User'=>$accounts][$type];
            
            if (!in_array($addUser['ID'], $users)) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            
            if ($type == 'Delivery') {
                $this->db->insert2List('DeliveryAccess', [':DeliveryID'=> $addUser['ID'], ':AccessGroupID'=> $id]);
            } elseif ($type == 'Staff') {
                $this->db->insert2List('StaffAccess', [':StaffID'=> $addUser['ID'], ':AccessGroupID'=> $id]);
            } else {
                $accountData = $this->db->querySList('select Account, Role, ParentUUID from PersonalAccount where ID = :ID', [':ID'=>$addUser['ID']])[0];
                $account = $accountData['Account'];
                $this->db->insert2List('AccountAccess', [":Account"=> $account, ":AccessGroupID"=> $id]);
                if (intval($accountData['Role']) === COMENDSROLE) {
                    $mainAccount = $this->db->querySList('select Account from PersonalAccount where UUID = :UUID', [':UUID'=>$accountData['ParentUUID']])[0]['Account'];
                    array_push($delMainUserArr, $mainAccount);
                } elseif (intval($accountData['Role']) === COMENDMROLE) {
                    array_push($delMainUserArr, $accountData['Account']);
                }
            }
        }
        // 主账号Sip去重
        $delMainUserArr = array_unique($delMainUserArr);

        // 查询权限组MAC地址
        $macs = access\getAccessMACArray($id);
        \util\computed\setGAppData(["mac"=>$macs, 'delMainUserArr' => $delMainUserArr]);
    }

    public function deletePerson()
    {
        $params = [
            "ID"=>"",
            // User: [{ID:1, Type: 'Delivery'}.....]
            "User"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $delUsers = $params['User'];
        $userId = $params['userAliasId'];
        access\checkValid($id, $userId);

        $delMainUserArr = [];
        foreach ($delUsers as $delUser) {
            $type = $delUser['Type'];

            if ($type == 'Delivery') {
                $this->db->exec2ListWArray(
                    'delete from DeliveryAccess where DeliveryID=:DeliveryID and AccessGroupID=:AccessGroupID',
                    [':DeliveryID'=> $delUser['ID'], ':AccessGroupID'=> $id]
                );
            } elseif ($type == 'Staff') {
                $this->db->exec2ListWArray(
                    'delete from StaffAccess where StaffID=:StaffID and AccessGroupID=:AccessGroupID',
                    [':StaffID'=> $delUser['ID'], ':AccessGroupID'=> $id]
                );
            } else {
                $accountData = $this->db->querySList('select Account, Role, ParentUUID from PersonalAccount where ID = :ID', [':ID'=>$delUser['ID']])[0];
                $account = $accountData['Account'];
                if (intval($accountData['Role']) === COMENDSROLE) {
                    $mainAccount = $this->db->querySList('select Account from PersonalAccount where UUID = :UUID', [':UUID'=>$accountData['ParentUUID']])[0]['Account'];
                    array_push($delMainUserArr, $mainAccount);
                } elseif (intval($accountData['Role']) === COMENDMROLE) {
                    array_push($delMainUserArr, $accountData['Account']);
                }
                $this->db->exec2ListWArray('delete from AccountAccess where Account=:Account and AccessGroupID=:AccessGroupID', [":Account"=> $account, ":AccessGroupID"=> $id]);
            }
        }
        // 主账号Sip去重
        $delMainUserArr = array_unique($delMainUserArr);

        // 查询权限组MAC地址
        $macs = access\getAccessMACArray($id);
        \util\computed\setGAppData(["mac"=>$macs, 'delMainUserArr' => $delMainUserArr]);
    }

    public function getList()
    {
        list($offset, $rows) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        $params = [
            "Key" => "",
            "userAliasId"=> "",
            "SelfCustomizeForm"=> "",
            "Type"=> ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $key = $params["Key"];
        $communityId = $params["userAliasId"];
        $customizeForm = $params["SelfCustomizeForm"];
        $type = $params["Type"];

        // 6.3区别社区和办公
        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];

        if ($type == 'all') {
            $total = $this->db->querySList(
                'select count(*) from AccessGroup where CommunityID = :CommunityID and Name like :Name'.($key!=''?' and UnitID = 0':''),
                [":CommunityID"=>$communityId, ":Name"=>"%$key%"]
            )[0]['count(*)'];
            $accesses = $this->db->querySList(
                "select * from AccessGroup where CommunityID = :CommunityID and Name like :Name". ($key!=""?" and UnitID = 0":"") ." order by ID desc limit $offset,$rows",
                [":CommunityID"=>$communityId, ":Name"=>"%$key%"]
            );
        } elseif ($type == 'customized') {
            $total = $this->db->querySList(
                'select count(*) from AccessGroup where CommunityID = :CommunityID and UnitID = 0 and Name like :Name',
                [":CommunityID"=>$communityId, ":Name"=>"%$key%"]
            )[0]['count(*)'];
            $accesses = $this->db->querySList(
                "select * from AccessGroup where CommunityID = :CommunityID and UnitID = 0 and Name like :Name order by ID desc limit $offset,$rows",
                [":CommunityID"=>$communityId, ":Name"=>"%$key%"]
            );
        } elseif ($type == 'system') {
            $total = $this->db->querySList(
                'select count(*) from AccessGroup where CommunityID = :CommunityID and UnitID != 0',
                [":CommunityID"=>$communityId]
            )[0]['count(*)'];
            $accesses = $this->db->querySList(
                "select * from AccessGroup where CommunityID = :CommunityID and UnitID != 0 order by ID desc limit $offset,$rows",
                [":CommunityID"=>$communityId]
            );
        }

        foreach ($accesses as &$value) {
            if ($value['UnitID'] != 0) {
                $unitName = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$value['UnitID']])[0]['UnitName'];
                if ($projectGrade == COMMUNITYGRADE) {
                    $value['Name'] = vsprintf(MSGTEXT['BuildAccessName'], [$unitName]);
                } elseif ($projectGrade == OFFICEGRADE) {
                    $value['Name'] = vsprintf(MSGTEXT['departmentAccessName'], [$unitName]);
                }
            }
            $value['Device'] = access\getAccessDevicesLocation($value['ID'], $value['UnitID'], $communityId);

            // V6.4 权限组数目统计忽略PM App
            $residentCount = $this->db->querySList(
                'select count(*) from AccountAccess A join PersonalAccount P on A.Account = P.Account where A.AccessGroupID = :AccessGroupID and P.Role != 40',
                [":AccessGroupID"=> $value["ID"]]
            )[0]['count(*)'];
            $staffCount = 0;
            $deliveryCount = 0;
            $staffCount = $this->db->querySList(
                'select count(*) from StaffAccess where AccessGroupID = :AccessGroupID',
                [":AccessGroupID"=> $value["ID"]]
            )[0]['count(*)'];
            $deliveryCount = $this->db->querySList(
                'select count(*) from DeliveryAccess where AccessGroupID = :AccessGroupID',
                [":AccessGroupID"=> $value["ID"]]
            )[0]['count(*)'];

            list($value['BeginTime'], $value['EndTime'], $value['StartTime'], $value['StopTime']) =
            access\setAccessTimeFormat(["BeginTime"=>$value['BeginTime'],
                "EndTime"=>$value["EndTime"],
                "StartTime"=>$value['StartTime'],
                "StopTime"=>$value['StopTime']
            ], $customizeForm);

            $count = $residentCount+$staffCount+$deliveryCount;
            $value['User'] = $count;
        }

        \util\computed\setGAppData(["data"=>["detail"=>$accesses, "row"=>$accesses, "total"=>$total]]);
    }

    public function getInfo()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $communityId = $params['userAliasId'];
        access\checkValid($id, $communityId);

        $info = $this->db->querySList('select * from AccessGroup where ID = :ID', [':ID' => $id])[0];

        if ($info['UnitID'] != 0) {
            $unitName = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$info['UnitID']])[0]['UnitName'];
            $info['Name'] = vsprintf(MSGTEXT['BuildAccessName'], [$unitName]);
        }

        $info['Device'] = $this->db->querySList('select A.*, D.Grade from AccessGroupDevice A join Devices D on D.MAC = A.MAC where A.AccessGroupID = :ID', [':ID' => $id]);
        foreach ($info['Device'] as &$val) {
            $val['Relay'] = access\getRelayKeyPosition($val['Relay']);
            $val['SecurityRelay'] = access\getRelayKeyPosition($val['SecurityRelay']);
        }
        unset($val);
        
        $devices = access\getAccessDevicesLocation($id, $info['UnitID'], $communityId);
        $info['DateFlag'] = access\getDateFlagPosition($info['DateFlag']);
        $info['Location'] = $devices;
        $info['StartDay'] = explode(' ', $info['BeginTime'])[0];
        $info['StopDay'] = explode(' ', $info['EndTime'])[0];
        \util\computed\setGAppData(["data"=>$info]);
    }

    public function getAccessPerson()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $userId = $params['userAliasId'];
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);

        access\checkValid($id, $userId);
        list($delivery, $staff, $account) = access\getAllPersonInAccess($id, $serchKey, $serchValue);
        \util\computed\setGAppData(["data"=>array_merge($account, $delivery, $staff)]);
    }

    public function getNotAccessPerson()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $userId = $params['userAliasId'];
        access\checkValid($id, $userId);
        list($offset, $rows, $serchKey, $serchValue) = \util\model\getQueryLimitAndSearch($this->dataContainer);
        list($delivery, $staff, $account) = access\getPersonNotInAccess($id, $serchKey, $serchValue);
        \util\computed\setGAppData(["data"=>array_merge($account, $delivery, $staff)]);
    }

    public function getAllAccess()
    {
        global $cMessage;
        $params = [
            "ID"=>"",
            "RoomID"=>"",
            "userAliasId"=>"",
            // 6.3 办公项目获取权限组条件
            "DepartmentID"=>"",
            "Role"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        // 获取修改权限组需要用户本身ID，因为需要获取自身已经绑定的权限组
        $id = $params['ID'];
        $roomId = $params['RoomID'];
        $communityId = $params['userAliasId'];
        $departmentId = $params['DepartmentID'];
        $role = $params['Role'];
        $data = $this->db->querySList('select ID,Role,ParentID from PersonalAccount where RoomID = :ID', [":ID"=>$roomId])[0];
        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];
        if ($projectGrade == COMMUNITYGRADE) {
            if ($roomId) {
                $data = $this->db->querySList('select ID,Role,ParentID from PersonalAccount where RoomID = :ID', [":ID"=>$roomId])[0];
                $bindArray = [':ParentID'=>$communityId, ":ID"=>$data["ID"]];
                $count = $this->db->querySList('select count(*) from PersonalAccount where ID = :ID and ParentID = :ParentID', $bindArray)[0]['count(*)'];
                if ($count == 0) {
                    $cMessage->echoErrorMsg(StateNotPermission);
                }
                $id = $id ? $id : $data['ID'];
                $result = access\getUserAllowAccess($id);
            } else {
                $result = $this->db->querySList('select * from AccessGroup where CommunityID = :CommunityID', [":CommunityID"=>$communityId]);
            }
        } elseif ($projectGrade == OFFICEGRADE) {
            if ($role == OFFPERSONNELROLE) {
                $result = access\getUserAllowAccess($id, ['Role' => $role, 'ParentID' => $communityId, 'UnitID' => $departmentId]);
            } else {
                $result = $this->db->querySList('select * from AccessGroup where CommunityID = :CommunityID', [":CommunityID"=>$communityId]);
            }
        }

        foreach ($result as &$val) {
            if ($val['UnitID'] != 0) {
                $unitName = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$val['UnitID']])[0]['UnitName'];
                if ($projectGrade == COMMUNITYGRADE) {
                    $val['Name'] = vsprintf(MSGTEXT['BuildAccessName'], [$unitName]);
                } elseif ($projectGrade ==  OFFICEGRADE) {
                    $val['Name'] = vsprintf(MSGTEXT['departmentAccessName'], [$unitName]);
                }
            }
            
            $val["Device"] = access\getAccessDevicesLocation($val['ID'], $val['UnitID'], $communityId);
        }

        \util\computed\setGAppData(["data"=> $result]);
    }

    public function addUser()
    {
        global $cMessage;
        $params = [
            // 主从账户ID
            "ID"=>"",
            "PIN"=>"",
            "Card"=>"",
            "AccessGroup"=>"",
            "Step"=>"",
            "Device"=>"",
            "DateFlag"=>"",
            "StartTime"=>"",
            "StopTime"=>"",
            "StartDay"=>"",
            "StopDay"=>"",
            "SchedulerType"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $step = $params["Step"];
        $pin = $params['PIN'];
        $card = $params['Card'];
        $accessGroup = $params['AccessGroup'];
        $devices = $params["Device"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $startDay = $params["StartDay"];
        $stopDay = $params["StopDay"];
        $schedulerType = $params["SchedulerType"];
        $communityId = access\getCommunityId($id);
        $data = $this->db->querySList('select ID,Account,UnitID,Role,ParentID from PersonalAccount where ID=:ID', [':ID'=>$id])[0];
        $this->log->actionLog('#model#access#addUser#param='.json_encode($params));
        $account = $data['Account'];
        if ($step == 1) {
            if (access\checkUserAccessValid($id, $accessGroup) !== true) {
                $cMessage->echoErrorMsg(StateNotPermission);
            }
            $pin = $pin === '' ? null : $pin;
            $card = $card === '' ? null : $card;
            // 检查PIN和Card是否重复
            staff\checkPinCardUnique($pin, $communityId, 2, 'PIN');
            staff\checkPinCardUnique($card, $communityId, 2, 'Card');
            access\addUserKeyCard($id, $pin, $card, $accessGroup, $this->env);
            access\checkStartLessStopTime($params);
            access\checkAccessUserMacValid($devices, $id);
    
            list($dateFlag, $schedulerType) = access\dealDataFlag($dateFlag, $schedulerType);
            $beginTime = "$startDay 00:00:00";
            $endTime = "$stopDay 23:59:59";
        } else {
            $pin = null;
            $card = null;
            // 获取所在楼栋的权限组
            $accessGroup = $this->db->querySList('select ID from AccessGroup where UnitID=:UnitID', [":UnitID"=>$data['UnitID']])[0]['ID'];
            if (!$accessGroup) {
                $this->log->actionLog("#model#access#addUser#accessGroup=$accessGroup");
                $cMessage->echoErrorMsg(StateAddFail);
            }
            access\addUserKeyCard($id, $pin, $card, [$accessGroup], $this->env);
            
            $node = $account;
            if ($data['Role'] == COMENDSROLE) {
                $node = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID'=> $data['ParentID']])[0]['Account'];
            }
            // 获取自己所有的设备门口机
            $devices = $this->db->querySList('select MAC, Relay, SecurityRelay from Devices where Node=:Node and Type in (0,1,50)', [":Node"=>$node]);
            foreach ($devices as &$device) {
                $relay = explode(";", $device['Relay']);
                foreach ($relay as $key => &$val) {
                    $val = $key;
                }
                unset($val);
                $device['Relay'] = implode(";", $relay);

                $securityRelay = explode(";", $device['SecurityRelay']);
                foreach ($securityRelay as $key => &$val) {
                    $val = $key;
                }
                unset($val);
                $device['SecurityRelay'] = implode(";", $securityRelay);
            }
            unset($device);

            $dateFlag = 0;
            $endTime = $beginTime = '0000-00-00 00:00:00';
            $schedulerType = 1;
            $startTime = '00:00:00';
            $stopTime = '23:59:59';
        }
        
        $this->db->insert2List('UserAccessGroup', [
            ":Account"=>$account,
            ":SchedulerType"=>$schedulerType,
            ":DateFlag"=>$dateFlag,
            ":BeginTime"=>$beginTime,
            ":EndTime"=>$endTime,
            ":StartTime"=>$startTime,
            ":StopTime"=>$stopTime
        ]);

        $groupId = $this->db->lastInsertId();

        foreach ($devices as $device) {
            $relay = access\getRelay($device['Relay']);
            $securityRelay = access\getRelay($device['SecurityRelay']);
            $this->db->insert2List('UserAccessGroupDevice', [':UserAccessGroupID'=>$groupId, ':MAC'=>$device['MAC'], ":Relay"=>$relay, ":SecurityRelay"=>$securityRelay]);
        }

        \util\computed\setGAppData(["Account"=> $account, "AccessGroup"=> $accessGroup, "CommunityId"=>$communityId]);
    }

    public function getUserForPM()
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>"",
            "SelfTimeZone"=>"",
            "SelfCustomizeForm"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $timeZone = $params["SelfTimeZone"];
        $customizeForm = $params["SelfCustomizeForm"];

        $data = $this->db->querySList('select Account,Role,ParentID from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $account = $data['Account'];
        access\checkUserValid($id, $communityId);


        // 查PIN，card，人脸
        $pin = $this->db->querySList('select * from CommPerPrivateKey where Account = :Account order by ID desc', [':Account'=>$account]);
        $card = $this->db->querySList('select * from CommPerRfKey where Account = :Account order by ID desc', [':Account'=>$account]);
        $face = $this->db->queryAllList('FaceMng', ['equation'=>[':PersonalAccountID'=>$id]]);
        
        $pin = \util\time\setQueryTimeZone($pin, $timeZone, $customizeForm);
        $card = \util\time\setQueryTimeZone($card, $timeZone, $customizeForm);
        $face = \util\time\setQueryTimeZone($face, $timeZone, $customizeForm);

        foreach ($pin as &$val) {
            // 住戶的PIN加密显示
            if ($val['Special'] == '1') {
                $val['Code'] = '****';
            } else {
                $val['Code'] = \util\computed\setPinIsEncryptPin($communityId, $val['Code']);
            }
        }
        unset($val);

        // 获取access group
        $access = $this->db->querySList('select AG.* from AccessGroup AG join AccountAccess A on AG.ID = A.AccessGroupID where A.Account=:Account', [":Account"=>$account]);
        $access = \util\time\setQueryTimeZone($access, $timeZone, $customizeForm, ['StartTime', 'StopTime']);
        // 6.3区别社区和办公
        $projectGrade = $this->db->querySList('select Grade from Account where ID = :ID', [':ID' => $communityId])[0]['Grade'];
        foreach ($access as &$val) {
            if ($val['UnitID'] != 0) {
                $unitName = $this->db->querySList('select UnitName from CommunityUnit where ID = :ID', [":ID"=>$val['UnitID']])[0]['UnitName'];
                if ($projectGrade == COMMUNITYGRADE) {
                    $val['Name'] = vsprintf(MSGTEXT['BuildAccessName'], [$unitName]);
                } elseif ($projectGrade == OFFICEGRADE) {
                    $val['Name'] = vsprintf(MSGTEXT['departmentAccessName'], [$unitName]);
                }
            }

            $val['Location'] = access\getAccessDevicesLocation($val['ID'], $val['UnitID'], $communityId);
        }
        unset($val);

        // 获取自己的权限设置
        $selfAccess = $this->db->querySList('select * from UserAccessGroup where Account = :Account', [":Account"=>$account])[0];
        $selfAccess["Device"] = $this->db->querySList('select * from UserAccessGroupDevice where UserAccessGroupID=:UserAccessGroupID', [":UserAccessGroupID"=>$selfAccess['ID']]);
        // 处理relay及SecurityRelay
        foreach ($selfAccess["Device"] as &$val) {
            $val['Relay'] = access\getRelayKeyPosition($val['Relay']);
            $val['SecurityRelay'] = access\getRelayKeyPosition($val['SecurityRelay']);
        }

        $selfAccess['DateFlag'] = access\getDateFlagPosition($selfAccess['DateFlag']);
        unset($val);
    

        \util\computed\setGAppData(["data"=> ['PIN'=>$pin, 'Card'=>$card, 'Face'=> $face, 'AccessGroup'=>$access, "SelfAccess"=>$selfAccess]]);
    }

    public function addUserPINCard($type)
    {
        $params = [
            "ID"=>"",
            "Code"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $code = $params["Code"];
        // 办公、社区标志
        $projectType = $this->db->querySList("select Grade from Account where ID = :ID", [
            ":ID"=>$communityId])[0]["Grade"]=="23"?"office":"community";
        access\checkUserValid($id, $communityId);
        staff\checkPinCardUnique($code, $communityId, $projectType == 'office' ? 3 : 2, $type == 0 ? 'PIN' : 'Card');
        $data = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $account = $data['Account'];
        $table = ['CommPerPrivateKey', 'CommPerRfKey'][$type];
        $this->db->insert2List($table, [
            ":Account"=>$account,
            ":Code"=>$code,
            ":CommunityID"=>$communityId,
            ":CreateTime"=>\util\computed\getNow()
        ]);
        \util\computed\setGAppData(["Account"=>$account]);
        $this->auditLog->setLog($type == 0 ? AuditCodeAddPin : AuditCodeAddRf, $this->env, [$code], $account);
    }

    public function addUserPIN()
    {
        $this->addUserPINCard(0);
    }

    public function addUserCard()
    {
        $this->addUserPINCard(1);
    }

    public function editUserPINCard($type)
    {
        $params = [
            "ID"=>"",
            "Code"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $code = $params["Code"];
        staff\checkPinCardUnique($code, $communityId, 2, $type == 0 ? 'PIN' : 'Card', $id);
        $table = ['CommPerPrivateKey', 'CommPerRfKey'][$type];
        $account = $this->db->querySList("select Account from $table where ID = :ID", [":ID"=>$id])[0]["Account"];
        if (!($type === 0 && $code === '****')) {
            $this->db->exec2ListWArray("update $table set Code=:Code where ID=:ID and CommunityID=:CommunityID", [
                ":CommunityID"=>$communityId,
                ":ID"=>$id,
                ":Code"=>$code
            ]);
            $this->auditLog->setLog($type == 0 ? AuditCodeEditPin : AuditCodeEditRf, $this->env, [$code], $account);
        }
        \util\computed\setGAppData(["Account"=>$account]);
    }

    public function editUserPIN()
    {
        $this->editUserPINCard(0);
    }

    public function editUserCard()
    {
        $this->editUserPINCard(1);
    }

    public function deleteUserPINCard($type)
    {
        $params = [
            "ID"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];

        $table = ['CommPerPrivateKey', 'CommPerRfKey'][$type];
        $cardData = $this->db->querySList("select Account, Code from $table where ID = :ID", [":ID"=>$id])[0];
        $account = $cardData["Account"];
        $code = $cardData["Code"];
        $this->db->exec2ListWArray("delete from $table where ID=:ID and CommunityID=:CommunityID", [
            ":CommunityID"=>$communityId,
            ":ID"=>$id
        ]);
        \util\computed\setGAppData(["Account"=>$account]);
        $this->auditLog->setLog($type == 0 ? AuditCodeDeletePin : AuditCodeDeleteRf, $this->env, [$code], $account);
    }

    public function deleteUserPIN()
    {
        $this->deleteUserPINCard(0);
    }

    public function deleteUserCard()
    {
        $this->deleteUserPINCard(1);
    }

    public function setUserAccessGroup()
    {
        global $cMessage;
        $params = [
            // 主從账户ID
            "ID"=>"",
            "AccessGroup"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params['ID'];
        $accessGroup = $params['AccessGroup'];
        $communityId = $params['userAliasId'];

        // 检查用户权限
        access\checkUserValid($id, $communityId);
        if (access\checkUserAccessValid($id, $accessGroup) !== true) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }

        // 删除原本所有权限组，重新添加
        $data = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $account = $data['Account'];
        // 之前的权限组
        $oldAccess = $this->db->querySList('select AccessGroupID from AccountAccess where Account = :Account', [':Account'=> $account]);
        foreach ($oldAccess as &$val) {
            $val = $val['AccessGroupID'];
        }
        unset($val);

        $this->db->delete2ListWKey('AccountAccess', 'Account', $account);
        foreach ($accessGroup as $val) {
            $this->db->insert2List('AccountAccess', [
                ":Account"=>$account,
                ":AccessGroupID"=>$val
            ]);
        }
        \util\computed\setGAppData(["Account"=> $account, "AccessGroup"=> array_unique(array_merge($accessGroup, $oldAccess))]);
    }

    public function setSelfAccess()
    {
        $params = [
            // 主從账户ID
            "ID"=>"",
            "Device"=>"",
            "DateFlag"=>"",
            "StartTime"=>"",
            "StopTime"=>"",
            "StartDay"=>"",
            "StopDay"=>"",
            "SchedulerType"=>"",
            "userAliasId"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $devices = $params["Device"];
        $dateFlag = $params["DateFlag"];
        $startTime = $params["StartTime"];
        $stopTime = $params["StopTime"];
        $startDay = $params["StartDay"];
        $stopDay = $params["StopDay"];
        $schedulerType = $params["SchedulerType"];
        //检查社区是否允许pm有pub+private权限
        access\checkCommunityHaveAccessArea($communityId);
        // 检查用户权限
        access\checkUserValid($id, $communityId);
        access\checkStartLessStopTime($params);
        access\checkAccessUserMacValid($devices, $id);

        $data = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [":ID"=>$id])[0];
        $account = $data['Account'];

        list($dateFlag, $schedulerType) = access\dealDataFlag($dateFlag, $schedulerType);
        $beginTime = "$startDay 00:00:00";
        $endTime = "$stopDay 23:59:59";

        $accessId = $this->db->querySList('select ID from UserAccessGroup where Account=:Account', [':Account'=>$account])[0]['ID'];
        $this->db->update2ListWKey('UserAccessGroup', [
            ":SchedulerType"=>$schedulerType,
            ":DateFlag"=>$dateFlag,
            ":StartTime"=>$startTime,
            ":StopTime"=>$stopTime,
            ":BeginTime"=>$beginTime,
            ":EndTime"=>$endTime,
            ":Account"=>$account
        ], "Account");
        $this->db->delete2ListWKey('UserAccessGroupDevice', 'UserAccessGroupID', $accessId);
        foreach ($devices as $device) {
            $relay = access\getRelay($device['Relay']);
            $securityRelay = access\getRelay($device['SecurityRelay']);
            $this->db->insert2List('UserAccessGroupDevice', [':UserAccessGroupID'=>$accessId, ':MAC'=>$device['MAC'], ":Relay"=>$relay, ":SecurityRelay"=>$securityRelay]);
        }

        \util\computed\setGAppData(["Account"=> $account]);
    }
}
