<?php

namespace plan\process;

const USER_PROCESS_V2 = [
    "addMainEndUserForNewCheck" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery
        ]
    ],
    "addMainEndUserForNew" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addNewPmGetCallType"
        ], [
            "type" => "model",
            "model" => "user.addComMainUser"
        ], [
            "type" => "model",
            "model" => "access.addUser"
        ], [
            "type" => "model",
            "model" => "photo.addFace"
        ], [
            "type" => "model",
            "model" => "notify.addFace",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "user.afterAddComMainUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "model",
            "model" => "notify.userCreateComEmail",
        ], [
            "type" => "model",
            "model" => "notify.userMainComAdd",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],
    "addSubEndUserForNewCheck" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "setMngToMainAliasWRoom"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "maxSubUserCheck"]
            ]
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery
        ]
    ],
    "addSubEndUserForNew" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "setMngToMainAliasWRoom"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck", "dataContainer" => ["name" => "const", "data" => ["ID" => null]]],
                ["name" => "maxSubUserCheck"]
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.addComSubUser",
        ], [
            "type" => "model",
            "model" => "access.addUser"
        ], [
            "type" => "model",
            "model" => "user.afterAddComSubUser",
        ], [
            "type" => "model",
            "model" => "user.afterAddUser",
        ], [
            "type" => "middle",
            "queue" => [
                ["name" => "setUserToAlias"],
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "photo.addFace"
        ], [
            "type" => "model",
            "model" => "notify.addFace",
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "notify.userCreateComEmail",
        ], [
            "type" => "model",
            "model" => "notify.userSubComAdd",
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd,
            "options" => ["id" => "ID", "active" => "Active"]
        ]
    ],

    'queryNewUserInfoForPM' => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "userData.queryInfoForPM",
        ], [
            "type" => "echo",
            "code" => StateSuccessQuery,
            "options" => ["data" => "data"]
        ]
    ],

    'editNewUserInfoForPM' => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "phoneDeal"],
                ["name" => "firstLastNameDeal"],
                ["name" => "userNameCheck"],
                ["name" => "userEmailDeal"],
                ["name" => "userEmailCheck"],
                ["name" => "userMobileDeal"],
                ["name" => "userMobileCheck"],
            ]
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => "ID",
            "action" => "on"
        ], [
            "type" => "model",
            "model" => "user.updateAllUserForPM",
        ], [
            "type" => "event",
            "event" => "editUserEmail",
            "params" => ["ID", "Email"],
            "action" => "emit"
        ], [
            "type" => "branches",
            "branches" => [
                "changeEmail" => [
                    [
                        "type" => "model",
                        "model" => "user.afterUpdateEmail"
                    ], [
                        "type" => "model",
                        "model" => "notify.userCreateComEmail",
                    ],
                ]
            ]
        ], [
            "type" => "model",
            // 修改主从账户通知都在这
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessEdit
        ]
    ],

    "deletePMEndUser" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"]
            ]
        ], [
            "type" => "model",
            "model" => "user.deleteEndUserForPM"
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ]
    ],

    'addUserSingleFace' => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
                ["name" => "changeParamValue", "params" => ["Step" => 1]]
            ]
        ], [
            "type" => "model",
            "model" => "photo.addFace",
        ], [
            "type" => "model",
            "model" => "notify.addFace",
        ], [
            "type" => "echo",
            "code" => StateSuccessAdd
        ]
    ],

    'deleteUserSingleFace' => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "setPMAlias"],
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "model",
            "model" => "photo.remove",
        ], [
            "type" => "model",
            "model" => "notify.comDeleteFace",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ]
    ],

    //删除社区用户
    "deleteComAccount" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "middle",
            "queue" => [
                ["name" => "checkDelCode"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.setDelAccountParams"
        ], [
            "type" => "model",
            "model" => "user.deleteEndUserForPM"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "notify.newSetUser",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ]
    ],

    //删除单住户主账号
    "deleteSingleMainAccount" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "middle",
            "queue" => [
                ["name" => "checkDelCode"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.setParamsAndDelSub",
        ], [
            "type" => "model",
            "model" => "user.delete",
        ], [
            "type" => "model",
            "model" => "user.afterDeletePerMain",
        ], [
            "type" => "model",
            "model" => "user.afterDeleteMain",
        ], [
            "type" => "model",
            "model" => "user.afterDelete"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "model",
            "model" => "notify.userMainPerDelete",
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],

    //删除单住户从帐号
    "deleteSingleSubAccount" => [
        [
            "type" => "middle",
            "queue" => [
                ["name" => "getAliasId"],
            ]
        ], [
            "type" => "middle",
            "queue" => [
                ["name" => "checkDelCode"],
            ]
        ], [
            "type" => "database",
            "method" => "begin"
        ], [
            "type" => "model",
            "model" => "user.delSingleSubAccount"
        ], [
            "type" => "database",
            "method" => "commit"
        ], [
            "type" => "echo",
            "code" => StateSuccessDelete
        ],
    ],

];
