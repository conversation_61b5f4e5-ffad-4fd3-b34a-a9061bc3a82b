<?php

namespace model\notify;

trait access
{
    public function changeAccess()
    {
        $params = [
            "mac" => "",
            'userAliasId'=>"",
            "ID"=>"",
            'delMainUserArr' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $mac = $params["mac"];
        $communityId = $params["userAliasId"];
        $id = $params["ID"];
        $delMainUserArr = empty($params['delMainUserArr']) ? []:$params['delMainUserArr'];
        $this->log->actionLog("#model#notify#changeAccess#communityId=$communityId;id=$id;mac=".json_encode($mac)."delMainUserArr=".json_encode($delMainUserArr));
        \webAccessGroupModifyNotify($communityId, $id, $mac, $delMainUserArr);
    }

    // 增删改 社区物业和快递
    public function changeStaffWithAccess()
    {
        $params = [
            "userAliasId" => "",
            // ID必须是数组
            "ID"=>"",
            "Type"=>"",
            // AccessGroup必须是数组
            "AccessGroup"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog('#model#notify#changeStaff#params='.json_encode($params));
        $community = $params["userAliasId"];
        $id = $params["ID"];
        $type = $params["Type"];
        $accessGroup = $params["AccessGroup"];

        if ($type == 0) {
            \webCommunityPersonalModifyNotify($community, [], $id, $accessGroup);
        } else {
            \webCommunityPersonalModifyNotify($community, $id, [], $accessGroup);
        }
    }

    // 单张人脸ID
    public function addFace()
    {
        global $cMessage;
        $params = [
            "path" => "",
            'fileName'=> '',
            'account'=> '',
            'Step'=>'',
            'projectType'=>'',
            'userAliasId' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $path = $params["path"];
        $fileName = $params["fileName"];
        $account = $params["account"];
        $step = $params["Step"];
        $projectType = $params["projectType"];
        $userAliasId = $params['userAliasId'];
        $this->log->actionLog('#model#notify#addFace#params='.json_encode($params));
        if ($step == 1 && $path) {
            $projectInfo = $this->db->querySList('select UUID from Account where ID = :ID', [':ID' => $userAliasId])[0];
            if ($projectType == 'community') {
                //新旧社区导入单张face有区分,新社区采用新接口，状态码会返回多种情况
                $commInfo = $this->db->querySList('select IsNew from CommunityInfo where AccountID = :ID', [':ID' => $userAliasId])[0];
                if($commInfo['IsNew'] === '0'){
                    $res = \uploadOneFacePicHandle($path, $fileName, $account, $projectInfo['UUID']);
                }else{
                    $res = \uploadOneFacePicHandleV65($path, $fileName, $account, $projectInfo['UUID']);
                    if ($res != 0) {
                        $this->log->actionLog('#model#notify#addFace#res='.$res);
                        $cMessage->echoErrorMsg(FACE_ERROR_CODE[$res]);
                    }
                }
            } else {
                $res = \uploadOneOfficeFacePicHandle($path, $fileName, $account, $projectInfo['UUID']);
            }
            
            $this->log->actionLog('#model#notify#addFace#res='.$res);
            if ($res != 0) {
                $cMessage->echoErrorMsg(StateAddFaceFail);
            }
        }
    }

    /**
     * @description: 添加Staff的人脸
     * @author: cj 2022-12-6 10:04:56 V6.5.2
     * @LastEditor: cj 2022-12-6 10:04:56 V6.5.2
     * @param $path 人脸上传路径
     * @param $fileName 照片文件名
     * @param $staffUUID staff UUID
     * @return void
     */
    public function addStaffFace()
    {
        global $cMessage;
        $params = [
            'path' => '',
            'fileName'=> '',
            'staffUUID'=> '',
            'userAliasId' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $path = $params['path'];
        $fileName = $params['fileName'];
        $staffUUID = $params['staffUUID'];
        $communityId = $params['userAliasId'];
        $this->log->actionLog('#model#notify#addStaffFace#params='.json_encode($params));
        $commInfo = $this->db->querySList('select IsNew from CommunityInfo where AccountID = :ID', [':ID' => $communityId])[0];
        // 新小区才有上传Staff人脸功能
        if ($commInfo['IsNew'] === '1' && $path) {
            $res = \uploadStaffOneFacePicHandle($path, $fileName, $staffUUID);
            if ($res != 0) {
                $this->log->actionLog('#model#notify#addStaffFace#res='.$res);
                $cMessage->echoErrorMsg(FACE_ERROR_CODE[$res]);
            }
        }
    }

    /**
     * @description: 删除Staff的人脸
     * @author: cj 2022-12-6 14:40:25 V6.5.2
     * @LastEditor: cj 2022-12-6 14:40:25 V6.5.2
     * @param {Array} $FaceUrlArr 人脸上传文件路径
     * @return void
     */
    public function deleteStaffFace()
    {
        $params = [
            'FaceUrlArr' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#deleteStaffFace#params=".json_encode($params));
        $faceUrlArr = $params['FaceUrlArr'];
        if (count($faceUrlArr) > 0) {
            \delStaffFacePicsHandle($faceUrlArr);
        }
    }

    /**
     * @description: 编辑Staff的人脸
     * @author: cj 2022-12-7 11:27:25 V6.5.2
     * @LastEditor: cj 2022-12-7 11:27:25 V6.5.2
     * @param {bool} $isDeleteFace 是否删除人脸
     * @return void
     */
    public function editStaffFace()
    {
        $params = [
            'IsDeleteFace' => ''
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog("#model#notify#editStaffFace#params=".json_encode($params));
        $isDeleteFace = $params['IsDeleteFace'] ? true:false;
        if ($isDeleteFace) {
            $this->deleteStaffFace();
        }
        $this->addStaffFace();
    }
}
