<?php
/**
 * @description
 * <AUTHOR>
 * @date 2022-03-30 16:00:20
 * @version V6.4
 * @lastEditor cj
 * @lastEditTime 2023-12-13 10:48:23
 * @lastVersion V6.7.0
 */

namespace package\common\model\order\method;

trait Get
{
    public function getSubscribeUserInfo()
    {
        $params = ['Users'];
        list($users) = $this->getParams($params);

        $this->log->debug(
            'params:users={users}',
            ['users' => $users]
        );

        if (!is_array($users)) {
            throw new \Exception("users must be a array.");
        }

        $this->loadUtil('account');
        $users = $this->utils->account->getUsersInfo($users);
        // 用户分类，主账户，从账户，pmApp 3类
        $mains = [];
        $subs = [];
        $pms = [];
        $all = [];
        
        // 社区需要APT NO.
        $roomIdToUserId = [];
        $projectIds = [];


        foreach ($users as $user) {
            // $all[$user['ID']] = $user;
            $role = intval($user['Role']);
            if (in_array($role, MAINROLE) && $role != PMENDMROLE) {
                $user['ProjectId'] = $user['ParentID'];
                array_push($projectIds, $user['ProjectId']);
                array_push($mains, $user);
                if ($role === COMENDMROLE) {
                    // 社区主账户存储RoomID，查询房间号
                    $roomIdToUserId[$user['RoomID']] = $user['ID'];
                }
                $all[$user['ID']] = $user;
            } elseif (in_array($role, SUBROLE)) {
                array_push($subs, $user);
            } elseif ($role === PMENDMROLE) {
                $user['ProjectId'] = $user['ParentID'];
                array_push($projectIds, $user['ProjectId']);
                array_push($pms, $user);
                $all[$user['ID']] = $user;
            }
        }

        if (count($roomIdToUserId) > 0) {
            $roomIds = array_keys($roomIdToUserId);
            $roomData = $this->utils->account->getAptNO($roomIds);
            foreach ($roomData as $value) {
                $roomId = $value['ID'];
                $apt = $value['RoomName'];
                
                $userId = $roomIdToUserId[$roomId];
                $all[$userId] = array_merge($all[$userId], ['APT' => $apt]);
            }
        }

        // 从账户加上小区或者ins的id
        if (count($subs) > 0) {
            $subMainIds = $this->share->util->assocArrayToArray($subs, 'ParentID');
            $subMainUsers = $this->utils->account->getUsersInfo($subMainIds);
            $subMainUserIdKey = [];
            foreach ($subMainUsers as $subMainUser) {
                $subMainUserIdKey[$subMainUser['ID']] = $subMainUser;
            }

            foreach ($subs as &$sub) {
                $sub['ProjectId'] = $subMainUserIdKey[$sub['ParentID']]['ParentID'];
                array_push($projectIds, $sub['ProjectId']);
                $sub['APT'] = $this->utils->account->getAptNO($subMainUserIdKey[
                    $sub['ParentID']]['RoomID'])[0]['RoomName'];
                $all[$sub['ID']] = $sub;
            }
            unset($sub);
        }

        $projectIds = array_unique($projectIds);

        $this->loadUtil('account', true);
        $projectInfos = $this->utils->_common->account->accountSelectByArray([['ID', $projectIds]], 'ID,UUID,Location');

        $projects = [];
        foreach ($projectInfos as $value) {
            $projects[$value['ID']] = $value;
        }

        foreach ($all as &$user) {
            $user['ProjectUUID'] = $projects[$user['ProjectId']]['UUID'];
            $user['ProjectName'] = $projects[$user['ProjectId']]['Location'];
        }
        unset($user);


        return [
            'main' => $mains,
            'sub' => $subs,
            'pm' => $pms,
            'all' => $all
        ];
    }

    /**
     * @description: 获得视频存储的相关信息
     * @param: {array}  VideoSites 单住户或社区的ID
     * @param: {string} ProjectType 项目类型('multiple','single','office')
     * @return array
     * @author: shoubin.chen 2024/11/5 15:10:36 V7.1.0
     * @lastEditor: shoubin.chen 2024/11/5 15:10:36  V7.1.0
     */
    public function getSubscribeVideoStorageInfo()
    {
        $params = ['VideoSites', 'ProjectType'];
        list($videoSites, $projectType) = $this->getParams($params);

        $this->loadUtil('account');
        $this->loadUtil('videoStorage');
        $this->loadUtil('videoStorage', true);

        $projectIds = $tmpSites = $videoStorages = $insIDList = [];
        //单住户
        if ($projectType == PAY_TYPE_SINGLE) {
            $tmpSites = $this->utils->account->personalAccountSelectByArray([['ID', $videoSites], ['Role', PERENDMROLE]]);
            foreach ($tmpSites as $item) {
                $insIDList[] = $item['ParentID'];
            }
            $siteUUIDs = array_column($tmpSites, 'UUID');
            $videoStorages = $this->utils->_common->videoStorage->getVideoStoragesBySingles($siteUUIDs);

        } else if ($projectType == PAY_TYPE_MULTIPLE) {
            // 社区
            $tmpSites = $this->utils->account->accountSelectByArray([['Grade', COMMUNITYGRADE], ['ID', $videoSites]]);
            $projectIds = array_column($tmpSites, 'ID');
            foreach ($tmpSites as $item) {
                $insIDList[] = $item['ManageGroup'];
            }
            $siteUUIDs = array_column($tmpSites, 'UUID');
            $videoStorages = $this->utils->_common->videoStorage->getVideoStoragesByCommunities($siteUUIDs);
        }
        $videoStorages = $this->share->util->arrayColumnAsKey($videoStorages, 'SiteUUID');
        if (count(array_unique($insIDList)) > 1) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
        $tmpSites = $this->share->util->arrayColumnAsKey($tmpSites, 'UUID');
        $insInfo = $this->utils->account->accountSelectByKey('ID', $insIDList[0])[0];
        $disInfo = $this->utils->account->accountSelectByKey('UUID', $insInfo['ParentUUID'])[0];
        foreach ($tmpSites as &$val) {
            if ($projectType == PAY_TYPE_SINGLE) {
                $val['ProjectUUID'] = $insInfo['UUID'];
                $val['ProjectID'] = $insInfo['ID'];
                $val['ProjectName'] = $insInfo['Location'];
                $val['Object'] = $val['Name'];
            } else if ($projectType == PAY_TYPE_MULTIPLE) {
                $val['ProjectUUID'] = $val['UUID'];
                $val['ProjectID'] = $val['ID'];
                $val['ProjectName'] = $val['Location'];
                $val['Object'] = $val['Location'];
            }
            $val['VideoStorageExpireTime'] = $videoStorages[$val['UUID']]['ExpireTime'];
        }
        unset($val);


        $chargeData = $sites = $configs = [];
        $projectMap = [PAY_TYPE_MULTIPLE => 1, PAY_TYPE_SINGLE => 2, PAY_TYPE_OFFICE => 3];

        foreach ($tmpSites as $s) {
            $uuid = $s['UUID'];
            $configItem = $videoStorages[$uuid];
            $siteItem = $tmpSites[$uuid];
            $location = $projectType == PAY_TYPE_MULTIPLE ? $s['Location'] : '';
            $chargeItem = [
                'Instance' => [
                    'Distributor' => $disInfo['Account'],
                    'Installer' => $insInfo['Account'],
                    'Project' => $location,
                    'Type' => $projectMap[$projectType],
                    'ProjectUUID' => $s['ProjectUUID'],
                ]
            ];

            $chargeData[] = $chargeItem;
            $configs[] = $configItem;
            $sites[] = $siteItem;
        }

        return [
            'site' => $sites, 'config' => $configs, 'chargeData' => $chargeData, 'projectIds' => $projectIds
        ];
    }

    /**
     * @description: 获得三方锁的相关信息
     * @param {array} ThirdLockUUIDs 三方锁的UUID
     * @return array
     * @throws \Exception
     * @author: csc 2025/2/6 14:30 V7.1.0
     * @lastEditors: csc 2025/2/6 14:30 V7.1.0
     */
    public function getSubscribeThirdLockInfo()
    {
        $params = ['ThirdLockUUIDs', 'ProjectType'];
        list($thirdLockUUIDs, $projectType) = $this->getParams($params);

        $this->loadUtil('account');
        $this->loadUtil('communityUnit');
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('itec', true);

        $insIDList = [];
        $lockInfo = $this->dao->thirdLockRelateInfo->selectByArray([['LockUUID', $thirdLockUUIDs]]);
        $projectUUIDs = array_column($lockInfo, 'AccountUUID');
        $tmpSites = $this->utils->account->accountSelectByArray([['UUID', $projectUUIDs]]);
        foreach ($tmpSites as $item) {
            $insIDList[] = $item['ManageGroup'];
        }
        if (count(array_unique($insIDList)) > 1) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
        $projectIds = array_column($tmpSites, 'ID');
        $tmpSites = $this->share->util->arrayColumnAsKey($tmpSites, 'UUID');
        $insInfo = $this->utils->account->accountSelectByKey('ID', $insIDList[0])[0];
        $disInfo = $this->utils->account->accountSelectByKey('UUID', $insInfo['ParentUUID'])[0];
        foreach ($tmpSites as &$val) {
            if ($projectType == PAY_TYPE_SINGLE) {
                $val['ProjectUUID'] = $insInfo['UUID'];
                $val['ProjectID'] = $insInfo['ID'];
                $val['ProjectName'] = $insInfo['Location'];
                $val['Object'] = $val['Name'];
            } else if ($projectType == PAY_TYPE_MULTIPLE) {
                $val['ProjectUUID'] = $val['UUID'];
                $val['ProjectID'] = $val['ID'];
                $val['ProjectName'] = $val['Location'];
                $val['Object'] = $val['Location'];
            }
        }
        unset($val);

        $chargeData = $sites = $configs = [];
        $projectMap = [PAY_TYPE_MULTIPLE => 1, PAY_TYPE_SINGLE => 2, PAY_TYPE_OFFICE => 3];

        $personalAccountList = [];
        $personalAccountUUIDs = array_unique(array_filter(array_column($lockInfo, 'PersonalAccountUUID')));
        if (!empty($personalAccountUUIDs)) {
            if ($projectType == PAY_TYPE_SINGLE) {
                $personalAccountList = $this->utils->account->personalAccountSelectByArray([['UUID', $personalAccountUUIDs]]);
            } else if ($projectType == PAY_TYPE_MULTIPLE) {
                $personalAccountList = $this->utils->account->getUnitAndAptInfoByUUID($personalAccountUUIDs);
            }
            $personalAccountList = array_column($personalAccountList, null, 'UUID');
        }
        foreach ($lockInfo as $item) {
            $item['AptName'] = $item['UnitName'] = $item['LockName'] = '';
            if (!empty($item['PersonalAccountUUID'])) {
                if ($projectType == PAY_TYPE_SINGLE) {
                    $item['AptName'] = $personalAccountList[$item['PersonalAccountUUID']]['Name'];
                } else if ($projectType == PAY_TYPE_MULTIPLE) {
                    $item['AptName'] = $personalAccountList[$item['PersonalAccountUUID']]['RoomNumber'];
                    $item['CommunityUnitUUID'] = $personalAccountList[$item['PersonalAccountUUID']]['UnitUUID'];
                    $item['UnitName'] = $personalAccountList[$item['PersonalAccountUUID']]['UnitName'];
                }
            } else if (!empty($item['CommunityUnitUUID'])) {
                $unitInfo = $this->utils->communityUnit->getUnitInfoByKey('UUID', $item['CommunityUnitUUID']);
                $item['UnitName'] = $unitInfo['UnitName'];
            }
            if ($item['Brand'] == '3') {
                $item['LockName'] = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $item['LockUUID']]])[0]['Name'];
            } else if ($item['Brand'] == '6') {
                $item['LockName'] = $this->utils->_common->itec->getItecLock([['UUID', $item['LockUUID']]])[0]['Name'];
            }

            $projectUUID = $item['AccountUUID'];
            $siteItem = $tmpSites[$projectUUID];
            $location = $projectType == PAY_TYPE_MULTIPLE ? $siteItem['Location'] : '';
            $chargeItem = [
                'Instance' => [
                    'Distributor' => $disInfo['Account'],
                    'Installer' => $insInfo['Account'],
                    'Project' => $location,
                    'Type' => $projectMap[$projectType],
                    'ProjectUUID' => $projectUUID,
                ]
            ];
            $chargeData[] = $chargeItem;
            $sites[] = $siteItem;
            $configs[] = $item;
        }

        return [
            'site' => $sites, 'chargeData' => $chargeData, 'projectIds' => $projectIds, 'config' => $configs
        ];
    }

    /**
     * @description: 获得三方锁的相关信息(刷计费模型时使用，计费模型空名额也需要刷，LockUUID是为NULL)
     * @param {array} ThirdLockUUIDs 三方锁的UUID
     * @return array
     * @throws \Exception
     * @author: csc 2025/2/6 14:30 V7.1.0
     * @lastEditors: csc 2025/2/6 14:30 V7.1.0
     */
    public function getSubscribeThirdLockInfoForRelateInfoUUID()
    {
        $params = ['ThirdLockRelateInfoUUIDs', 'ProjectType'];
        list($thirdLockRelateInfoUUIDs, $projectType) = $this->getParams($params);

        $this->loadUtil('account');
        $this->loadUtil('communityUnit');
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('itec', true);

        $insIDList = [];
        $lockInfo = $this->dao->thirdLockRelateInfo->selectByArray([['UUID', $thirdLockRelateInfoUUIDs]]);
        $projectUUIDs = array_column($lockInfo, 'AccountUUID');
        $tmpSites = $this->utils->account->accountSelectByArray([['UUID', $projectUUIDs]]);
        foreach ($tmpSites as $item) {
            $insIDList[] = $item['ManageGroup'];
        }
        if (count(array_unique($insIDList)) > 1) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
        $projectIds = array_column($tmpSites, 'ID');
        $tmpSites = $this->share->util->arrayColumnAsKey($tmpSites, 'UUID');
        $insInfo = $this->utils->account->accountSelectByKey('ID', $insIDList[0])[0];
        $disInfo = $this->utils->account->accountSelectByKey('UUID', $insInfo['ParentUUID'])[0];
        foreach ($tmpSites as &$val) {
            if ($projectType == PAY_TYPE_SINGLE) {
                $val['ProjectUUID'] = $insInfo['UUID'];
                $val['ProjectID'] = $insInfo['ID'];
                $val['ProjectName'] = $insInfo['Location'];
                $val['Object'] = $val['Name'];
            } else if ($projectType == PAY_TYPE_MULTIPLE) {
                $val['ProjectUUID'] = $val['UUID'];
                $val['ProjectID'] = $val['ID'];
                $val['ProjectName'] = $val['Location'];
                $val['Object'] = $val['Location'];
            }
        }
        unset($val);

        $chargeData = $sites = $configs = [];
        $projectMap = [PAY_TYPE_MULTIPLE => 1, PAY_TYPE_SINGLE => 2, PAY_TYPE_OFFICE => 3];

        $personalAccountList = [];
        $personalAccountUUIDs = array_unique(array_filter(array_column($lockInfo, 'PersonalAccountUUID')));
        if (!empty($personalAccountUUIDs)) {
            if ($projectType == PAY_TYPE_SINGLE) {
                $personalAccountList = $this->utils->account->personalAccountSelectByArray([['UUID', $personalAccountUUIDs]]);
            } else if ($projectType == PAY_TYPE_MULTIPLE) {
                $personalAccountList = $this->utils->account->getUnitAndAptInfoByUUID($personalAccountUUIDs);
            }
            $personalAccountList = array_column($personalAccountList, null, 'UUID');
        }
        foreach ($lockInfo as $item) {
            $item['AptName'] = $item['UnitName'] = $item['LockName'] = '';
            if (!empty($item['PersonalAccountUUID'])) {
                if ($projectType == PAY_TYPE_SINGLE) {
                    $item['AptName'] = $personalAccountList[$item['PersonalAccountUUID']]['Name'];
                } else if ($projectType == PAY_TYPE_MULTIPLE) {
                    $item['AptName'] = $personalAccountList[$item['PersonalAccountUUID']]['RoomNumber'];
                    $item['CommunityUnitUUID'] = $personalAccountList[$item['PersonalAccountUUID']]['UnitUUID'];
                    $item['UnitName'] = $personalAccountList[$item['PersonalAccountUUID']]['UnitName'];
                }
            } else if (!empty($item['CommunityUnitUUID'])) {
                $unitInfo = $this->utils->communityUnit->getUnitInfoByKey('UUID', $item['CommunityUnitUUID']);
                $item['UnitName'] = $unitInfo['UnitName'];
            }
            if (!empty($item['LockUUID'])) {
                if ($item['Brand'] == '3') {
                    $item['LockName'] = $this->utils->_common->dormakaba->getDormakabaLock([['UUID', $item['LockUUID']]])[0]['Name'];
                } else if ($item['Brand'] == '6') {
                    $item['LockName'] = $this->utils->_common->itec->getItecLock([['UUID', $item['LockUUID']]])[0]['Name'];
                }
            }

            $projectUUID = $item['AccountUUID'];
            $siteItem = $tmpSites[$projectUUID];
            $location = $projectType == PAY_TYPE_MULTIPLE ? $siteItem['Location'] : '';
            $chargeItem = [
                'Instance' => [
                    'Distributor' => $disInfo['Account'],
                    'Installer' => $insInfo['Account'],
                    'Project' => $location,
                    'Type' => $projectMap[$projectType],
                    'ProjectUUID' => $projectUUID,
                ]
            ];
            $chargeData[] = $chargeItem;
            $sites[] = $siteItem;
            $configs[] = $item;
        }

        return [
            'site' => $sites, 'chargeData' => $chargeData, 'projectIds' => $projectIds, 'config' => $configs
        ];
    }

    public function getPriceToDay()
    {
        $params = ['NextTime', 'Users', 'Type', 'PayerType', 'userId', 'VideoSites', 'ThirdLockUUIDs'];
        list($nextTime, $users, $type, $payerType, $userId, $videoSites, $thirdLockUUIDs) = $this->getParams($params);
        $this->log->debug(
            'params:nextTime={nextTime};type={type};payerType={payerType};userId={userId};users={users};videoSites={videoSites};thirdLockUUIDs={thirdLockUUIDs}',
            ['nextTime' => $nextTime, 'type' => $type, 'payerType' => $payerType,
             'userId' => $userId, 'users' => $users, 'videoSites' => $videoSites, 'thirdLockUUIDs' => $thirdLockUUIDs]
        );

        $usersInfo = $this->callSelfFunc('getSubscribeUserInfo', [$users]);
        $videoStorageInfo = $this->callSelfFunc('getSubscribeVideoStorageInfo', [$videoSites, $type]);
        $thirdLockInfo = $this->callSelfFunc('getSubscribeThirdLockInfo', [$thirdLockUUIDs, $type]);

        // 获取按天付费的用户的费用
        $userPrice = $this->getUserPriceToDay($usersInfo, $userId, $payerType, $type, $nextTime);
        // 获取按天付费的视频存储的费用
        $videoPrice = $this->getVideoPriceToDay($videoStorageInfo,$payerType,$type,$nextTime);
        // 获取按天付费的三方锁的费用
        $thirdLockPrice = $this->getThirdLockPriceToDay($thirdLockInfo, $userId,$payerType,$type,$nextTime);

        $price = \share\util\outputComputedCount(\share\util\inputComputedCount($userPrice + $videoPrice + $thirdLockPrice));
        return $price;
    }

    private function getUserPriceToDay($usersInfo, $userId, $payerType, $type, $nextTime)
    {
        $userInfoAll = $usersInfo['all'];
        if (empty($userInfoAll)) {
            return 0;
        }
        $this->callSelfFunc('checkPayPermission', [$usersInfo, $userId, $payerType]);
        $this->loadUtil('account', true);;
        //验证所有用户过期时间是否都大于下次时间
        foreach ($usersInfo['all'] as $user) {
            if (intval($user['Role']) === PERENDMROLE) {
                // 单住户时区看自己
                $timeZone = $user['TimeZone'];
            } else {
                // 其他时区看项目
                $project = $this->utils->_common->account->getManagerInfo($user['ProjectId']);
                $timeZone = $project['TimeZone'];
            }
            $expireTime = $user['ExpireTime'];
            // 把expiretime转换为客户时区进行计算,因为按天只算年月日,这样才准确.nextTime本来就是按客户时区,不用转换
            $expireTime = \share\util\setTimeZone($expireTime, $timeZone, 3);
            if (strtotime($nextTime) <= strtotime($expireTime)) {
                $this->output->echoErrorMsg(STATE_PAY_NEXTTIME_INVALID);
            }
        }

        // 获取参数
        $this->loadProvider('billsysUtil');
        $price = $this->services->billsysUtil->computedUserChargeDataAndPrice(
            $type,
            $usersInfo,
            PAY_SUBSCRIPTION_BY_DAY,
            null,
            1,
            ['NextTime' => $nextTime]
        )['price'];
        return $price;
    }

    private function getVideoPriceToDay($videoStorageInfo, $payerType, $type, $nextTime)
    {
        //TODO 校验支付权限
        $siteInfoAll = $videoStorageInfo['site'];
        if (empty($siteInfoAll)) {
            return 0;
        }
        // 校验下次时间是否 大于 过期时间
        foreach ($siteInfoAll as $item) {
            $expireTime = $item['VideoStorageExpireTime'];
            $timeZone = $item['TimeZone'];
            $expireTime = \share\util\setTimeZone($expireTime, $timeZone, 3);
            if (strtotime($nextTime) <= strtotime($expireTime)) {
                $this->output->echoErrorMsg(STATE_PAY_NEXTTIME_INVALID);
            }
        }

        $this->loadProvider('billsysUtil');
        $price = $this->services->billsysUtil->computedVideoStorageChargeDataAndPrice(
            $type,
            $videoStorageInfo,
            PAY_SUBSCRIPTION_BY_DAY,
            ['NextTime' => $nextTime]
        )['price'];
        return $price;
    }

    private function getThirdLockPriceToDay($thirdLockInfo, $userId, $payerType, $type, $nextTime)
    {
        $configInfoAll = $thirdLockInfo['config'];
        if (empty($configInfoAll)) {
            return 0;
        }

        $this->callSelfFunc('checkPayPermissionByThirdLock', [$thirdLockInfo, $userId, $payerType]);

        // 校验下次时间是否 大于 过期时间
        $siteInfoAll = array_column($thirdLockInfo['site'], null, 'UUID');
        foreach ($configInfoAll as $item) {
            $expireTime = $item['ExpireTime'];
            $siteUUID = $item['AccountUUID'];
            $timeZone = $siteInfoAll[$siteUUID]['TimeZone'];
            $expireTime = \share\util\setTimeZone($expireTime, $timeZone, 3);
            if (strtotime($nextTime) <= strtotime($expireTime)) {
                $this->output->echoErrorMsg(STATE_PAY_NEXTTIME_INVALID);
            }
        }

        $this->loadProvider('billsysUtil');
        $price = $this->services->billsysUtil->computedThirdLockChargeDataAndPrice(
            $type,
            $thirdLockInfo,
            PAY_SUBSCRIPTION_BY_DAY,
            ['NextTime' => $nextTime],
            true
        )['price'];
        return $price;
    }

    /**
     * @description:订单详情
     * @author:lwj 2022/11/30 10:56 V6.5.4
     * @lastEditor: lwj 2022/11/30 10:56 V6.5.4
     * @param:{string} ID orderID
     * @return array
     */
    public function getInfo()
    {
        $params = ['ID', 'TimeZone', 'CustomizeForm'];
        list($id, $timeZone, $customizeForm) = $this->getParams($params);
        $orderListTable = PROXY_TABLES['orderList'];
        $orderData = $this->db->querySList(
            "select * from $orderListTable where ID = :ID",
            [':ID' => $id]
        )[0];
        if(empty($orderData)) return [];
        $type = $orderData['Type'];
        $mixType = $orderData['MixType'];
        $orderData['OriginalPrice'] = $this->share->util->outputComputedCount($orderData['TotalPrice']);
        $disCount = $this->share->util->computedDiscount($orderData['FinalPrice'], $orderData['Discount']);
        $orderData['TotalPrice'] = $this->share->util->outputComputedCount($disCount);
        $orderData['CouponCount'] = $this->share->util->outputComputedCount($orderData['CouponCount']);
        $orderData['BeforeOncePrice'] = $this->share->util->outputComputedCount($orderData['BeforeOncePrice']);
        $orderData['StatusEnum'] = $orderData['Status'];
        $statusMsgList = $this->getStatusMsgList();
        $orderData['Status'] = $statusMsgList[$orderData['Status']];
        $payTypeMsgList = $this->getPayTypeMsgList();
        $orderData['Type'] = $payTypeMsgList[$orderData['Type']];
        $orderData['TypeEnum'] = $type;

        $mixTypeArr = $this->share->util->getBitPositions($orderData['MixType']);
        $orderData['MixTypeArr'] = $mixTypeArr;
        $this->loadUtil('order', true);
        $orderData['TypeStr'] = $this->utils->_common->order->getPaymentMixTypeStr($mixTypeArr, $orderData['SubscriptionUUID']);


        $orderData['ShowNextTime'] = $this->utils->_common->order->getShowNextExpireTime($mixTypeArr, $orderData['SubscriptionUUID']);

        $accountTable = PROXY_TABLES['account'];
        $projectType = intval($orderData['ProjectType']);

        $orderData['ProjectType'] = $this->db->queryAllList(
            $accountTable,
            ['equation' => [':ID' => $orderData['InstallID']]]
        )[0]['Grade'];

        if (intval($orderData['PayerType']) === PAYER_TYPE_INS) {
            $orderData['Payer'] = $this->db->querySList(
                "select A.Account from $accountTable A
                 join $accountTable B on A.ID=B.ManageGroup where B.ID=:ID",
                [':ID' => $orderData['AccountID']]
            )[0]['Account'];
        }
        //PM订单展示成LoginAccount
        if ($orderData['PayerType'] === strval(PAYER_TYPE_PM)) {
            $this->loadUtil('account', true);
            $orderData['Payer'] = $this->utils->_common->account->getManagerListByArray([['Account', $orderData['Payer']]])[0]['LoginAccount'];
        }
        $orderData = $this->share->util->setQueryTimeZone([$orderData], $timeZone, $customizeForm)[0];
        //setQueryTimeZone没有NextTime，怕影响业务先单独放这里处理
        $orderData['NextTime'] = $this->share->util->setTimeZone($orderData['NextTime'], $timeZone, $customizeForm);

        if ($type == PAY_TYPE['rentManagerIntegration']) {
            $orderData['RentManager'] = $this->callSelfFunc('getRentManagerAmountList', [$orderData['ID'], $orderData['Months'], $orderData['OriginalPrice']]);
        } else {
            $orderData['Children'] = $this->callSelfFunc('getChildrenAmountList', [$id, $mixType, $orderData['Months']]);
        }

        $orderData['ChildrenThirdLock'] = [];
        //订单有包含三方锁，额外查询三方锁每项内容
        if (array_intersect($mixTypeArr, [PAY_TYPE['singleThirdLockActive'], PAY_TYPE['communityThirdLockActive'], PAY_TYPE['singleThirdLockRenew'], PAY_TYPE['communityThirdLockRenew']])) {
            $orderData['ChildrenThirdLock'] = $this->callSelfFunc('getChildrenThirdLockList', [$id]);
        }

        $orderData['NewOfficeChildren'] = [];
        if($projectType === ORDER_PROJECT_TYPE_NEW_OFFICE){
            $orderData['NewOfficeChildren'] = $this->callSelfFunc('getChildrenNewOfficeList', [$orderData['UUID'], $orderData['PayPlatform']]);
            $orderData['IsRenew'] = $this->utils->_common->order->checkPaymentMixTypeIsRenew($mixTypeArr) ? 1 : 0;
        }
        $orderData['CreditCount'] = $this->share->util->outputComputedCount($orderData['CreditCount']);
        return $orderData;
    }

    //获取三方锁每项内容
    public function getChildrenThirdLockList()
    {
        $params = ['OrderID'];
        list($orderID) = $this->getParams($params);
        $childrenList = [];

        $thirdLockItemList = $this->dao->orderThirdLockList->selectByKey('OrderID', $orderID);
        foreach ($thirdLockItemList as $item) {
            $childrenList[] = [
                'Amount' => $this->share->util->outputComputedCount($item['Amount']),
                'Price' => $this->share->util->outputComputedCount($item['Amount']),
                'Type' => $item['ServiceType'],
                'ProjectUUID' => $item['ProjectUUID'],
                'ProjectName' => $item['ProjectName'],
                'MonthlyFee' => json_decode($item['ChargeData'], true)['MonthlyFee'],
                'Days' => $item['Days'],
                'Brand' => $item['Brand'],
                'LockUUID' => $item['LockUUID'],
                'LockName' => $item['LockName'],
                'PersonalAccountUUID' => $item['PersonalAccountUUID'],
                'AptName' => $item['AptName'],
                'CommunityUnitUUID' => $item['CommunityUnitUUID'],
                'UnitName' => $item['UnitName'],
            ];
        }

        return $childrenList;
    }

    public function getChildrenNewOfficeList()
    {
        $params = ['OrderListUUID', 'PlatformType'];
        list($orderUuid, $platformType) = $this->getParams($params);
        $childrenList = [];

        $itemList = $this->dao->orderListOffice->selectByKey('OrderListUUID', $orderUuid);

        $this->log->debug("getChildrenNewOfficeList
         orderUuid: $orderUuid, platformType: $platformType");
        foreach ($itemList as $item) {
            if (intval($platformType) === ORDER_PAY_PLATFORM_CREDIT){
                // credit支付时 只显示credit
                $price = $this->share->util->outputComputedCount($item['TotalCredit']);
                $monthlyFee = $this->share->util->outputComputedCount($item['MonthlyCredit']);
                $this->log->debug("getOrderInfo credit pay info price:$price monthlyFee:$monthlyFee");
            }else{
                $unitPrice = $this->share->util->outputComputedCount($item['CreditUnitPrice']);
                $totalCredit = $this->share->util->outputComputedCount($item['TotalCredit']);
                $monthlyCredit = $this->share->util->outputComputedCount($item['MonthlyCredit']);
                $price = $this->share->util->roundToTwoDecimal($unitPrice * $totalCredit);
                $monthlyFee = $this->share->util->roundToTwoDecimal($unitPrice * $monthlyCredit);

                $this->log->debug("getOrderInfo normal pay info unitPrice:$unitPrice totalCredit:$totalCredit monthlyCredit:$monthlyCredit
                  price:$price monthlyFee:$monthlyFee");
            }

            $childrenList[] = [
                'Price' => $price,
                'Type' => $item['ServiceType'],
                'Office' => $item['AccountUUID'],
                'Name' => $item['ItemName'],
                'MonthlyFee'=> $monthlyFee,
                'RenewDay' => $item['RenewDay'],
                'OfficeName' => $item['AccountLocation'],
                'AccountUUID' => $item['AccountUUID'],
                'CreditUnitPrice' => $item['CreditUnitPrice'],
                'TotalCredit' => $item['TotalCredit'],
            ];
        }

        return $childrenList;
    }
    /**
     * @Author: chenpl
     * @Description: 获取RentManager订单明细
     * @Params:
     * @Return:
     * @Date: 2024/10/31
     */
    public function getRentManagerAmountList()
    {
        $params = ['OrderID', 'Months', 'OriginalPrice'];
        list($orderID, $months, $originalPrice) = $this->getParams($params);
        $data = [];

        $rentManagerCustomerList = $this->dao->rentManagerOrderList->selectByKey('OrderID', $orderID);
        if (!empty($rentManagerCustomerList)) {
            foreach ($rentManagerCustomerList as $item) {
                $customerInfo ['ID'] = json_decode($item['ChargeData'], true)['ID'];
                $customerInfo ['UUID'] = $item['RentManagerCustomerUUID'];
                $customerInfo ['CompanyName'] = json_decode($item['ChargeData'], true)['CompanyName'];
                $customerInfo ['CompanyCode'] = json_decode($item['ChargeData'], true)['CompanyCode'];
                $customerInfo ['Months'] = intval($months);
                $customerInfo ['MonthlyFee'] = $this->share->util->outputComputedCount($item['MonthlyFee']);
                $customerInfo ['TotalPrice'] = $originalPrice;
                $data[] = $customerInfo;
            }
        }

        return $data;
    }

    /**
     * @description:订单明细
     * @author:lwj 2022/11/30 10:56 V6.5.4
     * @lastEditor: lwj 2022/11/30 10:56 V6.5.4
     * @param:{string} ID orderID
     * @return array
     */
    public function getChildrenAmountList()
    {
        $params = ['OrderID', 'MixType', 'OrderMonths'];
        list($orderID, $mixType, $orderMonths) = $this->getParams($params);
        $childrenList = [];
        $mixTypeArr = $this->share->util->getBitPositions($mixType);
        foreach ($mixTypeArr as $type) {
            $orderType = intval($type);
            $renewAppArr = [PAY_TYPE['active'], PAY_TYPE['buyOutApp'], PAY_TYPE['landline']];
            if (in_array($orderType, $renewAppArr)) {
                $subOrderType = [PAY_SUB_TYPE['active'], PAY_SUB_TYPE['buyOutApp'], PAY_SUB_TYPE['landline'], PAY_SUB_TYPE['activePM']];
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', $subOrderType]]);
                foreach ($result as &$val) {
                    $val['Price'] = $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $childrenList[] = $val;
                }
            }

            if ($orderType === PAY_TYPE['renewToMonth']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['renewToMonth']]]);
                foreach ($result as &$val) {
                    $outApps = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['landline']], ['ParentID', $val['AppID']]]);
                    $val['OutApps'] = count($outApps);
                    $val['OutAppsAmount'] = $val['OutApps'] === 0 ? 0 : $this->share->util->outputComputedCount(
                        $outApps[0]['Amount'] * $val['OutApps']
                    );
                    $outAppsAmount = ($val['OutApps'] === 0 ? 0 : $outApps[0]['Amount'] * count($outApps)) + $val['Amount'];
                    $val['Price'] = $this->share->util->outputComputedCount($outAppsAmount * $orderMonths);
                    $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $childrenList[] = $val;
                }
            }

            if ($orderType === PAY_TYPE['renewToDay']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', [PAY_SUB_TYPE['renewToDay'], PAY_SUB_TYPE['renewPM']]]]);
                foreach ($result as &$val) {
                    $val['Price'] = $val['Amount'] = $this->share->util->outputComputedCount($val['Amount']);
                    $val['MonthlyFee'] = json_decode($val['ChargeData'], true)['MonthlyFee'];
                    $childrenList[] = $val;
                }
            }

            // v7.1.0 视频存储
            if ($orderType === PAY_TYPE['singleVideoStorage']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['singleVideoStorage']]]);
                foreach ($result as &$val) {
                    $item = [
                        'Amount' => $this->share->util->outputComputedCount($val['Amount']),
                        'Price' => $this->share->util->outputComputedCount($val['Amount']),
                        'Object' => $val['Object'],
                        'Type' => $val['Type'],
                        'ProjectUUID' => $val['ProjectUUID']
                    ];
                    $childrenList[] = $item;
                }
            }
            if ($orderType === PAY_TYPE['communityVideoStorage']) {
                $result = $this->dao->orderEndUserList->selectByArray([['OrderID', $orderID], ['Type', PAY_SUB_TYPE['communityVideoStorage']]]);
                foreach ($result as $val) {
                    $item = [
                        'Amount' => $this->share->util->outputComputedCount($val['Amount']),
                        'Price' => $this->share->util->outputComputedCount($val['Amount']),
                        'Object' => $val['Object'],
                        'Type' => $val['Type'],
                        'ProjectUUID' => $val['ProjectUUID'],
                        'ProjectName' => $val['ProjectName'],
                        'MonthlyFee' => json_decode($val['ChargeData'], true)['MonthlyFee'],
                        'Days' => $val['Days'],
                    ];

                    $childrenList[] = $item;
                }
            }
        }

        return $childrenList;
    }

    /**
     * @description:订单状态词条
     * @author:lwj 2023/02/01 10:56 V6.5.4
     * @lastEditor: lwj 2023/02/01 10:56 V6.5.4
     * @param:
     * @return array
     */
    public function getStatusMsgList()
    {
        $msgText = $this->share->util->getMsg()->getMsgText();
        $statusMsgList = [
            $msgText['processing'],
            $msgText['successed'],
            $msgText['failed'],
            $msgText['timeOut'],
            $msgText['abnormal'],
            $msgText['cancel'],
            $msgText['systemProcessing']
        ];
        return $statusMsgList;
    }

    /**
     * @description:订单类型词条
     * @author:lwj 2023/02/01 10:56 V6.5.4
     * @lastEditor: lwj 2023/02/01 10:56 V6.5.4
     * @param:
     * @return array
     */
    public function getPayTypeMsgList()
    {
        $msgText = $this->share->util->getMsg()->getMsgText();
        $payTypeMsgList = [
            '',
            $msgText['activation'], // 1 激活
            $msgText['subscription'], // 2 续费
            $msgText['additionalApp'],// 3 额外的小睿账号
            $msgText['subscription'],//4 续费
            $msgText['featureActivationFee'],//5 功能激活费
            $msgText['featureMonthlyFee'],//6 功能月费
            $msgText['featurePriceDifferences'],//7 高级功能差价
            $msgText['subscription'],//8 续费
            $msgText['autoRenew'],//9 自动续费
            $msgText['videoStorage'],//10 单住户视频存储
            $msgText['videoStorage'],//11 社区视频存储
            $msgText['rentManagerIntegration'],//12 rentManager
            '', // 13 多类型订单付费
            $msgText['thirdLock'],//14 单住户三方锁激活
            $msgText['thirdLock'],//15 社区三方锁激活
            $msgText['thirdLock'],//16 单住户三方锁续费
            $msgText['thirdLock'],//17 社区三方锁续费
        ];
        return $payTypeMsgList;
    }

    public function getNewPayTypeMsgList()
    {
        $msgText = $this->share->util->getMsg()->getMsgText();
        $payTypeMsgList = [
            '',
            $msgText['activation'], // 1 激活
            $msgText['subscription'], // 2 续费
            $msgText['additionalApp'],// 3 额外的小睿账号
            $msgText['subscription'],//4 续费
            $msgText['featureActivationFee'],//5 功能激活费
            $msgText['featureMonthlyFee'],//6 功能月费
            $msgText['featurePriceDifferences'],//7 高级功能差价
            $msgText['subscription'],//8 续费
            $msgText['autoRenew'],//9 自动续费
            $msgText['videoStorage'],//10 单住户视频存储
            $msgText['videoStorage'],//11 社区视频存储
            $msgText['rentManagerIntegration'],//12 rentManager
            '', // 13 多类型订单付费
            $msgText['thirdLock'],//14 单住户三方锁激活
            $msgText['thirdLock'],//15 社区三方锁激活
            $msgText['thirdLock'],//16 单住户三方锁续费
            $msgText['thirdLock'],//17 社区三方锁续费
        ];
        return $payTypeMsgList;
    }

    /**
     * @description:订单支付人词条
     * @author:lwj 2023/02/01 10:56 V6.5.4
     * @lastEditor: lwj 2023/02/01 10:56 V6.5.4
     * @param:
     * @return array
     */
    public function getPayerMsgList()
    {
        $msgText = $this->share->util->getMsg()->getMsgText();
        $payerMsgList = [
            $msgText['family'],
            $msgText['community']
        ];
        return $payerMsgList;
    }

    
    /*
     *@description 获取支付人是否有订单
     *<AUTHOR> 2023-03-30 16:47:22 V6.5.4
     *@lastEditor cj 2023-03-30 16:47:22 V6.5.4
     *@param {*} Payer
     *@return 0|1
     */
    public function getHavePayment()
    {
        $params = ['Payer'];
        list($payer) = $this->getParams($params);
        return $this->dao->orderList->selectByKey('Payer', $payer, 'count(*)')[0]['count(*)'] > 0 ? 1:0;
    }

    /*
     *@description 获取自动续费订单的用户信息
     *<AUTHOR> 2023-12-13 09:58:36 V6.7.0
     *@lastEditor cj 2023-12-13 09:58:36 V6.7.0
     *@param {*} Array 查询数组条件
     *@param {*} Field 查询字段
     *@return info 自动续费顶多的用户信息
     */
    public function getSubscriptionEndUserInfo()
    {
        $params = ['Array', 'Field'];
        list($searchArr, $field) = $this->getParams($params);
        return $this->dao->subscriptionEndUserList->selectByArray($searchArr, $field);
    }

    /*
     *@description 获取自动续费订单信息
     *<AUTHOR> 2023-12-13 09:58:36 V6.7.0
     *@lastEditor cj 2023-12-13 09:58:36 V6.7.0
     *@param {*} Array 查询数组条件
     *@param {*} Field 查询字段
     *@return info 自动续费顶多的用户信息
     */
    public function getSubscriptionList()
    {
        $params = ['Array', 'Field'];
        list($searchArr, $field) = $this->getParams($params);
        return $this->dao->subscriptionList->selectByArray($searchArr, $field);
    }

    public function getInstallerIDByVideoStorageInfo()
    {
        $params = ['VideoSites', 'PayerType'];
        list($videoSites, $payerType) = $this->getParams($params);
        $sites = $videoSites['site'];

        $insIDList = [];
        $hasCommunityItem = $hasSingleItem = false;
        foreach ($sites as $item) {
            if ($payerType == 1) {
                //pm支付下，视频存储的社区只能有一个项目
                //如果是pm，installerId存社区的id
                $insIDList[] = $item['ProjectID'];
                break;
            }
            if ($item['Grade'] == COMMUNITYGRADE) {
                $insIDList[] = $item['ManageGroup'];
                $hasCommunityItem = true;
            } else if ($item['Role'] == PERENDMROLE) {
                $insIDList[] = $item['ProjectID'];
                $hasSingleItem = true;
            }
        }
        $insIDList = array_unique($insIDList);
        if (count($insIDList) > 1 || ($hasCommunityItem && $hasSingleItem)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
        return $insIDList[0];
    }
    public function getInstallerIDByThirdLockInfo()
    {
        $params = ['ThirdLockInfo', 'PayerType'];
        list($thirdLockInfo, $payerType) = $this->getParams($params);
        $sites = $thirdLockInfo['site'];

        $insIDList = [];
        $hasCommunityItem = $hasSingleItem = false;
        foreach ($sites as $item) {
            if ($payerType == 1) {
                //pm支付下，视频存储的社区只能有一个项目
                //如果是pm，installerId存社区的id
                $insIDList[] = $item['ProjectID'];
                break;
            }
            if ($item['Grade'] == COMMUNITYGRADE) {
                $insIDList[] = $item['ManageGroup'];
                $hasCommunityItem = true;
            } else if ($item['Role'] == PERENDMROLE) {
                $insIDList[] = $item['ProjectID'];
                $hasSingleItem = true;
            }
        }
        $insIDList = array_unique($insIDList);
        if (count($insIDList) > 1 || ($hasCommunityItem && $hasSingleItem)) {
            $this->output->echoErrorMsg(STATE_NOT_PERMISSION);
        }
        return $insIDList[0];
    }

    public function getPaymentTypeStr()
    {
        $params = ['MixType', 'SubscriptionUUID'];
        list($mixType, $subscriptionUUID) = $this->getParams($params);
        // 判断是否包含自动续费
        $hasAutoRenew = false;
        if (in_array(PAY_TYPE['autoRenew'], $mixType)) {
            $hasAutoRenew = true;
        }

        // 兼容rentmanage的mixType没有包含自动扣费
        if (!empty($subscriptionUUID)) {
            $hasAutoRenew = true;
        }

        $msgText = $this->share->util->getMsg()->getMsgText();

        //续费
        $typeArr = [];
        $hasRenew = $hasActive = false;

        if (array_intersect($mixType, [1])) {
            $typeArr[] = $msgText['paymentApp'];
            $hasActive = true;
        }
        if (array_intersect($mixType, [3])) {
            $typeArr[] = $msgText['additionalApp'];
            $hasActive = true;
        }
        if (array_intersect($mixType, [2, 4, 8])) {
            $typeArr[] = $msgText['paymentApp'];
            $hasRenew = true;
        }
        if (array_intersect($mixType, [10, 11])) {
            $typeArr[] = $msgText['videoStorage'];
            $hasRenew = true;
        }
        if (array_intersect($mixType, [12])) {
            $typeArr[] = $msgText['rentManagerIntegration'];
            $hasRenew = true;
        }
        if (array_intersect($mixType, [14, 15])) {
            $typeArr[] = $msgText['thirdLock'];
            $hasActive = true;
        }
        if (array_intersect($mixType, [16, 17])) {
            $typeArr[] = $msgText['thirdLock'];
            $hasRenew = true;
        }

        //激活也有多类型
        if ($hasActive) {
            $typeStr = $msgText['activation'] . "(" . implode(',', $typeArr) . ")";
        } else if ($hasRenew && $hasAutoRenew) {
            $typeStr = $msgText['autoRenew'] . "(" . implode(',', $typeArr) . ")";
        } else if ($hasRenew) {
            $typeStr = $msgText['subscription'] . "(" . implode(',', $typeArr) . ")";
        } else {
            $typeStr = $this->callSelfFunc('getPayTypeMsgList')[$mixType[0]];
        }

        return $typeStr;
    }

    public function getShowNextExpireTime()
    {
        $params = ['MixType', 'SubscriptionUUID'];
        list($mixType, $subscriptionUUID) = $this->getParams($params);
        // 判断是否包含自动续费
        $hasAutoRenew = false;
        if (in_array(PAY_TYPE['autoRenew'], $mixType)) {
            $hasAutoRenew = true;
        }

        // 兼容rentmanage的mixType没有包含自动扣费
        if (!empty($subscriptionUUID)) {
            $hasAutoRenew = true;
        }

        // 自动扣费不显示下次过期时间
        if ($hasAutoRenew) {
            return 0;
        }

        // 按日续费、社区视频存储、社区三方锁续费显示下次过期时间
        if (array_intersect($mixType, [PAY_TYPE['renewToDay'],PAY_TYPE['communityVideoStorage'],PAY_TYPE['communityThirdLockRenew']])) {
            return 1;
        }
        return 0;
    }

    public function getPaymentMixTypeStr()
    {
        $params = ['MixType', 'SubscriptionUUID'];
        list($mixType, $subscriptionUUID) = $this->getParams($params);
        $msgText = $this->share->util->getMsg()->getMsgText();

        if (count($mixType) === 1 && intval($mixType[0]) === PAY_TYPE['credit']) {
            return $msgText['credit'];
        }

        // 判断是否包含自动续费
        $hasAutoRenew = false;
        if (in_array(PAY_TYPE['autoRenew'], $mixType)) {
            $hasAutoRenew = true;
        }
        if (!empty($subscriptionUUID)) {
            $hasAutoRenew = true;
        }

        $mixType = array_diff(array_unique($mixType), [PAY_TYPE['autoRenew']]);


        $payTypeMsgList = [
            ['', ''],
            [$msgText['activation'], $msgText['paymentApp']], // 1 激活app
            [$msgText['subscription'], $msgText['paymentApp']], // 2 续费app
            [$msgText['activation'], $msgText['additionalApp']], // 3 额外的小睿账号
            [$msgText['subscription'], $msgText['paymentApp']], // 4 续费app -落地
            [$msgText['activation'], $msgText['premiumPlan']], // 5 高级功能激活费
            [$msgText['subscription'], $msgText['premiumPlan']], // 6 高级功能续费 -月费
            [$msgText['activation'], $msgText['premiumPlan']], // 7高级功能差价
            [$msgText['subscription'], $msgText['paymentApp']], // 8 续费app -按日期续费
            ['', ''], // 9 自动续费
            [$msgText['subscription'], $msgText['videoStorage']], // 10 视频存储续费 -单住户视频存储
            [$msgText['subscription'], $msgText['videoStorage']], // 11 视频存储续费 -社区视频存储
            [$msgText['subscription'], $msgText['rentManagerIntegration']], // 12 rentManager续费
            ['', ''], // 13 多类型订单付费
            [$msgText['activation'], $msgText['thirdLock']], // 14 单住户三方锁激活
            [$msgText['activation'], $msgText['thirdLock']], // 15 社区三方锁激活
            [$msgText['subscription'], $msgText['thirdLock']], // 16 单住户三方锁续费
            [$msgText['subscription'], $msgText['thirdLock']], // 17 社区三方锁续费
            [$msgText['credit']],  // 18 credit
            [$msgText['activation'], $msgText['alarmDoor']], // 19 门禁激活
            [$msgText['activation'], $msgText['paymentApp']], // 20 app激活 -personnel
            [$msgText['activation'], $msgText['paymentApp']], // 21 app激活 -admin
            [$msgText['activation'], $msgText['premiumPlan']], // 22 高级功能激活
            [$msgText['subscription'], $msgText['alarmDoor']], // 23 门禁续费
            [$msgText['subscription'], $msgText['paymentApp']], // 24 续费app -personnel
            [$msgText['subscription'], $msgText['paymentApp']], // 25 续费app -admin
            [$msgText['subscription'], $msgText['premiumPlan']], 26// 高级功能续费
        ];

        //续费
        $typeArr = [];
        $typePrefix = '';
        foreach ($mixType as $type) {
            // 因为只会有一种付费类型(激活/续费/自动激活，所以可以直接给typePrefix赋值)
            $typeArr[] = $payTypeMsgList[$type][1];
            if ($hasAutoRenew) {
                $typePrefix = $msgText['autoRenew'];
            }else{
                $typePrefix = $payTypeMsgList[$type][0];
            }
        }
        $typeArr = array_unique($typeArr);
        return $typePrefix .  "(". implode(",", $typeArr). ")";
    }
    public function getMixTypeSqlQuery()
    {
        $params = ['BitPositions'];
        list($bitPositions) = $this->getParams($params);

        if (empty($bitPositions)) {
            return "";
        }

        if (count($bitPositions) == 1) {
            $combinedValue = $bitPositions[0];
        } else {
            $combinedValue = implode(' | ', $bitPositions);
        }

        return " And MixType & ($combinedValue) != 0";
    }

    /**
     * @description: 获得订单表的Type的十进制数
     * @param:
     * @return array
     * @author: shoubin.chen 2024/12/15 13:18:53 V7.1.0
     * @lastEditor: shoubin.chen 2024/12/15 13:18:53  V7.1.0
     */
    public function getOrderListType()
    {
        $params = ['SearchType'];
        list($searchType) = $this->getParams($params);

        $searchType = strval($searchType);
        $typeMap = [
            PAYMENT_SEARCH_TYPE['Activation'] => [
                PAY_TYPE['active'], //UPDATE OrderList SET MixType = 1 WHERE Type=1;
                PAY_TYPE['activePersonnelApp'],
                PAY_TYPE['activeAdminApp'],
            ],
            PAYMENT_SEARCH_TYPE['RenewApp'] => [
                PAY_TYPE['renewToMonth'],//UPDATE OrderList SET MixType = 2 WHERE Type=2;
                PAY_TYPE['landline'],//UPDATE OrderList SET MixType = 8 WHERE Type=4;
                PAY_TYPE['renewToDay'],//UPDATE OrderList SET MixType = 128 WHERE Type=8;
                PAY_TYPE['renewPersonnelApp'],
                PAY_TYPE['renewAdminApp'],
            ],
            PAYMENT_SEARCH_TYPE['AdditionalApp'] => PAY_TYPE['buyOutApp'],//UPDATE OrderList SET MixType = 4 WHERE Type=3;
//            PAYMENT_SEARCH_TYPE['LandLine'] => PAY_TYPE['landline'],
//            PAYMENT_SEARCH_TYPE['Features'] => [
//                PAY_TYPE['featureOnce'],//UPDATE OrderList SET MixType = 16 WHERE Type=5;
//                PAY_TYPE['featureMonth'],//UPDATE OrderList SET MixType = 32 WHERE Type=6;
//                PAY_TYPE['featureDiff']//UPDATE OrderList SET MixType = 64 WHERE Type=7;
//            ],
            PAYMENT_SEARCH_TYPE['AutoRenewApp'] => [
                PAY_TYPE['autoRenew'],//app自动续费，9，2，4，8
                PAY_TYPE['renewToMonth'],
                PAY_TYPE['landline'],
                PAY_TYPE['renewToDay'],
            ],
            PAYMENT_SEARCH_TYPE['AutoRenewVideoStorage'] => [
                PAY_TYPE['autoRenew'],//视频存储自动续费，9，10，11
                PAY_TYPE['singleVideoStorage'],
                PAY_TYPE['communityVideoStorage']
            ],
            PAYMENT_SEARCH_TYPE['RenewVideoStorage'] => [
                PAY_TYPE['singleVideoStorage'],//UPDATE OrderList SET MixType = 512 WHERE Type=10;
                PAY_TYPE['communityVideoStorage'],//UPDATE OrderList SET MixType = 1024 WHERE Type=11;
            ],
            PAYMENT_SEARCH_TYPE['RentManager'] => PAY_TYPE['rentManagerIntegration'],//UPDATE OrderList SET MixType = 2048 WHERE Type=12;
            PAYMENT_SEARCH_TYPE['AutoRenewRentManager'] => [
                PAY_TYPE['autoRenew'],
                PAY_TYPE['rentManagerIntegration'],
            ],
            PAYMENT_SEARCH_TYPE['ActivationThirdLock'] => [
                PAY_TYPE['singleThirdLockActive'],
                PAY_TYPE['communityThirdLockActive'],
            ],
            PAYMENT_SEARCH_TYPE['RenewThirdLock'] => [
                PAY_TYPE['singleThirdLockRenew'],
                PAY_TYPE['communityThirdLockRenew'],
            ],
            PAYMENT_SEARCH_TYPE['AutoRenewThirdLock'] => [
                PAY_TYPE['autoRenew'],
                PAY_TYPE['singleThirdLockRenew'],
                PAY_TYPE['communityThirdLockRenew'],
            ],
            PAYMENT_SEARCH_TYPE['Credit'] => PAY_TYPE['credit'],
            PAYMENT_SEARCH_TYPE['ActivationDoor'] => PAY_TYPE['activeDoor'],
            PAYMENT_SEARCH_TYPE['ActivationPremiumPlan'] => [
                PAY_TYPE['featureOnce'],//UPDATE OrderList SET MixType = 16 WHERE Type=5;
                PAY_TYPE['featureDiff'],//UPDATE OrderList SET MixType = 64 WHERE Type=7;
                PAY_TYPE['activePremiumPlan'],
            ],
            PAYMENT_SEARCH_TYPE['RenewDoor'] => PAY_TYPE['renewDoor'],
            PAYMENT_SEARCH_TYPE['RenewPremiumPlan'] => [
                PAY_TYPE['featureMonth'],//UPDATE OrderList SET MixType = 32 WHERE Type=6;
                PAY_TYPE['renewPremiumPlan'],
            ],
        ];
        $mixType = $typeMap[$searchType];
        $mixTypeList = [];
        if (is_array($mixType)) {
            foreach ($mixType as $type) {
                $mixTypeList[] = $this->share->util->getDecimalFromBits($type);
            }
            return $mixTypeList;
        } else {
            $mixTypeList[] = $this->share->util->getDecimalFromBits($mixType);
        }
        return $mixTypeList;
    }

    public function getServiceTypeStr()
    {
        $params = ['ServiceType'];
        list($serviceType) = $this->getParams($params);
        $msgText = $this->share->util->getMsg()->getMsgText();
        return $msgText[ORDER_SERVICE_TYPE[intval($serviceType)]];
    }

    public function checkPaymentMixTypeIsRenew()
    {
        $params = ['MixType'];
        list($mixType) = $this->getParams($params);

        foreach ($mixType as $type) {
            $typeInt = intval($type);
            if ($typeInt == PAY_TYPE['renewDoor'] || $typeInt == PAY_TYPE['renewPersonnelApp'] ||
                $typeInt == PAY_TYPE['renewAdminApp'] || $typeInt == PAY_TYPE['renewPremiumPlan'] ){
                return true;
            }
        }
        return false;
    }

    public function getNewOfficeOrderNum()
    {
        $params = ['UUID'];
        list($uuid) = $this->getParams($params);

        return $this->dao->orderList->getNewOfficeOrderNum($uuid);
    }
}
