<?php
/*
 * @Description: 添加用户
 * @version:
 * @Author: kxl
 * @Date: 2020-01-17 10:34:50
 * @LastEditors: cj
 */

namespace model\user;

trait add
{
    /**
     * @msg: 添加个人从账户
     * @services: sip,charge(在getSubActive中所引用)
     */
    public function addPerSubApp()
    {
        global $gApp,$cMessage;
        $mainUserId = $gApp['userAliasId'];
        $mainUser = $gApp['userAlias'];
        // 参数设置
        $params = [
            "Name"=>"",
            "Email"=>"",
            "MobileNumber"=>"",
            "PhoneCode"=>"",
            "Phone"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $name = $params['Name'];
        $phone = $params['Phone'];
        $phoneCode = $params['PhoneCode'];
        $perMngData = $this->db->querySList('select A.Account,A.ID from Account A left join PersonalAccount P on P.ParentID = A.ID where P.ID = :ID', [':ID' => $mainUserId])[0];
        $perMngAccount = $perMngData['Account'];
        $perMngId = $perMngData['ID'];
        $subMemberNumber = $this->db->querySList('select count(*) from PersonalAccount where ParentID = :ParentID and Role = 11', [':ParentID' => $mainUserId])[0]['count(*)'];
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"] - 1;
        if ($subMemberNumber >= $sysMaxSubCount) {
            $cMessage->echoErrorMsg(StateFamilyMemberCannotCreate);
        }
        // sip获取
        list($sip, $sipGroup) = $this->getSubSipAndGroup($mainUser, $perMngAccount);
        // 激活状态获取
        $active = $this->getSubActive($mainUser, $mainUserId, $perMngId, 2);
        $sipPw = \util\string\generatePw(12);
        $lang = $this->db->querySList('select Language from PersonalAccount where ID = :ID', [':ID' => $mainUserId])[0]['Language'];

        $password = \util\string\generatePw(8);
        $now = \util\computed\getNow();
        $uuid = \util\string\uuid();
        $bindArray = [
            ':Account' => $sip,
            ':Passwd' => md5($password),
            ':MobileNumber' => $mobile,
            ':Email' => $email,
            ':Name' => $name,
            ':ParentID' => $mainUserId,
            ':CreateTime' => $now,
            ':Phone' => $phone,
            ':PhoneCode' => $phoneCode,
            ':Role' => PERENDSROLE,
            ':SipAccount' => $sip,
            ':SipPwd' => $sipPw,
            ':ExpireTime' => DEFAULTEXPIRETIME,
            ':PhoneStatus' => 0,
            ':RoomID' => 0,
            ':Active' => $active,
            ':ActiveTime' => $active == 1 ? $now : null,
            ':Language' => $lang,
            ':UUID' => $uuid
        ];
        $this->db->insert2List('PersonalAccount', $bindArray);
        $id = $this->db->lastInsertId();
        $bindArray = \util\computed\delKeyFirstChar($bindArray);
        $bindArray['Passwd'] = $password;
        $this->log->actionLog('#model#addPerSubApp#bindArray'.json_encode($bindArray));
        // 更改数据流
        \util\computed\setGAppData(array_merge($bindArray, ['ID' => $id]));

        // 添加pbx,计算sipenable必须放在最后
        $options = ['type' => 6,
                    'node' => 0, //主账号id
                    'group' => $sipGroup,
                    'community' => $perMngId,
                    'communityType' => 1,
                    'enableGroup' => 1, //室内机群响铃
                    'sip' => $sip,
                    'passwd' => $sipPw, ];

        $sipService = $this->services['sip'];
        $res = $sipService->add2Freeswish($sip, $options);
        if (!$res) {
            $cMessage->echoErrorMsg(StateSipStatus);
        }

        $this->log->endUserLog(2, null, "add user:$name");
        $this->auditLog->setLog(AuditCodeAddFamilyMember, $this->env, [$sip], $sip);
        if ($email != '') {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $name], $sip);
        }
        if ($mobile != '') {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $name], $sip);
        }
        \util\computed\setSmartHomeTask(['Type' => 7, 'Key' => $sip]);
    }

    /**
     * @msg: 添加社区从账户
     * @services: sip,charge(在getSubActive中所引用)
     */
    public function addComSubUser()
    {
        global $gApp,$cMessage;
        $mainUserId = $gApp['userAliasId'];
        $mainUser = $gApp['userAlias'];
        // 参数设置
        $params = [
            'FirstName' => '',
            'LastName' => '',
            'Name' => '',
            'MobileNumber' => '',
            'Email' => '',
            'PhoneCode' => '',
            'Phone' => '',
            "IsAdmin"=>""
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $firstName = $params['FirstName'];
        $lastName = $params['LastName'];
        $name = $params['Name'];
        $phone = $params['Phone'];
        $phoneCode = $params['PhoneCode'];
        $isAdmin = $params['IsAdmin'];
        $needData = $this->db->querySList('select A.Account,A.ID,P.UnitID,P.Active,P.ParentID from Account A
         left join PersonalAccount P on P.ParentID = A.ID where P.ID = :ID', [':ID' => $mainUserId])[0];
        $comMngAccount = $needData['Account'];
        $comMngId = $needData['ID'];
        $buildId = $needData['UnitID'];
        $communityId = $needData['ParentID'];
        if ($needData["Active"] == "0") {
            $cMessage->echoErrorMsg(StateActiveFamilyAccount);
        }
        $familyMemberNumber = $this->db->querySList('select count(*) from PersonalAccount where ParentID = :ParentID and Role = 21', [':ParentID' => $mainUserId])[0]['count(*)'];
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"] - 1;
        if ($familyMemberNumber >= $sysMaxSubCount) {
            $cMessage->echoErrorMsg(StateFamilyMemberCannotCreate);
        }

        // sip获取
        list($sip, $sipGroup) = $this->getSubSipAndGroup($mainUser, $comMngAccount);
        // 激活状态获取
        $active = $this->getSubActive($mainUser, $mainUserId, $comMngId, 1);
        $sipPw = \util\string\generatePw(12);

        $password = \util\string\generatePw(8);
        $now = \util\computed\getNow();
        $lang = $this->db->querySList('select Language from PersonalAccount where ID = :ID', [':ID' => $mainUserId])[0]['Language'];
        $uuid = \util\string\uuid();
        $bindArray = [
            ':Account' => $sip,
            ':Passwd' => md5($password),
            ':MobileNumber' => $mobile,
            ':FirstName' => $firstName,
            ':LastName' => $lastName,
            ':Name' => $name,
            ':Email' => $email,
            ':ParentID' => $mainUserId,
            ':UnitID' => $buildId,
            ':CreateTime' => $now,
            ':Phone' => $phone,
            ':PhoneCode' => $phoneCode,
            ':Role' => COMENDSROLE,
            ':SipAccount' => $sip,
            ':SipPwd' => $sipPw,
            ':ExpireTime' => DEFAULTEXPIRETIME,
            ':PhoneStatus' => 0,
            ':RoomID' => 0,
            ':Active' => $active,
            ':Language' => $lang,
            ':UUID' => $uuid
        ];
        $this->db->insert2List('PersonalAccount', $bindArray);
        $id = $this->db->lastInsertId();
        $bindArray = \util\computed\delKeyFirstChar($bindArray);
        $bindArray['Passwd'] = $password;
        unset($bindArray['RoomID']);
        $this->log->actionLog('#model#addComSubUser#bindArray'.json_encode($bindArray));
        // 更改数据流
        \util\computed\setGAppData(array_merge($bindArray, ['ID' => $id]));

        // 添加pbx，计算sipenable
        $options = ['type' => 6,
        'node' => "1.$buildId.0.0.$mainUserId", //主账号id
        'group' => $sipGroup,
        'community' => $comMngId,
        'communityType' => 0,
        'enableGroup' => 1, //室内机群响铃
        'sip' => $sip,
        'passwd' => $sipPw, ];

        $sipService = $this->services['sip'];
        $res = $sipService->add2Freeswish($sip, $options);
        if (!$res) {
            $cMessage->echoErrorMsg(StateSipStatus);
        }

        $this->log->endUserLog(2, null, "add user:$name");

        $this->auditLog->setLog(AuditCodeAddFamilyMember, $this->env, [$sip], $sip);
        if ($email) {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $name], $sip);
        }
        if ($mobile) {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $name], $sip);
        }

        // 兼容新旧小区，兼容installer添加主账户
        $isNew = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [':AccountID' => $communityId])[0]['IsNew'];
        if ($isNew == 1) {
            \util\computed\setGAppBranch('addAccess');
        }
        // 智能家居，增加用户权限
        $info = json_encode(['isAdmin' => $isAdmin]);
        // 通知智能家居增加房间主账号字段  @LastEditors: cj
        $roomSip = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID'=>$mainUserId])[0]['Account'];
        \util\computed\setSmartHomeTask(['Type' => 7, 'Key' => $sip, 'Info' => $info, 'RoomSip'=>$roomSip]);
    }

    /**
     * @msg: 添加个人主账户
     * @services: sip,charge(在getMainExActive中所引用)
     */
    public function addPerMainUser()
    {
        global $gApp,$cMessage;
        $perMngId = $gApp['userAliasId'];
        $perMngAccount = $gApp['userAlias'];
        // 参数设置
        $params = [
            'Name' => '',
            'Email' => '',
            'MobileNumber' => '',
            'PhoneCode' => '',
            'Phone' => '',
            'Phone2' => '',
            'Phone3' => '',
            'PhoneStatus' => '',
            'TimeZone' => '',
            'RoomNumber' => '',
            'Address' => '',
            'Language' => '',
            'EnableIpDirect' => '',
            'isSingleMonitor' => '',
            'Relay' => '',
            'MAC' => '',
            'Location' => '',
        ];
        // 参数获取
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $name = $params['Name'];
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $phone = $params['Phone'];
        $phone2 = $params['Phone2'];
        $phone3 = $params['Phone3'];
        $phoneCode = $params['PhoneCode'];
        $phoneStatus = $params['PhoneStatus'];
        $timeZone = $params['TimeZone'];
        $roomNumber = $params['RoomNumber'];
        $address = $params['Address'];
        $lang = $params['Language'];
        $enableIpDirect = $params['EnableIpDirect'];
        // 6.2新增 新建单住户绑定设备选项
        $singleIsVillaMonitor = $params['isSingleMonitor'];
        $relay = $params['Relay'];
        $mac = $params['MAC'];
        $location = $params['Location'];

        list($sip, $sipGroup) = $this->getSipAndSetGroup($perMngAccount, $perMngId);
        $sipPw = \util\string\generatePw(12);

        $password = \util\string\generatePw(8);
        $now = \util\computed\getNow();
        $uuid = \util\string\uuid();

        $bindArray = [
            ':Account' => $sip,
            ':Passwd' => md5($password),
            ':Email' => $email,
            ':MobileNumber' => $mobile,
            ':Name' => $name,
            ':ParentID' => $perMngId,
            ':CreateTime' => $now,
            ':Phone' => $phone,
            ':Phone2' => $phone2,
            ':Phone3' => $phone3,
            ':PhoneCode' => $phoneCode,
            ':PhoneStatus' => $phoneStatus,
            ':Address' => $address,
            ':Role' => PERENDMROLE,
            ':SipAccount' => $sip,
            ':TimeZone' => $timeZone,
            ':SipPwd' => $sipPw,
            ':RoomID' => 0,
            ':RoomNumber' => $roomNumber,
            ':Language' => $lang,
            ':EnableIpDirect' => $enableIpDirect,
            ':ExpireTime' => DEFAULTEXPIRETIME,
            ':UUID' => $uuid
        ];
        $this->db->insert2List('PersonalAccount', $bindArray);
        $id = $this->db->lastInsertId();

        // 6.2单住户室内机限制
        if ($singleIsVillaMonitor == '1') {
            // 开启绑定室内机，设备加入devicesSpecial列表
            $this->db->insert2List('DevicesSpecial', [
                ':Account' => $sip,
                ':MAC' => $mac,
            ]);
            \util\computed\setGAppData(['Type' => '2', 'Node' => $sip, 'Relay' => $relay, 'Location' => $location]);
            // 进入添加设备的分支
            \util\computed\setGAppBranch('addIndoorMonitor');
        }

        // 2021-06-02 kxl 增加自动激活激活时间
        // list($active, $expireTime) = $this->getMainExActive($id, $perMngId, 2);
        $chargeService = $this->services['billsysUtil'];
        $charges = $chargeService->getCharge('single', $perMngId, [$id], PAYACTIVE);
        $charges = $charges[$id];
        $active = 0;
        if ($charges['ActiveFee'] == 0) {
            $active = 1;
        }

        $this->db->update2ListWID('PersonalAccount', [':ID' => $id, ':Active' => $active, ':ActiveTime' => $active == 1 ? $now : null]);
        if ($charges['MonthlyFee'] == 0) {
            $this->db->update2ListWID('PersonalAccount', [':ID' => $id, ':PhoneExpireTime' => DEFAULTEXPIRETIME]);
        }

        $bindArray = \util\computed\delKeyFirstChar($bindArray);
        // $bindArray['Passwd'] = $password;
        $this->log->actionLog('#model#addComSubUser#bindArray'.json_encode($bindArray));
        $addParams = $bindArray;

        // 兼容5.0前有个人公共设备的场景
        $pubDevices = $this->db->querySList('select ID from PersonalDevices where Community = :Node and Flag = 1', [':Node' => $perMngAccount]);
        foreach ($pubDevices as $pubDevice) {
            $this->db->insert2List('PerNodeDevices', [':NodeID' => $id, ':PerDevID' => $pubDevice['ID']]);
        }
        $bindArray = [':Account' => $sip, ':FreeAppCount' => 0];
        $this->db->insert2List('PersonalAccountCnf', $bindArray);
        $this->db->insert2List('VideoLength', [':Node' => $sip]);
        // 插入一条billing
        $this->db->insert2List('PersonalBillingInfo', [':Account' => $sip]);
        // 更改数据流
        $addParams['Passwd'] = $password;
        \util\computed\setGAppData(array_merge($addParams, ['ID' => $id]));

        // 添加pbx
        $options = ['type' => 6,
                    'node' => 0, //主账号id
                    'group' => $sipGroup,
                    'community' => $perMngId,
                    'communityType' => 1,
                    'enableGroup' => 1, //室内机群响铃
                    'sip' => $sip,
                    'passwd' => $sipPw, ];
        $sipService = $this->services['sip'];
        $res = $sipService->add2Freeswish($sip, $options);
        if (!$res) {
            $cMessage->echoErrorMsg(StateSipStatus);
        }
        $this->auditLog->setLog(AuditCodeAddFamilyMaster, $this->env, [$sip], $sip);
        if ($email != '') {
            $this->auditLog->setLog(AuditCodeUserEmail, $this->env, [$email, $name], $sip);
        }
        if ($mobile != '') {
            $this->auditLog->setLog(AuditCodeUserMobile, $this->env, [$mobile, $name], $sip);
        }
        $this->services['sip']->addMul2Freeswish([['sip' => $sip, 'enableGroup' => $phoneStatus == 1 ? 0 : 1]]);
        \util\computed\setSmartHomeTask(['Type' => 6, 'Key' => $sip]);
    }

    /**
     * @msg: 添加社区主账户
     */
    public function addComMainUser()
    {
        global $lang;
        $params = [
            'RoomID' => '',
            'FirstName' => '',
            'LastName' => '',
            'Name' => '',
            'Email' => '',
            'MobileNumber' => '',
            'Phone' => '',
            'Phone2' => '',
            'Phone3' => '',
            'PhoneCode' => '',
            'CallType' => '',
            'Key' => '',
            'Language' => '',
            'userAliasId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $this->log->actionLog('#model#addComMainUser#params='.json_encode($params));
        $roomID = $params['RoomID'];
        $firstName = $params['FirstName'];
        $lastName = $params['LastName'];
        $name = $params['Name'];
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $phone = $params['Phone'];
        $phone2 = $params['Phone2'];
        $phone3 = $params['Phone3'];
        $phoneCode = $params['PhoneCode'];
        $phoneState = $params['CallType'];
        $key = $params['Key'];
        $lang = $params['Language'] == null ? $lang : $params['Language'];
        $userId = $params['userAliasId'];

        $userData = $this->db->queryAllList('PersonalAccount', ['equation' => [':RoomID' => $roomID]])[0];
        $buildId = $userData['UnitID'];
        $this->addComMainUsreCom($userId, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, false, $lang);
        \util\computed\setGAppData(['ID' => $userData['ID']]);
        // 兼容新旧小区，兼容installer添加主账户
        $isNew = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [':AccountID' => $userId])[0]['IsNew'];
        if ($isNew == 1) {
            \util\computed\setGAppBranch('addAccess');
        }
    }

    /**
     * @msg: 添加社区房间和绑定室内机设备
     */
    public function addRoom()
    {
        global $cMessage;
        $params = [
            'ID' => '',
            'FirstName' => '',
            'LastName' => '',
            'Name' => '',
            'Email' => '',
            'MobileNumber' => '',
            'Phone' => '',
            'Phone2' => '',
            'Phone3' => '',
            'PhoneCode' => '',
            'CallType' => '',
            'Key' => '',
            'BuildID' => '',
            'RoomNumber' => '',
            'RoomName' => '',
            'IsAddUser' => '',
            'Language' => '',
            'EnableIpDirect' => '',
            'userAliasId' => '',
            'IsComMonitor' => '',
            'MAC' => '',
            'ArmingFunction' => '',
            'NetGroupNumber' => '',
            'Location' => '',
            'Relay' => '',
            "Layout"=>"",
            "LayoutName"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $firstName = $params['FirstName'];
        $lastName = $params['LastName'];
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $phone = $params['Phone'];
        $phone2 = $params['Phone2'];
        $phone3 = $params['Phone3'];
        $phoneCode = $params['PhoneCode'];
        $phoneState = $params['CallType'];
        $key = $params['Key'];
        $buildId = $params['BuildID'];
        $aptName = $params['RoomNumber'];
        $aptNo = $params['RoomName'];
        $userId = $params['userAliasId'];
        $isAddUser = $params['IsAddUser'];
        $lang = $params['Language'];
        $enableIpDirect = $params['EnableIpDirect'];
        $layout = $params["Layout"];
        $layoutName = $params["LayoutName"];

        $isComMonitor = $params['IsComMonitor'];
        $mac = $params['MAC'];
        $armingFunction = $params['ArmingFunction'];
        $netWork = $params['NetGroupNumber'];
        $location = $params['Location'];
        $relay = $params['Relay'];

        if (!$this->db->isExistFiled('CommunityUnit', [':ID' => $buildId, ':MngAccountID' => $userId])) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        if ($this->db->isExistFiled('CommunityRoom', [':RoomName' => $aptNo, ':UnitID' => $buildId])) {
            $cMessage->echoErrorMsg(StateAptExit, [], [$aptNo]);
        }

        $nodeId = $this->addNewRoom($buildId, $aptNo, $userId, $layout, $layoutName);
        $this->db->update2ListWID('PersonalAccount', [':ID' => $nodeId, ':RoomNumber' => $aptName]);
        if ($isComMonitor == '1') {
            // 添加设备到室内机方案的表
            $data = $this->db->querySList('select ID from DevicesSpecial where MAC = :MAC', [':MAC' => $mac]);
            if (count($data) != 0) {
                $cMessage->echoErrorMsg(StateMacExits);
            } else {
                $account = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID' => $nodeId])[0]['Account'];
                $this->db->insert2List('DevicesSpecial', [':Account' => $account, ':MAC' => $mac]);
                $this->log->actionLog("#model#insert#DevicesSpecial#Account=$account;MAC=$mac");
            }
        }
        if ($isAddUser === '1') {
            $userData = $this->db->queryAllList('PersonalAccount', ['equation' => [':ID' => $nodeId]])[0];
            $roomID = $userData['RoomID'];
            $this->addComMainUsreCom($userId, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, false, $lang);
            $this->db->update2ListWKey('PersonalAccount', [':RoomID' => $roomID, ':EnableIpDirect' => $enableIpDirect], 'RoomID');
            // 兼容新旧小区，兼容installer添加主账户
            $isNew = $this->db->querySList('select IsNew from CommunityInfo where AccountID=:AccountID', [':AccountID' => $userId])[0]['IsNew'];
            if ($isNew == 1) {
                \util\computed\setGAppBranch('addAccess');
            }
        } else {
            $account = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID' => $nodeId])[0]['Account'];
            \util\computed\setGAppData(['Account' => $account, 'ID' => $nodeId]);
        }
        if ($isComMonitor == '1') {
            // 添加设备到设备表
            $this->models['deviceCommunity']->add(1);
            $this->models['notify']->devComAddForManage(1);
        }
    }

    /**
     * @msg: PM添加社区主账户
     */
    public function addComMainForPM()
    {
        global $lang;
        $params = [
            'Build' => '',
            'Room' => '',
            'FirstName' => '',
            'LastName' => '',
            'Name' => '',
            'Email' => '',
            'MobileNumber' => '',
            'Phone' => '',
            'Phone2' => '',
            'Phone3' => '',
            'PhoneCode' => '',
            'PhoneState' => '',
            'Key' => '',
            'TempKeyPermission' => '',
            'RfCard' => '',
            'RoomNumber' => '',
            'EnableIpDirect' => '',
            'userAliasId' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $buildId = $params['Build'];
        $roomID = $params['Room'];
        $firstName = $params['FirstName'];
        $lastName = $params['LastName'];
        $name = $params['Name'];
        $email = $params['Email'];
        $mobile = $params['MobileNumber'];
        $phone = $params['Phone'];
        $phone2 = $params['Phone2'];
        $phone3 = $params['Phone3'];
        $phoneCode = $params['PhoneCode'];
        $phoneState = $params['PhoneState'];
        $key = $params['Key'];
        $card = $params['RfCard'];
        $roomName = $params['RoomNumber'];
        $tempKeyPermission = $params['TempKeyPermission'];
        $enableIpDirect = $params['EnableIpDirect'];
        $userId = $params['userAliasId'];
        $this->addComMainUsreCom($userId, $buildId, $roomID, $email, $mobile, $firstName, $lastName, $phone, $phone2, $phone3, $phoneCode, $phoneState, $key, $card, $lang, $tempKeyPermission);
        $this->db->update2ListWKey('PersonalAccount', [':RoomID' => $roomID, ':RoomNumber' => $roomName, ':EnableIpDirect' => $enableIpDirect], 'RoomID');
    }

    /**
     * @name: getSubSipAndGroup
     * @msg: 获取从账户sip和群组
     *
     * @param $mainUser：主账户，$perMngAccount：管理员账户
     * @return:
     */
    public function getSubSipAndGroup($mainUser, $perComMngAccount)
    {
        global $cMessage;
        // 获取sip账号
        $sipService = $this->services['sip'];
        $sip = $sipService->assignSip($perComMngAccount, '7');
        if ($sip === false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }
        // 获取主账户SIP群组
        $sipGroup = $this->db->queryAllList('SipGroup2', ['equation' => [':Account' => $mainUser]])[0]['SipGroup'];

        return [$sip, $sipGroup];
    }

    /**
     * @name: getActive
     * @msg: 获取从账户激活状态
     *
     * @param $mainUser：主账户，$perComMngId：管理员ID,$type：类型enum(1:社区,2:个人)
     * @return:
     */
    public function getSubActive($mainUser, $mainUserId, $perComMngId, $type)
    {
        // 收费服务
        $chargeService = $this->services['billsysUtil'];
        $charges = $chargeService->getCharge($type == 1 ? 'multiple' : 'single', $perComMngId, [$mainUserId], PAYADDAPP);
        $charges = $charges[$mainUserId];
        $appCount = $this->db->queryAllList('PersonalAccountCnf', ['equation' => [':Account' => $mainUser]])[0]['FreeAppCount'];
        $this->log->actionLog("#model#user##FreeAppCount=$appCount;AppNumber=".$charges['AppNumber']);
        $appCount += $charges['AppNumber'] - 1;
        $this->log->actionLog("#model#user##appCount=$appCount");
        $sunApp = $this->db->querySList('select count(*) from PersonalAccount where Role in ('.PERENDSROLE.','.COMENDSROLE.') and ParentID = :ParentID and Active = 1', [':ParentID' => $mainUserId])[0]['count(*)'];
        $this->log->actionLog("#model#user#sunApp=$sunApp;AddAppFee=".$charges['AddAppFee']);
        //额外app收费为0时自动激活，但是不计算购买app数量
        if (intval($sunApp) >= intval($appCount) && $charges['AddAppFee'] != 0) {
            $active = 0;
        } else {
            $active = 1;
        }

        return $active;
    }

    /**
     * @name: getMainExActive
     * @msg: 根据收费计划来获取即将创建主账户的激活和过期时间
     *
     * @param $perComMngId:管理员Id，$type：类型enum(1:社区,2:个人)
     * @return:
     */
    public function getMainExActive($id, $perComMngId, $type)
    {
        $this->log->actionLog("#model#getMainExActive#perConMngId=$perComMngId;type=$type");
        $chargeService = $this->services['billsysUtil'];
        $charges = $chargeService->getCharge($type == 1 ? 'multiple' : 'single', $perComMngId, [$id], PAYACTIVE);
        list($active, $expireTime) = $chargeService->computedExTimeActive($charges, $id, $type);
        if ($type == 2 && ($charges['MonthlyFee'] == 0 || $charges['ActivityId']['MonthlyFee'] == 0)) {
            $expireTime = DEFAULTEXPIRETIME;
        }

        return [$active, $expireTime];
    }

    /**
     * @name: getSipAndGroup
     * @msg: 获取主账户的sip和群组
     * @services：sip
     *
     * @param $perComMngAccount：管理员帐户，$perComMngId管理员Id
     * @return: [sip,sipGroup]
     */
    public function getSipAndSetGroup($perComMngAccount, $perComMngId)
    {
        global $cMessage;
        $this->log->actionLog("#model#getSipAndSetGroup#perComMngAccount=$perComMngAccount;perConMngId=$perComMngId");
        $sipService = $this->services['sip'];
        $sip = $sipService->assignSip($perComMngAccount, '6');
        if ($sip === false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccount);
        }
        $sipGroup = $sipService->getSipGroup($perComMngId);
        if ($sipGroup == false) {
            $cMessage->echoErrorMsg(StateIncorrectSipAccountGroup);
        }
        // 插入群组号
        $this->db->insert2List('SipGroup2', [':Account' => $sip, ':SipGroup' => $sipGroup]);
        $this->log->actionLog("#model#getSipAndSetGroup#sip=$sip;sipGroup=$sipGroup");

        return [$sip, $sipGroup];
    }

    public function addNewPmGetCallType()
    {
        $params = [
            'RoomID' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $roomID = $params['RoomID'];

        $account = $this->db->querySList('select Account from PersonalAccount where RoomID=:ID', [':ID' => $roomID])[0]['Account'];
        $callType = $this->db->querySList('select CallType from PersonalAccountCnf where Account=:Account', [':Account' => $account])[0]['CallType'];
        \util\computed\setGAppData(['CallType' => $callType]);
    }

    /**
     * @msg: App添加社区从账户,新增家庭从账户限制
     */
    public function addAppComSubUser()
    {
        global $gApp,$cMessage;
        $mainUserId = $gApp['userAliasId'];
        $familyMember = $this->db->querySList('select Pc.AllowCreateSlaveCnt,Pc.Flags,Pa.Account,Pa.ParentID from PersonalAccount Pa join PersonalAccountCnf Pc on Pa.Account = Pc.Account where Pa.ID = :ID', [':ID' => $mainUserId])[0];
        
        $addedAppNumber = $this->db->querySList('select count(*) from APPSpecial where Node = :Node', [':Node' => $familyMember['Account']])[0]['count(*)'];
        $now = \util\computed\getNow();
        $FeatureExpireTime = $this->db->querySList('select FeatureExpireTime from CommunityInfo where AccountID = :AccountID', [':AccountID' => $familyMember['ParentID']])[0]['FeatureExpireTime'];
        $Item = $this->db->querySList('select F.Item from ManageFeature M join FeaturePlan F on M.FeatureID = F.ID where M.AccountID = :ID', [':ID' => $familyMember['ParentID']])[0]['Item'];
        $isFamilyLimit = \util\computed\getSpecifyBitLE($Item, 4);
        $sysMaxSubCount = $this->db->queryAllList('SystemExtremum')[0]["MaxApps"] - 1;
        if ($familyMember['AllowCreateSlaveCnt'] != $sysMaxSubCount
         && $isFamilyLimit == 1 && $familyMember['Flags'] % 2 == 1 && strtotime($FeatureExpireTime) > strtotime($now)) {
            if (($addedAppNumber + 1) > $familyMember['AllowCreateSlaveCnt']) {
                $cMessage->echoErrorMsg(StateFamilyMemberBeyond);
            }
        }
        $this->addComSubUser();
        $params = [
            //从账户ID
            'ID' => '',
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $subID = $params['ID'];
        $subAccount = $this->db->querySList('select Account from PersonalAccount where ID = :ID', [':ID' => $subID])[0]['Account'];
        $bindArray = [
            ':Node' => $familyMember['Account'],
            ':Account' => $subAccount,
            ':CreateTime' => $now,
        ];
        $this->db->insert2List('APPSpecial', $bindArray);
        $this->log->actionLog("#model#addAppComSubUser#insertAPPSpecial#Account=$subAccount");
    }

    public function importKit()
    {
        global $cMessage, $gApp;
        $params = [
            "Account"=>""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $account = $params["Account"];
        $file = $_FILES["Device"];
        $fileName = $file["tmp_name"];
        $this->log->actionLog("#model#manageData#execImportComData#");
        require_once __DIR__.'/../../plugin/PHPExcel/PHPExcel/IOFactory.php';
        $objPHPExcel = \PHPExcel_IOFactory::load($fileName);

        //获取表格行数
        $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
        //获取表格列数
        $columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();

        $header2Key = [
            'Indoor Monitor Mac'=>'indoor'
        ];

        $dataArr = [];
        $keyColumn = [];
        $startRow = 1;
        $headerNull = 0;
        for ($row = $startRow; $row <= $rowCount; $row++) {
            if ($row != $startRow) {
                $data = [];
            }

            // 列循环
            for ($column = 'A'; $column <= $columnCount; $column++) {
                // 获取单元格值
                $value = $objPHPExcel->getActiveSheet()->getCell($column.$row)->getValue();
                $value = $value === null ? "" : strval($value);
                // 获取第一行值
                if ($row == $startRow) {
                    $key = $header2Key[$value];
                    // 无效列表头
                    if (!$key) {
                        $headerNull ++;
                    }
                    $keyColumn[$column] = $key;
                } else {
                    $cellText = $value;
                    $key = $keyColumn[$column];
                    if (!$key) {
                        continue;
                    }
                    $data[$key] = $value;
                }
                $data["row"] = $row;
            }
            if ($row != $startRow) {
                array_push($dataArr, $data);
            }
        }
        if ($headerNull == count($keyColumn)) {
            $cMessage->echoErrorMsg(StateNotImportFailed);
        }

        $data = $this->db->querySList(
            'select ID,Grade,ParentID,TimeZone,Language from Account where Account = :Account',
            [':Account' => $account]
        );
        if (count($data) == 0 || ($data[0]['Grade'] != 11 && $data[0]['Grade'] != 22)) {
            $cMessage->echoErrorMsg(StateEnterValidAccount);
        }

        if ($data[0]['Grade'] == 11) {
            $disId = $data[0]['ID'];
            $account = $account."-PersonalManage";
            $insId = $this->db->querySList(
                'select ID from Account where Account = :Account',
                [':Account' => $account]
            )[0]['ID'];
        } else {
            $disId = $data[0]['ParentID'];
            $insId = $data[0]['ID'];
        }

        // 过滤无效MAC
        $devices = [];
        $invalidDevices = [];
        foreach ($dataArr as $val) {
            if ($val['indoor'] == '') {
                continue;
            }
            // 判断device有没有被加入dis的mac库里和installer的库里
            $count = $this->db->querySList(
                'select count(*) from DeviceForRegister where MAC = :MAC1 and MngID=:MngID and PerMngID in (0, :InstallerID)',
                [':MAC1' => $val['indoor'], ':MngID' => $disId, ":InstallerID" => $insId]
            )[0]['count(*)'];
            if ($count != 1) {
                array_push($invalidDevices, $val['indoor']);
                continue;
            }

            array_push($devices, $val);
        }
        if (count($invalidDevices) != 0) {
            $cMessage->echoErrorMsg(StateInvalidKitImportMAC, [], [implode(",", $invalidDevices)]);
        }

        $this->db->begin();
        foreach ($devices as $val) {
            // 创建住户
            $resetParams = \util\model\saveParams();
            $gApp["userAliasId"] = $insId;
            $gApp["userAlias"] = $account;
            list($usec, $sec) = explode(" ", microtime());
            $time = explode('.', $sec.($usec*1000))[0];
            \util\computed\setGAppData([
                'Name' => substr(strval($time), strlen(strval($time))-6),
                'Email' => null,
                'MobileNumber' => null,
                'PhoneCode' => '',
                "Phone"=>"",
                "Phone2"=>"",
                "Phone3"=>"",
                'TimeZone'=>$data[0]['TimeZone'],
                'RoomNumber'=>'',
                'Address'=>'',
                'Language'=>$data[0]['Language'],
                'EnableIpDirect'=>0,
                'PhoneStatus'=>0
            ]);
            $this->models['user']->addPerMainUser();
            $sip = $gApp['plan']['data']['Account'];
            $resetParams();

            $resetParams = \util\model\saveParams();
            \util\computed\setGAppData([
                'Type' => 2,
                'MAC' => $val['indoor'],
                'Location' => 'Living Room',
                'Node' => $sip,
                "Relay"=>"",
                'userAliasId'=> $insId,
                'userAlias' => $account
            ]);
            $this->models['devicePersonal']->addForMng(true);
            $resetParams();

            $this->db->delete2ListWKey('PendingRegUser', 'MAC', $val['indoor']);
            $this->db->insert2List('PendingRegUser', [':Account' => $sip, ':MAC'=>$val['indoor'], ":Status" => 0]);
        }
        $this->db->commit();
    }
}
