<?php
error_reporting(0);

require_once(dirname(__FILE__) . '/../socket.php');
require_once(dirname(__FILE__) . '/../funcs_kafka.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbhost = DATABASEIP;
	$dbport = DATABASEPORT;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getUUID($db)
{
    $sth = $db->prepare("select uuid() as uuid");
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return str_replace('-', '', $ret['uuid']);
}

function startPcapCapture($type, $mac)
{
	$db = getDB();
    $uuid = getUUID($db);
	$sth = $db->prepare("insert into PcapCaptureControl (UUID, MAC) values (:uuid, :mac)");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();

    pcapCaptureControl($type, $uuid, $mac);
}

function stopPcapCapture($type, $mac)
{
    $db = getDB();
    $sth = $db->prepare("select UUID from PcapCaptureControl where MAC =:mac order by ID desc limit 1");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);

    $sth1 = $db->prepare("update PcapCaptureControl set Status = 1 where MAC =:mac");
    $sth1->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth1->execute();

    $db = null;
    pcapCaptureControl($type, $ret['UUID'], $mac);
}

function pcapCaptureControl($type, $uuid, $mac) 
{
    $data[] = $type;
    $data[] = $uuid;
    $data[] = $mac;

    $socket = new CPcapCaptureControlSocket();
    $socket->setMsgID(MSG_P2A_PCAP_CAPTURE_CONTROL);
    $socket->copy($data);
}

function cmd_usage($cmd)
{
    echo("usage: php ". $cmd . " <mac> <type> \n");
    echo("type: 1 start, 0 stop  \n");
    echo("check: 执行完脚本到对应的rtsp节点执行 grep PcapControl /var/log/csvrtsplog/vrtspd.INFO \n");
    echo("download: php download_pcap.php <mac> \n");
    echo("export: sz <mac>.tar.gz \n");
    exit(0);
}

//使用说明：① php pcap_capture.php $mac $type(1开启,0结束)
if ($argc != 3) {
    cmd_usage($argv[0]);
}

$mac = $argv[1];
$type = $argv[2];

if ($type == 1) {
    startPcapCapture($type, $mac);
} elseif ($type == 0) {
    stopPcapCapture($type, $mac);
}