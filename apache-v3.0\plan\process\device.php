<?php
namespace plan\process;

const DEVICE_PROCESS = [
    "queryPerDevForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryPerForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryComDevForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryComForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryPerPubDevForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryPerPubForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryComPubDevForApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryComPubForApp"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryDevForWeb"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getUserTimeZone"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryForWeb"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "editPerLocation"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"devLocationCheck","params"=>"Location"],
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.editPerLocation"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.devPerUpdate"
        ],
        // TODO alexa通知
    ],
    "editComLocation"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"devLocationCheck","params"=>"Location"],
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.editComLocation"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.devComUpdate",
            "dataContainer"=>"setAliasIdToMngId"
        ],
        // TODO alexa通知
    ],
    "addUserDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"userNameCheck","params"=>"Location"],
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.addFromUser"
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.addDevWCode"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ],[
            "type"=>"model",
            "model"=>"notify.devAddForUser"
        ],
        // TODO alexa通知
    ],
    "deletePer"=>[
        [
            "type"=>"model",
            "model"=>"devicePersonal.deletePer"
        ],[
            "type"=>"model",
            "model"=>"notify.devPerDelete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ],
    ],
    "getAccountDevice"=>[
        [
            "type"=>"model",
            "model"=>"devicePersonal.getAccountDevice"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "addCommunityDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"macCheck","params"=>"MAC"],
                ["name"=>"devLocationCheck","params"=>"Location"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin",
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.add"
        ],[
            "type"=>"database",
            "method"=>"commit",
        ],[
            "type"=>"model",
            "model"=>"notify.devComAddForManage",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "deleteCommunityDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin",
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.delete"
        ],[
            "type"=>"database",
            "method"=>"commit",
        ],[
            "type"=>"model",
            "model"=>"notify.devComDel",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "deleteBetCommunityDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin",
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.batchDelete"
        ],[
            "type"=>"database",
            "method"=>"commit",
        ],[
            "type"=>"model",
            "model"=>"notify.devComMulDev",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "editCommunityDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"devLocationCheck","params"=>"Location"],
            ]
        ],[
            "type"=>"database",
            "method"=>"begin",
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.edit"
        ],[
            "type"=>"database",
            "method"=>"commit",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "getCommunityDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getMngTimeZone"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.query",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getCommunityAllPubDev"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.getAllPublicDevice",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getPMDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryForPM",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "eidtPMDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"devLocationCheck","params"=>"Location"]
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.editPM",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.devComUpdate",
        ]
    ],
    "getPMDeviceInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryInfo",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getPMDoorDevice"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryDoor",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getAllPubDevForKeyChoose"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.getAllPubForKeyChoose"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "getAllDevForKeyChoose"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.getAllForKeyChoose"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "getComDevForAreaMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryForArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComDevForSuperMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryForSuper"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMacLibraryForSup"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"]
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.queryForSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMacLibraryForArea"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.queryForArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getMacLibraryForPCMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getMngTimeZone"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.queryForInstall"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addMacLibraryForSup"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"macCheck","params"=>"MAC"],
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.addForSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "addMacLibraryForArea"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"macCheck","params"=>"MAC"],
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.addArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "addMacLibraryForPCMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"macCheck","params"=>"MAC"],
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.addPC"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "deleteMacLibrary"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.delete"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete
        ]
    ],
    "uploadFileMacLibraryPC"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.uploadFilePCMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "uploadFileMacLibraryArea"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"macLibrary.uploadFileArea"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "uploadFileMacLibrarySup"=>[
        [
            "type"=>"model",
            "model"=>"macLibrary.uploadFileSup"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "setDevConfig"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"devMacInMngCheck","params"=>"MAC"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.setDevConfig"
        ],[
            "type"=>"model",
            "model"=>"notify.setConfig"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "setDevConfigOnce"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"devMacInMngCheck","params"=>"MAC"]
            ]
        ], [
            "type"=>"model",
            "model"=>"notify.setConfigOnce"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "rebootDev"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"devMacInMngCheck","params"=>"MAC"]
            ]
        ],[
            "type"=>"model",
            "model"=>"notify.reboot"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "remoteDev"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"devMacInMngCheck","params"=>"MAC"]
            ]
        ],[
            "type"=>"model",
            "model"=>"notify.remote"
        ]
    ],
    "getDevForUpgrade"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryForUpgrade"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryPerDeviceForMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"getMngTimeZone"]
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryForMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ],
    ],
    "addPerDeviceForMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"macCheck","params"=>"MAC"],
                ["name"=>"devLocationCheck","params"=>"Location"],
            ],
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.addForMng"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ],[
            "type"=>"model",
            "model"=>"userData.afterPerAddDev"
        ],[
            "type"=>"model",
            "model"=>"notify.devPerAddForManage"
        ],
        // TODO alexa通知
    ],
    "editPerDeviceForMng"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ],
        ],[
            "type"=>"database",
            "method"=>"begin",
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.editForPerMng"
        ],[
            "type"=>"database",
            "method"=>"commit",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],
    ],
    "queryPerDevWithType"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"devicePersonal.queryDevWithType"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryComDevWithType"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryDevWithType"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "queryUserDoorDevicePM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.getUserDoorDevices"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取设备信息
    "getPMDoorDeviceForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"deviceCommunity.queryDoor",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];
