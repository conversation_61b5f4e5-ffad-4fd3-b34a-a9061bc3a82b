<?php

namespace model\staff;

trait remove
{
    function delete($type) {
        $params = [
            "ID" => "",
            "userAliasId" => ""
        ];
        $params = \util\model\getParamsFromContainer($params, $this->dataContainer);
        $id = $params["ID"];
        $communityId = $params["userAliasId"];
        $id = explode(';', $id);

        $where ="ID in ( ".str_replace(';',',',$params["ID"]).")";
        $datas = $this->db->querySList("select * from ".TABLES[$type]." where $where",[]);

        $access = [];
        foreach ($id as $val) {
            checkValid($val, $communityId, $type);
            $access = array_merge($access, getGroup($val, $type));
            delete($val, $type);
        }

        \util\computed\setGAppData(["ID" => $id, "Type" => $type, "AccessGroup" => $access]);


        $account = $this->db->querySlist('select Account from Account where ID=:ID', [':ID' => $communityId])[0]['Account'];
        foreach ($datas as $data){
            if ($data['PinCode']){
                $this->auditLog->setLog(AuditCodeDeletePin, $this->env, [$data['PinCode']], $account);
            }
            if ($data['CardCode']){
                $this->auditLog->setLog(AuditCodeDeleteRf, $this->env, [$data['CardCode']], $account);
            }
        }
    }

    function deliveryDelete() {
        $this->delete(0);
    }

    function staffDelete() {
        $this->delete(1);
    }
}