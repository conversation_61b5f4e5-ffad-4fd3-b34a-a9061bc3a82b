<?php
/*
 * @Description: 身份验证执行人(@单例)
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2019-12-20 14:39:23
 * @LastEditors: cj
 */
namespace executor;

include_once __DIR__."/basic.php";
include_once __DIR__."/../interfaces/executor/main.php";

class CAuthHandler extends \executor\Basic implements \interfaces\executor\main\IExecutor
{
    public $type;
    public $roles;
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    public function parse()
    {
        //TODO 有效的type值
    }
    
    public function exec()
    {
        global $cLog,$gApp,$cMessage;
        switch ($this->type) {
            case 'web':
                $this->webAuth();
                break;
            case 'app':
                $this->appAuth();
                break;
            case 'alexa':
                $this->alexaAuth();
                break;
            case 'basic':
                $this->basicAuth();
                break;
        }
        $cLog->actionLog("#executor#auth#user=".$gApp["user"]);
        $this->authRole();
        $role = $gApp["role"];
        $active = $gApp["plan"]["active"];

        if (in_array($role, R_END_USER_ROLE) && $active) {
            $this->activeAuth();
        }
        $this->getUserID();
        // V6.4日本服务器迁移，增加指定dis下用户重定向
        // if (\util\role\checkJCloudDis($gApp["user"])) {
        //     $cMessage->echoErrorMsg(StateJCloudServer, ['Url' => J_CLOUD]);
        // }
    }

    private function webAuth()
    {
        global $gApp,$cMessage, $cLog;
        $version = $_SERVER[VERSIONHEADER];
        // 不存在版本，说明是4.6以前版本
        if (!$version) {
            $cMessage->echoErrorMsg(StateServerUpgradeTips);
        }

        // 与服务器版本不对应
        if ($version != VERSION) {
            $cMessage->echoErrorMsg(StateServerUpgradeTips);
        }

        $webAuth = $this->services["webAuth"];
        $account = $webAuth->auth();
        // 不存在则返回false
        if ($account === false) {
            $cMessage->echoErrorMsg(StateInvalidIdentity);
        }
        $gApp["user"] = $account;
        //解析token并记录(区分是哪个身份操作的)
        $token = \util\string\decodeToken();
        $cLog->actionLog("#executor#webAuth#token=".$token);
    }

    private function appAuth()
    {
        global $gApp,$cMessage;
        $appAuth = $this->services["appAuth"];
        $account = $appAuth->auth();
        // 不存在则返回false
        if ($account === false) {
            $cMessage->echoErrorMsg(StateInvalidIdentity);
        }
        $gApp["user"] = $account;
    }

    private function alexaAuth()
    {
        global $gApp,$cMessage;
        $alexaAuth = $this->services["alexaAuth"];
        $account = $alexaAuth->auth();

        // 不存在则返回false
        if ($account === false) {
            $cMessage->echoErrorMsg(StateInvalidIdentity);
        }
        $gApp["user"] = $account;
    }

    public function basicAuth()
    {
        global $gApp,$db,$cMessage;
        $token = $_SERVER['HTTP_AUTHORIZATION'];
        list($clientId, $secret) = explode('_', base64_decode($token));
        if ($clientId != AKUVOX_REQUEST_CLIENT_ID || $secret != AKUVOX_REQUEST_SECRET) {
            $cMessage->echoErrorMsg(StateInvalidIdentity);
        }

        $node = $gApp['plan']['data']['Node'];
        $data = $db->querySList(
            'select A.Account from Account A join PersonalAccount P on P.ParentID = A.ID where P.Account=:Account and P.Role in (10, 20)',
            [':Account' => $node]
        );
        if (count($data) === 0) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $gApp["user"] = $data[0]['Account'];
    }

    private function activeAuth()
    {
        global $gApp,$cMessage;
        $activeAuth = $this->services["activeAuth"];
        $active = $activeAuth->authActive($gApp["user"]);
        if (!$active) {
            $cMessage->echoErrorMsg(StateInvalidIdentity);
        }

        $expire = $activeAuth->authExpire($gApp["user"]);
        if (!$expire) {
            $cMessage->echoErrorMsg(StateAccountExpire);
        }
    }

    private function authRole()
    {
        // 确定用户身份
        global $gApp,$cMessage,$cLog;
        include_once __DIR__."/../util/role.php";
        // 转换user
        $loginAccount = $gApp["user"];
        $account = \util\role\getAccount($loginAccount);
        if (false === $account) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $user = $gApp["user"] = $account;
        $loginUser = $gApp['loginUser'] = $loginAccount;
        // 返回的是接口验证型的Role
        $role = \util\role\getComRole($user);
        $cLog->actionLog("#executor#auth#role=$role;allow=".json_encode($this->roles));
        $cLog->businessLog("user={user},role={role}", ['user' => $gApp["user"], 'role' => $role]);
        if (is_array($this->roles) && !in_array($role, $this->roles)) {
            $cMessage->echoErrorMsg(StateNotPermission);
        }
        $gApp["role"] = $role;
        $gApp["userAlias"] = $user;
    }

    // ID使用次数频繁，在这个地方先查出来
    private function getUserID()
    {
        global $gApp;
        $role = $gApp["role"];
        $user = $gApp["user"];
        include_once __DIR__."/../database/main.php";
        $db = \database\CDatabase::getInstance();
        if (in_array($role, R_END_USER_ROLE)) {
            $data = $db->querySList("select ID from PersonalAccount where Account = :Account", [":Account"=>$user]);
        } else {
            $data = $db->querySList("select ID from Account where Account = :Account", [":Account"=>$user]);
        }
        
        $gApp["userId"] = $data[0]["ID"];
        $gApp["userAliasId"] = $data[0]["ID"];
    }
}
