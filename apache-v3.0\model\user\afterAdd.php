<?php
/*
 * @Description: 添加用户后续
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-17 10:36:40
 * @LastEditors  : kxl
 */
namespace model\user;
trait afterAdd {
    /**
     * @msg: 添加个人从账户后续 
     */
    function afterAddPerSubUser () {
        // global $gApp;
        // $node = $gApp["userAlias"];
        // $this->log->actionLog("#model#afterAddPerSubUser#node=$node");
        // personnalUpdateNodeNotify($node);
    }
    /**
     * @msg: 添加社区从账户后续 
     */
    function afterAddComSubUser () {
        // global $gApp;
        // $node = $gApp["userAlias"];
        // $nodeId = $gApp["userAliasId"];
        // $data = $this->db->querySList("select ParentID,UnitID from PersonalAccount where ID = :ID",[":ID"=>$nodeId])[0];
        // $comMngId = $data["ParentID"];
        // $buildId = $data["UnitID"];
        // $this->log->actionLog("#model#afterAddComSubUser#comMngId=$comMngId;buildId=$buildId");
        // communityUpdateNodeNotify($node,$comMngId,$buildId,1);
    }

    /**
     * @msg: 添加个人主账户后续 
     */
    function afterAddPerMainUser () {
        // global $gApp;
        // $perMngAccount = $gApp["userAlias"];
        // 参数设置
        // $params = [
        //     "Account"=>"",
        //     "ID"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // personnalUpdateUserNotify($account, UPDATE_ADD_USER);
    }

    /**
     * @msg: 添加社区主账户后续 
     */
    function afterAddComMainUser () {
        // $params = [
        //     "Account"=>"",
        //     "ID"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
       
    }

    function afterAddMainUser () {

    }

    // 添加账户后续
    function afterAddUser () {
        // global $gApp;
        // // 参数设置
        // $params = [
        //     "Account"=>"",
        //     "Passwd"=>"",
        //     "Email"=>""
        // ];
        // $params = \util\model\getParamsFromContainer($params,$this->dataContainer);
        // $account = $params["Account"];
        // $password = $params["Passwd"];
        // $email = $params["Email"];
        // $this->log->actionLog("#model#afterAddUser#account=$account,email=$email");
        // perCreateUser($account, $password, $email);
    }
}