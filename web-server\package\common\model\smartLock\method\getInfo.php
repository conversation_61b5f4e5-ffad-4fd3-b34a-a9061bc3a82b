<?php

/**
 * @description 获取第三方设备相关信息的方法
 * <AUTHOR>
 * @date 2022-06-23 13:51:56
 * @version V6.4
 * @LastEditor cj
 * @lastEditTime 2024-10-18 11:45:43
 * @LastVersion V6.5.1
 */

namespace package\common\model\smartLock\method;

trait GetInfo
{
    /**
     * @description: 获取单住户可关联的全部relay
     * @author: cj 2022/6/23 13:57 V6.4.1
     * @param AllDevices指可关联第三方锁的全部AK设备信息
     * @param BindDevices已绑定的设备和对应Relay,形如["MAC地址" => ['1','2']]
     * @return {*}
     * @LastEditor: cj 2022/6/23 13:57 V6.4.1
     */
    public function getAssociateRelayList()
    {
        $params = ['AllDevices', 'BindDevices'];
        list($allDevices, $bindDevices) = $this->getParams($params);
        $allRelay = [];
        $this->loadUtil('version', true);
        $this->loadUtil('device', true);
        $deviceModel = $this->utils->_common->version->getSmartLockDevice();
        $deviceModel = array_column($deviceModel, 'VersionNumber');
        foreach ($allDevices as $val) {
            $relaysData = $this->utils->_common->device->relayDecode($val['Relay']);
            $firmware = explode('.', $val['Firmware'])[0];
            if (in_array($firmware, $deviceModel)) {
                foreach ($relaysData as $key => $relayData) {
                    if ($relayData['enable'] === 1) {
                        $deviceRelay = [];
                        $deviceRelay['RelayName'] = $relayData['name'];
                        $deviceRelay['Location'] = $val['Location'];
                        $deviceRelay['MAC'] = $val['MAC'];
                        $deviceRelay['Relay'] = intval($key);
                        $deviceRelay['IsBind'] = in_array($deviceRelay['Relay'], is_array($bindDevices[$val['MAC']]) ? $bindDevices[$val['MAC']] : []);
                        array_push($allRelay, $deviceRelay);
                    }
                }
            }
        }
        return $allRelay;
    }

    /**
     * @description: 获取社区主账号可关联的全部relay
     * @author: cj 2022/9/28 13:57 V6.5.1
     * @param AllDevices指可关联第三方锁的全部AK设备信息
     * @param BindDevices已绑定的设备和对应Relay,形如["MAC地址" => ['1','2']]
     * @param AccessDevices权限组勾选的Relay,形如["MAC地址" => relay和]
     * @return {*}
     * @LastEditor: cj 2022/9/28 13:57 V6.5.1
     */
    public function getAssociateRelayListWAccess()
    {
        $params = ['AllDevices', 'BindDevices', 'AccessDevices'];
        list($allDevices, $bindDevices, $accessDevices) = $this->getParams($params);
        $allRelay = [];
        $this->loadUtil('version', true);
        $this->loadUtil('device', true);
        $deviceModel = $this->utils->_common->version->getSmartLockDevice();
        $deviceModel = array_column($deviceModel, 'VersionNumber');
        foreach ($allDevices as $val) {
            $relaysData = $this->utils->_common->device->relayDecode($val['Relay']);
            $firmware = explode('.', $val['Firmware'])[0];
            if (in_array($firmware, $deviceModel)) {
                foreach ($relaysData as $key => $relayData) {
                    $relayValue = $this->utils->_common->device->getRelayIndexValue($key);
                    // 权限组Relay和设备Relay取交集
                    if ($relayData['enable'] === 1 && ($relayValue & $accessDevices[$val['MAC']]) === $relayValue) {
                        $deviceRelay = [];
                        $deviceRelay['RelayName'] = $relayData['name'];
                        $deviceRelay['Location'] = $val['Location'];
                        $deviceRelay['MAC'] = $val['MAC'];
                        $deviceRelay['Relay'] = intval($key);
                        $deviceRelay['IsBind'] = in_array($deviceRelay['Relay'], is_array($bindDevices[$val['MAC']]) ? $bindDevices[$val['MAC']] : []);
                        array_push($allRelay, $deviceRelay);
                    }
                }
            }
        }
        return $allRelay;
    }

    /**
     * @description:获取ThirdPartyLockDevice的信息
     * @author: shoubin.chen 2024/5/16 10:38:43 V6.8.0
     * @lastEditor: shoubin.chen 2024/5/16 10:38:43  V6.8.0
     */
    public function getThirdPartyLockDevice()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->thirdPartyLockDevice->selectByArray($array, $fields);
    }

    /**
     * @description:获取已经绑定的salto和dormakaba的设备relay
     * @author: kzr 2024/12/10 14:38:43 V7.1.0
     * @lastEditor: kzr 2024/12/10 14:38:43  V7.1.0
     */
    public function getBindRelay(){
        $params = ['allDevices','bindDevice'];
        list($allDevices,$bindDevice) = $this->getParams($params);
        $deviceMap = [];

        $this->loadUtil('salto',true);
        $this->loadUtil('dormakaba',true);
        $this->loadUtil('iTec',true);
        $this->loadUtil('ttLock',true);
        $this->loadUtil('smartLockSL20', true);

        foreach ($allDevices as $device) {
            $deviceMap[$device['UUID']] = $device['MAC'];
        }
        
        // 获取已经绑定的salto和dormakaba和iTec和ttLock的设备relay
        $deviceUUID = array_column($allDevices, 'UUID');
        
        // 处理 Salto 设备
        $saltoDevices = $this->utils->_common->salto->getSaltoLock([['DeviceUUID', $deviceUUID]], "DeviceUUID,Relay");
        foreach ($saltoDevices as $saltoDevice) {
            $relay = $this->share->util->getDateScript($saltoDevice['Relay']);
            $mac = $deviceMap[$saltoDevice['DeviceUUID']];
            if (!isset($bindDevice[$mac])) {
                $bindDevice[$mac] = [];
            }
            $bindDevice[$mac][] = $relay;
        }
        
        // 处理 Dormakaba 设备
        $dormakabaDevices = $this->utils->_common->dormakaba->getDormakabaLock([['DeviceUUID', $deviceUUID]], "DeviceUUID,Relay");
        foreach ($dormakabaDevices as $dormakabaDevice) {
            $relay = $this->share->util->getDateScript($dormakabaDevice['Relay']);
            $mac = $deviceMap[$dormakabaDevice['DeviceUUID']];
            if (!isset($bindDevice[$mac])) {
                $bindDevice[$mac] = [];
            }
            $bindDevice[$mac][] = $relay;
        }

        // 处理SL20设备
        $bindSL20List = $this->utils->_common->smartLockSL20->getSL20Lock([['DeviceUUID', $deviceUUID]], "DeviceUUID,Relay");
        foreach ($bindSL20List as $bindSL20Device) {
            $relay = $this->share->util->getDateScript($bindSL20Device['Relay']);
            $mac = $deviceMap[$bindSL20Device['DeviceUUID']];
            if (!isset($bindDevice[$mac])) {
                $bindDevice[$mac] = [];
            }
            $bindDevice[$mac][] = $relay;
        }


        // 处理 iTec 设备
        $iTecDevices =  $this->utils->_common->iTec->getITecLock([['DeviceUUID', $deviceUUID]], "DeviceUUID,Relay");
        foreach ($iTecDevices as $iTecDevice) {
            $relay = $this->share->util->getDateScript($iTecDevice['Relay']);
            $mac = $deviceMap[$iTecDevice['DeviceUUID']];
            if (!isset($bindDevice[$mac])) {
                $bindDevice[$mac] = [];
            }
            $bindDevice[$mac][] = $relay;
        }

        //处理 ttLock
        $ttLockDevices = $this->utils->_common->ttLock->getTtLock([['DeviceUUID', $deviceUUID]], "DeviceUUID,Relay");
        foreach ($ttLockDevices as $ttLockDevice) {
            $relay = $this->share->util->getDateScript($ttLockDevice['Relay']);
            $mac = $deviceMap[$ttLockDevice['DeviceUUID']];
            if (!isset($bindDevice[$mac])) {
                $bindDevice[$mac] = [];
            }
            $bindDevice[$mac][] = $relay;
        }

        return $bindDevice;
    }

    /**
     * @description:校验当前锁有没有权限
     * @author: kzr 2025/03/12 10:38:43 V7.1.0
     * @lastEditor: kzr 2024/03/12 10:38:43  V7.1.0
     */
    public function checkThirdLockAuth()
    {
        $params = ['LockUUID','Brand','Account','ProjectID'];
        list($lockUUID,$brand,$user,$projectId) = $this->getParams($params);

        //查询当前项目时区
        $this->loadUtil('account',true);
        $projectInfo = $this->utils->_common->account->getManagerInfo($projectId);
        $selfTimeZone = $projectInfo['TimeZone'];
        $now = date('Y-m-d H:i:s');
        //当前是项目时间
        $projectTime = $this->share->util->setTimeZone($now,$selfTimeZone, '', '-');

        //查询这个人关联的权限组
        $accountAccessGroups = $this->dao->accountAccess->selectByArray([['Account',$user]]);
        $accountAccessGroupId = array_column($accountAccessGroups,'AccessGroupID');

        if (count($accountAccessGroupId)==0){
            //没有权限组 则视为没有权限
            return false;
        }

        ///查询这个锁的权限组
        $thirdLockAccessGroups = $this->dao->accessGroupThirdLock->selectByArray([['LockUUID',$lockUUID],['Brand',$brand]]);
        $thirdLockAccessGroupId = array_column($thirdLockAccessGroups,'AccessGroupID');

        if (count($thirdLockAccessGroupId)==0){
            //没有权限组 则视为没有权限
            return false;
        }

        //判断是否有权限
        $hasAuth = false;
        //取交集
        $intersect = array_intersect($accountAccessGroupId,$thirdLockAccessGroupId);
        $intersect = array_values($intersect);

        if (count($intersect)==0){
            //没有交集 则视为没有权限
            return false;
        }

        $acessGroups = $this->dao->accessGroup->selectByArray([['ID',$intersect]]);

        foreach ($acessGroups as $accessGroup){
            switch ($accessGroup['SchedulerType']){
                case SCHEDULER_TYPE_ONCE:
                    //never
                    if ($projectTime>= $accessGroup['BeginTime'] && $projectTime<= $accessGroup['EndTime']){
                        $hasAuth = true;
                        break 2;
                    }
                    break;
                case SCHEDULER_TYPE_DAILY:
                    //daily
                    $time = date('H:i:s', strtotime($projectTime));
                    if ($time>= $accessGroup['StartTime'] && $time<= $accessGroup['StopTime']){
                        $hasAuth = true;
                        break 2;
                    }
                    break;
                case SCHEDULER_TYPE_WEEK:
                    //weekly
                    $weekDay = date('N', strtotime($projectTime));
                    $time = date('H:i:s', strtotime($projectTime));
                    $allowedDays = $accessGroup['DateFlag'];
                    
                    $adjustedWeekDay = ($weekDay % 7) + 1; // 将 1-7 转换为 1-7，且周日变为 1
                    
                    $isAllowed = ($allowedDays & (1 << ($adjustedWeekDay - 1))) > 0; // 检查是否允许访问
                    if ($isAllowed && $time >= $accessGroup['StartTime'] && $time <= $accessGroup['StopTime']) {
                        $hasAuth = true;
                        break 2;
                    }
                    break;
            }
        }

        return $hasAuth;
    }
}
