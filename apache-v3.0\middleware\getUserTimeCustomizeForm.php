<?php
/*
 * @Description: 
 * @version: 
 * @Author: kxl
 * @Date: 2020-01-20 17:58:32
 * @LastEditors  : kxl
 */
namespace middleware;
include_once __DIR__."/../interfaces/middleware/main.php";
use \interfaces\middleware\main\IMiddleware;
include_once __DIR__."/../database/main.php";
include_once __DIR__."/../util/computed.php";

class CGetUserTimeCustomizeForm implements IMiddleware {
    public function handle(\Closure $next) {
        global $cLog,$gApp;
        // 时区要用自己的Id
        // $id = $gApp["userId"];
        // $db = \database\CDatabase::getInstance();
        // $customizeForm = $db->querySList("select CustomizeForm from PersonalAccount where ID = :ID",[":ID"=>$id])[0]["CustomizeForm"];
        // $cLog->actionLog("#middle#getTimeCustomizeForm#id=$id;customizeForm=$customizeForm");
        // \util\computed\setGAppData(["SelfCustomizeForm"=>$customizeForm]);
        $next();
    }
}