<?php
/*
 * @Description: 收费计划
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
namespace model;
include_once __DIR__."/../../util/computed.php";
include_once __DIR__."/../../util/model.php";

include_once __DIR__."/query.php";
include_once __DIR__."/update.php";
class CChargePlan {
    use \model\chargePlan\query;
    use \model\chargePlan\update;
    private function outputComputedCount ($data) {
        return \util\computed\outputComputedCount($data);
    }

    private function inputComputedCount ($data) {
        return \util\computed\inputComputedCount($data);
    }
}