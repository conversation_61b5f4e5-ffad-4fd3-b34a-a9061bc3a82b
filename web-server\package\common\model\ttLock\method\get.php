<?php

namespace package\common\model\ttLock\method;

trait Get
{
    /**
     * @description: 获取ttLock信息
     * @author: kzr 2025/02/10 20:55:36 V7.1.0
     * @lastEditor: kzr 2025/02/10 20:55:36 V7.1.0
     */
    public function getTtLock()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }
        
        return $this->dao->ttLock->selectByArray($array, $fields);
    }

    /**
     * @description: 获取ttLockAccount的信息
     * @author: kzr 2025/02/10 20:55:36 V7.1.0
     * @lastEditor: kzr 2025/02/10 20:55:36 V7.1.0
     */
    public function getTtLockAccount()
    {
        $params = ['Array', 'Fields'];
        list($array, $fields) = $this->getParams($params);
        if ($fields === null or $fields === '') {
            $fields = '*';
        }

        return $this->dao->ttLockAccount->selectByArray($array, $fields);
    }

    public function getRelayInUse()
    {
        $params = ['DeviceUUID', 'MAC'];
        list($deviceUUID, $mac) = $this->getParams($params);

        $this->loadUtil('smartLock');
        $bindLockRelayList = $this->utils->smartLock->getThirdPartyLockDevice([['MAC', $mac]], "Relay");
        $relayInUse = [];

        $this->loadUtil('device');
        foreach (array_column($bindLockRelayList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }

        $this->loadUtil('iTec',true);
        $this->loadUtil('salto', true);
        $this->loadUtil('dormakaba', true);
        $this->loadUtil('ttLock', true);
        $this->loadUtil('smartLockSL20', true);
        $bindSaltoList = $this->utils->_common->salto->getSaltoLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindDormakabaList = $this->utils->_common->dormakaba->getDormakabaLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindITecList = $this->utils->_common->iTec->getITecLock([['DeviceUUID', $deviceUUID]], "Relay");
        $bindTtLockList = $this->callSelfFunc('getTtLock', [[['DeviceUUID', $deviceUUID]], "Relay"]);
        $bindSL20List = $this->utils->_common->smartLockSL20->getSL20Lock([['DeviceUUID', $deviceUUID]], "Relay");
        foreach (array_column($bindDormakabaList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSaltoList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindITecList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindTtLockList, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        foreach (array_column($bindSL20List, 'Relay') as $relayIndex) {
            $relayInUse[] = $this->utils->device->getLockRelayIndexValue($relayIndex);
        }
        return array_map('intval',$relayInUse);
    }

    /**
     * @description: 获取终端用户绑定的ttLock个数
     * @param: PersonalAccountUUID 主账户UUID
     * @author: kzr 2025/02/11 09:11:36 V7.1.0
     * @lastEditor: kzr 2025/02/11 09:11:36 V7.1.0
     */ 
    public function getTtLockNumByEndUser()
    {
        $params = ['PersonalAccountUUID'];
        list($PersonalAccountUUID) = $this->getParams($params);

        $this->loadUtil('account');
        $mainUser = $this->utils->account->personalAccountSelectByKey('UUID', $PersonalAccountUUID)[0];
        $lockNum = 0;
        $mainUserUUID = $mainUser['UUID']; 

        $bindArray=[
            'PersonalAccountUUID'=>$mainUserUUID
        ];
        $lockNum = $this->dao->ttLock->selectLockCountByMainUser($bindArray);

        return $lockNum;
    }
}

