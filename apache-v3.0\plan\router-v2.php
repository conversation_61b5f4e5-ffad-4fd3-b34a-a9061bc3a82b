<?php
include_once __DIR__ . "/addProcess.php";
include_once __DIR__ . "/process/main.php";
$cAddProcess = CAddProcess::getInstance();
$PLProcess = \plan\process\getProcess();

$PLRouterV2 = [
//    'v2/getpaccessgrouplist' => [
//        "method" => "GET",
//        "params" => ["Key", "page", "row", "Type"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["getAccessList"]
//        )
//    ],
//    'v2/getpallaccessgroup' => [
//        "method" => "GET",
//        "params" => ["RoomID", "ID", "DepartmentID", "Role"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["getAllAccess"]
//        )
//    ],
//    'v2/getpaccessgroupinfo' => [
//        "method" => "GET",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["getAccessInfo"]
//        )
//    ],
//    'v2/getpaccessgroupperson' => [
//        "method" => "GET",
//        "params" => ["ID", "serchKey", "serchValue"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["getAccessPerson"]
//        )
//    ],
//    'v2/getpaccessgroupnotperson' => [
//        "method" => "GET",
//        "params" => ["ID", "serchKey", "serchValue"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["getNotAccessPerson"]
//        )
//    ],
//    'v2/addpaccessgroup' => [
//        "method" => "POST",
//        "params" => [
//            "Name",
//            "Device",
//            "DateFlag",
//            "StartTime",
//            "StopTime",
//            "StartDay",
//            "StopDay",
//            "SchedulerType"
//        ],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["addAccess"]
//        )
//    ],
//    'v2/modifypaccessgroup' => [
//        "method" => "POST",
//        "params" => [
//            "ID",
//            "Name",
//            "Device",
//            "DateFlag",
//            "StartTime",
//            "StopTime",
//            "StartDay",
//            "StopDay",
//            "SchedulerType"
//        ],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["editAccess"]
//        )
//    ],
//    'v2/deletepaccessgroup' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["deleteAccess"]
//        )
//    ],
//    'v2/addpaccessgroupperson' => [
//        "method" => "POST",
//        "params" => ["ID", "User"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["addAccessPerson"]
//        )
//    ],
//    'v2/deletepaccessgroupperson' => [
//        "method" => "POST",
//        "params" => ["ID", "User"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $cAddProcess->unshift(
//            [
//                "type" => "middle",
//                "queue" => [
//                    ["name" => "setPMAlias"],
//                ]
//            ],
//            $PLProcess["deleteAccessPerson"]
//        )
//    ],
    "v2/getpenduserlist" => [
        "method" => "GET",
        "params" => ["Build", "Room", "Status", "Active", "Role", "serchKey", "serchValue", "row", "page"],
        "roles" => [RPROPERTYMANAGE],
        "type" => "web",
        "process" => $PLProcess["getNewPMUserList"]
    ],
    // "v2/deletependuser" => [
    //     "method" => "POST",
    //     "params" => ["ID"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" => $PLProcess["deletePMEndUser"]
    // ],
//    'v2/getpdeliveryauth' => [
//        "method" => "GET",
//        "params" => ["serchKey", "serchValue", "row", "page"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["getDeliveryList"]
//    ],
//    'v2/getpstaffauth' => [
//        "method" => "GET",
//        "params" => ["serchKey", "serchValue", "row", "page"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["getStaffList"]
//    ],
//    'v2/getpdelivertyinfo' => [
//        "method" => "GET",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["getDeliveryInfo"]
//    ],
//    'v2/getpstaffinfo' => [
//        "method" => "GET",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["getStaffInfo"]
//    ],
//    'v2/addpdeliverykey' => [
//        "method" => "POST",
//        "params" => ["Name", "PinCode", "CardCode", "AccessGroup", 'Build', 'Floor'],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["addDelivery"]
//    ],
//    'v2/addpstaffkey' => [
//        "method" => "POST",
//        "params" => ["Name", "CardCode", "AccessGroup", 'Face', 'Build', 'Floor'],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["addStaff"]
//    ],
//    'v2/modifypdeliverykey' => [
//        "method" => "POST",
//        "params" => ["ID", "Name", "PinCode", "CardCode", "AccessGroup", 'Face', 'IsDeleteFace', 'Build', 'Floor'],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editDelivery"]
//    ],
//    'v2/modifypstaffkey' => [
//        "method" => "POST",
//        "params" => ["ID", "Name", "CardCode", "AccessGroup", 'Face', 'IsDeleteFace', 'Build', 'Floor'],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editStaff"]
//    ],
//    'v2/deletepdeliverykey' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["deleteDelivery"]
//    ],
//    'v2/deletepstaffkey' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["deleteStaff"]
//    ],
    // 'v2/addnewpmainusercheck' => [
    //     "method" => "POST",
    //     "params" => ["RoomID", "FirstName", "LastName", "Email", "MobileNumber"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" => $PLProcess["addMainEndUserForNewCheck"]
    // ],
    // 'v2/addnewpmainuser' => [
    //     "method" => "POST",
    //     "params" => ["RoomID", "FirstName", "LastName", "Email", "MobileNumber", "Phone", "Phone2", "Phone3", "PhoneCode",
    //         "Step", "PIN", "Card", "Face", "AccessGroup",
    //         "Device", "DateFlag", "StartTime", "StopTime", "StartDay", "StopDay", "SchedulerType"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" =>  $cAddProcess->unshift(
    //         [
    //             "type" => "middle",
    //             "queue" => [
    //                 ["name" => "setInitLandline"],
    //             ]
    //         ],
    //         $PLProcess["addMainEndUserForNew"]
    //     )
    // ],
    // 'v2/addnewpsubusercheck' => [
    //     "method" => "POST",
    //     "params" => ["RoomID", "FirstName", "LastName", "Email", "MobileNumber"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" => $PLProcess["addSubEndUserForNewCheck"]
    // ],
    // 'v2/addnewpsubuser' => [
    //     "method" => "POST",
    //     "params" => ["RoomID", "FirstName", "LastName", "Email", "MobileNumber", "Phone", "PhoneCode",
    //         "Step", "PIN", "Card", "Face", "AccessGroup",
    //         "Device", "DateFlag", "StartTime", "StopTime", "StartDay", "StopDay", "SchedulerType"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" => $cAddProcess->unshift(
    //         [
    //             "type" => "middle",
    //             "queue" => [
    //                 ["name" => "setInitLandline"],
    //             ]
    //         ],
    //         $PLProcess["addSubEndUserForNew"]
    //     )
    // ],

//    'v2/getroomdevices' => [
//        "method" => "GET",
//        "params" => ["ID","PID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["queryUserDoorDevicePM"]
//    ],
    'v2/getpuserinfo' => [
        "method" => "GET",
        "params" => ["ID"],
        "roles" => [RPROPERTYMANAGE],
        "type" => "web",
        "process" => $PLProcess["queryNewUserInfoForPM"]
    ],
//    'v2/getpuserallaccess' => [
//        "method" => "GET",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["queryNewUserAccessPM"]
//    ],
    // 'v2/editpuserinfo' => [
    //     "method" => "POST",
    //     "params" => ["ID", "FirstName", "LastName", "Email", "MobileNumber", "PhoneCode", "Phone", "Phone2", "Phone3"],
    //     "roles" => [RPROPERTYMANAGE],
    //     "type" => "web",
    //     "process" => $PLProcess["editNewUserInfoForPM"]
    // ],
//    'v2/addpuserpin' => [
//        "method" => "POST",
//        "params" => ["ID", "Code"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["addUserAccessPIN"]
//    ],
//    'v2/addpusercard' => [
//        "method" => "POST",
//        "params" => ["ID", "Code"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["addUserAccessCard"]
//    ],
//    'v2/editpuserpin' => [
//        "method" => "POST",
//        "params" => ["ID", "Code"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editUserAccessPIN"]
//    ],
//    'v2/editpusercard' => [
//        "method" => "POST",
//        "params" => ["ID", "Code"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editUserAccessCard"]
//    ],
//    'v2/deletepuserpin' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["deleteUserAccessPIN"]
//    ],
//    'v2/deletepusercard' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["deleteUserAccessCard"]
//    ],
//    'v2/editpuseraccessgroup' => [
//        "method" => "POST",
//        "params" => ["ID", "AccessGroup"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editUserAccessGroup"]
//    ],
//    'v2/editpselfaccess' => [
//        "method" => "POST",
//        "params" => ["ID", "Device", "DateFlag", "StartTime", "StopTime", "StartDay", "StopDay", "SchedulerType"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["editUserSelfAccess"]
//    ],
//    'v2/addpface' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["addUserSingleFace"]
//    ],
//    'v2/deletepface' => [
//        "method" => "POST",
//        "params" => ["ID"],
//        "roles" => [RPROPERTYMANAGE],
//        "type" => "web",
//        "process" => $PLProcess["deleteUserSingleFace"]
//    ],
    'v2/createDifferenceOrder' => [
        "method" => "POST",
        "params" => ["OldFeeUUID", "NewFeeUUID", "CommunityID", "FeatureFeeType","NewFeatureID"],
        "roles" => [RCOMMUNITYGRADE,RPERSONGRADE],
        "type" => "web",
        "process" => $PLProcess["createDifferenceOrder"]
    ],
    // 'v2/importKit' => [
    //     "method" => "POST",
    //     "params" => ["Account"],
    //     "roles" => [RSUPERGRADE],
    //     "type" => "web",
    //     "process" => $PLProcess["importKit"]
    // ]
];
