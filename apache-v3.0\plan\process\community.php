<?php
namespace plan\process;

const COMMUNITY_PROCESS = [
    "getAllBuild"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"build.queryAll",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "addBuild"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"build.add",
        ],[
            "type"=>"model",
            "model"=>"notify.buildAdd",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd,
        ]
    ],
    "delBuild"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"build.delete",
        ],[
            "type"=>"model",
            "model"=>"notify.buildDelete",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
        ]
    ],
    "editBuild"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"build.edit",
        ],[
            "type"=>"model",
            "model"=>"notify.buildUpdate",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit,
        ]
    ],
    "getCommunityInfo"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.queryInfo",
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComDataAllCountForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getAllCount"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComMotionDataForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getData"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComListForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getUserId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getMyCommunityList"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getComLastFiveDoorLogForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getLastFiveDoorLog"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getWeekDataForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getWeekData"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getCommunityDetailForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
                ["name"=>"getUserId"]
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getCommunityDetail"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "setCommunityDetailForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"event",
            "event"=>"editAptPinType",
            "params"=>"userAliasId",
            "action"=>"on"
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"communityData.setCommunityDetail"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit,
            "options"=>["reload"=>"reload"]
        ],[
            "type"=>"event",
            "event"=>"editAptPinType",
            "params"=>"AptPinType",
            "action"=>"emit"
        ],[
            "type"=>"branches",
            "branches"=>[
                "changeAptPinType"=>[
                    [
                        "type"=>"model",
                        "model"=>"notify.communityAptUpdate"
                    ]
                ],
            ]
        ],[
            "type"=>"model",
            "model"=>"notify.communityDataUpdate"
        ],[
            "type"=>"model",
            "model"=>"notify.communityUserPinUpdate"
        ]
    ],
    "getRoomResidentForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getRoomResident"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getUserAssociateDevForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getUserAssociateDev"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getBuildRoomForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getBuildRoom"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //Dis获取房间信息
    "getBuildRoomForDis"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getBuildRoomForDis"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "setTimeZoneForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.chagneTime"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.communityTimeUpdate",
            "dataContainer"=>"setAliasIdToId",
        ]
    ],
    "setMotionForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.setMotion"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.communityMotionUpdate",
            "dataContainer"=>"setAliasIdToId",
        ]
    ],
    "setComVisitorForPM"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.setVisitor"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ],[
            "type"=>"model",
            "model"=>"notify.communityDataUpdate",
            "dataContainer"=>"setAliasIdToId",
        ]
    ],
    "addCommunity"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"communityNameCheck", "dataContainer" => ["name" => "const", "data" => ["ProjectGrade" => COMMUNITYGRADE]]]
            ]
        ],[
            "type"=>"model",
            "model"=>"manage.addCommunity"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessAdd
        ]
    ],
    "editCommunity"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"],
                ["name"=>"communityNameCheck", "dataContainer" => ["name" => "const", "data" => ["ProjectGrade" => COMMUNITYGRADE]]]
            ]
        ],[
            "type"=>"event",
            "event"=>"editAptPinType",
            "params"=>"ID",
            "action"=>"on"
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.editCommunity"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"event",
            "event"=>"editAptPinType",
            "params"=>"AptPinType",
            "action"=>"emit"
        ],[
            "type"=>"branches",
            "branches"=>[
                "changeAptPinType"=>[
                    [
                        "type"=>"model",
                        "model"=>"notify.communityAptUpdate",
                        "dataContainer"=>"setIdToAliasId"
                    ]
                ],
            ]
        ],[
            "type"=>"model",
            "model"=>"notify.communityDataUpdate",
            "dataContainer"=>"setIdToAliasId"
        ],[
            "type"=>"model",
            "model"=>"notify.communityTimeUpdate",
            "dataContainer"=>"setIdToAliasId"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    "deleteCommunity"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"getAliasId"]
            ]
        ],[
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.deleteCommunity"
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessDelete,
            "options"=>["token"=>"token"]
        ],[
            "type"=>"model",
            "model"=>"notify.communityDel"
        ],
    ],
    "setCommunity"=>[
        [
            "type"=>"database",
            "method"=>"begin"
        ],[
            "type"=>"model",
            "model"=>"manage.setCommunity",
            // "models"=>["chargePlan.setManage"]
        ],[
            "type"=>"database",
            "method"=>"commit"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessEdit
        ]
    ],
    // "importCommunity"=>[
    //     [
    //         "type"=>"middle",
    //         "queue"=>[
    //             ["name"=>"getAliasId"]
    //         ]
    //     ],[
    //         "type"=>"model",
    //         "model"=>"manageData.execImportComData"
    //     ],[
    //         "type"=>"model",
    //         "model"=>"manageData.importCommunity"
    //     ],[
    //         "type"=>"echo",
    //         "code"=>SateSuccessImport
    //     ],[
    //         "type"=>"model",
    //         "model"=>"notify.communityAdd"
    //     ],
    // ],
    "getAllCommunity"=>[
        [
            "type"=>"model",
            "model"=>"manage.queryAllCommunity"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getPMCommunityData"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getPMAllData"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    "getPMComLastDoorLog"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getLastFifteenDoorLog"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
    //pm app获取楼栋、房间信息
    "getBuildRoomForPMApp"=>[
        [
            "type"=>"middle",
            "queue"=>[
                ["name"=>"setPMAppAlias"],
                ["name"=>"getAliasId"],
            ]
        ],[
            "type"=>"model",
            "model"=>"communityData.getBuildRoom"
        ],[
            "type"=>"echo",
            "code"=>StateSuccessQuery,
            "options"=>["data"=>"data"]
        ]
    ],
];
