<?php
error_reporting(0);
require_once(dirname(__FILE__) . '/../../config/dynamic_config.php');
require_once(dirname(__FILE__) . '/../../config/base.php');
require_once(dirname(__FILE__) . '/../../config/global.php');
require_once(dirname(__FILE__) . '/../adapt_define.php');
require_once(dirname(__FILE__) . '/../socket.php');
require_once(dirname(__FILE__) . '/../funcs_face_interface.php');
require_once(dirname(__FILE__) . '/../funcs_common.php');
require_once(dirname(__FILE__) . '/../utility.php');
require_once(dirname(__FILE__) . '/../funcs_user.php');
require_once(dirname(__FILE__) . '/../funcs.php');

//使用说明：① php del_account_email.php $email $account_type 
if ($argc != 3) {
    echo "param error\n";
    echo "usage: php del_account_email.php email account_type\n";
    exit(1);
}

$email = $argv[1];
$account_type = $argv[2];
sendDeleteAccountConfirmEmail($email, $account_type);