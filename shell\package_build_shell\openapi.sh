#!/bin/bash
# ****************************************************************************
# Author        :   sicen
# Last modified :   2023-09-25
# Description   :   openapi 构建脚本
# Modifier      :
# ****************************************************************************

###################### 定义变量 ######################
PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

AKCS_SRC_ROOT=$PROJECT_PATH
source "$AKCS_SRC_ROOT"/shell/package_build_shell/source.sh

###################### 编译、整合代码，准备代码同步 ######################
cd $AKCS_SRC_WEB/openapi/build
bash build.sh clean
bash build.sh build

cd $AKCS_SRC_WEB/web-server/script
bash build.sh openApi
mv "$AKCS_SRC_WEB/web-server/dist" "$AKCS_PACKAGE_ROOT_OPENAPI/v4"
rm -R "$AKCS_PACKAGE_ROOT_OPENAPI/v4/test"
cp -rf $AKCS_SRC_WEB/apache-v3.0/notify "$AKCS_PACKAGE_ROOT_OPENAPI/v4/share/"
cp -rf $AKCS_SRC_WEB/shell/change_web_conf_by_etcd.php $AKCS_PACKAGE_ROOT_OPENAPI_SCRIPT/